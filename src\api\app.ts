import hRequest from '@/utils/http';
import type { DataType } from '@/utils/http/types';

// 分页
export const appPage = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/business/app/info/page',
    params
  });
};
// 新增
export const addApp = (data: any) => {
  return hRequest.post<DataType>({
    url: "/business/app/info",
    data: data
  });
};
// 修改
export const editApp = (data: any) => {
  return hRequest.put<DataType>({
    url: "/business/app/info",
    data: data
  });
};
// 详情
export const detailsApp = (id: any) => {
  return hRequest.get<DataType>({
    url: `/business/app/info/${id}`,
  });
};
// 删除
export const deleteApp = (id: any) => {
  return hRequest.delete<DataType>({
    url: `/business/app/info/${id}`,
  });
};