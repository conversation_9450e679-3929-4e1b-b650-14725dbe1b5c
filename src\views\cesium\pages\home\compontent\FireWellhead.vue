<template>
  <BaseCard title="消防井口径统计">
    <div class="relative">
      <div class="chart" ref="onlineRef"></div>
      <div class="absolute top-3 left-23.75% z-1">
        <img :src="getImages('cesium/fire-bg.png')" alt="" />
      </div>
    </div>
  </BaseCard>
</template>
<script lang="ts" setup>
import { initFireEchart } from "@/lib/echarts";
import { xfStat } from "@/api/home";
import { getImages } from "@/utils/getImages";
const { initChart } = useEchart();
const onlineRef = ref();
const list = ref<any[]>([]);
const getChart = async () => {
  const { data, code } = await xfStat();
  if (code === 200) {
    list.value = Object.keys(data).map((key) => ({
      name: key,
      value: data[key],
    }));
  }
  onlineRef.value && initChart(onlineRef.value, initFireEchart(list.value));
};
onMounted(() => {
  getChart();
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 250px;
  z-index: 2;
}
</style>
