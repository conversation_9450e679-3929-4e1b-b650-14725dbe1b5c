export const initVisitorEchart = (date: any, twoGis: any, threeGis: any, gisService: any, backgroundManage: any) => {
  const option: any = {
    grid: {
      top: '20%',
      left: "1%",
      right: '0',
      bottom: '0',
      containLabel: true,
    },
    legend: {
      icon: 'stack',
      itemWidth: 20,
      itemHeight: 2,
      right: "0",
      top: '7%',
      data: ['二维管网平台', '三维管网平台', 'GIS信息服务平台', '后台系统']
    },
    xAxis: {
      type: 'category',
      axisTick: { show: false },
      axisLine: {
        lineStyle: {
          color: "#F6F6F6",
        },
      },
      axisLabel: {
        textStyle: {
          color: "#2C3037",
        },
      },
      data: date
    },
    yAxis: {
      type: 'value',
      name: "单位：个",
      nameTextStyle: {
        color: "#5C5F66",
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#F6F6F6",
        },
      },
    },
    series: [
      {
        name: '二维管网平台',
        type: 'line',
        smooth: true,
        symbol: "circle",
        showSymbol: false,
        data: twoGis
      },
      {
        name: '三维管网平台',
        type: 'line',
        smooth: true,
        symbol: "circle",
        showSymbol: false,
        data: threeGis
      },
      {
        name: 'GIS信息服务平台',
        type: 'line',
        smooth: true,
        symbol: "circle",
        showSymbol: false,
        data: gisService
      },
      {
        name: '后台系统',
        type: 'line',
        smooth: true,
        symbol: "circle",
        showSymbol: false,
        data: backgroundManage
      },
    ]
  }
  return option
}