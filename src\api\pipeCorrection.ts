import hRequest from '@/utils/http'

/**
 * 管点属性错误状态枚举
 */
export enum AttributeErrorStatus {
  /** 未审核 */
  PENDING = '0',
  /** 已审核 */
  APPROVED = '1', 
  /** 已驳回 */
  REJECTED = '2',
  /** 已处理 */
  PROCESSED = '3'
}

/**
 * 通用响应结构
 */
export interface ApiResponse<T = any> {
  /** 消息状态码 */
  code: number
  /** 消息内容 */
  msg: string
  /** 数据对象 */
  data: T
}

/**
 * 分页数据对象
 */
export interface PageInfo<T = any> {
  /** 总记录数 */
  totalCount: number
  /** 每页记录数 */
  pageSize: number
  /** 总页数 */
  totalPage: number
  /** 当前页数 */
  currPage: number
  /** 列表数据 */
  list: T[]
}

/**
 * 管点属性错误业务对象 (用于新增/修改/审核)
 */
export interface AttributeErrorDto {
  /** ID */
  id?: number
  /** 上报人id */
  reporterId: number
  /** 管点id */
  ptId: number
  /** 类型 */
  type: string
  /** 状态(0未审核，1已审核，2已驳回，3已处理) */
  status: string
  /** 更新值 */
  updateValue: string
  /** 描述 */
  remark: string
  /** 创建人 */
  createBy?: number
  /** 创建时间 */
  createTime?: string
  /** 修改人 */
  updateBy?: number
  /** 修改时间 */
  updateTime?: string
}

/**
 * 管点属性错误视图对象 (用于详情查询)
 */
export interface AttributeErrorVo {
  /** ID */
  id: number
  /** 上报人id */
  reporterId: number
  /** 管点id */
  ptId: number
  /** 类型 */
  type: string
  /** 状态(0未审核，1已审核，2已驳回，3已处理) */
  status: string
  /** 更新值 */
  updateValue: string
  /** 描述 */
  remark: string
  /** 创建人 */
  createBy: number
  /** 创建时间 */
  createTime: string
  /** 修改人 */
  updateBy: number
  /** 修改时间 */
  updateTime: string
}

/**
 * 管点属性错误分页视图对象 (用于分页查询)
 */
export interface AttributeErrorPageVo {
  /** ID */
  id: number
  /** 上报人id */
  reporterId: number
  /** 上报人名称 */
  reporterName: string
  /** 管点id */
  ptId: number
  /** 管点编码 */
  gxddh: string
  /** 更新值 */
  updateValue: string
  /** 驳回理由 */
  rejectReason: string
  /** 类型 */
  type: string
  /** 状态(0未审核，1已审核，2已驳回，3已处理) */
  status: string
  /** 创建人 */
  createBy: number
  /** 创建时间 */
  createTime: string
}

/**
 * 分页查询参数
 */
export interface AttributeErrorPageParams {
  /** 分页大小 */
  pageSize?: number
  /** 当前页数 */
  pageNum?: number
  /** 排序列 */
  orderByColumn?: string
  /** 排序的方向desc或者asc */
  isAsc?: string
  /** 状态筛选 */
  status?: string
  /** 第一页数量 */
  firstNum?: number
}

/**
 * 审核属性纠错请求体
 */
export interface AttributeErrorReviewDto {
  /** 纠错记录ID */
  id: number;
  /** 审核意见 */
  ptId: number;
  fieldName?: string;
  modifyValue?: any;
  rejectReason?: string;
}

/**
 * 错误类型code-label映射
 */
export const PIPE_ERROR_TYPE_LABEL_MAP: Record<string, string> = {
  sx: '属性错误',
  fsw: '附属物错误',
  gc: '高程信息错误',
  js: '井深信息错误',
  jggg: '井盖规格错误',
  jgcz: '井盖材质错误',
  szdl: '所在道路错误',
};

/**
 * 原子API：审核通过
 */
export async function reviewPass(dto: AttributeErrorReviewDto): Promise<ApiResponse<void>> {
  return hRequest.put<ApiResponse<void>>({
    url: '/analyse/attribute/error/review/pass',
    data: dto
  })
}

/**
 * 原子API：审核驳回
 */
export async function reviewReject(dto: AttributeErrorReviewDto): Promise<ApiResponse<void>> {
  return hRequest.put<ApiResponse<void>>({
    url: `/analyse/attribute/error/review/reject`,
    data: dto
  })
}

/**
 * 业务API：统一审核接口（自动分流）
 */
export async function reviewPipeCorrection(
  id: number,
  ptId: number,
  fieldName: string,
  modifyValue: string,
  status: AttributeErrorStatus,
  rejectReason?: string
): Promise<ApiResponse<void>> {
  if (status === AttributeErrorStatus.APPROVED) {
    return reviewPass({ id, ptId, fieldName, modifyValue });
  } else if (status === AttributeErrorStatus.REJECTED) {
    return reviewReject({ id, ptId, fieldName, modifyValue, rejectReason });
  } else {
    throw new Error('仅支持审核通过或驳回');
  }
}

/**
 * 管点属性纠错API类
 */
export class PipeCorrectionApi {
  /**
   * 新增属性纠错记录
   * @param data 属性错误数据
   * @returns 返回创建的记录ID
   */
  static async createAttributeError(data: AttributeErrorDto): Promise<ApiResponse<number>> {
    return hRequest.post<ApiResponse<number>>({
      url: '/analyse/attribute/error',
      data
    })
  }

  /**
   * 修改属性纠错记录
   * @param data 属性错误数据
   * @returns 返回修改后的记录ID
   */
  static async updateAttributeError(data: AttributeErrorDto): Promise<ApiResponse<number>> {
    return hRequest.put<ApiResponse<number>>({
      url: '/analyse/attribute/error',
      data
    })
  }

  /**
   * 获取属性纠错记录详情
   * @param id 记录ID
   * @returns 属性错误详情
   */
  static async getAttributeErrorDetail(id: number): Promise<ApiResponse<AttributeErrorVo>> {
    return hRequest.get<ApiResponse<AttributeErrorVo>>({
      url: `/analyse/attribute/error/${id}`
    })
  }

  /**
   * 删除属性纠错记录
   * @param id 记录ID
   * @returns 删除结果
   */
  static async deleteAttributeError(id: number): Promise<ApiResponse<void>> {
    return hRequest.delete<ApiResponse<void>>({
      url: `/analyse/attribute/error/${id}`
    })
  }

  /**
   * 分页查询属性纠错记录
   * @param params 查询参数
   * @returns 分页数据
   */
  static async getAttributeErrorPage(params: AttributeErrorPageParams): Promise<ApiResponse<PageInfo<AttributeErrorPageVo>>> {
    return hRequest.get<ApiResponse<PageInfo<AttributeErrorPageVo>>>({
      url: '/analyse/attribute/error/page',
      params
    })
  }
}

/**
 * 便捷的导出函数
 */

/**
 * 提交管点属性纠错
 * @param ptId 管点ID
 * @param type 错误类型
 * @param updateValue 更新值
 * @param remark 错误描述
 * @param reporterId 上报人ID
 * @returns 创建结果
 */
export async function submitPipeCorrection(
  ptId: number,
  type: string,
  updateValue: string,
  remark: string,
  reporterId: number
): Promise<ApiResponse<number>> {
  const data: AttributeErrorDto = {
    ptId,
    type,
    updateValue,
    remark,
    reporterId,
    status: AttributeErrorStatus.PENDING
  }
  
  return PipeCorrectionApi.createAttributeError(data)
}

/**
 * 获取待审核的纠错记录
 * @param pageParams 分页参数
 * @returns 待审核记录列表
 */
export async function getPendingCorrections(
  pageParams: Omit<AttributeErrorPageParams, 'status'> = {}
): Promise<ApiResponse<PageInfo<AttributeErrorPageVo>>> {
  return PipeCorrectionApi.getAttributeErrorPage({
    ...pageParams,
    status: AttributeErrorStatus.PENDING
  })
}

// 默认导出API类
export default PipeCorrectionApi
