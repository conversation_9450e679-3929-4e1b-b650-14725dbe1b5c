<!--
 * @Description: 卷帘对比组件
 * @Date: 2024-01-15 10:00:00
 * @Author: 项目开发团队
 * @LastEditor: 项目开发团队
 * @LastEditTime: 2024-01-15 15:00:00
-->
<template>
  <custom-card
    :width="'460px'"
    @closeHandler="close"
    v-show="mainItem.show"
    :main-item="mainItem"
    :title="mainItem.title ?? (mainItem.title = '卷帘对比工具')"
  >
    <div class="action-buttons">
      <el-button
        type="primary"
        class="primary-btn w-130px"
        :disabled="isComparing"
        @click="startCompare"
      >
        开始对比
      </el-button>
      <el-button
        type="danger"
        class="danger-btn w-130px"
        :disabled="!isComparing"
        @click="stopCompare"
      >
        停止对比
      </el-button>
      <el-button class="w-130px clear-btn" @click="resetCompare">
        重置设置
      </el-button>
    </div>

    <div class="tip-container cursor-pointer">
      <el-alert type="info" :closable="false" show-icon>
        <template #title>
          <span class="tip-text">选择对比图层，通过分割线对比当前底图</span>
        </template>
      </el-alert>
    </div>

    <!-- 图层选择区域 -->
    <div class="layer-selection">
      <div class="layer-selector">
        <label class="layer-label">对比图层</label>
        <el-select
          v-model="compareLayer"
          placeholder="请选择要对比的图层"
          class="layer-select"
          :disabled="isComparing"
        >
          <el-option
            v-for="layer in availableLayers"
            :key="layer.value"
            :label="layer.label"
            :value="layer.value"
            :disabled="layer.value === getCurrentBaseLayerType()"
          >
            <span>{{ layer.label }}</span>
            <span 
              v-if="layer.value === getCurrentBaseLayerType()" 
              class="option-disabled-tip"
            >
              (当前底图)
            </span>
          </el-option>
        </el-select>
      </div>
      
      <!-- 对比说明 -->
      <div class="compare-description">
        <el-alert type="warning" :closable="false" show-icon>
          <template #title>
            <span class="description-text">
              对比模式：当前底图 vs 选择的对比图层
            </span>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 当前状态显示 -->
    <el-descriptions border :column="1" class="status-descriptions">
      <el-descriptions-item
        label="对比状态"
        class-name="status-value"
        label-class-name="status-label"
      >
        <el-tag :type="isComparing ? 'success' : 'info'">
          {{ isComparing ? '对比进行中' : '未开始对比' }}
        </el-tag>
      </el-descriptions-item>

      <el-descriptions-item
        label="基础图层"
        class-name="status-value"
        label-class-name="status-label"
      >
        <span>{{ getLayerName(getCurrentBaseLayerType()) }}</span>
      </el-descriptions-item>

      <el-descriptions-item
        label="对比图层"
        class-name="status-value"
        label-class-name="status-label"
      >
        <span>{{ getLayerName(compareLayer) || '未选择' }}</span>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 操作提示 -->
    <div v-if="isComparing" class="operation-tips">
      <el-alert type="success" :closable="false" show-icon>
        <template #title>
          <span class="tip-text">拖拽分割线可调整对比区域</span>
        </template>
      </el-alert>
    </div>
  </custom-card>
</template>

<script lang="ts" setup>
import CustomCard from "@/components/dialog/CustomCard.vue";
import { useDialogStore } from "@/stores/Dialogs";
import { useOperateToolStore } from "@/stores/OperateToolStore";
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { AppCesium } from "@/lib/cesium/AppCesium";

/**
 * 组件属性定义
 */
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});

/**
 * 可用图层配置
 */
const availableLayers = ref([
  {
    label: "天地图地形",
    value: "ter",
    description: "天地图地形图层"
  },
  {
    label: "天地图影像",
    value: "img", 
    description: "天地图影像图层"
  },
  {
    label: "天地图电子地图",
    value: "vec",
    description: "天地图电子地图图层"
  }
]);

/**
 * 响应式数据
 */
const isComparing = ref(false);
const compareLayer = ref<string>(''); // 将在组件挂载时智能选择

/**
 * 智能选择默认对比图层
 */
const initDefaultCompareLayer = (): void => {
  const currentBaseType = getCurrentBaseLayerType();
  const availableOptions = availableLayers.value.filter(layer => layer.value !== currentBaseType);
  
  if (availableOptions.length > 0) {
    // 优先选择影像图层，如果影像图层是当前底图则选择第一个可用图层
    const preferredLayer = availableOptions.find(layer => layer.value === 'img') || availableOptions[0];
    compareLayer.value = preferredLayer.value;
  } else {
    // 如果没有可用选项，默认选择影像图层
    compareLayer.value = 'img';
  }
  
  console.log('🔄 [RollingCompare] 初始化默认对比图层:', {
    currentBase: currentBaseType,
    selectedCompare: compareLayer.value
  });
};

/**
 * 根据传入的地图类型确定地图引擎
 */
const mapType = props.mainItem.type === "cesium" ? "cesium" : "maplibre";

/**
 * 存储和路由实例
 */
const operateToolStore = useOperateToolStore();
const dialogStore = useDialogStore();
const route = useRoute();

/**
 * 获取图层名称
 * @param value - 图层值
 * @returns 图层名称
 */
const getLayerName = (value: string): string => {
  const layer = availableLayers.value.find(l => l.value === value);
  return layer?.label || '';
};

/**
 * 获取当前底图类型
 * @returns 当前底图的类型值
 */
const getCurrentBaseLayerType = (): string => {
  try {
    if (mapType === "cesium") {
      // Cesium引擎：暂时无法准确获取当前底图类型，返回默认值
      // 可以通过分析当前viewer的imageryLayers来判断
      return 'vec'; // 默认假设是电子地图
    } else {
      // MapLibre引擎：通过BaseLayerManager获取当前底图
      const baseLayerManager = AppMaplibre.getBaseLayerManager();
      const currentBasemap = baseLayerManager.getCurrentBasemap();
      
      if (currentBasemap) {
        // 根据底图配置映射到我们的图层类型
        if (currentBasemap.id.includes('satellite') || currentBasemap.id.includes('img')) {
          return 'img';
        } else if (currentBasemap.id.includes('terrain') || currentBasemap.id.includes('ter')) {
          return 'ter';
        } else {
          return 'vec'; // 默认电子地图
        }
      }
    }
  } catch (error) {
    console.warn('获取当前底图类型失败:', error);
  }
  
  return 'vec'; // 默认返回电子地图
};

/**
 * 验证图层选择
 * @returns 验证结果
 */
const validateSelection = (): boolean => {
  if (!compareLayer.value) {
    ElMessage.warning('请选择对比图层');
    return false;
  }
  
  // 检查当前底图和对比图层是否相同
  const currentBaseType = getCurrentBaseLayerType();
  if (currentBaseType === compareLayer.value) {
    const currentLayerName = getLayerName(currentBaseType);
    const compareLayerName = getLayerName(compareLayer.value);
    ElMessage.warning(`当前底图已是${currentLayerName}，请选择不同的对比图层`);
    return false;
  }
  
  return true;
};

/**
 * 开始卷帘对比
 */
const startCompare = async (): Promise<void> => {
  try {
    if (!validateSelection()) {
      return;
    }

    console.log('🎬 [RollingCompare] 开始卷帘对比...', {
      mapType,
      compareLayer: compareLayer.value
    });

    if (mapType === "cesium") {
      // Cesium 卷帘对比
      const cesiumInstance = AppCesium.getInstance();
      await startCesiumCompare(cesiumInstance);
    } else {
      // MapLibre 卷帘对比
      await startMapLibreCompare();
    }

    isComparing.value = true;
    ElMessage.success('卷帘对比已开启');
    
  } catch (error) {
    console.error('❌ [RollingCompare] 开始对比失败:', error);
    ElMessage.error('开启卷帘对比失败：' + (error as Error).message);
  }
};

/**
 * 开始 Cesium 卷帘对比
 * @param cesiumInstance - Cesium 实例
 */
const startCesiumCompare = async (cesiumInstance: any): Promise<void> => {
  try {
    // 获取天地图图层配置
    const BC = (window as any).BC;
    if (!BC) {
      throw new Error('BC库未加载');
    }

    // 创建对比图层
    const compareBaseLayer = BC.ImageryLayerFactory.createTdtImageryLayer({
      key: 'b254904598a72fd14661de55fa70511d',
      style: compareLayer.value,
    });

    // 启用卷帘对比
    const viewer = cesiumInstance.getViewer();
    viewer.mapSplit.enable = true;
    
    // 添加对比图层（将覆盖在当前底图上，通过分割线控制显示区域）
    viewer.mapSplit.addBaseLayer(compareBaseLayer, -1);

    console.log('✅ [RollingCompare] Cesium卷帘对比启动成功');
    
  } catch (error) {
    console.error('❌ [RollingCompare] Cesium卷帘对比启动失败:', error);
    throw error;
  }
};

/**
 * 开始 MapLibre 卷帘对比
 */
const startMapLibreCompare = async (): Promise<void> => {
  try {
    const map = AppMaplibre.getMap();
    if (!map) {
      throw new Error('MapLibre地图实例未找到');
    }

    // MapLibre的卷帘对比实现
    // 这里需要实现MapLibre版本的卷帘对比逻辑
    // 可以使用maplibre-gl-compare插件或自定义实现
    
    console.log('✅ [RollingCompare] MapLibre卷帘对比启动成功');
    ElMessage.info('MapLibre卷帘对比功能开发中...');
    
  } catch (error) {
    console.error('❌ [RollingCompare] MapLibre卷帘对比启动失败:', error);
    throw error;
  }
};

/**
 * 停止卷帘对比
 */
const stopCompare = (): void => {
  try {
    console.log('⏹️ [RollingCompare] 停止卷帘对比...');

    if (mapType === "cesium") {
      // 停止 Cesium 卷帘对比
      const cesiumInstance = AppCesium.getInstance();
      const viewer = cesiumInstance.getViewer();
      
      if (viewer.mapSplit) {
        viewer.mapSplit.enable = false;
        // 清理添加的图层
        // viewer.mapSplit.clearLayers(); // 如果有这个方法
      }
    } else {
      // 停止 MapLibre 卷帘对比
      // 清理MapLibre卷帘对比相关设置
    }

    isComparing.value = false;
    ElMessage.success('卷帘对比已停止');
    
  } catch (error) {
    console.error('❌ [RollingCompare] 停止对比失败:', error);
    ElMessage.error('停止卷帘对比失败：' + (error as Error).message);
  }
};

/**
 * 重置对比设置
 */
const resetCompare = (): void => {
  try {
    // 如果正在对比，先停止
    if (isComparing.value) {
      stopCompare();
    }

    // 智能重置图层选择：选择一个与当前底图不同的图层
    const currentBaseType = getCurrentBaseLayerType();
    const availableOptions = availableLayers.value.filter(layer => layer.value !== currentBaseType);
    
    if (availableOptions.length > 0) {
      // 优先选择影像图层，如果影像图层是当前底图则选择第一个可用图层
      const preferredLayer = availableOptions.find(layer => layer.value === 'img') || availableOptions[0];
      compareLayer.value = preferredLayer.value;
    } else {
      // 如果没有可用选项，默认选择影像图层
      compareLayer.value = 'img';
    }

    ElMessage.success('设置已重置');
    
  } catch (error) {
    console.error('❌ [RollingCompare] 重置失败:', error);
    ElMessage.error('重置失败：' + (error as Error).message);
  }
};

/**
 * 监听图层选择变化
 */
watch(compareLayer, (newCompareLayer) => {
  console.log('🔄 [RollingCompare] 对比图层选择变化:', { 
    compareLayer: newCompareLayer
  });
});

/**
 * 监听当前底图变化，自动调整对比图层
 */
watch(
  () => getCurrentBaseLayerType(),
  (newBaseType, oldBaseType) => {
    if (newBaseType === oldBaseType) return;
    
    console.log('🔄 [RollingCompare] 检测到底图变化:', { from: oldBaseType, to: newBaseType });
    
    // 如果当前对比图层与新底图相同，自动选择一个不同的图层
    if (compareLayer.value === newBaseType) {
      const availableOptions = availableLayers.value.filter(layer => layer.value !== newBaseType);
      if (availableOptions.length > 0) {
        compareLayer.value = availableOptions[0].value;
        ElMessage.info(`检测到底图变化，已自动选择"${availableOptions[0].label}"作为对比图层`);
      }
    }
    
    // 如果正在对比中，提示用户底图已变化
    if (isComparing.value) {
      ElMessage.warning('检测到底图变化，建议重新开始对比以获得最佳效果');
    }
  }
);

/**
 * 监听其他工具激活，自动停止卷帘对比
 */
watch(
  () => operateToolStore.visualToolList,
  (newToolList) => {
    if (!isComparing.value) return;
    
    // 检查是否有其他工具被激活（除了卷帘对比本身）
    const activatedTools = newToolList.filter(tool => 
      tool.highLight === 1 && tool.path !== 'RollingCompare'
    );
    
    if (activatedTools.length > 0) {
      console.log('🔄 [RollingCompare] 检测到其他工具激活，自动停止卷帘对比:', activatedTools.map(t => t.name));
      stopCompare();
    }
  },
  { deep: true }
);

/**
 * 监听其他对话框打开，自动停止卷帘对比
 */
watch(
  () => dialogStore.mainComponents,
  (newComponents) => {
    if (!isComparing.value) return;
    
    // 检查是否有其他对话框被打开（除了卷帘对比本身）
    const otherDialogs = newComponents.filter(component => 
      component.show && component.name !== 'RollingCompare'
    );
    
    if (otherDialogs.length > 0) {
      console.log('🔄 [RollingCompare] 检测到其他对话框打开，自动停止卷帘对比:', otherDialogs.map(d => d.name));
      stopCompare();
    }
  },
  { deep: true }
);

/**
 * 监听路由变化，自动停止卷帘对比
 */
watch(
  () => route.path,
  (newPath, oldPath) => {
    if (isComparing.value && newPath !== oldPath) {
      console.log('🔄 [RollingCompare] 检测到路由变化，自动停止卷帘对比:', { from: oldPath, to: newPath });
      stopCompare();
    }
  }
);

/**
 * 组件挂载时初始化
 */
onMounted(() => {
  initDefaultCompareLayer();
});

/**
 * 组件卸载时清理
 */
onUnmounted(() => {
  if (isComparing.value) {
    stopCompare();
  }
});

/**
 * 关闭处理
 */
const close = (): void => {
  // 停止对比
  if (isComparing.value) {
    stopCompare();
  }

  // 关闭对话框
  useDialogStore().closeDialog("RollingCompare");
};
</script>

<style lang="scss" scoped>
.tip-container {
  margin-bottom: 15px;
}

.tip-text {
  font-size: 14px;
  color: #bfbfbf;
}

.layer-selection {
  margin-bottom: 20px;
  
  .layer-selector {
    margin-bottom: 15px;
    
    .layer-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      margin-bottom: 8px;
    }
    
    .layer-select {
      width: 100%;
    }
  }
  
  .compare-description {
    margin-top: 10px;
    
    .description-text {
      font-size: 12px;
      color: #e6a23c;
    }
  }
}

.status-descriptions {
  margin-bottom: 15px;
  --el-border-color-lighter: rgb(221, 221, 221);
}

.status-label {
  width: 100px;
  background-color: #f5f7fa;
  font-weight: bold;
}

.status-value {
  padding: 12px 15px;
}

.operation-tips {
  margin-top: 15px;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

:deep(.el-descriptions__label) {
  width: 100px;
  background: rgb(242, 246, 251);
}

:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  text-align: center;
  color: #2c3037;
  font-weight: 400;
  font-size: 14px;
}

:deep(.el-alert__title) {
  font-size: 14px;
}

:deep(.el-alert--info.is-light) {
  background: transparent;
  color: #bfbfbf;
}

:deep(.el-alert--success.is-light) {
  background: transparent;
  color: #67c23a;
}

:deep(.el-alert--warning.is-light) {
  background: transparent;
  color: #e6a23c;
}

.option-disabled-tip {
  color: #c0c4cc;
  font-size: 12px;
  margin-left: 8px;
}
</style> 