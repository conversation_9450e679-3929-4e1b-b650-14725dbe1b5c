# 图层顺序最佳实践

## 概述

本文档说明了在地图应用中正确的图层顺序安排，特别是管线和管点图层的相对位置关系。

## 图层顺序原则

### 🎯 核心原则

1. **点要素在线要素之上**：确保点标记不被线条遮挡
2. **注记在几何要素之上**：文字标签应该清晰可见
3. **交互要素在静态要素之上**：可点击的要素应该在最上层
4. **业务图层在底图之上**：所有业务数据都应该在底图之上

### 📊 标准图层顺序（从底到顶）

```
顶层 ↑
├── 8. 文字注记图层
│   ├── MVT注记图层 ({layerId}_label)
│   └── 自定义标签图层
├── 7. 交互式业务图层
│   ├── 其他业务图层
│   └── 临时绘制图层
├── 6. 设备聚合图层
│   ├── 设备展开图层 (device_{icon}_expanded_{id})
│   ├── 设备聚合数量图层 (device_{icon}_cluster_count)
│   ├── 设备聚合图层 (device_{icon}_clusters)
│   └── 设备主图层 (device_{icon})
├── 5. 管点图层 ⭐ 关键：在管线之上
│   └── 管点图层 (mvt_pipeNode)
├── 4. 管线图层
│   └── 管线图层 (mvt_pipeLine)
├── 3. 基础设备图层
│   └── 设备图层 (device-layer)
├── 2. 管网图层
│   ├── 管网节点图层 (pipe-nodes-layer)
│   ├── 管网线路图层 (pipe-lines-layer)
│   ├── 管网节点标签 (pipe-nodes-labels)
│   └── 管网线路标签 (pipe-lines-labels)
├── 1. 标绘图层
│   ├── 标绘点图层 (plot-features-layer-point)
│   ├── 标绘线图层 (plot-features-layer-line)
│   ├── 标绘面填充图层 (plot-features-layer-polygon-fill)
│   └── 标绘面边框图层 (plot-features-layer-polygon-stroke)
├── 底图注记图层
│   └── 天地图注记图层 (tdt-*-annotation)
└── 底图图层
    ├── 天地图底图 (tdt-*)
    ├── 百度地图底图 (baidu-*)
    └── 高德地图底图 (amap-*)
底层 ↓
```

## 管线与管点图层关系

### 🔍 为什么管点要在管线之上？

#### **视觉原因**：
- **可见性**：管点通常是圆形标记，如果在管线下方会被线条遮挡
- **识别性**：管点是重要的节点信息，需要清晰可见
- **交互性**：用户需要能够点击管点进行操作

#### **技术原因**：
- **事件响应**：上层图层优先响应鼠标事件
- **渲染顺序**：MapLibre按图层顺序渲染，后添加的图层在上方
- **样式优先级**：上层图层的样式不会被下层图层影响

### 📋 实际案例对比

#### **错误的图层顺序**：
```
管线图层 (mvt_pipeLine)     ← 在上方
管点图层 (mvt_pipeNode)     ← 在下方，被遮挡
```

**问题**：
- ❌ 管点被管线遮挡，难以看清
- ❌ 点击管点时可能误触管线
- ❌ 管点的重要信息无法突出显示

#### **正确的图层顺序**：
```
管点图层 (mvt_pipeNode)     ← 在上方，清晰可见
管线图层 (mvt_pipeLine)     ← 在下方，作为连接线
```

**优势**：
- ✅ 管点清晰可见，不被遮挡
- ✅ 点击交互准确响应
- ✅ 视觉层次清晰，符合用户认知

## 实现方式

### 🔧 BaseLayerManager实现

```typescript
// 在BaseLayerManager.ts中的正确顺序
const allBusinessLayerIds = [
  ...plotLayerIds,           // 1. 标绘图层
  ...pipeLayerIds,           // 2. 管网图层
  ...deviceLayerIds,         // 3. 设备图层
  ...pipeLineLayerIds,       // 4. 管线图层
  ...pipeNodeLayerIds,       // 5. 管点图层 ⭐ 在管线之上
  ...deviceClusterLayerIds,  // 6. 设备聚合图层
  ...mvtLabelLayerIds,       // 7. MVT注记图层
  ...businessLayerIds        // 8. 其他业务图层
]
```

### 🎯 切换底图时的处理

当切换底图时，`BaseLayerManager`会：

1. **检测所有业务图层**：识别当前地图上的所有业务图层
2. **按正确顺序排列**：将图层按预定义的顺序重新排列
3. **移动到顶层**：确保所有业务图层都在新底图之上
4. **保持相对顺序**：维护业务图层之间的正确层级关系

### 📊 调试验证

开启调试模式可以看到图层顺序：

```javascript
🔝 检测到需要置顶的业务图层（从底层到顶层）: {
  "1. 标绘图层": ["plot-features-layer-point"],
  "2. 管网图层": ["pipe-nodes-layer", "pipe-lines-layer"],
  "3. 设备图层": ["device-layer"],
  "4. 管线图层": ["mvt_pipeLine"],
  "5. 管点图层": ["mvt_pipeNode"],        // ⭐ 在管线之上
  "6. 设备聚合图层": ["device_pressure_clusters"],
  "7. MVT注记图层": ["pipeline_layer_label"],
  "8. 其他业务图层": ["custom_layer"]
}
```

## 最佳实践建议

### 🎨 图层设计原则

#### **1. 点线面分层**
```
面图层（填充） → 线图层 → 点图层
```

#### **2. 几何要素与注记分层**
```
几何要素图层 → 注记图层
```

#### **3. 静态与动态分层**
```
静态背景图层 → 动态业务图层 → 交互图层
```

### 🔧 开发建议

#### **1. 图层命名规范**
```typescript
// 推荐的命名模式
const layerNames = {
  pipeLine: 'mvt_pipeLine',      // 管线图层
  pipeNode: 'mvt_pipeNode',      // 管点图层
  pipeLabel: 'mvt_pipe_label',   // 管线注记
  nodeLabel: 'mvt_node_label'    // 管点注记
}
```

#### **2. 图层创建顺序**
```typescript
// 按从底到顶的顺序创建图层
await pipeLineLayer.addTo(map)  // 先添加管线
await pipeNodeLayer.addTo(map)  // 后添加管点
await labelLayer.addTo(map)     // 最后添加注记
```

#### **3. 动态调整**
```typescript
// 在需要时手动调整图层顺序
map.moveLayer('mvt_pipeNode', 'mvt_pipeLine')  // 将管点移到管线之上
```

### 🧪 测试验证

#### **1. 视觉测试**
- 检查管点是否清晰可见
- 验证管点不被管线遮挡
- 确认点击交互正常

#### **2. 功能测试**
```typescript
// 测试图层顺序
const layers = map.getStyle().layers
const pipeLineIndex = layers.findIndex(l => l.id === 'mvt_pipeLine')
const pipeNodeIndex = layers.findIndex(l => l.id === 'mvt_pipeNode')

expect(pipeNodeIndex).toBeGreaterThan(pipeLineIndex)
```

#### **3. 交互测试**
- 点击管点应该响应管点事件
- 点击管线应该响应管线事件
- 不应该出现事件冲突

## 常见问题解决

### 🐛 问题1：管点被管线遮挡

**症状**：管点图标看不清或完全被遮挡

**原因**：管点图层在管线图层下方

**解决方案**：
```typescript
// 确保管点图层在管线图层之上
map.moveLayer('mvt_pipeNode', 'mvt_pipeLine')

// 或者在BaseLayerManager中正确配置顺序
```

### 🐛 问题2：点击管点时触发管线事件

**症状**：点击管点时响应的是管线的点击事件

**原因**：管线图层在管点图层上方，拦截了点击事件

**解决方案**：
```typescript
// 调整图层顺序，确保管点在上方
await baseLayerManager.ensureLayerOrder()
```

### 🐛 问题3：切换底图后图层顺序混乱

**症状**：切换底图后，管点又被管线遮挡了

**原因**：底图切换时没有正确维护图层顺序

**解决方案**：
```typescript
// 使用完善的BaseLayerManager
const baseLayerManager = new BaseLayerManager({
  map: map,
  debug: true  // 开启调试查看图层顺序
})

await baseLayerManager.switchBasemap('new-basemap')
```

## 总结

正确的图层顺序是地图应用用户体验的重要组成部分。特别是管点图层必须在管线图层之上，这不仅是视觉设计的需要，也是功能交互的要求。

通过完善的 `BaseLayerManager`，我们确保了：

- 🎯 **管点始终在管线之上**：保证管点的可见性和可交互性
- 🔄 **切换底图时顺序不变**：维护正确的图层层级关系
- 📊 **清晰的调试信息**：便于开发和维护
- 🧪 **完整的测试覆盖**：确保功能的稳定性

这些改进为用户提供了一致、直观的地图交互体验。
