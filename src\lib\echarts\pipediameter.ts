export const initDiameterEchart = (xData: any, data: any) => {
  const option: any = {
    grid: {
      top: '20%',
      left: "5%",
      right: '5%',
      bottom: '5%',
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
        textStyle: {
          color: "#fff",
        },
      },
    },
    xAxis: {
      type: 'category',
      axisTick: { show: false },
      axisLine: {
        lineStyle: {
          color: "#F6F6F6",
        },
      },
      axisLabel: {
        interval: 0,
        textStyle: {
          color: "#2C3037",
        },
      },
      data: xData
    },
    yAxis: {

      type: 'value',
      name: "单位：米",
      nameTextStyle: {
        color: "#5C5F66",
        align: 'left',
        padding: [0, 0, 0, -53]
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#F6F6F6",
        },
      },
    },

    series: [
      {
        barWidth: 6,
        data: data,
        itemStyle: {
          color: '#1966FF'
        },
        type: 'bar'
      }
    ]
  };
  return option
}