/**
 * @fileoverview Vue Router路由配置主文件
 * @description 配置应用程序的路由系统，整合MapLibre和Cesium模块路由
 * <AUTHOR>
 * @version 1.0.0
 */

import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router';
import { maplibreRouter } from './maplibre';
import { cesiumRouter } from './cesium';
import { adminRouter } from './admin';

/**
 * @description 创建Vue Router实例
 * @details 配置HTML5历史模式和路由规则
 */
const router = createRouter({
  // 使用HTML5历史模式，基于import.meta.env.BASE_URL
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      redirect: "/gateway"
    },
    {
      path: "/gateway",
      name: "gateway",
      component: () => import('@/views/gateway/index.vue'),
    },
    {
      path: '/',
      name: 'Layout',
      redirect: '/maplibre',
      component: () => import('@/views/Layout.vue'),
      children: [
        // MapLibre二维地图模块路由
        maplibreRouter,
        // Cesium三维地图模块路由
        cesiumRouter,
        adminRouter
      ]
    }
  ]
});


export default router;
