<template>
  <el-select
    :class="normalClass"
    class="mwidht custom-vis-input custom-select role-selecct"
    popper-class="slct_popper"
    :multiple="multiple"
    :teleported="teleported"
    v-model="value"
    :placeholder="placeHolder"
  >
    <el-option
      v-for="item in options"
      :label="getLabel(item)"
      :value="getValue(item)"
    />
  </el-select>
</template>

<script lang="ts" setup>
import { computed, defineProps, defineEmits } from "vue";
const getLabel = (item: any) => {
  return item[props.label];
};
const getValue = (item: any) => item[props.value];
const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: "",
  },
  multiple: {
    type: [Boolean],
    default: false,
  },
  options: {
    type: [Array, Object],
    require: true,
  },
  label: {
    type: [String],
    default: "label",
  },
  value: {
    type: [String],
    default: "value",
  },
  popperClass: {
    type: String,
    default: "",
  },
  normalClass: {
    type: String,
    default: "",
  },
  teleported: {
    type: Boolean,
    default: false,
  },
  placeHolder: {
    type: String,
    default: "请选择",
  },
});
const emits = defineEmits(["update:modelValue"]);

let value = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emits("update:modelValue", val);
  },
});
</script>

<style lang="scss" scoped>
.custom__select {
  width: 100%;
}
</style>
