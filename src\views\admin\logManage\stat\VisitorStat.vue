<template>
  <div>
    <div>近七日访客统计</div>
    <div class="chart" ref="categoryRef"></div>
  </div>
</template>
<script lang="ts" setup>
import { initVisitorEchart } from "@/lib/echarts";
import { visitorDay } from "@/api/log";
const { initChart } = useEchart();
const categoryRef = ref();
const date = ref<any>([]);
const twoGis = ref<any>([]);
const threeGis = ref<any>([]);
const gisService = ref<any>([]);
const backgroundManage = ref<any>([]);
const getChart = async () => {
  const result = await visitorDay();
  if (result.code === 200) {
    result.data.forEach((item: any) => {
      date.value.push(item.date);
      twoGis.value.push(item.twoGis);
      threeGis.value.push(item.threeGis);
      gisService.value.push(item.gisService);
      backgroundManage.value.push(item.backgroundManage);
    });
  }
  categoryRef.value &&
    initChart(
      categoryRef.value,
      initVisitorEchart(
        date.value,
        twoGis.value,
        threeGis.value,
        gisService.value,
        backgroundManage.value
      )
    );
};
onMounted(() => {
  getChart();
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 240px;
}
</style>
