<template>
  <page-card :close-icon="false" class="flow-analysis" title="流向分析">
    <!-- 按钮区域 -->
    <el-row class="button-section" flex="~ row justify-start">
      <el-button
        type="primary"
        class="primary-btn h-9 w-100px"
        :loading="isSelectingStartPoint"
        @click="handleSelectStartPoint"
      >
        选择起点
      </el-button>
      <el-button
        type="primary"
        class="primary-btn h-9 w-100px"
        :loading="isSelectingEndPoint"
        @click="handleSelectEndPoint"
      >
        选择终点
      </el-button>
      <el-button
        type="primary"
        class="primary-btn h-9 w-100px"
        :loading="isAnalyzing"
        @click="handleAnalysis"
        :disabled="
          !startPoint ||
          !endPoint ||
          isSelectingStartPoint ||
          isSelectingEndPoint
        "
      >
        分析
      </el-button>
      <el-button
        class="clear-btn h-9 w-100px"
        @click="handleClear"
        :disabled="isAnalyzing || isSelectingStartPoint || isSelectingEndPoint"
      >
        清除
      </el-button>
    </el-row>
    <!-- 选择的点显示区域 -->
    <div
      v-if="startPoint || endPoint"
      grid="~ cols-2 gap-4"
      class="points-section"
    >
      <el-row v-if="startPoint" class="point-row">
        <el-col :span="24">
          <el-text class="point-label">已选择起点：</el-text>
          <div class="point-info">
            <span class="point-text">
              管点编号: {{ startPoint.gxddh || "无" }}
            </span>
            <span class="coordinate-text">
              坐标: {{ startPoint.lng.toFixed(6) }},
              {{ startPoint.lat.toFixed(6) }}
            </span>
          </div>
        </el-col>
      </el-row>
      <el-row v-if="endPoint" class="point-row">
        <el-col :span="24">
          <el-text class="point-label">已选择终点：</el-text>
          <div class="point-info">
            <span class="point-text">
              管点编号: {{ endPoint.gxddh || "无" }}
            </span>
            <span class="coordinate-text">
              坐标: {{ endPoint.lng.toFixed(6) }}, {{ endPoint.lat.toFixed(6) }}
            </span>
          </div>
        </el-col>
      </el-row>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
import type { MapEngineType } from "@/components/BufferAnalysisDrawButtons.vue";
import { queryConnectivity } from "@/api/analysis";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";

/**
 * 定义点坐标接口
 */
interface AnalysisPoint {
  lng: number;
  lat: number;
  gxddh?: string; // 管点编号
}

const route = useRoute();
const mapEngine = computed((): MapEngineType => {
  return route.path.includes("cesium") ? "cesium" : "maplibre";
});

/**
 * 响应式数据状态
 */
// 点选择相关状态
const startPoint = ref<AnalysisPoint | null>(null);
const endPoint = ref<AnalysisPoint | null>(null);
const isSelectingStartPoint = ref<boolean>(false);
const isSelectingEndPoint = ref<boolean>(false);
const isAnalyzing = ref<boolean>(false);

// 流向线图层管理
const flowLineLayerId = "flow-analysis-line";
const flowLineSourceId = "flow-analysis-source";

// 动画控制
let animationId: number | null = null;

/**
 * 从地图选择管点
 * @returns Promise<AnalysisPoint> 返回选择的管点信息
 */
const selectPointFromMap = (): Promise<AnalysisPoint> => {
  return new Promise((resolve, reject) => {
    try {
      const map = AppMaplibre.getMap();

      if (!map) {
        throw new Error("地图实例未初始化");
      }

      // 修改鼠标样式提示用户可以点击
      map.getCanvas().style.cursor = "crosshair";

      // 绑定单次点击事件
      map.once("click", (e: any) => {
        try {
          // 恢复默认鼠标样式
          map.getCanvas().style.cursor = "";

          // 查询点击位置的管点要素
          const features = map.queryRenderedFeatures(e.point, {
            layers: ["mvt_pipeNode"],
          });

          if (features.length === 0) {
            throw new Error("请点击管点位置");
          }

          const feature: any = features[0];
          const gxddh = feature.properties?.gxddh;

          if (!gxddh) {
            throw new Error("该管点缺少编号信息");
          }

          const point: AnalysisPoint = {
            lng: feature.geometry.coordinates[0] as number,
            lat: feature.geometry.coordinates[1] as number,
            gxddh: gxddh,
          };

          resolve(point);
        } catch (error) {
          // 恢复默认鼠标样式
          map.getCanvas().style.cursor = "";
          reject(error);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * 参考FlowAnalysisCard.vue的addFlowLine方法添加带箭头的流向线
 * @param geojsonData 流向分析结果数据
 */
const addFlowLine = async (geojsonData: any) => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      console.error("地图实例不存在");
      return;
    }

    // 清除之前的流向线
    clearFlowLine();

    // 添加数据源
    map.addSource(flowLineSourceId, {
      type: "geojson",
      data: geojsonData,
    });

    // 添加主线图层（橙色粗线）
    map.addLayer({
      type: "line",
      source: flowLineSourceId,
      id: flowLineLayerId,
      layout: {
        "line-join": "round",
        "line-cap": "round",
      },
      paint: {
        "line-color": "#ff6600", // 橙色流向线
        "line-width": 6,
      },
    });

    // 添加动画虚线图层（白色虚线）
    map.addLayer({
      type: "line",
      source: flowLineSourceId,
      id: "flow-line-dashed",
      paint: {
        "line-color": "white",
        "line-width": 2,
        "line-dasharray": [0, 3, 3],
      },
    });

    // 启动虚线动画
    startDashAnimation();

    // 添加箭头图标
    await addArrowIcon(geojsonData);

    console.log("已添加带箭头的流向分析结果线");
  } catch (error) {
    console.error("添加流向线失败:", error);
  }
};

/**
 * 清除流向线
 */
const clearFlowLine = () => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) return;

    // 停止动画
    if (animationId) {
      cancelAnimationFrame(animationId);
      animationId = null;
    }

    // 删除图层
    if (map.getLayer(flowLineLayerId)) {
      map.removeLayer(flowLineLayerId);
    }
    if (map.getLayer("flow-line-dashed")) {
      map.removeLayer("flow-line-dashed");
    }
    if (map.getLayer("flow-arrow-layer")) {
      map.removeLayer("flow-arrow-layer");
    }

    // 删除数据源
    if (map.getSource(flowLineSourceId)) {
      map.removeSource(flowLineSourceId);
    }
    if (map.getSource("flow-arrow-source")) {
      map.removeSource("flow-arrow-source");
    }

    // 删除箭头图标
    if (map.hasImage("flow-arrow")) {
      map.removeImage("flow-arrow");
    }

    console.log("已清除流向线");
  } catch (error) {
    console.error("清除流向线失败:", error);
  }
};

/**
 * 启动虚线动画
 */
const startDashAnimation = () => {
  const map = AppMaplibre.getMap();
  if (!map) return;

  const dashArraySequence = [
    [0, 3, 3],
    [0.5, 4, 2.5],
    [1, 4, 2],
    [1.5, 4, 1.5],
    [2, 4, 1],
    [2.5, 4, 0.5],
    [3, 4, 0],
    [0, 0.5, 3, 3.5],
    [0, 1, 3, 3],
    [0, 1.5, 3, 2.5],
    [0, 2, 3, 2],
    [0, 2.5, 3, 1.5],
    [0, 3, 3, 1],
    [0, 3.5, 3, 0.5],
  ];

  let step = 0;

  function animateDashArray(timestamp: number) {
    // 使用dashArraySequence中的下一个值更新line-dasharray
    const newStep = parseInt(
      ((timestamp / 200) % dashArraySequence.length) as any
    );

    if (newStep !== step && map.getLayer("flow-line-dashed")) {
      map.setPaintProperty(
        "flow-line-dashed",
        "line-dasharray",
        dashArraySequence[step]
      );
      step = newStep;
    }

    // 请求动画的下一帧
    animationId = requestAnimationFrame(animateDashArray);
  }

  // 开始动画
  animationId = requestAnimationFrame(animateDashArray);
};

/**
 * 创建箭头SVG图标
 */
const createArrowSVG = (): string => {
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path d="M10 2 L18 10 L10 18 L10 14 L2 14 L2 6 L10 6 Z" fill="#ff6600" stroke="#ffffff" stroke-width="1"/>
    </svg>
  `)}`;
};

/**
 * 添加箭头图标
 * @param geojsonData 流向分析结果数据
 */
const addArrowIcon = async (geojsonData: any) => {
  const map = AppMaplibre.getMap();
  if (!map) return;

  // 清除之前的箭头图层
  if (map.getSource("flow-arrow-source")) {
    if (map.getLayer("flow-arrow-layer")) {
      map.removeLayer("flow-arrow-layer");
    }
    map.removeSource("flow-arrow-source");
  }
  if (map.hasImage("flow-arrow")) {
    map.removeImage("flow-arrow");
  }

  try {
    // 创建箭头SVG图标
    const arrowSVG = createArrowSVG();
    const response = await fetch(arrowSVG);
    const blob = await response.blob();
    const image = await createImageBitmap(blob);

    map.addImage("flow-arrow", image);

    map.addSource("flow-arrow-source", {
      type: "geojson",
      data: geojsonData,
    });

    map.addLayer({
      id: "flow-arrow-layer",
      type: "symbol",
      source: "flow-arrow-source",
      layout: {
        "symbol-placement": "line",
        "symbol-spacing": 80, // 箭头间隔
        "icon-image": "flow-arrow", // 箭头图标
        "icon-size": 0.8,
        "icon-allow-overlap": true,
      },
    });

    console.log("已添加箭头图标");
  } catch (error) {
    console.error("加载箭头图标失败:", error);
    // 如果箭头图标加载失败，使用文本箭头作为备用方案
    addTextArrows(geojsonData);
  }
};

/**
 * 备用方案：使用文本箭头
 * @param geojsonData 流向分析结果数据
 */
const addTextArrows = (geojsonData: any) => {
  const map = AppMaplibre.getMap();
  if (!map) return;

  try {
    map.addSource("flow-arrow-source", {
      type: "geojson",
      data: geojsonData,
    });

    map.addLayer({
      id: "flow-arrow-layer",
      type: "symbol",
      source: "flow-arrow-source",
      layout: {
        "symbol-placement": "line",
        "symbol-spacing": 60,
        "text-field": "→", // Unicode箭头
        "text-size": 16,
        "text-allow-overlap": true,
      },
      paint: {
        "text-color": "#ff6600",
        "text-halo-color": "#ffffff",
        "text-halo-width": 1,
      },
    });

    console.log("已添加文本箭头");
  } catch (error) {
    console.error("添加文本箭头失败:", error);
  }
};

/**
 * 处理选择起点
 */
const handleSelectStartPoint = async () => {
  isSelectingStartPoint.value = true;
  try {
    ElMessage.info("请在地图上点击管点选择起点");

    const selectedPoint = await selectPointFromMap();
    startPoint.value = selectedPoint;

    ElMessage.success(`已选择起点：${selectedPoint.gxddh}`);
  } catch (error) {
    console.error("选择起点失败:", error);
    ElMessage.error(
      `选择起点失败：${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isSelectingStartPoint.value = false;
  }
};

/**
 * 处理选择终点
 */
const handleSelectEndPoint = async () => {
  isSelectingEndPoint.value = true;
  try {
    ElMessage.info("请在地图上点击管点选择终点");

    const selectedPoint = await selectPointFromMap();

    // 检查是否与起点相同
    if (startPoint.value && selectedPoint.gxddh === startPoint.value.gxddh) {
      throw new Error("终点不能与起点相同");
    }

    endPoint.value = selectedPoint;

    ElMessage.success(`已选择终点：${selectedPoint.gxddh}`);
  } catch (error) {
    console.error("选择终点失败:", error);
    ElMessage.error(
      `选择终点失败：${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isSelectingEndPoint.value = false;
  }
};

/**
 * 处理流向分析
 */
const handleAnalysis = async () => {
  if (!startPoint.value || !endPoint.value) {
    ElMessage.warning("请先选择起点和终点");
    return;
  }

  if (!startPoint.value.gxddh || !endPoint.value.gxddh) {
    ElMessage.warning("起点或终点缺少管点编号信息");
    return;
  }

  isAnalyzing.value = true;
  try {
    // 调用流向分析API
    const response = await queryConnectivity(
      startPoint.value.gxddh,
      endPoint.value.gxddh
    );

    console.log("流向分析API响应:", response);

    if (response && response.status === 200) {
      const featureCollection = response.data;

      if (
        featureCollection &&
        featureCollection.features &&
        featureCollection.features.length > 0
      ) {
        // 在地图上显示流向分析结果
        addFlowLine(featureCollection);

        ElMessage.success(
          `流向分析完成，找到 ${featureCollection.features.length} 条流向路径`
        );
      } else {
        ElMessage.info("未找到流向路径");
      }
    } else {
      throw new Error(response?.data?.message || "分析失败");
    }
  } catch (error) {
    console.error("流向分析失败:", error);
    ElMessage.error(
      `分析失败：${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isAnalyzing.value = false;
  }
};

/**
 * 处理清除操作
 */
const handleClear = () => {
  // 清除数据状态
  startPoint.value = null;
  endPoint.value = null;

  // 清除地图图层
  clearFlowLine();

  ElMessage.info("已清除");
};

/**
 * 组件卸载时清理资源
 */
onUnmounted(() => {
  clearFlowLine();
  console.log("流向分析组件已卸载，清理完成");
});
</script>

<style scoped lang="scss">
.flow-analysis {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 550px;
  min-height: 130px;
  z-index: 1000;
}

.points-section {
  margin: 20px 0;
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 6px;

  .point-row {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .point-label {
    color: #2c3037;
    font-size: 14px;
    margin-right: 8px;
  }

  .point-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .point-text {
    color: #1890ff;
    font-size: 14px;
    font-weight: 500;
  }

  .coordinate-text {
    color: #666;
    font-size: 12px;
  }
}
// .select-btn {
//   width: 100%;
//   background-color: #1890ff;
//   border-color: #1890ff;
//   font-size: 13px;
//   padding: 8px 12px;

//   &:hover {
//     background-color: #40a9ff;
//     border-color: #40a9ff;
//   }

//   &:disabled {
//     background-color: #d9d9d9;
//     border-color: #d9d9d9;
//   }
// }

// .clear-btn {
//   width: 100%;
//   background-color: #8c8c8c;
//   border-color: #8c8c8c;
//   color: white;
//   font-size: 13px;
//   padding: 8px 12px;

//   &:hover {
//     background-color: #a8a8a8;
//     border-color: #a8a8a8;
//   }

//   &:disabled {
//     background-color: #d9d9d9;
//     border-color: #d9d9d9;
//   }
// }
</style>
