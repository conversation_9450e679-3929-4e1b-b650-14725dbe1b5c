import hRequest from '@/utils/http'
import type { DataType } from '@/utils/http/types'
export const anglePage = (params: any) => {
  return hRequest.get<DataType>({
    url: '/business/visual/angle/page',
    params
  })
}
// 新增
export const angleAdd = (data: any) => {
  return hRequest.post<DataType>({
    url: '/business/visual/angle',
    data
  })
}
// 修改
export const angleEdit = (data: any) => {
  return hRequest.put<DataType>({
    url: '/business/visual/angle',
    data
  })
}
// 详情
export const angleDetail = (id: string) => {
  return hRequest.get<DataType>({
    url: `/business/visual/angle/${id}`,
  })
}
// 删除
export const angleDelete = (id: string) => {
  return hRequest.delete<DataType>({
    url: `/business/visual/angle/${id}`,
  })
}
