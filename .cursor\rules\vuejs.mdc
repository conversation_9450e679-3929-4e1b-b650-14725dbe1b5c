---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: false
---
---
description: Vue.js 编码规则和最佳实践
globs: **/*.vue
alwaysApply: false
---

# Vue.js 规则

## 组件结构
- 使用组合式 API 而非选项式 API
- 保持组件小巧且专注
- 正确集成 TypeScript
- 实现适当的 props 验证
- 使用正确的 emit 声明
- 保持模板逻辑简洁

## 组合式 API
- 正确使用 ref 和 reactive
- 实现适当的生命周期钩子
- 使用 composables 实现可复用逻辑
- 保持 setup 函数整洁
- 正确使用计算属性
- 实现适当的侦听器

## 状态管理
- 使用 Pinia 进行状态管理
- 保持 stores 模块化
- 使用适当的状态组合
- 实现适当的 actions
- 正确使用 getters
- 适当处理异步状态

## 性能
- 正确使用组件懒加载
- 实现适当的缓存
- 正确使用计算属性
- 避免不必要的侦听器
- 正确使用 v-show 与 v-if
- 实现适当的 key 管理

## 路由
- 正确使用 Vue Router
- 实现适当的导航守卫
- 正确使用路由元字段
- 适当处理路由参数
- 实现适当的懒加载
- 使用适当的导航方法

## 表单
- 正确使用 v-model
- 实现适当的验证
- 适当处理表单提交
- 显示适当的加载状态
- 使用适当的错误处理
- 实现适当的表单重置

## TypeScript 集成
- 使用适当的组件类型定义
- 实现适当的 prop 类型
- 使用适当的 emit 声明
- 处理适当的类型推断
- 使用适当的 composable 类型
- 实现适当的 store 类型

## 测试
- 编写适当的单元测试
- 实现适当的组件测试
- 正确使用 Vue Test Utils
- 适当测试 composables
- 实现适当的模拟
- 测试异步操作

## 最佳实践
- 遵循 Vue 风格指南
- 使用适当的命名约定
- 保持组件组织有序
- 实现适当的错误处理
- 使用适当的事件处理
- 为复杂逻辑编写文档

## 构建和工具
- 使用 Vite 进行开发
- 配置适当的构建设置
- 正确使用环境变量
- 实现适当的代码分割
- 使用适当的资源处理
- 配置适当的优化