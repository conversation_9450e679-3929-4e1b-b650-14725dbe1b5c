/**
 * @fileoverview 图层组实现
 * @description 图层组管理类，支持嵌套图层结构和批量操作
 * <AUTHOR>
 * @version 2.0.0
 */

import { type LayerItem } from "../type/LayerItem";
import { BaseLayer } from "./BaseLayer";
import { LayerFactory } from "./LayerFactory";
import { Map as glMap } from "maplibre-gl";
import { 
  LayerType, 
  LayerStatus,
  type LayerConfig,
  type LayerGroupConfig,
  type LayerLoadOptions,
  type LayerEventData
} from '../types/LayerTypes'
/**
 * @class SortedMap
 * @description 有序映射类，维护插入顺序的键值对集合
 * @template K - 键类型
 * @template V - 值类型
 */
class SortedMap<K, V> {
  private _map = new Map<K, V>()
  private _list: K[] = []

  /**
   * @description 添加键值对
   * @param key - 键
   * @param value - 值
   */
  add(key: K, value: V): void {
    if (!this._map.has(key)) {
      this._list.push(key)
    }
    this._map.set(key, value)
  }

  /**
   * @description 获取所有值的数组
   * @returns 值数组
   */
  values(): (V | undefined)[] {
    return this._list.map(key => this._map.get(key))
  }

  /**
   * @description 获取所有键的数组
   * @returns 键数组
   */
  keys(): K[] {
    return [...this._list]
  }

  /**
   * @description 根据键获取值
   * @param key - 键
   * @returns 值或undefined
   */
  get(key: K): V | undefined {
    return this._map.get(key)
  }

  /**
   * @description 删除键值对
   * @param key - 键
   * @returns 是否删除成功
   */
  delete(key: K): boolean {
    const index = this._list.findIndex(k => k === key)
    if (index > -1) {
      this._list.splice(index, 1)
      return this._map.delete(key)
    }
    return false
  }

  /**
   * @description 在指定位置插入键值对
   * @param index - 插入位置
   * @param key - 键
   * @param value - 值
   */
  insert(index: number, key: K, value: V): void {
    // 如果键已存在，先删除
    this.delete(key)
    
    // 插入到指定位置
    this._list.splice(index, 0, key)
    this._map.set(key, value)
  }

  /**
   * @description 交换两个位置的元素
   * @param index1 - 位置1
   * @param index2 - 位置2
   */
  swap(index1: number, index2: number): void {
    if (index1 >= 0 && index1 < this._list.length && 
        index2 >= 0 && index2 < this._list.length) {
      [this._list[index1], this._list[index2]] = [this._list[index2], this._list[index1]]
    }
  }

  /**
   * @description 查找键的索引
   * @param predicate - 查找条件
   * @returns 索引，未找到返回-1
   */
  findIndex(predicate: (value: K, index: number, obj: K[]) => boolean): number {
    return this._list.findIndex(predicate)
  }

  /**
   * @description 获取集合大小
   * @returns 大小
   */
  get size(): number {
    return this._list.length
  }

  /**
   * @description 清空集合
   */
  clear(): void {
    this._list.length = 0
    this._map.clear()
  }

  /**
   * @description 检查是否包含指定键
   * @param key - 键
   * @returns 是否包含
   */
  has(key: K): boolean {
    return this._map.has(key)
  }

  /**
   * @description 遍历所有键值对
   * @param callback - 回调函数
   */
  forEach(callback: (value: V, key: K, index: number) => void): void {
    this._list.forEach((key, index) => {
      const value = this._map.get(key)
      if (value !== undefined) {
        callback(value, key, index)
      }
    })
  }
}
/**
 * @class LayerGroup
 * @description 图层组类，管理多个子图层的容器
 * @extends BaseLayer
 */
export class LayerGroup extends BaseLayer {
  protected declare _delegate: SortedMap<string, BaseLayer>
  private _expanded: boolean = true
  private _loadingChildren: Set<string> = new Set()

  /**
   * @constructor
   * @param options - 图层组配置
   */
  constructor(options: LayerGroupConfig | any) {
    super(options)
    this._expanded = options.expanded !== false
    
    // 确保 children 数组存在
    if (!this._options.children) {
      this._options.children = []
    }
  }
  /**
   * @description 图层组内部添加实现
   * @param map - MapLibre地图实例
   * @param isRefresh - 是否为刷新操作
   * @param options - 加载选项
   * @returns 图层代理对象
   */
  async addToInternal(map: glMap, isRefresh?: boolean, options?: LayerLoadOptions): Promise<SortedMap<string, BaseLayer>> {
    try {
      const startTime = performance.now()
      
      // 初始化代理对象
      this._delegate = new SortedMap<string, BaseLayer>()
      
      // 并行加载子图层（如果配置允许）
      const loadPromises: Promise<void>[] = []
      
      for (const child of this._options.children || []) {
        const loadPromise = this.loadChildLayer(child, map, options)
        
        if (options?.async !== false) {
          loadPromises.push(loadPromise)
        } else {
          await loadPromise
        }
      }
      
      // 等待所有异步加载完成
      if (loadPromises.length > 0) {
        await Promise.allSettled(loadPromises)
      }
      
      // 更新性能指标
      this._performanceMetrics.loadTime = performance.now() - startTime
      this._performanceMetrics.tileSuccessRate = this.calculateSuccessRate()
      
      this.emit('loaded', {
        layerId: this._id,
        type: 'loaded',
        data: { 
          childCount: this._delegate.size,
          loadTime: this._performanceMetrics.loadTime
        },
        timestamp: Date.now()
      })
      
      return this._delegate
    } catch (error) {
      this._performanceMetrics.errorCount = (this._performanceMetrics.errorCount || 0) + 1
      console.error(`图层组 ${this._name} 加载失败:`, error)
      
      this.emit('error', {
        layerId: this._id,
        type: 'error',
        data: error,
        timestamp: Date.now()
      })
      
      throw error
    }
  }

  /**
   * @description 加载子图层
   * @param child - 子图层配置
   * @param map - 地图实例
   * @param options - 加载选项
   * @private
   */
  private async loadChildLayer(child: LayerConfig, map: glMap, options?: LayerLoadOptions): Promise<void> {
    try {
      this._loadingChildren.add(child.id || child.title || 'unknown')
      
      let layer: BaseLayer
      
      // 根据配置创建图层
      if (child.children && Array.isArray(child.children) && child.children.length > 0) {
        layer = LayerFactory.createLayer({ ...child, type: LayerType.GROUP })
      } else {
        layer = LayerFactory.createLayer(child)
      }
      
      if (layer) {
        // 设置事件监听器
        this.setupChildEventListeners(layer)
        
        // 添加到代理对象
        this._delegate.add(layer.id, layer)
        
        // 添加到地图
        await layer.addTo(map, this, options)
      }
    } catch (error) {
      console.error(`加载子图层失败:`, error)
      throw error
    } finally {
      this._loadingChildren.delete(child.id || child.title || 'unknown')
    }
  }

  /**
   * @description 设置子图层事件监听器
   * @param layer - 子图层
   * @private
   */
  private setupChildEventListeners(layer: BaseLayer): void {
    // 转发子图层事件
    layer.on('loaded', (event) => this.emit('childLoaded', event))
    layer.on('error', (event) => this.emit('childError', event))
    layer.on('visibilityChanged', (event) => {
      this.emit('childVisibilityChanged', event)
      // 更新图层组的显示状态
      this.updateGroupVisibility()
    })
  }

  /**
   * @description 计算成功率
   * @returns 成功率
   * @private
   */
  private calculateSuccessRate(): number {
    if (this._delegate.size === 0) return 1
    
    let successCount = 0
    this._delegate.forEach((layer) => {
      if (layer.status === LayerStatus.LOADED) {
        successCount++
      }
    })
    
    return successCount / this._delegate.size
  }

  /**
   * @description 更新图层组的显示状态
   * @private
   */
  private updateGroupVisibility(): void {
    // 如果有任何子图层可见，则图层组可见
    let hasVisibleChild = false
    this._delegate.forEach((layer) => {
      if (layer.show) {
        hasVisibleChild = true
      }
    })
    
    // 更新图层组状态但不触发子图层变化
    const oldShow = this._show
    this._show = hasVisibleChild
    
    if (oldShow !== this._show) {
      this.emit('visibilityChanged', {
        layerId: this._id,
        type: 'visibilityChanged',
        data: { show: this._show },
        timestamp: Date.now()
      })
    }
  }
  /**
   * @description 飞行到图层组范围（图层组不支持此操作）
   */
  flyTo(): void {
    console.warn('图层组不支持飞行到范围操作')
    return
  }

  /**
   * @description 设置图层组及所有子图层的显示状态
   * @param show - 是否显示
   */
  set show(show: boolean) {
    if (this._show !== show) {
      this._show = show
      
      // 设置所有子图层的显示状态
      if (this._delegate) {
        this._delegate.forEach((layer) => {
          if (layer) {
            layer.show = this._show
          }
        })
      }
      
      this.emit('visibilityChanged', {
        layerId: this._id,
        type: 'visibilityChanged',
        data: { show: this._show },
        timestamp: Date.now()
      })
    }
  }

  /**
   * @description 获取图层组的显示状态
   * @returns 是否显示
   */
  get show(): boolean {
    return this._show
  }

  /**
   * @description 获取是否展开
   * @returns 是否展开
   */
  get expanded(): boolean {
    return this._expanded
  }

  /**
   * @description 设置是否展开
   * @param expanded - 是否展开
   */
  set expanded(expanded: boolean) {
    if (this._expanded !== expanded) {
      this._expanded = expanded
      
      this.emit('expandedChanged', {
        layerId: this._id,
        type: 'expandedChanged',
        data: { expanded: this._expanded },
        timestamp: Date.now()
      })
    }
  }
  findLayer(layerId: string): BaseLayer | null {
    if (this._id === layerId) {
      return this;
    }
    for (const layer of this._delegate.values()) {
      const value = (layer as BaseLayer).findLayer(layerId);
      if (value) {
        return value;
      }
    }
    return null;
  }

  getLayerTree(): LayerItem {
    const children: LayerItem[] = [];
    for (const layer of this._delegate.values()) {
      children.push((layer as BaseLayer).getLayerTree());
    }
    return {
      id: this._id,
      name: this._name,
      show: !!children.find((l) => l.show),
      type: "group",
      children,
      showLayerControl: this._options.showLayerControl,
      icon: this._options.icon
    };
  }
  /**
   * 移除子图层
   * @param layerId
   */
  removeChild(layerId: string) {
    const layer = this._delegate.get(layerId);
    if (layer) {
      layer.removeInternal();
      this._delegate.delete(layerId);
    }
  }
  removeInternal() {
    const keys = [...this._delegate.keys()];
    for (const layer of keys) {
      this.removeChild(layer);
    }
  }
  /**
   * 添加图层
   * @param layer
   */
  addLayer(layer: BaseLayer, index?: number) {
    layer.parent = this;
    if (index === 0 || index) {
      this._delegate.insert(index, layer.id, layer);
    } else {
      this._delegate.add(layer.id, layer);
    }
  }
  /**
   * 移除图层
   * @param layerId
   */
  removeLayer(layerId: string) {
    this._delegate.delete(layerId);
  }
  /**
   * 移动图层
   * @param layerId 图层id
   * @param target 目标图层
   */
  moveLayer(layerId: string, target: LayerGroup, index?: any) {
    if (this._id === target.id) {
      const index1 = this._delegate.findIndex((i) => i === layerId);
      this._delegate.swap(index1, index);
    } else {
      const layer = this._delegate.get(layerId);
      target.addLayer(layer as any, index);
      this.removeLayer(layerId);
    }
  }
  insertLayer(index: number, layer: BaseLayer) {
    layer.parent = this;
    this._delegate.insert(index, layer.id, layer);
  }
  /**
   * @description 添加新图层
   * @param options - 图层配置
   * @param index - 插入位置
   * @param loadOptions - 加载选项
   * @returns 创建的图层实例
   */
  async addNewLayer(options: LayerConfig, index?: number, loadOptions?: LayerLoadOptions): Promise<BaseLayer> {
    try {
      let layer: BaseLayer
      
      // 根据配置创建图层
      if (options.children && Array.isArray(options.children) && options.children.length > 0) {
        layer = LayerFactory.createLayer({ ...options, type: LayerType.GROUP })
      } else {
        layer = LayerFactory.createLayer(options)
      }
      
      if (layer) {
        // 设置事件监听器
        this.setupChildEventListeners(layer)
        
        // 添加到指定位置
        if (typeof index === 'number') {
          this._delegate.insert(index, layer.id, layer)
        } else {
          this._delegate.add(layer.id, layer)
        }
        
        // 添加到地图
        if (this._map) {
          await layer.addTo(this._map, this, loadOptions)
        }
        
        this.emit('childAdded', {
          layerId: this._id,
          type: 'childAdded',
          data: { childId: layer.id, index },
          timestamp: Date.now()
        })
      }
      
      return layer
    } catch (error) {
      console.error(`添加新图层失败:`, error)
      throw error
    }
  }
  findLayers(predicate: (layer: BaseLayer) => boolean): BaseLayer[] | any[] {
    const layers = [];
    if (this._delegate) {
      for (const layer of this._delegate.values()) {
        if (predicate(layer as BaseLayer)) {
          layers.push(layer);
        }
        if (layer instanceof LayerGroup) {
          layers.push(...layer.findLayers(predicate));
        }
      }
    }
    return layers;
  }

  findLayerByPredicate(predicate: (layer: BaseLayer) => boolean): BaseLayer | null {
    if (predicate(this)) {
      return this;
    }
    for (const layer of this._delegate.values()) {
      if (layer instanceof LayerGroup) {
        const value = layer.findLayerByPredicate(predicate);
        if (value) {
          return value;
        }
      } else if (predicate(layer as BaseLayer)) {
        return layer as BaseLayer;
      }
    }
    return null; 
  }
}
