<template>
  <div class="page-card">
    <header>
      <el-text class="card-title">{{ title }}</el-text>
      <el-button @click="close" v-if="closeIcon" link>
        <el-icon :size="18" color="#A8B5D8">
          <CloseBold />
        </el-icon>
      </el-button>
    </header>
    <main>
      <el-scrollbar height="100%">
        <slot></slot>
      </el-scrollbar>
    </main>
  </div>
</template>

<script setup lang="ts">
import { CloseBold } from '@element-plus/icons-vue'

withDefaults(defineProps<{ title: string, closeIcon?: boolean }>(), {
  closeIcon: true
})

const emit = defineEmits(['closeCard'])
const close = () => {
  emit('closeCard')
}
</script>

<style scoped lang="scss">
.page-card {
  border-radius: 6px;
  background-color: #fff;
  box-shadow: 0 0 12px 3px rgba($color: #A9BEE8, $alpha: 0.4);
  z-index: 10
}

header {
  height: 44px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  border-bottom: 1px solid #EEEEEE;
}

.card-title {
  color: var(--el-text-color-primary);
  font-size: 16px;
}

main {
  height: calc(100% - 45px);
  box-sizing: border-box;
  padding: 20px 8px 20px 20px;
}

.el-scrollbar {
  padding-right: 12px;
}
</style>