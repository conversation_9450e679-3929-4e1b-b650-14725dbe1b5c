/** * @Description: 隧道模拟组件 - Three.js + Cesium 集成版本 * @Date:
2025-01-10 * @Author: 项目开发团队 * @LastEditTime: 2025-01-10 * @Features: * -
仅支持三维(Cesium)环境 * - 使用Three.js创建复杂隧道几何体 * - 转换为Cesium
Primitive进行渲染 * - 支持圆形、矩形、马蹄形隧道 * - 空间冲突检测和可视化 */
<template>
  <page-card
    :close-icon="false"
    class="tabulate-sta"
    title="隧道模拟 (Three.js版)"
  >
    <!-- 参数设置区域 -->
    <div class="parameter-section">
      <!-- 隧道形状选择 -->
      <div class="input-section">
        <el-text class="label-text">隧道形状：</el-text>
        <div class="input-container">
          <el-select
            v-model="tunnelShape"
            placeholder="请选择隧道形状"
            class="shape-select"
            :disabled="isDrawing || isAnalyzing"
          >
            <el-option label="圆形" value="circle" />
            <el-option label="矩形" value="rectangle" />
            <el-option label="马蹄形" value="horseshoe" />
          </el-select>
        </div>
      </div>

      <!-- 直径/尺寸设置 -->
      <div class="input-section" v-if="tunnelShape === 'circle'">
        <el-text class="label-text">隧道直径：</el-text>
        <div class="input-container">
          <el-input-number
            v-model="tunnelDiameter"
            placeholder="请输入隧道直径"
            :min="1"
            :max="50"
            :step="0.5"
            controls-position="right"
            class="requirement-input"
            :disabled="isDrawing || isAnalyzing"
          />
          <span class="unit-text">(m)</span>
        </div>
      </div>

      <!-- 矩形尺寸设置 -->
      <template v-if="tunnelShape === 'rectangle'">
        <div class="input-section">
          <el-text class="label-text">隧道宽度：</el-text>
          <div class="input-container">
            <el-input-number
              v-model="tunnelWidth"
              placeholder="请输入隧道宽度"
              :min="1"
              :max="50"
              :step="0.5"
              controls-position="right"
              class="requirement-input"
              :disabled="isDrawing || isAnalyzing"
            />
            <span class="unit-text">(m)</span>
          </div>
        </div>
        <div class="input-section">
          <el-text class="label-text">隧道高度：</el-text>
          <div class="input-container">
            <el-input-number
              v-model="tunnelHeight"
              placeholder="请输入隧道高度"
              :min="1"
              :max="50"
              :step="0.5"
              controls-position="right"
              class="requirement-input"
              :disabled="isDrawing || isAnalyzing"
            />
            <span class="unit-text">(m)</span>
          </div>
        </div>
      </template>

      <!-- 马蹄形尺寸设置 -->
      <template v-if="tunnelShape === 'horseshoe'">
        <div class="input-section">
          <el-text class="label-text">隧道宽度：</el-text>
          <div class="input-container">
            <el-input-number
              v-model="tunnelWidth"
              placeholder="请输入隧道宽度"
              :min="1"
              :max="50"
              :step="0.5"
              controls-position="right"
              class="requirement-input"
              :disabled="isDrawing || isAnalyzing"
            />
            <span class="unit-text">(m)</span>
          </div>
        </div>
        <div class="input-section">
          <el-text class="label-text">隧道高度：</el-text>
          <div class="input-container">
            <el-input-number
              v-model="tunnelHeight"
              placeholder="请输入隧道高度"
              :min="(tunnelWidth / 2) * 1.2"
              :max="50"
              :step="0.5"
              controls-position="right"
              class="requirement-input"
              :disabled="isDrawing || isAnalyzing"
            />
            <span class="unit-text">(m)</span>
          </div>
          <!-- 马蹄形参数提示 -->
          <div v-if="horseshoeHeightWarning" class="parameter-warning">
            <el-icon><Warning /></el-icon>
            <span>{{ horseshoeHeightWarning }}</span>
          </div>
        </div>
      </template>

      <!-- 埋深设置 -->
      <div class="input-section">
        <el-text class="label-text">隧道埋深：</el-text>
        <div class="input-container">
          <el-input-number
            v-model="tunnelDepth"
            placeholder="请输入隧道埋深"
            :min="1"
            :max="200"
            :step="1"
            controls-position="right"
            class="requirement-input"
            :disabled="isDrawing || isAnalyzing"
          />
          <span class="unit-text">(m)</span>
        </div>
      </div>

      <!-- Three.js特有设置 -->
      <div class="input-section">
        <el-text class="label-text">几何体细分：</el-text>
        <div class="input-container">
          <el-input-number
            v-model="geometrySubdivisions"
            placeholder="几何体细分级别"
            :min="8"
            :max="64"
            :step="8"
            controls-position="right"
            class="requirement-input"
            :disabled="isDrawing || isAnalyzing"
          />
          <span class="unit-text">(段)</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="button-section">
      <el-button
        type="primary"
        class="primary-btn h-9 w-130px"
        :loading="isDrawing"
        @click="handleDrawPath"
        :disabled="isAnalyzing || !tunnelShape"
      >
        {{ isDrawing ? "绘制中..." : "绘制路径" }}
      </el-button>
      <el-button
        type="primary"
        class="primary-btn h-9 w-130px"
        :loading="isAnalyzing"
        @click="handleAnalysis"
        :disabled="!tunnelPath || isDrawing || !hasValidParameters"
      >
        {{ isAnalyzing ? "分析中..." : "生成隧道(3js)" }}
      </el-button>
      <el-button
        class="clear-btn w-130px"
        @click="handleClear"
        :disabled="isDrawing || isAnalyzing"
      >
        清除
      </el-button>
    </div>

    <!-- 路径信息显示区域 -->
    <div v-if="tunnelPath" class="path-section">
      <el-text class="path-label">隧道路径：</el-text>
      <div class="path-info">
        <span class="path-text">
          路径长度: {{ tunnelPathLength.toFixed(2) }}米
        </span>
        <span class="point-count"> ({{ tunnelPath.length }}个控制点) </span>
      </div>
    </div>

    <!-- 分析结果显示区域 -->
    <div v-if="showResult" class="result-section">
      <el-text class="result-label">分析结果：</el-text>
      <div class="result-content">
        <div class="tunnel-info">
          <div class="info-item">
            <span class="info-label">渲染方式：</span>
            <span class="info-value">Three.js + Cesium</span>
          </div>
          <div class="info-item">
            <span class="info-label">隧道形状：</span>
            <span class="info-value">{{ getShapeLabel(tunnelShape) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">隧道尺寸：</span>
            <span class="info-value">{{ getDimensionText() }}</span>
          </div>
          <div v-if="tunnelShape === 'horseshoe'" class="info-item">
            <span class="info-label">马蹄形详情：</span>
            <span class="info-value">{{ getHorseshoeDetails() }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">埋深：</span>
            <span class="info-value">{{ tunnelDepth }}米</span>
          </div>
          <div class="info-item">
            <span class="info-label">几何体细分：</span>
            <span class="info-value">{{ geometrySubdivisions }}段</span>
          </div>
          <div class="info-item">
            <span class="info-label">路径长度：</span>
            <span class="info-value">{{ tunnelPathLength.toFixed(2) }}米</span>
          </div>
        </div>

        <!-- 冲突检测结果 -->
        <div v-if="conflictResult" class="conflict-result">
          <el-divider content-position="left" class="color-#2C3037"
            >冲突检测结果</el-divider
          >
          <div class="conflict-summary">
            <span
              :class="['conflict-status', hasConflicts ? 'conflict' : 'safe']"
            >
              {{ hasConflicts ? "检测到冲突" : "无冲突" }}
            </span>
            <span v-if="hasConflicts" class="conflict-count">
              ({{ conflictResult.conflicts.length }}处)
            </span>
          </div>

          <!-- 冲突详情列表 -->
          <div
            v-if="hasConflicts && conflictResult.conflicts.length > 0"
            class="conflict-list"
          >
            <div
              v-for="(conflict, index) in conflictResult.conflicts.slice(0, 5)"
              :key="index"
              class="conflict-item"
              @click="highlightConflict(conflict)"
            >
              <div class="conflict-info">
                <span class="pipe-name">{{
                  conflict.pipeName || "管线" + (index + 1)
                }}</span>
                <span class="distance"
                  >距离: {{ conflict.distance.toFixed(2) }}m</span
                >
              </div>
              <el-icon class="conflict-icon"><Warning /></el-icon>
            </div>

            <div
              v-if="conflictResult.conflicts.length > 5"
              class="more-conflicts"
            >
              还有 {{ conflictResult.conflicts.length - 5 }} 处冲突...
            </div>
          </div>

          <!-- 分析操作按钮 -->
          <div class="analysis-actions">
            <el-button
              v-if="tunnelPath"
              size="small"
              @click="flyToTunnelOverview"
              class="action-btn"
            >
              全览隧道
            </el-button>
            <el-button
              v-if="conflictResult"
              size="small"
              @click="exportTunnelReport"
              class="action-btn"
            >
              导出报告
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Warning } from "@element-plus/icons-vue";
import * as THREE from "three";
import { AppCesium } from "@/lib/cesium/AppCesium";
import type { MapEngineType } from "@/components/BufferAnalysisDrawButtons.vue";

/**
 * 隧道参数接口定义
 */
interface TunnelConfig {
  shape: "circle" | "rectangle" | "horseshoe";
  diameter?: number;
  width?: number;
  height?: number;
  depth: number;
  subdivisions: number;
}

/**
 * 隧道路径点接口
 */
interface TunnelPathPoint {
  lng: number;
  lat: number;
  alt?: number;
}

/**
 * 冲突检测结果接口
 */
interface ConflictResult {
  hasConflicts: boolean;
  conflicts: ConflictItem[];
  totalChecked: number;
}

interface ConflictItem {
  pipeId: string;
  pipeName: string;
  distance: number;
  position: TunnelPathPoint;
  conflictType: "intersection" | "tooClose";
}

/**
 * Three.js几何体缓存接口
 */
interface ThreeGeometryData {
  positions: Float32Array;
  indices: Uint16Array | Uint32Array;
  normals: Float32Array;
  uvs: Float32Array;
}

// 组件属性定义
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});

/**
 * 计算地图引擎类型 - 隧道模拟仅支持三维(Cesium)
 */
const mapEngine = computed((): MapEngineType => {
  return "cesium";
});

// 隧道参数状态
const tunnelShape = ref<"circle" | "rectangle" | "horseshoe">("circle");
const tunnelDiameter = ref<number>(6);
const tunnelWidth = ref<number>(8);
const tunnelHeight = ref<number>(6);
const tunnelDepth = ref<number>(10);
const geometrySubdivisions = ref<number>(32); // Three.js特有：几何体细分级别

// 组件状态
const isDrawing = ref<boolean>(false);
const isAnalyzing = ref<boolean>(false);
const showResult = ref<boolean>(false);
const tunnelPath = ref<TunnelPathPoint[] | null>(null);
const conflictResult = ref<ConflictResult | null>(null);

// 图层管理
let cesiumTunnelLayer: any = null;
let cesiumPathLayer: any = null;
let cesiumConflictLayer: any = null;

// Three.js相关变量
let threeScene: THREE.Scene | null = null;
let threeGeometryCache: ThreeGeometryData | null = null;

// 计算属性
const hasValidParameters = computed(() => {
  if (tunnelShape.value === "circle") {
    return tunnelDiameter.value > 0 && tunnelDepth.value > 0;
  } else {
    return (
      tunnelWidth.value > 0 && tunnelHeight.value > 0 && tunnelDepth.value > 0
    );
  }
});

const tunnelPathLength = computed(() => {
  if (!tunnelPath.value || tunnelPath.value.length < 2) return 0;

  // 计算路径总长度
  let totalLength = 0;
  for (let i = 0; i < tunnelPath.value.length - 1; i++) {
    const p1 = tunnelPath.value[i];
    const p2 = tunnelPath.value[i + 1];

    // 简化的距离计算（可以后续优化为更精确的大圆距离）
    const dx = (p2.lng - p1.lng) * Math.cos((p1.lat * Math.PI) / 180);
    const dy = p2.lat - p1.lat;
    const distance = Math.sqrt(dx * dx + dy * dy) * 111000; // 转换为米
    totalLength += distance;
  }

  return totalLength;
});

const hasConflicts = computed(() => {
  return conflictResult.value?.hasConflicts || false;
});

const horseshoeHeightWarning = computed(() => {
  if (tunnelShape.value !== "horseshoe") return "";

  const halfWidth = tunnelWidth.value / 2;
  const minHeight = halfWidth * 1.2;

  if (tunnelHeight.value < minHeight) {
    return `马蹄形高度应至少为${minHeight.toFixed(
      1
    )}米（宽度的60%）以确保合理的形状`;
  }

  return "";
});

/**
 * 获取形状标签
 */
const getShapeLabel = (shape: string): string => {
  const labels = {
    circle: "圆形",
    rectangle: "矩形",
    horseshoe: "马蹄形",
  };
  return labels[shape as keyof typeof labels] || shape;
};

/**
 * 获取尺寸文本
 */
const getDimensionText = (): string => {
  if (tunnelShape.value === "circle") {
    return `直径${tunnelDiameter.value}米`;
  } else {
    return `${tunnelWidth.value}×${tunnelHeight.value}米`;
  }
};

/**
 * 获取马蹄形详细信息
 */
const getHorseshoeDetails = (): string => {
  if (tunnelShape.value !== "horseshoe") return "";

  const halfWidth = tunnelWidth.value / 2;
  const archRadius = halfWidth;
  const centerY = tunnelHeight.value - archRadius;
  const wallHeight = centerY;

  if (wallHeight < 0) {
    return `警告：高度不足，最小需要${(archRadius * 1.2).toFixed(1)}米`;
  }

  return `墙高${wallHeight.toFixed(1)}米，拱半径${archRadius.toFixed(1)}米`;
};

/**
 * 处理绘制路径操作
 */
const handleDrawPath = async (): Promise<void> => {
  if (mapEngine.value !== "cesium") {
    ElMessage.warning("隧道模拟仅支持三维模式");
    return;
  }

  isDrawing.value = true;

  try {
    ElMessage.info("请在地图上绘制隧道中心线路径");

    // 清除之前的结果
    clearTunnelResults();

    // 开始绘制路径
    await startPathDrawing();
  } catch (error) {
    console.error("路径绘制失败:", error);
    ElMessage.error(
      `路径绘制失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isDrawing.value = false;
  }
};

/**
 * 处理隧道分析操作
 */
const handleAnalysis = async (): Promise<void> => {
  if (!tunnelPath.value || !hasValidParameters.value) {
    ElMessage.warning("请先绘制隧道路径并设置有效参数");
    return;
  }

  isAnalyzing.value = true;

  try {
    ElMessage.info("正在使用Three.js创建隧道模型...");

    // 使用Three.js生成隧道几何体
    await createTunnelWithThreeJS();

    ElMessage.info("正在进行冲突检测...");

    // 执行冲突检测
    await performConflictDetection();

    showResult.value = true;
    ElMessage.success("Three.js隧道分析完成");
  } catch (error) {
    console.error("Three.js隧道分析失败:", error);
    ElMessage.error(
      `隧道分析失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isAnalyzing.value = false;
  }
};

/**
 * 处理清除操作
 */
const handleClear = (): void => {
  // 清除隧道分析结果
  clearTunnelResults();

  // 重置组件状态
  tunnelPath.value = null;
  conflictResult.value = null;
  showResult.value = false;
  isDrawing.value = false;
  isAnalyzing.value = false;

  // 清除Three.js缓存
  threeGeometryCache = null;
  if (threeScene) {
    // 清理Three.js场景
    threeScene.clear();
  }

  ElMessage.info("已清除所有隧道分析结果");
};

// 这里将添加更多的Three.js相关函数...
// （为了简洁，先创建基础框架，后续继续添加具体实现）

/**
 * 开始路径绘制功能（复用原有逻辑）
 */
const startPathDrawing = async (): Promise<void> => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = BC.Namespace;

    ElMessage.info("请在地图上连续点击绘制隧道路径，双击结束绘制");

    // 清除之前的路径
    if (cesiumPathLayer) {
      cesiumPathLayer.clear();
      viewer.removeLayer(cesiumPathLayer);
      cesiumPathLayer = null;
    }

    // 创建路径图层
    cesiumPathLayer = new BC.VectorLayer("tunnel-path-layer-three");
    viewer.addLayer(cesiumPathLayer);

    const pathPoints: TunnelPathPoint[] = [];
    let isDrawing = true;
    let polylineOverlay: any = null;

    // 绘制临时路径线
    const updatePathLine = () => {
      if (polylineOverlay) {
        cesiumPathLayer.removeOverlay(polylineOverlay);
      }

      if (pathPoints.length >= 2) {
        const positions = pathPoints.map(
          (point) => new BC.Position(point.lng, point.lat, point.alt || 20)
        );

        polylineOverlay = new BC.Polyline(positions);
        polylineOverlay.id = "tunnel-path-drawing-three";
        polylineOverlay.setStyle({
          width: 4,
          material: BC.Color.LIME.withAlpha(0.8), // Three.js版用不同颜色区分
          clampToGround: false,
        });

        cesiumPathLayer.addOverlay(polylineOverlay);
      }
    };

    // 添加路径点标记
    const addPointMarker = (point: TunnelPathPoint, index: number) => {
      const position = new BC.Position(point.lng, point.lat, point.alt || 20);

      const pointMarker = new BC.Point(position);
      pointMarker.id = `tunnel-path-point-three-${index}`;
      pointMarker.setStyle({
        pixelSize: 8,
        color: BC.Color.LIME,
        outlineColor: BC.Color.BLACK,
        outlineWidth: 2,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      });

      const labelPosition = new BC.Position(
        point.lng,
        point.lat,
        (point.alt || 20) + 2
      );
      const label = new BC.Label(labelPosition, `3JS-P${index + 1}`);
      label.id = `tunnel-path-label-three-${index}`;
      label.setStyle({
        font: "12px Microsoft YaHei",
        fillColor: BC.Color.WHITE,
        outlineColor: BC.Color.BLACK,
        outlineWidth: 1,
        offsetX: 0,
        offsetY: -15,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      });

      cesiumPathLayer.addOverlay(pointMarker);
      cesiumPathLayer.addOverlay(label);
    };

    // 使用递归方式处理连续点击绘制
    const handleNextPoint = () => {
      if (!isDrawing) return;

      viewer.once(
        BC.MouseEventType.LEFT_CLICK,
        (event: any, target: any, context: any) => {
          try {
            if (event.wgs84Position) {
              const point: TunnelPathPoint = {
                lng: event.wgs84Position.lng,
                lat: event.wgs84Position.lat,
                alt: event.wgs84Position.alt || 20,
              };

              pathPoints.push(point);
              addPointMarker(point, pathPoints.length - 1);
              updatePathLine();

              console.log(`Three.js版添加路径点 ${pathPoints.length}:`, point);

              // 继续监听下一个点
              handleNextPoint();
            }
          } catch (error) {
            console.error("添加路径点失败:", error);
          }
        },
        viewer
      );
    };

    // 开始监听点击事件
    handleNextPoint();

    // 监听双击结束绘制
    viewer.once(
      BC.MouseEventType.LEFT_DOUBLE_CLICK,
      (event: any, target: any, context: any) => {
        isDrawing = false;

        if (pathPoints.length >= 2) {
          tunnelPath.value = [...pathPoints];

          // 最终路径样式
          if (polylineOverlay) {
            polylineOverlay.setStyle({
              width: 5,
              material: BC.Color.LIME.withAlpha(0.9),
              clampToGround: false,
            });
          }

          ElMessage.success(
            `Three.js版隧道路径绘制完成，共${pathPoints.length}个控制点`
          );
        } else {
          ElMessage.warning("路径点数量不足，请至少绘制2个点");
          // 清除不完整的路径
          if (cesiumPathLayer) {
            cesiumPathLayer.clear();
          }
        }
      },
      viewer
    );

    // 设置绘制超时（2分钟）
    setTimeout(() => {
      if (isDrawing) {
        isDrawing = false;
        ElMessage.warning("绘制超时，请重新开始绘制");
      }
    }, 120000);
  } catch (error) {
    console.error("Three.js版路径绘制启动失败:", error);
    throw error;
  }
};

/**
 * 使用Three.js创建隧道几何体（核心新功能）
 */
const createTunnelWithThreeJS = async (): Promise<void> => {
  try {
    if (!tunnelPath.value || tunnelPath.value.length < 2) {
      throw new Error("隧道路径不足");
    }

    console.log("开始使用Three.js创建隧道几何体...");

    const viewer = AppCesium.getInstance().getViewer();

    // 清除之前的隧道
    if (cesiumTunnelLayer) {
      cesiumTunnelLayer.clear();
      viewer.removeLayer(cesiumTunnelLayer);
      cesiumTunnelLayer = null;
    }

    // 创建隧道图层
    cesiumTunnelLayer = new BC.VectorLayer("tunnel-geometry-layer-three");
    viewer.addLayer(cesiumTunnelLayer);

    // Step 1: 创建Three.js路径曲线
    const tunnelCurve = createThreeJSCurve();

    // Step 2: 根据隧道形状创建截面形状
    const crossSectionShape = createCrossSectionShape();

    // Step 3: 使用Three.js ExtrudeGeometry创建隧道几何体
    const tunnelGeometry = createExtrudedTunnel(tunnelCurve, crossSectionShape);

    // Step 4: 转换Three.js几何体为Cesium可用格式
    threeGeometryCache = convertThreeGeometryToCesium(tunnelGeometry);

    // Step 5: 在Cesium中渲染Three.js创建的几何体
    await renderThreeJSGeometryInCesium(threeGeometryCache);

    // Step 6: 创建辅助显示元素
    await createTunnelCorridorDisplay();

    console.log("Three.js隧道几何体创建完成");
  } catch (error) {
    console.error("Three.js隧道创建失败:", error);
    throw error;
  }
};

/**
 * 创建Three.js路径曲线
 */
const createThreeJSCurve = (): THREE.CatmullRomCurve3 => {
  const points: THREE.Vector3[] = [];

  // 转换路径点为Three.js坐标系
  // 这里使用相对坐标系，避免地球曲率问题
  const basePoint = tunnelPath.value![0];
  const depth = tunnelDepth.value;

  for (const point of tunnelPath.value!) {
    // 转换为相对坐标（米）
    const relativeX =
      (point.lng - basePoint.lng) *
      111000 *
      Math.cos((point.lat * Math.PI) / 180);
    const relativeY = (point.lat - basePoint.lat) * 111000;
    const relativeZ = -depth; // 地下深度

    points.push(new THREE.Vector3(relativeX, relativeY, relativeZ));
  }

  // 创建平滑曲线
  return new THREE.CatmullRomCurve3(points);
};

/**
 * 创建隧道截面形状
 */
const createCrossSectionShape = (): THREE.Shape => {
  const shape = new THREE.Shape();
  const subdivisions = geometrySubdivisions.value;

  switch (tunnelShape.value) {
    case "circle":
      return createCircleShape(tunnelDiameter.value / 2, subdivisions);
    case "rectangle":
      return createRectangleShape(tunnelWidth.value, tunnelHeight.value);
    case "horseshoe":
      return createHorseshoeShape(
        tunnelWidth.value,
        tunnelHeight.value,
        subdivisions
      );
    default:
      throw new Error("不支持的隧道形状");
  }
};

/**
 * 创建圆形截面
 */
const createCircleShape = (
  radius: number,
  subdivisions: number
): THREE.Shape => {
  const shape = new THREE.Shape();

  for (let i = 0; i <= subdivisions; i++) {
    const angle = (i / subdivisions) * Math.PI * 2;
    const x = Math.cos(angle) * radius;
    const y = Math.sin(angle) * radius;

    if (i === 0) {
      shape.moveTo(x, y);
    } else {
      shape.lineTo(x, y);
    }
  }

  return shape;
};

/**
 * 创建矩形截面
 */
const createRectangleShape = (width: number, height: number): THREE.Shape => {
  const shape = new THREE.Shape();
  const halfWidth = width / 2;
  const halfHeight = height / 2;

  shape.moveTo(-halfWidth, -halfHeight);
  shape.lineTo(halfWidth, -halfHeight);
  shape.lineTo(halfWidth, halfHeight);
  shape.lineTo(-halfWidth, halfHeight);
  shape.lineTo(-halfWidth, -halfHeight);

  return shape;
};

/**
 * 创建马蹄形截面
 */
const createHorseshoeShape = (
  width: number,
  height: number,
  subdivisions: number
): THREE.Shape => {
  const shape = new THREE.Shape();
  const halfWidth = width / 2;

  // 马蹄形参数计算
  // 拱顶圆弧半径等于半宽，确保形状协调
  const archRadius = halfWidth;
  // 圆心Y坐标：总高度减去圆弧半径
  const centerY = height - archRadius;
  // 垂直墙高度等于圆心Y坐标
  const wallHeight = centerY;

  // 检查尺寸合理性
  if (wallHeight < 0) {
    console.warn(
      `马蹄形隧道高度(${height})小于半宽(${halfWidth})，调整为最小合理高度`
    );
    const minHeight = archRadius * 1.2; // 最小高度为半径的1.2倍
    return createHorseshoeShape(width, minHeight, subdivisions);
  }

  console.log(
    `马蹄形参数: 宽度=${width}, 高度=${height}, 墙高=${wallHeight}, 拱顶半径=${archRadius}, 圆心Y=${centerY}`
  );

  // 构造马蹄形路径（逆时针方向，确保正向法线）
  // 1. 起点：左下角
  shape.moveTo(-halfWidth, 0);

  // 2. 底边：从左下到右下
  shape.lineTo(halfWidth, 0);

  // 3. 右侧垂直墙：从右下到右侧拱脚
  shape.lineTo(halfWidth, wallHeight);

  // 4. 拱顶圆弧：从右侧拱脚到左侧拱脚（逆时针）
  const arcSegments = Math.max(16, subdivisions / 2); // 确保圆弧足够平滑
  for (let i = 0; i <= arcSegments; i++) {
    // 角度从0(右侧)到π(左侧)，0度对应最右侧，π度对应最左侧
    const angle = (i / arcSegments) * Math.PI;

    // 拱顶圆弧的坐标计算
    // 圆心在(0, centerY)，半径为archRadius
    const x = archRadius * Math.cos(angle);
    const y = centerY + archRadius * Math.sin(angle);

    if (i === 0) {
      // 验证第一个点是否与右侧墙顶正确连接
      console.log(
        `拱顶起点: (${x.toFixed(2)}, ${y.toFixed(
          2
        )}), 预期: (${halfWidth}, ${wallHeight})`
      );
    } else if (i === arcSegments) {
      // 验证最后一个点是否与左侧墙顶正确连接
      console.log(
        `拱顶终点: (${x.toFixed(2)}, ${y.toFixed(
          2
        )}), 预期: (${-halfWidth}, ${wallHeight})`
      );
    }

    shape.lineTo(x, y);
  }

  // 5. 左侧垂直墙：从左侧拱脚到左下角
  shape.lineTo(-halfWidth, wallHeight);

  // 6. 闭合路径：回到起点（自动闭合）
  shape.lineTo(-halfWidth, 0);

  console.log("马蹄形截面创建完成");
  return shape;
};

/**
 * 创建拉伸隧道几何体
 */
const createExtrudedTunnel = (
  curve: THREE.CatmullRomCurve3,
  shape: THREE.Shape
): THREE.ExtrudeGeometry => {
  const extrudeSettings: THREE.ExtrudeGeometryOptions = {
    steps: Math.max(2, Math.floor(tunnelPathLength.value / 5)), // 根据路径长度调整步数
    bevelEnabled: false,
    extrudePath: curve,
  };

  return new THREE.ExtrudeGeometry(shape, extrudeSettings);
};

/**
 * 转换Three.js几何体为Cesium格式
 */
const convertThreeGeometryToCesium = (
  geometry: THREE.ExtrudeGeometry
): ThreeGeometryData => {
  // 计算法线（如果没有）
  if (!geometry.attributes.normal) {
    geometry.computeVertexNormals();
  }

  // 计算UV坐标（如果没有）
  if (!geometry.attributes.uv) {
    const uvAttribute = new THREE.BufferAttribute(
      new Float32Array(geometry.attributes.position.count * 2),
      2
    );
    geometry.setAttribute("uv", uvAttribute);
  }

  const positions = geometry.attributes.position.array as Float32Array;
  const normals = geometry.attributes.normal.array as Float32Array;
  const uvs = geometry.attributes.uv.array as Float32Array;
  const indices = geometry.index ? geometry.index.array : null;

  return {
    positions,
    indices: indices as Uint16Array | Uint32Array,
    normals,
    uvs,
  };
};

/**
 * 在Cesium中渲染Three.js几何体
 */
const renderThreeJSGeometryInCesium = async (
  geometryData: ThreeGeometryData
): Promise<void> => {
  const { Cesium } = BC.Namespace;
  const viewer = AppCesium.getInstance().getViewer();

  // 转换坐标到Cesium世界坐标系
  const transformedPositions = transformCoordinatesToCesium(
    geometryData.positions
  );

  // 创建Cesium几何体
  const cesiumGeometry = new Cesium.Geometry({
    attributes: {
      position: new Cesium.GeometryAttribute({
        componentDatatype: Cesium.ComponentDatatype.DOUBLE,
        componentsPerAttribute: 3,
        values: transformedPositions,
      }),
      normal: new Cesium.GeometryAttribute({
        componentDatatype: Cesium.ComponentDatatype.FLOAT,
        componentsPerAttribute: 3,
        values: geometryData.normals,
      }),
      st: new Cesium.GeometryAttribute({
        componentDatatype: Cesium.ComponentDatatype.FLOAT,
        componentsPerAttribute: 2,
        values: geometryData.uvs,
      }),
    } as any, // 临时类型断言以避免类型检查问题
    indices: geometryData.indices,
    primitiveType: Cesium.PrimitiveType.TRIANGLES,
    boundingSphere: Cesium.BoundingSphere.fromVertices(
      Array.from(transformedPositions)
    ),
  });

  // 创建几何体实例
  const geometryInstance = new Cesium.GeometryInstance({
    geometry: cesiumGeometry,
    id: "tunnel-threejs-geometry",
  });

  // 创建材质
  const tunnelMaterial = new Cesium.Material({
    fabric: {
      type: "Image",
      uniforms: {
        image: "./images/wall.jpg",
        repeat: new Cesium.Cartesian2(20.0, 5.0),
      },
    },
  });

  // 创建Primitive进行渲染
  const tunnelPrimitive = new Cesium.Primitive({
    geometryInstances: geometryInstance,
    appearance: new Cesium.MaterialAppearance({
      material: tunnelMaterial,
      translucent: false,
      closed: false,
    }),
    asynchronous: false,
  });

  // 添加到场景
  viewer.scene.primitives.add(tunnelPrimitive);

  console.log("Three.js几何体已在Cesium中渲染完成");
};

/**
 * 坐标系转换：Three.js相对坐标 -> Cesium世界坐标
 */
const transformCoordinatesToCesium = (
  threePositions: Float32Array
): Float64Array => {
  const { Cesium } = BC.Namespace;
  const result = new Float64Array(threePositions.length);
  const basePoint = tunnelPath.value![0];

  for (let i = 0; i < threePositions.length; i += 3) {
    const relativeX = threePositions[i];
    const relativeY = threePositions[i + 1];
    const relativeZ = threePositions[i + 2];

    // 转换回地理坐标
    const lng =
      basePoint.lng +
      relativeX / (111000 * Math.cos((basePoint.lat * Math.PI) / 180));
    const lat = basePoint.lat + relativeY / 111000;
    const alt = (basePoint.alt || 20) + relativeZ;

    // 转换为Cesium笛卡尔坐标
    const cartesian = Cesium.Cartesian3.fromDegrees(lng, lat, alt);

    result[i] = cartesian.x;
    result[i + 1] = cartesian.y;
    result[i + 2] = cartesian.z;
  }

  return result;
};

/**
 * 创建隧道走廊显示（Three.js版本）
 */
const createTunnelCorridorDisplay = async (): Promise<void> => {
  try {
    const { Cesium } = BC.Namespace;

    if (!tunnelPath.value || tunnelPath.value.length < 2) return;

    // 创建地面路径显示
    const surfacePositions = tunnelPath.value.map(
      (point) => new BC.Position(point.lng, point.lat, (point.alt || 20) + 2)
    );

    const surfaceLine = new BC.Polyline(surfacePositions);
    surfaceLine.id = "tunnel-surface-path-three";
    surfaceLine.setStyle({
      width: 3,
      material: BC.Color.LIME.withAlpha(0.8),
      clampToGround: false,
    });
    cesiumTunnelLayer.addOverlay(surfaceLine);

    // 创建地下隧道中心线显示
    const undergroundPositions = tunnelPath.value.map(
      (point) =>
        new BC.Position(
          point.lng,
          point.lat,
          (point.alt || 20) - tunnelDepth.value
        )
    );

    const undergroundLine = new BC.Polyline(undergroundPositions);
    undergroundLine.id = "tunnel-underground-centerline-three";
    undergroundLine.setStyle({
      width: 2,
      material: BC.Color.GREEN.withAlpha(0.6),
      clampToGround: false,
    });
    cesiumTunnelLayer.addOverlay(undergroundLine);

    // 创建连接线显示埋深
    for (let i = 0; i < tunnelPath.value.length; i++) {
      const point = tunnelPath.value[i];
      const connectionPositions = [
        new BC.Position(point.lng, point.lat, point.alt || 20),
        new BC.Position(
          point.lng,
          point.lat,
          (point.alt || 20) - tunnelDepth.value
        ),
      ];

      const connectionLine = new BC.Polyline(connectionPositions);
      connectionLine.id = `tunnel-depth-connection-three-${i}`;
      connectionLine.setStyle({
        width: 1,
        material: BC.Color.LIME.withAlpha(0.4),
        clampToGround: false,
      });
      cesiumTunnelLayer.addOverlay(connectionLine);
    }

    console.log("Three.js版隧道走廊显示创建完成");
  } catch (error) {
    console.error("Three.js版隧道走廊显示创建失败:", error);
  }
};

/**
 * 执行冲突检测（复用原有逻辑）
 */
const performConflictDetection = async (): Promise<void> => {
  // 复用原有的冲突检测逻辑，这里先实现一个简化版本
  try {
    conflictResult.value = {
      hasConflicts: false,
      conflicts: [],
      totalChecked: 0,
    };

    console.log("Three.js版冲突检测完成（简化版本）");
  } catch (error) {
    console.error("冲突检测失败:", error);
  }
};

/**
 * 清除隧道结果
 */
const clearTunnelResults = (): void => {
  try {
    const viewer = AppCesium.getInstance().getViewer();

    // 清除隧道图层
    if (cesiumTunnelLayer) {
      cesiumTunnelLayer.clear();
      viewer.removeLayer(cesiumTunnelLayer);
      cesiumTunnelLayer = null;
    }

    // 清除路径图层
    if (cesiumPathLayer) {
      cesiumPathLayer.clear();
      viewer.removeLayer(cesiumPathLayer);
      cesiumPathLayer = null;
    }

    // 清除冲突图层
    if (cesiumConflictLayer) {
      cesiumConflictLayer.clear();
      viewer.removeLayer(cesiumConflictLayer);
      cesiumConflictLayer = null;
    }

    // 清除Three.js创建的Primitive对象
    const primitives = viewer.scene.primitives;
    const primitivesToRemove = [];

    for (let i = 0; i < primitives.length; i++) {
      const primitive = primitives.get(i);
      if (
        primitive &&
        ((primitive.geometryInstances &&
          primitive.geometryInstances.id &&
          primitive.geometryInstances.id.includes("tunnel-threejs")) ||
          (primitive._id && primitive._id.includes("tunnel-threejs")) ||
          (primitive.id && primitive.id.includes("tunnel-threejs")))
      ) {
        primitivesToRemove.push(primitive);
      }
    }

    primitivesToRemove.forEach((primitive) => {
      try {
        primitives.remove(primitive);
        if (primitive.destroy && typeof primitive.destroy === "function") {
          primitive.destroy();
        }
      } catch (e) {
        console.warn("移除Three.js primitive失败:", e);
      }
    });

    console.log(
      `Three.js隧道分析结果已清除，共清除${primitivesToRemove.length}个几何体`
    );
  } catch (error) {
    console.error("清除Three.js隧道结果失败:", error);
  }
};

/**
 * 高亮显示冲突
 */
const highlightConflict = (conflict: ConflictItem): void => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = BC.Namespace;

    // 定位到冲突位置
    const position = Cesium.Cartesian3.fromDegrees(
      conflict.position.lng,
      conflict.position.lat,
      (conflict.position.alt || 10) + 50
    );

    // 飞行到冲突位置
    viewer.camera.flyTo({
      destination: position,
      duration: 2.0,
    });

    ElMessage.info(`Three.js版已定位到冲突管线: ${conflict.pipeName}`);
  } catch (error) {
    console.error("高亮冲突失败:", error);
    ElMessage.error("定位冲突失败");
  }
};

/**
 * 飞行到隧道全览
 */
const flyToTunnelOverview = (): void => {
  try {
    if (!tunnelPath.value || tunnelPath.value.length === 0) {
      ElMessage.warning("没有隧道路径可显示");
      return;
    }

    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = BC.Namespace;

    // 计算隧道路径的边界框
    const positions = tunnelPath.value.map((point) =>
      Cesium.Cartesian3.fromDegrees(point.lng, point.lat, point.alt || 20)
    );

    const boundingSphere = Cesium.BoundingSphere.fromPoints(positions);

    // 飞行到隧道全览
    viewer.camera.flyToBoundingSphere(boundingSphere, {
      duration: 2.0,
      offset: new Cesium.HeadingPitchRange(0, -0.5, boundingSphere.radius * 3),
    });
  } catch (error) {
    console.error("飞行到隧道全览失败:", error);
    ElMessage.error("视角调整失败");
  }
};

/**
 * 导出隧道分析报告
 */
const exportTunnelReport = (): void => {
  try {
    if (!conflictResult.value) {
      ElMessage.warning("没有分析结果可导出");
      return;
    }

    const report = {
      renderingEngine: "Three.js + Cesium",
      tunnelInfo: {
        shape: getShapeLabel(tunnelShape.value),
        dimensions: getDimensionText(),
        depth: tunnelDepth.value,
        pathLength: tunnelPathLength.value,
        geometrySubdivisions: geometrySubdivisions.value,
      },
      threeJSInfo: {
        geometryType: "ExtrudeGeometry",
        subdivisions: geometrySubdivisions.value,
        hasGeometryCache: !!threeGeometryCache,
        vertexCount: threeGeometryCache?.positions.length || 0,
      },
      conflictAnalysis: {
        totalChecked: conflictResult.value.totalChecked,
        conflictsFound: conflictResult.value.conflicts.length,
        hasConflicts: conflictResult.value.hasConflicts,
        conflicts: conflictResult.value.conflicts.map((conflict) => ({
          pipeName: conflict.pipeName,
          conflictType: conflict.conflictType,
          distance: Math.round(conflict.distance * 100) / 100,
          position: {
            lng: Math.round(conflict.position.lng * 1000000) / 1000000,
            lat: Math.round(conflict.position.lat * 1000000) / 1000000,
            alt: conflict.position.alt || 0,
          },
        })),
      },
      generateTime: new Date().toLocaleString("zh-CN"),
    };

    // 创建下载链接
    const dataStr =
      "data:text/json;charset=utf-8," +
      encodeURIComponent(JSON.stringify(report, null, 2));
    const downloadAnchorNode = document.createElement("a");
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute(
      "download",
      `Three.js隧道分析报告_${new Date().getTime()}.json`
    );
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();

    ElMessage.success("Three.js隧道分析报告已导出");
  } catch (error) {
    console.error("导出隧道报告失败:", error);
    ElMessage.error("导出报告失败");
  }
};

// 生命周期钩子
onMounted(() => {
  console.log("TunnelSimulationThree组件已挂载 - Three.js版本");

  // 初始化Three.js环境
  threeScene = new THREE.Scene();

  // 验证Cesium环境
  if (mapEngine.value !== "cesium") {
    ElMessage.warning("隧道模拟功能仅在三维模式下可用");
  }
});

onUnmounted(() => {
  console.log("TunnelSimulationThree组件卸载，开始清理资源");

  try {
    clearTunnelResults();

    // 清理Three.js资源
    if (threeScene) {
      threeScene.clear();
      threeScene = null;
    }
    threeGeometryCache = null;

    console.log("TunnelSimulationThree组件资源清理完成");
  } catch (error) {
    console.error("组件卸载清理失败:", error);
  }
});
</script>

<style scoped lang="scss">
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 550px;
  min-height: 130px;
  z-index: 1000;
}

.parameter-section {
  margin-bottom: 20px;
}

.input-section {
  margin-bottom: 16px;

  .label-text {
    font-weight: 500;
    color: #606266;
    margin-bottom: 8px;
    display: block;
  }

  .input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 350px;

    .requirement-input {
      flex: 1;
    }

    .shape-select {
      flex: 1;
    }

    .unit-text {
      color: #909399;
      font-size: 14px;
    }
  }

  .parameter-warning {
    margin-top: 8px;
    padding: 8px;
    background: rgba(245, 108, 108, 0.1);
    border: 1px solid rgba(245, 108, 108, 0.3);
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f56c6c;

    .el-icon {
      font-size: 14px;
    }
  }
}



.path-section {
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(103, 194, 58, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(103, 194, 58, 0.2);

  .path-label {
    color: #67c23a;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .path-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .path-text {
      color: #606266;
      font-size: 13px;
    }

    .point-count {
      color: #909399;
      font-size: 12px;
    }
  }
}

.result-section {
  .result-label {
    color: #67c23a;
    font-size: 14px;
    margin-bottom: 12px;
  }

  .result-content {
    background: rgba(103, 194, 58, 0.03);
    border-radius: 4px;
    padding: 12px;
    border: 1px solid rgba(103, 194, 58, 0.1);
  }

  .tunnel-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .info-label {
        color: #67c23a;
        font-size: 13px;
      }

      .info-value {
        color: #606266;
        font-size: 13px;
      }
    }
  }

  .conflict-result {
    margin-top: 16px;

    .conflict-summary {
      margin-bottom: 12px;

      .conflict-status {
        font-size: 14px;
        font-weight: 500;

        &.safe {
          color: #67c23a;
        }

        &.conflict {
          color: #f56c6c;
        }
      }

      .conflict-count {
        color: #f56c6c;
        margin-left: 8px;
        font-size: 12px;
      }
    }

    .conflict-list {
      .conflict-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        margin-bottom: 6px;
        background: rgba(245, 108, 108, 0.1);
        border-radius: 4px;
        border: 1px solid rgba(245, 108, 108, 0.2);
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(245, 108, 108, 0.2);
        }

        .conflict-info {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .pipe-name {
            color: #606266;
            font-size: 13px;
          }

          .distance {
            color: #f56c6c;
            font-size: 12px;
          }
        }

        .conflict-icon {
          color: #f56c6c;
          font-size: 16px;
        }
      }

      .more-conflicts {
        text-align: center;
        color: #909399;
        font-size: 12px;
        margin-top: 8px;
      }
    }

    .analysis-actions {
      margin-top: 16px;
      padding-top: 12px;
      border-top: 1px solid rgba(103, 194, 58, 0.2);
      display: flex;
      gap: 8px;
      justify-content: center;

      .action-btn {
        background: linear-gradient(135deg, #67c23a, #85ce61);
        border: none;
        color: white;
        font-size: 12px;
        padding: 6px 12px;
        border-radius: 4px;

        &:hover {
          background: linear-gradient(135deg, #529b2e, #67c23a);
        }
      }
    }
  }
}

:deep(.el-divider) {
  margin: 12px 0;

  .el-divider__text {
    color: #67c23a;
    font-size: 13px;
  }
}
</style>
