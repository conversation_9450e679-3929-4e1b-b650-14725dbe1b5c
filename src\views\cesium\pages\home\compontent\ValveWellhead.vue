<template>
  <BaseCard title="阀门井口径统计">
    <div class="chart" ref="valveRef"></div>
  </BaseCard>
</template>
<script lang="ts" setup>
import { initValveEchart } from "@/lib/echarts";
import { fmStat } from "@/api/home";

const { initChart } = useEchart();
const valveRef = ref();
const list = ref<any[]>([]);
const xData = ref<any>([]);
const getChart = async () => {
  const { data, code } = await fmStat();
  if (code === 200) {
    xData.value = Object.keys(data).map((key) => {
      return key;
    });
    list.value = Object.keys(data).map((key) => {
      return data[key];
    });
  }
  valveRef.value &&
    initChart(
      valveRef.value,
      initValveEchart(xData.value.slice(0, 5), list.value.slice(0, 5))
    );
};
onMounted(() => {
  getChart();
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 180px;
}
</style>
