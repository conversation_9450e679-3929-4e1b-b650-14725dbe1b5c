<!--
 * @Author: <PERSON>
 * @Date: 2024-12-16 16:00:00
 * @Description: 设备图层控制
-->
<template>
  <div class="layer-ctrl">
    <div
      @click="handleAllLayerVisible"
      class="layer-ctrl-title cursor-pointer flex items-center justify-center"
      :class="{ active: isActive }"
    >
      <span class="node-label">全部设备</span>
    </div>
    <el-tree
      ref="treeRef"
      :data="deviceData"
      :expand-on-click-node="false"
      show-checkbox
      accordion
      check-on-click-node
      node-key="id"
      :props="defaultProps"
      @check-change="handleNodeClick"
    >
      <template #default="{ node }">
        <div class="flex items-center">
          <div class="icon-wrapper">
            <img
              v-if="node.data?.code === 'noise'"
              :src="noiseActiveIcon"
              class="node-icon"
              :class="{ inactive: !node.checked }"
              alt=""
            />
            <img
              v-else-if="node.data?.code === 'pressure'"
              :src="pressureActiveIcon"
              class="node-icon"
              :class="{ inactive: !node.checked }"
              alt=""
            />
            <img
              v-else-if="node.data?.code === 'flowmeter'"
              :src="flowmeterActiveIcon"
              class="node-icon"
              :class="{ inactive: !node.checked }"
              alt=""
            />
            <img
              v-else-if="node.data?.code === 'watermeter'"
              :src="watermeterActiveIcon"
              class="node-icon"
              :class="{ inactive: !node.checked }"
              alt=""
            />
            <img
              v-else-if="node.data?.code === 'homemeter'"
              :src="homemeterActiveIcon"
              class="node-icon"
              :class="{ inactive: !node.checked }"
              alt=""
            />
          </div>
          <span class="node-label">
            {{ node.label }}
          </span>
        </div>
      </template>
    </el-tree>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElTree } from 'element-plus';

// 导入SVG图标
import noiseActiveIcon from '@/assets/icon-svg/device/noise-active.svg';
import pressureActiveIcon from '@/assets/icon-svg/device/pressure-active.svg';
import flowmeterActiveIcon from '@/assets/icon-svg/device/flowmeter-active.svg';
import watermeterActiveIcon from '@/assets/icon-svg/device/watermeter-active.svg';
import homemeterActiveIcon from '@/assets/icon-svg/device/homemeter-active.svg';

// 定义设备类型和代码
type DeviceCode =
  | 'noise'
  | 'pressure'
  | 'flowmeter'
  | 'watermeter'
  | 'homemeter';

interface DeviceType {
  id: string;
  label: string;
  code: DeviceCode;
}

// 定义设备类型数据
const deviceTypes: DeviceType[] = [
  { id: 'noise', label: '噪声设备', code: 'noise' },
  { id: 'pressure', label: '压力表', code: 'pressure' },
  { id: 'flowmeter', label: '流量计', code: 'flowmeter' },
  { id: 'watermeter', label: '远传水表', code: 'watermeter' },
  { id: 'homemeter', label: '入户远传表', code: 'homemeter' },
];

// 树组件引用
const treeRef = ref<InstanceType<typeof ElTree>>();

// 全选状态
const isActive = ref(true);

// 设备数据
const deviceData = ref(deviceTypes);

// 所有图层ID
const allLayerIds = ref<string[]>(deviceTypes.map((item) => item.id));

// 处理全部图层可见性
const handleAllLayerVisible = () => {
  isActive.value = !isActive.value;

  if (isActive.value) {
    // 全部选中
    treeRef.value?.setCheckedKeys(allLayerIds.value);
    console.log('显示全部图层');
  } else {
    // 全部取消选中
    treeRef.value?.setCheckedKeys([]);
    console.log('隐藏全部图层');
  }
};

// 树组件默认配置
const defaultProps = {
  children: 'children',
  label: 'label',
  class: () => 'child-node',
};

// 处理节点点击
const handleNodeClick = (data: DeviceType, checked: boolean): void => {
  console.log(`图层 ${data.label} 已${checked ? '显示' : '隐藏'}`);

  // 检查是否全部选中
  const checkedKeys = treeRef.value?.getCheckedKeys() || [];
  if (checkedKeys.length === allLayerIds.value.length) {
    isActive.value = true;
  } else {
    isActive.value = false;
  }
};

// 初始化，默认全选
onMounted(() => {
  // 设置默认选中
  treeRef.value?.setCheckedKeys(allLayerIds.value);
});
</script>

<style scoped lang="scss">
.layer-ctrl-title {
  height: 40px;
  width: 175px;
  margin-bottom: 10px;
  // background-color: rgba(32, 55, 100, 0.5);
  border-radius: 4px;
  border: 1px solid #4080ff;

  .node-label {
    color: rgba($color: #fff, $alpha: 0.7);
  }
}

.layer-ctrl-title.active {
  // background-color: rgba(64, 158, 255, 0.2);
  border: 1px solid #4080ff;

  .node-label {
    color: #fff;
  }
}

.layer-ctrl {
  position: absolute;
  left: 20px;
  top: 20px;
  width: 175px;
  max-height: 100%;
  background: transparent;

  :deep(.el-tree) {
    background: transparent;
  }

  // 节点
  :deep(.el-tree-node) {
    &:not(:first-child) {
      margin-top: 12px;
    }
  }

  // 子节点
  :deep(.child-node) > .el-tree-node__content {
    width: 100%;
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
    border-radius: 2px;

    box-shadow: 0px 0px 4px 0px rgba(196, 216, 255, 0.7);
    background: rgba(255, 255, 255, 1);
    padding: 0 12px;

    .node-label {
      color: rgba($color: #fff, $alpha: 0.7);
      margin-left: 8px;
      font-size: 14px;
    }

    .icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
    }

    .node-icon {
      width: 24px;
      height: 24px;

      &.inactive {
        opacity: 0.5;
        filter: grayscale(1);
      }
    }

    .el-icon {
      order: 3;
      margin-left: auto;
      margin-right: 16px;
    }
  }

  :deep(.child-node.is-checked) > .el-tree-node__content {
    background-color: rgba(64, 158, 255, 0.2);
    border: 1px solid #4080ff;

    .node-label {
      color: #fff;
    }
  }

  :deep(.el-checkbox) {
    display: none;
  }

  :deep(.el-tree-node__expand-icon) {
    display: none;
  }
}
</style>
