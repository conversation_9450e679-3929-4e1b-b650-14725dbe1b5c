import hRequest from '@/utils/http';

/**
 * @fileoverview 用水户相关API接口
 * @description 提供用水户管理相关的API调用方法，已适配hRequest请求库
 * @note 已从axios迁移到hRequest，所有请求方法使用配置对象参数
 * @version 2.0.0 - 适配hRequest
 */

/**
 * @description 用水户基本信息接口
 */
export interface WaterUserVo {
  /** id */
  id: string;
  /** 用户名 */
  userName: string;
  /** 用户号 */
  userNo: string;
  /** 用户类型 */
  userType: string;
  /** 用水地址 */
  waterAddress: string;
  /** 营业所编号 */
  businessPlaceNo: string;
  gxddh: string;
  /** 联系电话 */
  contactsPhone: string;
  /** 状态（1正常，2停用，3已销户） */
  status: string;
  /** 是否删除 0未删除，1已删除 */
  isDelete: string;
  /** 身份证号 */
  idNo: string;
}

/**
 * @description 用水户分页信息接口（包含关联状态）
 */
export interface WaterUserPageVo extends WaterUserVo {
  /** 是否关联（0未关联，1已关联） */
  isRelated: string;
  gid: any;
}

/**
 * @description 批量挂接用水户请求接口
 */
export interface WaterUserPtDto {
  /** 用水户id */
  waterUserid: string;
  /** 管点id */
  ptId: number;
}

/**
 * @description 分页信息接口
 */
export interface PageInfo<T> {
  /** 总记录数 */
  totalCount: number;
  /** 每页大小 */
  pageSize: number;
  /** 总页数 */
  totalPage: number;
  /** 当前页 */
  currPage: number;
  /** 数据列表 */
  list: T[];
}

/**
 * @description 通用响应接口
 */
export interface ApiResponse<T> {
  /** 响应码 */
  code: number;
  /** 响应消息 */
  msg: string;
  /** 响应数据 */
  data: T;
}

/**
 * @description 用水户分页查询参数接口
 */
export interface WaterUserPageQuery {
  /** 页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 排序字段 */
  orderByColumn?: string;
  /** 是否升序 */
  isAsc?: string;
  /** 用户名 */
  userName?: string;
  /** 是否挂接（0未挂接，1已挂接） */
  isHang?: number;
  /** 起始记录数 */
  firstNum?: number;
}

/**
 * @description 用水户列表查询参数接口
 */
export interface WaterUserListQuery {
  /** 用户名 */
  userName?: string;
  /** 用户号 */
  userNo?: string;
}

/**
 * @description 水表附属物分页查询参数接口
 */
export interface WaterMeterPageQuery {
  /** 页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 排序字段 */
  orderByColumn?: string;
  /** 是否升序 */
  isAsc?: string;
  /** 编码 */
  bm?: string;
  /** 是否挂接（0未挂接，1已挂接） */
  isHang?: number;
  /** 起始记录数 */
  firstNum?: number;
}

// ==================== API 函数 ====================

/**
 * @description 获取用水户详情
 * @param {string} id 用水户ID
 * @returns {Promise<ApiResponse<WaterUserVo>>} 用水户详情响应
 */
export const getWaterUserDetail = async (id: string): Promise<ApiResponse<WaterUserVo>> => {
  const result = await hRequest.get<ApiResponse<WaterUserVo>>({
    url: `/analyse/water/user/${id}`
  });
  return result;
};

/**
 * @description 用水户分页查询
 * @param {WaterUserPageQuery} params 查询参数
 * @returns {Promise<ApiResponse<PageInfo<WaterUserPageVo>>>} 分页查询响应
 */
export const getWaterUserPage = async (
  params: WaterUserPageQuery = {}
): Promise<ApiResponse<PageInfo<WaterUserPageVo>>> => {
  const result = await hRequest.get<ApiResponse<PageInfo<WaterUserPageVo>>>({
    url: "/analyse/water/user/page",
    params
  });
  return result;
};

/**
 * @description 获取用水户列表
 * @param {WaterUserListQuery} params 查询参数
 * @returns {Promise<ApiResponse<WaterUserVo[]>>} 用水户列表响应
 */
export const getWaterUserList = async (
  params: WaterUserListQuery = {}
): Promise<ApiResponse<WaterUserVo[]>> => {
  const result = await hRequest.get<ApiResponse<WaterUserVo[]>>({
    url: "/analyse/water/user/list",
    params
  });
  return result;
};

/**
 * @description 批量解除挂接
 * @param {number[]} ptIds 管点ID数组
 * @returns {Promise<ApiResponse<void>>} 操作结果响应
 */
export const batchCancelAttach = async (ptIds: number[]): Promise<ApiResponse<void>> => {
  const result = await hRequest.post<ApiResponse<void>>({
    url: "/analyse/gs/pt/batch/cancel/attach",
    data: ptIds
  });
  return result;
};

/**
 * @description 批量挂接用水户
 * @param {WaterUserPtDto[]} waterUserPts 用水户挂接信息数组
 * @returns {Promise<ApiResponse<void>>} 操作结果响应
 */
export const batchAttachWaterUser = async (
  waterUserPts: WaterUserPtDto[]
): Promise<ApiResponse<void>> => {
  const result = await hRequest.post<ApiResponse<void>>({
    url: "/analyse/gs/pt/batch/attach/water/user",
    data: waterUserPts
  });
  return result;
};

/**
 * @description 管点分页信息接口（用于水表附属物查询）
 */
export interface GsPtPageVo {
  /** 管点ID */
  gid: number;
  /** 编码 */
  bm: string;
  /** 其他管点相关字段... */
  [key: string]: any;
}

/**
 * @description 水表附属物分页查询
 * @param {WaterMeterPageQuery} params 查询参数
 * @returns {Promise<ApiResponse<PageInfo<GsPtPageVo>>>} 分页查询响应
 */
export const getWaterMeterPage = async (
  params: WaterMeterPageQuery = {}
): Promise<ApiResponse<PageInfo<GsPtPageVo>>> => {
  const result = await hRequest.get<ApiResponse<PageInfo<GsPtPageVo>>>({
    url: "/analyse/gs/pt/water/meter/page",
    params
  });
  return result;
};