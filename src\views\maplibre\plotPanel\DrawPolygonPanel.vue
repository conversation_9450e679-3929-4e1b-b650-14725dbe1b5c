<!--
 * @Description: 多边形标绘绘制编辑面板 - 重构版本
 * @Date: 2024-01-20 11:00:00
 * @Author: 项目开发团队
 * @LastEditor: 项目开发团队
 * @LastEditTime: 2024-01-20 15:00:00
-->
<template>
  <div element-loading-background="rgba(0,0,0,0)">
    <el-form
      ref="ruleFormRef"
      :model="formData"
      :rules="rules"
      class="admin-sub-form"
      label-width="auto"
    >
      <!-- 基本信息 -->

      <el-form-item label="标绘类型">
        <base-input disabled class="custom-visform-input" value="多边形" />
      </el-form-item>

      <el-form-item label="名称" prop="name">
        <base-input
          class="custom-visform-input"
          v-model="formData.name"
          placeholder="请输入多边形名称"
        />
      </el-form-item>

      <!-- 多边形特有信息：顶点数 -->
      <div v-if="hasGeometry">
        <el-form-item label="顶点数">
          <base-input
            class="custom-visform-input"
            :value="`${pointCount} 个顶点`"
            disabled
          />
        </el-form-item>
        <el-form-item label="状态">
          <base-input
            class="custom-visform-input"
            :value="pointCount >= 3 ? '有效多边形' : '需要至少3个顶点'"
            disabled
          />
        </el-form-item>
      </div>

      <!-- 绘制操作 -->

      <el-form-item label="绘制操作">
        <el-button
          type="primary"
          @click="startDraw"
          :disabled="isDrawing"
          :loading="isDrawing"
        >
          <el-icon><Edit /></el-icon>
          {{ isEditing ? "重新绘制" : hasGeometry ? "重新绘制" : "开始绘制" }}
        </el-button>
      </el-form-item>

      <!-- 描述信息 -->
      <el-form-item label="标绘描述">
        <base-input
          class="custom-visform-input"
          v-model="formData.remark"
          :rows="3"
          placeholder="请输入多边形描述（可选）"
          maxlength="200"
        />
      </el-form-item>

      <!-- 操作按钮 -->
      <div class="flex justify-end">
        <el-button
          @click="onSubmit"
          :loading="submitLoading"
          :disabled="!hasGeometry"
          type="primary"
          class="primary-btn w-80px"
        >
          {{ isEditing ? "更新" : "确定" }}
        </el-button>
        <el-button
          class="w-80px clear-btn"
          @click="onCancel"
          :loading="submitLoading"
          block
        >
          取消
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, type Ref } from "vue";
import { ElForm } from "element-plus";
import { Edit } from "@element-plus/icons-vue";
import BaseInput from "@/components/input/index.vue";
import { vxRule } from "@/utils/validator";

// 导入基础Panel逻辑
import {
  useBaseDrawPanel,
  type BaseDrawPanelProps,
  type BaseDrawPanelEmits,
} from "./BaseDrawPanel";

// Props和Emits定义
const props = withDefaults(defineProps<BaseDrawPanelProps>(), {
  drawData: null,
  plotService: null,
});

const emit = defineEmits<BaseDrawPanelEmits>();

// 表单引用和验证规则
const ruleFormRef = ref<InstanceType<typeof ElForm>>();
const rules = {
  name: vxRule(),
};

// 多边形特有计算属性：顶点数
const pointCount = computed(() => {
  if (
    formData.value.geojson?.geometry &&
    formData.value.geojson.geometry.type === "Polygon"
  ) {
    const coordinates = formData.value.geojson.geometry.coordinates;
    if (coordinates && coordinates[0] && Array.isArray(coordinates[0])) {
      // 多边形的第一个环的顶点数（不包括闭合点）
      return Math.max(0, coordinates[0].length - 1);
    }
  }
  return 0;
});

// 使用基础Panel逻辑，并提供多边形特有的自定义逻辑
const {
  submitLoading,
  isDrawing,
  formData,
  hasGeometry,
  isEditing,
  startDraw,
  onSubmit,
  onCancel,
} = useBaseDrawPanel(
  {
    featureType: "polygon",
    featureTypeName: "多边形",
    drawMessage: "请在地图上连续点击绘制多边形，双击结束绘制",
    successMessage: "多边形绘制完成",
    placeholder: "请输入多边形名称",
    descriptionPlaceholder: "请输入多边形描述（可选）",
  },
  props,
  emit,
  {
    // 自定义提交前验证：检查表单验证和顶点数
    onBeforeSubmit: async (formData) => {
      if (!ruleFormRef.value) return false;

      try {
        const valid = await ruleFormRef.value.validate();
        if (!valid) return false;

        // 检查顶点数
        if (pointCount.value < 3) {
          console.error("多边形至少需要3个顶点");
          return false;
        }

        return true;
      } catch (error) {
        console.error("表单验证失败:", error);
        return false;
      }
    },
  }
);
</script>

<style lang="scss" scoped></style>
