# MVT图层管线注记功能实现

## 概述

本文档记录了对 `MvtLayer.ts` 的改进，为包含"管线"关键字的MVT图层自动添加注记功能，显示管线的口径和材质信息。

## 功能特性

### 🎯 核心功能

1. **智能检测**：自动检测图层名称是否包含管线相关关键字
2. **注记显示**：显示管线的口径（管径）和材质信息
3. **层级控制**：注记图层仅在缩放级别 ≥ 18 时显示
4. **联动控制**：注记图层与主图层显示状态同步
5. **独立控制**：提供独立控制注记图层显示/隐藏的接口

### 🔍 检测条件

图层名称包含以下任一关键字时，自动启用注记功能：
- `管线`
- `管道`
- `pipeline`
- `pipe`

## 技术实现

### 📊 类属性扩展

```typescript
export class MvtLayer extends BaseLayer {
  /** 注记图层ID */
  private _labelLayerId?: string
  /** 注记图层是否显示 */
  private _labelVisible: boolean = true
  /** 是否为管线图层 */
  private _isPipelineLayer: boolean = false
}
```

### 🔧 核心方法

#### 1. 管线图层检测
```typescript
private checkIsPipelineLayer(): boolean {
  return this.name.includes('管线') || 
         this.name.includes('管道') || 
         this.name.includes('pipeline') ||
         this.name.includes('pipe')
}
```

#### 2. 注记图层创建
```typescript
private createLabelLayer(map: glMap): void {
  // 构建多字段兼容的文本表达式
  // 支持多种字段名称组合
  // 配置注记样式和显示规则
}
```

#### 3. 显示状态控制
```typescript
protected showInternal(show: boolean): void {
  super.showInternal(show)
  // 同步控制注记图层显示状态
}
```

### 📝 数据字段支持

注记功能支持多种字段名称组合：

#### 管径字段（优先级递减）：
1. `gj` - 管径（中文字段）
2. `diameter` - 直径（英文字段）
3. `pipe_diameter` - 管道直径（英文字段）

#### 材质字段（优先级递减）：
1. `cz` - 材质（中文字段）
2. `material` - 材质（英文字段）
3. `pipe_material` - 管道材质（英文字段）

#### 显示格式：
- **完整信息**：`300mm\nPE` （管径 + 换行 + 材质）
- **仅管径**：`300mm`
- **仅材质**：`PE`
- **无信息**：空字符串

### 🎨 注记样式配置

```typescript
const labelLayer = {
  id: this._labelLayerId,
  type: 'symbol',
  source: this.id,
  minzoom: 18, // 仅在缩放级别≥18时显示
  layout: {
    'text-field': textExpression,
    'text-font': ['Open Sans Regular', 'Arial Unicode MS Regular'],
    'text-size': 12,
    'text-anchor': 'center',
    'text-offset': [0, 0],
    'text-allow-overlap': false,
    'text-ignore-placement': false,
    'symbol-placement': 'line', // 沿线放置
    'text-rotation-alignment': 'map',
    'text-pitch-alignment': 'viewport',
    'visibility': 'visible'
  },
  paint: {
    'text-color': '#2C3037',      // 深灰色文字
    'text-halo-color': '#FFFFFF', // 白色光晕
    'text-halo-width': 1,         // 光晕宽度
    'text-opacity': 0.9           // 透明度
  }
}
```

## API接口

### 🔧 公共属性

#### `labelVisible: boolean`
获取或设置注记图层的显示状态
```typescript
// 获取注记显示状态
const isVisible = mvtLayer.labelVisible

// 设置注记显示状态
mvtLayer.labelVisible = true
```

#### `isPipelineLayer: boolean` (只读)
获取是否为管线图层
```typescript
const isPipeline = mvtLayer.isPipelineLayer
```

#### `labelLayerId: string | undefined` (只读)
获取注记图层ID
```typescript
const labelId = mvtLayer.labelLayerId
```

### 🎛️ 公共方法

#### `toggleLabel(): boolean`
切换注记图层显示状态
```typescript
// 切换注记显示状态
const newState = mvtLayer.toggleLabel()
console.log(`注记现在${newState ? '显示' : '隐藏'}`)
```

## 使用示例

### 📋 基本使用

```typescript
// 创建MVT图层
const mvtLayer = new MvtLayer({
  id: 'pipeline-layer',
  name: '智慧水务管线图层', // 包含"管线"关键字
  url: 'https://example.com/tiles/{z}/{x}/{y}.pbf',
  type: 'line',
  paint: {
    'line-color': '#0066CC',
    'line-width': 2
  }
})

// 添加到地图
await mvtLayer.addTo(map)

// 检查是否为管线图层
if (mvtLayer.isPipelineLayer) {
  console.log('这是管线图层，已自动添加注记功能')
}
```

### 🎛️ 注记控制

```typescript
// 隐藏注记
mvtLayer.labelVisible = false

// 显示注记
mvtLayer.labelVisible = true

// 切换注记显示状态
mvtLayer.toggleLabel()

// 获取注记图层ID（用于直接操作MapLibre图层）
const labelLayerId = mvtLayer.labelLayerId
if (labelLayerId) {
  // 可以直接操作MapLibre的注记图层
  map.setPaintProperty(labelLayerId, 'text-color', '#FF0000')
}
```

### 🔄 图层联动

```typescript
// 隐藏主图层时，注记图层也会自动隐藏
mvtLayer.show = false

// 显示主图层时，注记图层根据labelVisible状态决定是否显示
mvtLayer.show = true

// 注记图层的显示需要同时满足：
// 1. 主图层显示 (mvtLayer.show = true)
// 2. 注记开启 (mvtLayer.labelVisible = true)
// 3. 缩放级别 ≥ 18
```

## 数据要求

### 📊 MVT瓦片数据结构

为了正确显示注记，MVT瓦片数据应包含以下属性字段：

```json
{
  "type": "Feature",
  "geometry": {
    "type": "LineString",
    "coordinates": [[lng1, lat1], [lng2, lat2]]
  },
  "properties": {
    "gj": 300,        // 管径（毫米）
    "cz": "PE",       // 材质
    // 或者使用英文字段
    "diameter": 300,
    "material": "PE",
    // 或者使用带前缀的字段
    "pipe_diameter": 300,
    "pipe_material": "PE"
  }
}
```

### 🔧 字段映射

如果数据字段名称不在支持列表中，可以通过以下方式处理：

1. **数据预处理**：在生成MVT瓦片时，将字段名称映射到支持的字段
2. **扩展支持**：修改 `createLabelLayer` 方法中的 `textExpression`，添加新的字段名称

## 性能优化

### 🚀 优化特性

1. **按需创建**：只有管线图层才创建注记图层
2. **层级控制**：注记仅在高缩放级别显示，减少低级别渲染负担
3. **文本优化**：配置合理的文本重叠和放置策略
4. **联动控制**：主图层隐藏时，注记图层也自动隐藏

### 📊 性能建议

1. **数据优化**：确保MVT瓦片数据结构合理，避免冗余字段
2. **缩放控制**：根据实际需求调整 `minzoom` 值
3. **文本策略**：根据数据密度调整 `text-allow-overlap` 等参数

## 兼容性说明

### ✅ 向后兼容

- 不影响现有非管线图层的功能
- 保持原有API接口不变
- 扩展功能为可选特性

### 🔄 升级说明

现有代码无需修改，新功能自动生效：

```typescript
// 现有代码继续工作
const layer = new MvtLayer(options)
await layer.addTo(map)

// 新功能自动启用（如果是管线图层）
// 可选择性使用新的控制接口
if (layer.isPipelineLayer) {
  layer.labelVisible = false // 可选：控制注记显示
}
```

## 故障排除

### 🐛 常见问题

#### 1. 注记不显示
**可能原因**：
- 缩放级别 < 18
- 注记图层被隐藏 (`labelVisible = false`)
- 主图层被隐藏 (`show = false`)
- 数据中缺少支持的字段

**解决方案**：
```typescript
// 检查缩放级别
console.log('当前缩放级别:', map.getZoom())

// 检查注记状态
console.log('注记显示状态:', mvtLayer.labelVisible)
console.log('主图层显示状态:', mvtLayer.show)

// 检查是否为管线图层
console.log('是否为管线图层:', mvtLayer.isPipelineLayer)
```

#### 2. 注记内容为空
**可能原因**：
- MVT数据中缺少管径和材质字段
- 字段名称不在支持列表中

**解决方案**：
```typescript
// 检查数据字段（在浏览器开发者工具中）
map.on('click', 'layer-id', (e) => {
  console.log('要素属性:', e.features[0].properties)
})
```

#### 3. 注记位置不正确
**可能原因**：
- `symbol-placement` 设置不当
- 线要素几何形状问题

**解决方案**：
- 检查线要素的几何形状是否正确
- 调整 `text-offset` 参数

## 扩展建议

### 🚀 功能扩展

1. **多语言支持**：根据地图语言设置显示不同语言的注记
2. **样式主题**：支持不同的注记样式主题
3. **动态字段**：支持运行时配置字段映射
4. **聚合显示**：在低缩放级别显示聚合信息

### 🎨 样式扩展

```typescript
// 可以扩展支持自定义样式
interface LabelStyle {
  textColor?: string
  textSize?: number
  haloColor?: string
  haloWidth?: number
  minZoom?: number
}

// 使用示例
mvtLayer.setLabelStyle({
  textColor: '#FF0000',
  textSize: 14,
  minZoom: 16
})
```

## 总结

通过这次改进，MvtLayer获得了：

- 🎯 **智能识别**：自动检测管线图层并启用注记功能
- 📊 **灵活显示**：支持多种数据字段组合和显示格式
- 🎛️ **精确控制**：提供细粒度的显示控制接口
- 🚀 **性能优化**：按需创建和层级控制
- 🔄 **完全兼容**：不影响现有功能，平滑升级

这些改进使得MVT图层能够更好地服务于管线数据的可视化需求，为用户提供更丰富的信息展示和更好的交互体验。
