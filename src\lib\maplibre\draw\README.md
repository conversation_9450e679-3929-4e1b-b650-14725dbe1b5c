# MapLibre 绘制工具

基于 Terra Draw 的 MapLibre GL JS 绘制编辑工具封装，提供完整的绘制、编辑功能。

## 功能特性

- ✅ **多种绘制模式**：点、线段、多边形、矩形、圆形、自由绘制
- ✅ **完整编辑功能**：移动点、移动线、线上加点、点打断线、删除点
- ✅ **事件系统**：完整的绘制事件监听和处理
- ✅ **样式配置**：可自定义的绘制样式
- ✅ **GeoJSON 兼容**：符合 GeoJSON 标准的几何体输出
- ✅ **TypeScript 支持**：完整的类型定义

## 安装依赖

```bash
npm install terra-draw terra-draw-maplibre-gl-adapter
```

## 基本使用

### 1. 创建绘制管理器

```typescript
import { createDrawManager } from '@/lib/maplibre/draw';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';

// 获取地图实例
const map = AppMaplibre.getMap();

// 创建绘制管理器
const drawManager = createDrawManager(map, {
  config: {
    defaultMode: 'select',
    enabled: true,
    styles: {
      polygon: {
        fillColor: '#3388ff',
        fillOpacity: 0.2,
        outlineColor: '#3388ff',
        outlineWidth: 2
      }
    }
  },
  onEvent: (event) => {
    console.log('绘制事件:', event);
  }
});
```

### 2. 切换绘制模式

```typescript
// 切换到多边形绘制模式
drawManager.setMode('polygon');

// 切换到选择模式
drawManager.setMode('select');

// 切换到点绘制模式
drawManager.setMode('point');
```

### 3. 获取绘制结果

```typescript
// 获取所有绘制的要素
const features = drawManager.getAllFeatures();

// 获取特定要素
const feature = drawManager.getFeatureById('feature-id');

// 导出为 GeoJSON
const geoJson = {
  type: 'FeatureCollection',
  features: features
};
```

### 4. 管理要素

```typescript
// 清除所有要素
drawManager.clearAllFeatures();

// 删除特定要素
drawManager.deleteFeatures(['feature-id-1', 'feature-id-2']);

// 添加要素
const newFeatures = [
  {
    type: 'Feature',
    geometry: {
      type: 'Point',
      coordinates: [103.5, 29.4]
    },
    properties: {}
  }
];
drawManager.addFeatures(newFeatures);
```

## 配置选项

### DrawConfig

```typescript
interface DrawConfig {
  enabled?: boolean;                    // 是否启用绘制工具
  defaultMode?: DrawMode;               // 默认绘制模式
  showControls?: boolean;               // 是否显示控制按钮
  controlPosition?: string;             // 控制按钮位置
  styles?: DrawStyleConfig;             // 样式配置
  editing?: DrawEditConfig;             // 编辑配置
}
```

### 样式配置

```typescript
const styleConfig = {
  point: {
    color: '#3388ff',
    outlineColor: '#ffffff',
    outlineWidth: 2,
    radius: 6
  },
  linestring: {
    color: '#3388ff',
    width: 3,
    opacity: 0.8
  },
  polygon: {
    fillColor: '#3388ff',
    fillOpacity: 0.2,
    outlineColor: '#3388ff',
    outlineWidth: 2
  },
  selected: {
    color: '#ff6b35',
    outlineColor: '#ffffff',
    outlineWidth: 3
  }
};
```

### 编辑配置

```typescript
const editConfig = {
  allowDrag: true,              // 允许拖拽移动
  allowResize: true,            // 允许调整大小
  allowRotate: false,           // 允许旋转
  allowAddPointOnLine: true,    // 允许在线上添加点
  allowDeletePoint: true,       // 允许删除点
  minPoints: 3,                 // 最小点数限制
  maxPoints: 100                // 最大点数限制
};
```

## 事件处理

### 事件类型

```typescript
enum DrawEventType {
  DRAW_START = 'draw.start',        // 开始绘制
  DRAW_FINISH = 'draw.finish',      // 绘制完成
  DRAW_CANCEL = 'draw.cancel',      // 绘制取消
  FEATURE_SELECT = 'feature.select', // 要素选中
  FEATURE_DESELECT = 'feature.deselect', // 要素取消选中
  FEATURE_UPDATE = 'feature.update', // 要素更新
  FEATURE_DELETE = 'feature.delete', // 要素删除
  MODE_CHANGE = 'mode.change'       // 模式改变
}
```

### 事件监听

```typescript
import { DrawEventTypeEnum } from '@/lib/maplibre/draw';

// 监听绘制完成事件
drawManager.addEventListener(DrawEventTypeEnum.DRAW_FINISH, (event) => {
  console.log('绘制完成:', event.features);
});

// 监听要素选择事件
drawManager.addEventListener(DrawEventTypeEnum.FEATURE_SELECT, (event) => {
  console.log('选中要素:', event.features);
});

// 监听模式改变事件
drawManager.addEventListener(DrawEventTypeEnum.MODE_CHANGE, (event) => {
  console.log('模式改变:', event.mode);
});
```

## 绘制模式

### 支持的绘制模式

- **select**: 选择模式，用于选择和编辑已有要素
- **point**: 点绘制模式
- **linestring**: 线段绘制模式
- **polygon**: 多边形绘制模式
- **rectangle**: 矩形绘制模式
- **circle**: 圆形绘制模式
- **freehand**: 自由绘制模式

### 编辑功能

在选择模式下，支持以下编辑操作：

1. **移动要素**: 拖拽要素进行移动
2. **移动顶点**: 拖拽顶点改变形状
3. **添加顶点**: 在线段中点添加新顶点
4. **删除顶点**: 删除选中的顶点
5. **调整大小**: 缩放要素大小
6. **旋转**: 旋转要素（可配置）

## 完整示例

```vue
<template>
  <div class="draw-example">
    <div class="controls">
      <select v-model="currentMode" @change="changeMode">
        <option value="select">选择</option>
        <option value="point">点</option>
        <option value="linestring">线段</option>
        <option value="polygon">多边形</option>
        <option value="rectangle">矩形</option>
      </select>
      <button @click="clearAll">清除所有</button>
      <button @click="exportGeoJSON">导出GeoJSON</button>
    </div>
    <div class="status">
      要素数量: {{ featureCount }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import { createDrawManager, DrawEventTypeEnum } from '@/lib/maplibre/draw';
import type { DrawManager } from '@/lib/maplibre/draw';

const currentMode = ref('select');
const featureCount = ref(0);
let drawManager: DrawManager | null = null;

const changeMode = () => {
  drawManager?.setMode(currentMode.value as any);
};

const clearAll = () => {
  drawManager?.clearAllFeatures();
  featureCount.value = 0;
};

const exportGeoJSON = () => {
  if (drawManager) {
    const features = drawManager.getAllFeatures();
    const geoJson = {
      type: 'FeatureCollection',
      features: Array.isArray(features) ? features : [features]
    };
    console.log('GeoJSON:', JSON.stringify(geoJson, null, 2));
  }
};

onMounted(() => {
  const map = AppMaplibre.getMap();
  if (map) {
    drawManager = createDrawManager(map, {
      config: {
        defaultMode: 'select',
        enabled: true
      },
      onEvent: (event) => {
        if (event.type === DrawEventTypeEnum.DRAW_FINISH) {
          featureCount.value = drawManager?.getState().featureCount || 0;
        }
      }
    });
  }
});

onUnmounted(() => {
  drawManager?.destroy();
});
</script>
```

## API 参考

### DrawManager

#### 方法

- `start()`: 启动绘制工具
- `stop()`: 停止绘制工具
- `setMode(mode: DrawMode)`: 设置绘制模式
- `getCurrentMode()`: 获取当前绘制模式
- `getAllFeatures()`: 获取所有要素
- `getFeatureById(id: string)`: 根据ID获取要素
- `deleteFeatures(ids: string[])`: 删除要素
- `clearAllFeatures()`: 清除所有要素
- `addFeatures(features: any[])`: 添加要素
- `validateGeometry(geometry: any)`: 验证几何体
- `getState()`: 获取当前状态
- `updateStyles(styles: any)`: 更新样式
- `addEventListener(type, callback)`: 添加事件监听
- `removeEventListener(type, callback)`: 移除事件监听
- `destroy()`: 销毁绘制工具

### 工具函数

- `createDrawManager(map, options)`: 创建绘制管理器

## 注意事项

1. **地图初始化**: 确保在地图完全加载后再初始化绘制工具
2. **内存管理**: 组件销毁时记得调用 `destroy()` 方法
3. **事件处理**: 合理使用事件监听，避免内存泄漏
4. **样式配置**: 样式更新会重新创建绘制模式，可能影响性能
5. **要素管理**: 大量要素时注意性能优化

## 故障排除

### 常见问题

1. **绘制工具无法启动**
   - 检查地图是否已初始化
   - 确认 Terra Draw 依赖已正确安装

2. **样式不生效**
   - 检查样式配置格式是否正确
   - 确认样式属性名称是否匹配

3. **事件不触发**
   - 检查事件类型是否正确
   - 确认事件监听器是否正确添加

4. **要素无法编辑**
   - 确认当前模式为选择模式
   - 检查编辑配置是否启用相应功能

### 调试建议

1. 开启控制台日志查看详细信息
2. 使用浏览器开发者工具检查网络请求
3. 检查 Terra Draw 版本兼容性
4. 验证 GeoJSON 数据格式正确性 