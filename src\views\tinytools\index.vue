<!--
 * @fileoverview 微工具组件
 * @description 地图操作工具栏组件，提供各种地图操作功能的快捷入口
 * <AUTHOR>
 * @version 2.0.0
 * @date 2024-01-16
-->
<template>
  <div
    class="base-operate"
    :class="
      route.name === 'CesiumHome' && drawerView ? 'base-rt' : 'base-false'
    "
  >
    <div
      class="operate-item flex-c"
      @click="operateClick(item)"
      :class="[item.highLight === 1 ? 'active' : 'disactive']"
      v-for="(item, index) in visualToolList"
      :key="index"
    >
      <!-- <span class="label">{{ item.name }}</span> -->
      <div
        class="w-26px h-26px custom-tooltip"
        @mouseover="item.isShow = true"
        @mouseout="item.isShow = false"
      >
        <span class="tooltip-text">{{ item.name }}</span>
        <img
          :src="
            item.highLight === 1 || item.isShow
              ? getImages(`tools/${item.img}-active.png`)
              : getImages(`tools/${item.img}.png`)
          "
          class="w-26px h-26px"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, watch, ref } from "vue";
import { useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import { useOperateToolStore } from "@/stores/OperateToolStore";
import { useToggle3D } from "@/stores/toggle3D";
import { useDialogStore } from "@/stores/Dialogs";
import { getImages } from "@/utils/getImages";
import { ElMessage } from "element-plus";
import { AppCesium } from "@/lib/cesium/AppCesium";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { MapLibreCanvasScreenshot } from "@/utils/MapLibreCanvasScreenshot";
import { printMapLibreMap } from "@/utils/MapLibrePrintManager";
import { toggleFullScreen } from "@/utils/FullScreenManager";
/**
 * @description 组件属性定义
 */
const props = defineProps({
  /** 地图引擎类型 */
  type: {
    type: String,
    default: () => "cesium",
    validator: (value: string) => ["cesium", "maplibre"].includes(value),
  },
});
const route = useRoute();
const isShow = ref(false);
const drawerStore = useDrawerStore();
const { drawerView } = storeToRefs(drawerStore);
// 存储实例
const operateToolStore = useOperateToolStore();
const toggle3DStore = useToggle3D();
const dialogStore = useDialogStore();

// 响应式状态
const { visualToolList } = storeToRefs(operateToolStore);
const { is3D } = storeToRefs(toggle3DStore);

/**
 * @description 组件挂载时初始化
 */
onMounted(() => {
  operateToolStore.changeSystem(props.type);
  console.log(`微工具组件初始化完成，引擎类型: ${props.type}`);
});

/**
 * @description 组件卸载时清理资源
 */
onUnmounted(() => {
  // 清理透明度切换的防抖定时器
  if (transparencyDebounceTimer) {
    clearTimeout(transparencyDebounceTimer);
    transparencyDebounceTimer = null;
  }

  console.log("微工具组件已卸载，资源清理完成");
});

/**
 * @description 获取工具提示内容
 * @param {any} item - 工具项
 * @returns {string} 提示文本
 */
const getTooltipContent = (item: any): string => {
  if (item.path === "Toggle3D") {
    return `${item.name} (当前: ${toggle3DStore.getModeDescription()})`;
  }
  return item.name;
};

/**
 * @description 处理工具项点击事件
 * @param {any} operate - 被点击的操作工具项
 */
const operateClick = async (operate: any): Promise<void> => {
  try {
    // 切换工具高亮状态
    operateToolStore.toggleHighLight(operate);

    // // 特殊处理二三维切换功能
    // if (operate.path === "Toggle3D") {
    //   handleToggle3D(operate);
    //   return;
    // }
    if (operate.path === "ResetView") {
      await handleResetView(operate);
      operateToolStore.disactive(operate.path);
      return;
    }

    if (operate.path === "ResetRegion") {
      await handleResetRegion(operate);
      operateToolStore.disactive(operate.path);
      return;
    }

    if (operate.path === "RollingCompare") {
      handleRollingCompare(operate);
      operateToolStore.disactive(operate.path);
      return;
    }

    if (operate.path === "ScreenCapture") {
      handleScreenCapture();
      operateToolStore.disactive(operate.path);
      return;
    }

    if (operate.path === "FullScreen") {
      handleFullScreen();
      operateToolStore.disactive(operate.path);
      return;
    }

    if (operate.path === "DrawPrint") {
      handleDrawPrint();
      operateToolStore.disactive(operate.path);
      return;
    }

    if (operate.path === "GroundTransparent") {
      handleGroundTransparent(operate);
      operateToolStore.disactive(operate.path);
      return;
    }

    // 处理其他工具的对话框显示
    handleToolDialog(operate);
  } catch (error) {
    console.error("工具操作失败:", error);
    ElMessage.error(`${operate.name}操作失败`);
  }
};

const handleScreenCapture = async () => {
  try {
    // 显示截图提示
    // ElMessage.info("开始MapLibre地图截图，正在处理...");

    // 首先测试Canvas状态
    const screenshotter = new MapLibreCanvasScreenshot();
    const canvasInfo = screenshotter.getCanvasInfo();
    console.log("📋 Canvas状态信息:", canvasInfo);

    // 如果Canvas状态显示preserveDrawingBuffer为false，说明配置还有问题
    if (canvasInfo && !canvasInfo.preserveDrawingBuffer) {
      ElMessage.warning("Canvas配置可能有问题，preserveDrawingBuffer为false");
    }

    // 尝试截图
    const result = await screenshotter.takeScreenshot({
      format: "png",
      quality: 0.9,
      fileName: "maplibre_map_test",
      autoDownload: true,
      copyToClipboard: true,
    });

    if (result.success && result.canvasInfo) {
      console.log("📸 MapLibre截图完成:", {
        fileName: result.fileName,
        size: `${result.canvasInfo.width}x${result.canvasInfo.height}px`,
        fileSize: `${(result.canvasInfo.fileSize / 1024).toFixed(2)}KB`,
        format: result.canvasInfo.format,
        timestamp: new Date().toISOString(),
      });

      ElMessage.success(
        `截图成功！文件大小: ${(result.canvasInfo.fileSize / 1024).toFixed(
          2
        )}KB`
      );
    } else {
      console.error("❌ 截图失败:", result.error);
      ElMessage.error(`截图失败: ${result.error || "未知错误"}`);
    }
  } catch (error: any) {
    console.error("📸 截图失败:", error);
    ElMessage.error(`截图失败: ${error.message || error}`);
  }
};

/**
 * @description 处理全屏功能
 */
const handleFullScreen = async () => {
  try {
    // ElMessage.info("正在切换全屏状态...");

    const result = await toggleFullScreen();

    if (result.success) {
      const statusText = result.isFullScreen ? "进入" : "退出";
      console.log(`🖥️ ${statusText}全屏成功:`, result.message);
      // ElMessage.success(result.message);
    } else {
      console.error("❌ 全屏操作失败:", result.error);
      // ElMessage.error(result.message);
    }
  } catch (error: any) {
    console.error("🖥️ 全屏功能错误:", error);
    ElMessage.error(`全屏操作失败: ${error.message || error}`);
  }
};

/**
 * @description 处理卷帘对比
 * @param {any} operate - 卷帘对比工具项
 */
const handleRollingCompare = (operate: any): void => {
  console.log("🎬 [TinyTools] 打开卷帘对比对话框...");

  // 确保operate对象有正确的属性，然后使用 handleToolDialog
  operate.type = props.type; // 添加地图类型到operate对象
  handleToolDialog(operate);
};

/**
 * @description 处理复位范围
 * @param {any} operate - 复位范围工具项
 */
const handleResetRegion = (operate: any): void => {
  if (props.type === "maplibre") {
    AppMaplibre.resetRegion();
  }
};

/**
 * @description 处理复位视角
 * @param {any} operate - 复位视角工具项
 */
const handleResetView = (operate: any): void => {
  if (props.type === "cesium") {
    AppCesium.getInstance().goToHome();
  } else {
    AppMaplibre.goToHome();
  }
};

// 地表透明度状态管理
const isGroundTransparent = ref(false);
const isTransparencyProcessing = ref(false);
let transparencyDebounceTimer: number | null = null;

/**
 * @description 处理地表透明功能
 * @param operate 操作对象
 */
const handleGroundTransparent = async (operate: any): Promise<void> => {
  const { Cesium } = BC.Namespace;
  console.log("🌍 开始处理地表透明");

  // 防抖处理，避免快速连续点击
  if (transparencyDebounceTimer) {
    clearTimeout(transparencyDebounceTimer);
  }

  transparencyDebounceTimer = setTimeout(async () => {
    await executeTransparencyToggle(operate);
  }, 1000);
};

/**
 * @description 执行透明度切换
 * @param operate 操作对象
 */
const executeTransparencyToggle = async (operate: any): Promise<void> => {
  if (props.type !== "cesium") {
    ElMessage.warning("此功能仅在三维模式下可用");
    return;
  }

  if (isTransparencyProcessing.value) {
    // ElMessage.info("透明度切换正在进行中，请稍候...");
    return;
  }

  try {
    isTransparencyProcessing.value = true;
    const { Cesium } = BC.Namespace;
    const viewer = AppCesium.getInstance().getViewer();

    // 切换透明度状态
    isGroundTransparent.value = !isGroundTransparent.value;
    const targetTransparent = isGroundTransparent.value;
    const alphaValue = targetTransparent ? 0.4 : 1.0;

    console.log(
      `🔄 切换地表透明度: ${
        targetTransparent ? "透明" : "不透明"
      } (alpha: ${alphaValue})`
    );

    let processedCount = 0;
    let totalCount = 0;

    // 遍历所有倾斜摄影图层
    await new Promise<void>((resolve, reject) => {
      try {
        viewer.eachLayer((layer: any) => {
          if (layer.attr && layer.attr.type === "reality") {
            const overlays = layer.getOverlaysByAttr("id", layer.attr.id);
            if (overlays && overlays.length > 0) {
              totalCount += overlays.length;

              overlays.forEach(async (tilesetInstance: any, index: number) => {
                try {
                  await processTilesetTransparency(
                    tilesetInstance,
                    targetTransparent,
                    alphaValue
                  );
                  processedCount++;

                  // 检查是否全部处理完成
                  if (processedCount === totalCount) {
                    resolve();
                  }
                } catch (error) {
                  console.error(`处理第${index + 1}个倾斜摄影图层失败:`, error);
                  processedCount++;
                  if (processedCount === totalCount) {
                    resolve();
                  }
                }
              });
            }
          }
        }, this);

        // 如果没有找到任何倾斜摄影图层
        if (totalCount === 0) {
          console.warn("未找到倾斜摄影图层");
          ElMessage.warning("未找到可设置透明度的倾斜摄影图层");
          isGroundTransparent.value = !isGroundTransparent.value; // 回滚状态
          resolve();
        }
      } catch (error) {
        reject(error);
      }
    });

    // 更新工具状态
    operate.highLight = targetTransparent ? 1 : 0;

    const statusText = targetTransparent ? "透明" : "不透明";
    console.log(
      `✅ 地表透明度切换完成: ${statusText}，处理了 ${processedCount} 个图层`
    );
    // ElMessage.success(`地表模型已设置为${statusText}状态`);
  } catch (error) {
    console.error("❌ 地表透明度切换失败:", error);
    ElMessage.error("地表透明度切换失败，请重试");

    // 回滚状态
    isGroundTransparent.value = !isGroundTransparent.value;
    operate.highLight = isGroundTransparent.value ? 1 : 0;
  } finally {
    isTransparencyProcessing.value = false;
  }
};

/**
 * @description 处理单个Tileset的透明度设置
 * @param tilesetInstance BC Tileset实例
 * @param transparent 是否透明
 * @param alphaValue 透明度值
 */
const processTilesetTransparency = async (
  tilesetInstance: any,
  transparent: boolean,
  alphaValue: number
): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      const tileset = tilesetInstance.delegate;

      if (!tileset) {
        console.warn("无效的Tileset对象");
        resolve();
        return;
      }

      const applyTransparency = () => {
        try {
          if (transparent) {
            // 启用透明度
            console.log(`🎨 设置透明度: alpha=${alphaValue}`);

            // 方法1：使用Cesium3DTileStyle（推荐）
            tileset.style = new BC.Namespace.Cesium.Cesium3DTileStyle({
              color: {
                conditions: [["true", `rgba(255, 255, 255, ${alphaValue})`]],
              },
            });

            // 性能优化设置（仅在透明时应用）
            tileset.skipLevelOfDetail = true;
            tileset.baseScreenSpaceError = 1024;
            tileset.skipScreenSpaceErrorFactor = 16;
            tileset.skipLevels = 1;
            tileset.immediatelyLoadDesiredLevelOfDetail = false;
            tileset.loadSiblings = false;
            tileset.cullWithChildrenBounds = true;
          } else {
            // 恢复不透明状态
            console.log("🔄 恢复不透明状态");

            // 完全重置样式
            tileset.style = undefined;

            // 恢复默认性能设置
            tileset.skipLevelOfDetail = false;
            tileset.baseScreenSpaceError = 16;
            tileset.skipScreenSpaceErrorFactor = 2;
            tileset.skipLevels = 0;
            tileset.immediatelyLoadDesiredLevelOfDetail = true;
            tileset.loadSiblings = true;
            tileset.cullWithChildrenBounds = false;
          }

          resolve();
        } catch (styleError) {
          console.error("样式设置失败，尝试其他方法:", styleError);

          // 备用方案：尝试使用CustomShader
          try {
            if (transparent) {
              tileset.customShader = new BC.Namespace.Cesium.CustomShader({
                uniforms: {
                  u_transparency: {
                    value: alphaValue,
                    type: BC.Namespace.Cesium.UniformType.FLOAT,
                  },
                },
                fragmentShaderText: `
                  void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
                    material.diffuse.a *= u_transparency;
                    material.alpha *= u_transparency;
                  }
                `,
              });
            } else {
              tileset.customShader = undefined;
            }
            resolve();
          } catch (shaderError) {
            console.error("CustomShader设置也失败:", shaderError);
            reject(shaderError);
          }
        }
      };

      // 检查Tileset是否已准备就绪
      if (tileset.ready) {
        applyTransparency();
      } else {
        // 等待Tileset加载完成
        tileset.readyPromise
          .then(() => {
            applyTransparency();
          })
          .catch((loadError: any) => {
            console.error("Tileset加载失败:", loadError);
            reject(loadError);
          });
      }
    } catch (error) {
      console.error("处理Tileset透明度失败:", error);
      reject(error);
    }
  });
};

/**
 * @description 处理制图打印功能
 * @details 调用MapLibre地图的打印功能，生成专业的打印页面
 */
const handleDrawPrint = async (): Promise<void> => {
  console.log("🖨️ 开始处理制图打印");

  // 只支持MapLibre引擎
  if (props.type === "cesium") {
    ElMessage.warning("制图打印功能暂不支持三维模式，请切换到二维地图模式");
    return;
  }

  try {
    // 显示开始打印的提示
    // ElMessage.info("正在准备制图打印，请稍候...");

    // 调用制图打印功能 - 使用iframe方案避免新窗口问题
    const result = await printMapLibreMap({
      title: "四川省乐山市沙湾区供水管网地图",
      quality: 0.95,
      includeMapInfo: false,
      orientation: "landscape",
      printMethod: "iframe", // 使用iframe方案，避免新窗口不关闭的问题
    });

    if (result.success) {
      console.log("✅ 制图打印成功:", {
        title: "供水管网地图",
        printMethod: "iframe",
        mapInfo: result.mapInfo,
        timestamp: new Date().toISOString(),
      });

      // ElMessage.success("制图打印已完成，请检查打印机状态");
    } else {
      console.error("❌ 制图打印失败:", result.error);
      ElMessage.error(`制图打印失败: ${result.error || "未知错误"}`);
    }
  } catch (error: any) {
    console.error("🖨️ 制图打印异常:", error);
    ElMessage.error(`制图打印失败: ${error.message || error}`);
  }
};



/**
 * @description 处理工具对话框显示
 * @param {any} operate - 工具项
 */
const handleToolDialog = (operate: any): void => {
  // 只有激活状态才打开对话框
  if (operate.highLight === 1) {
    dialogStore.addDialog({
      name: operate.name,
      path: operate.path,
      type: operate.type,
    });

    console.log(`已打开工具对话框: ${operate.name}`);
  }
};

/**
 * @description 监听引擎类型变化，更新工具列表
 */
watch(
  () => props.type,
  (newType) => {
    operateToolStore.changeSystem(newType);
    console.log(`引擎类型变化: ${newType}`);
  }
);
</script>

<style lang="scss" scoped>
/**
 * 操作工具栏样式
 */
.base-operate {
  width: 60px;
  position: absolute;
  right: 16px;
  top: 16px;
  z-index: 1;
  background: #fff;
  box-sizing: border-box;
  padding: 10px 10px 0 10px;
  cursor: pointer;
  border-radius: 4px;
  // text-align: center;

  .operate-item {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
    &:hover {
      border-radius: 4px;
      background: linear-gradient(
        90deg,
        rgb(4, 83, 255),
        rgb(50, 127, 254) 100%
      );
    }
  }
  .active {
    border-radius: 4px;
    background: linear-gradient(90deg, rgb(4, 83, 255), rgb(50, 127, 254) 100%);
  }
}
.base-rt {
  top: 14px;
  right: 430px;
}
.base-false {
  right: 30px;
}
/* 响应式设计 */
@media (max-width: 768px) {
  .base-operate {
    right: 15px;
    top: 120px;

    .operate-item {
      width: 45px;
      height: 45px;
      margin: 0 0 8px 0;

      img {
        width: 35px;
        height: 35px;
      }
    }
  }
}
.custom-tooltip {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.custom-tooltip .tooltip-text {
  visibility: hidden;
  width: 60px;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 0;
  right: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;

  /* 自定义样式 */
  font-family: Arial, sans-serif;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

// .custom-tooltip .tooltip-text::after {
//   content: "";
//   position: absolute;
//   top: 100%;
//   left: 50%;
//   margin-left: -5px;
//   border-width: 5px;
//   border-style: solid;
//   border-color: #555 transparent transparent transparent;
// }

.custom-tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}
</style>
