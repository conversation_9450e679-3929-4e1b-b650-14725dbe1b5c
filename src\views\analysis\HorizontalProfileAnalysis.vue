<template>
  <page-card :close-icon="false" class="tabulate-sta" title="纵断面分析">
    <!-- 操作按钮区域 -->
    <el-button 
      type="primary" 
      class="select-btn w-130px h-9" 
      :loading="isSelecting"
      @click="select"
    >
      拾取管线
    </el-button>

    <el-button 
      class="clear-btn w-130px h-9" 
      @click="removeLayer"
      :disabled="isSelecting"
    >
      清除
    </el-button>

    <!-- 分析状态显示 -->
    <!-- <div v-if="currentSelected && showSvg" class="analysis-info">
      <el-text class="info-label">已选择管线：</el-text>
      <span class="info-text">{{ currentSelected.name || currentSelected.id }}</span>
      <span class="pipe-count">（共 {{ analyzeData.length }} 条相关管线）</span>
    </div> -->

    <!-- 图表显示区域 -->
    <div v-if="showSvg" class="chart-container">
      <!-- 左侧固定标签区域 -->
      <div class="fixed-labels">
        <div class="label-item" style="top: 254px;">管点编码</div>
        <div class="label-item" style="top: 274px;">地面高程/m</div>
        <div class="label-item" style="top: 294px;">管点高程/m</div>
        <div class="label-item" style="top: 314px;">埋深/m</div>
        <div class="label-item" style="top: 334px;">管线长度/m</div>
        <div class="label-item" style="top: 354px;">断面尺寸/mm</div>
      </div>

      <!-- 可滚动的图表区域 -->
      <div class="scrollable-chart">
        <div id="longitudinal-chart" class="d3Chart"></div>
      </div>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import { ref, onUnmounted, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { AppCesium } from "@/lib/cesium/AppCesium";
import {
  queryCrossSectionalV,
  lineListByBms,
} from "@/api/analysis";
import { uuid } from "@/utils/uuid";

// D3相关导入 - 使用动态导入绕过类型检查
// @ts-ignore
import * as d3Import from "d3";
// @ts-ignore
import * as turfImport from "@turf/turf";
import type { MapEngineType } from "@/components/BufferAnalysisDrawButtons.vue";

const d3 = d3Import as any;
const turf = turfImport as any;

/**
 * @interface SelectedPipeline 选中的管线接口
 */
interface SelectedPipeline {
  id: string;
  item: any;
  name: string;
  geometry?: any;
}

/**
 * @interface HighlightFeature 高亮特征接口
 */
interface HighlightFeature {
  type: "Feature";
  id: string;
  geometry: any;
  properties: {
    color: string;
    pipeCode: string;
  };
}

const route = useRoute();
const mapEngine = computed((): MapEngineType => {
  return route.path.includes("cesium") ? "cesium" : "maplibre";
});

// 组件状态
const showSvg = ref<boolean>(false);
const analyzeData = ref<any[]>([]);
const currentSelected = ref<SelectedPipeline | null>(null);
const isSelecting = ref<boolean>(false);

// Cesium图层管理
const cesiumHighlightLayer = ref<any>(null);

/**
 * @method select 拾取管线功能（支持2D/3D）
 */
const select = (): void => {
  if (isSelecting.value) return;
  
  // 清除之前的分析结果
  removeLayer();
  
  if (mapEngine.value === 'cesium') {
    selectCesiumPipeline();
  } else {
    selectMaplibrePipeline();
  }
};

/**
 * @method selectCesiumPipeline Cesium三维管线拾取
 */
const selectCesiumPipeline = (): void => {
  try {
    isSelecting.value = true;
    ElMessage.info('请在3D地图上点击管线进行拾取');

    AppCesium.getInstance().selectPipeComponent((res: any) => {
      try {
        if (res && res.type === 'line' && res.id && res.position) {
          const selectedPipeline: SelectedPipeline = {
            id: res.id,
            item: res.properties || {},
            name: res.properties?.qdbh || res.properties?.zdbh || res.id,
            geometry: res.geometry
          };

          handlePipelineSelection(selectedPipeline, 'line');
        } else {
          ElMessage.warning('请点击管线位置进行拾取');
        }
      } catch (error) {
        console.error('处理Cesium管线选择失败:', error);
        ElMessage.error('管线选择处理失败');
      } finally {
        isSelecting.value = false;
      }
    });
  } catch (error) {
    console.error('Cesium管线拾取失败:', error);
    ElMessage.error('管线拾取失败，请重试');
    isSelecting.value = false;
  }
};

/**
 * @method selectMaplibrePipeline MapLibre二维管线拾取
 */
const selectMaplibrePipeline = (): void => {
  try {
    isSelecting.value = true;
    const map = AppMaplibre.getMap();

    if (!map) {
      ElMessage.error('地图实例不存在');
      isSelecting.value = false;
      return;
    }

    ElMessage.info('请在地图上点击管线进行拾取');

    map.once("click", (e: any) => {
      try {
        let degree = 10;
        var features = map.queryRenderedFeatures(
          [
            [e.point.x - degree / 2, e.point.y - degree / 2],
            [e.point.x + degree / 2, e.point.y + degree / 2],
          ],
          {
            layers: ["mvt_pipeLine"],
          }
        );

        if (features.length > 1) {
          // ElMessage.info("检测到多条管线，选择第一条进行分析");
          handlePipelineSelection({
            id: features[0].properties.bm,
            item: features[0].properties,
            name: features[0].properties.qdbh,
          });
        } else if (features.length === 1) {
          handlePipelineSelection({
            id: features[0].properties.bm,
            item: features[0].properties,
            name: features[0].properties.qdbh,
          });
        } else {
          ElMessage.warning("请在管线上拾取！");
        }
      } catch (error) {
        console.error('MapLibre管线拾取处理失败:', error);
        ElMessage.error('管线拾取处理失败');
      } finally {
        isSelecting.value = false;
      }
    });
  } catch (error) {
    console.error('MapLibre管线拾取失败:', error);
    ElMessage.error('管线拾取失败，请重试');
    isSelecting.value = false;
  }
};

/**
 * @method handlePipelineSelection 处理管线选择
 * @param {SelectedPipeline} pipeline - 选中的管线
 */
const handlePipelineSelection = (pipeline: SelectedPipeline, type: string = 'point'): void => {
  currentSelected.value = pipeline;
  
  // 根据管线ID或起点编号查找上下游
  const nodeId = pipeline.item?.qdbh || pipeline.name || pipeline.id;
  if (nodeId) {
    findUpAndDownStream(nodeId, type);
  } else {
    ElMessage.warning('无法获取管线节点信息');
    isSelecting.value = false;
  }
};

/**
 * @method findUpAndDownStream 根据nodeId找上下游管线
 * @param {string} nodeId - 节点ID
 */
const findUpAndDownStream = async (nodeId: string, type: string = 'point'): Promise<void> => {
  try {
    // ElMessage.info("正在进行纵断面分析...");
    let params: any = {
      nodeId: nodeId,
    }
    if(type === 'line'){
      params = {
        edgeId: nodeId.replace('_', ''),
      }
    }
    // 调用纵断面分析接口
    const analysisResult = await queryCrossSectionalV(params);
    console.log("纵断面分析API响应:", analysisResult);

    if (analysisResult.status === 200 && analysisResult.data) {
      const result = analysisResult.data;

      // 提取受影响管线ID
      const pipeIds = result.data.edges
        ? result.data.edges.map((item: any) => item.id)
        : [];

      console.log("提取到的管线ID:", pipeIds);

      if (pipeIds.length > 0) {
        // 查询管线详细信息
        const pipeData = await lineListByBms(pipeIds);
        console.log("管线详情查询结果:", pipeData);

        if (
          pipeData.code === 200 &&
          pipeData.data &&
          pipeData.data.length > 0
        ) {
          const pipeDisplayData = pipeData.data;
          analyzeData.value = pipeDisplayData;

          // 在地图上高亮显示管线
          const features: HighlightFeature[] = pipeDisplayData.map((item: any) => ({
            type: "Feature",
            id: uuid(),
            geometry: JSON.parse(item.geojson || "{}"),
            properties: {
              color: "#ff6600", // 橙色高亮
              pipeCode: item.bm || item.gxbm || item.id,
            },
          }));

          // 根据地图引擎类型选择高亮方式
          if (mapEngine.value === 'cesium') {
            addCesiumHighlightLine(features);
          } else {
            addMaplibreHighlightLine(features);
          }

          showSvg.value = true;

          // 延迟绘制D3图表，确保地图图层已添加
          setTimeout(() => {
            d3draw(pipeDisplayData);
            // ElMessage.success(
            //   `纵断面分析完成，找到 ${pipeDisplayData.length} 条相关管线`
            // );
          }, 500);

          console.log(`地图上已显示 ${pipeDisplayData.length} 条管线`);
        } else {
          ElMessage.warning("未查询到管线详细信息");
        }
      } else {
        ElMessage.warning("分析结果中没有找到相关管线");
      }
    } else {
      ElMessage.error("纵断面分析失败，请重试");
    }
  } catch (error) {
    console.error("纵断面分析失败:", error);
    ElMessage.error(
      `分析失败：${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isSelecting.value = false;
  }
};

/**
 * @method addCesiumHighlightLine 在Cesium中添加高亮线条
 * @param {HighlightFeature[]} features - 要高亮的特征数组
 */
const addCesiumHighlightLine = (features: HighlightFeature[]): void => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    
    if (!viewer) {
      console.error('Cesium viewer实例不存在');
      return;
    }

    // 清除之前的高亮图层
    if (cesiumHighlightLayer.value) {
      cesiumHighlightLayer.value.clear();
      viewer.removeLayer(cesiumHighlightLayer.value);
      cesiumHighlightLayer.value = null;
    }

    // 创建新的VectorLayer
    cesiumHighlightLayer.value = new BC.VectorLayer('longitudinal-highlight-layer');
    viewer.addLayer(cesiumHighlightLayer.value);

    // 添加高亮线条到图层
    features.forEach((feature, index) => {
      if (feature.geometry && feature.geometry.coordinates) {
        try {
          let positions: any[] = [];

          if (feature.geometry.type === 'MultiLineString') {
            // 处理多线段
            feature.geometry.coordinates.forEach((lineString: number[][]) => {
              const linePositions = lineString.map((coord: number[]) => {
                return new BC.Position(coord[0], coord[1], coord[2] || 25);
              });
              positions = positions.concat(linePositions);
            });
          } else if (feature.geometry.type === 'LineString') {
            // 处理单线段
            positions = feature.geometry.coordinates.map((coord: number[]) => {
              return new BC.Position(coord[0], coord[1], coord[2] || 25);
            });
          }

          if (positions.length > 0) {
            // 创建BC线段并添加到图层
            const polylineOverlay = new BC.Polyline(positions);
            polylineOverlay.id = `longitudinal-highlight-${index}`;
            polylineOverlay.setStyle({
              width: 6,
              material: BC.Color.ORANGE.withAlpha(0.9), // 橙色高亮
              clampToGround: false,
            });

            cesiumHighlightLayer.value.addOverlay(polylineOverlay);
          }
        } catch (error) {
          console.warn(`添加第${index}条高亮线失败:`, error);
        }
      }
    });

    console.log(`已在Cesium中添加 ${features.length} 条高亮管线`);
  } catch (error) {
    console.error('Cesium高亮线条添加失败:', error);
  }
};

/**
 * @method addMaplibreHighlightLine 在MapLibre中添加高亮线条
 * @param {HighlightFeature[]} features - 要高亮的特征数组
 */
const addMaplibreHighlightLine = (features: HighlightFeature[]): void => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      console.error("地图实例不存在，无法添加高亮图层");
      return;
    }

    // 移除已有的高亮图层
    if (map.getLayer("highlightline")) {
      map.removeLayer("highlightline");
    }
    if (map.getSource("highlightline")) {
      map.removeSource("highlightline");
    }

    // 添加新的高亮图层
    map.addSource("highlightline", {
      type: "geojson",
      data: {
        type: "FeatureCollection",
        features: features,
      },
    });

    map.addLayer({
      id: "highlightline",
      type: "line",
      source: "highlightline",
      layout: {
        "line-join": "round",
        "line-cap": "round",
      },
      paint: {
        "line-color": "#ff6600", // 橙色高亮
        "line-width": 4,
        "line-opacity": 0.8,
      },
    });

    console.log(`已在MapLibre中添加高亮图层，显示 ${features.length} 条管线`);
  } catch (error) {
    console.error('MapLibre高亮线条添加失败:', error);
  }
};

/**
 * @method d3draw D3绘制纵断面图表
 * @param {any[]} testdata - 图表数据
 */
const d3draw = (testdata: any[]): void => {
  try {
    // 计算管线长度（保留用于显示）
    testdata.forEach((item: any) => {
      if (item.geojson) {
        item.gxcd = turf.length(JSON.parse(item.geojson), { units: "meters" });
      } else {
        item.gxcd = item.gdcd || 0;
      }
    });

    // 使用固定间距设置x坐标，而不是基于管线长度计算
    const fixedSpacing = 90; // 固定间距（像素）
    testdata.forEach((item: any, index: number) => {
      item.xdata = index * fixedSpacing; // 每个点之间固定间距
    });

    console.log("纵断面分析数据（固定间距）:", testdata);

    // 基于固定间距计算图表宽度
    let width = Math.max(testdata.length * fixedSpacing, 650); // 确保最小宽度
    const height = 200;

    // 清除之前的图表
    d3.select("#longitudinal-chart").selectAll("svg").remove();

    let svg = d3
      .select("#longitudinal-chart")
      .append("svg")
      .attr("style", `width:${width + 100}px;height:400px;margin-left: 10px;`); // 减少左边距，因为标签现在是固定的

    // 获取横竖轴最大最小值
    const maxDataY = d3.max(testdata, (d: any) =>
      parseFloat(d.qdms || d.zdms || 0)
    );
    // 使用固定间距计算的最大x值
    const maxDataX = (testdata.length - 1) * fixedSpacing;

    // 定义横竖轴范围
    const xRange = d3.scaleLinear().domain([0, maxDataX + fixedSpacing]).range([0, width]);
    const yRange = d3.scaleLinear().domain([0, maxDataY]).range([0, height]);

    // 创建横竖轴比例
    // 自定义x轴刻度，显示管点序号
    const xAxis = d3.axisTop(xRange)
      .tickValues(testdata.map((_: any, i: number) => i * fixedSpacing))
      .tickFormat((_: any, i: number) => `P${i + 1}`); // P1, P2, P3...
    const yAxis = d3.axisLeft(yRange).ticks(5);

    // 将轴附加到图表上
    svg
      .append("g")
      .attr("class", "axis")
      .attr("transform", "translate(" + 25 + "," + 30 + ")") // 减少左边距
      .call(xAxis)
      .append("text")
      .attr("fill", "#2C3037")
      .attr("transform", "translate(" + (width - 50) + "," + 0 + ")")
      .attr("y", 6)
      .attr("dy", "0.71em")
      .attr("text-anchor", "end")
      .text("管点序号");

    svg
      .append("g")
      .attr("class", "yaxis")
      .attr("transform", "translate(" + 25 + "," + 30 + ")") // 减少左边距
      .call(yAxis)
      .append("text")
      .attr("fill", "#2C3037")
      .attr("transform", "translate(" + 0 + "," + (height - 35) + "),rotate(-90)")
      .attr("y", 6)
      .attr("dy", "0.71em")
      .attr("text-anchor", "end")
      .text("埋深/m");

    // 添加连接线
    const d3line = d3
      .line()
      .x((d: any) => xRange(d.xdata))
      .y((d: any) => yRange(Number(d.qdms || d.zdms || 0)));

    svg
      .append("g")
      .selectAll("path")
      .data(testdata)
      .enter()
      .append("path")
      .attr("stroke", "#00B1FF")
      .attr("transform", "translate(" + 25 + "," + 30 + ")") // 减少左边距
      .attr("stroke-width", 2)
      .attr("fill", "none")
      .attr("stroke-linejoin", "round")
      .attr("stroke-linecap", "round")
      .join("path")
      .attr("d", d3line(testdata));

    // 添加管点圆形标记
    svg
      .append("g")
      .selectAll("circle")
      .data(testdata)
      .enter()
      .append("circle")
      .attr("transform", "translate(" + 25 + "," + 30 + ")") // 减少左边距
      .attr("stroke", "#d81e06")
      .attr("fill", "#d81e06")
      .attr("cx", (d: any) => xRange(d.xdata))
      .attr("cy", (d: any) => yRange(d.qdms || d.zdms || 0))
      .attr("r", 2);

    // 添加虚线
    svg
      .append("g")
      .selectAll("line")
      .data(testdata)
      .enter()
      .append("line")
      .attr("transform", "translate(" + 25 + "," + 30 + ")") // 减少左边距
      .attr("x1", (d: any) => xRange(d.xdata))
      .attr("x2", (d: any) => xRange(d.xdata))
      .attr("y1", (d: any) => yRange(d.qdms || d.zdms || 0) + 10)
      .attr("y2", 350)
      .style("stroke-dasharray", "5,5")
      .style("stroke", "#00B1FF");

    // 添加管点编码文本 - 左对齐到虚线位置
    svg
      .append("g")
      .selectAll("text")
      .data(testdata)
      .enter()
      .append("text")
      .attr("fill", "#2C3037")
      .attr("transform", (d: any) => {
        return "translate(" + (xRange(d.xdata) + 30) + "," + 250 + ")"; // 调整位置
      })
      .attr("y", 6)
      .attr("dy", "0.71em")
      .attr("text-anchor", "start") // 改为左对齐
      .attr("font-size", 10)
      .text((d: any) => d.qdbh || d.zdbh || d.bm);

    // 添加地面高程文本 - 左对齐到虚线位置
    svg
      .append("g")
      .selectAll(".ground-elevation")
      .data(testdata)
      .enter()
      .append("text")
      .attr("class", "ground-elevation")
      .attr("fill", "#2C3037")
      .attr("transform", (d: any) => {
        return "translate(" + (xRange(d.xdata) + 30) + "," + 270 + ")"; // 调整位置
      })
      .attr("y", 6)
      .attr("dy", "0.71em")
      .attr("text-anchor", "start") // 改为左对齐
      .attr("font-size", 10)
      .text((d: any) => {
        const gc = parseFloat(d.qdgc || d.zdgc || 0);
        const ms = parseFloat(d.qdms || d.zdms || 0);
        return (gc + ms).toFixed(2);
      });

    // 添加管点高程文本 - 左对齐到虚线位置
    svg
      .append("g")
      .selectAll(".pipe-elevation")
      .data(testdata)
      .enter()
      .append("text")
      .attr("class", "pipe-elevation")
      .attr("fill", "#2C3037")
      .attr("transform", (d: any) => {
        return "translate(" + (xRange(d.xdata) + 30) + "," + 290 + ")"; // 调整位置
      })
      .attr("y", 6)
      .attr("dy", "0.71em")
      .attr("text-anchor", "start") // 改为左对齐
      .attr("font-size", 10)
      .text((d: any) => d.qdgc || d.zdgc || "0.00");

    // 添加管点埋深文本 - 左对齐到虚线位置
    svg
      .append("g")
      .selectAll(".pipe-depth")
      .data(testdata)
      .enter()
      .append("text")
      .attr("class", "pipe-depth")
      .attr("fill", "#2C3037")
      .attr("transform", (d: any) => {
        return "translate(" + (xRange(d.xdata) + 30) + "," + 310 + ")"; // 调整位置
      })
      .attr("y", 6)
      .attr("dy", "0.71em")
      .attr("text-anchor", "start") // 改为左对齐
      .attr("font-size", 10)
      .text((d: any) => d.qdms || d.zdms || "0.00");

    // 添加管线长度文本 - 左对齐到虚线位置
    svg
      .append("g")
      .selectAll(".pipe-length")
      .data(testdata)
      .enter()
      .append("text")
      .attr("class", "pipe-length")
      .attr("fill", "#2C3037")
      .attr("transform", (d: any) => {
        return "translate(" + (xRange(d.xdata) + 30) + "," + 330 + ")"; // 调整位置
      })
      .attr("y", 6)
      .attr("dy", "0.71em")
      .attr("text-anchor", "start") // 改为左对齐
      .attr("font-size", 10)
      .text((d: any) => (d.gxcd || 0).toFixed(2));

    // 添加断面尺寸文本（管径）- 左对齐到虚线位置
    svg
      .append("g")
      .selectAll(".pipe-diameter")
      .data(testdata)
      .enter()
      .append("text")
      .attr("class", "pipe-diameter")
      .attr("fill", "#2C3037")
      .attr("transform", (d: any) => {
        return "translate(" + (xRange(d.xdata) + 30) + "," + 350 + ")"; // 调整位置
      })
      .attr("y", 6)
      .attr("dy", "0.71em")
      .attr("text-anchor", "start") // 改为左对齐
      .attr("font-size", 10)
      .text((d: any) => d.gj || d.dmcc || "");

    // 左侧标签现在使用HTML实现，固定不动
    // 右侧标签已移除，只保留左侧固定标签

    console.log('D3纵断面图表绘制完成');
  } catch (error) {
    console.error('D3图表绘制失败:', error);
    ElMessage.error('图表绘制失败');
  }
};

/**
 * @method removeLayer 移除图层和清理资源
 */
const removeLayer = (): void => {
  try {
    // 重置状态
    showSvg.value = false;
    analyzeData.value = [];
    currentSelected.value = null;
    isSelecting.value = false;

    // 根据地图引擎清理相应资源
    if (mapEngine.value === 'cesium') {
      clearCesiumLayers();
    } else {
      clearMaplibreLayers();
    }

    // 清除D3图表
    if (typeof d3 !== "undefined") {
      d3.select("#longitudinal-chart").selectAll("svg").remove();
      console.log("已清除D3图表");
    }

    // ElMessage.info("已清除纵断面分析结果");
  } catch (error) {
    console.error("清除图层失败:", error);
    ElMessage.error("清除失败");
  }
};

/**
 * @method clearCesiumLayers 清理Cesium图层
 */
const clearCesiumLayers = (): void => {
  try {
    if (cesiumHighlightLayer.value) {
      const viewer = AppCesium.getInstance().getViewer();
      if (viewer) {
        cesiumHighlightLayer.value.clear();
        viewer.removeLayer(cesiumHighlightLayer.value);
      }
      cesiumHighlightLayer.value = null;
      console.log("已清除Cesium高亮图层");
    }
  } catch (error) {
    console.error('清理Cesium图层失败:', error);
  }
};

/**
 * @method clearMaplibreLayers 清理MapLibre图层
 */
const clearMaplibreLayers = (): void => {
  try {
    const map = AppMaplibre.getMap();
    
    if (map) {
      // 移除高亮图层
      if (map.getLayer("highlightline")) {
        map.removeLayer("highlightline");
      }
      if (map.getSource("highlightline")) {
        map.removeSource("highlightline");
      }
      console.log("已清除MapLibre高亮图层");
    }
  } catch (error) {
    console.error('清理MapLibre图层失败:', error);
  }
};

/**
 * @method onMounted 组件挂载时的初始化
 */
onMounted(() => {
  console.log(`纵断面分析组件已挂载 (${mapEngine.value}模式)`);
});

/**
 * @method onUnmounted 组件卸载时清理资源
 */
onUnmounted(() => {
  try {
    removeLayer();
    console.log("纵断面分析组件已卸载，清理完成");
  } catch (error) {
    console.error('组件卸载清理失败:', error);
  }
});
</script>

<style scoped lang="scss">
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 800px;
  min-height: 130px;
  z-index: 1000;
}

.button-section {
  margin-bottom: 20px;
  gap: 12px;

  .el-col {
    padding: 0 4px;
  }

  .select-btn {
    width: 100%;
    background-color: #1890ff;
    border-color: #1890ff;
    font-size: 13px;
    padding: 8px 12px;

    &:hover {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }

    &:disabled {
      background-color: #d9d9d9;
      border-color: #d9d9d9;
    }
  }

  .clear-btn {
    width: 100%;
    background-color: #8c8c8c;
    border-color: #8c8c8c;
    color: white;
    font-size: 13px;
    padding: 8px 12px;

    &:hover {
      background-color: #a8a8a8;
      border-color: #a8a8a8;
    }

    &:disabled {
      background-color: #d9d9d9;
      border-color: #d9d9d9;
    }
  }
}

.analysis-info {
  margin: 15px 0;
  padding: 10px;
  background-color: #f6f8fa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;

  .info-label {
    color: #2c3037;
    font-size: 14px;
    margin-right: 8px;
  }

  .info-text {
    color: #1890ff;
    font-size: 14px;
    font-weight: 500;
  }

  .pipe-count {
    color: #666;
    font-size: 12px;
    margin-left: 8px;
  }
}

#longitudinal-chart {
  overflow-x: auto;
  overflow-y: hidden;
  
  &::-webkit-scrollbar {
    width: 5px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: rgb(8, 192, 195);
  }

  &::-webkit-scrollbar-corner {
    background-color: #08c0c3;
  }

  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(8, 192, 195, 0.2);
  }
}

// 图表容器布局
.chart-container {
  position: relative;
  display: flex;
  width: 100%;
  height: 400px;
  // border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

// 固定的左侧标签区域
.fixed-labels {
  position: relative;
  width: 75px;
  // background: #f8f9fa;
  // border-right: 1px solid #e0e0e0;
  flex-shrink: 0; // 不允许收缩
  z-index: 10;
}

.label-item {
  position: absolute;
  left: 5px;
  font-size: 10px;
  font-weight: bold;
  color: #2C3037;
  white-space: nowrap;
  line-height: 1;
}

// 可滚动的图表区域
.scrollable-chart {
  flex: 1;
  overflow-x: auto;
  overflow-y: hidden;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: rgb(8, 192, 195);
  }

  &::-webkit-scrollbar-corner {
    background-color: #08c0c3;
  }

  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(8, 192, 195, 0.2);
  }
}

.d3Chart {
  min-width: 100%;
  height: 100%;
}

:deep(svg) {
  margin: 0;

  path {
    fill: none;
    stroke: #76bf8a;
    stroke-width: 3px;
  }
}
</style>
