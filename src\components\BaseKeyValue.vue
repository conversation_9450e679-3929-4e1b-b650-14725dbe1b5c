<template>
  <div class="detail-item w-full flex font-size-3.5 font-400">
    <div class="text-#5C5F66 min-w-80px label">
      <span>{{ label }}</span
      >：
    </div>
    <div class="text-#2C3037 flex-1 break-all">{{ value }}</div>
  </div>
</template>
<script setup lang="ts">
defineProps<{
  label: string;
  value: string | number | undefined;
}>();
</script>

<style scoped lang="scss">
.label {
  text-align: justify;
  text-align-last: justify; // 添加这一句，对最后一行生效
}
</style>
