import { lineListByBms, pointListByBms } from '@/api/analysis';
import { AppCesium } from '@/lib/cesium/AppCesium';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import * as turf from '@turf/turf';

/**
 * 定位功能相关的图层ID常量
 */
const LOCATION_LAYER_IDS = {
  LINE_SOURCE: 'pipe-location-line-source',
  LINE_LAYER: 'pipe-location-line-layer',
  POINT_SOURCE: 'pipe-location-point-source',
  POINT_LAYER: 'pipe-location-point-layer',
} as const;

/**
 * 定位类型枚举
 */
type LocationType = 'line' | 'point';

/**
 * 地图引擎类型
 */
type MapEngine = 'cesium' | 'maplibre';

/**
 * 清除所有定位相关的高亮图层
 * @param mapEngine 地图引擎类型
 */
export const clearPipeLocationHighlight = (
  mapEngine: MapEngine = 'maplibre'
): void => {
  try {
    if (mapEngine === 'cesium') {
      // Cesium模式下清除管线定位高亮
      try {
        const viewer = AppCesium.getInstance().getViewer();
        const { Cesium } = BC.Namespace;
        
        // 清除所有图层的高亮样式，恢复默认样式
        viewer.eachLayer((layer: any) => {
          if (layer.attr && layer.attr.type === 'pipeline') {
            const overlays = layer.getOverlaysByAttr('id', layer.attr.id);
            if (overlays && overlays.length > 0) {
              // 恢复默认样式（白色）
              overlays[0].setStyle(
                new Cesium.Cesium3DTileStyle({
                  color: 'vec4(1.0, 1.0, 1.0, 1.0)' // 白色默认
                })
              );
            }
          }
        }, null);
        
        // 清除定位相关的临时图层
        const layersToRemove: any[] = [];
        viewer.eachLayer((layer: any) => {
          if (layer.attr && layer.attr.id === 'locGeoJsonLayer') {
            layersToRemove.push(layer);
          }
        }, null);
        
        layersToRemove.forEach(layer => {
          viewer.removeLayer(layer);
        });
        
        console.log('已清除Cesium管线定位高亮');
      } catch (error) {
        console.error('清除Cesium管线定位高亮失败:', error);
      }
      return;
    }

    if (mapEngine === 'maplibre') {
      const map = AppMaplibre.getMap();

      // 必须先删除图层，再删除数据源
      // 1. 先删除所有图层
      const layerIds = [
        LOCATION_LAYER_IDS.LINE_LAYER,
        LOCATION_LAYER_IDS.POINT_LAYER,
      ];
      layerIds.forEach((layerId) => {
        if (map.getLayer(layerId)) {
          map.removeLayer(layerId);
          console.log(`已删除图层: ${layerId}`);
        }
      });

      // 2. 再删除所有数据源
      const sourceIds = [
        LOCATION_LAYER_IDS.LINE_SOURCE,
        LOCATION_LAYER_IDS.POINT_SOURCE,
      ];
      sourceIds.forEach((sourceId) => {
        if (map.getSource(sourceId)) {
          map.removeSource(sourceId);
          console.log(`已删除数据源: ${sourceId}`);
        }
      });

      console.log('已清除所有管线定位高亮图层');
    }
  } catch (error) {
    console.error('清除管线定位高亮图层失败:', error);
  }
};

/**
 * 根据管线编码进行定位和高亮显示
 * @param pipeCodes 管线编码数组
 * @param mapEngine 地图引擎类型
 * @param type 定位类型，默认为线要素
 * @param options 额外选项
 */
export const pipeLocationByBms = async (
  pipeCodes: string[],
  mapEngine: MapEngine,
  type: LocationType = 'line',
  options?: {
    /** 是否自动缩放到定位范围 */
    autoFit?: boolean;
    /** 缩放时的内边距 */
    padding?: number;
    /** 高亮颜色 */
    highlightColor?: string;
    /** 线宽度 */
    lineWidth?: number;
    /** 点半径 */
    pointRadius?: number;
  }
): Promise<void> => {
  const {
    autoFit = false,
    padding = 100,
    highlightColor = '#fc5531',
    lineWidth = 4,
    pointRadius = 6,
  } = options || {};

  try {
    // 验证输入参数
    if (!pipeCodes || pipeCodes.length === 0) {
      console.warn('管线编码数组为空，无法进行定位');
      return;
    }

    // 清除之前的高亮图层
    clearPipeLocationHighlight(mapEngine);

    if (mapEngine === 'cesium') {
      // TODO: 三维定位后续对管线模型定位并高亮
      console.log('3D模式定位功能待实现，管线编码:', pipeCodes);
      const { Cesium } = BC.Namespace;

      if(type === 'point') {
        handleCesiumPointLocation(pipeCodes);

      
      } else if(type === 'line') {
        handleCesiumLineLocation(pipeCodes);
      }
      AppCesium.getInstance()
        .getViewer()
        .eachLayer((layer: any) => {
          if (layer.attr.type === 'pipeline') {
            const overlays = layer.getOverlaysByAttr('id', layer.attr.id);
            if (overlays.length > 0) {
              const conditions = pipeCodes.map((code) => [
                "${id} === '" + code + "'",
                'vec4(1.0, 1.0, 0.0, 1)',
              ]);
              conditions.push(['true', 'vec4(1.0,1.0,1.0,1)']);
              overlays[0].setStyle(
                new Cesium.Cesium3DTileStyle({
                  color: {
                    conditions: conditions,
                  },
                })
              );
            }
          }
        }, null);
      return;
    }

    if (mapEngine === 'maplibre') {
      if (type === 'line') {
        await handleLineLocation(
          pipeCodes,
          autoFit,
          padding,
          highlightColor,
          lineWidth
        );
      } else if (type === 'point') {
        await handleMaplibrePointLocation(
          pipeCodes,
          autoFit,
          padding,
          highlightColor,
          pointRadius
        );
      }
    }
  } catch (error) {
    console.error('管线定位失败:', error);
    throw error;
  }
};
const setHeight = (point: any, height: number) => {
  const { Cesium } = BC.Namespace;
  let cartographic = Cesium.Cartographic.fromCartesian(point);
  cartographic.height = height;
  return Cesium.Cartographic.toCartesian(cartographic);
};
async function handleCesiumPointLocation(pipeCodes: string[]) {
  if (!pipeCodes || pipeCodes.length === 0) return;
  const { code, data } = await pointListByBms(pipeCodes);
  const { Cesium } = BC.Namespace;
  if (code === 200) {
    const features = data.map((item: any) => ({
      type: 'Feature' as const,
      geometry: JSON.parse(item.geojson),
      properties: item,
    }));
    const geojson = {
      type: 'FeatureCollection',
      features: features,
    };
    let geojsonLayer = new BC.GeoJsonLayer(
      'locGeoJsonLayer',
      geojson as any
    );
    geojsonLayer.addTo(AppCesium.getInstance().getViewer());
    geojsonLayer.eachOverlay((overlay: any) => {
      const cartesian3 = overlay.position.getValue(new BC.JulianDate());
      const newCartesian3 = setHeight(
        cartesian3,
        parseFloat(overlay.properties.dmgc.getValue(new BC.JulianDate()))
      );
      AppCesium.getInstance()
        .getViewer()
        .camera.lookAt(
          newCartesian3,
          new Cesium.HeadingPitchRange(
            BC.Math.toRadians(14.192604699345909),
            BC.Math.toRadians(-45.46914000324496 || 0),
            30
          )
        );

      AppCesium.getInstance()
        .getViewer()
        .camera.lookAtTransform(BC.Matrix4.IDENTITY);
    }, null);
    AppCesium.getInstance().getViewer().removeLayer(geojsonLayer);
  }
}
async function handleCesiumLineLocation(pipeCodes: string[]) {
  if (!pipeCodes || pipeCodes.length === 0) return;
  const { code, data } = await lineListByBms(pipeCodes);
  if (code === 200) {
    const features = data.map((item: any) => ({
      type: 'Feature' as const,
      geometry: JSON.parse(item.geojson),
      properties: item,
    }));
    const geojson = {
      type: 'FeatureCollection',
      features: features,
    };
    let geojsonLayer = new BC.GeoJsonLayer(
      'locGeoJsonLayer',
      geojson as any
    );
    geojsonLayer.addTo(AppCesium.getInstance().getViewer());
    geojsonLayer.eachOverlay((overlay: any) => {
      const positions = overlay.polyline.positions.getValue(new BC.JulianDate())
      console.log(parseFloat(overlay.properties.qdgc.getValue(new BC.JulianDate())))
      console.log(parseFloat(overlay.properties.zdgc.getValue(new BC.JulianDate())))
      const p1 = setHeight(
        positions[0],
        parseFloat(overlay.properties.qdgc.getValue(new BC.JulianDate()))
      )
      const p2 = setHeight(
        positions[1],
        parseFloat(overlay.properties.zdgc.getValue(new BC.JulianDate()))
      )
      overlay.polyline.positions = [p1, p2]
      AppCesium.getInstance().getViewer().flyTo(overlay)
    }, null)
    setTimeout(() => {
      AppCesium.getInstance().getViewer().removeLayer(geojsonLayer)
    }, 2000)
  }
}

/**
 * 处理线要素定位
 */
async function handleLineLocation(
  pipeCodes: string[],
  autoFit: boolean,
  padding: number,
  color: string,
  width: number
): Promise<void> {
  try {
    const { code, data } = await lineListByBms(pipeCodes);

    if (code !== 200 || !data || data.length === 0) {
      console.warn('未找到对应的管线数据:', pipeCodes);
      return;
    }

    const features = data.map((item: any) => ({
      type: 'Feature' as const,
      geometry: JSON.parse(item.geojson),
      properties: {
        pipeCode: item.bm || item.gxbm || item.id,
      },
    }));

    const map = AppMaplibre.getMap();

    // 添加数据源
    map.addSource(LOCATION_LAYER_IDS.LINE_SOURCE, {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: features,
      },
    });

    // 添加高亮图层
    map.addLayer({
      type: 'line',
      source: LOCATION_LAYER_IDS.LINE_SOURCE,
      id: LOCATION_LAYER_IDS.LINE_LAYER,
      layout: {
        'line-join': 'round',
        'line-cap': 'round',
      },
      paint: {
        'line-color': color,
        'line-width': width,
        'line-opacity': 0.8,
      },
    });

    // 自动缩放到定位范围
    if (autoFit) {
      const bbox = turf.bbox({
        type: 'FeatureCollection',
        features: features,
      });
      map.fitBounds(bbox as [number, number, number, number], {
        padding: padding,
        duration: 1000,
      });
    }

    console.log(`已高亮显示 ${data.length} 条管线`);
  } catch (error) {
    console.error('线要素定位失败:', error);
    throw error;
  }
}

/**
 * 处理点要素定位
 */
async function handleMaplibrePointLocation(
  pipeCodes: string[],
  autoFit: boolean,
  padding: number,
  color: string,
  radius: number
): Promise<void> {
  try {
    const { code, data } = await pointListByBms(pipeCodes);

    if (code !== 200 || !data || data.length === 0) {
      console.warn('未找到对应的管点数据:', pipeCodes);
      return;
    }

    const features = data.map((item: any) => ({
      type: 'Feature' as const,
      geometry: JSON.parse(item.geojson),
      properties: {
        pipeCode: item.bm || item.gxbm || item.id,
      },
    }));

    const map = AppMaplibre.getMap();

    // 添加数据源
    map.addSource(LOCATION_LAYER_IDS.POINT_SOURCE, {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: features,
      },
    });

    // 添加高亮图层
    map.addLayer({
      type: 'circle',
      source: LOCATION_LAYER_IDS.POINT_SOURCE,
      id: LOCATION_LAYER_IDS.POINT_LAYER,
      paint: {
        'circle-color': color,
        'circle-radius': radius,
        'circle-opacity': 0.8,
        'circle-stroke-width': 2,
        'circle-stroke-color': '#ffffff',
        'circle-stroke-opacity': 1,
      },
    });

    // 自动缩放到定位范围
    if (autoFit) {
      const bbox = turf.bbox({
        type: 'FeatureCollection',
        features: features,
      });
      map.fitBounds(bbox as [number, number, number, number], {
        padding: padding,
        duration: 1000,
      });
    }

    console.log(`已高亮显示 ${data.length} 个管点`);
  } catch (error) {
    console.error('点要素定位失败:', error);
    throw error;
  }
}

/**
 * 获取当前是否有活动的定位高亮图层
 * @param mapEngine 地图引擎类型
 * @returns 是否有活动的高亮图层
 */
export const hasPipeLocationHighlight = (
  mapEngine: MapEngine = 'maplibre'
): boolean => {
  try {
    if (mapEngine === 'cesium') {
      // TODO: 实现Cesium的检查逻辑
      return false;
    }

    if (mapEngine === 'maplibre') {
      const map = AppMaplibre.getMap();
      return Object.values(LOCATION_LAYER_IDS).some(
        (id) => map.getLayer(id) !== undefined
      );
    }

    return false;
  } catch (error) {
    console.error('检查定位高亮图层状态失败:', error);
    return false;
  }
};
