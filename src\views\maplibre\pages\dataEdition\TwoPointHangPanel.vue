<template>
  <page-card
    class="two-point-hang-panel"
    title="两点栓点"
    @closeCard="closeCard"
  >
    <!-- 参照点选择区域 -->

    <div class="brd">
      <div class="title">参照点1</div>
      <div>
        <el-button
          type="primary"
          class="bg-#E3EDFF color-#1966FF border-color-#1966FF w-25 h-9"
          :loading="isSelectingPoint1"
          @click="handleSelectPoint1"
        >
          选择
        </el-button>
      </div>

      <div class="distance-input-group">
        <div class="distance-label">参照点1到栓点距离(米)：</div>
        <el-input
          v-model="distance1"
          placeholder="请输入距离"
          :disabled="!referencePoint1"
          class="distance-input"
          @input="handleDistance1Input"
          @blur="handleDistance1Blur"
        ></el-input>
      </div>
    </div>

    <div class="brd">
      <div class="title">参照点2</div>
      <el-button
        type="primary"
        class="bg-#E3EDFF color-#1966FF border-color-#1966FF w-25 h-9"
        :loading="isSelectingPoint2"
        @click="handleSelectPoint2"
      >
        选择
      </el-button>
      <div class="distance-input-group">
        <el-text class="distance-label">参照点2到栓点距离(米)：</el-text>
        <el-input
          v-model="distance2"
          placeholder="请输入距离"
          :disabled="!referencePoint2"
          class="distance-input"
          @input="handleDistance2Input"
          @blur="handleDistance2Blur"
        ></el-input>
      </div>
    </div>

    <!-- 计算按钮区域 -->
    <el-row class="calculation-section" flex="~ row justify-start">
      <el-button
        type="primary"
        class="calculate-btn h-9"
        :loading="isCalculating"
        @click="handleCalculate"
        :disabled="!canCalculate"
      >
        计算栓点
      </el-button>
      <el-button
        class="clear-btn h-9"
        @click="handleClear"
        :disabled="isCalculating || isSelectingPoint1 || isSelectingPoint2"
      >
        清除
      </el-button>
    </el-row>

    <!-- 选择的参照点显示区域 -->
    <div v-if="referencePoint1 || referencePoint2" class="points-section">
      <div class="flex items-center justify-between">
        <el-row v-if="referencePoint1" class="point-row">
          <el-col :span="24">
            <el-text class="point-label">参照点1：</el-text>
            <div class="point-info">
              <span class="point-text">
                管点编号: {{ referencePoint1.gxddh || "无" }}
              </span>
              <span class="coordinate-text">
                坐标: {{ referencePoint1.lng.toFixed(6) }},
                {{ referencePoint1.lat.toFixed(6) }}
              </span>
              <span v-if="distance1" class="distance-text">
                栓点距离: {{ distance1 }}米
              </span>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="referencePoint2" class="point-row">
          <el-col :span="24">
            <el-text class="point-label">参照点2：</el-text>
            <div class="point-info">
              <span class="point-text">
                管点编号: {{ referencePoint2.gxddh || "无" }}
              </span>
              <span class="coordinate-text">
                坐标: {{ referencePoint2.lng.toFixed(6) }},
                {{ referencePoint2.lat.toFixed(6) }}
              </span>
              <span v-if="distance2" class="distance-text">
                栓点距离: {{ distance2 }}米
              </span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 两点间距离显示 -->
      <div v-if="referencePointsDistance" class="distance-reference-row">
        <span class="color-#2C3037">
          参照点间距离：{{ referencePointsDistance.formatted }}
        </span>
        <span class="color-#909399">（可作为设置栓点距离的参考）</span>
      </div>
    </div>
    <div
      v-if="showResult"
      class="result-section"
      :class="calculationResult.success ? 'success' : 'error'"
    >
      <el-icon v-if="calculationResult.success" class="color-#00B42A"
        ><CircleCheckFilled
      /></el-icon>
      <el-icon v-else class="color-#FF7373"><CircleCloseFilled /></el-icon>

      <el-text class="result-label" v-if="calculationResult.success"
        >计算结果：</el-text
      >
      <span :class="'result-text'">{{ calculationResult.message }}</span>
    </div>
    <!-- 栓点结果表格 -->
    <el-row
      v-if="showResultTable && hangPoints.length > 0"
      class="table-section"
    >
      <el-col :span="24">
        <div class="title mb-2.5">计算出的栓点位置</div>
        <el-table
          :data="hangPoints"
          style="width: 100%; height: 130px"
          class="routeCt"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
          ></el-table-column>
          <el-table-column label="栓点编号" width="90">
            <template #default="scope"> 栓点{{ scope.$index + 1 }} </template>
          </el-table-column>
          <el-table-column prop="lng" label="经度" width="120">
            <template #default="scope">
              {{ scope.row.lng.toFixed(8) }}
            </template>
          </el-table-column>
          <el-table-column prop="lat" label="纬度" width="120">
            <template #default="scope">
              {{ scope.row.lat.toFixed(8) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="distance1"
            label="到参照点1距离(米)"
            width="140"
          >
            <template #default="scope">
              {{ scope.row.distance1.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="distance2"
            label="到参照点2距离(米)"
            width="140"
          >
            <template #default="scope">
              {{ scope.row.distance2.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="90" fixed="right">
            <template #default="scope">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleAddNode(scope.row)"
              >
                新增管点
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import * as turf from "@turf/turf";
import { CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue";
/**
 * 处理关闭面板
 */
const closeCard = () => {
  try {
    console.log("关闭两点栓点面板");

    // 先清理所有数据和图层
    handleClear();

    // 通知父组件关闭面板
    emit("close");

    ElMessage.info("已关闭两点栓点功能");
  } catch (error) {
    console.error("关闭面板失败:", error);
    ElMessage.error("关闭面板失败");
  }
};

/**
 * 临时图层ID常量
 */
const TEMP_LAYER_IDS = {
  REFERENCE_POINT1_SOURCE: "two-point-hang-point1-source",
  REFERENCE_POINT1_LAYER: "two-point-hang-point1-layer",
  REFERENCE_POINT2_SOURCE: "two-point-hang-point2-source",
  REFERENCE_POINT2_LAYER: "two-point-hang-point2-layer",
  CIRCLE1_SOURCE: "two-point-hang-circle1-source",
  CIRCLE1_LAYER: "two-point-hang-circle1-layer",
  CIRCLE2_SOURCE: "two-point-hang-circle2-source",
  CIRCLE2_LAYER: "two-point-hang-circle2-layer",
  HANG_POINTS_SOURCE: "two-point-hang-points-source",
  HANG_POINTS_LAYER: "two-point-hang-points-layer",
} as const;

/**
 * 定义参照点接口
 */
interface ReferencePoint {
  lng: number;
  lat: number;
  gxddh?: string; // 管点编号
}

/**
 * 定义栓点接口
 */
interface HangPoint {
  lng: number;
  lat: number;
  distance1: number; // 到参照点1的距离
  distance2: number; // 到参照点2的距离
}

/**
 * 定义计算结果接口
 */
interface CalculationResult {
  success: boolean;
  message: string;
}

/**
 * 响应式数据状态
 */
// 参照点相关状态
const referencePoint1 = ref<ReferencePoint | null>(null);
const referencePoint2 = ref<ReferencePoint | null>(null);
const distance1 = ref<string>("");
const distance2 = ref<string>("");
const isSelectingPoint1 = ref<boolean>(false);
const isSelectingPoint2 = ref<boolean>(false);
const isCalculating = ref<boolean>(false);

// 计算结果状态
const showResult = ref<boolean>(false);
const calculationResult = ref<CalculationResult>({
  success: false,
  message: "",
});
const hangPoints = ref<HangPoint[]>([]);
const showResultTable = ref<boolean>(false);

/**
 * 获取数字形式的距离值
 */
const getDistance1Number = (): number => {
  const num = parseFloat(distance1.value);
  return isNaN(num) ? 0 : num;
};

const getDistance2Number = (): number => {
  const num = parseFloat(distance2.value);
  return isNaN(num) ? 0 : num;
};

/**
 * 处理距离1输入
 */
const handleDistance1Input = (value: string): void => {
  // 只允许数字、小数点和负号
  const cleanValue = value.replace(/[^\d.-]/g, "");
  distance1.value = cleanValue;
};

/**
 * 处理距离1失焦
 */
const handleDistance1Blur = (): void => {
  const num = parseFloat(distance1.value);
  if (isNaN(num) || num < 0) {
    distance1.value = "";
    ElMessage.warning("请输入有效的距离值（大于0）");
  }
};

/**
 * 处理距离2输入
 */
const handleDistance2Input = (value: string): void => {
  // 只允许数字、小数点和负号
  const cleanValue = value.replace(/[^\d.-]/g, "");
  distance2.value = cleanValue;
};

/**
 * 处理距离2失焦
 */
const handleDistance2Blur = (): void => {
  const num = parseFloat(distance2.value);
  if (isNaN(num) || num < 0) {
    distance2.value = "";
    ElMessage.warning("请输入有效的距离值（大于0）");
  }
};

/**
 * 计算属性：是否可以进行计算
 */
const canCalculate = computed(() => {
  return (
    referencePoint1.value &&
    referencePoint2.value &&
    getDistance1Number() > 0 &&
    getDistance2Number() > 0 &&
    !isSelectingPoint1.value &&
    !isSelectingPoint2.value
  );
});

/**
 * 计算属性：两个参照点之间的距离
 */
const referencePointsDistance = computed(() => {
  if (!referencePoint1.value || !referencePoint2.value) {
    return null;
  }

  try {
    // 使用turf.js计算精确距离
    const point1 = turf.point([
      referencePoint1.value.lng,
      referencePoint1.value.lat,
    ]);
    const point2 = turf.point([
      referencePoint2.value.lng,
      referencePoint2.value.lat,
    ]);
    const distance = turf.distance(point1, point2, { units: "meters" });

    return {
      meters: distance,
      kilometers: distance / 1000,
      formatted:
        distance > 1000
          ? `${(distance / 1000).toFixed(2)}公里`
          : `${distance.toFixed(2)}米`,
    };
  } catch (error) {
    console.error("计算参照点距离失败:", error);
    return null;
  }
});

/**
 * 从地图选择管点
 * @returns Promise<ReferencePoint> 返回选择的管点信息
 */
const selectPointFromMap = (): Promise<ReferencePoint> => {
  return new Promise((resolve, reject) => {
    try {
      const map = AppMaplibre.getMap();

      if (!map) {
        throw new Error("地图实例未初始化");
      }

      // 修改鼠标样式提示用户可以点击
      map.getCanvas().style.cursor = "crosshair";

      // 绑定单次点击事件
      map.once("click", (e: any) => {
        try {
          // 恢复默认鼠标样式
          map.getCanvas().style.cursor = "";
          const degree = 10;
          // 查询点击位置的管点要素
          const features = map.queryRenderedFeatures(
            [
              [e.point.x - degree / 2, e.point.y - degree / 2],
              [e.point.x + degree / 2, e.point.y + degree / 2],
            ],
            {
              layers: ["mvt_pipeNode"],
            }
          );

          if (features.length === 0) {
            throw new Error("请点击管点位置");
          }

          const feature: any = features[0];
          const gxddh = feature.properties?.gxddh;

          if (!gxddh) {
            throw new Error("该管点缺少编号信息");
          }

          const point: ReferencePoint = {
            lng: feature.geometry.coordinates[0] as number,
            lat: feature.geometry.coordinates[1] as number,
            gxddh: gxddh,
          };

          resolve(point);
        } catch (error) {
          // 恢复默认鼠标样式
          map.getCanvas().style.cursor = "";
          reject(error);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * 添加参照点的临时图层显示
 * @param point 参照点
 * @param pointNumber 点编号：1 | 2
 */
const addReferencePointLayer = (point: ReferencePoint, pointNumber: 1 | 2) => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      console.error("地图实例不存在");
      return;
    }

    const sourceId =
      pointNumber === 1
        ? TEMP_LAYER_IDS.REFERENCE_POINT1_SOURCE
        : TEMP_LAYER_IDS.REFERENCE_POINT2_SOURCE;
    const layerId =
      pointNumber === 1
        ? TEMP_LAYER_IDS.REFERENCE_POINT1_LAYER
        : TEMP_LAYER_IDS.REFERENCE_POINT2_LAYER;
    const labelLayerId = `${layerId}-label`;
    // 使用更明显的颜色：参照点1深绿色，参照点2深蓝色
    const color = pointNumber === 1 ? "#00cc00" : "#0066cc";
    const strokeColor = pointNumber === 1 ? "#004d00" : "#003d73"; // 更深的边框色

    const featureData = {
      type: "FeatureCollection" as const,
      features: [
        {
          type: "Feature" as const,
          geometry: {
            type: "Point" as const,
            coordinates: [point.lng, point.lat],
          },
          properties: {
            type: `reference-point-${pointNumber}`,
            gxddh: point.gxddh || "",
            pointNumber: pointNumber,
          },
        },
      ],
    };

    // 检查并更新数据源
    const existingSource = map.getSource(sourceId);
    if (existingSource) {
      // 如果数据源已存在，直接更新数据
      (existingSource as any).setData(featureData);
      console.log(`已更新参照点${pointNumber}数据源:`, point.gxddh);
    } else {
      // 数据源不存在，创建新的数据源和图层
      map.addSource(sourceId, {
        type: "geojson",
        data: featureData,
      });

      // 添加圆形标记图层（更大、更明显）
      map.addLayer({
        id: layerId,
        type: "circle",
        source: sourceId,
        paint: {
          "circle-radius": [
            "interpolate",
            ["linear"],
            ["zoom"],
            4,
            6, // 增大标记尺寸
            6,
            10, // 在高缩放级别下更大
          ],
          "circle-color": color,
          "circle-stroke-width": 2, // 增大边框宽度
          "circle-stroke-color": strokeColor,
          "circle-opacity": 0.9, // 增加不透明度
          "circle-stroke-opacity": 1,
        },
      });

      // 添加标签图层（修复字体和文本表达式）
      map.addLayer({
        id: labelLayerId,
        type: "symbol",
        source: sourceId,
        layout: {
          "text-field": ["concat", "参照点", ["get", "pointNumber"]], // 简化为单行文本
          "text-font": ["Open Sans Regular", "Arial Unicode MS Regular"], // 使用MapLibre支持的字体
          "text-offset": [0, -1.5], // 调整偏移
          "text-anchor": "bottom",
          "text-size": [
            "interpolate",
            ["linear"],
            ["zoom"],
            10,
            10, // 缩小字体
            18,
            16, // 在高缩放级别下适中大小
          ],
          "text-allow-overlap": true, // 允许文字重叠显示
          "text-ignore-placement": true, // 忽略布局限制确保显示
        },
        paint: {
          "text-color": strokeColor, // 使用深色文字
          "text-halo-color": "#ffffff",
          "text-halo-width": 2, // 调整光晕宽度
          "text-halo-blur": 0.5, // 调整光晕模糊效果
        },
      });

      console.log(`已创建参照点${pointNumber}标签图层，字体和样式:`, {
        labelLayerId,
        pointNumber,
        gxddh: point.gxddh,
        textField: ["concat", "参照点", ["get", "pointNumber"]],
        textColor: strokeColor,
      });
    }
  } catch (error) {
    console.error(`添加参照点${pointNumber}临时图层失败:`, error);
  }
};

/**
 * 创建圆形图层显示距离范围
 * @param center 圆心坐标
 * @param radiusMeters 半径（米）
 * @param circleNumber 圆编号：1 | 2
 */
const addCircleLayer = (
  center: ReferencePoint,
  radiusMeters: number,
  circleNumber: 1 | 2
) => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      console.error("地图实例不存在");
      return;
    }

    const sourceId =
      circleNumber === 1
        ? TEMP_LAYER_IDS.CIRCLE1_SOURCE
        : TEMP_LAYER_IDS.CIRCLE2_SOURCE;
    const layerId =
      circleNumber === 1
        ? TEMP_LAYER_IDS.CIRCLE1_LAYER
        : TEMP_LAYER_IDS.CIRCLE2_LAYER;
    const color = circleNumber === 1 ? "#00ff0040" : "#0080ff40"; // 半透明颜色
    const strokeColor = circleNumber === 1 ? "#00ff00" : "#0080ff";

    // 使用turf创建圆形
    const centerPoint = turf.point([center.lng, center.lat]);
    const circle = turf.circle(centerPoint, radiusMeters / 1000, {
      steps: 64,
      units: "kilometers",
    });

    // 检查并更新数据源
    const existingSource = map.getSource(sourceId);
    if (existingSource) {
      // 如果数据源已存在，直接更新数据
      (existingSource as any).setData(circle);
      console.log(`已更新圆形${circleNumber}数据源，半径: ${radiusMeters}米`);
    } else {
      // 数据源不存在，创建新的数据源和图层
      map.addSource(sourceId, {
        type: "geojson",
        data: circle,
      });

      // 添加填充图层
      map.addLayer({
        id: layerId,
        type: "fill",
        source: sourceId,
        paint: {
          "fill-color": color,
          "fill-opacity": 0.3,
        },
      });

      // 添加边框图层
      map.addLayer({
        id: `${layerId}-stroke`,
        type: "line",
        source: sourceId,
        paint: {
          "line-color": strokeColor,
          "line-width": 2,
          "line-opacity": 0.8,
        },
      });

      console.log(`已创建圆形${circleNumber}临时图层，半径: ${radiusMeters}米`);
    }
  } catch (error) {
    console.error(`添加圆形${circleNumber}临时图层失败:`, error);
  }
};

/**
 * 添加栓点结果图层
 * @param points 栓点数组
 */
const addHangPointsLayer = (points: HangPoint[]) => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      console.error("地图实例不存在");
      return;
    }

    console.log("开始创建栓点图层，数据:", points);

    const features = points.map((point, index) => ({
      type: "Feature" as const,
      geometry: {
        type: "Point" as const,
        coordinates: [point.lng, point.lat],
      },
      properties: {
        type: "hang-point",
        index: index + 1,
        distance1: point.distance1,
        distance2: point.distance2,
        // 简化距离信息显示
        distanceText: `${point.distance1}m,${point.distance2}m`,
      },
    }));

    const featureData = {
      type: "FeatureCollection" as const,
      features: features,
    };

    console.log("栓点要素数据:", featureData);

    // 检查并更新数据源
    const existingSource = map.getSource(TEMP_LAYER_IDS.HANG_POINTS_SOURCE);
    if (existingSource) {
      // 如果数据源已存在，直接更新数据
      (existingSource as any).setData(featureData);
      console.log("已更新栓点数据源");
    } else {
      // 数据源不存在，创建新的数据源和图层
      map.addSource(TEMP_LAYER_IDS.HANG_POINTS_SOURCE, {
        type: "geojson",
        data: featureData,
      });

      console.log("已创建栓点数据源:", TEMP_LAYER_IDS.HANG_POINTS_SOURCE);

      // 添加圆形标记图层（更大、更醒目）
      map.addLayer({
        id: TEMP_LAYER_IDS.HANG_POINTS_LAYER,
        type: "circle",
        source: TEMP_LAYER_IDS.HANG_POINTS_SOURCE,
        paint: {
          "circle-radius": [
            "interpolate",
            ["linear"],
            ["zoom"],
            4,
            6, // 增大标记尺寸
            6,
            10, // 在高缩放级别下更大
          ],
          "circle-color": "#ff4400", // 更醒目的橙红色
          "circle-stroke-width": 2, // 增大边框宽度
          "circle-stroke-color": "#b30000", // 深红色边框
          "circle-opacity": 0.9, // 增加不透明度
          "circle-stroke-opacity": 1,
        },
      });

      console.log("已创建栓点圆形图层:", TEMP_LAYER_IDS.HANG_POINTS_LAYER);

      // 添加标签图层（修复字体和文本表达式）
      const labelLayerId = `${TEMP_LAYER_IDS.HANG_POINTS_LAYER}-label`;
      map.addLayer({
        id: labelLayerId,
        type: "symbol",
        source: TEMP_LAYER_IDS.HANG_POINTS_SOURCE,
        layout: {
          "text-field": ["concat", "栓点", ["get", "index"]], // 简化为单行文本
          "text-font": ["Open Sans Regular", "Arial Unicode MS Regular"], // 使用MapLibre支持的字体
          "text-offset": [0, -1.5], // 调整偏移
          "text-anchor": "bottom",
          "text-size": [
            "interpolate",
            ["linear"],
            ["zoom"],
            10,
            10, // 缩小字体
            18,
            16, // 在高缩放级别下适中大小
          ],
          "text-allow-overlap": true, // 允许文字重叠显示
          "text-ignore-placement": true, // 忽略布局限制确保显示
        },
        paint: {
          "text-color": "#b30000", // 使用深红色文字
          "text-halo-color": "#ffffff",
          "text-halo-width": 2, // 调整光晕宽度
          "text-halo-blur": 0.5, // 调整光晕模糊效果
        },
      });

      console.log("已创建栓点标签图层，配置:", {
        labelLayerId,
        textField: ["concat", "栓点", ["get", "index"]],
        textColor: "#b30000",
        fontSize: "10-16px",
      });
    }
  } catch (error: any) {
    console.error("添加栓点临时图层失败:", error);
    console.error(
      "错误详情:",
      error instanceof Error ? error.stack : String(error)
    );
  }
};

/**
 * 计算两个圆的交点
 * @param point1 第一个圆心
 * @param radius1 第一个圆的半径（米）
 * @param point2 第二个圆心
 * @param radius2 第二个圆的半径（米）
 * @returns 交点数组
 */
const calculateCircleIntersection = (
  point1: ReferencePoint,
  radius1: number,
  point2: ReferencePoint,
  radius2: number
): HangPoint[] => {
  try {
    // 使用turf.js计算实际地理距离
    const turfPoint1 = turf.point([point1.lng, point1.lat]);
    const turfPoint2 = turf.point([point2.lng, point2.lat]);
    const distanceMeters = turf.distance(turfPoint1, turfPoint2, {
      units: "meters",
    });

    console.log("精确计算参数:", {
      point1: { lng: point1.lng, lat: point1.lat },
      point2: { lng: point2.lng, lat: point2.lat },
      radius1: radius1,
      radius2: radius2,
      realDistance: distanceMeters,
    });

    // 检查圆是否相交
    if (distanceMeters > radius1 + radius2) {
      throw new Error(
        `两个圆不相交，当前距离${distanceMeters.toFixed(2)}米 > 半径之和${(
          radius1 + radius2
        ).toFixed(2)}米，请调整距离参数`
      );
    }

    if (distanceMeters < Math.abs(radius1 - radius2)) {
      throw new Error(
        `一个圆完全包含另一个圆，当前距离${distanceMeters.toFixed(
          2
        )}米 < 半径差${Math.abs(radius1 - radius2).toFixed(
          2
        )}米，请调整距离参数`
      );
    }

    if (distanceMeters === 0 && radius1 === radius2) {
      throw new Error("两个圆完全重合，无法确定栓点位置");
    }

    // 如果距离为0但半径不同，返回圆心点（理论上不应该发生）
    if (distanceMeters === 0) {
      throw new Error("参照点重合，无法进行栓点计算");
    }

    // 计算交点位置
    // 使用余弦定理和几何关系计算交点
    const a =
      (radius1 * radius1 -
        radius2 * radius2 +
        distanceMeters * distanceMeters) /
      (2 * distanceMeters);
    const h = Math.sqrt(radius1 * radius1 - a * a);

    // 计算两点连线的方位角
    const bearing = turf.bearing(turfPoint1, turfPoint2);

    // 中间点：从point1沿着到point2方向移动距离a
    const midPoint = turf.destination(turfPoint1, a / 1000, bearing, {
      units: "kilometers",
    });

    // 计算垂直于两点连线的方向
    const perpBearing1 = bearing + 90;
    const perpBearing2 = bearing - 90;

    // 两个交点：从中间点沿垂直方向移动距离h
    const intersection1 = turf.destination(midPoint, h / 1000, perpBearing1, {
      units: "kilometers",
    });
    const intersection2 = turf.destination(midPoint, h / 1000, perpBearing2, {
      units: "kilometers",
    });

    const intersections: HangPoint[] = [];

    // 验证第一个交点
    if (
      intersection1 &&
      intersection1.geometry &&
      intersection1.geometry.coordinates
    ) {
      const coords1 = intersection1.geometry.coordinates;
      intersections.push({
        lng: coords1[0],
        lat: coords1[1],
        distance1: radius1,
        distance2: radius2,
      });
    }

    // 验证第二个交点（只有当它与第一个交点不同时才添加）
    if (
      intersection2 &&
      intersection2.geometry &&
      intersection2.geometry.coordinates
    ) {
      const coords2 = intersection2.geometry.coordinates;

      // 检查两个交点是否实际不同（考虑精度）
      const distanceBetweenIntersections = turf.distance(
        intersection1,
        intersection2,
        { units: "meters" }
      );
      if (distanceBetweenIntersections > 0.01) {
        // 1厘米的精度
        intersections.push({
          lng: coords2[0],
          lat: coords2[1],
          distance1: radius1,
          distance2: radius2,
        });
      }
    }

    // 验证计算结果：检查计算出的交点到参照点的距离是否正确
    intersections.forEach((intersection, index) => {
      const intersectionPoint = turf.point([
        intersection.lng,
        intersection.lat,
      ]);
      const dist1 = turf.distance(turfPoint1, intersectionPoint, {
        units: "meters",
      });
      const dist2 = turf.distance(turfPoint2, intersectionPoint, {
        units: "meters",
      });

      console.log(`交点${index + 1}验证:`, {
        交点坐标: { lng: intersection.lng, lat: intersection.lat },
        到参照点1距离: dist1.toFixed(2) + "米（期望：" + radius1 + "米）",
        到参照点2距离: dist2.toFixed(2) + "米（期望：" + radius2 + "米）",
        距离误差1: Math.abs(dist1 - radius1).toFixed(2) + "米",
        距离误差2: Math.abs(dist2 - radius2).toFixed(2) + "米",
      });
    });

    console.log(`成功计算出 ${intersections.length} 个交点`);
    return intersections;
  } catch (error) {
    console.error("计算圆交点失败:", error);
    throw error;
  }
};

/**
 * 清除栓点相关的临时图层
 */
const clearHangPointsLayers = () => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      return;
    }

    // 清理栓点图层和标签图层
    const hangPointLayerId = TEMP_LAYER_IDS.HANG_POINTS_LAYER;
    const hangPointLabelLayerId = `${hangPointLayerId}-label`;
    const hangPointSourceId = TEMP_LAYER_IDS.HANG_POINTS_SOURCE;

    // 移除栓点图层
    if (map.getLayer(hangPointLayerId)) {
      try {
        map.removeLayer(hangPointLayerId);
        console.log(`已移除栓点图层: ${hangPointLayerId}`);
      } catch (error) {
        console.warn(`移除栓点图层失败:`, error);
      }
    }

    // 移除栓点标签图层
    if (map.getLayer(hangPointLabelLayerId)) {
      try {
        map.removeLayer(hangPointLabelLayerId);
        console.log(`已移除栓点标签图层: ${hangPointLabelLayerId}`);
      } catch (error) {
        console.warn(`移除栓点标签图层失败:`, error);
      }
    }

    // 移除栓点数据源
    if (map.getSource(hangPointSourceId)) {
      try {
        map.removeSource(hangPointSourceId);
        console.log(`已移除栓点数据源: ${hangPointSourceId}`);
      } catch (error) {
        console.warn(`移除栓点数据源失败:`, error);
      }
    }

    console.log("已清除栓点相关图层");
  } catch (error) {
    console.error("清除栓点图层失败:", error);
  }
};

/**
 * 清除所有临时图层
 */
const clearAllTempLayers = () => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      return;
    }

    // 首先收集所有需要移除的图层ID
    const layersToRemove: string[] = [];
    const sourcesToRemove: string[] = [];

    Object.values(TEMP_LAYER_IDS).forEach((id) => {
      // 收集主图层
      if (map.getLayer(id)) {
        layersToRemove.push(id);
      }

      // 收集标签图层
      const labelLayerId = `${id}-label`;
      if (map.getLayer(labelLayerId)) {
        layersToRemove.push(labelLayerId);
      }

      // 收集边框图层
      const strokeLayerId = `${id}-stroke`;
      if (map.getLayer(strokeLayerId)) {
        layersToRemove.push(strokeLayerId);
      }

      // 收集数据源
      if (map.getSource(id)) {
        sourcesToRemove.push(id);
      }
    });

    // 先移除所有图层
    layersToRemove.forEach((layerId) => {
      try {
        map.removeLayer(layerId);
        console.log(`已移除图层: ${layerId}`);
      } catch (layerError) {
        console.warn(`移除图层 ${layerId} 时出错:`, layerError);
      }
    });

    // 再移除所有数据源
    sourcesToRemove.forEach((sourceId) => {
      try {
        map.removeSource(sourceId);
        console.log(`已移除数据源: ${sourceId}`);
      } catch (sourceError) {
        console.warn(`移除数据源 ${sourceId} 时出错:`, sourceError);
      }
    });

    console.log("已清除所有两点栓点临时图层");
  } catch (error) {
    console.error("清除临时图层失败:", error);
  }
};

/**
 * 处理选择参照点1
 */
const handleSelectPoint1 = async (): Promise<void> => {
  try {
    if (isSelectingPoint1.value) {
      return;
    }

    isSelectingPoint1.value = true;
    ElMessage.info("请在地图上点击选择参照点1");

    const point = await selectPointFromMap();
    referencePoint1.value = point;

    // 添加参照点图层
    addReferencePointLayer(point, 1);

    ElMessage.success(`已选择参照点1: ${point.gxddh}`);

    // 如果已有参照点2，提示两点间距离
    if (referencePoint2.value && referencePointsDistance.value) {
      ElMessage.info(
        `两个参照点间距离为: ${referencePointsDistance.value.formatted}`
      );
    }
  } catch (error) {
    console.error("选择参照点1失败:", error);
    ElMessage.error(
      `选择参照点1失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isSelectingPoint1.value = false;
  }
};

/**
 * 处理选择参照点2
 */
const handleSelectPoint2 = async (): Promise<void> => {
  try {
    if (isSelectingPoint2.value) {
      return;
    }

    isSelectingPoint2.value = true;
    ElMessage.info("请在地图上点击选择参照点2");

    const point = await selectPointFromMap();
    referencePoint2.value = point;

    // 添加参照点图层
    addReferencePointLayer(point, 2);

    ElMessage.success(`已选择参照点2: ${point.gxddh}`);

    // 如果已有参照点1，计算并提示两点间距离
    if (referencePoint1.value && referencePointsDistance.value) {
      ElMessage.info(
        `两个参照点间距离为: ${referencePointsDistance.value.formatted}，可作为设置栓点距离的参考`
      );
    }
  } catch (error) {
    console.error("选择参照点2失败:", error);
    ElMessage.error(
      `选择参照点2失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isSelectingPoint2.value = false;
  }
};

/**
 * 处理计算栓点
 */
const handleCalculate = async (): Promise<void> => {
  try {
    if (!canCalculate.value) {
      ElMessage.warning("请完成参照点选择和距离设置");
      return;
    }

    isCalculating.value = true;
    showResult.value = false;
    showResultTable.value = false;
    hangPoints.value = [];

    // 清除上次计算的栓点图层
    clearHangPointsLayers();

    ElMessage.info("正在计算栓点位置...");

    // 首先绘制辅助圆圈（无论计算是否成功都绘制）
    addCircleLayer(referencePoint1.value!, getDistance1Number(), 1);
    addCircleLayer(referencePoint2.value!, getDistance2Number(), 2);

    try {
      // 计算两圆交点
      const intersections = calculateCircleIntersection(
        referencePoint1.value!,
        getDistance1Number(),
        referencePoint2.value!,
        getDistance2Number()
      );

      if (intersections.length === 0) {
        throw new Error("无法计算出有效的栓点位置");
      }

      // 显示栓点
      addHangPointsLayer(intersections);

      // 更新结果
      hangPoints.value = intersections;
      calculationResult.value = {
        success: true,
        message: `成功计算出 ${intersections.length} 个栓点位置`,
      };
      showResult.value = true;
      showResultTable.value = true;

      ElMessage.success(`计算完成，共找到 ${intersections.length} 个栓点位置`);
    } catch (calculationError) {
      console.error("计算栓点失败:", calculationError);

      // 即使计算失败，也显示结果（包含错误信息），圆圈已经绘制
      calculationResult.value = {
        success: false,
        message: `计算失败: ${
          calculationError instanceof Error
            ? calculationError.message
            : "未知错误"
        }`,
      };
      showResult.value = true;
      showResultTable.value = false; // 计算失败时不显示结果表格

      ElMessage.warning(
        `计算失败: ${
          calculationError instanceof Error
            ? calculationError.message
            : "未知错误"
        }，参照圆圈已显示供参考`
      );
    }
  } catch (error) {
    console.error("启动计算失败:", error);
    ElMessage.error(
      `启动计算失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isCalculating.value = false;
  }
};

/**
 * 处理清除操作
 */
const handleClear = (): void => {
  try {
    // 清除数据
    referencePoint1.value = null;
    referencePoint2.value = null;
    distance1.value = "";
    distance2.value = "";
    hangPoints.value = [];
    showResult.value = false;
    showResultTable.value = false;
    calculationResult.value = { success: false, message: "" };

    // 清除地图图层
    clearAllTempLayers();

    ElMessage.success("已清除所有数据");
  } catch (error) {
    console.error("清除操作失败:", error);
    ElMessage.error("清除操作失败");
  }
};

/**
 * 处理新增管点
 * @param hangPoint 栓点数据
 */
const handleAddNode = (hangPoint: HangPoint): void => {
  try {
    // 触发父组件的新增管点事件
    emit("addNode", {
      lng: hangPoint.lng,
      lat: hangPoint.lat,
      source: "two-point-hang",
      referencePoint1: referencePoint1.value,
      referencePoint2: referencePoint2.value,
      distance1: getDistance1Number(),
      distance2: getDistance2Number(),
    });

    // ElMessage.success("开始新增管点，请填写管点信息");
  } catch (error) {
    console.error("新增管点失败:", error);
    ElMessage.error("新增管点失败");
  }
};

// 定义组件事件
const emit = defineEmits<{
  addNode: [
    data: {
      lng: number;
      lat: number;
      source: string;
      referencePoint1: ReferencePoint | null;
      referencePoint2: ReferencePoint | null;
      distance1: number;
      distance2: number;
    }
  ];
  close: [];
}>();
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
// 组件卸载时清理资源
onUnmounted(() => {
  clearAllTempLayers();
});
</script>

<style scoped>
.two-point-hang-panel {
  width: 600px;
  min-height: 130px;
  position: absolute;
  top: 190px;
  left: 10px;
  z-index: 1000;
}

.button-section {
  margin-bottom: 16px;
  gap: 12px;
}

.distance-section {
  margin-bottom: 16px;
}

.distance-input-group {
  display: flex;
  align-items: center;
  /* flex-direction: column; */
  /* gap: 8px; */
}

.distance-label {
  width: 160px;
  font-size: 14px;
  color: #5c5f66;
  font-weight: 500;
}

.distance-input {
  width: 200px;
  height: 32px;
}
.title {
  font-size: 14px;
  color: #2c3037;
}
.brd {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  /* border: 1px solid rgba(220, 220, 220, 1); */
  border-radius: 4px;
  padding: 0px 10px 20px 10px;
  /* margin: 10px 0; */
}
.calculation-section {
  margin-bottom: 16px;
  gap: 12px;
}

.points-section {
  /* display: flex;
  align-items: center;
  justify-content: space-between; */
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.point-row {
  margin-bottom: 8px;
}

.point-label {
  font-weight: 500;
  font-size: 14px;
  color: #1966ff;
  margin-bottom: 4px;
  display: block;
}

.point-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.point-text {
  font-size: 14px;
  color: #2c3037;
}

.coordinate-text {
  font-size: 14px;
  color: #909399;
  font-family: SansCN-Regular;
}

.distance-text {
  font-size: 14px;
  color: #ff7d00;
  font-weight: 500;
}

.result-section {
  margin: 20px 0;
  color: #333333;
  padding: 5px 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.result-label {
  /* font-weight: 600; */
  font-size: 14px;
  color: #333333;
  margin-right: 5px;
  margin-left: 10px;
}

.result-text {
  font-size: 14px;
  margin-left: 10px;
}

/* .result-text.success {
  color: #67c23a;
}

.result-text.error {
  color: #f56c6c;
} */
.success {
  background: #eff9f1;
  border: 1px solid #00b42a;
}

.error {
  background: #fef1f1;
  border: 1px solid #ff9292;
}
.table-section {
  margin-bottom: 16px;
}

.table-label {
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
  display: block;
}

.pagination {
  margin-top: 16px;
  justify-content: center;
}

.distance-reference-row {
  /* margin-top: 16px;
  padding: 12px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #409eff; */
  width: 100%;
  box-sizing: border-box;
  padding: 5px;
  border: 1px dashed rgba(146, 183, 255, 1);
  background: rgba(241, 248, 254, 1);
  font-size: 14px;
}

.distance-reference {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.distance-reference-label {
  font-weight: 600;
  color: #409eff;
}

.distance-reference-value {
  font-size: 14px;
  font-weight: 500;
}

.distance-reference-tip {
  font-size: 12px;
  color: #666;
}
</style>
