import axios from "axios"

//修改为物联网设备URL
const IOT_DEVICE_URL = 'http://36.138.75.137:8077'
export interface IexportDevice {
  size: number,
  current: number,
  coordinate?: any,
  field_group?: any,
}
/**
 * 设备查询
 * @param data 查询条件
 * @returns 
 */
export const queryDevice = (data: any) => {
  return axios.get(IOT_DEVICE_URL + '/v1/device/coordinate', { params: data })
}


/**
 * 设备导出
 * @param data 查询条件
 * @returns 
 */
export const exportDevice = (data: IexportDevice) => {
  return axios.get(IOT_DEVICE_URL + '/v1/device/coordinate/export', { params: data })
}


/**
 * 设备字段查询
 * @param data 查询条件
 * @returns 
 */
export const queryDeviceField = (data: any) => {
  return axios.get(IOT_DEVICE_URL + '/v1/device/fields', { params: data })
}



