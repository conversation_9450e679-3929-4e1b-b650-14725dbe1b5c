<template>
  <div>
    <div class="flex justify-between">
      <div>月度访问趋势分析</div>
      <div>
        <el-select
          v-model="sevenDays"
          class="w-25 drawer-select"
          popper-class="drawer-popper-select"
          placeholder="请选择"
          size="small"
          @change="sedayChange"
        >
          <el-option
            v-for="item in last12Months"
            :key="ite"
            :label="item + '月'"
            :value="item"
          />
        </el-select>
      </div>
    </div>

    <div class="chart" ref="categoryRef"></div>
  </div>
</template>
<script lang="ts" setup>
import { initMonthlyEchart } from "@/lib/echarts";
import { visitorMonth } from "@/api/log";
const { initChart } = useEchart();
const now = new Date();
const currentYear = now.getFullYear();
const categoryRef = ref();
const sevenDays = ref(now.getMonth() + 1);
const date = ref<any>([]);
const twoGis = ref<any>([]);
const threeGis = ref<any>([]);
const gisService = ref<any>([]);
const backgroundManage = ref<any>([]);
const getCurrentAndPreviousMonths = (count) => {
  const months = [];
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  for (let month = 0; month <= currentMonth; month++) {
    months.push(month + 1);
  }
  return months;
};

// 获取当前月及前11个月（共12个月）
const last12Months = getCurrentAndPreviousMonths(12);
const getChart = async () => {
  date.value = [];
  twoGis.value = [];
  threeGis.value = [];
  gisService.value = [];
  backgroundManage.value = [];
  const result = await visitorMonth({
    year: currentYear,
    month: sevenDays.value,
  });
  if (result.code === 200) {
    result.data.forEach((item: any) => {
      date.value.push(item.date);
      twoGis.value.push(item.twoGis);
      threeGis.value.push(item.threeGis);
      gisService.value.push(item.gisService);
      backgroundManage.value.push(item.backgroundManage);
    });
    categoryRef.value &&
      initChart(
        categoryRef.value,
        initMonthlyEchart(
          date.value,
          twoGis.value,
          threeGis.value,
          gisService.value,
          backgroundManage.value
        )
      );
  }
};
const sedayChange = () => {
  getChart();
};
onMounted(() => {
  getChart();
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  // height: calc(100vh - 740px);
  height: 220px;
}
</style>
