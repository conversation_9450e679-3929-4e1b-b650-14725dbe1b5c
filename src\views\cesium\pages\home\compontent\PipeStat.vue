<template>
  <BaseCard title="管材统计">
    <div class="chart" ref="pipeStatRef"></div>
  </BaseCard>
</template>
<script lang="ts" setup>
import { initPipeStatEchart } from "@/lib/echarts";
import { gcStat } from "@/api/home";
const { initChart } = useEchart();
const pipeStatRef = ref();
const list = ref<any[]>([]);
const getChart = async () => {
  const { data, code } = await gcStat();
  if (code === 200) {
    list.value = Object.keys(data).map((key) => ({
      name: key,
      visits: data[key],
    }));
  }
  pipeStatRef.value &&
    initChart(pipeStatRef.value, initPipeStatEchart(list.value));
};
onMounted(() => {
  getChart();
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 300px;
}
</style>
