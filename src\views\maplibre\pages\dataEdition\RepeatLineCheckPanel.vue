<!--
 * @Description: 重复线检查面板
 * @Date: 2024-07-04
 * @Author: AI Assistant
 -->
<template>
  <page-card
    class="repeat-line-check-panel"
    title="重复线检查"
    @closeCard="closeCard"
  >
    <!-- 统计信息 -->
    <div class="stats-section" v-if="!loading">
      <el-alert
        :title="`发现 ${getRepeatGroupCount()} 组重复线数据${
          getRepeatGroupCount() > 0
            ? '，总计' + getTotalRepeatCount() + '条重复线'
            : ''
        }`"
        :type="total > 0 ? 'warning' : 'success'"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 操作按钮区域 -->
    <el-row class="button-section" flex="~ row justify-start">
      <el-button
        type="primary"
        class="select-btn h-9"
        :icon="Refresh"
        @click="handleRefresh"
        :loading="loading"
      >
        刷新检查
      </el-button>
    </el-row>

    <!-- 重复线列表表格 -->
    <el-row v-if="repeatLines.length > 0" class="table-section">
      <el-col :span="24">
        <el-text class="table-label">重复线详细信息</el-text>
        <el-table
          :data="repeatLines"
          v-loading="loading"
          border
          stripe
          class="routeCt"
          height="300"
          empty-text="暂无重复线数据"
          :span-method="arraySpanMethod"
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            :index="getTableIndex"
          />
          <el-table-column
            prop="count"
            label="重复数量"
            width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="gid"
            label="GID"
            width="80"
            align="center"
            show-overflow-tooltip
          />

          <el-table-column
            prop="gxbm"
            label="管线编码"
            width="130"
            show-overflow-tooltip
          />
          <el-table-column
            prop="gl"
            label="管类"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="gj"
            label="管径"
            width="90"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.gj ? `${row.gj}mm` : "-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="szdl"
            label="所在道路"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="操作"
            width="120"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleLocate(row)"
                title="定位到地图"
              >
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center">
          <div>
            <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
          </div>
          <!-- 分页组件 -->
          <el-pagination
            v-if="total > 0"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :total="total"
            :pager-count="5"
            layout="prev, pager, next, jumper"
            class="pagination"
            background
            small
          />
        </div>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import { Refresh, Position } from "@element-plus/icons-vue";
import PageCard from "@/components/PageCard.vue";
import { queryRepeatLine } from "@/api/pipeCheck";
import type {
  RepeatLnVo,
  RepeatLinePageQuery,
  RepeatLineGroup,
  RepeatLineDetail,
} from "@/api/pipeCheck";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import type { Map as MapLibreMap } from "maplibre-gl";

const REPEAT_LINE_LAYER_IDS = {
  SOURCE: "repeat-line-highlight-source",
  LAYER: "repeat-line-highlight-layer",
} as const;

interface Emits {
  (e: "close"): void;
}
const emit = defineEmits<Emits>();

const loading = ref(false);
const repeatLines = ref<RepeatLnVo[]>([]);
const originalGroupData = ref<RepeatLineGroup[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const pageSizes = ref([5, 10, 20, 50]);
let map: MapLibreMap | null = null;

onMounted(async () => {
  try {
    map = AppMaplibre.getMap();
  } catch (error) {
    console.warn("获取地图实例失败:", error);
  }
  await handleRefresh();
});

onUnmounted(() => {
  clearRepeatLineHighlight();
  resetData();
});

const clearRepeatLineHighlight = (): void => {
  if (!map) return;
  if (map.getLayer(REPEAT_LINE_LAYER_IDS.LAYER)) {
    map.removeLayer(REPEAT_LINE_LAYER_IDS.LAYER);
  }
  if (map.getSource(REPEAT_LINE_LAYER_IDS.SOURCE)) {
    map.removeSource(REPEAT_LINE_LAYER_IDS.SOURCE);
  }
};

const addRepeatLineHighlight = (geometry: any): void => {
  if (!map) return;
  clearRepeatLineHighlight();
  map.addSource(REPEAT_LINE_LAYER_IDS.SOURCE, {
    type: "geojson",
    data: {
      type: "FeatureCollection",
      features: [{ type: "Feature", geometry, properties: {} }],
    },
  });
  map.addLayer({
    id: REPEAT_LINE_LAYER_IDS.LAYER,
    type: "line",
    source: REPEAT_LINE_LAYER_IDS.SOURCE,
    layout: { "line-join": "round", "line-cap": "round" },
    paint: { "line-color": "#ff4d4f", "line-width": 6, "line-opacity": 0.8 },
  });
};

/**
 * 计算表格序号，基于重复组计算
 * @param index 行索引
 * @returns 序号
 */
const getTableIndex = (index: number): number => {
  const row = repeatLines.value[index];
  if (!row) return index + 1;

  // 只有组内第一行才显示序号，其他行通过行合并不显示
  if (row.rowIndexInGroup === 0) {
    // 计算当前组在当前页面中的序号
    let groupSequence = 0;
    const seenGroups = new Set<number>();

    for (let i = 0; i <= index; i++) {
      const currentRow = repeatLines.value[i];
      if (
        currentRow?.rowIndexInGroup === 0 &&
        !seenGroups.has(currentRow.groupIndex)
      ) {
        groupSequence++;
        seenGroups.add(currentRow.groupIndex);
        if (i === index) {
          return groupSequence;
        }
      }
    }
    return groupSequence;
  }

  // 非第一行返回占位符（实际不会显示，因为行合并）
  return index + 1;
};
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  handleRefresh();
};
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  handleRefresh();
};
const closeCard = () => {
  clearRepeatLineHighlight();
  resetData();
  emit("close");
};

// ==================== 数据处理工具函数 ====================

/**
 * 创建单个重复线的扁平化数据项
 * @param line 重复线详细信息
 * @param group 所属分组信息
 * @param groupIndex 分组索引
 * @param lineIndex 线在分组中的索引
 * @returns 扁平化数据项
 */
const createFlatLineItem = (
  line: RepeatLineDetail,
  group: RepeatLineGroup,
  groupIndex: number,
  lineIndex: number
): RepeatLnVo => {
  return {
    groupId: `group_${groupIndex}`,
    groupIndex: groupIndex,
    rowIndexInGroup: lineIndex,
    totalRowsInGroup: group.list.length,
    gxbm: line.gxbm || `未知编码_${groupIndex}_${lineIndex}`,
    count: group.count,
    gl: line.gl || "",
    gj: line.gj || "",
    szdl: line.szdl || "未知道路",
    geojson: line.geojson || "",
    gidArray: group.gidArray,
    // 当前行对应的具体重复线详细信息
    gid: line.gid,
    x: line.x,
    y: line.y,
    length: line.length,
  };
};

/**
 * 将重复线分组数据转换为扁平化格式，每个重复线作为单独一行
 * @param groupData 重复线分组数据
 * @returns 扁平化的重复线数据
 */
const convertGroupDataToFlatData = (
  groupData: RepeatLineGroup[]
): RepeatLnVo[] => {
  const flatData: RepeatLnVo[] = [];

  groupData.forEach((group, groupIndex) => {
    if (group.list && group.list.length > 0) {
      // 将每个重复线都作为单独的行
      group.list.forEach((line, lineIndex) => {
        const flatItem = createFlatLineItem(line, group, groupIndex, lineIndex);
        flatData.push(flatItem);
      });
    }
  });

  return flatData;
};

/**
 * 解析线要素的边界范围
 * @param line 重复线数据
 * @returns 边界范围或null
 */
const parseLineBounds = (
  line: RepeatLnVo
): [[number, number], [number, number]] | null => {
  if (!line.geojson) return null;

  try {
    const geometry = JSON.parse(line.geojson);
    if (geometry.type === "LineString" && geometry.coordinates) {
      return calculateBounds(geometry.coordinates);
    }
  } catch (error) {
    console.warn("解析线要素geojson失败:", error);
  }

  return null;
};

/**
 * 计算总的重复线数量
 * 基于原始分组数据进行统计，避免重复计算
 * @returns 总重复线数量
 */
const getTotalRepeatCount = (): number => {
  return originalGroupData.value.reduce((total, group) => {
    // 每个分组的重复线数量就是该组内list的长度
    return total + (group.list?.length || 0);
  }, 0);
};

/**
 * 计算重复线分组数量
 * @returns 重复线分组数量
 */
const getRepeatGroupCount = (): number => {
  return originalGroupData.value.length;
};

/**
 * 重置数据状态
 */
const resetData = (): void => {
  repeatLines.value = [];
  originalGroupData.value = [];
  total.value = 0;
};

/**
 * 表格行合并方法
 * @param row 当前行数据
 * @param column 当前列
 * @param rowIndex 行索引
 * @param columnIndex 列索引
 * @returns 合并信息 [rowspan, colspan]
 */
const arraySpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}: any): [number, number] => {
  const currentRow = row as RepeatLnVo;

  // 需要合并的列索引：序号(0)、重复数量(1)
  // 列索引对应：0=序号, 1=重复数量, 2=GID, 3=管线编码, 4=管类, 5=管径, 6=所在道路, 7=操作
  const mergeColumnIndexes = [0, 1];

  if (mergeColumnIndexes.includes(columnIndex)) {
    // 如果是组内第一行，显示合并单元格
    if (currentRow.rowIndexInGroup === 0) {
      return [currentRow.totalRowsInGroup, 1];
    } else {
      // 其他行不显示
      return [0, 0];
    }
  }

  // 其他列正常显示
  return [1, 1];
};

const handleRefresh = async (): Promise<void> => {
  loading.value = true;
  clearRepeatLineHighlight();

  try {
    const params: RepeatLinePageQuery = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    };

    const response = await queryRepeatLine(params);

    if (response.code === 200 && response.data) {
      // 保存原始分组数据
      const rawGroupData: RepeatLineGroup[] = response.data.list || [];
      originalGroupData.value = rawGroupData;

      // 转换为表格显示数据
      const convertedData = convertGroupDataToFlatData(rawGroupData);
      repeatLines.value = convertedData;

      // 更新分页信息
      total.value = response.data.totalCount || 0;

      // 用户反馈
      if (total.value === 0) {
        ElMessage.success("数据健康！未发现重复线");
      } else {
        const totalRepeatCount = getTotalRepeatCount();
        const groupCount = getRepeatGroupCount();
        ElMessage.info(
          `发现 ${groupCount} 组重复线，共计 ${totalRepeatCount} 条重复线`
        );
      }
    } else {
      // 错误处理
      const errorMsg = response.msg || "未知错误";
      ElMessage.error(`查询重复线失败: ${errorMsg}`);
      resetData();
    }
  } catch (error) {
    console.error("查询重复线异常:", error);
    const errorMsg = error instanceof Error ? error.message : "网络或系统异常";
    ElMessage.error(`查询重复线异常: ${errorMsg}`);
    resetData();
  } finally {
    loading.value = false;
  }
};

const handleLocate = (line: RepeatLnVo): void => {
  if (!map) {
    ElMessage.error("地图实例不可用");
    return;
  }

  try {
    const bounds = parseLineBounds(line);

    if (!bounds) {
      ElMessage.error("无法获取有效的线要素边界");
      return;
    }

    // 创建用于高亮的几何对象
    const geometry = JSON.parse(line.geojson);
    addRepeatLineHighlight(geometry);

    // 定位到线要素
    map.fitBounds(bounds, { padding: 50, duration: 1500 });

    // const lineName = line.gxbm || line.gid || "未知";
    // ElMessage.success(
    //   `已定位到管线: ${lineName} (GID: ${line.gid})，该组共发现 ${line.count} 条重复线`
    // );
  } catch (error) {
    console.error("定位失败:", error);
    ElMessage.error("定位失败，请检查数据格式");
  }
};

const calculateBounds = (
  coords: any[]
): [[number, number], [number, number]] | null => {
  if (!coords || coords.length === 0) return null;
  const flatCoords = coords.flat(Infinity).filter((c) => typeof c === "number");
  if (flatCoords.length < 2) return null;

  let minLng = Infinity,
    minLat = Infinity,
    maxLng = -Infinity,
    maxLat = -Infinity;
  for (let i = 0; i < flatCoords.length; i += 2) {
    const lng = flatCoords[i];
    const lat = flatCoords[i + 1];
    minLng = Math.min(minLng, lng);
    minLat = Math.min(minLat, lat);
    maxLng = Math.max(maxLng, lng);
    maxLat = Math.max(maxLat, lat);
  }
  return [
    [minLng, minLat],
    [maxLng, maxLat],
  ];
};
</script>

<style scoped lang="scss">
.repeat-line-check-panel {
  position: absolute;
  left: 10px;
  top: 150px;
  width: 724px;
  max-height: 85vh;
  z-index: 999;
}
.stats-section,
.button-section,
.table-section {
  margin-bottom: 16px;
}
.table-label {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}
</style>
