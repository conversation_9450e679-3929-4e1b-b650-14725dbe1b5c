import hRequest from '@/utils/http'
import type { DataType } from '@/utils/http/types'
// 分页
export const queryDict = (data: any) => {
  return hRequest.get<DataType>({
    url: '/sys/config/page',
    params: data
  })
}
// 新增
export const addDict = (data: any) => {
  return hRequest.post<DataType>({
    url: "/sys/config",
    data: data
  });
};
// 修改
export const editDict = (data: any) => {
  return hRequest.put<DataType>({
    url: "/sys/config",
    data: data
  });
};
// 详情
export const detailsDict = (id: any) => {
  return hRequest.get<DataType>({
    url: `/sys/config/${id}`,
  });
};
// 删除
export const deleteDict = (id: any) => {
  return hRequest.delete<DataType>({
    url: `/sys/config/${id}`,
  });
};