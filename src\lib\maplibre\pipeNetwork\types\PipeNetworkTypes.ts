/**
 * @fileoverview 管网数据类型定义
 * @description 定义管网编辑系统中的数据模型、接口和枚举
 * <AUTHOR>
 * @version 1.0.0
 */

import type { PlotFeature } from '@/lib/maplibre/layer/types/LayerTypes';

/**
 * @enum ManholeCoverSpec
 * @description 井盖规格枚举
 */
export enum ManholeCoverSpec {
  /** 700mm */
  SIZE_700 = '700',
  /** 600*600mm */
  SIZE_600X600 = '600*600',
  /** 1200*1000mm */
  SIZE_1200X1000 = '1200*1000',
  /** 1000mm */
  SIZE_1000 = '1000',
  /** 1200*800mm */
  SIZE_1200X800 = '1200*800',
  /** 1000*1000mm */
  SIZE_1000X1000 = '1000*1000',
  /** 800*800mm */
  SIZE_800X800 = '800*800',
  /** 1300*1200mm */
  SIZE_1300X1200 = '1300*1200',
  /** 1300*1300mm */
  SIZE_1300X1300 = '1300*1300',
  /** 1000*800mm */
  SIZE_1000X800 = '1000*800',
  /** 900*900mm */
  SIZE_900X900 = '900*900',
  /** 1400*900mm */
  SIZE_1400X900 = '1400*900',
  /** 1200*1200mm */
  SIZE_1200X1200 = '1200*1200',
  /** 800*500mm */
  SIZE_800X500 = '800*500',
  /** 500*1000mm */
  SIZE_500X1000 = '500*1000',
  /** 1200*1500mm */
  SIZE_1200X1500 = '1200*1500',
  /** 1200*500mm */
  SIZE_1200X500 = '1200*500',
  /** 1500*800mm */
  SIZE_1500X800 = '1500*800',
  /** 800mm */
  SIZE_800 = '800',
  /** 2000*1200mm */
  SIZE_2000X1200 = '2000*1200',
  /** 1000*100mm */
  SIZE_1000X100 = '1000*100',
  /** 1000*500mm */
  SIZE_1000X500 = '1000*500',
  /** 1000*1500mm */
  SIZE_1000X1500 = '1000*1500',
  /** 700*900mm */
  SIZE_700X900 = '700*900',
  /** 600*400mm */
  SIZE_600X400 = '600*400',
  /** 1100*450mm */
  SIZE_1100X450 = '1100*450',
  /** 1200*400mm */
  SIZE_1200X400 = '1200*400',
  /** 850*600mm */
  SIZE_850X600 = '850*600',
  /** 500*500mm */
  SIZE_500X500 = '500*500',
  /** 1000*600mm */
  SIZE_1000X600 = '1000*600',
  /** 700*700mm */
  SIZE_700X700 = '700*700',
  /** 1500*900mm */
  SIZE_1500X900 = '1500*900',
  /** 700*600mm */
  SIZE_700X600 = '700*600',
  /** 1200*700mm */
  SIZE_1200X700 = '1200*700',
  /** 1000*1200mm */
  SIZE_1000X1200 = '1000*1200',
  /** 400*400mm */
  SIZE_400X400 = '400*400',
  /** 1250*980mm */
  SIZE_1250X980 = '1250*980',
  /** 800*600mm */
  SIZE_800X600 = '800*600',
  /** 800*750mm */
  SIZE_800X750 = '800*750',
  /** 1200*2000mm */
  SIZE_1200X2000 = '1200*2000',
  /** 1500*500mm */
  SIZE_1500X500 = '1500*500',
  /** 1500*750mm */
  SIZE_1500X750 = '1500*750',
  /** 1500*1500mm */
  SIZE_1500X1500 = '1500*1500',
  /** 1200*600mm */
  SIZE_1200X600 = '1200*600',
  /** 750*500mm */
  SIZE_750X500 = '750*500',
  /** 300*300mm */
  SIZE_300X300 = '300*300',
  /** 400*700mm */
  SIZE_400X700 = '400*700',
  /** 1000*1100mm */
  SIZE_1000X1100 = '1000*1100',
  /** 1200*2500mm */
  SIZE_1200X2500 = '1200*2500',
  /** 800*1200mm */
  SIZE_800X1200 = '800*1200',
  /** 400*600mm */
  SIZE_400X600 = '400*600',
  /** 1200*3000mm */
  SIZE_1200X3000 = '1200*3000',
  /** 1300*1700mm */
  SIZE_1300X1700 = '1300*1700',
  /** 1300*700mm */
  SIZE_1300X700 = '1300*700',
  /** 1600*500mm */
  SIZE_1600X500 = '1600*500',
  /** 1000*2000mm */
  SIZE_1000X2000 = '1000*2000',
  /** 500*600mm */
  SIZE_500X600 = '500*600',
  /** 1360*1100mm */
  SIZE_1360X1100 = '1360*1100',
  /** 600mm */
  SIZE_600 = '600',
  /** 600*500mm */
  SIZE_600X500 = '600*500',
  /** 750mm */
  SIZE_750 = '750',
  /** 500*250mm */
  SIZE_500X250 = '500*250',
  /** 500*300mm */
  SIZE_500X300 = '500*300',
  /** 700*300mm */
  SIZE_700X300 = '700*300',
  /** 700*400mm */
  SIZE_700X400 = '700*400',
  /** 800*250mm */
  SIZE_800X250 = '800*250',
  /** 780*650mm */
  SIZE_780X650 = '780*650',
  /** 1400*700mm */
  SIZE_1400X700 = '1400*700',
  /** 300*200mm */
  SIZE_300X200 = '300*200',
  /** 300*150mm */
  SIZE_300X150 = '300*150',
  /** 1280*900mm */
  SIZE_1280X900 = '1280*900',
  /** 500*800mm */
  SIZE_500X800 = '500*800',
  /** 1500*1200mm */
  SIZE_1500X1200 = '1500*1200'
}

/**
 * @interface PipeEndPoint
 * @description 管线端点详细信息接口
 */
export interface PipeEndPoint {
  /** 管点ID */
  nodeId: string;
  /** 管点编号 */
  nodeCode: string;
  /** 坐标 X */
  x: number;
  /** 坐标 Y */
  y: number;
  /** 地面高程 (m) */
  groundElevation?: number;
  /** 管底高程 (m) */
  pipeElevation?: number;
  /** 埋深 (m) */
  buriedDepth?: number;
}

/**
 * @enum PipeNodeType
 * @description 管点类型枚举
 */
export enum PipeNodeType {

  /**直线点 */
  STRAIGHT_POINT = 'straight_point',
  /**转折点 */
  TURNING_POINT = 'turning_point',
  /**变材 */
  CHANGE_MATERIAL = 'change_material',
  /**变径 */
  CHANGE_DIAMETER = 'change_diameter',
  /**出地 */
  OUT_OF_GROUND = 'out_of_ground',
  /**出水口 */
  OUTLET = 'outlet',
  /**多通 */
  MULTI_WAY = 'multi_way',
  /**三通 */
  THREE_WAY = 'three_way',
  /**四通 */
  FOUR_WAY = 'four_way',
  /**起止点 */
  START_END = 'start_end',
  /**终止点 */
  END_POINT = 'end_point',
  /**入户 */
  ENTRY = 'entry',
  /**弯头 */
  BEND = 'bend',
  /**预留口 */
  RESERVE = 'reserve'
}

/**
 * @enum PipeLineType
 * @description 管线类型枚举
 */
export enum PipeLineType {
  /** 供水 */
  JS = 'JS',
}

/**
 * @enum PipeMaterial
 * @description 管线材质枚举
 */
export enum PipeMaterial {
  /** 铸铁 */
  DUCTILE_IRON = 'ductile_iron',
  /** PVC */
  PVC = 'pvc',
  /** PE */
  PE = 'pe',
  /** 玻璃钢 */
  STEEL = 'steel',
}

/**
 * @enum PipeStatus
 * @description 管网设施状态枚举
 */
export enum PipeStatus {
  /** 正常 */
  NORMAL = 'normal',
  /** 维修中 */
  MAINTENANCE = 'maintenance',
  /** 故障 */
  FAULT = 'fault',
  /** 停用 */
  DISABLED = 'disabled',
  /** 规划中 */
  PLANNED = 'planned'
}

/**
 * @enum PipeNodeAccessory
 * @description 管点附属物类型枚举
 */
export enum PipeNodeAccessory {
  /** 无附属物 */
  NONE = 'none',
  /** 井盖 */
  MANHOLE_COVER = 'manhole_cover',
  /** 阀门井 */
  VALVE_CHAMBER = 'valve_chamber',
  /** 水表井 */
  METER_CHAMBER = 'meter_chamber',
  /** 消火栓 */
  FIRE_HYDRANT = 'fire_hydrant',
  /** 排气阀 */
  AIR_VALVE = 'air_valve',
  /** 排污阀 */
  DRAIN_VALVE = 'drain_valve',
  /** 泵站 */
  PUMP_STATION = 'pump_station',
  /** 阀门 */
  VALVE = 'valve',
  /** 管帽 */
  PIPE_CAP = 'pipe_cap',
  /** 水表 */
  WATER_METER = 'water_meter',
  /** 水塔 */
  WATER_TOWER = 'water_tower',
  /** 消防井 */
  FIRE_WELL = 'fire_well',
  /** 预留口 */
  RESERVED_PORT = 'reserved_port',
  /** 检修井 */
  MAINTENANCE_WELL = 'maintenance_well'
}

/**
 * @enum ManholeCoverMaterial
 * @description 井盖材质枚举
 */
export enum ManholeCoverMaterial {
  /**金属 */
  METAL = 'metal',
  /**塑料 */
  PLASTIC = 'plastic',
  /**砼 */
  CONCRETE = 'concrete',
  /** 铸铁 */
  CAST_IRON = 'cast_iron',
  /** 其他 */
  OTHER = 'other'
  
}

/**
 * @enum PipeInstallMethod
 * @description 管线埋设方式枚举
 */
export enum PipeInstallMethod {
  /** 直埋 */
  DIRECT_BURIAL = 'direct_burial',
  /** 明管 */
  TRENCH = 'trench',
  /** 架空 */
  PIPE_JACKING = 'pipe_jacking',
  /** 其他 */
  OTHER = 'other'
}

/**
 * @interface PipeNodeAttributes
 * @description 管点业务属性接口
 */
export interface PipeNodeAttributes {
  /** 管点编号（点号） */
  nodeCode: string;
  /** 管点类型（管类） */
  nodeType: PipeNodeType;
  /** 属性描述 */
  attributes?: string;
  /** 附属物类型 */
  accessory?: PipeNodeAccessory;
  /** 地面高程 (m) */
  groundElevation?: number;
  /** 井深 (m) */
  wellDepth?: number;
  /** 井盖规格 */
  manholeCover?: ManholeCoverSpec;
  /** 井盖材质 */
  manholeCoverMaterial?: ManholeCoverMaterial;
  /** 所在道路 */
  roadName?: string;
}

/**
 * @interface PipeLineAttributes  
 * @description 管线业务属性接口
 */
export interface PipeLineAttributes {
  /** 管线类型（管类） */
  lineType: PipeLineType;
  /** 管径 (mm) */
  diameter: number;
  /** 材质 */
  material: PipeMaterial;
  /** 埋设方式 */
  installMethod?: PipeInstallMethod;
  /** 管段长度 (m) */
  length?: number;
  /** 起始点详细信息 */
  startPoint?: PipeEndPoint;
  /** 终止点详细信息 */
  endPoint?: PipeEndPoint;
  /** 所在道路 */
  roadName?: string;
}

/**
 * @interface PipeNodeProperties
 * @description 管点GeoJSON属性接口
 */
export interface PipeNodeProperties {
  /** 要素类型标识 */
  type: 'pipe-node';
  /** 几何体类型 */
  geometryType: 'point';
  /** 管点业务属性 */
  attributes: PipeNodeAttributes;
  /** 连接的管线ID列表 */
  connectedLines: string[];
  /** 样式信息 */
  style?: Record<string, any>;
}

/**
 * @interface PipeLineProperties
 * @description 管线GeoJSON属性接口
 */
export interface PipeLineProperties {
  /** 要素类型标识 */
  type: 'pipe-line';
  /** 几何体类型 */
  geometryType: 'linestring';
  /** 管线业务属性 */
  attributes: PipeLineAttributes;
  /** 起始管点ID */
  startNodeId: string;
  /** 终止管点ID */
  endNodeId: string;
  /** 样式信息 */
  style?: Record<string, any>;
}

/**
 * @interface PipeNodeGeoJSON
 * @description 管点GeoJSON要素接口
 */
export interface PipeNodeGeoJSON {
  type: 'Feature';
  geometry: {
    type: 'Point';
    coordinates: [number, number, number?]; // [经度, 纬度, 高程?]
  };
  properties: PipeNodeProperties;
}

/**
 * @interface PipeLineGeoJSON
 * @description 管线GeoJSON要素接口
 */
export interface PipeLineGeoJSON {
  type: 'Feature';
  geometry: {
    type: 'LineString';
    coordinates: [number, number, number?][]; // [[经度, 纬度, 高程?], ...]
  };
  properties: PipeLineProperties;
}

/**
 * @interface PipeNode
 * @description 管点数据模型，扩展PlotFeature
 */
export interface PipeNode extends Omit<PlotFeature, 'geojson'> {
  /** 要素类型 */
  type: 'pipe-node';
  /** GeoJSON数据 */
  geojson: PipeNodeGeoJSON;
  /** 连接的管线ID列表 */
  connectedLines: string[];
  /** 后端数据库GID（用于API对接） */
  gid?: number;
}

/**
 * @interface PipeLine
 * @description 管线数据模型，扩展PlotFeature
 */
export interface PipeLine extends Omit<PlotFeature, 'geojson'> {
  /** 要素类型 */
  type: 'pipe-line';
  /** GeoJSON数据 */
  geojson: PipeLineGeoJSON;
  /** 起始管点ID */
  startNodeId: string;
  /** 终止管点ID */
  endNodeId: string;
}

/**
 * @type PipeFeature
 * @description 管网要素联合类型
 */
export type PipeFeature = PipeNode | PipeLine;


/**
 * @interface PipeEditOperation
 * @description 管网编辑操作接口
 */
export interface PipeEditOperation {
  /** 操作ID */
  id: string;
  /** 操作类型 */
  type: 'add' | 'delete' | 'update' | 'move' | 'connect' | 'disconnect' | 'split';
  /** 操作目标类型 */
  targetType: 'node' | 'line';
  /** 操作目标ID */
  targetId: string;
  /** 操作前数据 */
  beforeData?: any;
  /** 操作后数据 */
  afterData?: any;
  /** 操作时间戳 */
  timestamp: number;
  /** 操作用户 */
  user?: string;
  /** 操作描述 */
  description?: string;
}

/**
 * @interface PipeValidationResult
 * @description 管网数据验证结果接口（简化版）
 */
export interface PipeValidationResult {
  /** 是否有效 */
  valid: boolean;
  /** 错误信息列表 */
  errors: string[];
  /** 警告信息列表 */
  warnings: string[];
}

/**
 * @interface PipeInteractionConfig
 * @description 管网交互配置接口
 */
export interface PipeInteractionConfig {
  /** 是否启用管点拖拽 */
  enableNodeDrag: boolean;
  /** 是否启用管线拖拽 */
  enableLineDrag: boolean;
  /** 是否启用线上加点 */
  enableAddNodeOnLine: boolean;
  /** 是否启用点打断线 */
  enableSplitLine: boolean;
  /** 自动连接距离阈值 (像素) */
  autoConnectThreshold: number;
  /** 选择容差 (像素) */
  selectionTolerance: number;
  /** 是否显示辅助线 */
  showHelperLines: boolean;
  /** 是否启用网格吸附 */
  enableGridSnap: boolean;
  /** 网格间距 (像素) */
  gridSpacing: number;
}

/**
 * @interface PipeRenderOptions
 * @description 管网渲染选项接口
 */
export interface PipeRenderOptions {
  /** 管点样式配置 */
  nodeStyle: {
    /** 默认样式 */
    default: Record<string, any>;
    /** 选中样式 */
    selected: Record<string, any>;
    /** 悬停样式 */
    hover: Record<string, any>;
    /** 按类型的样式映射 */
    typeStyles: Record<PipeNodeType, Record<string, any>>;
  };
  /** 管线样式配置 */
  lineStyle: {
    /** 默认样式 */
    default: Record<string, any>;
    /** 选中样式 */
    selected: Record<string, any>;
    /** 悬停样式 */
    hover: Record<string, any>;
    /** 按类型的样式映射 */
    typeStyles: Record<PipeLineType, Record<string, any>>;
  };
  /** 是否显示标签 */
  showLabels: boolean;
  /** 标签字段 */
  labelField: string;
  /** 最小显示级别 */
  minZoom: number;
  /** 最大显示级别 */
  maxZoom: number;
}


/**
 * @description 默认管点属性
 */
export const DEFAULT_PIPE_NODE_ATTRIBUTES: Partial<PipeNodeAttributes> = {
  nodeType: PipeNodeType.STRAIGHT_POINT,
  accessory: PipeNodeAccessory.NONE,
  wellDepth: 1.5
};

/**
 * @description 默认管线属性
 */
export const DEFAULT_PIPE_LINE_ATTRIBUTES: Partial<PipeLineAttributes> = {
  lineType: PipeLineType.JS,
  material: PipeMaterial.DUCTILE_IRON,
  installMethod: PipeInstallMethod.DIRECT_BURIAL,
  diameter: 100
};

/**
 * @description 默认交互配置
 */
export const DEFAULT_PIPE_INTERACTION_CONFIG: PipeInteractionConfig = {
  enableNodeDrag: true,
  enableLineDrag: true,
  enableAddNodeOnLine: true,
  enableSplitLine: true,
  autoConnectThreshold: 10,
  selectionTolerance: 5,
  showHelperLines: true,
  enableGridSnap: false,
  gridSpacing: 20
}; 