import hRequest from '@/utils/http'
import type { DataType } from '@/utils/http/types'
import type { 
  PlottingPageQuery, 
  PlottingPageVo,
  PlottingCreateRequest,
  PlottingUpdateRequest,
  PlottingInfoVo 
} from '@/types/plotting'

/**
 * @description 标绘信息分页查询
 * @param params 查询参数，包含分页信息和排序信息
 * @returns Promise<DataType<{ list: PlottingPageVo[], total: number }>> 返回分页查询结果
 */
export const plottingPage = (params: PlottingPageQuery) => {
  return hRequest.get<DataType<{ list: PlottingPageVo[], totalCount: number }>>({
    url: '/business/plott/info/page',
    params
  })
}

/**
 * @description 新增标绘信息
 * @param data 标绘信息数据对象
 * @returns Promise<DataType<number>> 返回新增结果，包含生成的ID
 */
export const plottingAdd = (data: PlottingCreateRequest) => {
  return hRequest.post<DataType<number>>({
    url: '/business/plott/info',
    data
  })
}

/**
 * @description 修改标绘信息
 * @param data 标绘信息数据对象，需包含ID字段
 * @returns Promise<DataType<void>> 返回修改结果
 */
export const plottingEdit = (data: PlottingUpdateRequest) => {
  return hRequest.put<DataType<void>>({
    url: '/business/plott/info',
    data
  })
}

/**
 * @description 获取标绘信息详情
 * @param id 标绘信息ID
 * @returns Promise<DataType<PlottingInfoVo>> 返回标绘信息详细数据
 */
export const plottingDetail = (id: string | number) => {
  return hRequest.get<DataType<PlottingInfoVo>>({
    url: `/business/plott/info/${id}`,
  })
}

/**
 * @description 删除标绘信息
 * @param id 标绘信息ID
 * @returns Promise<DataType<void>> 返回删除结果
 */
export const plottingDelete = (id: string | number) => {
  return hRequest.delete<DataType<void>>({
    url: `/business/plott/info/${id}`,
  })
} 