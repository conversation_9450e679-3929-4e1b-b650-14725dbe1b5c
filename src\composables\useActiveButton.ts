import { ref, computed, readonly, type Ref } from 'vue';

/**
 * 按钮组active状态管理hooks
 * @param initialValue 初始激活的按钮值
 * @returns 
 */
export function useActiveButton<T = string>(initialValue?: T) {
  // 当前激活的按钮值
  const activeValue = ref<T | null>(initialValue || null) as Ref<T | null>;

  /**
   * 设置激活的按钮
   * @param value 按钮值
   */
  const setActive = (value: T | null) => {
    activeValue.value = value;
  };

  /**
   * 切换按钮状态
   * @param value 按钮值
   */
  const toggle = (value: T) => {
    activeValue.value = activeValue.value === value ? null : value;
  };

  /**
   * 检查按钮是否激活
   * @param value 按钮值
   * @returns 是否激活
   */
  const isActive = (value: T) => {
    return computed(() => activeValue.value === value);
  };

  /**
   * 清除所有激活状态
   */
  const clearActive = () => {
    activeValue.value = null;
  };

  return {
    activeValue: readonly(activeValue),
    setActive,
    toggle,
    isActive,
    clearActive
  };
}
