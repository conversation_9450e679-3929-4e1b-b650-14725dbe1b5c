<!--
 * @Description: 矩形标绘绘制编辑面板 - 重构版本
 * @Date: 2024-01-20 11:00:00
 * @Author: 项目开发团队
 * @LastEditor: 项目开发团队
 * @LastEditTime: 2024-01-20 15:00:00
-->
<template>
  <div element-loading-background="rgba(0,0,0,0)">
    <el-form
      ref="ruleFormRef"
      :model="formData"
      :rules="rules"
      class="admin-sub-form"
      label-width="auto"
    >
      <!-- 基本信息 -->
      <el-form-item label="标绘类型">
        <base-input disabled class="custom-visform-input" value="矩形" />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <base-input
          class="custom-visform-input"
          v-model="formData.name"
          placeholder="请输入矩形名称"
        />
      </el-form-item>
      <!-- 绘制操作 -->

      <el-form-item label="绘制操作">
        <el-button
          type="primary"
          @click="startDraw"
          :disabled="isDrawing"
          :loading="isDrawing"
        >
          <el-icon><Edit /></el-icon>
          {{ isEditing ? "重新绘制" : hasGeometry ? "重新绘制" : "开始绘制" }}
        </el-button>
      </el-form-item>

      <!-- 描述信息 -->
      <el-form-item label="标绘描述">
        <base-input
          class="custom-visform-input"
          v-model="formData.remark"
          :rows="3"
          placeholder="请输入矩形描述（可选）"
          maxlength="200"
        />
      </el-form-item>

      <!-- 操作按钮 -->
      <div class="flex justify-end">
        <el-button
          type="primary"
          class="primary-btn w-80px"
          @click="onSubmit"
          :loading="submitLoading"
          :disabled="!hasGeometry"
        >
          {{ isEditing ? "更新" : "确定" }}
        </el-button>
        <el-button
          class="w-80px clear-btn"
          @click="onCancel"
          :loading="submitLoading"
        >
          取消
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ElForm } from "element-plus";
import { Edit } from "@element-plus/icons-vue";

import BaseInput from "@/components/input/index.vue";

import { vxRule } from "@/utils/validator";

// 导入基础Panel逻辑
import {
  useBaseDrawPanel,
  type BaseDrawPanelProps,
  type BaseDrawPanelEmits,
} from "./BaseDrawPanel";

// Props和Emits定义
const props = withDefaults(defineProps<BaseDrawPanelProps>(), {
  drawData: null,
  plotService: null,
});

const emit = defineEmits<BaseDrawPanelEmits>();

// 表单引用和验证规则
const ruleFormRef = ref<InstanceType<typeof ElForm>>();
const rules = {
  name: vxRule(),
};

// 使用基础Panel逻辑，矩形功能相对简单，主要使用基础功能
const {
  submitLoading,
  isDrawing,
  formData,
  hasGeometry,
  isEditing,
  startDraw,
  onSubmit,
  onCancel,
} = useBaseDrawPanel(
  {
    featureType: "rectangle",
    featureTypeName: "矩形",
    drawMessage: "请在地图上拖拽绘制矩形",
    successMessage: "矩形绘制完成",
    placeholder: "请输入矩形名称",
    descriptionPlaceholder: "请输入矩形描述（可选）",
  },
  props,
  emit,
  {
    // 自定义提交前验证：检查表单验证
    onBeforeSubmit: async (formData) => {
      if (!ruleFormRef.value) return false;

      try {
        const valid = await ruleFormRef.value.validate();
        return valid;
      } catch (error) {
        console.error("表单验证失败:", error);
        return false;
      }
    },
  }
);
</script>

<style lang="scss" scoped></style>
