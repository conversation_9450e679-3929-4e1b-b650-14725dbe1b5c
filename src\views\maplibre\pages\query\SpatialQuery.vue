<!--
 * @Description: 空间查询组件
 * @Date: 2022-04-22 08:54:28
 * @Author: GISerZ
 * @LastEditor: 项目开发团队
 * @LastEditTime: 2025-01-10 15:00:00
-->
<template>
  <page-card class="spatial-list" title="空间查询" :close-icon="false">
    <el-form>
      <el-form-item class="page-form-item" label="设备类型：">
        <el-select
          v-model="deviceType"
          placeholder="请选择"
          clearable
          @change="handleDeviceTypeChange"
        >
          <el-option
            v-for="item in iotDeviceOptions"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-row :gutter="12" class="filter-row">
        <el-col :span="12">
          <el-form-item class="page-form-item" label="设备名称：">
            <el-input
              v-model="deviceNameFilter"
              placeholder="请输入设备名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="page-form-item" label="使用状态：">
            <el-select
              v-model="deviceStatusFilter"
              placeholder="请选择设备状态"
              clearable
            >
              <el-option
                v-for="item in deviceStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <SpatialQueryButtons
      ref="spatialQueryButtonsRef"
      :map-engine="mapEngine"
      :config="spatialQueryConfig"
      @query-start="handleQueryStart"
      @query-complete="handleQueryComplete"
      @query-error="handleQueryError"
      @draw-start="handleDrawStart"
      @draw-finish="handleDrawFinish"
      :fetchLoading="loading"
    >
      <template #other-buttons>
        <el-button class="clear-btn" @click="handleReset">重置</el-button>
        <el-button class="clear-btn" :loading="isExporting" @click="handleExport">导出</el-button>
      </template>
    </SpatialQueryButtons>

    <!-- 设备数据表格 -->
    <div v-if="showTable" class="table-container">
      <el-table
        :data="paginatedTableData"
        style="width: 100%"
        height="440"
        v-loading="loading"
      >
        <el-table-column label="序号" width="60" align="center">
          <template #default="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="设备编码" show-overflow-tooltip width="180">
          <template #default="scope">
            {{ getFieldValue(scope.row, "device_code") }}
          </template>
        </el-table-column>
        <el-table-column label="设备名称" width="140" show-overflow-tooltip>
          <template #default="scope">
            {{ getFieldValue(scope.row, "device_name") }}
          </template>
        </el-table-column>
        <el-table-column label="设备大类" width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ getFieldDisplayValue(scope.row, "first_device_type_code") }}
          </template>
        </el-table-column>
        <el-table-column label="设备小类" width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ getFieldDisplayValue(scope.row, "second_device_type_code") }}
          </template>
        </el-table-column>
        <el-table-column label="在线状态" show-overflow-tooltip>
          <template #default="scope">
            {{ getFieldValue(scope.row, "online_status") === '1' ? '在线' : '离线' }}
          </template>
        </el-table-column>
        <el-table-column label="使用状态" show-overflow-tooltip>
          <template #default="scope">
            {{ getFieldValue(scope.row, "status") === '1' ? '可用' : '禁用' }}
          </template>
        </el-table-column>
        <el-table-column label="所属厂商" show-overflow-tooltip width="120">
          <template #default="scope">
            {{ getFieldDisplayValue(scope.row, "factory_code") }}
          </template>
        </el-table-column>
        <el-table-column label="阀门口径" show-overflow-tooltip>
          <template #default="scope">
            {{ getFieldValue(scope.row, "valve_diameter_code") }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="经度" width="100" show-overflow-tooltip>
          <template #default="scope">
            {{ getFieldValue(scope.row, "longitude") }}
          </template>
        </el-table-column>
        <el-table-column label="纬度" width="100" show-overflow-tooltip>
          <template #default="scope">
            {{ getFieldValue(scope.row, "latitude") }}
          </template>
        </el-table-column> -->
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="handleViewDevice(scope.row)"
            >
              详情
            </el-button>
            <el-button
              link
              v-if="showEditBtn(scope.row)"
              type="primary"
              size="small"
              @click="handleEditDevice(scope.row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagibox box-border p-x-5">
        <div class="pagitotal">共{{ total }}条数据</div>
        <pagination
          class="admin-pagi"
          :total="total"
          v-model:page="currentPage"
          v-model:limit="pageSize"
          @pagination="handlePagination"
          layout="prev, pager, next, jumper"
        ></pagination>
      </div>
    </div>
  </page-card>

  <!-- 设备表单弹窗 -->
  <DeviceForm
    v-model="deviceFormVisible"
    :is-edit="isEditMode"
    :loading="deviceFormLoading"
    :data="currentDeviceData"
    @submit="handleDeviceFormSubmit"
  />
</template>

<script lang="ts" setup>
import type {
  QueryType,
  QueryResult,
  SpatialQueryConfig
} from '@/components/SpatialQueryButtons.vue';
import { exportFile } from '@/utils/file';
import DeviceForm from './DeviceForm.vue';
import type { MapEngineType } from '@/components/SpatialQueryButtons.vue';
import {
  getFieldValue,
  getFieldDisplayValue,
  fetchDeviceSubTypes,
  spatialQuery,
  processQueryCoordinates,
  filterDevicesByType,
} from '@/utils/deviceUtils';
import { updateDevice, deviceExport } from '@/api/query';
import { useDebounceFn } from '@vueuse/core';

const route = useRoute()

const iotDeviceOptions = ref()

const mapEngine = computed((): MapEngineType => {
  return route.path.includes("cesium") ? "cesium" : "maplibre";
});

const deviceType = ref("");

// 筛选条件
const deviceNameFilter = ref("");
const deviceStatusFilter = ref("");

// 设备状态选项
const deviceStatusOptions = [
  { label: '可用', value: '1' },
  { label: '禁用', value: '0' },
];

// 定义设备数据类型
export interface IotDeviceField {
  fieldKey: string;
  fieldName: string;
  displayForm: string;
  fieldValues: string;
  source: string | null;
  dictMap: Record<string, string> | null;
  isMultiple: number;
  fieldType: number;
}
export interface IotDeviceList {
  code: number;
  data: IotDeviceField[][];
  message: string;
}
// 设备数据响应类型
export interface IotDeviceResponse {
  code: string;
  data: IotDeviceField[][];
  current: number;
  total: number;
  size: number;
  pages: number;
}

// 设备数据引用
const loading = ref(false);

// 原始数据（未筛选）
const originalTableData = ref<IotDeviceField[][]>([]);
// 筛选后的数据
const tableData = ref<IotDeviceField[][]>([]);
const showTable = ref(false);

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 计算属性 - 分页后的表格数据
const paginatedTableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tableData.value.slice(start, end);
});

const showEditBtn = (row: any) => {
  const keys = ['device_first_type_iot', 'device_first_type_trade']
  const firstDeviceTypeCode = getFieldValue(row, 'first_device_type_code')
  console.log(firstDeviceTypeCode, 'firstDeviceTypeCode')
  return keys.includes(firstDeviceTypeCode)
}

/**
 * 应用所有筛选条件
 */
const applyFilters = () => {
  let filteredData = [...originalTableData.value];

  // 设备类型筛选
  if (deviceType.value) {
    filteredData = filterDevicesByType(filteredData, deviceType.value);
  }

  // 设备名称筛选
  if (deviceNameFilter.value.trim()) {
    const nameKeyword = deviceNameFilter.value.trim().toLowerCase();
    filteredData = filteredData.filter(device => {
      const deviceName = getFieldValue(device, "device_name");
      return deviceName && deviceName.toLowerCase().includes(nameKeyword);
    });
  }

  // 设备状态筛选
  if (deviceStatusFilter.value) {
    filteredData = filteredData.filter(device => {
      const deviceStatus = getFieldValue(device, "status");
      return deviceStatus == deviceStatusFilter.value;
    });
  }
  tableData.value = filteredData;
  // debugger
  total.value = filteredData.length;
  currentPage.value = 1; // 重置到第一页
};

// 创建防抖的筛选函数
const debouncedApplyFilters = useDebounceFn(applyFilters, 300);

// 监听筛选条件变化
watch([deviceNameFilter, deviceStatusFilter], () => {
  if (originalTableData.value.length > 0) {
    debouncedApplyFilters();
  }
}, { deep: true });
// 获取设备数据
const fetchIotDevices = async (result: any) => {
  loading.value = true;
  const coordinates = processQueryCoordinates(result);

  try {
    const data: IotDeviceField[][] = await spatialQuery({ coordinate: coordinates });

    // 保存原始数据
    originalTableData.value = data;

    // 应用所有筛选条件
    applyFilters();
    showTable.value = true;
  } catch (err) {
    console.error("获取物联网设备数据出错:", err);
    tableData.value = [];
    total.value = 0;
    showTable.value = false;
  } finally {
    loading.value = false;
  }
};

/**
 * 处理分页事件（前端分页）
 * 由于接口没有分页参数，所有数据已在前端，只需要更新分页状态
 */
const handlePagination = () => {
  // 前端分页不需要重新请求数据
  // 分页数据通过 paginatedTableData 计算属性自动更新
  console.log(`当前页: ${currentPage.value}, 每页条数: ${pageSize.value}`);
};

/**
 * 处理设备类型选择变化事件
 * @param val 选中的设备类型编码
 */
const handleDeviceTypeChange = (val: string) => {

  // 如果有原始数据，重新应用筛选
  if (originalTableData.value.length > 0) {
    applyFilters();
  }
};

// DeviceForm 相关状态
const deviceFormVisible = ref(false);
const isEditMode = ref(false);
const deviceFormLoading = ref(false);
const currentDeviceData = ref<IotDeviceField[]>([]);

/**
 * 处理查看设备详情
 */
const handleViewDevice = (device: IotDeviceField[]) => {
  isEditMode.value = false;
  // 直接传递原始设备数据给表单组件进行动态渲染
  currentDeviceData.value = device;
  deviceFormVisible.value = true;
};

/**
 * 处理编辑设备
 */
const handleEditDevice = (device: IotDeviceField[]) => {
  isEditMode.value = true;
  // 编辑模式也传递原始设备数据
  currentDeviceData.value = device;
  deviceFormVisible.value = true;
};

/**
 * 处理设备表单提交
 */
const handleDeviceFormSubmit = async (submitData: any) => {
  try {
    deviceFormLoading.value = true;

    const { formData, imageData } = submitData;

    // 创建更新后的 deviceFieldList，过滤掉 device_code 和 first_device_type_code
    const updatedDeviceFieldList: IotDeviceField[] = currentDeviceData.value
      .filter(field => field.fieldKey !== 'device_code' && field.fieldKey !== 'first_device_type_code')
      .map(field => {
        // 特殊处理图片字段
        if (field.fieldKey === 'picture' && imageData[field.fieldKey]) {
          const imgData = imageData[field.fieldKey];
          return {
            ...field,
            fieldValues: imgData.code,
            dictMap: {
              [imgData.code]: `${imgData.fileName}::${imgData.url}`
            }
          };
        }

        // 如果 formData 中有对应字段的值，则更新 fieldValues
        if (formData.hasOwnProperty(field.fieldKey)) {
          return {
            ...field,
            fieldValues: formData[field.fieldKey] || ''
          };
        }
        // 否则保持原值
        return field;
      });

    const putData = {
      code: formData.device_code,
      isTemplate: 1,
      firstDeviceTypeCode: formData.first_device_type_code,
      deviceFieldList: updatedDeviceFieldList
    }

    const response = await updateDevice(putData);

    if (response.code === 200) {
      ElMessage.success('修改成功');
    } else {
      throw new Error(response.msg || '修改失败');
    }

    // 重新获取设备列表
    if (currentResult.value) {
      await fetchIotDevices(currentResult.value);
    }

    deviceFormVisible.value = false;
  } catch (error) {
    console.error("保存设备信息失败:", error);
    ElMessage.error("保存失败，请重试");
  } finally {
    deviceFormLoading.value = false;
  }
};

/**
 * @description 空间查询配置
 */
const spatialQueryConfig: Partial<SpatialQueryConfig> = {
  // 沙湾区边界配置
  regionBounds: {
    west: 103.42,
    south: 29.19,
    east: 103.74,
    north: 29.53,
    name: "沙湾区",
  },
  // UI配置
  buttonSize: "default",
  buttonLayout: "horizontal",
  buttonSpacing: 8,
  // 功能配置
  enabledQueries: ["all", "current", "polygon", "rectangle"],
  showTips: true,
  // showResult: true,
  // showResultDetails: true,
  // 绘制配置
  clearPreviousDrawing: true,
  autoSwitchToSelect: true,
};

// 组件状态
const currentResult = ref<QueryResult | null>(null);

// SpatialQueryButtons 组件引用
const spatialQueryButtonsRef = ref();

/**
 * @description 处理查询开始事件
 * @param {QueryType} type - 查询类型
 */
const handleQueryStart = (type: QueryType) => {
  // 清空当前查询结果
  currentResult.value = null;

  // 清空表格数据
  originalTableData.value = [];
  tableData.value = [];
  total.value = 0;
  currentPage.value = 1;

  // 重置设备类型筛选
  // deviceType.value = '';
};

/**
 * @description 处理查询完成事件
 * @param {QueryResult} result - 查询结果
 */
const handleQueryComplete = (result: QueryResult) => {
  currentResult.value = result;
  fetchIotDevices(result);
};

/**
 * @description 处理查询错误事件
 * @param {object} error - 错误信息
 */
const handleQueryError = (error: { type: QueryType; message: string }) => {
  console.error("空间统计查询错误:", error);
};

/**
 * @description 处理绘制开始事件
 * @param {QueryType} type - 绘制类型
 */
const handleDrawStart = (type: QueryType) => {
  console.log(`开始绘制统计区域: ${type}`);
};

/**
 * @description 处理绘制完成事件
 * @param {QueryResult} result - 绘制结果
 */
const handleDrawFinish = (result: QueryResult) => {
  currentResult.value = result;
};

const handleReset = () => {
  // 清除所有数据
  originalTableData.value = [];
  tableData.value = [];
  total.value = 0;
  currentPage.value = 1;
  showTable.value = false;
  deviceType.value = "";

  // 清空筛选条件
  deviceNameFilter.value = "";
  deviceStatusFilter.value = "";

  //清空绘制图形

  if (spatialQueryButtonsRef.value && spatialQueryButtonsRef.value.clearPreviousResults) {
    spatialQueryButtonsRef.value.clearPreviousResults();
  } else {
    console.error("无法访问 SpatialQueryButtons 的 clearPreviousResults 方法");
  }
  if (spatialQueryButtonsRef.value && spatialQueryButtonsRef.value.clearActive) {
    spatialQueryButtonsRef.value.clearActive();
  } else {
    console.error("无法访问 SpatialQueryButtons 的 clearActive 方法");
  }

  currentResult.value = null;
};

const isExporting = ref(false)
const handleExport = () => {
  if (!currentResult.value) {
    ElMessage.warning('请先进行空间查询');
    return;
  }

  if (!originalTableData.value.length) {
    ElMessage.warning('没有数据可导出');
    return; 
  }

  isExporting.value = true
  const coords = currentResult.value?.geometry.coordinates[0] ?? [];
  const coordsStr = coords.join(';');

  deviceExport({ coordinate: coordsStr, field_group: '' }).then((res) => {
    exportFile(res);
  }).catch((err) => {
    console.error('导出失败:', err);
    ElMessage.error('导出失败');
  }).finally(() => {
    isExporting.value = false;
  });
}

onMounted(async () => {
  iotDeviceOptions.value = await fetchDeviceSubTypes({ parentCode: 'iot_device_type', size: -1 });
  iotDeviceOptions.value.push({
    name: '智能表',
    code: 'zhinengbiao',
  });
});
</script>

<style lang="scss" scoped>
.spatial-list {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 694px;
}

.table-container {
  margin-top: 16px;
  background-color: #fff;
  border-radius: 4px;
  // padding: 12px;
  max-height: 500px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: end;

  :deep(.el-pagination) {
    --el-pagination-font-size: 13px;
    --el-pagination-button-width: 28px;
    --el-pagination-button-height: 28px;
  }
}
:deep(.pagibox) {
  height: 60px;
}

.filter-row {
  margin-left: 0 !important;
  margin-right: 0 !important;

  .el-col {
    padding-left: 6px !important;
    padding-right: 6px !important;
  }

  .el-col:first-child {
    padding-left: 0 !important;
  }

  .el-col:last-child {
    padding-right: 0 !important;
  }
}
</style>
