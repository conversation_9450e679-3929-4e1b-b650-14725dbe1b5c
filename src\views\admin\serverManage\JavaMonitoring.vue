<template>
  <div class="bg-#eef0f4 h-full p-2.5 box-border" v-loading="javaLoading">
    <el-scrollbar>
      <div class="java-top">
        <div class="db-left">
          <div class="img-top">
            <img :src="getImages('admin/cpu.png')" class="imgs" alt="" />
            <div class="title">CPU</div>
          </div>

          <!-- <div class="line"></div> -->
          <div class="db-table p-d">
            <el-table
              :data="cpuData"
              class="routeCt"
              :row-class-name="tableRowClassName"
              style="width: 100%"
              height="100%"
            >
              <el-table-column type="index" label="序号" width="200" />
              <el-table-column prop="name" label="属性">
                <template v-slot="scope">
                  <div v-if="scope.row.name == 'cpuNum'">核心</div>
                  <div v-if="scope.row.name == 'used'">CPU用户使用</div>
                  <div v-if="scope.row.name == 'sys'">CPU系统使用</div>
                  <div v-if="scope.row.name == 'free'">CPU当前空闲</div>
                </template>
              </el-table-column>
              <el-table-column prop="value" label="值" />
            </el-table>
          </div>
        </div>
        <div class="db-right">
          <div class="img-top">
            <img :src="getImages('admin/sys.png')" class="sys" alt="" />
            <div class="title">内存</div>
          </div>
          <!-- <div class="line"></div> -->
          <div class="db-table p-d">
            <el-table
              :data="sysData"
              class="routeCt"
              :row-class-name="tableRowClassName"
              style="width: 100%"
              height="100%"
            >
              <el-table-column type="index" label="序号" width="100" />
              <el-table-column prop="name" label="属性">
                <template v-slot="scope">
                  <div v-if="scope.row.name == 'total'">总内存</div>
                  <div v-if="scope.row.name == 'used'">已使用</div>
                  <div v-if="scope.row.name == 'free'">剩余内存</div>
                  <div v-if="scope.row.name == 'ramUS'">使用率</div>
                </template>
              </el-table-column>
              <el-table-column prop="value" label="内存" />
              <el-table-column prop="jvm" label="缓存" />
            </el-table>
          </div>
        </div>
      </div>
      <div class="serve p-d">
        <div class="img-top">
          <img :src="getImages('admin/give.png')" class="give" alt="" />
          <div class="title">服务器信息</div>
        </div>
        <el-descriptions class="custom-des" :column="2" border>
          <el-descriptions-item
            label-class-name="des-label"
            class-name="des-content"
            label="服务器名称"
          >
            {{ giveInfo?.computerName }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="des-label"
            class-name="des-content"
            label="操作系统"
          >
            {{ giveInfo?.osName }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="des-label even"
            class-name="des-content even"
            label="服务器IP"
          >
            {{ giveInfo?.computerIp }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="des-label even"
            class-name="des-content even"
            label="系统架构"
          >
            {{ giveInfo?.osArch }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="serve p-d">
        <div class="img-top">
          <img :src="getImages('admin/java.png')" class="java" alt="" />
          <div class="title">Java虚拟机信息</div>
        </div>
        <el-descriptions class="custom-des" :column="2" border>
          <el-descriptions-item
            label-class-name="des-label"
            class-name="des-content"
            label="java名称"
          >
            {{ javaInfo?.vmName }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="des-label"
            class-name="des-content"
            label="java版本"
          >
            {{ javaInfo?.javaVersion }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="des-label even"
            class-name="des-content even"
            label="启动时间"
          >
            {{ javaInfo?.startTime }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="des-label even"
            class-name="des-content even"
            label="运行时长"
          >
            {{ javaInfo?.runTime }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="des-label"
            class-name="des-content-remark"
            :span="4"
            label="安装路径"
          >
            {{ javaInfo?.installPath }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="des-label even"
            class-name="des-content-remark even"
            :span="4"
            label="项目路径"
          >
            {{ javaInfo?.projectPath }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="des-label"
            class-name="des-content-remark"
            :span="4"
            label="运行参数"
          >
            {{
              javaInfo?.runParameter === null ? "无" : javaInfo?.runParameter
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="java-btm">
        <div class="img-top">
          <img :src="getImages('admin/disk.png')" class="disk" alt="" />
          <div class="title">磁盘状态</div>
        </div>
        <!-- <div class="line"></div> -->
        <div class="disk-table p-d">
          <el-table
            :data="diskData"
            class="routeCt"
            :row-class-name="tableRowClassName"
            style="width: 100%"
            height="100%"
          >
            <el-table-column type="index" label="序号" width="100" />
            <el-table-column prop="dirName" label="盘符路径" />
            <el-table-column prop="sysTypeName" label="文件系统" />
            <el-table-column prop="typeName" label="盘符类型" />
            <el-table-column prop="total" label="总大小" />
            <el-table-column prop="free" label="可用大小" />
            <el-table-column prop="used" label="已用大小" />
            <el-table-column prop="usage" label="已用百分比" />
          </el-table>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script lang="ts" setup>
import { serveMonitor } from "@/api/server";
import { getImages } from "@/utils/getImages";
const route = useRoute();
const cpuData: any = ref([]);
const sysData: any = ref([]);
const arr: any = ref([]);
const arrs: any = ref([]);
const javaLoading = ref<boolean>(false);
const giveInfo = ref();
const javaInfo = ref();
const diskData = ref([]);
const getList = async () => {
  try {
    javaLoading.value = true;
    const result = await serveMonitor();
    // CPU
    await Object.keys(result.data.cpu).forEach((key) => {
      if (key != "total") {
        cpuData.value.push({
          name: key,
          value:
            key == "cpuNum" ? result.data.cpu[key] : result.data.cpu[key] + "%",
        });
      }
    });
    // 内存
    await Object.keys(result.data.jvm).forEach((key) => {
      if (key != "home" && key != "version") {
        arr.value.push({
          jvm:
            key == "usageRate"
              ? result.data.jvm[key] + "%"
              : result.data.jvm[key] + "M",
        });
      }
    });
    await Object.keys(result.data.mem).forEach((key) => {
      arrs.value.push({
        name: key,
        value:
          key == "ramUS"
            ? result.data.mem[key] + "%"
            : result.data.mem[key] + "G",
      });
    });
    let a = arrs.value.map((v: any, index: number) => {
      return { ...v, ...arr.value[index] };
    });
    sysData.value = a;
    // 服务信息
    giveInfo.value = result.data.sys;
    // Java虚拟机信息
    javaInfo.value = result.data.sysVm;
    // 磁盘状态
    result.data.sysFiles.forEach((e: any) => {
      if (e.usage) {
        e.usage = e.usage + "%";
      }
    });
    diskData.value = result.data.sysFiles;
  } finally {
    javaLoading.value = false;
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
onMounted(async () => {
  await getList();
});
</script>
<style lang="scss" scoped>
@mixin base-dbbox {
  width: 49.5%;
  // height: calc(100vh - 710px);
  background-color: #fff;
  padding-bottom: 20px;
}
.title {
  font-size: 18px;
  font-family: Source Han Sans CN, Source Han Sans CN-Bold;
  font-weight: 700;
  color: #202020;
  padding: 10px 0 10px 10px;
}
.line {
  border-top: 2px solid #e9eff3;
}
.db-left {
  @include base-dbbox();
  // box-sizing: border-box;
  // padding-right: 10px;
}
.db-right {
  @include base-dbbox();
}
.java-top {
  display: flex;
  justify-content: space-between;
}
.serve {
  width: 100%;
  background-color: #fff;
  margin: 10px 0;
  padding-bottom: 20px;
}
.java-btm {
  width: 100%;
  // height: calc(100vh - 880px);
  background-color: #fff;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 300px;
  background: transparent;
}
:deep(
    .el-descriptions__body
      .el-descriptions__table.is-bordered
      .el-descriptions__cell
  ) {
  // border-left: none;
  // border-right: none;
  border: none;
}
.db-table {
  height: 250px;
  // height: calc(100vh - 770px);
}
.disk-table {
  // height: calc(100vh - 820px);
  height: 180px;
}
.img-top {
  display: flex;
  align-items: center;
  padding-left: 20px;
}
.imgs {
  width: 19px;
  height: 19px;
}
.sys {
  width: 17px;
  height: 15px;
}
.give {
  width: 19px;
  height: 17px;
}
.java {
  width: 21px;
  height: 21px;
}
.disk {
  width: 19px;
  height: 18px;
}
.p-d {
  padding: 0 20px 20px 20px;
  box-sizing: border-box;
}
:deep(.even) {
  background-color: #f7faff !important;
}
:deep(.el-table__header) {
  height: 46px !important;
}
:deep(.el-table__row) {
  height: 46px !important;
}
</style>
