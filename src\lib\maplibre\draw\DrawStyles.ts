/**
 * @fileoverview MapLibre 绘制工具样式配置
 * @description 提供绘制工具的默认样式配置和样式管理功能
 * <AUTHOR>
 * @version 1.0.0
 */

import type { DrawStyleConfig } from './types';

/**
 * @description 默认绘制样式配置
 */
export const DEFAULT_DRAW_STYLES: Required<DrawStyleConfig> = {
  point: {
    color: '#3388ff',
    outlineColor: '#ffffff',
    outlineWidth: 2,
    radius: 6
  },
  linestring: {
    color: '#3388ff',
    width: 3,
    opacity: 0.8
  },
  polygon: {
    fillColor: '#3388ff',
    fillOpacity: 0.2,
    outlineColor: '#3388ff',
    outlineWidth: 2
  },
  selected: {
    color: '#ff6b35',
    outlineColor: '#ffffff',
    outlineWidth: 3
  }
};

/**
 * @description Terra Draw 样式配置生成器
 */
export class DrawStyleManager {
  private styles: Required<DrawStyleConfig>;

  /**
   * @constructor
   * @param customStyles - 自定义样式配置
   */
  constructor(customStyles?: Partial<DrawStyleConfig>) {
    this.styles = this.mergeStyles(DEFAULT_DRAW_STYLES, customStyles);
  }

  /**
   * @description 合并样式配置
   * @param defaultStyles - 默认样式
   * @param customStyles - 自定义样式
   * @returns 合并后的样式配置
   * @private
   */
  private mergeStyles(
    defaultStyles: Required<DrawStyleConfig>,
    customStyles?: Partial<DrawStyleConfig>
  ): Required<DrawStyleConfig> {
    if (!customStyles) return { ...defaultStyles };

    return {
      point: { ...defaultStyles.point, ...customStyles.point },
      linestring: { ...defaultStyles.linestring, ...customStyles.linestring },
      polygon: { ...defaultStyles.polygon, ...customStyles.polygon },
      selected: { ...defaultStyles.selected, ...customStyles.selected }
    };
  }

  /**
   * @description 获取点绘制模式的样式配置
   * @returns Terra Draw 点模式样式配置
   */
  getPointModeStyles() {
    const { point, selected } = this.styles;
    const radius = point.radius || 6;
    return {
      pointColor: point.color,
      pointOutlineColor: point.outlineColor,
      pointOutlineWidth: point.outlineWidth,
      pointWidth: radius * 2,
      selectedPointColor: selected.color,
      selectedPointOutlineColor: selected.outlineColor,
      selectedPointOutlineWidth: selected.outlineWidth
    };
  }

  /**
   * @description 获取线段绘制模式的样式配置
   * @returns Terra Draw 线段模式样式配置
   */
  getLineStringModeStyles() {
    const { linestring, point, selected } = this.styles;
    const radius = point.radius || 6;
    return {
      lineStringColor: linestring.color,
      lineStringWidth: linestring.width,
      closingPointColor: point.color,
      closingPointOutlineColor: point.outlineColor,
      closingPointOutlineWidth: point.outlineWidth,
      closingPointWidth: radius * 2,
      selectedLineStringColor: selected.color,
      selectedLineStringWidth: selected.outlineWidth
    };
  }

  /**
   * @description 获取多边形绘制模式的样式配置
   * @returns Terra Draw 多边形模式样式配置
   */
  getPolygonModeStyles() {
    const { polygon, point, selected } = this.styles;
    const radius = point.radius || 6;
    return {
      fillColor: polygon.fillColor,
      fillOpacity: polygon.fillOpacity,
      outlineColor: polygon.outlineColor,
      outlineWidth: polygon.outlineWidth,
      closingPointColor: point.color,
      closingPointOutlineColor: point.outlineColor,
      closingPointOutlineWidth: point.outlineWidth,
      closingPointWidth: radius * 2,
      selectedFillColor: selected.color,
      selectedFillOpacity: 0.3,
      selectedOutlineColor: selected.color,
      selectedOutlineWidth: selected.outlineWidth
    };
  }

  /**
   * @description 获取矩形绘制模式的样式配置
   * @returns Terra Draw 矩形模式样式配置
   */
  getRectangleModeStyles() {
    const { polygon, selected } = this.styles;
    return {
      fillColor: polygon.fillColor,
      fillOpacity: polygon.fillOpacity,
      outlineColor: polygon.outlineColor,
      outlineWidth: polygon.outlineWidth,
      selectedFillColor: selected.color,
      selectedFillOpacity: 0.3,
      selectedOutlineColor: selected.color,
      selectedOutlineWidth: selected.outlineWidth
    };
  }

  /**
   * @description 获取圆形绘制模式的样式配置
   * @returns Terra Draw 圆形模式样式配置
   */
  getCircleModeStyles() {
    const { polygon, point, selected } = this.styles;
    const radius = point.radius || 6;
    return {
      fillColor: polygon.fillColor,
      fillOpacity: polygon.fillOpacity,
      outlineColor: polygon.outlineColor,
      outlineWidth: polygon.outlineWidth,
      centerPointColor: point.color,
      centerPointOutlineColor: point.outlineColor,
      centerPointOutlineWidth: point.outlineWidth,
      centerPointWidth: radius * 2,
      selectedFillColor: selected.color,
      selectedFillOpacity: 0.3,
      selectedOutlineColor: selected.color,
      selectedOutlineWidth: selected.outlineWidth
    };
  }

  /**
   * @description 获取选择模式的样式配置
   * @returns Terra Draw 选择模式样式配置
   */
  getSelectModeStyles() {
    const { selected, point } = this.styles;
    const radius = point.radius || 6;
    return {
      selectedPointColor: selected.color,
      selectedPointOutlineColor: selected.outlineColor,
      selectedPointOutlineWidth: selected.outlineWidth,
      selectedPointWidth: radius * 2,
      midPointColor: point.color,
      midPointOutlineColor: point.outlineColor,
      midPointOutlineWidth: point.outlineWidth,
      midPointWidth: radius * 1.5,
      selectedLineStringColor: selected.color,
      selectedLineStringWidth: selected.outlineWidth,
      selectedPolygonColor: selected.color,
      selectedPolygonFillOpacity: 0.3,
      selectedPolygonOutlineColor: selected.color,
      selectedPolygonOutlineWidth: selected.outlineWidth
    };
  }

  /**
   * @description 获取自由绘制模式的样式配置
   * @returns Terra Draw 自由绘制模式样式配置
   */
  getFreehandModeStyles() {
    const { linestring, selected } = this.styles;
    return {
      lineStringColor: linestring.color,
      lineStringWidth: linestring.width,
      selectedLineStringColor: selected.color,
      selectedLineStringWidth: selected.outlineWidth
    };
  }

  /**
   * @description 更新样式配置
   * @param newStyles - 新的样式配置
   */
  updateStyles(newStyles: Partial<DrawStyleConfig>): void {
    this.styles = this.mergeStyles(this.styles, newStyles);
  }

  /**
   * @description 获取当前样式配置
   * @returns 当前样式配置
   */
  getStyles(): Required<DrawStyleConfig> {
    return { ...this.styles };
  }

  /**
   * @description 重置为默认样式
   */
  resetToDefault(): void {
    this.styles = { ...DEFAULT_DRAW_STYLES };
  }
} 