import hRequest from '@/utils/http'
import type { DataType } from '@/utils/http/types'
export const login = (data: any) => {
  return hRequest.post<DataType>({
    url: '/auth/login',
    data: data
  })
}
// 下载文件
export const downloadFile = (data: any) => {
  return hRequest.get<any>({
    url: '/common/file',
    responseType: 'blob',
     params: data
  });
};

//根据token鉴权
export const authToken = (token: string) => {
  return hRequest.get<DataType>({
    url: '/sys/user/auth',
    
  })
}
