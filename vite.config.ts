import { defineConfig } from 'vite';
import { fileURLToPath, URL } from 'node:url';
import vue from '@vitejs/plugin-vue';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import { viteVConsole } from 'vite-plugin-vconsole';
import path from 'path';
import UnoCSS from 'unocss/vite';
import fs from 'fs';

//element重载问题
const optimizeDepsElementPlusIncludes = ['element-plus/es'];
fs.readdirSync('node_modules/element-plus/es/components').map((dirname) => {
  fs.access(
    `node_modules/element-plus/es/components/${dirname}/style/css.mjs`,
    (err) => {
      if (!err) {
        optimizeDepsElementPlusIncludes.push(
          `element-plus/es/components/${dirname}/style/css`
        );
      }
    }
  );
});

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    UnoCSS({
      mode: 'vue-scoped', // inject <style scoped>
    }),
    viteStaticCopy({
      targets: [
        {
          src: 'node_modules/bcgis-sdk/dist/resources',
          dest: 'libs/bcgis-sdk'
        }
      ]
    }),
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia'],
      dts: 'auto-import.d.ts',
      dirs: ['./src/stores/*', './src/composables/*'],
      resolvers: [ElementPlusResolver({
        importStyle: 'sass'
      })],
      eslintrc: {
        enabled: false, // <-- this
        filepath: './.eslintrc-auto-import.json'
      }
    }),
    Components({
      dts: 'components.d.ts',
      types: [
        {
          from: 'vue-router',
          names: ['RouterLink', 'RouterView']
        }
      ],
      // 默认导出 src/components 下的组件
      dirs: ['src/components'],
      extensions: ['vue', 'tsx'],
      resolvers: [
        // 使用element图标：icon-ep-...   使用iconify图标：icon-...
        IconsResolver({
          prefix: 'icon',
          enabledCollections: ['ep', 'solar']
        }),
        ElementPlusResolver({
          importStyle: 'sass'
        })
      ]
    }),
    Icons({
      compiler: 'vue3',
      autoInstall: true
    }),
    createSvgIconsPlugin({
      // 指定需要缓存的图标文件夹
      iconDirs: [path.resolve(process.cwd(), 'src/assets/icon-svg/')],
      // 指定symbolId格式
      symbolId: 'icon-svg-[name]'
    }),
    viteVConsole({
      entry: path.resolve(__dirname, './src/main.ts'), // 入口文件
      localEnabled: false, // serve开发环境下
      enabled: false,
      // enabled: command !== 'serve' || mode === 'test', // 打包环境下/发布测试包,
      config: {
        // vconsole 配置项
        maxLogNumber: 1000,
        theme: 'light'
      }
    })
  ],
  base: process.env.NODE_ENV == 'development' ? './' : './',
  define: {
    CESIUM_BASE_URL: JSON.stringify(''),
  },
  optimizeDeps: {
    include: optimizeDepsElementPlusIncludes,
    esbuildOptions: {
      target: 'esnext'
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @use "@/assets/css/element.variable.scss" as *;
        `,
      },
    },
  },
  server: {
    host: true,
    cors: true,
    hmr: {
      overlay: false
    },
    proxy: {
      //物联网接口
      '/api/v1': {
        // target: 'http://************:7835/iot/api/v1',
        target: 'https://www.lshywater.cn/iot/api/v1',
        // target: 'https://hydrology.zhiyou-tec.com/iot/api/v1',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/v1/, '')
      },
      //漏水事件接口
      '/lltech/api': {
        target: 'http://************:7080/revenue/api',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/lltech\/v1/, '')
      },
      //漏水通知接口
      '/revenue/api': {
        target: 'http://************:7088/revenue/api',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/revenue\/v1/, '')
      },
      //系统后台接口
      '/api': {
        // target: 'http://************:9111/api',
        target: 'http://*************:8078/api',
        // target: 'https://gis.lshywater.cn/api',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      //天气接口
      '/weather/api': {
        target: 'http://t.weather.itboy.net/api',
        // target: 'http://gfeljm.tianqiapi.com/api',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/weather\/api/, '')
      },
      '/bcserver': {
        // target: 'http://192.168.9.12:8089/geoserver',
        target: 'http://*************:8077',
        changeOrigin: true,
          rewrite: (path) => path.replace(/^\/bcserver/, '')
      }
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  // 打包配置
  build: {
    // sourcemap: command === 'build' ? false : 'inline',
    outDir: 'dist',
    target: 'esnext',
    // 将js、css文件分离到单独文件夹
    rollupOptions: {
      output: {
        chunkFileNames: 'static/js/[name]-[hash].js',
        entryFileNames: 'static/js/[name]-[hash].js',
        assetFileNames: 'static/[ext]/[name]-[hash].[ext]'
      }
    }
  }
});
