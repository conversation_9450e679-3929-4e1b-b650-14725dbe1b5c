/**
 * @fileoverview MapLibre图层系统统一导出
 * @description 提供图层管理系统的完整API接口
 * <AUTHOR>
 * @version 2.0.0
 */

// 导入类型和类（用于函数参数类型和默认导出）
import { LayerManager } from './LayerManager'
import { BaseLayer } from './layers/BaseLayer'
import { LayerGroup } from './layers/LayerGroup'
import { LayerFactory } from './layers/LayerFactory'
import { ImageryLayer } from './layers/ImageryLayer'
import { TdtImageryLayer } from './layers/TdtImageryLayer'
import { CustomXYZLayer } from './layers/CustomXYZLayer'
import { MvtLayer } from './layers/MvtLayer'
import { BaiduMapLayer } from './layers/BaiduMapLayer'
import { AmapLayer } from './layers/AmapLayer'
import { 
  LayerType,
  LayerStatus,
  CoordinateSystem,
  type LayerConfig,
  type LayerManagerConfig,
  type LayerValidationResult
} from './types/LayerTypes'

// 核心类导出
export { LayerManager } from './LayerManager'
export { BaseLayer } from './layers/BaseLayer'
export { LayerGroup } from './layers/LayerGroup'
export { LayerFactory } from './layers/LayerFactory'

// 具体图层类导出
export { ImageryLayer } from './layers/ImageryLayer'
export { TdtImageryLayer } from './layers/TdtImageryLayer'
export { CustomXYZLayer } from './layers/CustomXYZLayer'
export { MvtLayer } from './layers/MvtLayer'
export { BaiduMapLayer } from './layers/BaiduMapLayer'
export { AmapLayer } from './layers/AmapLayer'

// 类型定义导出
export {
  LayerType,
  LayerStatus,
  CoordinateSystem,
  type LayerConfig,
  type BaseLayerConfig,
  type RasterLayerConfig,
  type VectorLayerConfig,
  type GroupLayerConfig,
  type TdtLayerConfig,
  type BaiduLayerConfig,
  type AmapLayerConfig,
  type LayerGroupConfig,
  type ImageryLayerConfig,
  type CustomXYZLayerConfig,
  type MvtLayerConfig,
  type LayerEventData,
  type LayerEventHandler,
  type LayerLoadOptions,
  type LayerPerformanceMetrics,
  type LayerDependency,
  type LayerValidationResult,
  type LayerTreeNode,
  type LayerManagerConfig
} from './types/LayerTypes'

// 兼容性导出
export { type LayerItem } from './type/LayerItem'

/**
 * @description 创建图层管理器实例
 * @param config - 图层管理器配置
 * @returns 图层管理器实例
 */
export function createLayerManager(config: LayerManagerConfig): LayerManager {
  return new LayerManager(config)
}

/**
 * @description 创建图层实例
 * @param config - 图层配置
 * @returns 图层实例
 */
export function createLayer(config: LayerConfig): BaseLayer {
  return LayerFactory.createLayer(config)
}

/**
 * @description 批量创建图层
 * @param configs - 图层配置数组
 * @returns 图层实例数组
 */
export function createLayers(configs: LayerConfig[]): BaseLayer[] {
  return LayerFactory.createLayers(configs)
}

/**
 * @description 验证图层配置
 * @param config - 图层配置
 * @returns 验证结果
 */
export function validateLayerConfig(config: LayerConfig): LayerValidationResult {
  return LayerFactory.validateLayerConfig(config)
}

/**
 * @description 获取图层类型的默认配置
 * @param layerType - 图层类型
 * @returns 默认配置
 */
export function getDefaultLayerConfig(layerType: LayerType | string): Partial<LayerConfig> {
  return LayerFactory.getDefaultConfig(layerType)
}

/**
 * @description 获取支持的图层类型列表
 * @returns 图层类型数组
 */
export function getSupportedLayerTypes(): string[] {
  return LayerFactory.getSupportedLayerTypes()
}

// 默认导出
export default {
  LayerManager,
  LayerFactory,
  BaseLayer,
  LayerGroup,
  ImageryLayer,
  TdtImageryLayer,
  CustomXYZLayer,
  MvtLayer,
  BaiduMapLayer,
  AmapLayer,
  LayerType,
  LayerStatus,
  CoordinateSystem,
  createLayerManager,
  createLayer,
  createLayers,
  validateLayerConfig,
  getDefaultLayerConfig,
  getSupportedLayerTypes
} 