/**
 * @fileoverview MapLibre地图坐标拾取适配器
 * @description 基于MapLibre地图引擎实现坐标拾取功能
 * <AUTHOR>
 * @version 2.0.0
 */

import { Map as MapLibreMap, Marker, LngLat } from 'maplibre-gl';
import { CoordinatePickerBase, type CoordinatePoint, type CoordinateResult, type PickerOptions } from './CoordinatePickerBase';
import { CoordinateTransform } from '@/utils/coordinate/CoordinateTransform';

/**
 * @class MaplibreCoordinatePicker
 * @description MapLibre地图坐标拾取适配器，实现二维地图的坐标拾取功能
 * @extends CoordinatePickerBase
 */
export class MaplibreCoordinatePicker extends CoordinatePickerBase {
  /** MapLibre地图实例 */
  private _map: MapLibreMap;
  
  /** 拾取标记实例 */
  private _marker?: Marker;
  
  /** 点击事件处理器 */
  private _clickHandler?: (e: any) => void;
  
  /**
   * 构造函数
   * @param map - MapLibre地图实例
   * @param options - 拾取配置选项
   * @throws {Error} 如果地图实例无效
   */
  constructor(map: MapLibreMap, options: PickerOptions = {}) {
    super(options);
    
    if (!map) {
      throw new Error('MapLibre地图实例不能为空');
    }
    
    this._map = map;
  }
  
  /**
   * 启动拾取处理
   * 创建点击事件处理器，设置鼠标样式
   * @protected
   * @override
   */
  protected onStart(): void {
    try {
      // 创建点击事件处理器
      this._clickHandler = (e: any) => {
        try {
          if (!e || !e.lngLat) {
            throw new Error('无效的点击事件数据');
          }
          
          const coord: CoordinatePoint = {
            lng: e.lngLat.lng,
            lat: e.lngLat.lat
          };
          
          // 显示标记
          if (this._options.showMarker) {
            this.showMarker(coord);
          }
          
          // 处理坐标
          const result = this.processCoordinate(coord, {
            x: e.point.x,
            y: e.point.y
          });
          
          // 触发拾取成功事件
          this.handlePickSuccess(result);
        } catch (error) {
          this.handlePickError(error as Error);
        }
      };
      
      // 添加点击事件监听
      this._map.on('click', this._clickHandler);
      
      // 修改鼠标样式为十字准星，提示用户可以点击
      this._map.getCanvas().style.cursor = 'crosshair';
    } catch (error) {
      console.error('启动MapLibre坐标拾取失败:', error);
      this.handlePickError(error as Error);
    }
  }
  
  /**
   * 停止拾取处理
   * 移除点击事件监听，恢复鼠标样式
   * @protected
   * @override
   */
  protected onStop(): void {
    try {
      // 移除点击事件监听
      if (this._clickHandler) {
        this._map.off('click', this._clickHandler);
        this._clickHandler = undefined;
      }
      
      // 恢复默认鼠标样式
      this._map.getCanvas().style.cursor = '';
      
      // 如果不需要保留标记，则清除
      if (!this._options.showMarker) {
        this.clearMarker();
      }
    } catch (error) {
      console.error('停止MapLibre坐标拾取失败:', error);
      // 这里不抛出错误，因为即使清理失败也应该继续进行其他清理操作
    }
  }
  
  /**
   * 配置更新处理
   * 当选项更新时，更新标记样式
   * @protected
   * @override
   */
  protected onOptionsUpdate(): void {
    try {
      // 更新标记样式
      if (this._marker && this._options.showMarker) {
        // 获取当前标记位置
        const position = this._marker.getLngLat();
        
        // 清除并重新创建标记，应用新样式
        this.clearMarker();
        this.showMarker({ 
          lng: position.lng, 
          lat: position.lat 
        });
      } else if (!this._options.showMarker) {
        // 如果关闭了标记显示，清除标记
        this.clearMarker();
      }
    } catch (error) {
      console.error('更新MapLibre坐标拾取选项失败:', error);
    }
  }
  
  /**
   * 销毁拾取器
   * 清理所有资源，使拾取器不可用
   * @override
   */
  public destroy(): void {
    try {
      // 停止拾取
      this.stop();
      
      // 清除标记
      this.clearMarker();
      
      // 清空引用
      this._clickHandler = undefined;
    } catch (error) {
      console.error('销毁MapLibre坐标拾取器失败:', error);
    }
  }
  
  /**
   * 获取地图中心点坐标
   * @returns 地图中心点坐标
   * @override
   * @throws {Error} 如果获取中心点失败
   */
  public getMapCenter(): CoordinatePoint {
    try {
      if (!this._map) {
        throw new Error('地图实例无效');
      }
      
      const center = this._map.getCenter();
      return {
        lng: center.lng,
        lat: center.lat
      };
    } catch (error) {
      console.error('获取MapLibre地图中心点失败:', error);
      throw error;
    }
  }
  
  /**
   * 飞行到指定坐标
   * 控制地图视图平滑飞行到指定坐标
   * @param coord - 目标坐标
   * @param duration - 飞行时长（毫秒），默认为1000毫秒
   * @returns Promise，飞行完成时解析
   * @override
   * @throws {Error} 如果飞行操作失败
   */
  public async flyTo(coord: CoordinatePoint, duration: number = 1000): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        if (!this._map) {
          throw new Error('地图实例无效');
        }
        
        if (!coord || typeof coord.lng !== 'number' || typeof coord.lat !== 'number') {
          throw new Error('无效的坐标');
        }
        
        // MapLibre的flyTo使用秒作为时间单位
        const durationInSeconds = duration / 1000;
        
        this._map.flyTo({
          center: [coord.lng, coord.lat],
          duration: durationInSeconds,
          zoom: this._map.getZoom() // 保持当前缩放级别
        });
        
        // 动画结束后触发解析
        setTimeout(() => {
          resolve();
        }, duration);
      } catch (error) {
        console.error('MapLibre飞行到指定坐标失败:', error);
        reject(error);
      }
    });
  }
  
  /**
   * 显示拾取标记
   * 在指定坐标位置创建并显示标记
   * @param coord - 坐标点
   * @private
   */
  private showMarker(coord: CoordinatePoint): void {
    try {
      // 清除现有标记
      this.clearMarker();
      
      // 创建标记DOM元素
      const el = document.createElement('div');
      el.className = 'coordinate-picker-marker';
      
      // 设置标记样式
      const size = this._options.markerStyle?.size || 8;
      const color = this._options.markerStyle?.color || '#ff0000';
      const opacity = this._options.markerStyle?.opacity || 0.8;
      
      // 应用样式
      Object.assign(el.style, {
        width: `${size}px`,
        height: `${size}px`,
        borderRadius: '50%',
        backgroundColor: color,
        opacity: `${opacity}`,
        border: '2px solid white',
        boxShadow: '0 0 2px rgba(0, 0, 0, 0.5)'
      });
      
      // 创建并添加标记
      this._marker = new Marker({ 
        element: el,
        anchor: 'center' // 居中对齐
      })
        .setLngLat(new LngLat(coord.lng, coord.lat))
        .addTo(this._map);
    } catch (error) {
      console.error('显示MapLibre坐标拾取标记失败:', error);
      // 标记显示失败不应影响主要功能
    }
  }
  
  /**
   * 清除拾取标记
   * 移除地图上的标记
   * @private
   */
  private clearMarker(): void {
    try {
      if (this._marker) {
        this._marker.remove();
        this._marker = undefined;
      }
    } catch (error) {
      console.error('清除MapLibre坐标拾取标记失败:', error);
      // 清除失败不抛出异常，继续执行
    }
  }
  
  /**
   * 处理坐标
   * 将原始坐标转换为多种坐标系的坐标
   * @param originalCoord - 原始坐标
   * @param screenCoord - 屏幕坐标
   * @returns 坐标处理结果，包含多种坐标系的值
   * @private
   */
  private processCoordinate(
    originalCoord: CoordinatePoint,
    screenCoord?: { x: number; y: number }
  ): CoordinateResult {
    try {
      // 格式化原始坐标精度
      const formattedCoord = this.formatCoordinatePrecision(originalCoord);
      
      // 创建基础结果
      const result: CoordinateResult = {
        originalCoord: formattedCoord,
        wgs84: formattedCoord, // MapLibre默认使用WGS84
        gcj02: CoordinateTransform.wgs84ToGcj02(formattedCoord),
        bd09: CoordinateTransform.wgs84ToBd09(formattedCoord),
        timestamp: Date.now(),
        screenCoord
      };
      
      // 如果需要，添加投影坐标
      if (this._options.includeProjection) {
        // 计算EPSG:3857墨卡托投影坐标
        const x = formattedCoord.lng * Math.PI * 6378137.0 / 180.0;
        let y = Math.log(Math.tan((90.0 + formattedCoord.lat) * Math.PI / 360.0)) * 6378137.0;
        
        // 处理极端情况
        if (!Number.isFinite(y)) {
          y = 0;
        }
        
        result.projectedCoord = {
          epsg3857: {
            lng: Number(x.toFixed(2)),
            lat: Number(y.toFixed(2)),
            alt: formattedCoord.alt
          }
        };
      }
      
      return result;
    } catch (error) {
      console.error('处理MapLibre坐标失败:', error);
      throw new Error(`坐标处理失败: ${(error as Error).message}`);
    }
  }
}
