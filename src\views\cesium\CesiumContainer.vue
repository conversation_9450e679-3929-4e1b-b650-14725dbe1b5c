<!--
  @fileoverview Cesium三维地球容器组件
  @description 负责创建和管理Cesium三维地球场景，配置WebGL上下文和地形深度测试
  <AUTHOR>
  @version 2.0.0 - 增强生命周期管理和引擎切换支持
-->

<template>
  <!-- Cesium三维场景容器DOM元素 -->
  <div
    ref="cesiumContainer"
    id="cesium-container"
    class="assets-cesium-container"
  ></div>

  <!-- 鼠标坐标显示组件 -->
  <CesiumCoordinateBar />
</template>

<script setup lang="ts">
import { AppCesium } from '@/lib/cesium/AppCesium';
import { ref, onMounted, onUnmounted, onBeforeUnmount } from 'vue';
import { getEngineSwitch } from '@/utils/EngineSwitch';
import CesiumCoordinateBar from '@/components/mouseCoordinate/CesiumCoordinateBar.vue';

/** @description Cesium容器DOM引用 */
const cesiumContainer = ref();

/** @description Cesium Viewer实例引用 */
let viewer: BC.Viewer | null = null;

/** @description 组件是否已销毁标志 */
let isDestroyed = false;

/** @description 初始化是否成功标志 */
let initializationSuccessful = false;

/** @description 引擎切换管理器实例 */
const engineSwitch = getEngineSwitch();

/**
 * @description 组件挂载后初始化Cesium场景
 */
onMounted(async () => {
  try {
    console.log('🚀 [CesiumContainer] 开始初始化Cesium场景...');

    // 检查DOM容器是否存在
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器DOM元素不存在');
    }

    // 创建Cesium Viewer实例
    viewer = await initializeCesiumViewer();

    if (!viewer) {
      throw new Error('Cesium Viewer创建失败');
    }

    // 初始化AppCesium实例
    await initializeAppCesium(viewer);

    // 标记初始化成功
    initializationSuccessful = true;

    // 注册清理任务到引擎切换管理器
    registerCleanupTasks();

    console.log('✅ [CesiumContainer] Cesium场景初始化完成');
  } catch (error) {
    console.error('❌ [CesiumContainer] Cesium初始化失败:', error);

    // 初始化失败时的清理
    await performEmergencyCleanup();

    // 可以向用户显示错误提示
    // ElMessage.error('三维地图初始化失败，请刷新页面重试');
  }
});

/**
 * @description 创建Cesium Viewer实例
 * @returns Promise<BC.Viewer | null> Cesium Viewer实例
 */
const initializeCesiumViewer = async (): Promise<BC.Viewer | null> => {
  try {
    console.log('📋 [CesiumContainer] 创建Cesium Viewer实例...');

    /**
     * @description 创建Cesium Viewer实例
     * @details 配置WebGL上下文选项，启用绘图缓冲区保留以支持截图功能
     */
    const viewerInstance = new BC.Viewer(cesiumContainer.value, {
      contextOptions: {
        webgl: {
          /**
           * @description 保留绘图缓冲区
           * @details 通过canvas.toDataURL()实现截图需要将该项设置为true
           */
          preserveDrawingBuffer: true,
        },
      },
    });

    /**
     * @description 启用地形深度测试
     * @details 确保地形与其他3D对象的正确深度排序和遮挡关系
     */
    viewerInstance.scene.globe.depthTestAgainstTerrain = true;

    // 启用比例尺
    viewerInstance.distanceLegend.enable = true;

    console.log('✅ [CesiumContainer] Cesium Viewer实例创建成功');
    return viewerInstance;
  } catch (error) {
    console.error('❌ [CesiumContainer] 创建Cesium Viewer失败:', error);
    return null;
  }
};

/**
 * @description 初始化AppCesium实例
 * @param viewerInstance Cesium Viewer实例
 */
const initializeAppCesium = async (
  viewerInstance: BC.Viewer
): Promise<void> => {
  try {
    console.log('📋 [CesiumContainer] 初始化AppCesium实例...');
    // 设置viewer实例
    AppCesium.getInstance().initViewer(viewerInstance);
    // 初始化基础地图
    await AppCesium.getInstance().initBaseMap(viewerInstance);

    // 加载图层
    await AppCesium.getInstance().loadLayers();

    // 模型点击事件
    viewerInstance.on(
      BC.MouseEventType.LEFT_CLICK,
      async (value: any) => {
        if (value.feature && value.feature.getPropertyIds) {
          console.log(value.feature.getPropertyIds());
          // const type = value.feature.getProperty('otype');
          // const point = value.feature.getProperty('point');
          // const tuceng = value.feature.getProperty('tuceng');
          // const line = value.feature.getProperty('line');
          const id = value.feature.getProperty('id');
          if (id.indexOf('_') === -1) {
            if (useDialogStore().detailDialogEnable) {
              useDialogStore().addDialog({
                name: '管点详情',
                path: 'ModelDetail',
                params: {
                  id,
                },
              });
            }
          }
        } else {
        }
      },
      this
    );
    // 飞行到默认位置
    AppCesium.getInstance().goToHome();

    console.log('✅ [CesiumContainer] AppCesium实例初始化完成');
  } catch (error) {
    console.error('❌ [CesiumContainer] AppCesium初始化失败:', error);
    throw error;
  }
};

/**
 * @description 注册清理任务到引擎切换管理器
 */
const registerCleanupTasks = (): void => {
  engineSwitch.addCleanupTask(async () => {
    console.log('🧹 [CesiumContainer] 执行引擎切换清理任务...');
    await performComponentCleanup();
  });
};

/**
 * @description 组件销毁前的清理（Vue生命周期）
 */
onBeforeUnmount(async () => {
  console.log('🔄 [CesiumContainer] 组件即将销毁，开始清理...');
  // 只有不是通过引擎切换销毁的情况下才执行清理
  // 引擎切换时的清理由EngineSwitch统一管理
  if (!engineSwitch.isSwitching()) {
    await performComponentCleanup();
  } else {
    console.log('⚠️ [CesiumContainer] 检测到引擎切换中，跳过Vue生命周期清理');
  }
});

/**
 * @description 组件卸载时的清理（Vue生命周期）
 */
onUnmounted(async () => {
  console.log('🗑️ [CesiumContainer] 组件已卸载，执行最终清理...');
  isDestroyed = true;

  // 只有不是通过引擎切换销毁的情况下才执行最终清理
  if (!engineSwitch.isSwitching()) {
    await performFinalCleanup();
  } else {
    console.log('⚠️ [CesiumContainer] 检测到引擎切换中，跳过最终清理');
  }
});

/**
 * @description 执行组件清理
 */
const performComponentCleanup = async (): Promise<void> => {
  if (isDestroyed) {
    console.log('⚠️ [CesiumContainer] 组件已销毁，跳过重复清理');
    return;
  }

  try {
    console.log('🧹 [CesiumContainer] 开始执行组件清理...');

    // 清理Cesium Viewer
    await cleanupCesiumViewer();

    // 清理AppCesium实例状态
    await cleanupAppCesium();

    // 清理DOM事件监听器
    cleanupEventListeners();

    // 清理定时器和异步任务
    cleanupTimersAndAsync();

    console.log('✅ [CesiumContainer] 组件清理完成');
  } catch (error) {
    console.error('❌ [CesiumContainer] 组件清理失败:', error);
  }
};

/**
 * @description 清理Cesium Viewer实例
 */
const cleanupCesiumViewer = async (): Promise<void> => {
  try {
    if (!viewer) {
      console.log('⚠️ [CesiumContainer] Viewer已不存在，跳过清理');
      return;
    }

    if (typeof viewer.destroy !== 'function') {
      console.warn('⚠️ [CesiumContainer] Viewer.destroy方法不存在');
      viewer = null;
      return;
    }

    console.log('🗑️ [CesiumContainer] 销毁Cesium Viewer实例...');

    // 保存viewer引用，防止在清理过程中被其他地方修改
    const viewerToClean = viewer;

    // 先重置引用，防止其他地方同时访问
    viewer = null;

    try {
      // 检查viewer是否已经被销毁（通过检查canvas是否还存在）
      if (viewerToClean.canvas && viewerToClean.canvas.parentNode) {
        console.log('📋 [CesiumContainer] Viewer状态正常，继续清理');

        // 清理场景内容
        if (viewerToClean.entities) {
          viewerToClean.entities.removeAll();
          console.log('🧹 [CesiumContainer] 实体已清理');
        }

        if (viewerToClean.scene && viewerToClean.scene.primitives) {
          viewerToClean.scene.primitives.removeAll();
          console.log('🧹 [CesiumContainer] 图元已清理');
        }

        if (viewerToClean.scene && viewerToClean.scene.groundPrimitives) {
          viewerToClean.scene.groundPrimitives.removeAll();
          console.log('🧹 [CesiumContainer] 地面图元已清理');
        }

        // 销毁viewer实例
        try {
          viewerToClean.destroy();
          console.log('✅ [CesiumContainer] Viewer.destroy()调用成功');
        } catch (destroyError) {
          // 检查是否是因为已被销毁而报错
          const errorMessage =
            destroyError instanceof Error
              ? destroyError.message
              : String(destroyError);
          if (errorMessage && errorMessage.includes('destroyed')) {
            console.log('ℹ️ [CesiumContainer] Viewer已被其他地方销毁');
          } else {
            console.error(
              '❌ [CesiumContainer] Viewer.destroy()调用失败:',
              destroyError
            );
          }
        }
      } else {
        console.log(
          'ℹ️ [CesiumContainer] Viewer已被销毁或无效，跳过destroy调用'
        );
      }
    } catch (cleanupError) {
      console.warn('⚠️ [CesiumContainer] 清理viewer时出现错误:', cleanupError);

      // 即使出错，也尝试调用destroy（作为最后的保险）
      try {
        if (
          viewerToClean.destroy &&
          typeof viewerToClean.destroy === 'function'
        ) {
          viewerToClean.destroy();
          console.log('🔄 [CesiumContainer] 保险destroy调用成功');
        }
      } catch (finalError) {
        console.warn('⚠️ [CesiumContainer] 最终destroy调用也失败:', finalError);
      }
    }

    console.log('✅ [CesiumContainer] Cesium Viewer清理流程完成');
  } catch (error) {
    console.error('❌ [CesiumContainer] 清理Cesium Viewer失败:', error);
    // 强制重置引用
    viewer = null;
  }
};

/**
 * @description 清理AppCesium实例状态
 */
const cleanupAppCesium = async (): Promise<void> => {
  try {
    console.log('🧹 [CesiumContainer] 清理AppCesium实例状态...');

    const cesiumInstance = AppCesium.getInstance();

    // 如果AppCesium有重置方法，调用它
    if (typeof (cesiumInstance as any).reset === 'function') {
      (cesiumInstance as any).reset();
    }

    // 清理单例状态（如果支持）
    if (typeof (AppCesium as any).resetInstance === 'function') {
      (AppCesium as any).resetInstance();
    }

    console.log('✅ [CesiumContainer] AppCesium状态清理完成');
  } catch (error) {
    console.error('❌ [CesiumContainer] 清理AppCesium状态失败:', error);
  }
};

/**
 * @description 清理DOM事件监听器
 */
const cleanupEventListeners = (): void => {
  try {
    console.log('🧹 [CesiumContainer] 清理DOM事件监听器...');

    // 移除可能的DOM事件监听器
    if (cesiumContainer.value) {
      // 清理容器事件
      const container = cesiumContainer.value;
      const clonedContainer = container.cloneNode(false);
      container.parentNode?.replaceChild(clonedContainer, container);
    }

    console.log('✅ [CesiumContainer] DOM事件监听器清理完成');
  } catch (error) {
    console.error('❌ [CesiumContainer] 清理DOM事件监听器失败:', error);
  }
};

/**
 * @description 清理定时器和异步任务
 */
const cleanupTimersAndAsync = (): void => {
  try {
    console.log('🧹 [CesiumContainer] 清理定时器和异步任务...');

    // 这里可以清理组件内的定时器
    // 如果有的话，添加clearTimeout/clearInterval调用

    console.log('✅ [CesiumContainer] 定时器和异步任务清理完成');
  } catch (error) {
    console.error('❌ [CesiumContainer] 清理定时器和异步任务失败:', error);
  }
};

/**
 * @description 执行最终清理
 */
const performFinalCleanup = async (): Promise<void> => {
  try {
    console.log('🗑️ [CesiumContainer] 执行最终清理...');

    // 清理容器DOM
    if (cesiumContainer.value) {
      cesiumContainer.value.innerHTML = '';
    }

    // 强制垃圾回收（如果支持）
    if (typeof (window as any).gc === 'function') {
      (window as any).gc();
    }

    console.log('✅ [CesiumContainer] 最终清理完成');
  } catch (error) {
    console.error('❌ [CesiumContainer] 最终清理失败:', error);
  }
};

/**
 * @description 紧急清理（初始化失败时调用）
 */
const performEmergencyCleanup = async (): Promise<void> => {
  try {
    console.log('🚨 [CesiumContainer] 执行紧急清理...');

    // 清理可能已创建的viewer
    if (viewer) {
      try {
        viewer.destroy();
      } catch (e) {
        console.warn('紧急清理viewer时出错:', e);
      }
      viewer = null;
    }

    // 清理DOM容器
    if (cesiumContainer.value) {
      cesiumContainer.value.innerHTML = '';
    }

    console.log('✅ [CesiumContainer] 紧急清理完成');
  } catch (error) {
    console.error('❌ [CesiumContainer] 紧急清理失败:', error);
  }
};

/**
 * @description 获取组件状态信息（用于调试）
 */
const getComponentStatus = () => {
  return {
    isDestroyed,
    initializationSuccessful,
    hasViewer: !!viewer,
    containerExists: !!cesiumContainer.value,
  };
};

// 开发环境下暴露调试信息
if (import.meta.env.DEV) {
  (window as any).cesiumContainerStatus = getComponentStatus;
}
</script>

<style scoped lang="scss">
/**
 * Cesium容器样式
 * 设置为全屏显示，绝对定位，无边距和内边距
 */
#cesium-container {
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  position: absolute;
}
</style>
