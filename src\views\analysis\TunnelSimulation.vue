<template>
  <page-card :close-icon="false" class="tabulate-sta" title="隧道模拟">
    <!-- 参数设置区域 -->
    <div class="parameter-section">
      <!-- 隧道形状选择 -->
      <div class="input-section">
        <el-text class="label-text">隧道形状：</el-text>
        <div class="input-container">
          <el-select
            v-model="tunnelShape"
            placeholder="请选择隧道形状"
            class="shape-select"
            :disabled="isDrawing || isAnalyzing"
          >
            <el-option label="圆形" value="circle" />
            <el-option label="矩形" value="rectangle" />
            <el-option label="马蹄形" value="horseshoe" />
          </el-select>
        </div>
      </div>

      <!-- 直径/尺寸设置 -->
      <div class="input-section" v-if="tunnelShape === 'circle'">
        <el-text class="label-text">隧道直径：</el-text>
        <div class="input-container">
          <el-input-number
            v-model="tunnelDiameter"
            placeholder="请输入隧道直径"
            :min="1"
            :max="50"
            :step="0.5"
            controls-position="right"
            class="requirement-input"
            :disabled="isDrawing || isAnalyzing"
          />
          <span class="unit-text">(m)</span>
        </div>
      </div>

      <!-- 矩形尺寸设置 -->
      <template v-if="tunnelShape === 'rectangle'">
        <div class="input-section">
          <el-text class="label-text">隧道宽度：</el-text>
          <div class="input-container">
            <el-input-number
              v-model="tunnelWidth"
              placeholder="请输入隧道宽度"
              :min="1"
              :max="50"
              :step="0.5"
              controls-position="right"
              class="requirement-input"
              :disabled="isDrawing || isAnalyzing"
            />
            <span class="unit-text">(m)</span>
          </div>
        </div>
        <div class="input-section">
          <el-text class="label-text">隧道高度：</el-text>
          <div class="input-container">
            <el-input-number
              v-model="tunnelHeight"
              placeholder="请输入隧道高度"
              :min="1"
              :max="50"
              :step="0.5"
              controls-position="right"
              class="requirement-input"
              :disabled="isDrawing || isAnalyzing"
            />
            <span class="unit-text">(m)</span>
          </div>
        </div>
      </template>

      <!-- 马蹄形尺寸设置 -->
      <template v-if="tunnelShape === 'horseshoe'">
        <div class="input-section">
          <el-text class="label-text">隧道宽度：</el-text>
          <div class="input-container">
            <el-input-number
              v-model="tunnelWidth"
              placeholder="请输入隧道宽度"
              :min="1"
              :max="50"
              :step="0.5"
              controls-position="right"
              class="requirement-input"
              :disabled="isDrawing || isAnalyzing"
            />
            <span class="unit-text">(m)</span>
          </div>
        </div>
        <div class="input-section">
          <el-text class="label-text">隧道高度：</el-text>
          <div class="input-container">
            <el-input-number
              v-model="tunnelHeight"
              placeholder="请输入隧道高度"
              :min="1"
              :max="50"
              :step="0.5"
              controls-position="right"
              class="requirement-input"
              :disabled="isDrawing || isAnalyzing"
            />
            <span class="unit-text">(m)</span>
          </div>
        </div>
      </template>

      <!-- 埋深设置 -->
      <div class="input-section">
        <el-text class="label-text">隧道埋深：</el-text>
        <div class="input-container">
          <el-input-number
            v-model="tunnelDepth"
            placeholder="请输入隧道埋深"
            :min="1"
            :max="200"
            :step="1"
            controls-position="right"
            class="requirement-input"
            :disabled="isDrawing || isAnalyzing"
          />
          <span class="unit-text">(m)</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="button-section">
      <el-button
        type="primary"
        class="primary-btn h-9 w-130px"
        :loading="isDrawing"
        @click="handleDrawPath"
        :disabled="isAnalyzing || !tunnelShape"
      >
        {{ isDrawing ? "绘制中..." : "绘制路径" }}
      </el-button>
      <el-button
        type="primary"
        class="primary-btn h-9 w-130px"
        :loading="isAnalyzing"
        @click="handleAnalysis"
        :disabled="!tunnelPath || isDrawing || !hasValidParameters"
      >
        {{ isAnalyzing ? "分析中..." : "生成隧道" }}
      </el-button>
      <el-button
        class="clear-btn w-130px"
        @click="handleClear"
        :disabled="isDrawing || isAnalyzing"
      >
        清除
      </el-button>
    </div>

    <!-- 分析结果显示区域 -->
    <div v-if="showResult" class="result-section">
      <el-text class="result-label">分析结果：</el-text>
      <div class="result-content">
        <!-- 冲突检测结果 -->
        <div v-if="conflictResult" class="conflict-result">
          <el-divider content-position="left" class="color-#2C3037"
            >冲突检测结果</el-divider
          >
          <div class="conflict-summary">
            <span
              :class="['conflict-status', hasConflicts ? 'conflict' : 'safe']"
            >
              {{ hasConflicts ? "检测到冲突" : "无冲突" }}
            </span>
            <span v-if="hasConflicts" class="conflict-count">
              ({{ conflictResult.conflicts.length }}处)
            </span>
          </div>

          <!-- 冲突详情列表 -->
          <!-- <div v-if="hasConflicts && conflictResult.conflicts.length > 0" class="conflict-list">
              <div 
                v-for="(conflict, index) in conflictResult.conflicts.slice(0, 5)" 
                :key="index"
                class="conflict-item"
                @click="highlightConflict(conflict)"
              >
                <div class="conflict-info">
                  <span class="pipe-name">{{ conflict.pipeName || '管线' + (index + 1) }}</span>
                  <span class="distance">距离: {{ conflict.distance.toFixed(2) }}m</span>
                </div>
                <el-icon class="conflict-icon"><Warning /></el-icon>
              </div>
              
              <div v-if="conflictResult.conflicts.length > 5" class="more-conflicts">
                还有 {{ conflictResult.conflicts.length - 5 }} 处冲突...
              </div>
            </div> -->
        </div>
      </div>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Warning } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
import { AppCesium } from "@/lib/cesium/AppCesium";
import { lineIntersect } from "@/api/analysis";
import type { MapEngineType } from "@/components/BufferAnalysisDrawButtons.vue";
import * as turf from "@turf/turf";

/**
 * 隧道参数接口定义
 */
interface TunnelConfig {
  shape: "circle" | "rectangle" | "horseshoe";
  diameter?: number;
  width?: number;
  height?: number;
  depth: number;
  opacity: number;
}

/**
 * 隧道路径点接口
 */
interface TunnelPathPoint {
  lng: number;
  lat: number;
  alt?: number;
}

/**
 * 冲突检测结果接口
 */
interface ConflictResult {
  hasConflicts: boolean;
  conflicts: ConflictItem[];
  totalChecked: number;
}

interface ConflictItem {
  pipeId: string;
  pipeName: string;
  distance: number;
  position: TunnelPathPoint;
  conflictType: "intersection" | "tooClose";
}

// 组件属性定义
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});

const route = useRoute();

/**
 * 计算地图引擎类型 - 隧道模拟仅支持三维(Cesium)
 */
const mapEngine = computed((): MapEngineType => {
  return "cesium";
});

// 隧道参数状态
const tunnelShape = ref<"circle" | "rectangle" | "horseshoe">("circle");
const tunnelDiameter = ref<number>(6);
const tunnelWidth = ref<number>(8);
const tunnelHeight = ref<number>(6);
const tunnelDepth = ref<number>(10);

// 材质配置
const tunnelMaterialType = ref("blue");
const tunnelOpacity = ref<number>(0.7);

// 组件状态
const isDrawing = ref<boolean>(false);
const isAnalyzing = ref<boolean>(false);
const showResult = ref<boolean>(false);
const tunnelPath = ref<TunnelPathPoint[] | null>(null);
const conflictResult = ref<ConflictResult | null>(null);

// 图层管理
let cesiumTunnelLayer: any = null;
let cesiumPathLayer: any = null;
let cesiumConflictLayer: any = null;
let tunnelPrimitiveLayer: any = null;
// 计算属性
const hasValidParameters = computed(() => {
  if (tunnelShape.value === "circle") {
    return tunnelDiameter.value > 0 && tunnelDepth.value > 0;
  } else {
    return (
      tunnelWidth.value > 0 && tunnelHeight.value > 0 && tunnelDepth.value > 0
    );
  }
});

const tunnelPathLength = computed(() => {
  if (!tunnelPath.value || tunnelPath.value.length < 2) return 0;

  // 计算路径总长度
  let totalLength = 0;
  for (let i = 0; i < tunnelPath.value.length - 1; i++) {
    const p1 = tunnelPath.value[i];
    const p2 = tunnelPath.value[i + 1];

    // 简化的距离计算（可以后续优化为更精确的大圆距离）
    const dx = (p2.lng - p1.lng) * Math.cos((p1.lat * Math.PI) / 180);
    const dy = p2.lat - p1.lat;
    const distance = Math.sqrt(dx * dx + dy * dy) * 111000; // 转换为米
    totalLength += distance;
  }

  return totalLength;
});

const hasConflicts = computed(() => {
  return conflictResult.value?.hasConflicts || false;
});

/**
 * 获取形状标签
 */
const getShapeLabel = (shape: string): string => {
  const labels = {
    circle: "圆形",
    rectangle: "矩形",
    horseshoe: "马蹄形",
  };
  return labels[shape as keyof typeof labels] || shape;
};

/**
 * 获取尺寸文本
 */
const getDimensionText = (): string => {
  if (tunnelShape.value === "circle") {
    return `直径${tunnelDiameter.value}米`;
  } else {
    return `${tunnelWidth.value}×${tunnelHeight.value}米`;
  }
};

/**
 * 处理绘制路径操作
 */
const handleDrawPath = async (): Promise<void> => {
  if (mapEngine.value !== "cesium") {
    ElMessage.warning("隧道模拟仅支持三维模式");
    return;
  }

  isDrawing.value = true;

  try {
    ElMessage.info("请在地图上绘制隧道中心线路径");

    // 清除之前的结果
    clearTunnelResults();

    // 开始绘制路径
    await startPathDrawing();
  } catch (error) {
    console.error("路径绘制失败:", error);
    ElMessage.error(
      `路径绘制失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isDrawing.value = false;
  }
};

/**
 * 处理隧道分析操作
 */
const handleAnalysis = async (): Promise<void> => {
  if (!tunnelPath.value || !hasValidParameters.value) {
    ElMessage.warning("请先绘制隧道路径并设置有效参数");
    return;
  }

  isAnalyzing.value = true;

  try {
    ElMessage.info("正在生成隧道模型...");

    // 生成隧道几何体
    await createTunnelGeometry();

    ElMessage.info("正在进行冲突检测...");

    // 执行冲突检测
    await performConflictDetection();

    showResult.value = true;
    ElMessage.success("隧道分析完成");
  } catch (error) {
    console.error("隧道分析失败:", error);
    ElMessage.error(
      `隧道分析失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isAnalyzing.value = false;
  }
};

/**
 * 处理清除操作
 */
const handleClear = (): void => {
  // 清除隧道分析结果
  clearTunnelResults();

  // 重置组件状态
  tunnelPath.value = null;
  conflictResult.value = null;
  showResult.value = false;
  isDrawing.value = false;
  isAnalyzing.value = false;

  clearPrimitives();

  ElMessage.info("已清除所有隧道分析结果");
};

const clearPrimitives = () => {
  // 清除隧道几何体和材质
  try {
    const viewer = AppCesium.getInstance().getViewer();

    // 清除隧道Primitive图层
    if (tunnelPrimitiveLayer) {
      tunnelPrimitiveLayer.clear();
      viewer.removeLayer(tunnelPrimitiveLayer);
      tunnelPrimitiveLayer = null;
      console.log("已清除隧道Primitive图层");
    }

    // 清除隧道图层
    if (cesiumTunnelLayer) {
      cesiumTunnelLayer.clear();
      viewer.removeLayer(cesiumTunnelLayer);
      cesiumTunnelLayer = null;
      console.log("已清除隧道几何图层");
    }

    // 清除路径图层
    if (cesiumPathLayer) {
      cesiumPathLayer.clear();
      viewer.removeLayer(cesiumPathLayer);
      cesiumPathLayer = null;
      console.log("已清除路径图层");
    }

    console.log("所有隧道相关图层已清除");
  } catch (error) {
    console.error("清除隧道几何体失败:", error);
  }
};

/**
 * 高亮显示冲突
 */
const highlightConflict = (conflict: ConflictItem): void => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = BC.Namespace;

    // 定位到冲突位置
    const position = Cesium.Cartesian3.fromDegrees(
      conflict.position.lng,
      conflict.position.lat,
      (conflict.position.alt || 10) + 50
    );

    // 飞行到冲突位置
    viewer.camera.flyTo({
      destination: position,
      duration: 2.0,
      complete: () => {
        // 创建临时高亮效果
        createTemporaryHighlight(conflict);
      },
    });

    ElMessage.info(`已定位到冲突管线: ${conflict.pipeName}`);
  } catch (error) {
    console.error("高亮冲突失败:", error);
    ElMessage.error("定位冲突失败");
  }
};

/**
 * 创建临时高亮效果
 */
const createTemporaryHighlight = (conflict: ConflictItem): void => {
  try {
    const { Cesium } = BC.Namespace;

    if (!cesiumConflictLayer) return;

    // 移除之前的临时高亮
    const existingHighlight = cesiumConflictLayer.getOverlaysByAttr(
      "id",
      "temp-highlight"
    );
    if (existingHighlight && existingHighlight.length > 0) {
      existingHighlight.forEach((overlay: any) => {
        cesiumConflictLayer.removeOverlay(overlay);
      });
    }

    // 创建高亮圆圈
    const position = new BC.Position(
      conflict.position.lng,
      conflict.position.lat,
      conflict.position.alt || 10
    );

    const highlightCircle = new BC.Circle(position, 10); // 10米半径的高亮圈
    highlightCircle.id = "temp-highlight";
    highlightCircle.setStyle({
      material: BC.Color.YELLOW.withAlpha(0.3),
      outline: true,
      outlineColor: BC.Color.YELLOW,
      outlineWidth: 3,
      height: conflict.position.alt || 10,
    });

    cesiumConflictLayer.addOverlay(highlightCircle);

    // 3秒后移除高亮
    setTimeout(() => {
      try {
        if (cesiumConflictLayer && highlightCircle) {
          cesiumConflictLayer.removeOverlay(highlightCircle);
        }
      } catch (e) {
        console.warn("移除临时高亮失败:", e);
      }
    }, 3000);
  } catch (error) {
    console.error("创建临时高亮失败:", error);
  }
};

/**
 * 开始路径绘制功能
 */
const startPathDrawing = async (): Promise<void> => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = BC.Namespace;

    ElMessage.info("请在地图上连续点击绘制隧道路径，双击结束绘制");

    // 清除之前的路径
    if (cesiumPathLayer) {
      cesiumPathLayer.clear();
      viewer.removeLayer(cesiumPathLayer);
      cesiumPathLayer = null;
    }

    // 创建路径图层
    cesiumPathLayer = new BC.VectorLayer("tunnel-path-layer");
    viewer.addLayer(cesiumPathLayer);

    const pathPoints: TunnelPathPoint[] = [];
    let isDrawing = true;
    let polylineOverlay: any = null;

    // 绘制临时路径线
    const updatePathLine = () => {
      if (polylineOverlay) {
        cesiumPathLayer.removeOverlay(polylineOverlay);
      }

      if (pathPoints.length >= 2) {
        const positions = pathPoints.map(
          (point) => new BC.Position(point.lng, point.lat, point.alt || 20)
        );

        polylineOverlay = new BC.Polyline(positions);
        polylineOverlay.id = "tunnel-path-drawing";
        polylineOverlay.setStyle({
          width: 4,
          material: BC.Color.CYAN.withAlpha(0.8),
          clampToGround: false,
        });

        cesiumPathLayer.addOverlay(polylineOverlay);
      }
    };

    // 添加路径点标记
    const addPointMarker = (point: TunnelPathPoint, index: number) => {
      const position = new BC.Position(point.lng, point.lat, point.alt || 20);

      const pointMarker = new BC.Point(position);
      pointMarker.id = `tunnel-path-point-${index}`;
      pointMarker.setStyle({
        pixelSize: 8,
        color: BC.Color.YELLOW,
        outlineColor: BC.Color.BLACK,
        outlineWidth: 2,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      });

      const labelPosition = new BC.Position(
        point.lng,
        point.lat,
        (point.alt || 20) + 2
      );
      const label = new BC.Label(labelPosition, `P${index + 1}`);
      label.id = `tunnel-path-label-${index}`;
      label.setStyle({
        font: "12px Microsoft YaHei",
        fillColor: BC.Color.WHITE,
        outlineColor: BC.Color.BLACK,
        outlineWidth: 1,
        offsetX: 0,
        offsetY: -15,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      });

      cesiumPathLayer.addOverlay(pointMarker);
      cesiumPathLayer.addOverlay(label);
    };

    // 使用递归方式处理连续点击绘制
    const handleNextPoint = () => {
      if (!isDrawing) return;

      viewer.once(
        BC.MouseEventType.LEFT_CLICK,
        (event: any) => {
          try {
            if (event.wgs84Position) {
              const point: TunnelPathPoint = {
                lng: event.wgs84Position.lng,
                lat: event.wgs84Position.lat,
                alt: event.wgs84Position.alt || 20,
              };

              pathPoints.push(point);
              addPointMarker(point, pathPoints.length - 1);
              updatePathLine();

              console.log(`添加路径点 ${pathPoints.length}:`, point);

              // 继续监听下一个点
              handleNextPoint();
            }
          } catch (error) {
            console.error("添加路径点失败:", error);
          }
        },
        viewer
      );
    };

    // 开始监听点击事件
    handleNextPoint();

    // 监听双击结束绘制
    viewer.once(
      BC.MouseEventType.LEFT_DOUBLE_CLICK,
      () => {
        isDrawing = false;

        if (pathPoints.length >= 2) {
          tunnelPath.value = [...pathPoints];

          // 最终路径样式
          if (polylineOverlay) {
            polylineOverlay.setStyle({
              width: 5,
              material: BC.Color.ORANGE.withAlpha(0.9),
              clampToGround: false,
            });
          }

          ElMessage.success(`隧道路径绘制完成，共${pathPoints.length}个控制点`);
        } else {
          ElMessage.warning("路径点数量不足，请至少绘制2个点");
          // 清除不完整的路径
          if (cesiumPathLayer) {
            cesiumPathLayer.clear();
          }
        }
      },
      viewer
    );

    // 设置绘制超时（2分钟）
    setTimeout(() => {
      if (isDrawing) {
        isDrawing = false;
        ElMessage.warning("绘制超时，请重新开始绘制");
      }
    }, 120000);
  } catch (error) {
    console.error("路径绘制启动失败:", error);
    throw error;
  }
};

/**
 * 创建隧道几何体
 */
const createTunnelGeometry = async (): Promise<void> => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = BC.Namespace;

    if (!tunnelPath.value || tunnelPath.value.length < 2) {
      throw new Error("隧道路径不足");
    }

    // 清除之前的隧道（包括Primitive和图层）
    clearPrimitives();

    // 创建隧道图层
    cesiumTunnelLayer = new BC.VectorLayer("tunnel-geometry-layer");
    viewer.addLayer(cesiumTunnelLayer);

    // 创建隧道Primitive图层
    tunnelPrimitiveLayer = new BC.PrimitiveLayer("tunnel-primitive-layer");
    viewer.addLayer(tunnelPrimitiveLayer);

    // 转换路径点为Cesium坐标，直接应用埋深
    const depth = tunnelDepth.value;
    const pathPositions = tunnelPath.value.map((point) =>
      Cesium.Cartesian3.fromDegrees(
        point.lng,
        point.lat,
        (point.alt || 20) - depth
      )
    );

    // 根据隧道形状创建不同的几何体
    switch (tunnelShape.value) {
      case "circle":
        await createCircularTunnel(pathPositions, tunnelDiameter.value / 2);
        break;
      case "rectangle":
        await createRectangularTunnel(
          pathPositions,
          tunnelWidth.value,
          tunnelHeight.value
        );
        break;
      case "horseshoe":
        await createHorseshoeTunnel(
          pathPositions,
          tunnelWidth.value,
          tunnelHeight.value
        );
        break;
      default:
        throw new Error("不支持的隧道形状");
    }

    console.log("隧道几何体创建完成");
  } catch (error) {
    console.error("隧道几何体创建失败:", error);
    throw error;
  }
};

/**
 * 创建圆形隧道
 */
const createCircularTunnel = async (
  pathPositions: any[],
  radius: number
): Promise<void> => {
  try {
    const { Cesium } = BC.Namespace;

    // 创建圆形截面形状点
    const circlePoints = [];
    const segments = 32; // 圆形分段数
    for (let i = 0; i < segments; i++) {
      const angle = (i * 2 * Math.PI) / segments;
      circlePoints.push(
        new Cesium.Cartesian2(
          radius * Math.cos(angle),
          radius * Math.sin(angle)
        )
      );
    }

    // 创建圆形隧道几何体
    const tunnelGeometry = new Cesium.PolylineVolumeGeometry({
      polylinePositions: pathPositions,
      shapePositions: circlePoints,
      cornerType: Cesium.CornerType.ROUNDED,
      vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
    });

    await renderTunnelGeometry(tunnelGeometry, "circular");
  } catch (error) {
    console.error("圆形隧道创建失败:", error);
    throw error;
  }
};

/**
 * 创建矩形隧道
 */
const createRectangularTunnel = async (
  pathPositions: any[],
  width: number,
  height: number
): Promise<void> => {
  try {
    const { Cesium } = BC.Namespace;

    // 创建矩形截面形状点
    const halfWidth = width / 2;
    const halfHeight = height / 2;
    const rectanglePoints = [
      new Cesium.Cartesian2(-halfWidth, -halfHeight),
      new Cesium.Cartesian2(halfWidth, -halfHeight),
      new Cesium.Cartesian2(halfWidth, halfHeight),
      new Cesium.Cartesian2(-halfWidth, halfHeight),
    ];

    // 创建矩形隧道几何体
    const tunnelGeometry = new Cesium.PolylineVolumeGeometry({
      polylinePositions: pathPositions,
      shapePositions: rectanglePoints,
      cornerType: Cesium.CornerType.MITERED,
      vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
    });

    await renderTunnelGeometry(tunnelGeometry, "rectangular");
  } catch (error) {
    console.error("矩形隧道创建失败:", error);
    throw error;
  }
};

/**
 * 创建马蹄形隧道
 */
const createHorseshoeTunnel = async (
  pathPositions: any[],
  width: number,
  height: number
): Promise<void> => {
  try {
    const { Cesium } = BC.Namespace;

    // 创建马蹄形截面形状点
    const halfWidth = width / 2;
    const horseshoePoints = [];

    // 底部直线段
    horseshoePoints.push(new Cesium.Cartesian2(-halfWidth, 0));
    horseshoePoints.push(new Cesium.Cartesian2(halfWidth, 0));

    // 右侧弧形段
    const segments = 16;
    for (let i = 0; i <= segments; i++) {
      const angle = (i * Math.PI) / segments;
      horseshoePoints.push(
        new Cesium.Cartesian2(
          halfWidth * Math.cos(angle),
          halfWidth * Math.sin(angle) + height - halfWidth
        )
      );
    }

    // 创建马蹄形隧道几何体
    const tunnelGeometry = new Cesium.PolylineVolumeGeometry({
      polylinePositions: pathPositions,
      shapePositions: horseshoePoints,
      cornerType: Cesium.CornerType.ROUNDED,
      vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
    });

    await renderTunnelGeometry(tunnelGeometry, "horseshoe");
  } catch (error) {
    console.error("马蹄形隧道创建失败:", error);
    throw error;
  }
};

/**
 * 渲染隧道几何体
 */
const renderTunnelGeometry = async (
  tunnelGeometry: any,
  shapeType: string
): Promise<void> => {
  try {
    const { Cesium } = BC.Namespace;
    const viewer = AppCesium.getInstance().getViewer();

    // 创建几何体实例
    const geometryInstance = new Cesium.GeometryInstance({
      geometry: tunnelGeometry,
      id: `tunnel-${shapeType}`,
    });

    // 创建动态配置的纯色半透明材质
    const tunnelMaterial = new Cesium.Material({
      fabric: {
        type: "Color",
        uniforms: {
          color: new Cesium.Color(0.2, 0.8, 0.3, tunnelOpacity.value),
        },
      },
    });

    // 创建Primitive进行渲染
    const tunnelPrimitive = new Cesium.Primitive({
      geometryInstances: geometryInstance,
      appearance: new Cesium.MaterialAppearance({
        material: tunnelMaterial,
        translucent: true, // 启用半透明支持
        closed: false,
      }),
      asynchronous: false,
    });

    // 添加到场景
    tunnelPrimitiveLayer.delegate.add(tunnelPrimitive);

    // 同时使用VectorLayer方式创建半透明显示
    await createTunnelCorridorDisplay(shapeType);

    console.log(`${shapeType}隧道几何体渲染完成，埋深：${tunnelDepth.value}米`);
  } catch (error) {
    console.error("隧道几何体渲染失败:", error);
    throw error;
  }
};

/**
 * 创建隧道走廊显示（用于更好的可视化效果）
 */
const createTunnelCorridorDisplay = async (
  shapeType: string
): Promise<void> => {
  try {
    const { Cesium } = BC.Namespace;

    if (!tunnelPath.value || tunnelPath.value.length < 2) return;

    // 创建地面路径显示
    const surfacePositions = tunnelPath.value.map(
      (point) => new BC.Position(point.lng, point.lat, point.alt || 20)
    );

    const surfaceLine = new BC.Polyline(surfacePositions);
    surfaceLine.id = "tunnel-surface-path";
    surfaceLine.setStyle({
      width: 3,
      material: BC.Color.YELLOW.withAlpha(0.8),
      clampToGround: false,
    });
    cesiumTunnelLayer.addOverlay(surfaceLine);

    // 创建地下隧道中心线显示
    const undergroundPositions = tunnelPath.value.map(
      (point) =>
        new BC.Position(
          point.lng,
          point.lat,
          (point.alt || 20) - tunnelDepth.value
        )
    );

    const undergroundLine = new BC.Polyline(undergroundPositions);
    undergroundLine.id = "tunnel-underground-centerline";
    undergroundLine.setStyle({
      width: 2,
      material: BC.Color.RED.withAlpha(0.6),
      clampToGround: false,
    });
    cesiumTunnelLayer.addOverlay(undergroundLine);

    // 创建连接线显示埋深
    for (let i = 0; i < tunnelPath.value.length; i++) {
      const point = tunnelPath.value[i];
      const connectionPositions = [
        new BC.Position(point.lng, point.lat, point.alt || 20),
        new BC.Position(
          point.lng,
          point.lat,
          (point.alt || 20) - tunnelDepth.value
        ),
      ];

      const connectionLine = new BC.Polyline(connectionPositions);
      connectionLine.id = `tunnel-depth-connection-${i}`;
      connectionLine.setStyle({
        width: 1,
        material: BC.Color.WHITE.withAlpha(0.4),
        clampToGround: false,
      });
      cesiumTunnelLayer.addOverlay(connectionLine);
    }

    console.log("隧道走廊显示创建完成");
  } catch (error) {
    console.error("隧道走廊显示创建失败:", error);
  }
};

/**
 * 执行隧道与管线的冲突检测（优化版 - 两阶段检测）
 */
const performConflictDetection = async (): Promise<void> => {
  try {
    const viewer = AppCesium.getInstance().getViewer();

    if (!tunnelPath.value || tunnelPath.value.length < 2) {
      throw new Error("隧道路径不足");
    }

    console.log("开始两阶段冲突检测...");

    // 清除之前的冲突显示
    if (cesiumConflictLayer) {
      cesiumConflictLayer.clear();
      viewer.removeLayer(cesiumConflictLayer);
      cesiumConflictLayer = null;
    }

    // 创建冲突图层
    cesiumConflictLayer = new BC.VectorLayer("tunnel-conflict-layer");
    viewer.addLayer(cesiumConflictLayer);

    // 第一阶段：平面相交检测
    ElMessage.info("第一阶段：平面相交检测...");
    const planarCandidates = await performPlanarIntersectionCheck();

    console.log(`平面检测完成，发现 ${planarCandidates.length} 个潜在冲突候选`);
    if (planarCandidates.length === 0) {
      // 没有平面相交，直接返回无冲突结果
      conflictResult.value = {
        hasConflicts: false,
        conflicts: [],
        totalChecked: 0,
      };
      console.log("平面检测无相交，跳过三维检测");
      return;
    }

    // 第二阶段：三维空间精确检测
    ElMessage.info(
      `第二阶段：对 ${planarCandidates.length} 个候选进行三维精确检测...`
    );
    const finalConflicts = await perform3DConflictDetection(planarCandidates);

    // 设置检测结果
    conflictResult.value = {
      hasConflicts: finalConflicts.length > 0,
      conflicts: finalConflicts,
      totalChecked: planarCandidates.length,
    };

    // 可视化冲突结果
    if (finalConflicts.length > 0) {
      await visualizeConflicts(finalConflicts);
    }

    console.log(
      `两阶段冲突检测完成，最终发现 ${finalConflicts.length} 个真实冲突`
    );
  } catch (error) {
    console.error("冲突检测失败:", error);
  }
};

/**
 * 第一阶段：平面相交检测
 */
const performPlanarIntersectionCheck = async (): Promise<ConflictItem[]> => {
  try {
    if (!tunnelPath.value) return [];

    // 构造隧道路径的线段数据
    const tunnelLineCoordinates = tunnelPath.value.map((point) => [
      point.lng,
      point.lat,
    ]);

    // 创建GeoJSON LineString对象
    const lineString = turf.lineString(tunnelLineCoordinates);

    // 计算缓冲距离（米）
    const bufferDistance = getTunnelRadius(); // 使用隧道半径加安全距离作为缓冲

    // 使用turf.buffer创建缓冲区面
    const bufferedPolygon = turf.buffer(lineString, bufferDistance, {
      units: "meters",
    });

    // 检查缓冲区是否创建成功
    if (!bufferedPolygon || !bufferedPolygon.geometry) {
      throw new Error("缓冲区面创建失败");
    }

    let formData = new FormData();
    formData.append("json", JSON.stringify(bufferedPolygon.geometry));
    const response = await lineIntersect(formData);
    if (response.code === 200 && response.data) {
      const intersectionResults = response.data;

      // 转换API结果为ConflictItem格式
      const candidates: ConflictItem[] = [];

      if (Array.isArray(intersectionResults)) {
        intersectionResults.forEach((result: any, index: number) => {
          //起点高程
          const qdgc = result.qdgc;
          //终点高程
          const zdgc = result.zdgc;
          //管线geojson
          const lineGeojson = result.geojson;
          //相交点
          const intersect = {
            lng: result.longitude,
            lat: result.latitude,
          };

          const intersectHeight = calcIntersectHeight(
            lineGeojson,
            intersect,
            qdgc,
            zdgc
          );

          candidates.push({
            pipeId: result.pipeId || `pipe_${index}`,
            pipeName: result.pipeName || result.gxbm || `管线${index + 1}`,
            distance: result.distance || 0,
            position: {
              lng: result.longitude || result.x || 0,
              lat: result.latitude || result.y || 0,
              alt: intersectHeight, // 使用计算出的相交点高程
            },
            conflictType: "intersection",
          });
        });
      }

      console.log(`平面检测API返回 ${candidates.length} 个候选冲突`);
      return candidates;
    } else {
      console.warn("lineIntersect API返回异常:", response);
      return [];
    }
  } catch (error) {
    console.error("平面相交检测失败:", error);
    // 如果API调用失败，回退到传统方法
    return await fallbackToTraditionalDetection();
  }
};

/**
 * 第二阶段：三维空间精确检测
 */
const perform3DConflictDetection = async (
  candidates: ConflictItem[]
): Promise<ConflictItem[]> => {
  try {
    const finalConflicts: ConflictItem[] = [];

    for (const candidate of candidates) {
      // 对每个候选进行三维空间精确计算
      const is3DConflict = await check3DConflict(candidate);

      if (is3DConflict) {
        finalConflicts.push(candidate);
      }
    }

    console.log(`三维精确检测完成，确认 ${finalConflicts.length} 个真实冲突`);
    return finalConflicts;
  } catch (error) {
    console.error("三维空间检测失败:", error);
    return [];
  }
};

/**
 * 检查单个候选的三维空间冲突
 */
const check3DConflict = async (candidate: ConflictItem): Promise<boolean> => {
  try {
    // 获取候选管线的三维信息（埋深、高程等）
    const pipeDepth = candidate.position.alt || 0;

    // 计算隧道在相交点位置的实际高程（基于路径高程分布和埋深）
    const tunnelDepthValue = calcTunnelHeightAtIntersection({
      lng: candidate.position.lng,
      lat: candidate.position.lat,
    });

    // 计算垂直距离
    const verticalDistance = Math.abs(pipeDepth - tunnelDepthValue);

    // 获取隧道尺寸作为安全距离
    const tunnelRadius = getTunnelRadius();
    const safeDistance = tunnelRadius + 1; // 隧道半径 + 1米安全距离

    // 判断是否存在三维冲突
    const hasConflict = verticalDistance < safeDistance;

    if (hasConflict) {
      // 更新候选的距离信息
      candidate.distance = verticalDistance;
      candidate.conflictType =
        verticalDistance < tunnelRadius ? "intersection" : "tooClose";
    }

    return hasConflict;
  } catch (error) {
    console.error("三维冲突检查失败:", error);
    return false;
  }
};

/**
 * 回退到传统检测方法（当API失败时）
 */
const fallbackToTraditionalDetection = async (): Promise<ConflictItem[]> => {
  try {
    console.log("回退到传统检测方法...");
    const pipelineData = await fetchPipelineData();
    return await analyzeConflicts(pipelineData);
  } catch (error) {
    console.error("传统检测方法也失败:", error);
    return [];
  }
};

/**
 * 获取管线数据
 */
const fetchPipelineData = async (): Promise<any[]> => {
  try {
    // 尝试从API获取管线数据
    const { pipeLineList } = await import("@/api/pipeLine");
    const response = await pipeLineList();

    if (response.data && response.data.length > 0) {
      return response.data.filter((pipe: any) => pipe.geojson);
    }

    throw new Error("无有效管线数据");
  } catch (error) {
    console.warn("获取管线数据失败，使用模拟数据:", error);
    return generateMockPipelineData();
  }
};

/**
 * 生成模拟管线数据用于演示
 */
const generateMockPipelineData = (): any[] => {
  const mockPipes = [];
  const basePoint = tunnelPath.value![0];

  // 生成几条模拟管线，有些与隧道路径相交
  for (let i = 0; i < 5; i++) {
    const offsetLat = (Math.random() - 0.5) * 0.01;
    const offsetLng = (Math.random() - 0.5) * 0.01;

    const mockPipe = {
      gid: `mock_pipe_${i}`,
      gxbm: `模拟管线_${i + 1}`,
      gl: i % 2 === 0 ? "给水" : "排水",
      gj: 200 + Math.random() * 300,
      qdms: 2 + Math.random() * 3,
      zdms: 2 + Math.random() * 3,
      cz: i % 3 === 0 ? "球墨铸铁" : "PE",
      geojson: JSON.stringify({
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: [
            [
              basePoint.lng - 0.005 + offsetLng,
              basePoint.lat - 0.005 + offsetLat,
            ],
            [
              basePoint.lng + 0.005 + offsetLng,
              basePoint.lat + 0.005 + offsetLat,
            ],
          ],
        },
        properties: {},
      }),
    };

    mockPipes.push(mockPipe);
  }

  return mockPipes;
};

/**
 * 分析隧道与管线的空间冲突
 */
const analyzeConflicts = async (
  pipelineData: any[]
): Promise<ConflictItem[]> => {
  const conflicts: ConflictItem[] = [];
  const safeDistance = 3; // 安全距离（米）
  const tunnelRadius = getTunnelRadius();

  for (const pipe of pipelineData) {
    try {
      if (!pipe.geojson) continue;

      const geometry = JSON.parse(pipe.geojson);
      if (
        geometry.type !== "Feature" ||
        geometry.geometry.type !== "LineString"
      ) {
        continue;
      }

      const pipeCoordinates = geometry.geometry.coordinates;

      // 检查管线的每个段与隧道路径的距离
      for (let i = 0; i < pipeCoordinates.length - 1; i++) {
        const pipePoint1 = {
          lng: pipeCoordinates[i][0],
          lat: pipeCoordinates[i][1],
          alt: pipe.qdms || 0, // 使用管线埋深
        };

        const pipePoint2 = {
          lng: pipeCoordinates[i + 1][0],
          lat: pipeCoordinates[i + 1][1],
          alt: pipe.zdms || 0,
        };

        // 检查管线段与隧道路径的最近距离
        const conflictInfo = checkLineConflict(
          pipePoint1,
          pipePoint2,
          tunnelRadius,
          safeDistance
        );

        if (conflictInfo.hasConflict) {
          conflicts.push({
            pipeId: pipe.gid || pipe.gxbm,
            pipeName: `${pipe.gl || "管线"} (${pipe.gj || "未知"}mm)`,
            distance: conflictInfo.distance,
            position: conflictInfo.position,
            conflictType:
              conflictInfo.distance < tunnelRadius
                ? "intersection"
                : "tooClose",
          });
          break; // 每条管线只记录一次冲突
        }
      }
    } catch (error) {
      console.warn(`管线 ${pipe.gid} 冲突检测失败:`, error);
    }
  }

  return conflicts;
};

/**
 * 检查管线段与隧道路径的冲突
 */
const checkLineConflict = (
  pipeStart: TunnelPathPoint,
  pipeEnd: TunnelPathPoint,
  tunnelRadius: number,
  safeDistance: number
): { hasConflict: boolean; distance: number; position: TunnelPathPoint } => {
  let minDistance = Infinity;
  let conflictPosition = pipeStart;

  // 检查管线与隧道路径每个段的距离
  for (let i = 0; i < tunnelPath.value!.length - 1; i++) {
    const tunnelStart = tunnelPath.value![i];
    const tunnelEnd = tunnelPath.value![i + 1];

    // 调整隧道深度
    const tunnelStartWithDepth = {
      ...tunnelStart,
      alt: (tunnelStart.alt || 20) - tunnelDepth.value,
    };
    const tunnelEndWithDepth = {
      ...tunnelEnd,
      alt: (tunnelEnd.alt || 20) - tunnelDepth.value,
    };

    // 计算两条线段的最短距离
    const distance = calculateLineSegmentDistance(
      pipeStart,
      pipeEnd,
      tunnelStartWithDepth,
      tunnelEndWithDepth
    );

    if (distance < minDistance) {
      minDistance = distance;
      // 简化：使用管线起点作为冲突位置
      conflictPosition = pipeStart;
    }
  }

  const totalRequired = tunnelRadius + safeDistance;
  return {
    hasConflict: minDistance < totalRequired,
    distance: minDistance,
    position: conflictPosition,
  };
};

/**
 * 计算两条线段之间的最短距离（简化算法）
 */
const calculateLineSegmentDistance = (
  line1Start: TunnelPathPoint,
  line1End: TunnelPathPoint,
  line2Start: TunnelPathPoint,
  line2End: TunnelPathPoint
): number => {
  // 简化计算：计算线段中点之间的距离
  const line1Mid = {
    lng: (line1Start.lng + line1End.lng) / 2,
    lat: (line1Start.lat + line1End.lat) / 2,
    alt: ((line1Start.alt || 0) + (line1End.alt || 0)) / 2,
  };

  const line2Mid = {
    lng: (line2Start.lng + line2End.lng) / 2,
    lat: (line2Start.lat + line2End.lat) / 2,
    alt: ((line2Start.alt || 0) + (line2End.alt || 0)) / 2,
  };

  return calculateDistance3D(line1Mid, line2Mid);
};

/**
 * 计算两点之间的3D距离
 */
const calculateDistance3D = (
  point1: TunnelPathPoint,
  point2: TunnelPathPoint
): number => {
  const { Cesium } = BC.Namespace;

  const pos1 = Cesium.Cartesian3.fromDegrees(
    point1.lng,
    point1.lat,
    point1.alt || 0
  );
  const pos2 = Cesium.Cartesian3.fromDegrees(
    point2.lng,
    point2.lat,
    point2.alt || 0
  );

  return Cesium.Cartesian3.distance(pos1, pos2);
};

/**
 * 获取隧道半径
 */
const getTunnelRadius = (): number => {
  switch (tunnelShape.value) {
    case "circle":
      return tunnelDiameter.value / 2;
    case "rectangle":
      return Math.max(tunnelWidth.value, tunnelHeight.value) / 2;
    case "horseshoe":
      return Math.max(tunnelWidth.value, tunnelHeight.value) / 2;
    default:
      return 3; // 默认半径
  }
};

/**
 * 可视化冲突结果
 */
const visualizeConflicts = async (conflicts: ConflictItem[]): Promise<void> => {
  try {
    const { Cesium } = BC.Namespace;

    for (let i = 0; i < conflicts.length; i++) {
      const conflict = conflicts[i];

      // 创建冲突点标记
      const position = new BC.Position(
        conflict.position.lng,
        conflict.position.lat,
        conflict.position.alt || 10
      );

      const conflictPoint = new BC.Point(position);
      conflictPoint.id = `conflict-point-${i}`;
      conflictPoint.setStyle({
        pixelSize: 12,
        color:
          conflict.conflictType === "intersection"
            ? BC.Color.RED
            : BC.Color.ORANGE,
        outlineColor: BC.Color.WHITE,
        outlineWidth: 2,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      });

      // 创建冲突标签
      const labelPosition = new BC.Position(
        conflict.position.lng,
        conflict.position.lat,
        (conflict.position.alt || 10) + 3
      );
      // const label = new BC.Label(labelPosition, `冲突!\n${conflict.pipeName}\n距离: ${conflict.distance.toFixed(2)}m`);
      const label = new BC.Label(labelPosition, `冲突!`);
      label.id = `conflict-label-${i}`;
      label.setStyle({
        font: "12px Microsoft YaHei",
        fillColor: BC.Color.WHITE,
        outlineColor: BC.Color.RED,
        outlineWidth: 2,
        offsetX: 0,
        offsetY: -30,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      });

      cesiumConflictLayer.addOverlay(conflictPoint);
      cesiumConflictLayer.addOverlay(label);
    }

    console.log(`已可视化${conflicts.length}处冲突`);
  } catch (error) {
    console.error("冲突可视化失败:", error);
  }
};

const clearTunnelResults = (): void => {
  try {
    const viewer = AppCesium.getInstance().getViewer();

    // 清除隧道Primitive图层
    if (tunnelPrimitiveLayer) {
      tunnelPrimitiveLayer.clear();
      viewer.removeLayer(tunnelPrimitiveLayer);
      tunnelPrimitiveLayer = null;
      console.log("已清除隧道Primitive图层");
    }

    // 清除隧道图层
    if (cesiumTunnelLayer) {
      cesiumTunnelLayer.clear();
      viewer.removeLayer(cesiumTunnelLayer);
      cesiumTunnelLayer = null;
      console.log("已清除隧道几何图层");
    }

    // 清除路径图层
    if (cesiumPathLayer) {
      cesiumPathLayer.clear();
      viewer.removeLayer(cesiumPathLayer);
      cesiumPathLayer = null;
      console.log("已清除路径图层");
    }

    // 清除冲突图层
    if (cesiumConflictLayer) {
      cesiumConflictLayer.clear();
      viewer.removeLayer(cesiumConflictLayer);
      cesiumConflictLayer = null;
      console.log("已清除冲突图层");
    }

    // 重置分析结果
    conflictResult.value = null;
    showResult.value = false;

    console.log("隧道分析结果已完全清除");
  } catch (error) {
    console.error("清除隧道结果失败:", error);
  }
};

// 生命周期钩子
onMounted(() => {
  console.log("TunnelSimulation组件已挂载");

  // 验证Cesium环境
  if (mapEngine.value !== "cesium") {
    ElMessage.warning("隧道模拟功能仅在三维模式下可用");
  }
});

onUnmounted(() => {
  console.log("TunnelSimulation组件卸载，开始清理资源");

  try {
    clearTunnelResults();
    console.log("TunnelSimulation组件资源清理完成");
  } catch (error) {
    console.error("组件卸载清理失败:", error);
  }
});

/**
 * 计算相交点的高程
 * @param lineGeojson 管线的GeoJSON字符串
 * @param intersectPoint 相交点坐标 {lng, lat}
 * @param startElevation 起点高程(qdgc)
 * @param endElevation 终点高程(zdgc)
 * @returns 相交点的高程值
 */
const calcIntersectHeight = (
  lineGeojson: string,
  intersectPoint: { lng: number; lat: number },
  startElevation: number,
  endElevation: number
): number => {
  try {
    // 解析管线GeoJSON
    const lineFeature = JSON.parse(lineGeojson);

    if (!lineFeature || !lineFeature.coordinates) {
      console.warn("无效的管线GeoJSON数据");
      return startElevation; // 返回起点高程作为默认值
    }

    // 创建turf线段对象
    const line = turf.lineString(lineFeature.coordinates);

    // 创建相交点对象
    const point = turf.point([intersectPoint.lng, intersectPoint.lat]);

    // 计算相交点到线段起点的距离
    const lineStart = turf.point(lineFeature.coordinates[0]);
    const distanceToStart = turf.distance(lineStart, point, {
      units: "meters",
    });

    // 计算管线总长度
    const totalLength = turf.length(line, { units: "meters" });

    // 计算位置比例 (0-1之间)
    let ratio = 0;
    if (totalLength > 0) {
      ratio = Math.min(1, Math.max(0, distanceToStart / totalLength));
    }

    // 根据比例在起点和终点高程之间进行线性插值
    const interpolatedElevation =
      startElevation + (endElevation - startElevation) * ratio;

    console.log("高程计算详情:", {
      startElevation,
      endElevation,
      distanceToStart: distanceToStart.toFixed(2),
      totalLength: totalLength.toFixed(2),
      ratio: ratio.toFixed(3),
      interpolatedElevation: interpolatedElevation.toFixed(2),
    });

    return interpolatedElevation;
  } catch (error) {
    console.error("计算相交点高程失败:", error);
    // 发生错误时返回起点高程作为默认值
    return startElevation || 0;
  }
};

/**
 * 计算隧道在相交点位置的实际高程
 * @param intersectPoint 相交点坐标 {lng, lat}
 * @returns 隧道在该点的实际高程（考虑埋深后的高程）
 */
const calcTunnelHeightAtIntersection = (intersectPoint: {
  lng: number;
  lat: number;
}): number => {
  try {
    if (!tunnelPath.value || tunnelPath.value.length < 2) {
      console.warn("隧道路径数据不足");
      return 0;
    }

    // 创建隧道路径的坐标数组
    const tunnelCoordinates = tunnelPath.value.map((point) => [
      point.lng,
      point.lat,
    ]);

    // 创建turf线段对象
    const tunnelLine = turf.lineString(tunnelCoordinates);

    // 创建相交点对象
    const point = turf.point([intersectPoint.lng, intersectPoint.lat]);

    // 计算相交点到隧道起点的距离
    const tunnelStart = turf.point(tunnelCoordinates[0]);
    const distanceToStart = turf.distance(tunnelStart, point, {
      units: "meters",
    });

    // 计算隧道总长度
    const totalLength = turf.length(tunnelLine, { units: "meters" });

    // 计算位置比例 (0-1之间)
    let ratio = 0;
    if (totalLength > 0) {
      ratio = Math.min(1, Math.max(0, distanceToStart / totalLength));
    }

    // 获取隧道起点和终点的地面高程
    const startElevation = tunnelPath.value[0].alt || 20;
    const endElevation =
      tunnelPath.value[tunnelPath.value.length - 1].alt || 20;

    // 根据比例在起点和终点地面高程之间进行线性插值
    const groundElevation =
      startElevation + (endElevation - startElevation) * ratio;

    // 计算隧道实际高程（地面高程 - 埋深）
    const tunnelElevation = groundElevation - tunnelDepth.value;

    console.log("隧道高程计算详情:", {
      startElevation,
      endElevation,
      distanceToStart: distanceToStart.toFixed(2),
      totalLength: totalLength.toFixed(2),
      ratio: ratio.toFixed(3),
      groundElevation: groundElevation.toFixed(2),
      tunnelDepth: tunnelDepth.value,
      tunnelElevation: tunnelElevation.toFixed(2),
    });

    return tunnelElevation;
  } catch (error) {
    console.error("计算隧道相交点高程失败:", error);
    // 发生错误时返回默认值（地面高程20米 - 埋深）
    return 20 - tunnelDepth.value;
  }
};
</script>

<style scoped lang="scss">
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 550px;
  min-height: 130px;
  z-index: 1000;
}

.parameter-section {
  margin-bottom: 20px;
}

.input-section {
  margin-bottom: 16px;

  .label-text {
    font-weight: 500;
    color: #606266;
    margin-bottom: 8px;
    display: block;
  }

  .input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 350px;

    .requirement-input {
      flex: 1;
    }

    .shape-select {
      flex: 1;
    }

    .unit-text {
      color: #909399;
      font-size: 14px;
    }
  }
}



.path-section {
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(167, 220, 255, 0.2);

  .path-label {
    color: #a7dcff;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .path-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .path-text {
      color: #fff;
      font-size: 13px;
    }

    .point-count {
      color: #999;
      font-size: 12px;
    }
  }
}

.result-section {
  .result-label {
    color: #a7dcff;
    font-size: 14px;
    margin-bottom: 12px;
  }

  .result-content {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 4px;
    padding: 12px;
    border: 1px solid rgba(167, 220, 255, 0.1);
  }

  .tunnel-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .info-label {
        color: #a7dcff;
        font-size: 13px;
      }

      .info-value {
        color: #fff;
        font-size: 13px;
      }
    }
  }

  .conflict-result {
    margin-top: 16px;

    .conflict-summary {
      margin-bottom: 12px;

      .conflict-status {
        font-size: 14px;
        font-weight: 500;

        &.safe {
          color: #67c23a;
        }

        &.conflict {
          color: #f56c6c;
        }
      }

      .conflict-count {
        color: #f56c6c;
        margin-left: 8px;
        font-size: 12px;
      }
    }

    .conflict-list {
      .conflict-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        margin-bottom: 6px;
        background: rgba(245, 108, 108, 0.1);
        border-radius: 4px;
        border: 1px solid rgba(245, 108, 108, 0.2);
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(245, 108, 108, 0.2);
        }

        .conflict-info {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .pipe-name {
            color: #fff;
            font-size: 13px;
          }

          .distance {
            color: #f56c6c;
            font-size: 12px;
          }
        }

        .conflict-icon {
          color: #f56c6c;
          font-size: 16px;
        }
      }

      .more-conflicts {
        text-align: center;
        color: #999;
        font-size: 12px;
        margin-top: 8px;
      }
    }

    .analysis-actions {
      margin-top: 16px;
      padding-top: 12px;
      border-top: 1px solid rgba(167, 220, 255, 0.2);
      display: flex;
      gap: 8px;
      justify-content: center;

      .action-btn {
        background: linear-gradient(135deg, #409eff, #66b3ff);
        border: none;
        color: white;
        font-size: 12px;
        padding: 6px 12px;
        border-radius: 4px;

        &:hover {
          background: linear-gradient(135deg, #337ab7, #409eff);
        }
      }
    }
  }
}

:deep(.el-divider) {
  margin: 12px 0;

  .el-divider__text {
    color: #a7dcff;
    font-size: 13px;
  }
}
</style>
