<template>
  <div
    class="bg-#eef0f4 h-full p-2.5 box-border overflow-hidden"
    v-loading="Loading"
  >
    <div class="info-top">
      <div class="title">配置信息</div>
      <div class="mt-5 color-#2C3037">
        <div class="row">
          <div class="fx">
            <div>Redis版本:</div>
            <div>{{ dataList?.version }}</div>
          </div>
          <div class="fx pr-50">
            <div>运行模型:</div>
            <div>
              {{
                dataList?.mode == "standalone"
                  ? "单机"
                  : dataList?.mode == "sentinel"
                  ? "哨兵"
                  : "集群"
              }}
            </div>
          </div>
        </div>
        <div class="row bg">
          <div class="fx">
            <div>端口:</div>
            <div>{{ dataList?.port }}</div>
          </div>
          <div class="fx pr-50">
            <div>客户端数:</div>
            <div>{{ dataList?.clients }}</div>
          </div>
        </div>
        <div class="row">
          <div class="fx">
            <div>运行时间（天）:</div>
            <div>{{ dataList?.uptime }}</div>
          </div>
          <div class="fx pr-50">
            <div>使用内存:</div>
            <div>{{ dataList?.useMemory }}</div>
          </div>
        </div>
        <div class="row bg">
          <div class="fx">
            <div>使用CPU:</div>
            <div>{{ dataList?.useCpu }}</div>
          </div>
          <div class="fx pr-50">
            <div>内存配置:</div>
            <div>{{ dataList?.memoryConfig }}</div>
          </div>
        </div>
        <div class="row">
          <div class="fx">
            <div>AOF是否开启:</div>
            <div>{{ dataList?.aofEnabled }}</div>
          </div>
          <div class="fx pr-50">
            <div>RDB是否成功:</div>
            <div>{{ dataList?.rdbSuccess }}</div>
          </div>
        </div>
        <div class="row bg">
          <div class="fx">
            <div>Key数量:</div>
            <div>{{ dataList?.keyNumber }}</div>
          </div>
          <div class="fx pr-50">
            <div>网络入口/出口:</div>
            <div>{{ dataList?.network }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="info-btm">
      <div class="info-left">
        <div class="title">命令统计</div>
        <div ref="leftRef" class="left-ets"></div>
      </div>
      <div class="info-right">
        <div class="title">内存信息</div>
        <div ref="rtRef" class="left-ets"></div>
        <div class="price">
          <div class="box bg-#F3F7FF" style="margin-right: 80px">
            <div class="name">总储存量：</div>
            <div class="value color-#1966FF">{{ dataList?.peakMemory }}</div>
          </div>
          <div class="box bg-#E3FFF5">
            <div class="name">使用率：</div>
            <div class="value color-#3AC59A">
              {{ dataList?.memoryUsageRate }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { serveCache } from "@/api/server";
import { initServerEchart, initMemoryEchart } from "@/lib/echarts";
const { initChart } = useEchart();
const leftRef = ref<HTMLElement>();
const rtRef = ref<HTMLElement>();
const ComData: any = ref([]);
const Loading = ref(false);
const dataList = ref<any>([]);
// 已消耗
const arr: any = ref([]);
// 使用率
const total: any = ref([]);
const unit = ref<string>("");
const getList = async () => {
  try {
    Loading.value = true;
    const result = await serveCache();
    dataList.value = result.data;
    unit.value = dataList.value?.useMemory.replace(/[^a-zA-Z]/g, "");
    const a: any = /(\d+(\.\d+)?)/.exec(dataList.value?.useMemory);
    const b: any = /(\d+(\.\d+)?)/.exec(dataList.value?.memoryUsageRate);
    arr.value.push({ value: a[0] });
    total.value.push({ value: b[0] });
    Object.keys(result.data.commandStats).forEach((key) => {
      ComData.value.push({ value: result.data.commandStats[key], name: key });
    });
  } finally {
    Loading.value = false;
  }
};
const getInitEcharts = async () => {
  rtRef.value &&
    initChart(
      rtRef.value,
      initMemoryEchart(arr.value, unit.value, total.value)
    );
  leftRef.value && initChart(leftRef.value, initServerEchart(ComData.value));
};
onMounted(async () => {
  await getList();
  await getInitEcharts();
});
</script>
<style lang="scss" scoped>
@mixin base-box {
  width: 49.5%;
  height: calc(100vh - 480px);
  background: #ffffff;
  border-radius: 6px;
  margin-top: 10px;
  padding: 16px;
  box-sizing: border-box;
  // display: flex;
  // justify-content: center;
  // margin-right: 10px;
}
.title {
  width: 100%;
  font-size: 18px;
  font-family: Source Han Sans CN, Source Han Sans CN-Bold;
  font-weight: 700;
  color: #202020;
  // padding: 20px 0 0 20px;
  border-bottom: 1px solid #e8ecf2;
  padding-bottom: 10px;
}
.info-top {
  width: 100%;
  // height: 288px;
  background: #ffffff;
  border-radius: 6px;
  padding: 16px;
  box-sizing: border-box;
  .content {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    overflow: auto;
  }
  .line {
    display: flex;
    justify-content: space-between;
    // border-left: 4px solid #0066ff;
    margin: 26px 30px;
    div {
      width: 340px;
      margin-left: 6px;
    }
    span {
      margin-left: 10px;
    }
  }
}
.info-btm {
  display: flex;
  justify-content: space-between;

  .info-left {
    @include base-box();
    // margin-right: 10px;
  }
  .info-right {
    @include base-box();
    position: relative;
    // display: flex;
    // justify-content: center;
  }
}
.price {
  // width: 400px;
  display: flex;
  position: absolute;
  bottom: 6%;
  left: 50%;
  transform: translate(-50%, -50%);
  .box {
    // padding-right: 60px;
    width: 240px;
    height: 50px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    // padding: 10px 0;
    .name {
      // font-size: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN-Medium;
      font-weight: 500;
      color: #2c3037;
      // margin-bottom: 10px;
    }
    .value {
      font-size: 24px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      // color: #000000;
    }
  }
}
.left-ets {
  width: 100%;
  height: 400px;
}
.fx {
  width: 460px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 10px;
}
.row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
}
.bg {
  width: 100%;
  background: #f7faff;
}
</style>
