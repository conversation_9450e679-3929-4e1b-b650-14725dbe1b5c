/**
 * @fileoverview 地图标注类型定义
 * @description 定义地图标注的标准GeoJSON Feature数据结构，与二维标绘功能保持完全一致
 * <AUTHOR>
 * @version 3.0.0
 */

import type { EngineType } from "./plotting";

/**
 * @description 标注样式配置
 */
export interface AnnotationStyle {
  /** 标注颜色 */
  color: string;
  /** 字体大小 */
  fontSize: number;
  /** X轴偏移量（像素） */
  offsetX: number;
  /** Y轴偏移量（像素） */
  offsetY: number;
  /** 透明度 (0-1) */
  opacity: number;
}

/**
 * @description GeoJSON Point几何体（标准格式，只包含经纬度）
 */
export interface PointGeometry {
  type: 'Point';
  coordinates: [number, number]; // [lng, lat] - 标准GeoJSON格式
}

/**
 * @description GeoJSON Feature Properties（包含样式和其他属性）
 */
export interface AnnotationFeatureProperties {
  /** 要素类型，固定为 'annotation' */
  type: 'annotation';
  /** 标注样式 */
  style: AnnotationStyle;
  /** 高程（仅三维标注使用） */
  alt?: number;
  /** 其他扩展属性 */
  [key: string]: any;
}

/**
 * @description 标准GeoJSON Feature
 */
export interface AnnotationGeoJSONFeature {
  type: 'Feature';
  geometry: PointGeometry;
  properties: AnnotationFeatureProperties;
}

/**
 * @description 地图标注要素（统一的标准格式）
 */
export interface IMapAnnotation {
  /** 标注唯一标识符 */
  id: string;
  /** 标注名称 */
  name: string;
  /** 标注描述 */
  description: string;
  /** 标准GeoJSON Feature */
  geojson: AnnotationGeoJSONFeature;
  /** 地图引擎类型 */
  engineType: EngineType;
  /** 创建时间戳 */
  createTime: number;
  /** 修改时间戳 */
  modifyTime: number;
  /** 是否可见 */
  visible: boolean;
  /** 是否锁定编辑 */
  locked: boolean;
}



/**
 * @description 标注创建参数接口
 */
export interface CreateAnnotationParams {
  /** 标注名称 */
  name: string;
  /** 经度 */
  lng: number;
  /** 纬度 */
  lat: number;
  /** 高度（米，仅三维标注需要） */
  alt?: number;
  /** 样式配置 */
  style: AnnotationStyle;
  /** 标注描述（可选） */
  description?: string;
  /** 引擎类型（可选，如未提供则根据上下文推断） */
  engineType?: 'cesium' | 'maplibre';
}

/**
 * @description 标注数据存储结构（统一的标准格式）
 */
export interface AnnotationDataStorage {
  /** 版本信息 */
  version: {
    version: string;
    lastUpdate: number;
    featureCount: number;
  };
  /** 标注要素数组 */
  features: IMapAnnotation[];
  /** 元数据 */
  metadata: {
    created: number;
    modified: number;
    description?: string;
  };
}

/**
 * @description 标注搜索结果接口
 */
export interface AnnotationSearchResult {
  /** 匹配的标注列表 */
  annotations: IMapAnnotation[];
  /** 搜索关键词 */
  keyword: string;
  /** 总数量 */
  total: number;
  /** 过滤后数量 */
  filtered: number;
} 