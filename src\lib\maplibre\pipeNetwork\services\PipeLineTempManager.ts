/**
 * @fileoverview 管线临时图层管理器
 * @description 专门管理管线绘制过程中的临时图层显示
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import type { Map as MapLibreMap } from 'maplibre-gl';
import { Subject } from 'rxjs';

/**
 * @interface TempLineNode
 * @description 临时管线节点信息
 */
export interface TempLineNode {
  /** 节点ID */
  nodeId: string;
  /** 节点编号 */
  nodeCode: string;
  /** 坐标 */
  coordinates: [number, number];
  /** 节点详情数据 */
  nodeData?: any;
}

/**
 * @interface TempLineData
 * @description 临时管线数据
 */
export interface TempLineData {
  /** 起始点 */
  startNode?: TempLineNode;
  /** 终止点 */
  endNode?: TempLineNode;
  /** 管线坐标 */
  lineCoordinates?: [number, number][];
}

/**
 * @class PipeLineTempManager
 * @description 管线临时图层管理器
 */
export class PipeLineTempManager {
  private map: MapLibreMap;
  private eventSubject = new Subject<{ type: string; data: any }>();
  
  // 图层ID常量
  private readonly TEMP_LINE_NODES_LAYER_ID = 'temp-line-nodes';
  private readonly TEMP_LINE_LAYER_ID = 'temp-line';
  private readonly TEMP_LINE_NODES_SOURCE_ID = 'temp-line-nodes-source';
  private readonly TEMP_LINE_SOURCE_ID = 'temp-line-source';
  
  // 临时数据存储
  private tempLineData: TempLineData = {};

  /**
   * @constructor
   * @param map MapLibre地图实例
   */
  constructor(map: MapLibreMap) {
    this.map = map;
    this.initializeLayers();
    console.log('PipeLineTempManager: 初始化完成');
  }

  /**
   * @function initializeLayers
   * @description 初始化临时图层
   */
  private initializeLayers(): void {
    this.createTempNodesLayer();
    this.createTempLineLayer();
  }

  /**
   * @function createTempNodesLayer
   * @description 创建临时管点图层
   */
  private createTempNodesLayer(): void {
    // 添加数据源
    if (!this.map.getSource(this.TEMP_LINE_NODES_SOURCE_ID)) {
      this.map.addSource(this.TEMP_LINE_NODES_SOURCE_ID, {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: []
        }
      });
    }

    // 添加图层
    if (!this.map.getLayer(this.TEMP_LINE_NODES_LAYER_ID)) {
      this.map.addLayer({
        id: this.TEMP_LINE_NODES_LAYER_ID,
        type: 'circle',
        source: this.TEMP_LINE_NODES_SOURCE_ID,
        paint: {
          'circle-radius': 8,
          'circle-color': '#00ff00', // 绿色区分管点绘制的红色
          'circle-stroke-width': 2,
          'circle-stroke-color': '#ffffff'
        }
      });
    }
  }

  /**
   * @function createTempLineLayer
   * @description 创建临时管线图层
   */
  private createTempLineLayer(): void {
    // 添加数据源
    if (!this.map.getSource(this.TEMP_LINE_SOURCE_ID)) {
      this.map.addSource(this.TEMP_LINE_SOURCE_ID, {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: []
        }
      });
    }

    // 添加图层
    if (!this.map.getLayer(this.TEMP_LINE_LAYER_ID)) {
      this.map.addLayer({
        id: this.TEMP_LINE_LAYER_ID,
        type: 'line',
        source: this.TEMP_LINE_SOURCE_ID,
        paint: {
          'line-color': '#ffff00', // 黄色临时管线
          'line-width': 4,
          'line-opacity': 0.8
        }
      });
    }
  }

  /**
   * @function addStartNode
   * @description 添加起始点
   */
  public addStartNode(nodeInfo: TempLineNode): void {
    this.tempLineData.startNode = nodeInfo;
    this.updateNodesDisplay();
    
    this.eventSubject.next({
      type: 'start-node-added',
      data: nodeInfo
    });
    
    console.log('PipeLineTempManager: 添加起始点', nodeInfo);
  }

  /**
   * @function addEndNode
   * @description 添加终止点并创建连线
   */
  public addEndNode(nodeInfo: TempLineNode): void {
    this.tempLineData.endNode = nodeInfo;
    
    // 创建连线
    if (this.tempLineData.startNode && this.tempLineData.endNode) {
      this.tempLineData.lineCoordinates = [
        this.tempLineData.startNode.coordinates,
        this.tempLineData.endNode.coordinates
      ];
      this.updateLineDisplay();
    }
    
    this.updateNodesDisplay();
    
    this.eventSubject.next({
      type: 'end-node-added',
      data: nodeInfo
    });
    
    console.log('PipeLineTempManager: 添加终止点', nodeInfo);
  }

  /**
   * @function updateStartNode
   * @description 更新起始点
   */
  public updateStartNode(nodeInfo: TempLineNode): void {
    this.tempLineData.startNode = nodeInfo;
    
    // 如果已有终止点，重新绘制连线
    if (this.tempLineData.endNode) {
      this.tempLineData.lineCoordinates = [
        this.tempLineData.startNode.coordinates,
        this.tempLineData.endNode.coordinates
      ];
      this.updateLineDisplay();
    }
    
    this.updateNodesDisplay();
    
    this.eventSubject.next({
      type: 'start-node-updated',
      data: nodeInfo
    });
  }

  /**
   * @function updateEndNode
   * @description 更新终止点
   */
  public updateEndNode(nodeInfo: TempLineNode): void {
    this.tempLineData.endNode = nodeInfo;
    
    // 如果已有起始点，重新绘制连线
    if (this.tempLineData.startNode) {
      this.tempLineData.lineCoordinates = [
        this.tempLineData.startNode.coordinates,
        this.tempLineData.endNode.coordinates
      ];
      this.updateLineDisplay();
    }
    
    this.updateNodesDisplay();
    
    this.eventSubject.next({
      type: 'end-node-updated',
      data: nodeInfo
    });
  }

  /**
   * @function clearStartNode
   * @description 清除起始点
   */
  public clearStartNode(): void {
    this.tempLineData.startNode = undefined;
    this.tempLineData.lineCoordinates = undefined;
    
    this.updateNodesDisplay();
    this.updateLineDisplay();
    
    this.eventSubject.next({
      type: 'start-node-cleared',
      data: null
    });
    
    console.log('PipeLineTempManager: 已清除起始点');
  }

  /**
   * @function clearEndNode
   * @description 清除终止点
   */
  public clearEndNode(): void {
    this.tempLineData.endNode = undefined;
    this.tempLineData.lineCoordinates = undefined;
    
    this.updateNodesDisplay();
    this.updateLineDisplay();
    
    this.eventSubject.next({
      type: 'end-node-cleared',
      data: null
    });
    
    console.log('PipeLineTempManager: 已清除终止点');
  }

  /**
   * @function updateNodesDisplay
   * @description 更新管点显示
   */
  private updateNodesDisplay(): void {
    const features: any[] = [];
    
    // 添加起始点
    if (this.tempLineData.startNode) {
      features.push({
        type: 'Feature',
        properties: {
          type: 'start-node',
          nodeCode: this.tempLineData.startNode.nodeCode,
          nodeId: this.tempLineData.startNode.nodeId
        },
        geometry: {
          type: 'Point',
          coordinates: this.tempLineData.startNode.coordinates
        }
      });
    }
    
    // 添加终止点
    if (this.tempLineData.endNode) {
      features.push({
        type: 'Feature',
        properties: {
          type: 'end-node',
          nodeCode: this.tempLineData.endNode.nodeCode,
          nodeId: this.tempLineData.endNode.nodeId
        },
        geometry: {
          type: 'Point',
          coordinates: this.tempLineData.endNode.coordinates
        }
      });
    }
    
    // 更新数据源
    const source = this.map.getSource(this.TEMP_LINE_NODES_SOURCE_ID) as any;
    if (source) {
      source.setData({
        type: 'FeatureCollection',
        features
      });
    }
  }

  /**
   * @function updateLineDisplay
   * @description 更新管线显示
   */
  private updateLineDisplay(): void {
    const source = this.map.getSource(this.TEMP_LINE_SOURCE_ID) as any;
    if (!source) return;
    
    // 如果没有线条坐标，清空显示
    if (!this.tempLineData.lineCoordinates || this.tempLineData.lineCoordinates.length < 2) {
      source.setData({
        type: 'FeatureCollection',
        features: []
      });
      return;
    }
    
    const lineFeature = {
      type: 'Feature',
      properties: {
        type: 'temp-line'
      },
      geometry: {
        type: 'LineString',
        coordinates: this.tempLineData.lineCoordinates
      }
    };
    
    // 更新数据源
    source.setData({
      type: 'FeatureCollection',
      features: [lineFeature]
    });
  }

  /**
   * @function getTempLineData
   * @description 获取临时管线数据
   */
  public getTempLineData(): TempLineData {
    return { ...this.tempLineData };
  }

  /**
   * @function setLineForEdit
   * @description 为编辑模式设置管线的临时显示
   * @param lineData 管线数据
   */
  public setLineForEdit(lineData: any): void {
    try {
      console.log('PipeLineTempManager: 为编辑模式设置管线临时显示', lineData);
      
      // 清除现有的临时显示
      this.clearAll();
      
      // 提取起止点信息
      const { startNode, endNode } = this.extractNodesFromLineData(lineData);
      
      if (startNode && endNode) {
        // 设置临时数据
        this.tempLineData.startNode = startNode;
        this.tempLineData.endNode = endNode;
        
        // 创建管线坐标
        if (lineData.geojson && typeof lineData.geojson === 'string') {
          try {
            const geojsonData = JSON.parse(lineData.geojson);
            if (geojsonData.type === 'LineString' && geojsonData.coordinates) {
              this.tempLineData.lineCoordinates = geojsonData.coordinates;
            } else {
              // 如果GeoJSON无效，使用起止点坐标
              this.tempLineData.lineCoordinates = [startNode.coordinates, endNode.coordinates];
            }
          } catch (error) {
            console.warn('解析管线GeoJSON失败，使用起止点坐标:', error);
            this.tempLineData.lineCoordinates = [startNode.coordinates, endNode.coordinates];
          }
        } else {
          // 如果没有GeoJSON数据，使用起止点坐标
          this.tempLineData.lineCoordinates = [startNode.coordinates, endNode.coordinates];
        }
        
        // 更新显示
        this.updateNodesDisplay();
        this.updateLineDisplay();
        
        // 触发事件
        this.eventSubject.next({
          type: 'line-set-for-edit',
          data: { lineData, startNode, endNode }
        });
        
        console.log('PipeLineTempManager: 编辑模式临时显示设置完成', {
          startNode: startNode.nodeCode,
          endNode: endNode.nodeCode,
          lineCoordinates: this.tempLineData.lineCoordinates?.length
        });
      } else {
        console.warn('PipeLineTempManager: 无法从管线数据中提取有效的起止点信息');
      }
    } catch (error) {
      console.error('PipeLineTempManager: 设置编辑模式临时显示失败:', error);
    }
  }

  /**
   * @function extractNodesFromLineData
   * @description 从管线数据中提取起止点信息
   * @param lineData 管线数据
   * @returns 起止点信息
   */
  private extractNodesFromLineData(lineData: any): { startNode?: TempLineNode; endNode?: TempLineNode } {
    try {
      // 检查必要的数据
      if (!lineData.qdbh || !lineData.zdbh) {
        console.warn('管线数据缺少起止点编号');
        return {};
      }
      const geojson = JSON.parse(lineData.geojson);
      const coordinates = geojson.coordinates;
      const startX = coordinates[0][0];
      const startY = coordinates[0][1];
      const endX = coordinates[1][0];
      const endY = coordinates[1][1];

      
      if (typeof startX !== 'number' || typeof startY !== 'number' || 
          typeof endX !== 'number' || typeof endY !== 'number') {
        console.warn('管线数据缺少有效的坐标信息');
        return {};
      }
      
      // 创建起始点
      const startNode: TempLineNode = {
        nodeId: `temp-start-${lineData.qdbh}`,
        nodeCode: lineData.qdbh,
        coordinates: [startX, startY],
        nodeData: {
          dmgc: lineData.qdgc, // 地面高程
          nodeType: 'start'
        }
      };
      
      // 创建终止点
      const endNode: TempLineNode = {
        nodeId: `temp-end-${lineData.zdbh}`,
        nodeCode: lineData.zdbh,
        coordinates: [endX, endY],
        nodeData: {
          dmgc: lineData.zdgc, // 地面高程
          nodeType: 'end'
        }
      };
      
      return { startNode, endNode };
    } catch (error) {
      console.error('从管线数据提取起止点信息失败:', error);
      return {};
    }
  }

  /**
   * @function clearAll
   * @description 清除所有临时显示
   */
  public clearAll(): void {
    this.tempLineData = {};
    
    // 清空数据源
    const nodesSource = this.map.getSource(this.TEMP_LINE_NODES_SOURCE_ID) as any;
    if (nodesSource) {
      nodesSource.setData({
        type: 'FeatureCollection',
        features: []
      });
    }
    
    const lineSource = this.map.getSource(this.TEMP_LINE_SOURCE_ID) as any;
    if (lineSource) {
      lineSource.setData({
        type: 'FeatureCollection',
        features: []
      });
    }
    
    this.eventSubject.next({
      type: 'cleared',
      data: null
    });
    
    console.log('PipeLineTempManager: 已清除所有临时显示');
  }

  /**
   * @function getEvents
   * @description 获取事件流
   */
  public getEvents() {
    return this.eventSubject.asObservable();
  }

  /**
   * @function destroy
   * @description 销毁管理器
   */
  public destroy(): void {
    this.clearAll();
    
    // 移除图层
    if (this.map.getLayer(this.TEMP_LINE_NODES_LAYER_ID)) {
      this.map.removeLayer(this.TEMP_LINE_NODES_LAYER_ID);
    }
    if (this.map.getLayer(this.TEMP_LINE_LAYER_ID)) {
      this.map.removeLayer(this.TEMP_LINE_LAYER_ID);
    }
    
    // 移除数据源
    if (this.map.getSource(this.TEMP_LINE_NODES_SOURCE_ID)) {
      this.map.removeSource(this.TEMP_LINE_NODES_SOURCE_ID);
    }
    if (this.map.getSource(this.TEMP_LINE_SOURCE_ID)) {
      this.map.removeSource(this.TEMP_LINE_SOURCE_ID);
    }
    
    this.eventSubject.complete();
    console.log('PipeLineTempManager: 已销毁');
  }
} 