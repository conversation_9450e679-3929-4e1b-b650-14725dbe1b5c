<!--
 * @Description: 地图标注组件
 * @Date: 2022-04-22 08:54:28
 * @Author: GISerZ
 * @LastEditor: 项目开发团队
 * @LastEditTime: 2023-08-10 15:00:00
-->
<template>
  <custom-card
    @closeHandler="close"
    v-show="mainItem.show"
    :main-item="mainItem"
    :title="mainItem.title"
  >
    <el-row type="flex" justify="space-between" style="padding: 10px 0">
      <el-input
        placeholder="搜索标注名称..."
        v-model="searchData"
        class="search_ipt"
        @blur="blurHandler"
        @keyup.enter="searchEvents"
        @clear="searchEvents"
        clearable
      >
        <template #suffix>
          <el-icon class="search_icon" @click="searchEvents">
            <Search />
          </el-icon>
        </template>
      </el-input>
      <el-button
        type="primary"
        class="primary-btn"
        @click="addNewBookMark"
        :disabled="isSelectingPosition"
      >
        {{ isSelectingPosition ? "请点击地图选择位置..." : "添加" }}
      </el-button>
    </el-row>
    <el-table
      :data="filteredBookMarkList"
      height="250"
      style="width: 300px"
      class="routeCt"
      :row-class-name="tableRowClassName"
    >
      <el-table-column prop="name" label="标注名称"> </el-table-column>
      <el-table-column label="操作" min-width="60">
        <template v-slot="scope">
          <el-button
            type="text"
            style="color: #1966ff"
            @click.native.prevent="locBookMark(scope.row)"
          >
            定位
          </el-button>
          <el-button
            type="text"
            style="color: #ff7373"
            @click.native.prevent="removeBookMarkById(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex justify-between items-center">
      <div>
        <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
      </div>
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :total="total"
        :pager-count="5"
        layout="prev, pager, next, jumper"
        class="pagination"
        background
        small
      ></el-pagination>
    </div>
  </custom-card>
  <el-dialog
    custom-class
    title="新建标注"
    v-model="addImgDigVisible"
    @close="closeDialog"
    :before-close="closeDialog"
    width="450px"
    :destroy-on-close="true"
  >
    <i
      class="close-icon close-icon2 iconfont icon-guanbi-o"
      @click="closeDialog"
    ></i>
    <el-form class="admin-sub-form" label-width="auto" :model="programInfo">
      <el-form-item label="标注名称">
        <el-input
          v-model="programInfo.name"
          placeholder="请输入标注名称"
          maxlength="20"
          show-word-limit
          clearable
        ></el-input>
      </el-form-item>

      <el-form-item label="标注高程">
        <el-input-number
          v-model="programInfo.alt"
          :min="0"
          :max="50000"
          :step="100"
          controls-position="right"
          placeholder="标注高程(米)"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="标注颜色">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-color-picker
            v-model="programInfo.color"
            show-alpha
            color-format="hex"
            :predefine="predefinedColors"
          />
          <el-input
            v-model="programInfo.color"
            placeholder="#FFFF00"
            style="flex: 1"
            maxlength="9"
          />
        </div>
      </el-form-item>

      <el-form-item label="字体大小">
        <el-select
          v-model="programInfo.fontSize"
          placeholder="选择字体大小"
          style="width: 100%"
        >
          <el-option label="12px (小)" value="12" />
          <el-option label="14px (默认)" value="14" />
          <el-option label="16px (中)" value="16" />
          <el-option label="18px (大)" value="18" />
          <el-option label="20px (特大)" value="20" />
          <el-option label="24px (超大)" value="24" />
        </el-select>
      </el-form-item>

      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="X偏移">
            <el-input-number
              v-model="programInfo.offsetX"
              :min="-100"
              :max="100"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Y偏移">
            <el-input-number
              v-model="programInfo.offsetY"
              :min="-100"
              :max="100"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label=" ">
        <div style="font-size: 12px; color: #666; line-height: 1.5">
          <div style="margin-bottom: 5px">
            <i class="el-icon-info" style="margin-right: 5px"></i>
            <span v-if="selectedPosition">
              标注位置：经度 {{ selectedPosition.lng }}°，纬度
              {{ selectedPosition.lat }}°
            </span>
            <span v-else style="color: #999"> 请先在地图上选择标注位置 </span>
          </div>
          <div style="color: #999">
            偏移量：X轴正值向右，Y轴正值向上（单位：像素）
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <ul class="btn_group">
        <button class="small_btn ripple" @click="addEvents">新 建</button>
        <button class="small_btn ripple" @click="closeDialog">关 闭</button>
      </ul>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  ref,
  computed,
  reactive,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
} from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { useDialogStore } from "@/stores/Dialogs";
import { useAnnotationStore } from "@/stores/AnnotationStore";
import { AppCesium } from "@/lib/cesium/AppCesium";
import type {
  IMapAnnotation,
  CreateAnnotationParams,
} from "@/types/annotation";

/**
 * 组件属性定义
 */
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});

// 存储实例
const annotationStore = useAnnotationStore();
const { annotationList } = storeToRefs(annotationStore);

// 响应式数据
const searchData = ref("");
const addImgDigVisible = ref(false);
const isSelectingPosition = ref(false); // 是否正在选择位置
const selectedPosition = ref<{ lng: number; lat: number; alt: number } | null>(
  null
); // 选中的位置

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);
const pageSizes = ref([5, 10, 20, 50]);
const total = ref(0);

// 分页事件处理函数
/**
 * 处理页码变更
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

/**
 * 处理页面大小变更
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

/**
 * 预定义颜色选项
 */
const predefinedColors = [
  "#FFFF00", // 黄色
  "#FF0000", // 红色
  "#00FF00", // 绿色
  "#0000FF", // 蓝色
  "#FF8000", // 橙色
  "#800080", // 紫色
  "#00FFFF", // 青色
  "#FF69B4", // 粉色
  "#FFFFFF", // 白色
  "#000000", // 黑色
];

/**
 * 程序信息表单数据
 */
const programInfo = reactive({
  name: "",
  alt: 1000,
  color: "#FFFF00",
  fontSize: "14",
  offsetX: 0,
  offsetY: 0,
});

// Cesium图层管理
let annotationLayer: BC.VectorLayer | null = null;
const annotationLayerMap = new Map<string, BC.Label>(); // 存储标注ID与Label对象的映射
const isDataLoading = ref(false); // 数据加载状态
const isLayerInitialized = ref(false); // 图层初始化状态

/**
 * 搜索过滤计算属性
 * 只进行搜索过滤，不处理分页
 */
const searchFilteredList = computed((): IMapAnnotation[] => {
  // 按搜索关键词过滤
  if (!searchData.value.trim()) {
    return [...annotationList.value];
  }

  const searchTerm = searchData.value.toLowerCase().trim();
  return annotationList.value.filter(
    (annotation) =>
      annotation.name.toLowerCase().includes(searchTerm) ||
      (annotation.description &&
        annotation.description.toLowerCase().includes(searchTerm))
  );
});

/**
 * 分页后的数据列表
 * 在搜索过滤基础上进行分页
 */
const filteredBookMarkList = computed((): IMapAnnotation[] => {
  const filtered = searchFilteredList.value;

  // 更新总数
  total.value = filtered.length;

  // 计算分页
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;

  return filtered.slice(start, end);
});

/**
 * 初始化Cesium标注图层
 */
const initAnnotationLayer = (): void => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    if (!viewer) {
      console.error("无法获取Cesium viewer实例");
      return;
    }

    // 创建标注图层
    annotationLayer = new BC.VectorLayer("mapAnnotationLayer");
    viewer.addLayer(annotationLayer);
    isLayerInitialized.value = true;

    // 如果数据已经存在，立即加载标注
    if (annotationList.value.length > 0) {
      loadExistingAnnotations();
    }
  } catch (error) {
    console.error("初始化标注图层失败:", error);
    ElMessage.error("初始化标注图层失败");
    isLayerInitialized.value = false;
  }
};

/**
 * 加载已存在的标注到地图
 */
const loadExistingAnnotations = (): void => {
  if (!annotationLayer) {
    return;
  }

  try {
    // 清空现有的标注
    annotationLayerMap.forEach((label) => {
      try {
        annotationLayer?.removeOverlay(label);
      } catch (error) {
        console.error("移除标注失败:", error);
      }
    });
    annotationLayerMap.clear();

    // 从store获取当前的标注列表
    const cesiumAnnotations = annotationList.value;

    // 重新创建所有标注
    cesiumAnnotations.forEach((annotation) => {
      createLabelOnMap(annotation);
    });
  } catch (error) {
    console.error("加载标注失败:", error);
  }
};

/**
 * 在地图上创建标注
 */
const createLabelOnMap = (annotation: IMapAnnotation): BC.Label | null => {
  if (!annotationLayer) {
    console.error("标注图层未初始化");
    return null;
  }

  try {
    // 检查是否已存在该标注，如果存在则先移除
    if (annotationLayerMap.has(annotation.id)) {
      const existingLabel = annotationLayerMap.get(annotation.id);
      if (existingLabel) {
        annotationLayer.removeOverlay(existingLabel);
      }
      annotationLayerMap.delete(annotation.id);
    }

    // 从GeoJSON坐标中提取经纬度
    const [lng, lat] = annotation.geojson.geometry.coordinates;
    // 从properties中提取高度（如果存在）
    const alt = annotation.geojson.properties.alt || 1000;
    const position = new BC.Position(lng, lat, alt);
    const label = new BC.Label(position, annotation.name);

    // 从properties中提取样式信息
    const style = annotation.geojson.properties.style;
    label.setStyle({
      fillColor: BC.Color.fromCssColorString(style.color) || BC.Color.YELLOW,
      font: `${style.fontSize}px Microsoft YaHei`,
      offsetX: style.offsetX || 0,
      offsetY: style.offsetY || 0,
    });

    // 添加到图层
    annotationLayer.addOverlay(label);

    // 保存映射关系
    annotationLayerMap.set(annotation.id, label);

    return label;
  } catch (error) {
    console.error(`创建标注"${annotation.name}"失败:`, error);
    return null;
  }
};

/**
 * 从地图上移除标注
 */
const removeLabelFromMap = (annotationId: string): void => {
  if (!annotationLayer) return;

  const label = annotationLayerMap.get(annotationId);
  if (label) {
    try {
      annotationLayer.removeOverlay(label);
      annotationLayerMap.delete(annotationId);
    } catch (error) {
      console.error("从地图移除标注失败:", error);
    }
  } else {
    // 如果映射关系丢失，重新加载所有标注
    try {
      const overlays = annotationLayer.getOverlays();
      if (overlays.length > 0) {
        loadExistingAnnotations();
      }
    } catch (error) {
      console.error("重新加载标注失败:", error);
    }
  }
};

/**
 * 关闭组件
 */
const close = () => {
  useDialogStore().closeDialog("MapAnnotation");
};

/**
 * 搜索事件
 */
const searchEvents = () => {
  // 搜索时重置分页
  currentPage.value = 1;
};

/**
 * 搜索框失焦事件
 */
const blurHandler = () => {
  // 失焦时触发搜索，重置分页
  currentPage.value = 1;
};

/**
 * 添加新标注 - 点击地图选择位置
 */
const addNewBookMark = () => {
  try {
    if (isSelectingPosition.value) {
      ElMessage.warning("请先完成当前位置选择");
      return;
    }

    // 开始选择位置
    isSelectingPosition.value = true;
    ElMessage.info("请在地图上点击选择标注位置");

    const viewer = AppCesium.getInstance().getViewer();
    if (!viewer) {
      ElMessage.error("无法获取Cesium viewer实例");
      isSelectingPosition.value = false;
      return;
    }

    handleMapClick();
  } catch (error) {
    console.error("开始选择位置失败:", error);
    ElMessage.error("开始选择位置失败");
    isSelectingPosition.value = false;
  }
};

/**
 * 处理地图点击事件
 */
const handleMapClick = () => {
  try {
    const viewer = AppCesium.getInstance().getViewer();

    isSelectingPosition.value = false;

    // 设置鼠标样式
    viewer.canvas.style.cursor = "crosshair";
    AppCesium.getInstance()
      .getViewer()
      .once(
        BC.MouseEventType.LEFT_CLICK,
        (e: any) => {
          const wgs84Position = e.wgs84Position;
          if (!wgs84Position) {
            ElMessage.error("无法获取地面坐标，请点击地面位置");
            return;
          }
          // 恢复鼠标样式
          viewer.canvas.style.cursor = "default";
          // 转换为经纬度和高度
          const clickedPosition = {
            lng: Number(wgs84Position.lng.toFixed(6)),
            lat: Number(wgs84Position.lat.toFixed(6)),
            alt: Number(wgs84Position.alt.toFixed(2)),
          };

          // 保存选中的位置
          selectedPosition.value = clickedPosition;

          // 重置表单为默认值
          programInfo.name = "";
          programInfo.alt = clickedPosition.alt;
          programInfo.color = "#FFFF00";
          programInfo.fontSize = "14";
          programInfo.offsetX = 0;
          programInfo.offsetY = 0;

          // 显示对话框
          addImgDigVisible.value = true;

          // ElMessage.success(
          //   `已选择位置：经度 ${clickedPosition.lng}°，纬度 ${clickedPosition.lat}°`
          // );
        },
        this
      );
  } catch (error) {
    console.error("处理地图点击失败:", error);
    ElMessage.error("处理地图点击失败");

    // 清理状态
    const viewer = AppCesium.getInstance().getViewer();
    if (viewer) {
      viewer.canvas.style.cursor = "default";
    }
    isSelectingPosition.value = false;
  }
};

/**
 * 定位到指定标注
 */
const locBookMark = async (annotation: IMapAnnotation) => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = BC.Namespace;

    // 从GeoJSON坐标中提取经纬度
    const [lng, lat] = annotation.geojson.geometry.coordinates;
    // 从properties中提取高度（如果存在）
    const alt = annotation.geojson.properties.alt || 1000;

    // 创建目标位置
    const destination = Cesium.Cartesian3.fromDegrees(
      lng,
      lat,
      alt + 500 // 在标注上方500米
    );

    // 飞行到目标位置
    await viewer.camera.flyTo({
      destination: destination,
      duration: 2.0,
    });

    ElMessage.success(`已定位到标注"${annotation.name}"`);
  } catch (error) {
    console.error("定位到标注失败:", error);
    ElMessage.error(`定位到标注"${annotation.name}"失败`);
  }
};

/**
 * 删除标注
 */
const removeBookMarkById = async (id: string) => {
  try {
    const annotation = annotationStore.getAnnotationById(id);
    if (!annotation) {
      ElMessage.warning("标注不存在");
      return;
    }

    // 先从地图上移除
    removeLabelFromMap(id);

    // 从存储中删除（异步操作）
    const success = await annotationStore.removeAnnotation(id);

    if (success) {
      ElMessage.success(`标注"${annotation.name}"删除成功`);

      // 如果删除后发现映射关系不一致，重新加载
      if (annotationLayerMap.size !== annotationList.value.length) {
        loadExistingAnnotations();
      }
    } else {
      ElMessage.error("删除标注失败");
      loadExistingAnnotations();
    }
  } catch (error) {
    console.error("删除标注失败:", error);
    ElMessage.error("删除标注失败");
    loadExistingAnnotations();
  }
};

/**
 * 关闭添加对话框
 */
const closeDialog = () => {
  addImgDigVisible.value = false;
  // 重置表单
  programInfo.name = "";
  programInfo.alt = 1000;
  programInfo.color = "#FFFF00";
  programInfo.fontSize = "14";
  programInfo.offsetX = 0;
  programInfo.offsetY = 0;
  // 清空选中位置
  selectedPosition.value = null;
};

/**
 * 确认添加标注
 */
const addEvents = async () => {
  if (!programInfo.name.trim()) {
    ElMessage.warning("请输入标注名称");
    return;
  }

  try {
    // 检查名称是否已存在
    if (annotationStore.isNameExists(programInfo.name.trim())) {
      ElMessage.warning("标注名称已存在，请换一个名称");
      return;
    }

    // 检查是否有选中的位置
    if (!selectedPosition.value) {
      ElMessage.error("未选择标注位置");
      return;
    }

    // 创建标注参数（engineType固定为cesium）
    const params: CreateAnnotationParams = {
      name: programInfo.name.trim(),
      lng: selectedPosition.value.lng,
      lat: selectedPosition.value.lat,
      alt: programInfo.alt,
      style: {
        color: programInfo.color,
        fontSize: parseInt(programInfo.fontSize),
        offsetX: programInfo.offsetX,
        offsetY: programInfo.offsetY,
        opacity: 1.0,
      },
      description: "", // 添加描述字段
    };

    // 添加到存储（异步操作）
    const newAnnotation = await annotationStore.addAnnotation(params);

    // 在地图上创建标注
    const label = createLabelOnMap(newAnnotation);

    if (!label) {
      ElMessage.warning("标注添加到地图失败，但数据已保存");
    }

    // 关闭对话框
    addImgDigVisible.value = false;
    // 重置表单
    programInfo.name = "";
    programInfo.alt = 1000;
    programInfo.color = "#FFFF00";
    programInfo.fontSize = "14";
    programInfo.offsetX = 0;
    programInfo.offsetY = 0;
    // 清空选中位置
    selectedPosition.value = null;

    ElMessage.success(`标注"${newAnnotation.name}"添加成功`);
  } catch (error) {
    console.error("添加标注失败:", error);
    ElMessage.error("添加标注失败");
  }
};



/**
 * 清理资源
 */
const cleanup = (): void => {
  try {
    // 清理选择位置状态
    if (isSelectingPosition.value) {
      isSelectingPosition.value = false;
      const viewer = AppCesium.getInstance().getViewer();
      if (viewer) {
        viewer.canvas.style.cursor = "default";
      }
    }

    // 清理标注图层
    if (annotationLayer) {
      const viewer = AppCesium.getInstance().getViewer();
      if (viewer) {
        viewer.removeLayer(annotationLayer);
      }
      annotationLayer = null;
    }

    // 清理状态
    annotationLayerMap.clear();
    selectedPosition.value = null;
    isDataLoading.value = false;
    isLayerInitialized.value = false;
  } catch (error) {
    console.error("清理资源失败:", error);
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
/**
 * 监听annotationList数据变化
 * 当store数据更新时，重新加载地图标注
 */
watch(
  () => annotationList.value,
  () => {
    if (isLayerInitialized.value && annotationLayer) {
      loadExistingAnnotations();
    }
  },
  { deep: true, immediate: false }
);

/**
 * 监听组件显示状态变化
 * 当组件显示时，确保数据和地图同步
 */
watch(
  () => props.mainItem.show,
  async (newShow) => {
    if (newShow) {
      // 组件显示时，确保数据是最新的
      if (!isDataLoading.value) {
        isDataLoading.value = true;
        try {
          await annotationStore.initAnnotations();
        } catch (error) {
          console.error("重新加载标注数据失败:", error);
        } finally {
          isDataLoading.value = false;
        }
      }

      // 如果图层还没初始化，延迟初始化
      if (!isLayerInitialized.value) {
        await nextTick();
        setTimeout(() => {
          initAnnotationLayer();
        }, 100);
      }
    }
  },
  { immediate: true }
);

/**
 * 组件挂载
 */
onMounted(async () => {
  // 初始化分页参数
  currentPage.value = 1;

  // 如果组件已经显示，启动初始化流程
  if (props.mainItem.show) {
    // 确保数据是最新的
    if (!isDataLoading.value) {
      isDataLoading.value = true;
      try {
        await annotationStore.initAnnotations();
      } catch (error) {
        console.error("加载标注数据失败:", error);
      } finally {
        isDataLoading.value = false;
      }
    }

    // 延迟初始化图层，确保Cesium已完全加载
    await nextTick();
    setTimeout(() => {
      initAnnotationLayer();
    }, 200);
  }
});

/**
 * 组件卸载
 */
onUnmounted(() => {
  cleanup();
});
</script>

<style lang="scss" scoped>
.search_ipt {
  width: 200px;
  height: 36px;
}

.search_icon {
  cursor: pointer;
  color: #909399;
  transition: color 0.3s;

  &:hover {
    color: #409eff;
  }
}

.upload {
  color: #409eff;
  font-weight: 500;
}

.upload:hover {
  color: #66b1ff;
}

:deep(.el-table) {
  .el-table__body-wrapper {
    max-height: 250px;
    overflow-y: auto;
  }
}

:deep(.el-dialog) {
  .el-form-item__label {
    color: #606266;
    font-weight: 500;
  }

  .el-form-item__content {
    line-height: 1.4;
  }
}

.close-icon2 {
  position: absolute;
  top: 15px;
  right: 15px;
  cursor: pointer;
  font-size: 16px;
  color: #909399;

  &:hover {
    color: #409eff;
  }
}

.dialog-footer {
  text-align: center;

  .btn_group {
    display: inline-flex;
    gap: 10px;
    list-style: none;
    padding: 0;
    margin: 0;

    .small_btn {
      padding: 8px 20px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #fff;
      color: #606266;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;

      &:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }

      &:first-child {
        background: #409eff;
        color: #fff;
        border-color: #409eff;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
}
.pagination {
  margin-top: 15px;
  justify-content: end;
  display: flex;
}
</style>
