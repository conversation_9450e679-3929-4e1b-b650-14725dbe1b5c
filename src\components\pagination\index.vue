<!--
 * @Author: xiao
 * @Date: 2022-09-22 14:50:55
 * @LastEditors: xiao
 * @LastEditTime: 2022-10-12 17:17:28
 * @Description:
-->
<template>
  <el-row :gutter="20" align="middle" justify="end">
    <el-pagination
      :background="background"
      :current-page.sync="currentPage"
      v-model:page-size="pageSize"
      class="custom-pagi"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      @size-change="handleSizeChange"
      popper-class="custom-pagi-select"
      @current-change="handleCurrentChange"
    />
  </el-row>
</template>

<script setup lang="ts">
import { computed } from "vue";
const props = defineProps({
  total: {
    required: true,
    type: Number,
  },
  page: {
    type: Number,
    default: 1,
  },
  limit: {
    type: Number,
    default: 10,
  },
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 50];
    },
  },
  layout: {
    type: String,
    default: "prev, pager, next, sizes",
  },
  background: {
    type: Boolean,
    default: true,
  },
  autoScroll: {
    type: Boolean,
    default: true,
  },
  hidden: {
    type: Boolean,
    default: false,
  },
  bodyStyle: {
    type: Object,
    default: () => {
      return {
        padding: "16px 24px",
      };
    },
  },
});
const emits = defineEmits(["update:page", "update:limit", "pagination"]);
const currentPage = computed({
  get() {
    return props.page;
  },
  set(val) {
    emits("update:page", val);
  },
});
const pageSize = computed({
  get() {
    return props.limit;
  },
  set(val) {
    emits("update:limit", val);
  },
});
// 切换条数
const handleSizeChange = (val: any) => {
  pageSize.value = val;
  emits("pagination");
};
// 切换页
const handleCurrentChange = (val: any) => {
  currentPage.value = val;
  emits("pagination");
};
</script>

<style lang="scss" scoped>
:deep(.el-pagination .el-select .el-input) {
  width: 100px;
}
</style>
