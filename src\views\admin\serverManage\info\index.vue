<template>
  <div class="base-main" grid="~ rows-[72px_1fr] gap-y-3">
    <div class="query-form">
      <el-form :model="queryForm">
        <el-row :gutter="40">
          <el-col :span="6">
            <el-form-item label="名称">
              <el-input
                v-model="queryForm.name"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button
                class="admin-query-btn"
                type="primary"
                @click="queryData"
                :icon="Search"
              >
                查询
              </el-button>
              <el-button class="admin-reset-btn" :icon="Refresh" @click="reset">
                重置</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="pb-unset! box-border [&_>div]:overflow-hidden">
      <el-button
        class="admin-add-btn"
        type="primary"
        :icon="Plus"
        @click="newData"
      >
        添加</el-button
      >
      <div class="table-height mt-4">
        <el-table
          v-loading="loading"
          class="routeCt"
          :row-class-name="tableRowClassName"
          :data="tableData"
          style="width: 100%"
          height="100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="100"
            align="center"
          />
          <el-table-column prop="name" label="名称" align="center" />
          <el-table-column prop="tenant" label="租户" align="center">
          </el-table-column>
          <el-table-column prop="sign" label="服务器标识" align="center">
          </el-table-column>
          <el-table-column
            prop="databaseAddress"
            label="数据库地址"
            show-overflow-tooltip
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="databasePort"
            label="端口号"
            show-overflow-tooltip
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="databaseName"
            label="数据库名称"
            show-overflow-tooltip
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="databaseUserName"
            label="数据库用户名"
            show-overflow-tooltip
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="databasePassword"
            label="数据库密码"
            show-overflow-tooltip
            align="center"
          >
          </el-table-column>
          <el-table-column label="操作" min-width="100" align="center">
            <template v-slot="{ row }">
              <div class="flex-c">
                <el-button
                  link
                  class="primary-link color-#1966FF"
                  @click="handleCheck(row)"
                >
                  详情</el-button
                >
                <div class="tb-line"></div>
                <el-button
                  link
                  class="primary-link color-#1966FF"
                  @click="handleEdit(row)"
                  >修改</el-button
                >
                <div class="tb-line"></div>
                <el-button
                  link
                  class="danger-link color-#FF7373"
                  @click="handleDelete(row)"
                  >删除</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagibox box-border p-x-5">
        <div class="pagitotal">共{{ tableSize }}条数据</div>
        <pagination
          class="admin-pagi"
          :total="tableSize"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          @pagination="getList"
        ></pagination>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :model-value="visible"
      width="25%"
      class="admin-dialog"
      :before-close="handleClose"
      :close-on-click-modal="false"
      ref="test"
    >
      <info-detail
        ref="iconRef"
        v-model:formData="formData"
        v-model:editable="editable"
      />
      <template v-slot:footer v-if="dialogTitle != '信息查看'">
        <div>
          <el-button
            @click="handleClose"
            class="custom-close-button"
            :loading="subBtnLoading"
            >取 消</el-button
          >
          <el-button
            type="primary"
            :loading="subBtnLoading"
            class="custom-sub-button"
            @click="eventSubmit"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { Search, Refresh, Plus } from "@element-plus/icons-vue";
import InfoDetail from "./InfoDetail.vue";
import {
  serverPage,
  addServer,
  editServer,
  detailsServer,
  deleteServer,
} from "@/api/server";
import type { SysServer, QueryForm } from "./type";
const initQueryForm = () => {
  return {
    name: "",
    pageNum: 1,
    pageSize: 20,
  };
};
const queryForm = ref<QueryForm>(initQueryForm());

const initFormData = () => {
  return {
    id: "",
    name: "",
    tenant: "",
    sign: "",
    databaseAddress: "",
    databasePort: "",
    databaseUserName: "",
    databaseName: "",
    databasePassword: "",
  };
};
const formData = ref<SysServer>(initFormData());
const loading = ref(false);
const tableData = ref<SysServer[]>([]);
const dialogTitle = ref("");
const visible = ref(false);
const editable = ref(false);
const subBtnLoading = ref(false);
const tableSize = ref(0);
const iconRef = ref();
const queryData = () => {
  getList();
};
const reset = () => {
  queryForm.value = initQueryForm();
  getList();
};
const newData = () => {
  formData.value = initFormData();
  editable.value = false;
  dialogTitle.value = "信息新增";
  visible.value = true;
};

const handleCheck = async (row: SysServer) => {
  editable.value = true;
  dialogTitle.value = "信息查看";
  const result = await detailsServer(row.id);
  formData.value = result.data;
  visible.value = true;
};
const handleEdit = async (row: SysServer) => {
  editable.value = false;
  dialogTitle.value = "信息修改";
  const result = await detailsServer(row.id);
  formData.value = result.data;
  visible.value = true;
};

const handleDelete = (row: SysServer) => {
  ElMessageBox.confirm("确定要删除当前信息吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox",
  }).then(() => {
    deleteServer(row.id).then((res) => {
      if (res.code == 200) {
        ElMessage({
          showClose: true,
          message: "删除成功",
          type: "success",
        });
        getList();
      }
    });
  });
};
const handleClose = () => {
  visible.value = false;
};
const eventSubmit = async () => {
  try {
    var info = await iconRef.value!.submitForm();
    if (info) {
      subBtnLoading.value = true;
      if (dialogTitle.value === "信息新增") {
        const result = await addServer(info);
        if (result.code === 200) {
          visible.value = false;
          ElMessage.success(result.msg);
          await getList();
        }
      } else {
        const result = await editServer(info);
        if (result.code === 200) {
          visible.value = false;
          ElMessage.success("修改成功");
          await getList();
        }
      }
    }
  } finally {
    subBtnLoading.value = false;
  }
};
const getList = async () => {
  try {
    loading.value = true;
    const result = await serverPage(queryForm.value);
    tableData.value = result.data.list;
    tableSize.value = result.data.totalCount;
  } finally {
    loading.value = false;
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.table-height {
  height: calc(100vh - 345px);
}
</style>
