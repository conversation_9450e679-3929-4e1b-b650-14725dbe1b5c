<!-- src/components/ChartContainer.vue -->
<template>
  <div class="chart-container">
    <div class="chart" v-show="hasValidData" ref="chartRef"></div>
    <el-empty v-show="!hasValidData" :image-size="imageSize" />
  </div>
</template>

<script setup lang="ts">
import type { ECOption } from '@/lib/echarts/echarts';



const props = withDefaults(defineProps<{
  // 图表数据
  data: ISeriesData[] | null
  // 图表配置函数，接收数据返回 ECharts option
  optionGenerator: (data: any) => ECOption
  // empty 组件的图片大小
  imageSize?: number
  // 图表高度
  height?: string | number
}>(), {
  data: null,
  imageSize: 120,
  height: '210px'
})

const chartHeight = computed(() => {
  const type = typeof props.height
  if (type === 'string') {
    return props.height
  } else {
    return props.height + 'px';
  }
})


const { initChart, updateChart } = useEchart()

// 图表引用
const chartRef = ref();
// 图表实例（非响应式）
let chartInstance: any = null;

// 判断数据是否有效
const hasValidData = computed(() => {
  return Array.isArray(props.data) && props.data.length > 0;
});

// 初始化图表
const initializeChart = async () => {
  if (hasValidData.value && chartRef.value) {
    await nextTick()
    chartInstance = initChart(chartRef.value, props.optionGenerator(props.data));
  }
};

// 更新图表
const updateChartData = async () => {
  if (hasValidData.value) {
    if (chartInstance) {
      await nextTick()
      // 如果已有实例，更新数据
      updateChart(chartInstance, props.optionGenerator(props.data));
    } else if (chartRef.value) {
      // 如果没有实例但DOM已挂载，初始化
      initializeChart();
    }
  } else {
    // 无数据时清除图表实例
    chartInstance = null;
  }
};

// 监听数据变化
watch(() => props.data, updateChartData, { deep: true });

// 组件挂载后初始化
onMounted(() => {
  initializeChart();
});
</script>

<style scoped>
.chart-container {
  width: 100%;
}

.chart {
  width: 100%;
  height: v-bind(chartHeight);
}
</style>