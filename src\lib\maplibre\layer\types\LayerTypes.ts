/**
 * @fileoverview MapLibre图层系统类型定义
 * @description 定义图层管理系统中所有类型、接口和枚举
 * <AUTHOR>
 * @version 2.0.0
 */

import type { EngineType } from '@/types/plotting';
import type { Map as GlMap } from 'maplibre-gl';

/**
 * @enum LayerType
 * @description 支持的图层类型枚举
 */
export enum LayerType {
  /** 图层组 */
  GROUP = 'group',
  /** 天地图图层 */
  TDT = 'tdt',
  /** 百度地图图层 */
  BAIDU = 'baidu',
  /** 高德地图图层 */
  AMAP = 'amap',
  /** 影像图层 */
  IMAGE = 'image',
  /** 自定义XYZ图层 */
  CUSTOM_XYZ = 'customxyz',
  /** MVT矢量瓦片图层 */
  MVT = 'mvt',
  /** WMS图层 */
  WMS = 'wms',
  /** 栅格图层 */
  RASTER = 'raster',
  /** 矢量图层 */
  VECTOR = 'vector',
  /** 要素图层 */
  FEATURE = 'feature',
  /** 设备图层 */
  DEVICE = 'device',
  /** 预警图层 */
  ALARM = 'alarm'
}

/**
 * @enum LayerStatus
 * @description 图层状态枚举
 */
export enum LayerStatus {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 加载中 */
  LOADING = 'loading',
  /** 加载完成 */
  LOADED = 'loaded',
  /** 加载失败 */
  ERROR = 'error',
  /** 已销毁 */
  DESTROYED = 'destroyed'
}

/**
 * @enum CoordinateSystem
 * @description 坐标系枚举
 */
export enum CoordinateSystem {
  /** WGS84坐标系 */
  WGS84 = 'WGS84',
  /** GCJ02坐标系（火星坐标系） */
  GCJ02 = 'GCJ02',
  /** BD09坐标系（百度坐标系） */
  BD09 = 'BD09',
  /** Web墨卡托投影 */
  WEB_MERCATOR = 'EPSG:3857',
  /** CGCS2000坐标系 */
  CGCS2000 = 'EPSG:4490'
}

/**
 * @enum LayerGroup
 * @description 图层分组枚举，用于管理不同类型图层的显示层级
 */
export enum LayerGroupType {
  /** 基础底图图层组 */
  BASE = 'base',
  /** 业务数据图层组 */
  DATA = 'data',
  /** 标绘/注记图层组 */
  PLOT = 'plot',
  /** 临时/工具图层组 */
  TOOL = 'tool',
  /** 覆盖物图层组 */
  OVERLAY = 'overlay'
}

/**
 * @enum LayerPriority
 * @description 图层优先级枚举，数值越大优先级越高（显示在上方）
 */
export enum LayerPriority {
  /** 底图图层优先级 */
  BASE = 0,
  /** 影像图层优先级 */
  IMAGERY = 10,
  /** 矢量数据图层优先级 */
  VECTOR = 20,
  /** 标绘图层优先级 */
  PLOT = 30,
  /** 临时图层优先级 */
  TEMPORARY = 40,
  /** 覆盖物图层优先级 */
  OVERLAY = 50
}

/**
 * @interface BaseLayerConfig
 * @description 基础图层配置接口
 */
export interface BaseLayerConfig {
  /** 图层唯一标识 */
  id?: string;
  /** 图层名称 */
  title?: string;
  /** 图层类型 */
  type: LayerType | string;
  /** 是否显示 */
  show?: boolean;
  /** 图层URL */
  url?: string;
  /** 图层透明度 (0-1) */
  opacity?: number;
  /** 最小缩放级别 */
  minZoom?: number;
  /** 最大缩放级别 */
  maxZoom?: number;
  /** 图层分组 */
  group?: LayerGroupType;
  /** 图层优先级 */
  priority?: LayerPriority;
  /** 图层元数据 */
  metadata?: Record<string, any>;
  /** 自定义属性 */
  [key: string]: any;
}

/**
 * @interface RasterLayerConfig
 * @description 栅格图层配置接口
 */
export interface RasterLayerConfig extends BaseLayerConfig {
  type: LayerType.RASTER | LayerType.CUSTOM_XYZ | LayerType.TDT | LayerType.BAIDU | LayerType.AMAP;
  /** 瓦片大小 */
  tileSize?: number;
  /** 瓦片URL模板 */
  tiles?: string[];
  /** 坐标系 */
  crs?: CoordinateSystem | string;
  /** 图层样式 */
  style?: string;
  /** 服务令牌 */
  token?: string;
  /** 子域名 */
  subdomains?: string[];
}

/**
 * @interface VectorLayerConfig
 * @description 矢量图层配置接口
 */
export interface VectorLayerConfig extends BaseLayerConfig {
  type: LayerType.VECTOR | LayerType.MVT;
  /** 矢量瓦片URL */
  tiles?: string[];
  /** 源图层名称 */
  sourceLayer?: string;
  /** 样式配置 */
  paint?: Record<string, any>;
  /** 布局配置 */
  layout?: Record<string, any>;
  /** 过滤器 */
  filter?: any[];
}

/**
 * @interface GroupLayerConfig
 * @description 图层组配置接口
 */
export interface GroupLayerConfig extends BaseLayerConfig {
  type: LayerType.GROUP;
  /** 子图层配置 */
  children: LayerConfig[];
}

/**
 * @interface TdtLayerConfig
 * @description 天地图图层配置接口
 */
export interface TdtLayerConfig extends RasterLayerConfig {
  type: LayerType.TDT;
  /** 图层类型 */
  layer: 'vec_w' | 'cva_w' | 'img_w' | 'cia_w' | 'ter_w' | 'cta_w';
  /** 坐标系 */
  crs: '3857' | '4326';
  /** 天地图令牌 */
  token?: string;
}

/**
 * @interface BaiduLayerConfig
 * @description 百度地图图层配置接口
 */
export interface BaiduLayerConfig extends RasterLayerConfig {
  type: LayerType.BAIDU;
  /** 地图样式 */
  style: 'normal' | 'satellite' | 'hybrid' | 'dark' | 'light';
  /** 是否显示标注 */
  showLabel?: boolean;
  /** 坐标系（百度地图使用BD09） */
  crs: CoordinateSystem.BD09;
}

/**
 * @interface AmapLayerConfig
 * @description 高德地图图层配置接口
 */
export interface AmapLayerConfig extends RasterLayerConfig {
  type: LayerType.AMAP;
  /** 地图样式 */
  style: 'normal' | 'satellite' | 'hybrid' | 'roadnet' | 'dark' | 'light';
  /** 语言设置 */
  lang?: 'zh_cn' | 'en';
  /** 比例尺 */
  scale?: 1 | 2;
  /** 坐标系（高德地图使用GCJ02） */
  crs: CoordinateSystem.GCJ02;
}

/**
 * @description 要素图层配置接口
 */
export interface FeatureLayerConfig extends BaseLayerConfig {
  type: LayerType.FEATURE;
  /** 要素数据列表 */
  features: PlotFeature[];
  /** 默认样式 */
  defaultStyle?: Partial<FeatureStyle>;
  /** 是否启用编辑 */
  editable?: boolean;
  /** 是否显示标签 */
  showLabels?: boolean;
}

/**
 * @enum DeviceStatus
 * @description 设备状态枚举
 */
export enum DeviceStatus {
  /** 在线 */
  ONLINE = 'online',
  /** 离线 */
  OFFLINE = 'offline',
  /** 故障 */
  ERROR = 'error',
  /** 报警 */
  ALARM = 'alarm'
}

/**
 * @interface DeviceLayerConfig
 * @description 设备图层配置接口
 */
export interface DeviceLayerConfig extends BaseLayerConfig {
  type: LayerType.DEVICE;
  /** 设备图标类型（如: noise, pressure, flow等） */
  icon?: string;
  /** 设备数据URL（GeoJSON格式） */
  url: string;
  /** 图标大小 */
  iconSize?: number;
  /** 是否显示设备标签 */
  showLabels?: boolean;
  /** 设备状态过滤器 */
  statusFilter?: DeviceStatus[];
  /** 设备样式配置 */
  deviceStyle?: {
    /** 图标透明度 */
    iconOpacity?: number;
    /** 图标偏移量 */
    iconOffset?: [number, number];
    /** 文本颜色 */
    textColor?: string;
    /** 文本大小 */
    textSize?: number;
    /** 文本偏移量 */
    textOffset?: [number, number];
  };
}

/**
 * @description 默认要素样式
 */
export const DEFAULT_FEATURE_STYLE: FeatureStyle = {
  pointColor: '#3388ff',
  pointSize: 6,
  pointOutlineColor: '#ffffff',
  pointOutlineWidth: 2,
  lineColor: '#3388ff',
  lineWidth: 3,
  fillColor: '#3388ff',
  fillOpacity: 0.2,
  strokeColor: '#3388ff',
  strokeWidth: 2
};

/**
 * @interface LayerEventData
 * @description 图层事件数据接口
 */
export interface LayerEventData {
  /** 图层ID */
  layerId: string;
  /** 事件类型 */
  type: string;
  /** 事件数据 */
  data?: any;
  /** 时间戳 */
  timestamp: number;
}

/**
 * @interface LayerLoadOptions
 * @description 图层加载选项接口
 */
export interface LayerLoadOptions {
  /** 是否异步加载 */
  async?: boolean;
  /** 加载超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retryCount?: number;
  /** 是否预加载 */
  preload?: boolean;
  /** 加载优先级 */
  priority?: 'high' | 'normal' | 'low';
}

/**
 * @interface LayerPerformanceMetrics
 * @description 图层性能指标接口
 */
export interface LayerPerformanceMetrics {
  /** 加载时间（毫秒） */
  loadTime: number;
  /** 渲染时间（毫秒） */
  renderTime: number;
  /** 内存使用量（字节） */
  memoryUsage: number;
  /** 瓦片加载成功率 */
  tileSuccessRate: number;
  /** 错误次数 */
  errorCount: number;
}

/**
 * @interface LayerDependency
 * @description 图层依赖关系接口
 */
export interface LayerDependency {
  /** 依赖的图层ID */
  layerId: string;
  /** 依赖类型 */
  type: 'required' | 'optional';
  /** 加载顺序 */
  order?: number;
}

/**
 * @interface LayerValidationResult
 * @description 图层配置验证结果接口
 */
export interface LayerValidationResult {
  /** 是否有效 */
  valid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

/**
 * @interface LayerTreeNode
 * @description 图层树节点接口
 */
export interface LayerTreeNode {
  /** 图层ID */
  id: string;
  /** 图层名称 */
  name: string;
  /** 图层类型 */
  type: LayerType | string;
  /** 是否显示 */
  show: boolean;
  /** 子节点 */
  children?: LayerTreeNode[];
  /** 图层配置 */
  config?: LayerConfig;
  /** 父节点ID */
  parentId?: string;
  /** 层级深度 */
  depth?: number;
}

/**
 * @type LayerEventHandler
 * @description 图层事件处理器类型
 */
export type LayerEventHandler = (event: LayerEventData) => void;

/**
 * @type LayerFactory
 * @description 图层工厂函数类型
 */
export type LayerFactory<T extends LayerConfig = LayerConfig> = (config: T) => Promise<any>;

/**
 * @interface LayerManagerConfig
 * @description 图层管理器配置接口
 */
export interface LayerManagerConfig {
  /** 地图实例 */
  map: GlMap;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 默认加载选项 */
  defaultLoadOptions?: LayerLoadOptions;
  /** 最大并发加载数 */
  maxConcurrentLoads?: number;
}

// 类型别名，用于向后兼容
export type LayerGroupConfig = GroupLayerConfig;
export type ImageryLayerConfig = RasterLayerConfig;
export type CustomXYZLayerConfig = RasterLayerConfig;
export type MvtLayerConfig = VectorLayerConfig;

// ===== 标绘功能相关类型定义 =====

/**
 * @description 要素样式配置接口
 */
export interface FeatureStyle {
  /** 点颜色 */
  pointColor: string;
  /** 点大小 */
  pointSize: number;
  /** 点边框颜色 */
  pointOutlineColor: string;
  /** 点边框宽度 */
  pointOutlineWidth: number;
  /** 线条颜色 */
  lineColor: string;
  /** 线条宽度 */
  lineWidth: number;
  /** 填充颜色 */
  fillColor: string;
  /** 填充透明度 */
  fillOpacity: number;
  /** 边框颜色 */
  strokeColor: string;
  /** 边框宽度 */
  strokeWidth: number;
}

/**
 * @description 标绘要素 GeoJSON Feature Properties
 */
export interface PlotFeatureProperties {
  /** 要素类型标识 */
  type: 'plot';
  /** 几何体类型 */
  geometryType: 'point' | 'linestring' | 'polygon' | 'rectangle';
  /** 要素样式 */
  style: FeatureStyle;
}

/**
 * @description 标绘要素 GeoJSON Feature
 */
export interface PlotGeoJSONFeature {
  type: 'Feature';
  geometry: {
    type: 'Point' | 'LineString' | 'Polygon';
    coordinates: any;
  };
  properties: PlotFeatureProperties;
}

/**
 * @description 标绘要素数据接口（统一GeoJSON格式）
 */
export interface PlotFeature {
  /** 要素唯一标识（数据库ID） */
  id: number;
  /** 要素名称 */
  name: string;
  /** 要素描述备注 */
  remark: string;
  /** GeoJSON Feature数据 */
  geojson: PlotGeoJSONFeature;
  /** 地图引擎类型 */
  engineType: EngineType;
  /** 更新时间 */
  updateTime?: string;
}

/**
 * @type LayerConfig
 * @description 图层配置联合类型
 */
export type LayerConfig = 
  | BaseLayerConfig
  | RasterLayerConfig
  | VectorLayerConfig
  | GroupLayerConfig
  | TdtLayerConfig
  | BaiduLayerConfig
  | AmapLayerConfig
  | FeatureLayerConfig
  | DeviceLayerConfig; 