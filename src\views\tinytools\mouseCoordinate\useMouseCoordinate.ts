/**
 * @fileoverview 鼠标坐标追踪功能业务逻辑
 * @description 提供鼠标在地图上移动时实时WGS84坐标显示的核心业务逻辑，支持MapLibre和Cesium两种地图引擎
 * <AUTHOR>
 * @version 1.1.0
 */

import { ref, onMounted, onUnmounted } from 'vue';
import type { CoordinatePoint } from '@/utils/coordinate/CoordinateTransform';

/**
 * @interface UseMouseCoordinateOptions
 * @description 鼠标坐标追踪选项接口，定义坐标追踪的配置参数
 */
interface UseMouseCoordinateOptions {
  /** 地图类型，支持MapLibre和Cesium两种引擎 */
  mapType: 'maplibre' | 'cesium';
  /** 地图实例，根据mapType类型提供对应引擎的地图实例 */
  mapInstance: any;
  /** 坐标精度（小数位数） */
  precision?: number;
}

/**
 * @description 鼠标坐标追踪组件业务逻辑
 * @param options - 鼠标坐标追踪配置选项
 * @returns 鼠标坐标相关状态和方法
 */
export function useMouseCoordinate(options: UseMouseCoordinateOptions) {
  // 创建默认坐标对象
  const defaultCoord: CoordinatePoint = { lng: 0, lat: 0 };
  
  // WGS84坐标系坐标
  const wgs84 = ref<CoordinatePoint>({ ...defaultCoord });
  
  // 是否有有效坐标
  const hasCoordinate = ref(false);
  
  // 鼠标事件处理器
  let mouseMoveHandler: any = null;
  let cesiumEventHandler: any = null;
  
  /**
   * 格式化坐标精度
   * @param coord - 坐标点
   * @returns 格式化后的坐标点
   */
  const formatCoordinatePrecision = (coord: CoordinatePoint): CoordinatePoint => {
    const precision = options.precision || 6;
    return {
      lng: Number(coord.lng.toFixed(precision)),
      lat: Number(coord.lat.toFixed(precision)),
      alt: coord.alt ? Number(coord.alt.toFixed(3)) : coord.alt
    };
  };
  
  /**
   * 处理MapLibre地图的鼠标移动事件
   * @param map - MapLibre地图实例
   */
  const setupMapLibreMouseTracking = (map: any) => {
    // 移除现有的事件监听（如果有）
    if (mouseMoveHandler) {
      cleanupEventListeners();
    }
    
    // 创建鼠标移动事件处理器
    mouseMoveHandler = (e: any) => {
      if (!e || !e.lngLat) {
        hasCoordinate.value = false;
        return;
      }
      
      const coord: CoordinatePoint = {
        lng: e.lngLat.lng,
        lat: e.lngLat.lat,
        alt: 0
      };
      
      // 格式化坐标精度
      wgs84.value = formatCoordinatePrecision(coord);
      
      // 设置有效坐标标志
      hasCoordinate.value = true;
    };
    
    // 添加鼠标移动事件监听
    map.on('mousemove', mouseMoveHandler);
  };
  
  /**
   * 处理Cesium地图的鼠标移动事件
   * @param viewer - Cesium查看器实例
   */
  const setupCesiumMouseTracking = (viewer: any) => {
    const { Cesium } = BC.Namespace;
    
    // 移除现有的事件监听（如果有）
    if (cesiumEventHandler) {
      cleanupEventListeners();
    }
    
    // 创建事件处理器
    cesiumEventHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    
    // 注册鼠标移动事件
    cesiumEventHandler.setInputAction((movement: any) => {
      if (!movement || !movement.endPosition) {
        hasCoordinate.value = false;
        return;
      }
      
      // 拾取射线与地球表面的交点
      const cartesian = viewer.camera.pickEllipsoid(
        movement.endPosition,
        viewer.scene.globe.ellipsoid
      );
      
      if (cartesian) {
        // 转换为经纬度坐标
        const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
        const coord: CoordinatePoint = {
          lng: Cesium.Math.toDegrees(cartographic.longitude),
          lat: Cesium.Math.toDegrees(cartographic.latitude),
          alt: cartographic.height || 0
        };
        
        // 格式化坐标精度
        wgs84.value = formatCoordinatePrecision(coord);
        
        // 设置有效坐标标志
        hasCoordinate.value = true;
      } else {
        // 尝试拾取地形
        const ray = viewer.camera.getPickRay(movement.endPosition);
        const cartesian = viewer.scene.globe.pick(ray, viewer.scene);
        
        if (cartesian) {
          const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
          const coord: CoordinatePoint = {
            lng: Cesium.Math.toDegrees(cartographic.longitude),
            lat: Cesium.Math.toDegrees(cartographic.latitude),
            alt: cartographic.height || 0
          };
          
          // 格式化坐标精度
          wgs84.value = formatCoordinatePrecision(coord);
          
          // 设置有效坐标标志
          hasCoordinate.value = true;
        } else {
          // 无法拾取到有效坐标
          hasCoordinate.value = false;
        }
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  };
  
  /**
   * 清理事件监听器
   */
  const cleanupEventListeners = () => {
    if (options.mapType === 'maplibre') {
      // 移除MapLibre事件监听
      if (options.mapInstance && mouseMoveHandler) {
        options.mapInstance.off('mousemove', mouseMoveHandler);
        mouseMoveHandler = null;
      }
    } else if (options.mapType === 'cesium') {
      // 销毁Cesium事件处理器
      if (cesiumEventHandler) {
        cesiumEventHandler.destroy();
        cesiumEventHandler = null;
      }
    }
  };
  
  /**
   * 启动鼠标坐标跟踪
   */
  const startTracking = () => {
    if (!options.mapInstance) {
      console.error('地图实例未提供，无法启动鼠标坐标跟踪');
      return;
    }
    
    if (options.mapType === 'maplibre') {
      setupMapLibreMouseTracking(options.mapInstance);
    } else if (options.mapType === 'cesium') {
      setupCesiumMouseTracking(options.mapInstance);
    } else {
      console.error(`不支持的地图类型: ${options.mapType}`);
    }
  };
  
  /**
   * 停止鼠标坐标跟踪
   */
  const stopTracking = () => {
    cleanupEventListeners();
    hasCoordinate.value = false;
  };
  
  /**
   * 重置坐标数据
   */
  const resetCoordinates = () => {
    wgs84.value = { ...defaultCoord };
    hasCoordinate.value = false;
  };
  
  // 组件挂载时启动跟踪
  onMounted(() => {
    startTracking();
  });
  
  // 组件卸载时清理资源
  onUnmounted(() => {
    stopTracking();
  });
  
  // 返回响应式数据和方法
  return {
    // 坐标数据
    wgs84,
    hasCoordinate,
    
    // 方法
    startTracking,
    stopTracking,
    resetCoordinates
  };
} 