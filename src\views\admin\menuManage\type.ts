export interface QueryForm {
  status: string;
  name: string;
  pageSize: number;
  pageNum: number;
}
export interface SysMenu {
  /**
   * ID
   */
  id?: number | string 
  /**
   * 父级id
   */
  parentId: string | number
  /**
   * 菜单名称
   */
  name: string
  /**
   * 页面标题
   */
  title?: string
  /**
   * 角色标识（权限字符）
   */
  code?: string
  /**
   * 路由地址
   */
  path?: string
  /**
   * 组件路径
   */
  component?: string
  /**
   * 显示顺序
   */
  orderNum?: string
  /**
   * 重定向路由
   */
  redirect?: string
  /**
   * 是否外链（0否，1是）
   */
  isFrame?: boolean
  /**
   * 显示状态（0隐藏，1显示）
   */
  visible?: string | number
  /**
   * 菜单类型（S系统，C目录，M菜单，B按钮）
   */
  type?: string | number
  /**
   * 权限标识（多个用英文逗号隔开）
   */
  perms?: string | number
  /**
   * 菜单图标
   */
  icon?: string | number
  /**
   * 状态（0停用，1正常）
   */
  status?: string|number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 创建人
   */
  createBy?: string
  /**
   * 修改人
   */
  updateBy?: string
  /**
   * 修改时间
   */
  updateTime?: Date
}