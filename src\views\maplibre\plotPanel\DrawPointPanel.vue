<!--
 * @Description: 点标绘绘制编辑面板 - 重构版本
 * @Date: 2024-01-20 11:00:00
 * @Author: 项目开发团队
 * @LastEditor: 项目开发团队
 * @LastEditTime: 2024-01-20 15:00:00
-->
<template>
  <div element-loading-background="rgba(0,0,0,0)">
    <el-form
      ref="ruleFormRef"
      :model="formData"
      :rules="rules"
      class="admin-sub-form"
      label-width="auto"
    >
      <!-- 基本信息 -->

      <el-form-item label="标绘类型">
        <base-input disabled class="custom-visform-input" value="点" />
      </el-form-item>

      <el-form-item label="名称" prop="name">
        <base-input
          class="custom-visform-input"
          v-model="formData.name"
          placeholder="请输入点名称"
        />
      </el-form-item>

      <!-- 坐标信息 -->

      <el-form-item label="经度">
        <base-input
          class="custom-visform-input"
          v-model.number="coordinates.longitude"
          disabled
          :type="'number'"
          placeholder="未绘制"
        />
      </el-form-item>

      <el-form-item label="纬度">
        <base-input
          class="custom-visform-input"
          disabled
          v-model.number="coordinates.latitude"
          :type="'number'"
          placeholder="未绘制"
        />
      </el-form-item>

      <!-- 绘制操作 -->

      <el-form-item label="绘制操作">
        <el-button
          type="primary"
          @click="startDraw"
          :disabled="isDrawing"
          :loading="isDrawing"
        >
          <el-icon><Edit /></el-icon>
          {{ isEditing ? "重新绘制" : hasGeometry ? "重新绘制" : "开始绘制" }}
        </el-button>
      </el-form-item>
      <!-- 描述信息 -->
      <el-form-item label="标绘描述">
        <base-input
          class="custom-visform-input"
          v-model="formData.remark"
          :rows="3"
          placeholder="请输入点位描述（可选）"
          maxlength="200"
        />
      </el-form-item>

      <!-- 操作按钮 -->
      <div class="flex justify-end">
        <el-button
          type="primary"
          class="primary-btn w-80px"
          @click="onSubmit"
          :loading="submitLoading"
          :disabled="!hasGeometry"
        >
          {{ isEditing ? "更新" : "确定" }}
        </el-button>
        <el-button
          class="w-80px clear-btn"
          @click="onCancel"
          :loading="submitLoading"
        >
          取消
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, type Ref } from "vue";
import { ElForm } from "element-plus";
import { Edit } from "@element-plus/icons-vue";
import BaseInput from "@/components/input/index.vue";
import { vxRule } from "@/utils/validator";
import type { PlotFeature } from "@/lib/maplibre/layer/types/LayerTypes";

// 导入基础Panel逻辑
import {
  useBaseDrawPanel,
  type BaseDrawPanelProps,
  type BaseDrawPanelEmits,
} from "./BaseDrawPanel";

// Props和Emits定义
const props = withDefaults(defineProps<BaseDrawPanelProps>(), {
  drawData: null,
  plotService: null,
});

const emit = defineEmits<BaseDrawPanelEmits>();

// 表单引用和验证规则
const ruleFormRef = ref<InstanceType<typeof ElForm>>();
const rules = {
  name: vxRule(),
};

// 坐标信息（点特有的功能）
const coordinates = ref({
  longitude: 0,
  latitude: 0,
});

/**
 * @description 从几何图形更新坐标显示（点特有逻辑）
 */
function updateCoordinatesFromGeometry(formData: Ref<PlotFeature>): void {
  if (
    formData.value.geojson?.geometry &&
    formData.value.geojson.geometry.type === "Point"
  ) {
    const coords = formData.value.geojson.geometry.coordinates;
    if (coords && coords.length >= 2) {
      coordinates.value.longitude = coords[0];
      coordinates.value.latitude = coords[1];
    }
  } else {
    // 清空坐标显示
    coordinates.value.longitude = 0;
    coordinates.value.latitude = 0;
  }
}

// 使用基础Panel逻辑，并提供点特有的自定义逻辑
const {
  submitLoading,
  isDrawing,
  formData,
  hasGeometry,
  isEditing,
  startDraw,
  onSubmit,
  onCancel,
} = useBaseDrawPanel(
  {
    featureType: "point",
    featureTypeName: "点",
    drawMessage: "请在地图上点击选择位置",
    successMessage: "点位绘制完成",
    placeholder: "请输入点名称",
    descriptionPlaceholder: "请输入点位描述（可选）",
  },
  props,
  emit,
  {
    // 自定义初始化逻辑：更新坐标显示
    onInitialized: (formData) => {
      updateCoordinatesFromGeometry(formData);
    },

    // 自定义几何数据更新逻辑：更新坐标显示
    onGeometryUpdated: (formData) => {
      updateCoordinatesFromGeometry(formData);
    },

    // 自定义绘制前逻辑：清空坐标显示
    onBeforeDraw: (formData) => {
      if (formData.value.geojson?.geometry) {
        coordinates.value.longitude = 0;
        coordinates.value.latitude = 0;
      }
    },

    // 自定义提交前验证：检查表单验证
    onBeforeSubmit: async (formData) => {
      if (!ruleFormRef.value) return false;

      try {
        const valid = await ruleFormRef.value.validate();
        return valid;
      } catch (error) {
        console.error("表单验证失败:", error);
        return false;
      }
    },
  }
);
</script>

<style lang="scss" scoped></style>
