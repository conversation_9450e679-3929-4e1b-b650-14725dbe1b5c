export const initAnnualAlsEchart = (date: any, twoGis: any, threeGis: any, gisService: any, backgroundManage: any) => {
  const option: any = {
    grid: {
      top: '24%',
      left: "1%",
      bottom: '0',
      right: '0',
      containLabel: true,
    },
    legend: {
      icon: 'stack',
      itemWidth: 20,
      itemHeight: 2,
      right: "0",
      top: '7%',
      data: ['二维管网平台', '三维管网平台', 'GIS信息服务平台', '后台系统']
    },
    xAxis: {
      type: 'category',
      axisTick: { show: false },
      axisLine: {
        lineStyle: {
          color: "#F6F6F6",
        },
      },
      axisLabel: {
        textStyle: {
          color: "#2C3037",
        },
      },
      data: date
    },
    yAxis: {

      type: 'value',
      name: "单位：个",
      nameTextStyle: {
        color: "#5C5F66",
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#F6F6F6",
        },
      },
    },

    series: [
      {
        name: "二维管网平台",
        barWidth: 4,
        data: twoGis,
        itemStyle: {
          color: '#1966FF'
        },
        type: 'bar'
      },
      {
        name: "三维管网平台",
        barWidth: 4,
        data: threeGis,
        itemStyle: {
          color: '#FF8282'
        },
        type: 'bar'
      },
      {
        name: "GIS信息服务平台",
        barWidth: 4,
        data: gisService,
        itemStyle: {
          color: '#6BDEB0'
        },
        type: 'bar'
      },
      {
        name: "后台系统",
        barWidth: 4,
        data: backgroundManage,
        itemStyle: {
          color: '#8300FF'
        },
        type: 'bar'
      }
    ]
  };
  return option
}