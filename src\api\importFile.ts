import hRequest from '@/utils/http';

/**
 * 通用响应结构
 */
export interface ApiResponse<T = any> {
  /** 消息状态码 */
  code: number;
  /** 消息内容 */
  msg: string;
  /** 数据对象 */
  data: T;
}

/**
 * 导入文件API类
 * 
 * @description 提供SHP文件导入、Excel导入和导出Excel模板等功能
 * <AUTHOR> Assistant
 * @since 2025-01-03
 */
class ImportFileApi {
  
  /**
   * 导入SHP数据到管点表
   * 
   * @param files - SHP文件数组（通常包含.shp, .shx, .dbf, .prj等文件）
   * @returns Promise<ApiResponse<void>>
   * 
   * @example
   * ```typescript
   * const files = [shpFile, shxFile, dbfFile];
   * await ImportFileApi.importPipeNodeShp(files);
   * ```
   */
  static async importPipeNodeShp(files: File[]): Promise<ApiResponse<void>> {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    return hRequest.post<ApiResponse<void>>({
      url: '/analyse/gs/pt/import/shp',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /**
   * Excel导入管点数据
   * 
   * @param file - Excel文件
   * @returns Promise<ApiResponse<void>>
   * 
   * @example
   * ```typescript
   * const excelFile = new File([blob], 'pipe_nodes.xlsx');
   * await ImportFileApi.importPipeNodeExcel(excelFile);
   * ```
   */
  static async importPipeNodeExcel(file: File): Promise<ApiResponse<void>> {
    const formData = new FormData();
    formData.append('file', file);

    return hRequest.post<ApiResponse<void>>({
      url: '/analyse/gs/pt/excel/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /**
   * 导出管点Excel模板
   * 
   * @returns Promise<Blob> Excel模板文件
   * 
   * @example
   * ```typescript
   * const blob = await ImportFileApi.exportPipeNodeTemplate();
   * // 下载文件
   * const url = URL.createObjectURL(blob);
   * const a = document.createElement('a');
   * a.href = url;
   * a.download = 'pipe_node_template.xlsx';
   * a.click();
   * ```
   */
  static async exportPipeNodeTemplate(): Promise<any> {
    return hRequest.get<any>({
      url: '/analyse/gs/pt/excel/template',
      responseType: 'blob'
    });
  }

  /**
   * Excel导入管线数据
   * 
   * @param file - Excel文件
   * @returns Promise<ApiResponse<void>>
   * 
   * @example
   * ```typescript
   * const excelFile = new File([blob], 'pipe_lines.xlsx');
   * await ImportFileApi.importPipeLineExcel(excelFile);
   * ```
   */
  static async importPipeLineExcel(file: File): Promise<ApiResponse<void>> {
    const formData = new FormData();
    formData.append('file', file);

    return hRequest.post<ApiResponse<void>>({
      url: '/analyse/gs/ln/excel/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /**
   * 导出管线Excel模板
   * 
   * @returns Promise<Blob> Excel模板文件
   * 
   * @example
   * ```typescript
   * const blob = await ImportFileApi.exportPipeLineTemplate();
   * // 下载文件
   * const url = URL.createObjectURL(blob);
   * const a = document.createElement('a');
   * a.href = url;
   * a.download = 'pipe_line_template.xlsx';
   * a.click();
   * ```
   */
  static async exportPipeLineTemplate(): Promise<any> {
    return hRequest.get<any>({
      url: '/analyse/gs/ln/excel/template',
      responseType: 'blob'
    });
  }
}

/**
 * 文件导入工具函数
 */
export const ImportFileUtils = {
  
  /**
   * 验证SHP文件包
   * 
   * @param files - 文件列表
   * @returns 验证结果和错误信息
   */
  validateShpFiles(files: File[]): { valid: boolean; message: string } {
    if (files.length === 0) {
      return { valid: false, message: '请选择SHP文件' };
    }

    const extensions = files.map(file => {
      const ext = file.name.split('.').pop()?.toLowerCase();
      return ext;
    });

    const requiredExts = ['shp', 'shx', 'dbf'];
    const missingExts = requiredExts.filter(ext => !extensions.includes(ext));

    if (missingExts.length > 0) {
      return { 
        valid: false, 
        message: `缺少必需的文件: ${missingExts.map(ext => `.${ext}`).join(', ')}` 
      };
    }

    return { valid: true, message: '文件验证通过' };
  },

  /**
   * 验证Excel文件
   * 
   * @param file - Excel文件
   * @returns 验证结果和错误信息
   */
  validateExcelFile(file: File): { valid: boolean; message: string } {
    if (!file) {
      return { valid: false, message: '请选择Excel文件' };
    }

    const ext = file.name.split('.').pop()?.toLowerCase();
    if (!['xls', 'xlsx'].includes(ext || '')) {
      return { valid: false, message: '只支持.xls和.xlsx格式的Excel文件' };
    }

    // 检查文件大小（限制10MB）
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      return { valid: false, message: '文件大小不能超过10MB' };
    }

    return { valid: true, message: '文件验证通过' };
  },

  /**
   * 下载文件
   * 
   * @param blob - 文件Blob
   * @param filename - 文件名
   */
  downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
};

// 默认导出API类
export default ImportFileApi;

// 具名导出API方法（便于按需导入）
export const {
  importPipeNodeShp,
  importPipeNodeExcel,
  exportPipeNodeTemplate,
  importPipeLineExcel,
  exportPipeLineTemplate
} = ImportFileApi;
