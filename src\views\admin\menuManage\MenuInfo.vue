<template>
  <div>
    <el-form
      :model="formData"
      :rules="rules"
      ref="form"
      :disabled="editable"
      class="custom-sub-form"
    >
      <el-form-item label="菜单名称" prop="name">
        <el-input placeholder="请输入" v-model="formData.name" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="页面标题" prop="title">
        <el-input placeholder="请输入" v-model="formData.title" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="菜单类型" prop="type">
        <el-select v-model="formData.type" :teleported="false" placeholder="请选择">
          <el-option
            v-for="item in menuState"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="上级菜单" prop="parentId">
        <el-tree-select
          v-model="formData.parentId"
          :data="menuData"
          check-strictly
          :render-after-expand="false"
          :props="{ value: 'id', label: 'label', children: 'children' }"
        />
      </el-form-item>
      <el-form-item v-if="formData.type != 'C'" label="权限标识" prop="perms">
        <el-input placeholder="请输入" v-model="formData.perms" maxlength="30"></el-input>
      </el-form-item>
      <el-form-item v-if="formData.type != 'B'" label="路由地址" prop="path">
        <el-input placeholder="请输入" v-model="formData.path" maxlength="50"></el-input>
      </el-form-item>
      <el-form-item label="菜单状态" v-if="formData.type != 'B'" prop="status">
        <el-switch
          v-model="formData.status"
          size="large"
          inline-prompt
          active-text="启用"
          :active-value="'1'"
          inactive-text="禁用"
          :inactive-value="'0'"
        />
      </el-form-item>
      <el-form-item label="显示状态" v-if="formData.type != 'B'" prop="visible">
        <el-switch
          v-model="formData.visible"
          inline-prompt
          size="large"
          active-text="启用"
          :active-value="'1'"
          inactive-text="禁用"
          :inactive-value="'0'"
        />
      </el-form-item>
      <el-form-item label="排序" prop="orderNum">
        <el-input-number
          v-model="formData.orderNum"
          controls-position="right"
          :min="1"
          :max="100"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="图标路径" prop="icon">
        <el-input placeholder="请输入" v-model.trim="formData.icon" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="菜单描述" prop="remark">
        <el-input
          placeholder="请输入"
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          maxlength="500"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script lang="ts" setup>
import { menuState } from '@/utils/constant';
const props = defineProps({
  formData: Object,
  menuData: Array,
  editable: Boolean
});
let formData: any = ref(props.formData);
let menuData = ref(props.menuData);
let editable = ref(props.editable);
watch(
  () => props.formData,
  (val) => {
    formData.value = val;
  }
);
watch(
  () => props.editable,
  (val) => {
    editable.value = val;
  }
);
const rules = {
  name: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
    { max: 20, message: '长度不超过20个字符', trigger: 'blur' }
  ],
  type: [{ required: true, message: '请选择菜单类型', trigger: ['change'] }],
  status: [{ required: true, message: '请输入菜单状态', trigger: 'blur' }],
  isFrame: [{ required: true, message: '', trigger: 'blur' }],
  visible: [{ required: true, message: '', trigger: 'blur' }],
  orderNum: [{ required: true, message: '请输入排序', trigger: 'blur' }]
};
let form: any = ref(null);
const submitForm = async (): Promise<any> => {
  if (formData.value.parentId == '主目录' || formData.value.parentId == '') {
    formData.value.parentId = 0;
  }
  return (await form.value?.validate()) ? formData.value : null;
};
const resetForm = () => {
  form.value.resetFields();
};
onMounted(() => {
  console.log(editable);
  resetForm();
});
defineExpose({
  submitForm,
  resetForm
});
</script>
