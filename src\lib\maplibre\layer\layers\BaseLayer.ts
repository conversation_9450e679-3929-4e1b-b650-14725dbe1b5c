import { guid } from '@/utils/uuid'
import { type LayerItem } from '../type/LayerItem'
import type { LayerGroup } from './LayerGroup'
import { Map as glMap } from 'maplibre-gl'
import { 
  LayerType, 
  LayerStatus,
  LayerGroupType,
  LayerPriority,
  type BaseLayerConfig, 
  type LayerEventData, 
  type LayerEventHandler,
  type LayerPerformanceMetrics,
  type LayerLoadOptions
} from '../types/LayerTypes'

/**
 * @class BaseLayer
 * @description 图层基类，提供图层的基础功能和生命周期管理
 * @abstract
 */
export abstract class BaseLayer {
  protected _map!: glMap
  protected _delegate!: any
  protected _show: boolean
  protected _type: LayerType | string
  protected _id: string
  protected _name: string
  protected _url: string
  protected _options: BaseLayerConfig
  protected _parent: LayerGroup | undefined
  protected _status: LayerStatus = LayerStatus.UNINITIALIZED
  protected _eventHandlers: Map<string, LayerEventHandler[]> = new Map()
  protected _performanceMetrics: Partial<LayerPerformanceMetrics> = {}
  protected _loadStartTime: number = 0
  protected _opacity: number = 1
  protected _minZoom?: number
  protected _maxZoom?: number
  protected _group: LayerGroupType
  protected _priority: LayerPriority

  /**
   * @constructor
   * @param options - 图层配置选项
   */
  constructor(protected options: BaseLayerConfig) {
    this._id = options.id ?? guid()
    this._name = options.name ?? '未命名'
    this._show = options.show === undefined ? true : options.show
    this._type = options.type
    this._url = options.url ?? options.id ?? ''
    this._options = options
    this._opacity = options.opacity ?? 1
    this._minZoom = options.minZoom
    this._maxZoom = options.maxZoom
    this._group = options.group ?? this.getDefaultGroup()
    this._priority = options.priority ?? this.getDefaultPriority()
    
    // 初始化性能指标
    this._performanceMetrics = {
      loadTime: 0,
      renderTime: 0,
      memoryUsage: 0,
      tileSuccessRate: 1,
      errorCount: 0
    }
  }
  /**
   * @description 将图层添加到地图
   * @param map - MapLibre地图实例
   * @param parent - 父图层组
   * @param options - 加载选项
   */
  async addTo(map: glMap, parent?: LayerGroup, options?: LayerLoadOptions): Promise<void> {
    try {
      this._map = map
      this._parent = parent
      this._status = LayerStatus.LOADING
      this._loadStartTime = performance.now()
      
      this.emit('loading', { layerId: this._id, type: 'loading', data: null, timestamp: Date.now() })
      
      if (this._show) {
        this._delegate = await this.addToInternal(map, false, options)
      }
      
      this._status = LayerStatus.LOADED
      this._performanceMetrics.loadTime = performance.now() - this._loadStartTime
      
      this.emit('loaded', { layerId: this._id, type: 'loaded', data: this._performanceMetrics, timestamp: Date.now() })
    } catch (error) {
      this._status = LayerStatus.ERROR
      this._performanceMetrics.errorCount = (this._performanceMetrics.errorCount || 0) + 1
      
      this.emit('error', { layerId: this._id, type: 'error', data: error, timestamp: Date.now() })
      console.error(`图层 ${this._name} 加载失败:`, error)
      throw error
    }
  }

  /**
   * @description 图层内部添加实现（子类必须实现）
   * @param map - MapLibre地图实例
   * @param isRefresh - 是否为刷新操作
   * @param options - 加载选项
   * @returns 图层代理对象
   */
  abstract addToInternal(map: glMap, isRefresh?: boolean, options?: LayerLoadOptions): Promise<any>

  /**
   * @description 飞行到图层范围
   */
  flyTo(): void {
    if (this._map && this._delegate) {
      this._map.flyTo(this._delegate)
    }
  }

  /**
   * @description 添加事件监听器
   * @param eventType - 事件类型
   * @param handler - 事件处理器
   */
  on(eventType: string, handler: LayerEventHandler): void {
    if (!this._eventHandlers.has(eventType)) {
      this._eventHandlers.set(eventType, [])
    }
    this._eventHandlers.get(eventType)!.push(handler)
  }

  /**
   * @description 移除事件监听器
   * @param eventType - 事件类型
   * @param handler - 事件处理器
   */
  off(eventType: string, handler?: LayerEventHandler): void {
    if (!this._eventHandlers.has(eventType)) return
    
    const handlers = this._eventHandlers.get(eventType)!
    if (handler) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    } else {
      handlers.length = 0
    }
  }

  /**
   * @description 发射事件
   * @param eventType - 事件类型
   * @param eventData - 事件数据
   */
  protected emit(eventType: string, eventData: LayerEventData): void {
    const handlers = this._eventHandlers.get(eventType)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(eventData)
        } catch (error) {
          console.error(`图层事件处理器执行失败 [${eventType}]:`, error)
        }
      })
    }
  }

  /**
   * @description 获取图层名称
   */
  get name(): string {
    return this._name
  }

  /**
   * @description 设置图层名称
   */
  set name(name: string) {
    this._name = name
    this.emit('nameChanged', { layerId: this._id, type: 'nameChanged', data: name, timestamp: Date.now() })
  }

  /**
   * @description 获取图层代理对象
   */
  get delegate(): any {
    return this._delegate
  }

  /**
   * @description 获取父图层组
   */
  get parent(): LayerGroup | undefined {
    return this._parent
  }

  /**
   * @description 设置父图层组
   */
  set parent(parent: LayerGroup | undefined) {
    this._parent = parent
  }

  /**
   * @description 获取图层ID
   */
  get id(): string {
    return this._id
  }

  /**
   * @description 获取图层类型
   */
  get type(): LayerType | string {
    return this._type
  }

  /**
   * @description 获取图层状态
   */
  get status(): LayerStatus {
    return this._status
  }

  /**
   * @description 获取图层透明度
   */
  get opacity(): number {
    return this._opacity
  }

  /**
   * @description 设置图层透明度
   */
  set opacity(opacity: number) {
    this._opacity = Math.max(0, Math.min(1, opacity))
    if (this._map && this._map.getLayer(this.id)) {
      this._map.setPaintProperty(this.id, 'raster-opacity', this._opacity)
    }
    this.emit('opacityChanged', { layerId: this._id, type: 'opacityChanged', data: this._opacity, timestamp: Date.now() })
  }

  /**
   * @description 获取图层显示状态
   */
  get show(): boolean {
    return this._show
  }

  /**
   * @description 设置图层显示状态
   */
  set show(show: boolean) {
    const oldShow = this._show
    this._show = show

    if (this._map && this._map.getLayer(this.id)) {
      this._map.setLayoutProperty(this.id, 'visibility', show ? 'visible' : 'none')
      // 调用子类的showInternal方法来处理额外的图层（如聚合图层、展开图层等）
      this.showInternal(show)
    } else if (show && this._status === LayerStatus.LOADED) {
      this.addToInternal(this._map).catch(error => {
        console.error(`重新显示图层 ${this._name} 失败:`, error)
      })
    }

    if (this._map) {
      this._map.resize()
    }

    if (oldShow !== show) {
      this.emit('visibilityChanged', { layerId: this._id, type: 'visibilityChanged', data: show, timestamp: Date.now() })
    }
  }

  /**
   * @description 获取性能指标
   */
  get performanceMetrics(): Partial<LayerPerformanceMetrics> {
    return { ...this._performanceMetrics }
  }

  /**
   * @description 内部显示控制方法
   * @param show - 是否显示
   */
  protected showInternal(show: boolean): void {
    this._delegate && (this._delegate.show = show)
  }

  /**
   * @description 移除图层
   */
  remove(): void {
    try {
      this._status = LayerStatus.DESTROYED
      
      // 清理事件监听器
      this._eventHandlers.clear()
      
      if (this._parent) {
        this._parent.removeChild(this._id)
      } else {
        this.removeInternal()
      }
      
      this.emit('removed', { layerId: this._id, type: 'removed', data: null, timestamp: Date.now() })
    } catch (error) {
      console.error(`移除图层 ${this._name} 失败:`, error)
    }
  }

  /**
   * @description 图层内部移除实现（子类必须实现）
   */
  abstract removeInternal(): void

  /**
   * @description 刷新图层
   */
  async refresh(): Promise<void> {
    if (this._map && this._status === LayerStatus.LOADED) {
      try {
        this.removeInternal()
        await this.addToInternal(this._map, true)
        this.emit('refreshed', { layerId: this._id, type: 'refreshed', data: null, timestamp: Date.now() })
      } catch (error) {
        console.error(`刷新图层 ${this._name} 失败:`, error)
        throw error
      }
    }
  }

  /**
   * @description 验证图层配置
   * @returns 验证结果
   */
  validate(): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = []
    const warnings: string[] = []

    if (!this._id) {
      errors.push('图层ID不能为空')
    }

    if (!this._type) {
      errors.push('图层类型不能为空')
    }

    if (this._opacity < 0 || this._opacity > 1) {
      warnings.push('图层透明度应在0-1之间')
    }

    if (this._minZoom !== undefined && this._maxZoom !== undefined && this._minZoom > this._maxZoom) {
      errors.push('最小缩放级别不能大于最大缩放级别')
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * @description 查找图层
   * @param layerId - 图层ID
   * @returns 找到的图层或null
   */
  findLayer(layerId: string): BaseLayer | null {
    if (this._id === layerId || this._url === layerId) {
      return this
    }
    return null
  }

  /**
   * @description 获取图层树结构
   * @returns 图层树节点
   */
  getLayerTree(): LayerItem {
    return {
      id: this._id,
      name: this._name,
      type: this._type,
      show: this._show,
      options: this._options
    }
  }

  /**
   * @description 克隆图层
   * @returns 克隆的图层配置
   */
  clone(): BaseLayerConfig {
    return {
      ...this._options,
      id: undefined, // 让系统生成新的ID
      title: `${this._name} (副本)`
    }
  }

  /**
   * @description 获取图层覆盖物（预留接口）
   */
  getOverlays(): any[] {
    return []
  }

  /**
   * @description 销毁图层，释放资源
   */
  destroy(): void {
    this.remove()
    this._map = null as any
    this._delegate = null
    this._parent = undefined
    this._eventHandlers.clear()
  }

  /**
   * @description 获取图层分组
   */
  get group(): LayerGroupType {
    return this._group
  }

  /**
   * @description 设置图层分组
   */
  set group(value: LayerGroupType) {
    this._group = value
  }

  /**
   * @description 获取图层优先级
   */
  get priority(): LayerPriority {
    return this._priority
  }

  /**
   * @description 设置图层优先级
   */
  set priority(value: LayerPriority) {
    this._priority = value
  }

  /**
   * @description 获取默认图层分组（子类可重写）
   * @returns 默认图层分组
   * @protected
   */
  protected getDefaultGroup(): LayerGroupType {
    // 根据图层类型返回默认分组
    switch (this._type) {
      case LayerType.FEATURE:
        return LayerGroupType.PLOT
      case LayerType.BAIDU:
      case LayerType.AMAP:
      case LayerType.TDT:
      case LayerType.CUSTOM_XYZ:
        return LayerGroupType.BASE
      case LayerType.GROUP:
        return LayerGroupType.DATA
      default:
        return LayerGroupType.DATA
    }
  }

  /**
   * @description 获取默认图层优先级（子类可重写）
   * @returns 默认图层优先级
   * @protected
   */
  protected getDefaultPriority(): LayerPriority {
    // 根据图层类型返回默认优先级
    switch (this._type) {
      case LayerType.FEATURE:
        return LayerPriority.PLOT
      case LayerType.BAIDU:
      case LayerType.AMAP:
      case LayerType.TDT:
      case LayerType.CUSTOM_XYZ:
        return LayerPriority.BASE
      case LayerType.MVT:
        return LayerPriority.VECTOR
      case LayerType.IMAGE:
        return LayerPriority.IMAGERY
      default:
        return LayerPriority.VECTOR
    }
  }

  /**
   * @description 比较图层优先级
   * @param otherLayer - 要比较的图层
   * @returns 如果当前图层优先级更高返回正数，相等返回0，更低返回负数
   */
  comparePriority(otherLayer: BaseLayer): number {
    return this._priority - otherLayer._priority
  }
}
