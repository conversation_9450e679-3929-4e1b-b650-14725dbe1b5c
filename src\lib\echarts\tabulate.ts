import { type ECOption } from "./echarts";


export const initTabulateEchart = (data: ISeriesData[], unit: string = '米') => {
  /**
   * 这里的累加有 浮点精度问题
   * 复现方式: 直接使用reduce累加
   * 这里先在每次累加时取前10位并转成数字
   * 最后再只取两位
   */
  const total = data.reduce((sum: number, item: ISeriesData) => {
    if(typeof item.value === 'string' && !isNaN(Number(item.value))) {
      item.value = Number(item.value)
    }
    if(!item.value) {
      item.value = 0
    }
    return Number((sum + item.value).toFixed(10))
    // return Number((sum + Number(item.value).toFixed(10)));
  }, 0)
  // 根据单位决定是否保留小数
  const totalDisplay = unit === '台' ? total.toString() : Number(total.toFixed(2)).toString();

  const option: ECOption = {
    title: {
      text: `总数：${totalDisplay}${unit}`,
      left: 'center',
      top: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 500,
        fontFamily: 'SansCN-Medium',
        color: '#2C3037'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.marker}  ${params.name}：${params.value}${unit}`
      }
    },
    series: [
      {
        type: 'pie',
        center: ['50%','50%'],
        radius: ['70%', '80%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: `{c|{c}${unit}}\n{hr|}`,
          fontSize: 12,
          fontWeight: 500,
          fontFamily: 'SansCN-Regular',
          distanceToLabelLine: 0, // label与line之间的距离
          color: 'inherit',
          rich: {
            // 代替第二段引导线
            hr: {
              borderColor: 'inherit',
              width: '100%',
              borderWidth: 0.5,
              height: 0
            },
          }
        },
        labelLayout: {
          verticalAlign: 'bottom',
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 0
        },
        data: data
      }
    ]
  }

  return option
}