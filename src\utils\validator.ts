/*
 * @Description:
 * @Date: 2022-04-27 10:45:41
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2022-08-29 11:47:00
 */

/**
 * @interface ValidationResult
 * @description 验证结果接口
 */
interface ValidationResult {
  result: boolean;
  errMsg: string;
}

/**
 * @type ValidationRule
 * @description 验证规则类型
 */
type ValidationRule = (value: string) => ValidationResult;

/**
 * @interface ValidationRules
 * @description 验证规则集合接口
 */
interface ValidationRules {
  [key: string]: ValidationRule;
  URL(url: string): ValidationResult;
  LowerCase(str: string): ValidationResult;
  UpperCase(str: string): ValidationResult;
  Alphabets(str: string): ValidationResult;
  Email(email: string): ValidationResult;
  Mobile(mobile: string): ValidationResult;
  Phone(phone: string): ValidationResult;
  Postcode(postcode: string): ValidationResult;
  Number(num: string): ValidationResult;
  Fax(fax: string): ValidationResult;
  Int(num: string): ValidationResult;
  IntPlus(num: string): ValidationResult;
  Float1(num: string): ValidationResult;
  Float2(num: string): ValidationResult;
  Float3(num: string): ValidationResult;
  FloatPlus3(num: string): ValidationResult;
  FloatPlus6(num: string): ValidationResult;
  Encode(code: string): ValidationResult;
  Encode2(code: string): ValidationResult;
  Encode3(code: string): ValidationResult;
  IdCard(code: string): ValidationResult;
  USCC(code: string): ValidationResult;
  CarNum(code: string): ValidationResult;
  CNandEnAndNum(code: string): ValidationResult;
  CNandEN(code: string): ValidationResult;
  CarCode(code: string): ValidationResult;
  cnName(code: string): ValidationResult;
  MobileOrPhone(val: string): ValidationResult;
  password(code: string): ValidationResult;
}

//校验规则列表（可扩展）
const rules: ValidationRules = {
  URL(url: string) {
    const regex =
      /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|top|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?"\\+&%$#=~_-]+))*$/;
    return valid(url, regex, "URL格式不正确");
  },

  LowerCase(str: string) {
    const regex = /^[a-z]+$/;
    return valid(str, regex, "只能输入小写字母");
  },

  UpperCase(str: string) {
    const regex = /^[A-Z]+$/;
    return valid(str, regex, "只能输入大写字母");
  },

  Alphabets(str: string) {
    const regex = /^[A-Za-z]+$/;
    return valid(str, regex, "只能输入字母");
  },

  Email(email: string) {
    const regex =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return valid(email, regex, "邮箱地址格式不正确");
  },

  Mobile(mobile: string) {
    const regex = /^1\d{10}$/;
    return valid(mobile, regex, "手机号格式不正确");
  },

  Phone(phone: string) {
    const regex = /^(0\d{2,3})?-?\d{7,8}$/;
    return valid(phone, regex, "电话号码格式不正确");
  },

  Postcode(postcode: string) {
    const regex = /^[0-9][0-9]{5}$/;
    return valid(postcode, regex, "邮编格式不正确");
  },

  Number(num: string) {
    const regex = /^\d+$/;
    return valid(num, regex, "只能输入纯数字");
  },

  Fax(fax: string) {
    const regex = /^(\d{3,4}-)?\d{7,8}$/;
    return valid(fax, regex, "传真格式不正确");
  },

  Int(num: string) {
    const regex = /^((0)|([1-9]\d*))$/;
    return valid(num, regex, "只能输入非负整数");
  },

  IntPlus(num: string) {
    const regex = /^[1-9]\d*$/;
    return valid(num, regex, "只能输入正整数");
  },

  Float1(num: string) {
    const regex = /^-?\d+(\.\d)?$/;
    return valid(num, regex, "只能输入数字，最多一位小数");
  },

  Float2(num: string) {
    const regex = /^-?\d+(\.\d{1,2})?$/;
    return valid(num, regex, "只能输入数字，最多两位小数");
  },

  Float3(num: string) {
    const regex = /^-?\d+(\.\d{1,3})?$/;
    return valid(num, regex, "只能输入数字，最多三位小数");
  },

  FloatPlus3(num: string) {
    const regex = /^\d+(\.\d{1,3})?$/;
    return valid(num, regex, "只能输入数字，最多三位小数");
  },

  FloatPlus6(num: string) {
    const regex = /^\d+(\.\d{1,6})?$/;
    return valid(num, regex, "只能输入数字，最多六位小数");
  },

  Encode(code: string) {
    const regex = /^(_|-|[a-zA-Z0-9])+$/;
    return valid(code, regex, "编码只能使用字母、数字、下划线、中划线");
  },

  Encode2(code: string) {
    const regex = /^([a-zA-Z0-9]{1,20})$/;
    return valid(code, regex, "只能使用英文、数字,不超过20位");
  },

  Encode3(code: string) {
    const regex = /^(_|[a-zA-Z0-9])+$/;
    return valid(code, regex, "编码只能使用字母、数字、下划线");
  },

  IdCard(code: string) {
    const regex =
      /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    return valid(code, regex, "请输入正确的身份证号码");
  },

  USCC(code: string) {
    const regex = /^[0-9A-Z]{18}/;
    return valid(code, regex, "请输入正确的社会信用号");
  },

  CarNum(code: string) {
    const regex =
      /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/i;
    return valid(code, regex, "请输入正确的车牌号");
  },

  CNandEnAndNum(code: string) {
    const regex = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/;
    return valid(code, regex, '只能使用中文、英文、数字和"_"');
  },

  CNandEN(code: string) {
    const regex = /^[a-zA-Z\u4e00-\u9fa5]+$/;
    return valid(code, regex, "只能使用中文、英文");
  },

  CarCode(code: string) {
    const regex =
      /^(?:[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领 A-Z]{1}[A-HJ-NP-Z]{1}(?:(?:[0-9]{5}[DF])|(?:[DF](?:[A-HJ-NP-Z0-9])[0-9]{4})))|(?:[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领 A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9 挂学警港澳]{1})$/;
    return valid(code, regex, "请输入正确的车牌号");
  },

  cnName(code: string) {
    const regex = /^(?:[\u4e00-\u9fa5·]{2,16})$/;
    console.log(valid(code, regex, "请输入中文名称"));
    return valid(code, regex, "请输入中文名称");
  },

  MobileOrPhone(val: string) {
    const result = /^1\d{10}$/.test(val) || /^(0\d{2,3})?-?\d{7,8}$/.test(val);
    return valid(val, result ? /.*/ : null, "手机或电话号格式不正确");
  },

  password(code: string) {
    const regex = /^(?![a-zA-Z]+$)(?!\d+$)(?![^\da-zA-Z\s]+$).{1,20}$/;
    return valid(code, regex, "由字母、数字、特殊字符任意2种组成,1-20位");
  },
};

/**
 * @description 验证函数
 * @param val 要校验的值
 * @param regex 校验正则,不是正则时val作为result的值
 * @param msg 校验不通过的错误信息
 * @returns 验证结果
 */
function valid(val: string, regex: RegExp | null, msg: string): ValidationResult {
  return {
    result: regex instanceof RegExp ? regex.test(val) : !!val,
    errMsg: msg,
  };
}

/**
 * @interface RuleConfig
 * @description 规则配置接口
 */
interface RuleConfig {
  required: boolean;
  trigger: string;
  message?: string;
  validator?: (rule: any, value: any, callback: (error?: Error) => void) => void;
}

/**
 * @description 生成验证规则
 * @param required 是否必填项，选填，默认"true"
 * @param type 校验类型，选填，String时必须是上面rules中存在的函数名，Function时只接收一个参数(输入值)，返回格式： {result:Boolean, errMsg:String}
 * @param trigger 触发动作，选填，默认"blur"
 * @param nullMsg 未输入的提示语，选填，required=true时有效
 * @returns 验证规则数组
 */
export function vxRule(
  required = true,
  type: string | ValidationRule | null = null,
  trigger = "blur",
  nullMsg = "该字段为必填项"
): RuleConfig[] {
  const rule: RuleConfig = { required: !!required, trigger };

  let check: ValidationRule | null = null;
  if (typeof type === "function") {
    check = type;
  } else {
    check = type ? rules[type] : null;
  }

  if (check) {
    //存在规则时添加规则
    rule.validator = (r: any, v: any, c: (error?: Error) => void) => {
      const { result, errMsg } = check!(v);
      if (required) {
        //必填项: null,undefined,"","  " 都算无输入内容
        return v == null || (v + "").trim() === ""
          ? c(new Error(nullMsg))
          : result
          ? c()
          : c(new Error(errMsg));
      }
      //选填项: null,undefined,"" 都算无输入内容，"  "会被校验
      return v == null || v + "" === "" || result ? c() : c(new Error(errMsg));
    };
  } else {
    rule.message = nullMsg;
  }
  return [rule];
}
