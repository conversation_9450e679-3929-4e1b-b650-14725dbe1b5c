<template>
  <custom-card
    @closeHandler="close"
    :width="'658px'"
    :top="'90px'"
    :right="'120px'"
    :title="mainItem.title"
  >
    <div grid="~ cols-[218px_1fr] gap-y-3 gap-x-5">
      <el-carousel
        height="122px"
        v-if="JSON.parse(mainItem.params.properties?.pictureList).length > 0"
      >
        <el-carousel-item
          v-for="item in JSON.parse(mainItem.params.properties?.pictureList)"
          :key="item"
        >
          <img :src="item.url" class="w-218px h-122px" alt="" />
          <!-- <h3 class="small justify-center" text="2xl">{{ item }}</h3> -->
        </el-carousel-item>
      </el-carousel>
      <div v-else>
        <img
          class="w-218px h-122px"
          :src="getImages('basemap/device-df.png')"
          alt=""
        />
      </div>
      <div>
        <div class="flex items-center mb-4">
          <div class="font-size-3.5 color-#2C3037">
            {{ mainItem.params.properties?.name }}
          </div>
          <div class="line"></div>
          <div class="font-size-3 color-#005DFF">
            设备状态：{{
              mainItem.params.properties?.status === 0 ? "可用" : "禁用"
            }}
          </div>
        </div>
        <base-key-value
          label="设备型号"
          :value="mainItem.params.properties?.modelNumber"
        />
        <base-key-value
          label="设备标识"
          :value="mainItem.params.properties?.code"
          class="my-3"
        />
        <base-key-value
          label="所属位置"
          :value="mainItem.params.properties?.address"
        />
      </div>
    </div>
    <div class="mt-5">
      <el-form
        ref="form"
        :model="mainItem.params.properties"
        class="admin-sub-form"
        label-width="auto"
        :disabled="true"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备大类：">
              <el-input
                class=""
                v-model.trim="mainItem.params.properties.firstDeviceTypeName"
                placeholder="请输入设备大类"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备小类：">
              <el-input
                class=""
                v-model.trim="mainItem.params.properties.secondDeviceTypeName"
                placeholder="请输入设备小类"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产日期：">
              <el-input
                class=""
                v-model.trim="mainItem.params.properties.equipmentTime"
                placeholder="无"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产商名称：">
              <el-input
                class=""
                v-model.trim="mainItem.params.properties.factoryName"
                placeholder="请输入产商名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经度：">
              <el-input
                class=""
                v-model.trim="mainItem.params.properties.longitude"
                placeholder="无"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度：">
              <el-input
                class=""
                v-model.trim="mainItem.params.properties.latitude"
                placeholder="无"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备在线状态：">
              <el-select
                v-model.trim="mainItem.params.properties.onlineStatus"
                placeholder="请选择状态"
              >
                <el-option label="在线" :value="1"></el-option>
                <el-option label="离线" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="flex justify-end">
      <el-button type="primary" class="btn mt-5" @click="updateClick"
        >上传挂接</el-button
      >
    </div>
  </custom-card>
</template>
<script lang="ts" setup>
import { getDevice } from "@/api/query";
import { getImages } from "@/utils/getImages";
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});
// const formData = ref(props.mainItem.params.properties);
const list = ref<any>([]);
// const dataInfo = ref<any>();
// interface FormData {
//   firstDeviceTypeName: string;
//   secondDeviceTypeName: string;
//   equipmentTime: string;
//   factoryName: string;
// }
// const initFormData = () => {
//   return {
//     firstDeviceTypeName: "",
//     secondDeviceTypeName: "",
//     equipmentTime: "",
//     factoryName: "",
//   };
// };
// const formData = ref<FormData>(initFormData());
const getData = async () => {
  // const result = await getDevice(props.mainItem.params.properties.code);
  // dataInfo.value = result.data;
  // formData.value = result.data;
  // console.log(formData, "111");
};
const close = () => {
  useDialogStore().closeDialog("DeviceDetail");
};
const updateClick = () => {
  useDialogStore().closeDialog("DeviceDetail");
  useDialogStore().addDialog({
    name: "上传挂接",
    path: "UploadFile",
    params: {
      code: props.mainItem.params.properties.code,
      first_device_type_code:
        props.mainItem.params.properties.firstDeviceTypeCode,
      pictureList: JSON.parse(props.mainItem.params.properties.pictureList),
    },
  });
};
onMounted(async () => {
  await getData();
});
</script>
<style lang="scss" scoped>
.label {
  text-align: justify;
  text-align-last: justify; // 添加这一句，对最后一行生效
}
.line {
  width: 1px;
  height: 14px;
  background: #c4c4c4;
  margin: 0 20px;
}
</style>
