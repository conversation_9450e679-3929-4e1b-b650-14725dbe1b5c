<template>
  <div class="footer">
    <div class="btn">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="btn-item"
        :class="isActive === index ? 'active' : ''"
        @click="handleClick(item, index)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>


const props = defineProps({
  type: {
    type: String,
    default: "maplibre",
  },
});
const isActive = ref(0);
const list = ref([
  {
    label: "横断面分析",
    value: "VerticalProfileAnalysis",
  },
  {
    label: "纵断面分析",
    value: "HorizontalProfileAnalysis",
  },
  {
    label: "埋深分析",
    value: "DepthAnalysis",
  },
  {
    label: "缓冲区分析",
    value: "BufferAnalysis",
  },
  {
    label: "水平净距分析",
    value: "HorizontalDistanceAnalysis",
  },
  {
    label: "垂直净距分析",
    value: "VerticalDistanceAnalysis",
  },
  {
    label: "流向分析",
    value: "FlowAnalysis",
  },
  {
    label: "连通性分析",
    value: "ConnectivityAnalysis",
  },
  {
    label: "爆管分析",
    value: "BurstAnalysis",
  },
  {
    label: "隧道模拟",
    value: "TunnelSimulation",
  },
]);
const handleClick = (item: any, index: number) => {
  isActive.value = index;
  useDialogStore().addDialog({
    name: item.label,
    path: item.value,
    type: props.type,
  })
};
</script>
<style lang="scss" scoped>
.footer {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 40px;
  z-index: 2;
}
.btn {
  display: flex;
  .btn-item {
    width: 170px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: url(@/assets/images/btn.png) no-repeat;
    margin-right: 20px;
    color: #a7dcff;
  }
  .active {
    background: url(@/assets/images/btn-active.png) no-repeat;
    color: #fff;
  }
}
</style>
