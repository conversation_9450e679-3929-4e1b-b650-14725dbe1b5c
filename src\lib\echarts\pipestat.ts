import { echarts } from './echarts'
export const initPipeStatEchart = (echartData: any) => {
  const attackSourcesColor = [
    new echarts.graphic.LinearGradient(1, 0, 0, 0, [
      {
        offset: 0,
        color: '#53C4FF'
      },
      {
        offset: 1,
        color: '#1966FF'
      }
    ]),
    new echarts.graphic.LinearGradient(1, 0, 0, 0, [
      {
        offset: 0,
        color: '#B9E7FF'
      },
      {
        offset: 1,
        color: '#00C2FF'
      }
    ]),
    new echarts.graphic.LinearGradient(1, 0, 0, 0, [
      {
        offset: 0,
        color: '#2EE57D'
      },
      {
        offset: 1,
        color: '#AAEFC2'
      }
    ]),
    new echarts.graphic.LinearGradient(1, 0, 0, 0, [
      {
        offset: 0,
        color: '#FFEBB5'
      },
      {
        offset: 1,
        color: '#FFB506'
      }
    ]),
    new echarts.graphic.LinearGradient(1, 0, 0, 0, [
      {
        offset: 0,
        color: '#FD2A2A'
      },
      {
        offset: 1,
        color: '#FFB5B5'
      }
    ]),

  ]
  const color = ['#F0F4FF', '#EAF3F7', '#EAF7ED', '#FFF5F0', '#FFF5F0']
  if (echartData.length < 1) return {}
  let max: number = Number(echartData[0].visits)
  for (let i = 0; i < echartData.length - 1; i++) {
    max = max < Number(echartData[i + 1].visits) ? Number(echartData[i + 1].visits) : max
  }
  const option: any = {
    tooltip: {
      trigger: 'axis',
      formatter(params: any) {
        for (let i = 0; i < params.length; i++) {
          return params[i].name + ':' + params[i].data.value + 'm'
        }
      }
    },
    legend: {
      show: false
    },
    grid: {
      top: '4%',
      left: '4%',
      right: -30,
      bottom: 0
      // containLabel: true,
    },
    xAxis: {
      show: false,
      type: 'value'
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        data: echartData.map((item: { name: any }) => item.name),
        axisLabel: {
          margin: 0,
          fontSize: 14,
          fontWright: 400,
          align: 'left',
          color: '#202020',
          padding: [20, 0, 60, 0],
          interval: 0,
          rich: {
            a1: {
              color: '#A7EBFA',
              fontSize: 20,
              fontWeight: 400,
              fontFamily: 'PangMen'
            },
            a2: {
              color: '#A7EBFA',
              fontSize: 20,
              fontWeight: 400,
              fontFamily: 'PangMen'
            },
            a3: {
              color: '#A7EBFA',
              fontSize: 20,
              fontWeight: 400,
              fontFamily: 'PangMen'
            },
            b: {
              color: '#A7EBFA',
              fontSize: 20,
              fontWeight: 400,
              fontFamily: 'PangMen'
            },
            c: {
              color: '#2C3037',
              padding: [4, 0, 0, 20],
              align: 'center',
              fontSize: 14,
              fontWeight: 400,
              fontFamily: 'Microsoft YaHei, Microsoft YaHei-Regular'
            }
          },
          // formatter: function (params: any) {
          //   let index = echartData.map((item: { name: any }) => item.name).indexOf(params)
          //   index = index + 1
          //   if (index - 1 < 3) {
          //     return ['{a' + index + '|' + 'TOP' + index + '}' + '{c|' + params + '}'].join('\n')
          //   } else {
          //     return ['{b|' + 'TOP' + index + '}' + '{c|' + params + '}'].join('\n')
          //   }
          // }
        }
      },
      {
        triggerEvent: true,
        show: true,
        inverse: true,
        data: echartData.map((item: { visits: any }) => item.visits),
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          interval: 0,
          // shadowOffsetX: '-60px',
          color: '#2C3037',
          align: 'right',
          verticalAlign: 'bottom',
          lineHeight: 30,
          fontSize: 16,
          fontFamily: 'PangMen',
          padding: [0, 60, 5, 0],
          rich: {
            a1: {
              color: '#ff4343',
              fontSize: 18,
              fontWeight: 700,
              fontFamily: 'Source-CN-Bold'
            },
            a2: {
              color: '#FFC743',
              fontSize: 18,
              fontWeight: 700,
              fontFamily: 'Source-CN-Bold'
            },
            a3: {
              color: '#0066FF',
              fontSize: 18,
              fontWeight: 700,
              fontFamily: 'Source-CN-Bold'
            },
            b: {
              color: '#91A0B9',
              fontSize: 18,
              fontWeight: 700,
              fontFamily: 'Source-CN-Bold'
            }
          },
          formatter: function (params: any) {
            let index = echartData.map((item: { visits: any }) => item.visits).indexOf(params)
            index = index + 1
            if (index - 1 < 3) {
              return ['{a' + index + '|' + params + '}' + 'm'].join('\n')
            } else {
              return ['{b|' + params + '}' + 'm'].join('\n')
            }
          }
        }
      }
    ],
    series: [
      {
        z: 2,
        name: 'value',
        type: 'bar',
        barWidth: 10,
        zlevel: 1,
        itemStyle: {
          // barBorderRadius: [0, 20, 20, 0],
          color: (colors: any) => {
            return attackSourcesColor[colors.dataIndex]
          }
        },
        data: echartData.map((item: { visits: any }) => {
          return {
            value: item.visits,
          }
        }),
        // label: {
        //   show: false,
        //   position: 'right',
        // }
        label: {
          normal: {
            show: true,
            position: "right",
            //TODO
            offset: [650, -20],
            formatter: "{c}",
          },
        },
      },
      {
        name: '背景',
        type: 'bar',
        barWidth: 10,
        barGap: '-100%',
        itemStyle: {
          color: (colors: any) => {
            return color[colors.dataIndex]
          }
          // color: color
        },
        data: new Array(echartData.length).fill(max * 3 / 2)
      }
    ]
  }
  return option
}