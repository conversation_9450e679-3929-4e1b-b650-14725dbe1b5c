
// import type { <PERSON><PERSON><PERSON> } from "element-plus/es/components/tree-v2/src/types"
export interface QueryForm {
  type: string;
  name: string;
  sys: string;
  pageSize: number;
  pageNum: number;
}
export interface SysIcon {
  /**
   * 类型
   */
  type: string
  /**
   * ID
   */
  id: string
  /**
   * 图标名称
   */
  name: string
  /**
   * 应用系统
   */
  sys: string
  // /**
  //  * 图标图片
  //  */
  // url: string
  /**
   * 图标图片
   */
  fileInfoDto: any

}