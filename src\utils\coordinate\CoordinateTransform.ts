/**
 * @fileoverview 坐标转换工具类
 * @description 提供多种坐标系之间的转换功能，支持WGS84、GCJ02、BD09等
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * @constant 椭球体参数
 */
const ELLIPSOID = {
  /** WGS84椭球体长轴 */
  WGS84_A: 6378137.0,
  /** WGS84椭球体扁率 */
  WGS84_F: 1 / 298.257223563,
  /** 克拉索夫斯基椭球体长轴 */
  KRASOVSKY_A: 6378245.0,
  /** 克拉索夫斯基椭球体扁率 */
  KRASOVSKY_F: 1 / 298.3
};

/**
 * @interface CoordinatePoint
 * @description 坐标点接口
 */
export interface CoordinatePoint {
  /** 经度或X坐标 */
  lng: number;
  /** 纬度或Y坐标 */
  lat: number;
  /** 高程或Z坐标 */
  alt?: number;
}

/**
 * @class CoordinateTransform
 * @description 坐标转换工具类，提供各种坐标系之间的转换功能
 */
export class CoordinateTransform {
  /**
   * 判断坐标是否在中国境内
   * @param lng - 经度
   * @param lat - 纬度
   * @returns 是否在中国境内
   */
  static isInChina(lng: number, lat: number): boolean {
    return lng >= 72.004 && lng <= 137.8347 && lat >= 0.8293 && lat <= 55.8271;
  }

  /**
   * 计算纬度偏移量
   * @param lng - 经度
   * @param lat - 纬度
   * @returns 纬度偏移量
   */
  private static transformLat(lng: number, lat: number): number {
    let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * 计算经度偏移量
   * @param lng - 经度
   * @param lat - 纬度
   * @returns 经度偏移量
   */
  private static transformLng(lng: number, lat: number): number {
    let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin(lng / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(lng / 12.0 * Math.PI) + 300.0 * Math.sin(lng / 30.0 * Math.PI)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * WGS84坐标转GCJ02坐标
   * @param point - WGS84坐标点
   * @returns GCJ02坐标点
   */
  static wgs84ToGcj02(point: CoordinatePoint): CoordinatePoint {
    const { lng, lat, alt = 0 } = point;
    
    if (!this.isInChina(lng, lat)) {
      return { lng, lat, alt };
    }

    let dLat = this.transformLat(lng - 105.0, lat - 35.0);
    let dLng = this.transformLng(lng - 105.0, lat - 35.0);
    const radLat = lat / 180.0 * Math.PI;
    let magic = Math.sin(radLat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    const sqrtMagic = Math.sqrt(magic);
    dLat = (dLat * 180.0) / ((ELLIPSOID.WGS84_A * (1 - 0.00669342162296594323)) / (magic * sqrtMagic) * Math.PI);
    dLng = (dLng * 180.0) / (ELLIPSOID.WGS84_A / sqrtMagic * Math.cos(radLat) * Math.PI);

    return {
      lng: lng + dLng,
      lat: lat + dLat,
      alt
    };
  }

  /**
   * GCJ02坐标转WGS84坐标
   * @param point - GCJ02坐标点
   * @returns WGS84坐标点
   */
  static gcj02ToWgs84(point: CoordinatePoint): CoordinatePoint {
    const { lng, lat, alt = 0 } = point;
    
    if (!this.isInChina(lng, lat)) {
      return { lng, lat, alt };
    }

    let dLat = this.transformLat(lng - 105.0, lat - 35.0);
    let dLng = this.transformLng(lng - 105.0, lat - 35.0);
    const radLat = lat / 180.0 * Math.PI;
    let magic = Math.sin(radLat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    const sqrtMagic = Math.sqrt(magic);
    dLat = (dLat * 180.0) / ((ELLIPSOID.WGS84_A * (1 - 0.00669342162296594323)) / (magic * sqrtMagic) * Math.PI);
    dLng = (dLng * 180.0) / (ELLIPSOID.WGS84_A / sqrtMagic * Math.cos(radLat) * Math.PI);

    return {
      lng: lng - dLng,
      lat: lat - dLat,
      alt
    };
  }

  /**
   * GCJ02坐标转BD09坐标
   * @param point - GCJ02坐标点
   * @returns BD09坐标点
   */
  static gcj02ToBd09(point: CoordinatePoint): CoordinatePoint {
    const { lng, lat, alt = 0 } = point;
    const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * Math.PI * 3000.0 / 180.0);
    const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * Math.PI * 3000.0 / 180.0);
    
    return {
      lng: z * Math.cos(theta) + 0.0065,
      lat: z * Math.sin(theta) + 0.006,
      alt
    };
  }

  /**
   * BD09坐标转GCJ02坐标
   * @param point - BD09坐标点
   * @returns GCJ02坐标点
   */
  static bd09ToGcj02(point: CoordinatePoint): CoordinatePoint {
    const { lng, lat, alt = 0 } = point;
    const x = lng - 0.0065;
    const y = lat - 0.006;
    const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * Math.PI * 3000.0 / 180.0);
    const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * Math.PI * 3000.0 / 180.0);
    
    return {
      lng: z * Math.cos(theta),
      lat: z * Math.sin(theta),
      alt
    };
  }

  /**
   * WGS84坐标转BD09坐标
   * @param point - WGS84坐标点
   * @returns BD09坐标点
   */
  static wgs84ToBd09(point: CoordinatePoint): CoordinatePoint {
    const gcj02 = this.wgs84ToGcj02(point);
    return this.gcj02ToBd09(gcj02);
  }

  /**
   * BD09坐标转WGS84坐标
   * @param point - BD09坐标点
   * @returns WGS84坐标点
   */
  static bd09ToWgs84(point: CoordinatePoint): CoordinatePoint {
    const gcj02 = this.bd09ToGcj02(point);
    return this.gcj02ToWgs84(gcj02);
  }

  /**
   * 度转弧度
   * @param degree - 角度值
   * @returns 弧度值
   */
  static degreeToRadian(degree: number): number {
    return degree * Math.PI / 180.0;
  }

  /**
   * 弧度转度
   * @param radian - 弧度值
   * @returns 角度值
   */
  static radianToDegree(radian: number): number {
    return radian * 180.0 / Math.PI;
  }

  /**
   * 格式化坐标显示
   * @param point - 坐标点
   * @param precision - 小数点精度，默认6位
   * @returns 格式化后的坐标对象
   */
  static formatCoordinate(point: CoordinatePoint, precision: number = 6): CoordinatePoint {
    return {
      lng: Number(point.lng.toFixed(precision)),
      lat: Number(point.lat.toFixed(precision)),
      alt: point.alt ? Number(point.alt.toFixed(3)) : 0
    };
  }

  /**
   * 转换坐标到指定坐标系
   * @param point - 源坐标点
   * @param fromType - 源坐标系类型
   * @param toType - 目标坐标系类型
   * @returns 转换后的坐标点
   */
  static transform(
    point: CoordinatePoint, 
    fromType: 'WGS84' | 'GCJ02' | 'BD09', 
    toType: 'WGS84' | 'GCJ02' | 'BD09'
  ): CoordinatePoint {
    if (fromType === toType) {
      return { ...point };
    }

    let result: CoordinatePoint;

    // 先转换为WGS84作为中间坐标系
    switch (fromType) {
      case 'GCJ02':
        result = this.gcj02ToWgs84(point);
        break;
      case 'BD09':
        result = this.bd09ToWgs84(point);
        break;
      default:
        result = { ...point };
    }

    // 从WGS84转换到目标坐标系
    switch (toType) {
      case 'GCJ02':
        return this.wgs84ToGcj02(result);
      case 'BD09':
        return this.wgs84ToBd09(result);
      default:
        return result;
    }
  }
} 