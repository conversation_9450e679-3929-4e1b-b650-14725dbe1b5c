/**
 * @fileoverview MapLibre模块路由配置
 * @description 定义MapLibre二维地图模块的路由规则和组件映射
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * @description MapLibre模块路由配置对象
 * @details 配置二维地图模块的路由结构，包括布局组件和子路由
 */
export const maplibreRouter = {
  path: '/maplibre',
  name: 'Maplibre',
  meta: {
    title: '二维模块',
  },
  redirect: '/maplibre/home',
  component: () => import('@/views/maplibre/index.vue'),
  children: [
    {
      path: 'home',
      name: 'MaplibreHome',
      meta: {
        title: 'GIS一张图',
        icon: 'map',
        acIcon: 'map-ac'
      },
      component: () => import('@/views/maplibre/pages/home/<USER>'),
    },
    {
      path: 'query',
      name: 'MaplibreQuery',
      meta: {
        title: '查询统计',
        icon: 'query',
        acIcon: 'query-ac'
      },
      component: () => import('@/views/maplibre/pages/query/index.vue'),
      redirect: '/maplibre/query/spatial',
      children: [
        {
          path: 'spatial',
          name: 'SpatialQuery',
          meta: { title: '空间查询' },
          component: () => import('@/views/maplibre/pages/query/SpatialQuery.vue')
        },
        {
          path: 'classified-query',
          name: 'ClassifiedQuery',
          meta: { title: '分类查询' },
          component: () => import('@/views/maplibre/pages/query/ClassifiedQuery.vue')
        },
        {
          path: 'tabulate',
          name: 'TabulateStatistics',
          meta: { title: '汇总统计' },
          component: () => import('@/views/maplibre/pages/query/TabulateStatistics.vue')
        },
        {
          path: 'general',
          name: 'GeneralStatistics',
          meta: { title: '通用统计' },
          component: () => import('@/views/maplibre/pages/query/GeneralStatistics.vue')
        },
        {
          path: 'classified',
          name: 'ClassifiedStatistics',
          meta: { title: '分类统计' },
          component: () => import('@/views/maplibre/pages/query/ClassifiedStatistics.vue')
        }
      ]
    },
    {
      path: 'pipeNetwork',
      name: 'MaplibrePipeNetwork',
      meta: {
        title: '管网分析',
        icon: 'pipe',
        acIcon: 'pipe-ac'
      },
      component: () => import('@/views/maplibre/pages/pipeNetwork/index.vue'),
      redirect: '/maplibre/pipeNetwork/crossSection',
      children: [
        {
          path: 'crossSection',
          name: 'CrossSection',
          meta: { title: '横断面分析' },
          component: () => import('@/views/analysis/VerticalProfileAnalysis.vue')
        },
        {
          path: 'horizontalProfile',
          name: 'HorizontalProfile',
          meta: { title: '纵断面分析' },
          component: () => import('@/views/analysis/HorizontalProfileAnalysis.vue')
        },
        {
          path: 'depthAnalysis',
          name: 'DepthAnalysis',
          meta: { title: '埋深分析' },
          component: () => import('@/views/analysis/DepthAnalysis.vue')
        },
        {
          path: 'bufferAnalysis',
          name: 'BufferAnalysis',
          meta: { title: '缓冲区分析' },
          component: () => import('@/views/analysis/BufferAnalysis.vue')
        },
        {
          path: 'horizontalDistanceAnalysis',
          name: 'HorizontalDistanceAnalysis',
          meta: { title: '水平净距分析' },
          component: () => import('@/views/analysis/HorizontalDistanceAnalysis.vue')
        },
        {
          path: 'verticalDistanceAnalysis',
          name: 'VerticalDistanceAnalysis',
          meta: { title: '垂直净距分析' },
          component: () => import('@/views/analysis/VerticalDistanceAnalysis.vue')
        },
        {
          path: 'connectivityAnalysis',
          name: 'ConnectivityAnalysis',
          meta: { title: '连通性分析' },
          component: () => import('@/views/analysis/ConnectivityAnalysis.vue')
        },
        {
          path: 'burstAnalysis',  
          name: 'BurstAnalysis',
          meta: { title: '爆管分析' },
          component: () => import('@/views/analysis/BurstAnalysis.vue')
        },
        {
          path: 'flowAnalysis',
          name: 'FlowAnalysis',
          meta: { title: '流向分析' },
          component: () => import('@/views/analysis/CsFlowAnalysis.vue')
        }
      ]
    },
    {
      path: 'special',
      name: 'MaplibreSpecial',
      meta: {
        title: '专题展示',
        icon: 'special',
        acIcon: 'special-ac'
      },
      component: () => import('@/views/maplibre/pages/special/index.vue'),
      redirect: '/maplibre/special/pipeDiameter',
      children: [
        {
          path: 'pipeDiameter',
          name: 'PipeDiameter',
          meta: { title: '管网口径专题' },
          component: () => import('@/views/maplibre/pages/special/PipeDiameter.vue')
        },
        {
          path: 'pipeMaterial',
          name: 'PipeMaterial',
          meta: { title: '管网材质专题' },
          component: () => import('@/views/maplibre/pages/special/PipeMaterial.vue')
        },
        {
          path: 'waterMeter',
          name: 'WaterMeter',
          meta: { title: '水表专题' },
          component: () => import('@/views/maplibre/pages/special/WaterMeter.vue')
        },
        {
          path: 'valve',
          name: 'Valve',
          meta: { title: '阀门专题' },
          component: () => import('@/views/maplibre/pages/special/Valve.vue')
        },
        {
          path: 'fireHydrant',
          name: 'FireHydrant',
          meta: { title: '消防栓专题' },
          component: () => import('@/views/maplibre/pages/special/FireHydrant.vue')
        },
        {
          path: 'pipeHistory',
          name: 'PipeHistory',
          meta: { title: '管网历史专题' },
          component: () => import('@/views/maplibre/pages/special/PipeHistory.vue')
        },
        
      ]
    },
    {
      path: 'dataEdition',
      name: 'MaplibreDataEdition',
      meta: {
        title: '数据编辑',
        icon: 'data',
        acIcon: 'data-ac'
      },
      component: () => import('@/views/maplibre/pages/dataEdition/index.vue'),
      redirect: '/maplibre/dataEdition/pipeEdition',
      children: [
        {
          path: 'pipeEdition',
          name: 'PipeEdition',
          meta: { title: '管网编辑' },
          component: () => import('@/views/maplibre/pages/dataEdition/PipeEdit.vue')
        },
        {
          path: 'pipeCheck',
          name: 'PipeCheck',
          meta: { title: '管网检查' },
          component: () => import('@/views/maplibre/pages/dataEdition/PipeCheck.vue')
        },
        {
          path: 'importExport',
          name: 'ImportExport',
          meta: { title: '导入导出' },
          component: () => import('@/views/maplibre/pages/dataEdition/ImportExport.vue')
        },
        {
          path: 'userHookList',
          name: 'UserHookList',
          meta: { title: '用户挂接' },
          component: () => import('@/views/maplibre/pages/dataEdition/UserHookList.vue')
        },
        
      ]
    },
  ],
};
