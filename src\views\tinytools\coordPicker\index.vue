<!--
 * @Description: 坐标拾取组件
 * @Date: 2022-04-22 08:54:28
 * @Author: GISerZ
 * @LastEditor: 项目开发团队
 * @LastEditTime: 2023-08-10 15:00:00
-->
<template>
  <custom-card
    :width="'460px'"
    @closeHandler="close"
    v-show="mainItem.show"
    :main-item="mainItem"
    :title="mainItem.title ?? (mainItem.title = '坐标拾取工具')"
  >
    <div class="action-buttons">
      <el-button
        type="primary"
        class="primary-btn w-130px"
        :disabled="isPicking"
        @click="startPicking"
      >
        开始拾取
      </el-button>
      <el-button
        type="danger"
        class="danger-btn w-130px"
        :disabled="!isPicking"
        @click="stopPicking"
      >
        停止拾取
      </el-button>
      <el-button class="w-130px clear-btn" @click="resetCoordinates">
        清空坐标
      </el-button>
    </div>
    <div class="tip-container cursor-pointer">
      <el-alert type="info" :closable="false" show-icon>
        <template #title>
          <span class="tip-text">
            {{ mapType === 'cesium' ? '点击三维地球获取坐标（包含高程）' : '点击地图获取坐标' }}
          </span>
        </template>
      </el-alert>
    </div>

    <el-descriptions border :column="1" class="coord-descriptions">
      <!-- WGS84坐标 -->
      <el-descriptions-item
        label="WGS84"
        class-name="coord-value"
        label-class-name="coord-label"
      >
        <div class="coord-row">
          <span>{{ formatCoord(wgs84, 6) }}</span>
          <el-button
            type="primary"
            size="small"
            circle
            title="复制坐标"
            @click="copyCoordinate('wgs84')"
          >
            <el-icon><DocumentCopy /></el-icon>
          </el-button>
        </div>
      </el-descriptions-item>

      <!-- GCJ02坐标 -->
      <el-descriptions-item
        label="GCJ02"
        class-name="coord-value"
        label-class-name="coord-label"
      >
        <div class="coord-row">
          <span>{{ formatCoord(gcj02, 6) }}</span>
          <el-button
            type="primary"
            size="small"
            circle
            title="复制坐标"
            @click="copyCoordinate('gcj02')"
          >
            <el-icon><DocumentCopy /></el-icon>
          </el-button>
        </div>
      </el-descriptions-item>

      <!-- BD09坐标 -->
      <el-descriptions-item
        label="BD09"
        class-name="coord-value"
        label-class-name="coord-label"
      >
        <div class="coord-row">
          <span>{{ formatCoord(bd09, 6) }}</span>
          <el-button
            type="primary"
            size="small"
            circle
            title="复制坐标"
            @click="copyCoordinate('bd09')"
          >
            <el-icon><DocumentCopy /></el-icon>
          </el-button>
        </div>
      </el-descriptions-item>

      <!-- EPSG:3857坐标 -->
      <el-descriptions-item
        label="EPSG:3857"
        class-name="coord-value"
        label-class-name="coord-label"
      >
        <div class="coord-row">
          <span>{{ formatCoord(epsg3857, 2) }}</span>
          <el-button
            type="primary"
            size="small"
            circle
            title="复制坐标"
            @click="copyCoordinate('epsg3857')"
          >
            <el-icon><DocumentCopy /></el-icon>
          </el-button>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </custom-card>
</template>

<script lang="ts" setup>
import { DocumentCopy } from "@element-plus/icons-vue";
import CustomCard from "@/components/dialog/CustomCard.vue";
import { useDialogStore } from "@/stores/Dialogs";
import { watch, onMounted } from "vue";
import { useCoordinatePicker } from "./useCoordinatePicker";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { AppCesium } from "@/lib/cesium/AppCesium";
import type { CoordinatePoint } from "@/utils/coordinate/CoordinateTransform";

/**
 * 组件属性定义
 */
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});

/**
 * 根据传入的地图类型确定坐标拾取器类型
 */
const mapType = props.mainItem.type === "cesium" ? "cesium" : "maplibre";

/**
 * 获取地图实例
 * @returns 对应类型的地图实例
 */
const getMapInstance = () => {
  if (mapType === "maplibre") {
    return AppMaplibre._map;
  } else {
    // 获取 Cesium 实例
    const viewer = AppCesium.getInstance().getViewer();
    return viewer;
  }
};

/**
 * 初始化坐标拾取逻辑
 */
const {
  wgs84,
  gcj02,
  bd09,
  epsg3857,
  isPicking,
  startPicking,
  stopPicking,
  resetCoordinates,
  copyCoordinate,
} = useCoordinatePicker({
  mapType,
  mapInstance: getMapInstance(),
  showMarker: true,
  markerStyle: {
    color: "#409EFF",
    size: 10,
    opacity: 0.9,
  },
});

/**
 * 格式化坐标显示
 * @param coord - 坐标对象
 * @param precision - 精度（小数位数）
 * @returns 格式化后的坐标字符串
 */
const formatCoord = (coord: CoordinatePoint, precision: number): string => {
  if (
    !coord ||
    typeof coord.lng !== "number" ||
    typeof coord.lat !== "number"
  ) {
    return "暂无数据";
  }

  // 如果是Cesium三维地图且有高程信息，显示三维坐标
  if (mapType === 'cesium' && typeof coord.alt === 'number' && !isNaN(coord.alt)) {
    return `${coord.lng.toFixed(precision)}, ${coord.lat.toFixed(precision)}, ${coord.alt.toFixed(2)}`;
  }

  // 否则只显示经纬度
  return `${coord.lng.toFixed(precision)}, ${coord.lat.toFixed(precision)}`;
};

/**
 * 组件加载时自动开始拾取
 */
onMounted(() => {
  // 延迟一下，确保地图实例已完全加载
  setTimeout(() => {
    if (props.mainItem.show) {
      startPicking();
    }
  }, 500);
});

/**
 * 监听主项目显示状态变化
 */
watch(
  () => props.mainItem.show,
  (newVal) => {
    // 当组件显示时自动开始拾取，隐藏时停止拾取
    if (newVal) {
      startPicking();
    } else if (isPicking.value) {
      stopPicking();
    }
  }
);

/**
 * 关闭处理
 */
const close = () => {
  // 停止坐标拾取
  if (isPicking.value) {
    stopPicking();
  }

  // 关闭对话框
  useDialogStore().closeDialog("CoordPicker");
};
</script>

<style lang="scss" scoped>
.tip-container {
  margin-bottom: 15px;
}

.tip-text {
  font-size: 14px;
  color: #bfbfbf;
}

.coord-descriptions {
  margin-bottom: 15px;
  --el-border-color-lighter: rgb(221, 221, 221);
}

.coord-label {
  width: 100px;
  background-color: #f5f7fa;
  font-weight: bold;
}

.coord-value {
  padding: 12px 15px;
}

.coord-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

:deep(.el-descriptions__label) {
  width: 100px;
  background: rgb(242, 246, 251);
}
:deep(
    .el-descriptions__body
      .el-descriptions__table.is-bordered
      .el-descriptions__cell
  ) {
  text-align: center;
  color: #2c3037;
  font-weight: 400;
  font-size: 14px;
}

:deep(.el-alert__title) {
  font-size: 14px;
}

:deep(.el-button.is-circle) {
  padding: 5px;
  margin-left: 8px;
}
:deep(.el-alert--info.is-light) {
  background: transparent;
  color: #bfbfbf;
}
</style>
