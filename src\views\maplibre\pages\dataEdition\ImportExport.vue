<template>
  <page-card :close-icon="false" class="tabulate-sta" title="导入导出">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleShpImport"
        :loading="isImportingShp"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/13.png')" alt="" srcset="" />
        </i>
        SHAPE导入
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleShpExportClick"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/14.png')" alt="" srcset="" />
        </i>
        SHAPE导出
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleDxfExport"
        :loading="isExportingDxf"
        :disabled="isExportingDxf"
        title="导出管线数据为DXF格式文件 (Ctrl+D)"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/15.png')" alt="" srcset="" />
        </i>
        {{ isExportingDxf ? "导出中..." : "CAD导出" }}
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleExcelImport"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/16.png')" alt="" srcset="" /> </i
        >Excel导入</el-button
      >
    </div>
  </page-card>

  <!-- shp导入面板 -->
  <ShpImportPanel v-if="showShpImportDialog" @close="handleShpImportClose" />

  <ExcelImportPanel
    v-if="showExcelImportDialog"
    @close="handleExcelImportClose"
  />

  <ShpExportPanel v-if="showShpExportPanel" @close="handleShpExportCancel" />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { ElMessage, ElNotification } from "element-plus";
import { Tools, Search } from "@element-plus/icons-vue";
import PageCard from "@/components/PageCard.vue";
import ShpImportPanel from "./ShpImportPanel.vue";
import ExcelImportPanel from "./ExcelImportPanel.vue";
import { exportDxf } from "@/api/pipeLine";
import { exportDxfFile } from "@/utils/file";
import ShpExportPanel from "./ShpExportPanel.vue";
import { getImages } from "@/utils/getImages";
// ============ 响应式状态 ============

/** 悬挂线检查相关状态 */
const showShpImportDialog = ref(false);
const isImportingShp = ref(false);
const showExcelImportDialog = ref(false);

/** DXF导出相关状态 */
const isExportingDxf = ref(false);
const lastExportTime = ref<string>("");
const exportProgress = ref<number>(0);

// ============ SHP导出相关状态 ============
const showShpExportPanel = ref(false);

// ============ 事件处理方法 ============

/**
 * @function handleHangingLineCheck
 * @description 处理悬挂线检查
 */
const handleShpImport = (): void => {
  try {
    if (showShpImportDialog.value) {
      // 如果面板已打开，直接关闭
      showShpImportDialog.value = false;
      return;
    }

    // 打开Shp导入面板
    showShpImportDialog.value = true;
  } catch (error) {
    console.error("启动悬挂线检查失败:", error);
  }
};

/**
 * @function handleHangingLineClose
 * @description 处理悬挂线检查面板关闭
 */
const handleShpImportClose = (): void => {
  try {
    showShpImportDialog.value = false;
    console.log("悬挂线检查面板已关闭");
  } catch (error) {
    console.error("关闭悬挂线检查面板失败:", error);
    ElMessage.error("关闭面板失败");
  }
};

const handleExcelImport = (): void => {
  showExcelImportDialog.value = true;
};

const handleExcelImportClose = (): void => {
  showExcelImportDialog.value = false;
};

/**
 * @function handleDxfExport
 * @description 处理DXF文件导出
 */
const handleDxfExport = async (): Promise<void> => {
  if (isExportingDxf.value) {
    ElMessage.warning("DXF导出正在进行中，请稍候...");
    return; // 防止重复点击
  }

  try {
    isExportingDxf.value = true;
    ElMessage.info("正在导出DXF文件，请稍候...");

    // 调用API导出DXF文件
    const response = await exportDxf();

    // 检查响应是否有效
    if (!response || !response.data) {
      throw new Error("服务器返回数据为空，请检查网络连接或联系管理员");
    }

    // 检查文件大小（大于100MB时提醒）
    const fileSizeInMB = response.data.size / (1024 * 1024);
    if (fileSizeInMB > 100) {
      ElMessage.warning(
        `文件较大(${fileSizeInMB.toFixed(2)}MB)，下载可能需要较长时间`
      );
    }

    // 使用专用工具函数处理文件下载
    const result = exportDxfFile(response, "pipeline_data.dxf");

    if (result.success) {
      // 更新导出时间
      lastExportTime.value = new Date().toLocaleString();

      // 显示成功消息和更详细的通知
      ElMessage.success(`DXF文件导出成功: ${result.fileName}`);

      // 显示富通知，包含更多信息
      ElNotification({
        title: "DXF导出完成",
        message: `文件 "${result.fileName}" 已成功下载到您的默认下载目录`,
        type: "success",
        duration: 5000,
        position: "top-right",
      });

      console.log("DXF导出成功:", result);
    } else {
      throw new Error(result.error || "DXF文件处理失败");
    }
  } catch (error: any) {
    console.error("DXF导出失败:", error);

    // 根据错误类型提供更详细的错误信息
    let errorMessage = "DXF导出失败";

    if (error?.response) {
      // HTTP错误
      const status = error.response.status;
      switch (status) {
        case 401:
          errorMessage = "身份验证失败，请重新登录";
          break;
        case 403:
          errorMessage = "权限不足，无法导出DXF文件";
          break;
        case 404:
          errorMessage = "导出接口不存在，请联系管理员";
          break;
        case 500:
          errorMessage = "服务器内部错误，请稍后重试或联系管理员";
          break;
        case 503:
          errorMessage = "服务器暂时不可用，请稍后重试";
          break;
        default:
          errorMessage = `服务器错误(${status})，请联系管理员`;
      }
    } else if (error?.message) {
      // 自定义错误或网络错误
      if (
        error.message.includes("Network Error") ||
        error.message.includes("网络")
      ) {
        errorMessage = "网络连接失败，请检查网络状态";
      } else if (
        error.message.includes("timeout") ||
        error.message.includes("超时")
      ) {
        errorMessage = "请求超时，可能是文件较大，请稍后重试";
      } else {
        errorMessage = `导出失败: ${error.message}`;
      }
    } else if (typeof error === "string") {
      errorMessage = `导出失败: ${error}`;
    }

    ElMessage.error(errorMessage);

    // 对于网络错误，提供重试建议
    if (
      error?.message?.includes("Network Error") ||
      error?.code === "NETWORK_ERROR"
    ) {
      setTimeout(() => {
        ElMessage.info("如果问题持续存在，请检查网络连接或联系管理员");
      }, 2000);
    }
  } finally {
    isExportingDxf.value = false;
    exportProgress.value = 0;
  }
};

/**
 * @function handleShpExportClick
 * @description 打开SHP导出选择弹框
 */
const handleShpExportClick = async () => {
  try {
    showShpExportPanel.value = true;
  } catch (error) {
    showShpExportPanel.value = false;
  }
};

/**
 * @function handleShpExportCancel
 * @description 取消SHP导出弹框
 */
const handleShpExportCancel = () => {
  showShpExportPanel.value = false;
};
</script>

<style scoped lang="scss">
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  // width: 550px;
  min-height: 120px;
  z-index: 1000;
}

.toolbar {
  margin-bottom: 16px;
}

.export-info {
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}
</style>
