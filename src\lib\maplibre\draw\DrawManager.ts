/**
 * @fileoverview MapLibre 绘制工具管理器
 * @description 基于 Terra Draw 的 MapLibre 绘制工具核心管理类
 * <AUTHOR>
 * @version 1.0.0
 */

import { TerraDraw } from 'terra-draw';
import { TerraDrawMapLibreGLAdapter } from 'terra-draw-maplibre-gl-adapter';
import { 
  TerraDrawSelectMode,
  TerraDrawPointMode,
  TerraDrawLineStringMode,
  TerraDrawPolygonMode,
  TerraDrawRectangleMode,
  TerraDrawCircleMode,
  TerraDrawFreehandMode
} from 'terra-draw';
import type { Map as MapLibreMap } from 'maplibre-gl';
import type { GeoJSONStoreFeatures } from 'terra-draw';

import { DrawStyleManager } from './DrawStyles';
import { DrawEventHandler } from './DrawEventHandler';
import { 
  DrawEventType,
  type DrawConfig, 
  type DrawMode, 
  type DrawState, 
  type DrawEventCallback, 
  type GeometryValidationResult,
  type DrawToolOptions
} from './types';

/**
 * @description MapLibre 绘制工具管理器
 */
export class DrawManager {
  private map: MapLibreMap;
  private terraDraw: TerraDraw | null = null;
  private styleManager: DrawStyleManager;
  private eventHandler: DrawEventHandler | null = null;
  private config: Required<DrawConfig>;
  private currentState: DrawState;

  /**
   * @constructor
   * @param map - MapLibre GL 地图实例
   * @param options - 绘制工具选项
   */
  constructor(map: MapLibreMap, options: DrawToolOptions = {}) {
    this.map = map;
    this.config = this.mergeConfig(options.config);
    this.styleManager = new DrawStyleManager(this.config.styles);
    
    // 初始化状态
    this.currentState = {
      currentMode: this.config.defaultMode,
      isDrawing: false,
      isEditing: false,
      selectedFeatureIds: [],
      featureCount: 0
    };

    // 初始化绘制工具
    this.initialize(options.onEvent);
  }

  /**
   * @description 合并配置选项
   * @param userConfig - 用户配置
   * @returns 完整配置
   * @private
   */
  private mergeConfig(userConfig?: Partial<DrawConfig>): Required<DrawConfig> {
    const defaultConfig: Required<DrawConfig> = {
      enabled: false, // 默认不启用，避免地图未加载完成时启动
      defaultMode: 'select' as DrawMode,
      showControls: false,
      controlPosition: 'top-right',
      styles: {},
      editing: {
        allowDrag: true,
        allowResize: true,
        allowRotate: false,
        allowAddPointOnLine: true,
        allowDeletePoint: true,
        minPoints: 3,
        maxPoints: 100
      }
    };

    if (!userConfig) return defaultConfig;

    return {
      ...defaultConfig,
      ...userConfig,
      styles: { ...defaultConfig.styles, ...userConfig.styles },
      editing: { ...defaultConfig.editing, ...userConfig.editing }
    };
  }

  /**
   * @description 初始化绘制工具
   * @param onEvent - 事件回调函数
   * @private
   */
  private initialize(onEvent?: DrawEventCallback): void {
    try {
      // 创建 Terra Draw 实例
      this.terraDraw = new TerraDraw({
        adapter: new TerraDrawMapLibreGLAdapter({
          map: this.map,
          coordinatePrecision: 9
        }),
        modes: this.createDrawModes()
      });
      // 创建事件处理器
      this.eventHandler = new DrawEventHandler(this.terraDraw);
      
      // 添加事件回调
      if (onEvent) {
        Object.values(DrawEventType).forEach(eventType => {
          this.eventHandler!.addEventListener(eventType, onEvent);
        });
      }

      // 注意：不再自动启动绘制工具，需要在地图加载完成后手动调用 start()
      console.log('绘制工具管理器初始化成功，等待地图加载完成后启动');
    } catch (error) {
      console.error('绘制工具管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * @description 创建绘制模式
   * @returns Terra Draw 模式数组
   * @private
   */
  private createDrawModes() {
    // 使用简化的样式配置
    const baseStyles = {
      pointColor: '#3388ff',
      pointOutlineColor: '#ffffff',
      pointOutlineWidth: 2,
      pointWidth: 12,
      lineStringColor: '#3388ff',
      lineStringWidth: 3,
      fillColor: '#3388ff',
      fillOpacity: 0.2,
      outlineColor: '#3388ff',
      outlineWidth: 2,
      // 选中状态样式 - 更醒目
      selectedPointColor: '#ff6b35',
      selectedPointOutlineColor: '#ffffff',
      selectedPointOutlineWidth: 3,
      selectedPointWidth: 16,
      selectedLineStringColor: '#ff6b35',
      selectedLineStringWidth: 4,
      selectedPolygonColor: '#ff6b35',
      selectedPolygonFillOpacity: 0.3,
      selectedPolygonOutlineColor: '#ff6b35',
      selectedPolygonOutlineWidth: 3,
      // 中点样式 - 用于线上加点
      midPointColor: '#28a745',
      midPointOutlineColor: '#ffffff',
      midPointOutlineWidth: 2,
      midPointWidth: 10
    };
    
    return [
      new TerraDrawSelectMode({
        flags: {
          polygon: {
            feature: {
              draggable: this.config.editing.allowDrag,
              rotateable: this.config.editing.allowRotate,
              scaleable: this.config.editing.allowResize,
              coordinates: {
                midpoints: this.config.editing.allowAddPointOnLine,
                draggable: this.config.editing.allowDrag,
                deletable: this.config.editing.allowDeletePoint
              }
            }
          },
          linestring: {
            feature: {
              draggable: this.config.editing.allowDrag,
              rotateable: this.config.editing.allowRotate,
              scaleable: this.config.editing.allowResize,
              coordinates: {
                midpoints: this.config.editing.allowAddPointOnLine,
                draggable: this.config.editing.allowDrag,
                deletable: this.config.editing.allowDeletePoint
              }
            }
          },
          point: {
            feature: {
              draggable: this.config.editing.allowDrag,
              rotateable: this.config.editing.allowRotate,
              scaleable: this.config.editing.allowResize
            }
          }
        },
        styles: baseStyles as any
      }),
      new TerraDrawPointMode({
        styles: baseStyles as any
      }),
      new TerraDrawLineStringMode({
        styles: baseStyles as any
      }),
      new TerraDrawPolygonMode({
        styles: baseStyles as any
      }),
      new TerraDrawRectangleMode({
        styles: baseStyles as any
      }),
      new TerraDrawCircleMode({
        styles: baseStyles as any
      }),
      new TerraDrawFreehandMode({
        styles: baseStyles as any
      })
    ];
  }

  /**
   * @description 检查地图是否已加载完成
   * @returns 地图是否已加载完成
   * @private
   */
  private isMapReady(): boolean {
    try {
      // 检查地图实例是否存在且已加载
      return !!(this.map && this.map.loaded() && this.map.isStyleLoaded());
    } catch (error) {
      console.warn('检查地图状态失败:', error);
      return false;
    }
  }

  /**
   * @description 等待地图加载完成
   * @param timeout - 超时时间(毫秒)，默认10秒
   * @returns Promise，在地图加载完成时resolve
   * @private
   */
  private waitForMapReady(timeout: number = 10000): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isMapReady()) {
        resolve();
        return;
      }

      const timeoutId = setTimeout(() => {
        reject(new Error('等待地图加载超时'));
      }, timeout);

      const checkMapReady = () => {
        if (this.isMapReady()) {
          clearTimeout(timeoutId);
          this.map.off('load', checkMapReady);
          this.map.off('styledata', checkMapReady);
          resolve();
        }
      };

      // 监听地图加载事件
      this.map.on('load', checkMapReady);
      this.map.on('styledata', checkMapReady);
      
      // 定期检查（作为备用方案）
      const intervalId = setInterval(() => {
        if (this.isMapReady()) {
          clearInterval(intervalId);
          clearTimeout(timeoutId);
          this.map.off('load', checkMapReady);
          this.map.off('styledata', checkMapReady);
          resolve();
        }
      }, 100);

      // 清理定时器
      setTimeout(() => clearInterval(intervalId), timeout);
    });
  }

  /**
   * @description 启动绘制工具
   * @param waitForMap - 是否等待地图加载完成，默认true
   */
  async start(waitForMap: boolean = true): Promise<void> {
    if (!this.terraDraw) {
      throw new Error('绘制工具未初始化');
    }

    try {
      // 如果需要等待地图加载且地图未准备好，则等待
      if (waitForMap && !this.isMapReady()) {
        console.log('地图尚未加载完成，等待地图加载...');
        await this.waitForMapReady();
        console.log('地图加载完成，启动绘制工具');
      }

      this.terraDraw.start();
      this.eventHandler?.startListening();
      this.setMode(this.config.defaultMode);
      console.log('绘制工具已启动');
    } catch (error) {
      console.error('启动绘制工具失败:', error);
      throw error;
    }
  }

  /**
   * @description 停止绘制工具
   */
  stop(): void {
    if (!this.terraDraw) {
      console.warn('绘制工具未初始化');
      return;
    }

    try {
      this.eventHandler?.stopListening();
      this.terraDraw.stop();
      console.log('绘制工具已停止');
    } catch (error) {
      console.error('停止绘制工具失败:', error);
    }
  }

  /**
   * @description 设置绘制模式
   * @param mode - 绘制模式
   */
  setMode(mode: DrawMode): void {
    if (!this.terraDraw) {
      throw new Error('绘制工具未初始化');
    }

    try {
      this.terraDraw.setMode(mode);
      this.currentState.currentMode = mode;
      this.eventHandler?.handleModeChange(mode);
      
      // === 新增：当切换到绘制模式时，确保绘制图层在顶层 ===
      if (mode !== 'select') {
        console.log(`切换到绘制模式 ${mode}，调整绘制图层顺序到顶层`);
        this.ensureDrawLayersOnTop();
      }
      
      console.log(`切换到绘制模式: ${mode}`);
    } catch (error) {
      console.error(`切换绘制模式失败 (${mode}):`, error);
      throw error;
    }
  }

  /**
   * @description 确保绘制图层在最顶层
   * @description 解决绘制过程中线条被基础图层覆盖的问题
   * @private
   */
  private ensureDrawLayersOnTop(): void {
    try {
      // 获取所有绘制相关的图层ID
      const drawLayerIds = this.getDrawLayerIds();
      
      if (drawLayerIds.length === 0) {
        console.log('未发现绘制图层，等待图层创建...');
        // 如果当前没有绘制图层，延迟检查（Terra Draw可能还未创建图层）
        setTimeout(() => {
          this.ensureDrawLayersOnTop();
        }, 100);
        return;
      }
      
      console.log(`发现 ${drawLayerIds.length} 个绘制图层，移动到顶层:`, drawLayerIds);
      
      // 将每个绘制图层移动到最顶层
      drawLayerIds.forEach(layerId => {
        try {
          this.map.moveLayer(layerId); // 移动到最顶层
          console.log(`✅ 绘制图层 ${layerId} 已移动到顶层`);
        } catch (error) {
          console.warn(`⚠️ 移动绘制图层 ${layerId} 失败，可能图层尚未创建:`, error);
        }
      });
      
      console.log('绘制图层顺序调整完成');
      
    } catch (error) {
      console.error('调整绘制图层顺序失败:', error);
    }
  }

  /**
   * @description 获取所有Terra Draw创建的图层ID
   * @description Terra Draw在MapLibre中创建的图层通常以特定前缀命名
   * @returns 绘制图层ID数组
   * @private
   */
  private getDrawLayerIds(): string[] {
    try {
      const allLayers = this.map.getStyle().layers || [];
      
      // Terra Draw 在 MapLibre 中创建的图层通常包含以下特征：
      // 1. 包含 'terra-draw' 前缀的图层
      // 2. 或者是绘制工具相关的临时图层
      const drawLayerIds = allLayers
        .filter(layer => {
          // 检查图层ID是否包含Terra Draw相关标识
          const idMatches = layer.id.includes('terra-draw') || 
                           layer.id.includes('draw-') ||
                           layer.id.includes('mode-') ||
                           // Terra Draw MapLibre适配器可能使用的命名模式
                           layer.id.startsWith('td-') ||
                           layer.id.startsWith('Terra');
          
          // 检查数据源是否来自Terra Draw（需要类型检查）
          const sourceMatches = 'source' in layer && 
                                typeof layer.source === 'string' && 
                                (layer.source.includes('terra-draw') || 
                                 layer.source.includes('draw-'));
          
          return idMatches || sourceMatches;
        })
        .map(layer => layer.id);
      
      return drawLayerIds;
    } catch (error) {
      console.error('获取绘制图层ID失败:', error);
      return [];
    }
  }

  /**
   * @description 获取当前绘制模式
   * @returns 当前绘制模式
   */
  getCurrentMode(): DrawMode {
    return this.currentState.currentMode;
  }

  /**
   * @description 获取所有绘制的要素
   * @returns GeoJSON 要素集合
   */
  getAllFeatures(): any[] {
    if (!this.terraDraw) {
      throw new Error('绘制工具未初始化');
    }

    try {
      const features = this.terraDraw.getSnapshot();
      this.currentState.featureCount = features.length;
      return features;
    } catch (error) {
      console.error('获取绘制要素失败:', error);
      throw error;
    }
  }

  /**
   * @description 根据ID获取要素
   * @param featureId - 要素ID
   * @returns GeoJSON 要素或null
   */
  getFeatureById(featureId: string): any {
    if (!this.terraDraw) {
      throw new Error('绘制工具未初始化');
    }

    try {
      const features = this.terraDraw.getSnapshot();
      return features.find(feature => feature.id === featureId) || null;
    } catch (error) {
      console.error(`获取要素失败 (ID: ${featureId}):`, error);
      return null;
    }
  }

  /**
   * @description 删除要素
   * @param featureIds - 要素ID数组
   */
  deleteFeatures(featureIds: string[]): void {
    if (!this.terraDraw) {
      throw new Error('绘制工具未初始化');
    }

    try {
      featureIds.forEach(id => {
        this.terraDraw!.removeFeatures([id]);
      });
      
      // 更新状态
      this.currentState.selectedFeatureIds = this.currentState.selectedFeatureIds
        .filter(id => !featureIds.includes(id));
      
      console.log(`删除要素: ${featureIds.join(', ')}`);
    } catch (error) {
      console.error('删除要素失败:', error);
      throw error;
    }
  }

  /**
   * @description 清除所有要素
   */
  clearAllFeatures(): void {
    if (!this.terraDraw) {
      throw new Error('绘制工具未初始化');
    }

    // 检查Terra Draw是否已启用
    if (!this.isEnabled()) {
      console.warn('Terra Draw未启用，尝试重新启动...');
      try {
        this.start();
        console.log('Terra Draw重新启动成功');
      } catch (error) {
        console.error('重新启动Terra Draw失败:', error);
        throw new Error('绘制工具状态异常，无法清除要素');
      }
    }

    try {
      // 保存当前模式，以便清除后恢复
      const currentMode = this.currentState.currentMode;
      
      // 清除所有要素
      this.terraDraw.clear();
      
      // 重新启动Terra Draw以确保状态正确重置
      // 这是为了解决Terra Draw在clear()后可能出现的状态不一致问题
      this.terraDraw.stop();
      this.terraDraw.start();
      
      // 恢复之前的绘制模式
      if (currentMode && currentMode !== 'select') {
        // 延迟一点时间确保Terra Draw完全重新启动
        setTimeout(() => {
          if (this.terraDraw && this.isEnabled()) {
            try {
              this.terraDraw.setMode(currentMode);
              console.log(`清除后恢复绘制模式: ${currentMode}`);
            } catch (error) {
              console.warn(`恢复绘制模式失败，切换到选择模式: ${error}`);
              if (this.terraDraw) {
                this.terraDraw.setMode('select');
                this.currentState.currentMode = 'select' as DrawMode;
              }
            }
          }
        }, 50);
      } else {
        // 确保设置为选择模式
        this.terraDraw.setMode('select');
        this.currentState.currentMode = 'select' as DrawMode;
      }
      
      // 重置状态
      this.currentState.selectedFeatureIds = [];
      this.currentState.featureCount = 0;
      
      console.log('清除所有绘制要素并重置状态');
    } catch (error) {
      console.error('清除要素失败:', error);
      
      // 如果清除失败，尝试重新初始化整个绘制工具
      try {
        console.log('尝试重新初始化绘制工具...');
        this.stop();
        this.terraDraw = new TerraDraw({
          adapter: new TerraDrawMapLibreGLAdapter({
            map: this.map,
            coordinatePrecision: 9
          }),
          modes: this.createDrawModes()
        });
        this.start();
        this.setMode('select' as DrawMode);
        this.currentState.selectedFeatureIds = [];
        this.currentState.featureCount = 0;
        console.log('绘制工具重新初始化成功');
      } catch (reinitError) {
        console.error('重新初始化绘制工具失败:', reinitError);
        throw error;
      }
    }
  }

  /**
   * @description 添加要素到地图
   * @param features - GeoJSON 要素数组
   */
  addFeatures(features: any[]): void {
    if (!this.terraDraw) {
      throw new Error('绘制工具未初始化');
    }

    try {
      // 确保features是数组格式，并逐个添加
      const featureArray = Array.isArray(features) ? features : [features];
      featureArray.forEach(feature => {
        // 使用单个要素调用addFeatures
        this.terraDraw!.addFeatures([feature] as any);
      });
      this.currentState.featureCount += featureArray.length;
      console.log(`添加 ${featureArray.length} 个要素`);
    } catch (error) {
      console.error('添加要素失败:', error);
      throw error;
    }
  }

  /**
   * @description 验证几何体
   * @param geometry - GeoJSON 几何体
   * @returns 验证结果
   */
  validateGeometry(geometry: any): GeometryValidationResult {
    const result: GeometryValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // 基本几何体类型检查
      if (!geometry || !geometry.type) {
        result.isValid = false;
        result.errors!.push('几何体类型缺失');
        return result;
      }

      // 坐标检查
      if (!geometry.coordinates) {
        result.isValid = false;
        result.errors!.push('几何体坐标缺失');
        return result;
      }

      // 根据几何体类型进行具体验证
      switch (geometry.type) {
        case 'Point':
          if (!Array.isArray(geometry.coordinates) || geometry.coordinates.length !== 2) {
            result.isValid = false;
            result.errors!.push('点几何体坐标格式错误');
          }
          break;
        
        case 'LineString':
          if (!Array.isArray(geometry.coordinates) || geometry.coordinates.length < 2) {
            result.isValid = false;
            result.errors!.push('线几何体至少需要2个点');
          }
          break;
        
        case 'Polygon':
          if (!Array.isArray(geometry.coordinates) || geometry.coordinates.length === 0) {
            result.isValid = false;
            result.errors!.push('多边形几何体坐标格式错误');
          } else {
            const ring = geometry.coordinates[0];
            if (!Array.isArray(ring) || ring.length < 4) {
              result.isValid = false;
              result.errors!.push('多边形至少需要4个点（首尾相同）');
            }
          }
          break;
      }

      return result;
    } catch (error) {
      result.isValid = false;
      result.errors!.push(`几何体验证失败: ${(error as Error).message}`);
      return result;
    }
  }

  /**
   * @description 获取当前状态
   * @returns 绘制工具状态
   */
  getState(): DrawState {
    return { ...this.currentState };
  }

  /**
   * @description 更新样式配置
   * @param newStyles - 新的样式配置
   */
  updateStyles(newStyles: any): void {
    try {
      this.styleManager.updateStyles(newStyles);
      
      // 重新创建模式以应用新样式
      if (this.terraDraw) {
        const currentMode = this.currentState.currentMode;
        this.stop();
        this.terraDraw = new TerraDraw({
          adapter: new TerraDrawMapLibreGLAdapter({
            map: this.map,
            coordinatePrecision: 9
          }),
          modes: this.createDrawModes()
        });
        this.start();
        this.setMode(currentMode);
      }
      
      console.log('样式配置已更新');
    } catch (error) {
      console.error('更新样式失败:', error);
      throw error;
    }
  }

  /**
   * @description 添加事件监听器
   * @param eventType - 事件类型
   * @param callback - 回调函数
   */
  addEventListener(eventType: DrawEventType, callback: DrawEventCallback): void {
    this.eventHandler?.addEventListener(eventType, callback);
  }

  /**
   * @description 移除事件监听器
   * @param eventType - 事件类型
   * @param callback - 回调函数
   */
  removeEventListener(eventType: DrawEventType, callback: DrawEventCallback): void {
    this.eventHandler?.removeEventListener(eventType, callback);
  }

  /**
   * @description 销毁绘制工具
   */
  destroy(): void {
    try {
      this.stop();
      this.eventHandler?.destroy();
      this.terraDraw = null;
      this.eventHandler = null;
      console.log('绘制工具管理器已销毁');
    } catch (error) {
      console.error('销毁绘制工具失败:', error);
    }
  }

  /**
   * @description 检查绘制工具是否已启用
   * @returns 绘制工具是否处于启用状态
   */
  isEnabled(): boolean {
    try {
      // 检查Terra Draw实例是否存在且已启用
      return this.terraDraw !== null && this.terraDraw.enabled === true;
    } catch (error) {
      console.warn('检查绘制工具状态失败:', error);
      return false;
    }
  }

  /**
   * @description 选中指定要素（显示编辑锚点）
   * @param featureId - 要素ID
   * @returns 是否选中成功
   */
  selectFeature(featureId: string): boolean {
    if (!this.terraDraw) {
      throw new Error('绘制工具未初始化');
    }

    if (!this.isEnabled()) {
      console.warn('绘制工具未启用，无法选中要素');
      return false;
    }

    try {
      // 确保当前为选择模式
      if (this.currentState.currentMode !== 'select') {
        this.setMode('select' as DrawMode);
      }

      // 尝试多种方式选中要素
      let success = false;

      // 方式1：使用Terra Draw的setSelected方法
      try {
        if (typeof (this.terraDraw as any).setSelected === 'function') {
          (this.terraDraw as any).setSelected([featureId]);
          success = true;
          console.log('通过setSelected选中要素:', featureId);
        }
      } catch (error) {
        console.warn('setSelected方法失败:', error);
      }

      // 方式2：使用select方法
      if (!success) {
        try {
          if (typeof (this.terraDraw as any).select === 'function') {
            (this.terraDraw as any).select(featureId);
            success = true;
            console.log('通过select选中要素:', featureId);
          }
        } catch (error) {
          console.warn('select方法失败:', error);
        }
      }

      // 方式3：通过模式的select方法
      if (!success) {
        try {
          const currentMode = (this.terraDraw as any).getMode();
          if (currentMode && typeof currentMode.select === 'function') {
            currentMode.select(featureId);
            success = true;
            console.log('通过mode.select选中要素:', featureId);
          }
        } catch (error) {
          console.warn('mode.select方法失败:', error);
        }
      }

      if (success) {
        // 更新状态
        this.currentState.selectedFeatureIds = [featureId];
        this.currentState.isEditing = true;
        console.log('要素选中成功，应显示编辑锚点:', featureId);
      } else {
        console.warn('所有选中方法都失败，要素可能无法显示编辑锚点');
      }

      return success;
    } catch (error) {
      console.error('选中要素失败:', error);
      return false;
    }
  }

  /**
   * @description 检测并解决绘制工具状态冲突
   * @param source - 冲突来源标识
   * @returns 是否成功解决冲突
   */
  resolveStateConflict(source: string = 'unknown'): boolean {
    try {
      console.log(`检测到绘制工具状态冲突，来源: ${source}`);

      if (!this.terraDraw) {
        console.log('Terra Draw实例不存在，无需处理冲突');
        return true;
      }

      // 检查当前状态
      const isCurrentlyEnabled = this.isEnabled();
      console.log(`当前Terra Draw状态: ${isCurrentlyEnabled ? '启用' : '禁用'}`);

      if (!isCurrentlyEnabled) {
        console.log('尝试重新启动Terra Draw...');
        try {
          this.start();
          if (this.isEnabled()) {
            console.log('Terra Draw重新启动成功');
            return true;
          } else {
            console.warn('Terra Draw重新启动后仍未启用');
          }
        } catch (startError) {
          console.error('重新启动Terra Draw失败:', startError);
        }

        // 如果重启失败，尝试重新创建实例
        console.log('尝试重新创建Terra Draw实例...');
        try {
          this.destroy();
          this.reinitialize();
          if (this.isEnabled()) {
            console.log('Terra Draw重新创建成功');
            return true;
          }
        } catch (recreateError) {
          console.error('重新创建Terra Draw失败:', recreateError);
          return false;
        }
      }

      // 状态正常，清除可能的冲突要素
      try {
        const features = this.getAllFeatures();
        if (features.length > 0) {
          console.log(`发现 ${features.length} 个要素，检查是否需要清理`);
          // 这里可以添加更细致的冲突检测逻辑
        }
      } catch (error) {
        console.warn('检查要素时出错:', error);
      }

      return true;
    } catch (error) {
      console.error('解决状态冲突失败:', error);
      return false;
    }
  }

  /**
   * @description 强制重新初始化绘制工具
   * @param preserveFeatures - 是否保留现有要素
   * @returns 是否成功
   */
  forceReinitialize(preserveFeatures: boolean = false): boolean {
    try {
      console.log('开始强制重新初始化绘制工具...');

      let preservedFeatures: any[] = [];
      
      // 保存现有要素（如果需要）
      if (preserveFeatures && this.terraDraw) {
        try {
          preservedFeatures = this.getAllFeatures();
          console.log(`保存了 ${preservedFeatures.length} 个要素`);
        } catch (error) {
          console.warn('保存要素失败:', error);
        }
      }

      // 销毁现有实例
      this.destroy();

      // 重新创建实例
      this.reinitialize();

      // 恢复要素（如果需要）
      if (preserveFeatures && preservedFeatures.length > 0) {
        try {
          this.addFeatures(preservedFeatures);
          console.log(`恢复了 ${preservedFeatures.length} 个要素`);
        } catch (error) {
          console.warn('恢复要素失败:', error);
        }
      }

      const success = this.isEnabled();
      console.log(`强制重新初始化${success ? '成功' : '失败'}`);
      return success;
    } catch (error) {
      console.error('强制重新初始化失败:', error);
      return false;
    }
  }

  /**
   * @description 重新初始化方法（私有）
   * @private
   */
  private reinitialize(): void {
    try {
      // 创建 Terra Draw 实例
      this.terraDraw = new TerraDraw({
        adapter: new TerraDrawMapLibreGLAdapter({
          map: this.map,
          coordinatePrecision: 9
        }),
        modes: this.createDrawModes()
      });
      
      // 创建事件处理器
      this.eventHandler = new DrawEventHandler(this.terraDraw);

      // 启动绘制工具
      if (this.config.enabled) {
        this.start();
      }

      console.log('绘制工具重新初始化成功');
    } catch (error) {
      console.error('重新初始化绘制工具失败:', error);
      throw error;
    }
  }
} 