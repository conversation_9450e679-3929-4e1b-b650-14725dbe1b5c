<template>
  <page-card class="classifed-sta" title="分类统计" :close-icon="false">
    <el-row>
      <el-text>管网分类统计：</el-text>
      <div class="buttons-row">
        <query-button v-for="button in pipeStatisticsTypes" :key="button.value" type="info"
          :loading="currType === button.value" :disabled="isLoading && currType !== button.value"
          :active="isActive(button.value).value"
          @click="handlePipeStatistics(button.value)">
          {{ button.label }}
        </query-button>
      </div>
    </el-row>
    <el-row class="mt-16px">
      <el-text>设备分类统计：</el-text>
      <div class="buttons-row">
        <query-button v-for="button in deviceStatisticsTypes" :key="button.value" type="info"
          :loading="currType === button.value" :disabled="isLoading && currType !== button.value"
          :active="isActive(button.value).value"
          @click="handleDeviceStatistics(button.value)">
          {{ button.label }}
        </query-button>
      </div>
    </el-row>
    <div class="query-result mt-16px" v-show="showResult">
      <el-text>统计结果</el-text>
      <StatisticsChart :data="currentData" :option-generator="currentOptionGenerator" />
    </div>
    <div class="query-result mt-16px" v-show="isRoadSta">
      <el-text>统计结果</el-text>
      <el-form-item class="font-size-14px mt-8px" label="区域选择：">
        <el-select
          v-model="selectedRegion"
          placeholder="请选择区域"
          @change="handleRegionChange"
        >
          <el-option
            v-for="region in regionOptions"
            :key="region"
            :label="region"
            :value="region"
          />
        </el-select>
      </el-form-item>
       <StatisticsChart
          :data="lineChartData"
          :option-generator="lineChartOptionGenerator"
          :height="300"
        />
    </div>
  </page-card>
</template>

<script setup lang="ts">
import { getLengthByPipeDiameter, getLengthByPath, getLengthByPipeMaterial,queryAccessoryCountByRoad } from '@/api/query';
import { initTabulateEchart,generateLineChartOption } from '@/lib/echarts';
import { fmStat } from '@/api/home';
import {
  spatialQuery, calculateDeviceTypeStatistics
} from '@/utils/deviceUtils';
import { type LineChartData } from '@/lib/echarts/lineChart';

/**
 * 管网统计类型枚举
 */
const pipeStatisticsTypes = [
  { label: '按阀门统计', value: 'valve' },
  { label: '按管材统计', value: 'material' },
  { label: '按管径统计', value: 'diameter' },
  { label: '按道路统计', value: 'pipeRoad' }
] as const;

/**
 * 设备统计类型枚举
 */
const deviceStatisticsTypes = [
  { label: '按道路统计', value: 'deviceRoad' },
  { label: '按设备类型统计', value: 'type' }
] as const;

// 统计类型联合类型
type StatisticsType = typeof pipeStatisticsTypes[number]['value'] | typeof deviceStatisticsTypes[number]['value'];

// 使用 useActiveButton 管理选中状态
const { setActive, isActive, clearActive } = useActiveButton<StatisticsType>();

const currType = ref('')
// 添加 isLoading 状态变量
const isLoading = ref(false)

// 管网数据
const pipeData = ref<ISeriesData[] | null>([]);

// 设备数据
const deviceData = ref<ISeriesData[] | null>([]);

// 当前数据类型（用于区分显示管网还是设备数据）
const currentDataType = ref<'pipe' | 'device'>('pipe');

// 当前单位
const currentUnit = ref<string>('米');

const showResult = computed(() => {
  const data = currentDataType.value === 'pipe' ? pipeData.value : deviceData.value;
  return Array.isArray(data) && data.length > 0;
});

// 当前显示的数据
const currentData = computed(() => {
  return currentDataType.value === 'pipe' ? pipeData.value : deviceData.value;
});

// 带单位的图表配置生成器
const currentOptionGenerator = computed(() => {
  return (data: ISeriesData[]) => initTabulateEchart(data, currentUnit.value);
});

// 折线图相关数据
const selectedRegion = ref<string>('');
const lineChartData = ref<LineChartData[]>([]);
const regionData = ref<Record<string, any>>({});

// 区域选项（从 regionData 的 key 生成）
const regionOptions = computed(() => {
  return Object.keys(regionData.value)
});

// 折线图配置生成器
const lineChartOptionGenerator = (data: LineChartData[]) => {
  return generateLineChartOption(data, `${selectedRegion.value} - 设备统计`);
};

/**
 * 处理区域选择变化
 * @param regionKey 选中的区域key
 */
const handleRegionChange = (regionKey: string) => {
  if (!regionKey) {
    lineChartData.value = [];
    return;
  }

  // 将选中区域的数据转换为折线图数据格式
  const selectedRegionData = regionData.value[regionKey];
  if (!selectedRegionData) {
    lineChartData.value = [];
    return;
  }

  lineChartData.value = Object.entries(selectedRegionData).map(([name, value]) => ({
    name,
    value: Number(value) || 0
  }));

  console.log(`切换到区域: ${regionKey}`, lineChartData.value);
};


/**
 * @description 处理管网统计点击事件
 * @param type 统计类型
 */
const handlePipeStatistics = async (type: typeof pipeStatisticsTypes[number]['value']) => {
  try {
    currType.value = type;
    setActive(type); // 设置选中状态
    isLoading.value = true;
    currentDataType.value = 'pipe'; // 设置为管网数据类型
    isRoadSta.value = false

    // 清空设备数据，避免显示冲突
    deviceData.value = [];
    regionData.value = {};
    selectedRegion.value = '';
    lineChartData.value = [];

    let result;

    // 根据类型调用不同的API
    switch (type) {
      case 'diameter':
        currentUnit.value = '米'; // 管径统计使用米
        result = await getLengthByPipeDiameter({ range: '' });
        break;
      case 'pipeRoad':
        currentUnit.value = '米'; // 管道路径统计使用米
        result = await getLengthByPath();
        break;
      case 'material':
        currentUnit.value = '米'; // 管材统计使用米
        result = await getLengthByPipeMaterial();
        break;
      case 'valve':
        currentUnit.value = '个'; // 阀门统计使用个
        result = await fmStat()
        
        break;
      default:
        ElMessage.warning('未知的统计类型');
        return;
    }

    // 统一处理API返回结果
    if (result && result.code === 200) {
      // 对管径数据特殊处理（移除小数点后的0）
      const shouldCleanupKeys = type === 'diameter';

      // 转换数据格式
      const arr = Object.entries(result.data).map(([k, v]): ISeriesData => ({
        name: shouldCleanupKeys ? k.replace(/\.0+$/, '') : k,
        value: v as number
      }));

      // 更新图表数据
      pipeData.value = arr;
    } else {
      ElMessage.error('获取统计数据失败');
      pipeData.value = [];
    }

  } catch (error) {
    console.error('统计请求失败:', error);
    ElMessage.error('获取统计数据失败，请稍后重试');
    pipeData.value = [];
  } finally {
    currType.value = '';
    isLoading.value = false // 设置加载状态为 false
  }
};

const isRoadSta= ref(false)

/**
 * @description 处理设备统计点击事件
 * @param type 统计类型
 */
const handleDeviceStatistics = async (type: typeof deviceStatisticsTypes[number]['value']) => {
  try {
    currType.value = type;
    setActive(type); // 设置选中状态
    isLoading.value = true;
    currentDataType.value = 'device';
    isRoadSta.value = false

    // 清空管网数据，避免显示冲突
    pipeData.value = [];
    deviceData.value = [];
    // 根据类型调用不同的统计逻辑
    switch (type) {
      case 'type':
        currentUnit.value = '台'; // 设备类型统计使用台
        // 按设备类型统计
        const data = await spatialQuery({ coordinate: '', fieldGroup: 'second_device_type_code' });

        if (data && Object.keys(data).length > 0) {
          const deviceStats = calculateDeviceTypeStatistics(data);

          // 按数量降序排序
          deviceStats.sort((a, b) => b.value - a.value);
          deviceData.value = deviceStats;
        } else {
          deviceData.value = [];
        }

        if (!deviceData.value || deviceData.value.length === 0) {
          ElMessage.warning('暂无设备数据');
        }
        break;
      case 'deviceRoad':
        isRoadSta.value = true
        currentUnit.value = '台'; // 按道路统计设备使用台
        // 按道路统计设备
        const result = await queryAccessoryCountByRoad()
        if (result.data && Object.keys(result.data).length > 0) {
          // 将 result.data 赋值给 regionData 以供折线图使用
          regionData.value = { ...result.data };

          // 默认选中第一个区域
          const firstRegion = Object.keys(result.data)[0];
          selectedRegion.value = firstRegion;

          // 自动加载第一个区域的数据
          handleRegionChange(firstRegion);

        } else {
          regionData.value = {};
          selectedRegion.value = '';
          lineChartData.value = [];
        }
        if (!regionData.value || Object.keys(regionData.value).length === 0) {
          ElMessage.warning('暂无设备数据');
        }
        break; // 改为 break，不要 return
      default:
        ElMessage.warning('未知的统计类型');
        return;
    }
  } catch (error) {
    console.error('设备统计请求失败:', error);
    ElMessage.error('获取设备统计数据失败，请稍后重试');
    deviceData.value = [];
  } finally {
    currType.value = '';
    isLoading.value = false;
    
  }
};

// 页面切换时自动清除选中状态
onBeforeUnmount(() => {
  clearActive();
});


</script>

<style lang="scss" scoped>
.classifed-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 694px;
}

.line-chart-section {
  .line-chart-container {
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    padding: 16px;
    background-color: #fafafa;
  }

  .el-select {
    .el-input__wrapper {
      border-radius: 6px;
    }
  }
}
</style>