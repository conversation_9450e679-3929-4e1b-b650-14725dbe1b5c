export const initFireEchart = (m2R2Data: any) => {
  // var m2R2Data = [
  //   {
  //     value: 335,
  //     legendname: "种类01",
  //     name: "种类01  335",
  //     itemStyle: { color: "#8d7fec" },
  //   },
  //   {
  //     value: 310,
  //     legendname: "种类02",
  //     name: "种类02  310",
  //     itemStyle: { color: "#5085f2" },
  //   },
  //   {
  //     value: 234,
  //     legendname: "种类03",
  //     name: "种类03  234",
  //     itemStyle: { color: "#e75fc3" },
  //   },
  //   {
  //     value: 154,
  //     legendname: "种类04",
  //     name: "种类04  154",
  //     itemStyle: { color: "#f87be2" },
  //   },
  // ];
  let total = 0
  m2R2Data.forEach((e: any) => {
     total += parseInt(e.value)
  })
  // let objData = array2obj(m2R2Data, "name");
  const option: any = {
    color: ['#1966FF', '#18BDB1'],
    title: [

      {
        text: total + "个",
        subtext: '消防井总数',
        textStyle: {
          fontSize: 20,
          color: "#1966FF",
        },
        subtextStyle: {
          fontSize: 14,
          color: "black",
        },
        textAlign: "center",
        x: "48.5%",
        y: "34%",
      },
    ],
    tooltip: {
      trigger: "item",
    },
    legend: {
      type: "scroll",
      orient: "horizontal",
      itemGap: 60,
      bottom: 20,
      formatter: function (name: any) {
        for (let i = 0; i < m2R2Data.length; i++) {
          if (name == m2R2Data[i].name) {
            return `${name}：${m2R2Data[i].value}个`
          }
        }
      },
    },
    series: [
      {
        name: "",
        type: "pie",
        center: ["50%", "45%"],
        radius: ["54%", "70%"],
        clockwise: false, //饼图的扇区是否是顺时针排布
        avoidLabelOverlap: false,
        label: {
          normal: {
            show: false,
            position: "outter",
            formatter: function (parms: any) {
              return parms.data.legendname;
            },
          },
        },
        labelLine: {
          normal: {
            length: 5,
            length2: 3,
            smooth: true,
          },
        },
        data: m2R2Data,
      },
    ],
  };
  return option
}