<template>
  <custom-card
    @closeHandler="close"
    :width="'658px'"
    :top="'90px'"
    :right="'120px'"
    :title="mainItem.title"
  >
    <el-form
      ref="form"
      :model="formData"
      class="admin-sub-form"
      label-width="auto"
      :disabled="true"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名称：">
            <el-input
              class=""
              v-model.trim="formData.deviceName"
              placeholder="请输入设备名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预警状态：">
            <el-input
              class=""
              v-model.trim="formData.status"
              placeholder="请输入预警状态"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="创建时间：">
            <el-input
              class=""
              v-model.trim="formData.createTime"
              placeholder="请输入创建时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="修改时间：">
            <el-input
              class=""
              v-model.trim="formData.updateTime"
              placeholder="请输入修改时间"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预警时间：">
            <el-input
              class=""
              v-model.trim="formData.alarmTime"
              placeholder="请输入预警时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预警级别：">
            <el-input
              class=""
              v-model.trim="formData.levelName"
              placeholder="请输入预警级别"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预警内容：">
            <el-input
              type="textarea"
              v-model.trim="formData.content"
              placeholder="请输入预警内容"
              resize="none"
              :rows="3"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="站点名称：">
            <el-input
              class=""
              v-model.trim="formData.siteName"
              placeholder="请输入站点名称"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-card>
</template>
<script lang="ts" setup>
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});
interface FormData {
  status: string | number;
  deviceName: string;
  alarmTime: string;
  levelName: string;
  content: string;
  createTime: string;
  updateTime: string;
  siteName: string;
}
const initFormData = () => {
  return {
    status: "",
    deviceName: "",
    alarmTime: "",
    levelName: "",
    content: "",
    createTime: "",
    updateTime: "",
    siteName: "",
  };
};
const formData = ref<FormData>(initFormData());
const dataInfo = ref(JSON.parse(props.mainItem.params.properties));
formData.value = dataInfo.value[0];
formData.value.status =
  dataInfo.value[0].status === 0
    ? "待处理"
    : dataInfo.value[0].status === 1
    ? "处理中"
    : "已处理";
// console.log(JSON.parse(props.mainItem.params.properties[0]));
const close = () => {
  useDialogStore().closeDialog("AlarmDetail");
};
</script>
<style lang="scss" scoped></style>
