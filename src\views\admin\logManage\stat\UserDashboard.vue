<template>
  <div>
    <div class="mb-5">用户看板</div>
    <el-form :model="queryForm">
      <el-row :gutter="20">
        <el-col :span="9">
          <el-form-item label="姓名：">
            <el-input v-model="queryForm.name" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="系统：">
            <el-select
             class="w-full admin-select"
                popper-class="admin-popper-select"
              v-model="queryForm.sys"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in system"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button
              class="admin-query-btn"
              type="primary"
              @click="queryData"
              :icon="Search"
            >
            </el-button>
            <el-button class="admin-reset-btn" :icon="Refresh" @click="reset">
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="list-ht">
      <el-scrollbar>
        <div class="mt-2.5">
          <div
            class="flex font-regular"
            v-for="(item, index) in list"
            :key="index"
          >
            <div class="step">
              <div class="w-3 h-3 rounded-50% bg-#0080FF"></div>
              <div
                class="line"
                :class="list.length - 1 === index ? 'h-0!' : ''"
              ></div>
            </div>

            <div>
              <div class="mb-2 font-size-3.5">
                <div class="color-#5C5F66">{{ item.operTime }}</div>
                <div class="flex items-center mt-2.5">
                  <img
                    v-if="item.photo"
                    :src="item.photo"
                    alt=""
                    class="w-8 h-8 rounded-50%"
                  />
                  <div
                    v-else
                    class="w-8 h-8 flex-c font-size-4 rounded-50% bg-#7C40AA color-#fff"
                  >
                    {{ item.operName[0] }}
                  </div>
                  <div class="color-#232A3A ml-2.5">
                    {{ item.operName }}访问{{
                      useConstValue(system, item.title)
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useConstValue } from "@/utils/utils";
import { userDashboard } from "@/api/log";
import { system } from "@/utils/constant";
import { Search, Refresh, Plus } from "@element-plus/icons-vue";
interface QueryForm {
  name: string;
  sys: string;
  pageSize: number;
  pageNum: number;
}
const initQueryForm = () => {
  return {
    name: "",
    sys: "",
    pageNum: 1,
    pageSize: 20,
  };
};
const queryForm = ref<QueryForm>(initQueryForm());
const list = ref<any>([]);
const getList = async () => {
  const result = await userDashboard(queryForm.value);
  list.value = result.data;
  console.log(list.value);
};
const queryData = () => {
  getList();
};
const reset = () => {
  queryForm.value = initQueryForm();
  getList();
};
onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.step {
  margin-right: 20px;
  .line {
    width: 2px;
    height: 82px;
    background: #f1f3f8;
    margin-left: 5px;
  }
}
.list-ht {
  height: calc(100vh - 260px);
}
</style>
