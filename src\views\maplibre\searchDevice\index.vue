<template>
  <custom-card
    :width="'450px'"
    @closeHandler="close"
    v-show="mainItem.show"
    :main-item="mainItem"
    :title="'设备搜索'"
  >
    <el-row :gutter="20">
      <el-col :span="10">
        <el-select
          v-model="queryForm.typeValue"
          filterable
          placeholder="请选择"
          @change="typeChange"
        >
          <el-option
            v-for="item in deviceType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="9">
        <el-input
          v-model="queryForm.name"
          placeholder="请输入设备名称"
          clearable
        />
      </el-col>
      <el-col :span="5">
        <el-button type="primary" class="btn" @click="search" :loading="loading"
          >查询</el-button
        >
      </el-col>
    </el-row>
    <div
      class="bg-#F8F9FA box-border p-4 mt-5 font-size-3.5"
      v-if="isShow"
      v-loading="loading"
    >
      <div class="color-#5C5F66">相关设备：</div>
      <div
        v-for="(item, index) in paginatedList"
        :key="index"
        class="flex items-center justify-between mt-2"
      >
        <div class="color-#2C3037">{{ item.name }}</div>
        <div class="color-#1966ff cursor-pointer" @click="locParcel(item)">
          详情
        </div>
      </div>
    </div>
    <div v-if="showResult" class="result-section">
      <div>
        <el-text class="result-label">查询结果：</el-text>
        <span class="error"> 暂无数据 </span>
      </div>
    </div>
    <div class="flex justify-between items-center mt-5">
      <div>
        <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
      </div>
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :total="total"
        :pager-count="3"
        layout="prev, pager, next"
        class="pagination"
        background
        small
      ></el-pagination>
    </div>
  </custom-card>
</template>
<script lang="ts" setup>
import type { Map as MapLibreMap } from "maplibre-gl";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { fetchDeviceSubTypes, fetchDeviceSummary } from "@/utils/deviceUtils";
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});
interface QueryForm {
  typeValue: string;
  name: string;
  pageNum: number;
  pageSize: number;
}
const initQueryForm = () => {
  return {
    typeValue: "",
    name: "",
    pageNum: 1,
    pageSize: 20,
  };
};
const queryForm = ref<QueryForm>(initQueryForm());
const typeValue = ref("");
const isShow = ref(false);
const showResult = ref(false);
const deviceType = ref<any>([]);
const list = ref<any>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const pageSizes = [10, 20, 50, 100];
const allDevices = ref<any[]>([]);
const filteredDevices = ref<any[]>([]);
const paginatedList = computed(() => {
  // const start = (currentPage.value - 1) * pageSize.value;
  // const end = start + pageSize.value;
  // return allDevices.value.slice(start, end);
  // 应用筛选逻辑
  const filtered = allDevices.value.filter((device) => {
    // const typeMatch =
    //   !queryForm.value.typeValue ||
    //   device.typeCode === queryForm.value.typeValue;
    const nameMatch =
      !queryForm.value.name || device.name.includes(queryForm.value.name);
    return nameMatch;
  });

  filteredDevices.value = filtered; // 存储筛选结果
  total.value = filtered.length; // 更新总数

  // 返回当前页数据
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filtered.slice(start, end);
});
let map: MapLibreMap | null = null;
/**
 * 分页页码改变
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

/**
 * 分页大小改变
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1; // 重置到第一页
};
const locParcel = (item: any) => {
  if (!map) {
    ElMessage.error("地图实例不可用");
    return;
  }
  map.flyTo({
    center: [item.longitude, item.latitude],
    zoom: 18,
  });
};
const search = async () => {
  try {
    loading.value = true;
    const result = await await fetchDeviceSummary({
      size: -1,
      current: 1,
      secondDeviceTypeCode: queryForm.value.typeValue,
    });
    allDevices.value = result;
    currentPage.value = 1;
    total.value = result.length;
    if (result.length === 0) {
      showResult.value = true;
      isShow.value = false;
    } else {
      showResult.value = false;
      isShow.value = true;
      list.value = result;
    }
  } finally {
    loading.value = false;
  }
};
const close = () => {
  useDialogStore().closeDialog("DeviceResearch");
};
const getDeviceTypes = async () => {
  const result = await fetchDeviceSubTypes({
    parentCode: "iot_device_type",
  });
  if (result.length > 0) {
    deviceType.value = result.map((item: any) => ({
      label: item.name,
      value: item.code,
    }));
    deviceType.value.push({
      label: "智能表",
      value: "zhinengbiao",
    });

    queryForm.value.typeValue = deviceType.value[1].value;
    await search();
  }
};
const typeChange = () => {
  queryForm.value.name = "";
  currentPage.value = 1; // 类型改变时重置到第一页
  search();
};
onMounted(async () => {
  try {
    map = AppMaplibre.getMap();
  } catch (error) {
    console.warn("获取地图实例失败:", error);
  }
  await getDeviceTypes();
});
</script>
<style lang="scss" scoped>
.result-section {
  margin-top: 16px;

  .result-label {
    color: #2c3037;
    font-size: 14px;
    margin-right: 8px;
  }

  .error {
    font-size: 14px;
    font-weight: 500;
    color: #ff4d4f;
  }
}
</style>
