<template>
  <div class="footer">
    <div class="btn">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="btn-item"
        :class="isActive === index ? 'active' : ''"
        @click="handleClick(item, index)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const isActive = ref(0);
const list = ref([
  {
    label: "空间查询",
    value: "0",
  },
  {
    label: "分类查询",
    value: "0",
  },
  {
    label: "汇总统计",
    value: "0",
  },
  {
    label: "通用统计",
    value: "0",
  },
  {
    label: "分类统计",
    value: "0",
  },
  {
    label: "用户链接",
    value: "0",
  },
]);
const handleClick = (item: any, index: number) => {
  isActive.value = index;
};
</script>
<style lang="scss" scoped>
.footer {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 40px;
  z-index: 2;
}
.btn {
  display: flex;
  .btn-item {
    width: 170px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: url(@/assets/images/btn.png) no-repeat;
    margin-right: 20px;
    color: #a7dcff;
  }
  .active {
    background: url(@/assets/images/btn-active.png) no-repeat;
    color: #fff;
  }
}
</style>
