import hRequest from '@/utils/http';
import type { DataType } from '@/utils/http/types';

// 分页
export const serverPage = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/business/server/manage/page',
    params
  });
};
// 新增
export const addServer = (data: any) => {
  return hRequest.post<DataType>({
    url: "/business/server/manage",
    data: data
  });
};
// 修改
export const editServer = (data: any) => {
  return hRequest.put<DataType>({
    url: "/business/server/manage",
    data: data
  });
};
// 详情
export const detailsServer = (id: any) => {
  return hRequest.get<DataType>({
    url: `/business/server/manage/${id}`,
  });
};
// 删除
export const deleteServer = (id: any) => {
  return hRequest.delete<DataType>({
    url: `/business/server/manage/${id}`,
  });
};
// 服务器信息
export const serveMonitor = () => {
  return hRequest.get<DataType<any>>({
    url: '/sys/monitor/server'
  });
};
// 缓存监控
export const serveCache = () => {
  return hRequest.get<DataType<any>>({
    url: '/sys/monitor/cache'
  });
};