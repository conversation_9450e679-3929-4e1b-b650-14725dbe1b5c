<template>
  <page-card class="tabulate-sta" title="汇总统计" :close-icon="false">
    <SpatialQueryButtons :map-engine="mapEngine" :config="spatialQueryConfig" @query-start="handleQueryStart"
      @query-complete="handleQueryComplete" @query-error="handleQueryError" @draw-start="handleDrawStart"
      @draw-finish="handleDrawFinish" />
    <div class="search-result" v-if="currentResult">
      <div class="statistics-data">
        <div>
          <div class="flex justify-between items-center pr-20px">
            <el-text>设备统计:</el-text>
            <el-button :loading="isDeviceExporting" @click="handleExportDevice" type="primary" link title="导出excel">
              <el-icon size="20">
                <SvgIcon name="query-export" />
              </el-icon>
            </el-button>
          </div>
          <div v-loading="isLoading" v-show="isDeviceChartReady" class="device-chart" ref="deviceChartRef"></div>
          <el-empty v-show="!isDeviceChartReady" :image-size="emptyImgSize" />
        </div>
        <div>
          <div class="flex justify-between items-center">
            <el-text>管网统计:</el-text>
            <el-button :loading="isPipeExporting" @click="handleExportPipe" type="primary" link title="导出excel">
              <el-icon size="20">
                <SvgIcon name="query-export" />
              </el-icon>
            </el-button>
          </div>
          <div v-loading="isLoading" class="pipe-chart" v-show="isPipeChartReady" ref="pipeChartRef"></div>
          <el-empty v-show="!isPipeChartReady" :image-size="emptyImgSize" />
        </div>
      </div>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import SpatialQueryButtons from '@/components/SpatialQueryButtons.vue';
import type {
  QueryType,
  QueryResult,
  SpatialQueryConfig
} from '@/components/SpatialQueryButtons.vue';
import { getLengthByPipeDiameter, tabulateExport,deviceExport } from '@/api/query';
import { initTabulateEchart } from '@/lib/echarts';
import { exportFile } from '@/utils/file';
import type { MapEngineType } from '@/components/SpatialQueryButtons.vue';
import {
  calculateDeviceTypeStatistics,
  spatialQuery,
  processQueryCoordinates
} from '@/utils/deviceUtils';

const route = useRoute()

const mapEngine = computed((): MapEngineType => {
  return route.path.includes('cesium') ? 'cesium' : 'maplibre';
});

const isLoading = ref(false)
const isDeviceExporting = ref(false)
const isPipeExporting = ref(false)

const { initChart } = useEchart()

const emptyImgSize = 120

const getPipeData = async (data: QueryResult) => {
  try {
    const params = data ? JSON.stringify(data.geometry) : ''
    const result = await getLengthByPipeDiameter({ range: params })
    if (result.code === 200) {
      return result.data
    }
    return null
  } catch (error) {
    console.error(error)
    return null
  }
}

let pipeChartOption: any = null
const isPipeChartReady = ref(false)
const pipeChartRef = ref()
const initPipeChart = (result: ISeriesData[] | null) => {
  try {
    if (!result) {
      pipeChartOption = null
      isPipeChartReady.value = false
    }
    isPipeChartReady.value = true
    nextTick(() => {
      pipeChartOption = initChart(pipeChartRef.value, initTabulateEchart(result!))
    })
  } catch (error) {
    console.error('管网统计图表生成失败')
    pipeChartOption = null
    isPipeChartReady.value = false
  }
}

let deviceChartOption: any = null
const isDeviceChartReady = ref(false)
const deviceChartRef = ref()

/**
 * 初始化设备统计图表
 */
const initDeviceChart = (result: ISeriesData[] | null) => {
  try {
    if (!result || result.length === 0) {
      deviceChartOption = null
      isDeviceChartReady.value = false
      return
    }
    isDeviceChartReady.value = true
    nextTick(() => {
      // 创建设备统计图表配置
      const deviceChartConfig = createDeviceChartOption(result)
      deviceChartOption = initChart(deviceChartRef.value, deviceChartConfig)
    })
  } catch (error) {
    console.error('设备统计图表生成失败', error)
    deviceChartOption = null
    isDeviceChartReady.value = false
  }
}

/**
 * 创建设备统计图表配置
 */
const createDeviceChartOption = (data: ISeriesData[]) => {
  const total = data.reduce((sum: number, item: ISeriesData) => {
    return sum + item.value
  }, 0)

  const option: any = {
    title: {
      text: `总数：${total}台`,
      left: 'center',
      top: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 500,
        fontFamily: 'SansCN-Medium',
        color: '#2C3037'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.marker}  ${params.name}：${params.value}台`
      }
    },
    series: [
      {
        type: 'pie',
        center: ['50%','50%'],
        radius: ['70%', '80%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{c|{c}台}\n{hr|}',
          fontSize: 12,
          fontWeight: 500,
          fontFamily: 'SansCN-Regular',
          distanceToLabelLine: 0,
          color: 'inherit',
          rich: {
            hr: {
              borderColor: 'inherit',
              width: '100%',
              borderWidth: 0.5,
              height: 0
            },
          }
        },
        labelLayout: {
          verticalAlign: 'bottom',
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 0
        },
        data: data
      }
    ]
  }

  return option
}


const getChartData = async (result: QueryResult) => {
  try {
    isLoading.value = true
    const pipeRes = await getPipeData(result)
    /**
     * 当前数据格式是: "90.0000000000": 1390.96,处理一次去掉0
     */
    if (pipeRes) {
      const arr = Object.entries(pipeRes).map(([k, v]): ISeriesData => ({
        name: k.replace(/\.0+$/, ''), // 去掉小数点后的0
        value: v as number
      }));
      initPipeChart(arr)
    }
  } catch (error) {
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

const showDevice = ref(false);

// 获取设备数据
const fetchIotDevices = async (result: any) => {

  try {
    isLoading.value = true  
    const coordinates = processQueryCoordinates(result);

    const response = await spatialQuery({ coordinate: coordinates,fieldGroup: 'second_device_type_code' });
    if (response && Object.keys(response).length > 0) {
      // 统计设备数据
      const deviceStats = calculateDeviceTypeStatistics(response);
      // 初始化设备图表
      initDeviceChart(deviceStats);

      showDevice.value = true;
    } else {
      initDeviceChart(null);
      showDevice.value = false;
    }
  } catch (err) {
    console.error('获取物联网设备数据出错:', err);
    initDeviceChart(null);
    showDevice.value = false;
  } finally {
    isLoading.value = false
  }
};

/**
 * @description 空间查询配置
 */
const spatialQueryConfig: Partial<SpatialQueryConfig> = {
  // 沙湾区边界配置
  regionBounds: {
    west: 103.42,
    south: 29.19,
    east: 103.74,
    north: 29.53,
    name: '沙湾区'
  },
  // UI配置
  buttonSize: 'default',
  buttonLayout: 'horizontal',
  buttonSpacing: 8,
  // 功能配置
  enabledQueries: ['all', 'current', 'polygon', 'rectangle'],
  showTips: true,
  // showResult: true,
  // showResultDetails: true,
  // 绘制配置
  clearPreviousDrawing: true,
  autoSwitchToSelect: true
};

// 组件状态
const currentResult = ref<QueryResult | null>(null);

/**
 * @description 处理查询开始事件
 * @param {QueryType} type - 查询类型
 */
const handleQueryStart = (type: QueryType) => {
  currentResult.value = null;
};

/** 全区范围 */
const handleQueryComplete = async (result: QueryResult) => {
  currentResult.value = result;
  // 获取管网统计数据
  await getChartData(result);
  // 获取设备统计数据
  await fetchIotDevices(result);
};

/**
 * @description 处理查询错误事件
 * @param {object} error - 错误信息
 */
const handleQueryError = (error: { type: QueryType; message: string }) => {
  console.error("汇总统计查询错误:", error);
};

/**
 * @description 处理绘制开始事件
 * @param {QueryType} type - 绘制类型
 */
const handleDrawStart = (type: QueryType) => {
  console.log(`开始绘制统计区域: ${type}`);
};

/**
 * @description 处理绘制完成事件
 * @param {QueryResult} result - 绘制结果
 */
const handleDrawFinish = async (result: QueryResult) => {
  currentResult.value = result;
};

/** 导出管网excel */
const handleExportPipe = async () => {
  try {
    isPipeExporting.value = true
    const coords = currentResult.value?.geometry || ''
    const params = coords ? JSON.stringify(coords) : ''
    const res = await tabulateExport({ range: params })
    exportFile(res)
  } catch (error) {
    console.error(error, '汇总统计导出失败')
  } finally {
    isPipeExporting.value = false
  }
}

/** 导出设备excel */
const handleExportDevice = async () => {
  try {
    isDeviceExporting.value = true
    const coords = currentResult.value?.geometry.coordinates[0] ?? []
    const coordsStr = coords.join(';')
    const res = await deviceExport({ coordinate: coordsStr,fieldGroup: '' })
    exportFile(res)
  } catch (error) {
    console.error(error, '设备导出失败')
  } finally {
    isDeviceExporting.value = false
  }
}

/** 清除 */
// const handleClear = () => {
//   currentResult.value = null
//   isDeviceChartReady.value = false
//   isPipeChartReady.value = false
//   showDevice.value = false

//   // 清除图表实例
//   if (deviceChartOption) {
//     deviceChartOption.dispose();
//     deviceChartOption = null;
//   }
//   if (pipeChartOption) {
//     pipeChartOption.dispose();
//     pipeChartOption = null;
//   }
// }
</script>

<style lang="scss" scoped>
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 694px;
}

.tabulate-sta-container {
  padding: 8px;
}

.search-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  font-weight: 700;
}

.statistics-data {
  display: flex;
  margin-top: 12px;

  >div {
    width: 50%;
  }

  .device-chart,
  .pipe-chart {
    width: 100%;
    height: 210px;
  }
}

.el-empty {
  padding: unset;
}
</style>