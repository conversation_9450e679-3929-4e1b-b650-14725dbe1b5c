<template>
  <component :is="menuComponent" v-if="routerInfo?.visible" :router-info="routerInfo">
    <template v-if="routerInfo?.children && routerInfo.children.length">
      <AsideComponent v-for="item in routerInfo.children" :key="item.name" :router-info="item" />
    </template>
  </component>
</template>

<script setup lang="ts">
import MenuItem from './menuItem.vue';
import AsyncSubmenu from './asyncSubmenu.vue';

defineOptions({
  name: 'AsideComponent'
});

const props = withDefaults(
  defineProps<{
    routerInfo: {
      [key: string]: any;
    } | null;
  }>(),
  {
    routerInfo: null
  }
);

const menuComponent = computed(() => {
  if (
    props.routerInfo?.children &&
    props.routerInfo.children.filter((item: any) => item.visible === '1').length
  ) {
    return AsyncSubmenu;
  } else {
    return MenuItem;
  }
});
</script>
