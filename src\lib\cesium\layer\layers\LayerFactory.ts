import { BillboardLayer } from './BillboardLayer';
import { ImageryLayer } from './ImageryLayer';
import { LayerGroup } from './LayerGroup';
import { TerrainLayer } from './TerrainLayer';
import { TilesetLayer } from './TilesetLayer';

export class LayerFactory {
  static crateLayer(type: string, options: any) {
    switch (type) {
      case 'imagery':
        return new ImageryLayer(options);
      case 'terrain':
        return new TerrainLayer(options);
      case 'reality':
        return new TilesetLayer(options);
      case 'pipeline':
        return new TilesetLayer(options);
      case 'tileset':
        return new TilesetLayer(options);
      case 'device':
        return new BillboardLayer(options);
      case 'group':
      default:
        return new LayerGroup(options);
    }
  }
}
