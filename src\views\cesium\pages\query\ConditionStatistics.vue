<template>
  <page-card class="condition-sta" title="条件统计" :close-icon="false">
    <el-form label-position="top">
      <!-- 动态条件表单 -->
      <div class="form-scroll-container">
        <div v-for="(condition, index) in queryForm.conditions" :key="index" class="condition-row">
        <el-row :gutter="12" class="condition-form-row">
          <el-col :span="5">
            <el-form-item class="page-form-item" :label="index === 0 ? '管网图层：' : ''">
              <el-select
                v-model="pipeType"
                placeholder="请选择管网图层"
                @change="handlePipeTypeChange"
                :disabled="loading"
              >
                <el-option
                  v-for="item in pipeLayerTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item class="page-form-item" :label="index === 0 ? '分类字段：' : ''">
              <el-select
                v-model="condition.field"
                placeholder="请选择分类字段"
                :disabled="!condition.pipeType || loading"
              >
                <el-option
                  v-for="item in currentFieldOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item class="page-form-item" :label="index === 0 ? '操作符：' : ''">
              <el-select
                v-model="condition.operator"
                placeholder="请选择操作符"
                :disabled="loading"
              >
                <el-option
                  v-for="item in operatorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item class="page-form-item" :label="index === 0 ? '输入值：' : ''">
              <el-input
                v-model="condition.value"
                placeholder="请输入值"
                :disabled="loading"
              />
            </el-form-item>
          </el-col>
          <el-col :span="3" v-if="queryForm.conditions.length > 1 && index < queryForm.conditions.length - 1">
            <el-form-item class="page-form-item" :label="index === 0 ? '逻辑运算：' : ''">
              <el-select
                v-model="condition.nextLogicalOp"
                placeholder="逻辑运算符"
                :disabled="loading"
              >
                <el-option label="AND" value="AND" />
                <el-option label="OR" value="OR" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item class="page-form-item" :label="''">
              <div class="condition-actions">
                <el-button
                  v-if="index === queryForm.conditions.length - 1"
                  type="primary"
                  @click="addCondition"
                  :disabled="loading"
                >
                  添加
                </el-button>
                <el-button
                  v-if="queryForm.conditions.length > 1"
                  type="danger"
                  @click="removeCondition(index)"
                  :disabled="loading"
                >
                  删除
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        </div>
      </div>
    </el-form>
    <SpatialQueryButtons
      ref="spatialQueryButtonsRef"
      :map-engine="mapEngine"
      :config="spatialQueryConfig"
      @query-start="handleQueryStart"
      @query-complete="handleQueryComplete"
      @query-error="handleQueryError"
      @draw-start="handleDrawStart"
      @draw-finish="handleDrawFinish"
      :fetchLoading="loading"
      class="px-6px"
    >
      <template #other-buttons>
        <el-button class="clear-btn" @click="handleReset">重置</el-button>
      </template>
    </SpatialQueryButtons>

    <!-- 表格和分页 -->
    <el-table
      :data="tableData"
      class="routeCt h-70"
      style="width: 100%"
      v-loading="tableLoading"
      empty-text="暂无数据"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
    >
      <el-table-column
        label="序号"
        width="60"
        align="center"
      >
        <template #default="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>

      <!-- 管点字段 -->
      <template v-if="pipeType === 'pipepoint'">
        <el-table-column prop="gid" label="GID" show-overflow-tooltip></el-table-column>
        <el-table-column prop="gl" label="类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="gxddh" label="管点编号" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sx" label="属性" show-overflow-tooltip></el-table-column>
        <el-table-column prop="fsw" label="附属物" show-overflow-tooltip></el-table-column>
        <el-table-column prop="dmgc" label="地面高程" show-overflow-tooltip></el-table-column>
        <el-table-column prop="x" label="X" show-overflow-tooltip></el-table-column>
        <el-table-column prop="y" label="Y" show-overflow-tooltip></el-table-column>
        <el-table-column prop="js" label="井深" show-overflow-tooltip></el-table-column>
        <el-table-column prop="jggg" label="井盖规格" show-overflow-tooltip></el-table-column>
        <el-table-column prop="jgcz" label="井盖材质" show-overflow-tooltip></el-table-column>
        <el-table-column prop="szdl" label="所在道路" show-overflow-tooltip></el-table-column>
      </template>

      <!-- 管线字段 -->
      <template v-else-if="pipeType === 'pipeline'">
        <el-table-column prop="gid" label="ID" show-overflow-tooltip></el-table-column>
        <el-table-column prop="gl" label="类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="qdbh" label="起点编号" show-overflow-tooltip></el-table-column>
        <el-table-column prop="zdbh" label="终点编号" show-overflow-tooltip></el-table-column>
        <el-table-column prop="qdx" label="起点X" show-overflow-tooltip></el-table-column>
        <el-table-column prop="qdy" label="起点Y" show-overflow-tooltip></el-table-column>
        <el-table-column prop="qdgc" label="起点高程" show-overflow-tooltip></el-table-column>
        <el-table-column prop="qdms" label="起点埋深" show-overflow-tooltip></el-table-column>
        <el-table-column prop="zdx" label="终点X" show-overflow-tooltip></el-table-column>
        <el-table-column prop="zdy" label="终点Y" show-overflow-tooltip></el-table-column>
        <el-table-column prop="zdgc" label="终点高程" show-overflow-tooltip></el-table-column>
        <el-table-column prop="zdms" label="终点埋深" show-overflow-tooltip></el-table-column>
        <el-table-column prop="cz" label="材质" show-overflow-tooltip></el-table-column>
        <el-table-column prop="msfs" label="埋设方式" show-overflow-tooltip></el-table-column>
        <el-table-column prop="gj" label="管径" show-overflow-tooltip></el-table-column>
        <el-table-column prop="szdl" label="所在道路" show-overflow-tooltip></el-table-column>
        <el-table-column prop="lx" label="流向" show-overflow-tooltip></el-table-column>
        <el-table-column prop="gdcd" label="管段长度" show-overflow-tooltip></el-table-column>
        <el-table-column prop="gxbm" label="管线编码" show-overflow-tooltip></el-table-column>
      </template>

      <!-- <el-table-column
        label="操作"
        fixed="right"
        width="80"
      >
        <template #default="scope">
          <el-button
            type="text"
            style="color: #1966ff"
            @click="handleCheck(scope.row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <div class="flex justify-between items-center mt-5">
      <div>
        <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
      </div>
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :total="total"
        :pager-count="5"
        layout="prev, pager, next, jumper"
        class="pagination"
        background
        small
      ></el-pagination>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import SpatialQueryButtons from '@/components/SpatialQueryButtons.vue';
import type {
  QueryType,
  QueryResult,
  SpatialQueryConfig
} from '@/components/SpatialQueryButtons.vue';
import type { MapEngineType } from '@/components/SpatialQueryButtons.vue';
import { getPipeLineAccessoryList, getPipeNodeAccessoryList, advancedQueryForPipeLine, advancedQueryForPipeNode } from '@/api/query';

const route = useRoute();

const mapEngine = computed((): MapEngineType => {
  return route.path.includes('cesium') ? 'cesium' : 'maplibre';
});

// 管网图层数据
const pipeLayerTypes = [
  { label: '管线', value: 'pipeline' },
  { label: '管点', value: 'pipepoint' }
] as const;

const operatorOptions = [
  { label: '等于', value: '=' },
  { label: '不等于', value: '!=' },
  { label: '大于', value: '>' },
  { label: '小于', value: '<' },
  { label: '大于等于', value: '>=' },
  { label: '小于等于', value: '<=' },
  { label: '包含', value: 'LIKE' },
  { label: '为空', value: 'IS NULL' },
  { label: '不为空', value: 'IS NOT NULL' },
  { label: '为空', value: 'IS NULL' },
  { label: '不为空', value: 'IS NOT NULL' }
];

const loading = ref(false);

const initQueryForm = () => {
  return {
    conditions: [
      {
        pipeType: "",
        field: "",
        operator: "",
        value: '',
        nextLogicalOp: "AND"
      }
    ],
    range: '',
    currPage: 1,
    pageSize: 10
  };
}
const queryForm = ref(initQueryForm());

// 管网图层类型 - 独立于queryForm，不作为传参，所有条件行共享
const pipeType = ref('');

// 预加载的分类字段数据
const pipelineFieldOptions = ref<Array<{ label: string; value: string }>>([]);
const pipepointFieldOptions = ref<Array<{ label: string; value: string }>>([]);

// 当前分类字段选项 - 根据管网图层类型动态计算
const currentFieldOptions = computed(() => {
  if (pipeType.value === 'pipeline') {
    return pipelineFieldOptions.value;
  } else if (pipeType.value === 'pipepoint') {
    return pipepointFieldOptions.value;
  }
  return [];
});

// 删除不需要的 getFieldOptions 方法，改用计算属性

/**
 * 添加条件行
 */
const addCondition = () => {
  queryForm.value.conditions.push({
    pipeType: pipeType.value, // 新行默认使用当前选择的管网图层类型
    field: "",
    operator: "",
    value: '',
    nextLogicalOp: "AND"
  });
};

/**
 * 删除条件行
 */
const removeCondition = (index: number) => {
  if (queryForm.value.conditions.length > 1) {
    queryForm.value.conditions.splice(index, 1);
  }
};

// 空间查询配置
const spatialQueryConfig: Partial<SpatialQueryConfig> = {
  // 沙湾区边界配置
  regionBounds: {
    west: 103.42,
    south: 29.19,
    east: 103.74,
    north: 29.53,
    name: '沙湾区'
  },
  // UI配置
  buttonSize: 'default',
  buttonLayout: 'horizontal',
  buttonSpacing: 8,
  // 功能配置
  enabledQueries: ['all', 'current', 'polygon', 'rectangle'],
  showTips: true,
  // 绘制配置
  clearPreviousDrawing: true,
  autoSwitchToSelect: true
};

// 组件状态
const currentResult = ref<QueryResult | null>(null);

// SpatialQueryButtons 组件引用
const spatialQueryButtonsRef = ref();

// 表格相关状态
const tableData = ref([]);
const tableLoading = ref(false);

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const pageSizes = [10, 20, 50, 100];

/**
 * 预加载分类字段数据
 */
const loadFieldOptions = async () => {
  try {
    loading.value = true;

    // 并行请求管线和管点的分类字段
    const [pipelineResponse, pipepointResponse] = await Promise.all([
      getPipeLineAccessoryList(),
      getPipeNodeAccessoryList()
    ]);

    // 处理管线字段数据
    if (pipelineResponse && pipelineResponse.code === 200 && pipelineResponse.data) {
      pipelineFieldOptions.value = pipelineResponse.data.map((item: any) => ({
        label: item.name,
        value: item.field
      }));
    }

    // 处理管点字段数据
    if (pipepointResponse && pipepointResponse.code === 200 && pipepointResponse.data) {
      pipepointFieldOptions.value = pipepointResponse.data.map((item: any) => ({
        label: item.name,
        value: item.field
      }));
    }

  } catch (error) {
    console.error('预加载分类字段失败:', error);
    ElMessage.error('加载分类字段失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 处理管网图层类型变化
 */
const handlePipeTypeChange = () => {
  // 清空所有条件行的分类字段选择
  queryForm.value.conditions.forEach((condition) => {
    condition.pipeType = pipeType.value;
    condition.field = '';
  });
  // 清空表格数据
  tableData.value = [];
  total.value = 0;
  currentPage.value = 1;
};

/**
 * 分页页码改变
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  queryForm.value.currPage = page;
  executeConditionQuery();
};

/**
 * 分页大小改变
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  queryForm.value.pageSize = size;
  queryForm.value.currPage = 1;
  executeConditionQuery();
};

/**
 * 表格行点击事件
 */
const handleRowClick = (row: any) => {
  console.log('行点击:', row);
};

/**
 * 表格行类名
 */
const tableRowClassName = ({ row, rowIndex }: { row: any; rowIndex: number }) => {
  // return rowIndex % 2 === 1 ? 'warning-row' : '';
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};

/**
 * 查看详情
 */
const handleCheck = (row: any) => {
  console.log('查看详情:', row);
  // TODO: 实现详情查看功能
};

/**
 * 执行条件统计查询
 */
const executeConditionQuery = async () => {
  if (!currentResult.value || queryForm.value.conditions.length === 0) {
    return;
  }

  // 检查是否所有条件都已填写完整
  // 检查是否选择了管网图层类型
  if (!pipeType.value) {
    ElMessage.warning('请选择管网图层类型');
    return;
  }

  const hasIncompleteCondition = queryForm.value.conditions.some(condition =>
    !condition.field || !condition.operator || !condition.value
  );

  if (hasIncompleteCondition) {
    ElMessage.warning('请完善所有条件信息');
    return;
  }

  try {
    loading.value = true;
    tableLoading.value = true;

    const coords = currentResult.value?.geometry ?? []
    const coordsStr = JSON.stringify(coords)

    // 设置查询范围
    queryForm.value.range = coordsStr;

    let statisticsResult;

    // 根据管网图层类型调用对应的统计接口
    if (pipeType.value === 'pipeline') {
      // 管线统计
      statisticsResult = await advancedQueryForPipeLine(queryForm.value);
    } else if (pipeType.value === 'pipepoint') {
      // 管点统计
      statisticsResult = await advancedQueryForPipeNode(queryForm.value);
    }

    // 处理查询结果
    if (statisticsResult && statisticsResult.code === 200) {
      // 更新表格数据和分页信息
      tableData.value = statisticsResult.data.list || [];
      total.value = statisticsResult.data.totalCount || 0;
      currentPage.value = statisticsResult.data.currPage || 1;
      pageSize.value = statisticsResult.data.pageSize || 10;

      console.log('条件统计查询结果:', statisticsResult);
    } else {
      ElMessage.error('查询失败: ' + (statisticsResult?.msg || '未知错误'));
      tableData.value = [];
      total.value = 0;
    }

  } catch (error) {
    console.error('条件统计查询失败:', error);
    ElMessage.error('条件统计查询失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
    tableLoading.value = false;
  }
};

/**
 * @description 处理查询开始事件
 * @param {QueryType} type - 查询类型
 */
const handleQueryStart = (type: QueryType) => {
  // 清空当前查询结果
  currentResult.value = null;
};

/**
 * @description 处理查询完成事件
 * @param {QueryResult} result - 查询结果
 */
const handleQueryComplete = async (result: QueryResult) => {
  currentResult.value = result;

  await executeConditionQuery();
};

/**
 * @description 处理查询错误事件
 * @param {object} error - 错误信息
 */
const handleQueryError = (error: { type: QueryType; message: string }) => {
  console.error('条件统计查询错误:', error);
};

/**
 * @description 处理绘制开始事件
 * @param {QueryType} type - 绘制类型
 */
const handleDrawStart = (type: QueryType) => {
  console.log(`开始绘制统计区域: ${type}`);
};

/**
 * @description 处理绘制完成事件
 * @param {QueryResult} result - 绘制结果
 */
const handleDrawFinish = (result: QueryResult) => {
  currentResult.value = result;
};

const handleReset = () => {
  // 清除所有数据
  queryForm.value = initQueryForm();
  pipeType.value = '';

  // 清空表格数据
  tableData.value = [];
  total.value = 0;
  currentPage.value = 1;

  if (spatialQueryButtonsRef.value && spatialQueryButtonsRef.value.clearActive) {
    spatialQueryButtonsRef.value.clearActive();
  } else {
    console.error('无法访问 SpatialQueryButtons 的 clearActive 方法');
  }

  currentResult.value = null;
};

// 组件挂载时预加载分类字段数据
onMounted(() => {
  loadFieldOptions();
});
</script>

<style scoped lang="scss">
.condition-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 794px;
  max-width: calc(100vw - 20px);
  max-height: 100%;
  box-sizing: border-box;
}

:deep(.el-row) {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

:deep(.el-col) {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

:deep(.page-form-item) {
  margin-bottom: 16px;

  .el-form-item__label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.condition-row {
  margin-bottom: 12px;

  &:not(:last-child) {
    border-bottom: 1px solid #ebeef5;
    // padding-bottom: 12px;
  }
}

.condition-form-row {
  align-items: flex-end;
}

.condition-actions {
  display: flex;
  gap: 8px;

  .el-button {
    min-width: 60px;
  }
}

.form-scroll-container {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.routeCt {
  margin-top: 16px;
}

:deep(.warning-row) {
  background: #fdf6ec;
}

:deep(.el-table .warning-row td) {
  background-color: #fdf6ec;
}

.pagination {
  justify-content: flex-end;
}
</style>