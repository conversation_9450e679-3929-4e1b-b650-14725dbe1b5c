# 基础图层管理器图层顺序优化

## 概述

本文档记录了对 `BaseLayerManager.ts` 中 `getTopLayerIds` 方法的完善，确保在切换底图时，所有业务图层（包括MVT注记图层、设备聚合图层等）始终保持在底图之上。

## 改进内容

### 🎯 核心问题：图层顺序管理

**问题描述**：
- ❌ 切换底图时，业务图层可能被底图覆盖
- ❌ MVT注记图层和设备聚合图层未被正确识别
- ❌ 动态创建的图层无法自动置顶
- ❌ 图层检测逻辑不够完善

**解决方案**：
- ✅ 完善图层检测逻辑，支持动态图层识别
- ✅ 添加MVT注记图层自动检测
- ✅ 添加设备聚合图层自动检测
- ✅ 增强底图图层识别机制
- ✅ 提供详细的调试信息

## 技术实现

### 🔧 图层分类检测

#### **1. 静态图层检测**
```typescript
// 标绘图层
const plotLayerIds = [
  'plot-features-layer-point',
  'plot-features-layer-line', 
  'plot-features-layer-polygon-fill',
  'plot-features-layer-polygon-stroke'
]

// 管网图层
const pipeLayerIds = [
  'pipe-nodes-layer',
  'pipe-lines-layer',
  'pipe-nodes-labels',
  'pipe-lines-labels'
]

// 基础设备图层
const deviceLayerIds = [
  'device-layer'
]

// 管线、管点图层
const pipeNodeLayerIds = ['mvt_pipeNode']
const pipeLineLayerIds = ['mvt_pipeLine']
```

#### **2. 动态MVT注记图层检测**
```typescript
// 检测所有以 "_label" 结尾的symbol类型图层
const mvtLabelLayerIds: string[] = []
allMapLayers.forEach(layer => {
  if (layer.id.endsWith('_label') && layer.type === 'symbol') {
    mvtLabelLayerIds.push(layer.id)
  }
})
```

**检测规则**：
- 图层ID以 `_label` 结尾
- 图层类型为 `symbol`
- 对应MVT图层的注记图层

#### **3. 动态设备聚合图层检测**
```typescript
// 检测设备相关的所有图层
const deviceClusterLayerIds: string[] = []
allMapLayers.forEach(layer => {
  const layerId = layer.id
  
  // 设备聚合图层：device_{icon}_clusters
  if (layerId.includes('_clusters') && layerId.startsWith('device_')) {
    deviceClusterLayerIds.push(layerId)
  }
  
  // 设备聚合数量图层：device_{icon}_cluster_count
  if (layerId.includes('_cluster_count') && layerId.startsWith('device_')) {
    deviceClusterLayerIds.push(layerId)
  }
  
  // 设备展开图层：device_{icon}_expanded_{clusterId}
  if (layerId.includes('_expanded_') && layerId.startsWith('device_')) {
    deviceClusterLayerIds.push(layerId)
  }
  
  // 设备主图层：device_{icon}
  if (layerId.startsWith('device_') && 
      !layerId.includes('_clusters') && 
      !layerId.includes('_cluster_count') && 
      !layerId.includes('_expanded_') &&
      !layerId.includes('_source')) {
    deviceClusterLayerIds.push(layerId)
  }
})
```

**检测的设备图层类型**：
- **聚合圆圈图层**：`device_{icon}_clusters`
- **聚合数量图层**：`device_{icon}_cluster_count`
- **展开设备图层**：`device_{icon}_expanded_{clusterId}`
- **设备主图层**：`device_{icon}`

### 🔍 底图图层识别

#### **新增 `isBasemapLayer` 方法**
```typescript
private isBasemapLayer(layerId: string): boolean {
  // 当前底图图层
  if (this.currentBasemap && layerId === this.currentBasemap.id) {
    return true
  }
  
  // 当前注记图层
  if (this.currentAnnotationLayer && layerId === this.currentAnnotationLayer) {
    return true
  }
  
  // 天地图相关图层
  if (layerId.startsWith('tdt-') || layerId.includes('-annotation')) {
    return true
  }
  
  // 百度地图相关图层
  if (layerId.startsWith('baidu-')) {
    return true
  }
  
  // 高德地图相关图层
  if (layerId.startsWith('amap-')) {
    return true
  }
  
  // 其他底图相关图层模式
  const basemapPatterns = [
    /^(satellite|vector|terrain|hybrid)[-_]/,  // 卫星、矢量、地形、混合底图
    /^(osm|mapbox|google)[-_]/,                // OSM、Mapbox、Google底图
    /^basemap[-_]/,                            // 通用底图前缀
    /[-_](base|background)$/                   // 底图后缀
  ]
  
  return basemapPatterns.some(pattern => pattern.test(layerId))
}
```

**识别规则**：
1. **当前活动底图**：通过管理器状态识别
2. **命名模式匹配**：通过正则表达式识别常见底图命名模式
3. **扩展性**：支持多种底图服务商的命名规则

### 📊 调试信息增强

```typescript
if (this.debug && layerIds.length > 0) {
  console.log('🔝 检测到需要置顶的业务图层:', {
    '标绘图层': plotLayerIds.filter(id => this.map.getLayer(id)),
    '管网图层': pipeLayerIds.filter(id => this.map.getLayer(id)),
    '设备图层': deviceLayerIds.filter(id => this.map.getLayer(id)),
    '管线图层': pipeLineLayerIds.filter(id => this.map.getLayer(id)),
    '管点图层': pipeNodeLayerIds.filter(id => this.map.getLayer(id)),
    'MVT注记图层': mvtLabelLayerIds.filter(id => this.map.getLayer(id)),
    '设备聚合图层': deviceClusterLayerIds.filter(id => this.map.getLayer(id)),
    '其他业务图层': businessLayerIds.filter(id => this.map.getLayer(id))
  })
}
```

## 图层检测流程

### 🔄 检测流程图

```
开始检测
    ↓
获取地图所有图层
    ↓
┌─────────────────────────────────────┐
│ 1. 检测静态预定义图层                │
│   - 标绘图层                        │
│   - 管网图层                        │
│   - 基础设备图层                    │
│   - 管线管点图层                    │
└─────────────────────────────────────┘
    ↓
┌─────────────────────────────────────┐
│ 2. 动态检测MVT注记图层              │
│   - 以"_label"结尾                 │
│   - 类型为"symbol"                 │
└─────────────────────────────────────┘
    ↓
┌─────────────────────────────────────┐
│ 3. 动态检测设备聚合图层              │
│   - device_{icon}_clusters         │
│   - device_{icon}_cluster_count    │
│   - device_{icon}_expanded_{id}    │
│   - device_{icon}                  │
└─────────────────────────────────────┘
    ↓
┌─────────────────────────────────────┐
│ 4. 检测其他业务图层                  │
│   - 排除底图图层                    │
│   - 排除已检测图层                  │
└─────────────────────────────────────┘
    ↓
┌─────────────────────────────────────┐
│ 5. 验证图层存在性                    │
│   - 检查图层是否在地图中            │
│   - 过滤不存在的图层                │
└─────────────────────────────────────┘
    ↓
返回需要置顶的图层列表
```

## 支持的图层类型

### 📋 完整图层类型列表

| 图层类别 | 图层ID模式 | 示例 | 检测方式 |
|----------|------------|------|----------|
| **标绘图层** | `plot-features-layer-*` | `plot-features-layer-point` | 静态列表 |
| **管网图层** | `pipe-*-layer` | `pipe-nodes-layer` | 静态列表 |
| **设备图层** | `device-layer` | `device-layer` | 静态列表 |
| **管线图层** | `mvt_pipeLine` | `mvt_pipeLine` | 静态列表 |
| **管点图层** | `mvt_pipeNode` | `mvt_pipeNode` | 静态列表 |
| **MVT注记** | `{layerId}_label` | `pipeline_layer_label` | 动态检测 |
| **设备聚合** | `device_{icon}_clusters` | `device_pressure_clusters` | 动态检测 |
| **聚合数量** | `device_{icon}_cluster_count` | `device_noise_cluster_count` | 动态检测 |
| **展开设备** | `device_{icon}_expanded_{id}` | `device_flow_expanded_123` | 动态检测 |
| **设备主图层** | `device_{icon}` | `device_watermeter` | 动态检测 |

### 🚫 排除的底图图层

| 底图类型 | 图层ID模式 | 示例 |
|----------|------------|------|
| **天地图** | `tdt-*`, `*-annotation` | `tdt-vector`, `tdt-vector-annotation` |
| **百度地图** | `baidu-*` | `baidu-normal`, `baidu-satellite` |
| **高德地图** | `amap-*` | `amap-normal`, `amap-satellite` |
| **通用底图** | `basemap-*`, `*-base`, `*-background` | `basemap-osm`, `layer-base` |

## 使用效果

### 🎯 切换底图时的图层顺序

```
顶层 ↑
├── 8. 其他业务图层
├── 7. MVT注记图层 ({layerId}_label)
├── 6. 设备展开图层 (device_{icon}_expanded_{id})
├── 6. 设备聚合数量图层 (device_{icon}_cluster_count)
├── 6. 设备聚合图层 (device_{icon}_clusters)
├── 6. 设备主图层 (device_{icon})
├── 5. 管点图层 (mvt_pipeNode) ⭐ 在管线图层之上
├── 4. 管线图层 (mvt_pipeLine)
├── 3. 设备图层 (device-layer)
├── 2. 管网图层 (pipe-*-layer)
├── 1. 标绘图层 (plot-features-layer-*)
├── 天地图注记图层 (tdt-*-annotation)
└── 底图图层 (tdt-*, baidu-*, amap-*)
底层 ↓
```

**重要说明**：
- ⭐ **管点图层始终在管线图层之上**，确保管点标记不被管线遮挡
- 数字表示图层的相对层级，数字越大越靠近顶层
- 同一层级内的图层按添加顺序排列

### 📊 调试输出示例

```javascript
🔝 检测到需要置顶的业务图层（从底层到顶层）: {
  "1. 标绘图层": ["plot-features-layer-point", "plot-features-layer-line"],
  "2. 管网图层": ["pipe-nodes-layer", "pipe-lines-layer"],
  "3. 设备图层": ["device-layer"],
  "4. 管线图层": ["mvt_pipeLine"],
  "5. 管点图层": ["mvt_pipeNode"],
  "6. 设备聚合图层": [
    "device_pressure_clusters",
    "device_pressure_cluster_count",
    "device_noise",
    "device_flow_expanded_123"
  ],
  "7. MVT注记图层": ["pipeline_layer_label", "water_system_label"],
  "8. 其他业务图层": ["custom_analysis_layer"]
}
```

**层级说明**：
- 数字越小的图层越靠近底图
- 数字越大的图层越靠近顶层
- **管点图层(5)在管线图层(4)之上**，确保管点可见性

## 性能优化

### 🚀 优化特性

1. **一次性检测**：在切换底图时统一检测所有图层
2. **存在性验证**：只处理实际存在的图层，避免无效操作
3. **分类处理**：按图层类型分类处理，提高检测效率
4. **调试模式**：仅在调试模式下输出详细信息

### 📈 性能建议

1. **图层命名规范**：遵循统一的命名规范，提高检测准确性
2. **图层分组**：合理使用图层分组，减少图层数量
3. **按需加载**：只在需要时创建图层，避免不必要的图层

## 扩展性

### 🔧 添加新图层类型

如需支持新的图层类型，可以通过以下方式扩展：

#### **1. 静态图层**
```typescript
// 在相应的数组中添加图层ID
const newLayerIds = [
  'new-layer-type-1',
  'new-layer-type-2'
]
```

#### **2. 动态图层**
```typescript
// 在动态检测部分添加新的检测逻辑
allMapLayers.forEach(layer => {
  const layerId = layer.id
  
  // 新的图层类型检测
  if (layerId.startsWith('new_prefix_') && layerId.endsWith('_suffix')) {
    newLayerIds.push(layerId)
  }
})
```

#### **3. 底图图层**
```typescript
// 在 isBasemapLayer 方法中添加新的底图模式
const basemapPatterns = [
  // 现有模式...
  /^new_basemap_pattern[-_]/,  // 新的底图模式
]
```

## 故障排除

### 🐛 常见问题

#### **1. 图层未被置顶**
**可能原因**：
- 图层命名不符合检测规则
- 图层类型不在支持列表中
- 图层在检测时尚未创建

**解决方案**：
```typescript
// 开启调试模式查看检测结果
const baseLayerManager = new BaseLayerManager({
  map: map,
  debug: true  // 开启调试
})

// 手动调用图层顺序调整
await baseLayerManager.ensureLayerOrder()
```

#### **2. 底图图层被误识别为业务图层**
**可能原因**：
- 底图图层命名不规范
- `isBasemapLayer` 方法未覆盖该命名模式

**解决方案**：
```typescript
// 在 isBasemapLayer 方法中添加新的识别规则
if (layerId.startsWith('your_basemap_prefix_')) {
  return true
}
```

#### **3. 动态图层检测失败**
**可能原因**：
- 图层创建时机问题
- 图层命名不符合预期模式

**解决方案**：
```typescript
// 确保在图层创建后调用图层顺序调整
await mvtLayer.addTo(map)
await deviceLayer.addTo(map)
await baseLayerManager.ensureLayerOrder()  // 手动调整顺序
```

## 总结

通过这次改进，基础图层管理器获得了：

- 🎯 **智能图层检测**：自动识别各种类型的业务图层
- 🔄 **动态图层支持**：支持运行时创建的MVT注记和设备聚合图层
- 🛡️ **底图保护**：确保底图始终在最底层
- 📊 **完善的调试**：提供详细的图层检测和调整信息
- 🚀 **高性能**：优化的检测算法，减少不必要的操作
- 🔧 **易扩展**：支持新图层类型的快速添加

这些改进确保了在任何底图切换场景下，所有业务图层都能保持正确的显示顺序，为用户提供一致的视觉体验。
