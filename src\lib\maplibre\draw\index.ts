/**
 * @fileoverview MapLibre 绘制工具导出文件
 * @description 提供绘制工具的统一导出接口
 * <AUTHOR>
 * @version 1.0.0
 */

// 导出核心类
export { DrawManager } from './DrawManager';
export { DrawStyleManager, DEFAULT_DRAW_STYLES } from './DrawStyles';
export { DrawEventHandler } from './DrawEventHandler';

// 导出类型定义
export type {
  DrawConfig,
  DrawMode,
  DrawState,
  DrawEventType,
  DrawEventData,
  DrawEventCallback,
  DrawStyleConfig,
  DrawEditConfig,
  GeometryValidationResult,
  DrawToolOptions
} from './types';

// 导出枚举
export { DrawMode as DrawModeEnum, DrawEventType as DrawEventTypeEnum } from './types';

// 导入DrawManager用于便捷函数
import { DrawManager } from './DrawManager';
import type { DrawToolOptions } from './types';
import type { Map as MapLibreMap } from 'maplibre-gl';

/**
 * @description 创建绘制工具管理器的便捷函数
 * @param map - MapLibre GL 地图实例
 * @param options - 绘制工具选项
 * @returns 绘制工具管理器实例
 * @example
 * ```typescript
 * import { createDrawManager } from '@/lib/maplibre/draw';
 * 
 * const drawManager = createDrawManager(map, {
 *   config: {
 *     defaultMode: 'polygon',
 *     styles: {
 *       polygon: {
 *         fillColor: '#ff0000',
 *         fillOpacity: 0.3
 *       }
 *     }
 *   },
 *   onEvent: (event) => {
 *     console.log('绘制事件:', event);
 *   }
 * });
 * ```
 */
export function createDrawManager(map: MapLibreMap, options?: DrawToolOptions): DrawManager {
  return new DrawManager(map, options);
} 