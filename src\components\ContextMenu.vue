<!--
 * @Description: 通用右键上下文菜单组件
 * @Date: 2024-01-10
 * @Author: AI Assistant
 -->
<template>
  <Teleport to="body">
    <div
      v-if="visible"
      class="context-menu"
      :style="{ left: position.x + 'px', top: position.y + 'px' }"
      @click.stop
      @contextmenu.prevent.stop
    >
      <div class="context-menu-content">
        <div
          v-for="(item, index) in menuItems"
          :key="index"
          class="context-menu-item"
          :class="{ 
            disabled: item.disabled,
            danger: item.type === 'danger'
          }"
          @click="handleItemClick(item)"
        >
          <el-icon v-if="item.icon" class="menu-icon">
            <component :is="item.icon" />
          </el-icon>
          <span class="menu-text">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </Teleport>
  
  <!-- 背景遮罩，点击关闭菜单 -->
  <div
    v-if="visible"
    class="context-menu-overlay"
    @click="handleClose"
    @contextmenu.prevent="handleClose"
  />
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import type { Component } from 'vue';

/**
 * @interface MenuItem
 * @description 菜单项配置
 */
export interface MenuItem {
  /** 菜单项唯一标识 */
  key: string;
  /** 显示文本 */
  label: string;
  /** 图标组件 */
  icon?: Component;
  /** 是否禁用 */
  disabled?: boolean;
  /** 菜单项类型 */
  type?: 'default' | 'danger';
  /** 点击回调 */
  action?: () => void;
}

/**
 * @interface Props
 * @description 组件属性
 */
interface Props {
  /** 是否显示菜单 */
  visible?: boolean;
  /** 菜单位置 */
  position?: { x: number; y: number };
  /** 菜单项列表 */
  menuItems?: MenuItem[];
}

/**
 * @interface Emits
 * @description 组件事件
 */
interface Emits {
  /** 关闭菜单 */
  (e: 'close'): void;
  /** 菜单项点击 */
  (e: 'itemClick', item: MenuItem): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  position: () => ({ x: 0, y: 0 }),
  menuItems: () => []
});

const emit = defineEmits<Emits>();

/**
 * @function handleItemClick
 * @description 处理菜单项点击
 */
const handleItemClick = (item: MenuItem): void => {
  if (item.disabled) return;
  
  // 执行菜单项的action
  if (item.action) {
    item.action();
  }
  
  // 发射点击事件
  emit('itemClick', item);
  
  // 关闭菜单
  handleClose();
};

/**
 * @function handleClose
 * @description 关闭菜单
 */
const handleClose = (): void => {
  emit('close');
};

// 监听ESC键关闭菜单
watch(() => props.visible, (visible) => {
  if (visible) {
    nextTick(() => {
      const handleKeydown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          handleClose();
          document.removeEventListener('keydown', handleKeydown);
        }
      };
      document.addEventListener('keydown', handleKeydown);
    });
  }
});
</script>

<style lang="scss" scoped>
.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: transparent;
}

.context-menu {
  position: fixed;
  z-index: 9999;
  min-width: 120px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e4e7ed;
  padding: 4px 0;
  
  .context-menu-content {
    .context-menu-item {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      cursor: pointer;
      font-size: 14px;
      color: #303133;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      &.disabled {
        color: #c0c4cc;
        cursor: not-allowed;
        background-color: transparent !important;
      }
      
      &.danger {
        color: #f56c6c;
        
        &:hover {
          background-color: #fef0f0;
        }
      }
      
      .menu-icon {
        margin-right: 8px;
        font-size: 16px;
      }
      
      .menu-text {
        flex: 1;
        white-space: nowrap;
      }
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .context-menu {
    background: #2d2d2d;
    border-color: #414243;
    
    .context-menu-item {
      color: #e5eaf3;
      
      &:hover {
        background-color: #3d3d3f;
      }
      
      &.disabled {
        color: #6c7293;
      }
      
      &.danger {
        color: #f7959a;
        
        &:hover {
          background-color: #4a2e2e;
        }
      }
    }
  }
}
</style> 