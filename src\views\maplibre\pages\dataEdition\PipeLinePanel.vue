<!--
 * @Description: 管线属性编辑面板
 * @Date: 2024-01-10
 * @Author: AI Assistant
 * @LastEditTime: 2024-01-10
 -->
<template>
  <CustomCard
    :title="title"
    :width="cardWidth"
    :left="position.left"
    :top="position.top"
    :right="position.right"
    @closeHandler="handleClose"
  >
    <div class="pipe-line-panel">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        class="admin-sub-form"
        label-width="auto"
      >
        <!-- 管线类型 -->
        <el-form-item label="管线类型:" prop="gl" required>
          <base-select
            v-model="formData.gl"
            :options="lineTypeOptions"
            placeholder="请选择管线类型"
            :disabled="readonly"
          />
        </el-form-item>

        <!-- 起始点号 -->
        <el-form-item label="起始点号:" prop="qdbh" required>
          <div class="relative w-full">
            <custom-input
              v-model.number="formData.qdbh"
              placeholder="请选择起始点号"
              :disabled="true"
            />
            <div
              class="absolute right-2 top-1 cursor-pointer"
              v-if="!readonly"
              @click="selectStartNodeFromMap"
            >
              <img src="@/assets/images/basemap/loc.png" alt="" srcset="" />
            </div>
            <!-- <el-button
              size="small"
              type="primary"
              icon="Location"
              @click="selectStartNodeFromMap"
              :disabled="readonly"
              v-if="!readonly"
              >选择起点
            </el-button> -->
          </div>
        </el-form-item>

        <!-- 终止点号 -->
        <el-form-item label="终止点号:" prop="zdbh" required>
          <div class="relative w-full">
            <custom-input
              v-model.number="formData.zdbh"
              placeholder="请选择终止点号"
              :disabled="true"
            />
            <!-- <el-button
              size="small"
              type="primary"
              icon="Location"
              @click="selectEndNodeFromMap"
              :disabled="readonly"
              v-if="!readonly"
              >选择终点
            </el-button> -->
            <div
              class="absolute right-2 top-1 cursor-pointer"
              v-if="!readonly"
              @click="selectEndNodeFromMap"
            >
              <img src="@/assets/images/basemap/loc.png" alt="" srcset="" />
            </div>
          </div>
        </el-form-item>

        <!-- 起始高程 -->
        <el-form-item label="起始高程(m):" prop="qdgc">
          <custom-input
            v-model.number="formData.qdgc"
            type="number"
            placeholder="请输入起始高程"
            :disabled="true"
          />
        </el-form-item>

        <!-- 终止高程 -->
        <el-form-item label="终止高程(m):" prop="zdgc">
          <custom-input
            v-model.number="formData.zdgc"
            type="number"
            placeholder="请输入终止高程"
            :disabled="true"
          />
        </el-form-item>

        <!-- 起始埋深 -->
        <el-form-item label="起始埋深(m):" prop="qdms">
          <custom-input
            v-model.number="formData.qdms"
            type="number"
            placeholder="请输入起始埋深"
            :disabled="readonly"
          />
        </el-form-item>

        <!-- 终止埋深 -->
        <el-form-item label="终止埋深(m):" prop="zdms">
          <custom-input
            v-model.number="formData.zdms"
            type="number"
            placeholder="请输入终止埋深"
            :disabled="readonly"
          />
        </el-form-item>

        <!-- 埋设方式 -->
        <el-form-item label="埋设方式:" prop="msfs">
          <base-select
            v-model="formData.msfs"
            :options="installMethodOptions"
            placeholder="请选择埋设方式"
            :disabled="readonly"
          />
        </el-form-item>

        <!-- 管径 -->
        <el-form-item label="管径(mm):" prop="gj" required>
          <base-select
            v-model="formData.gj"
            :options="diameterOptions"
            placeholder="请选择管径"
            :disabled="readonly"
          />
        </el-form-item>

        <!-- 材质 -->
        <el-form-item label="材质:" prop="cz" required>
          <base-select
            v-model="formData.cz"
            :options="materialOptions"
            placeholder="请选择材质"
            :disabled="readonly"
          />
        </el-form-item>

        <!-- 所在道路 -->
        <el-form-item label="所在道路:" prop="szdl">
          <base-select
            v-model="formData.szdl"
            :options="roadOptions"
            placeholder="请选择所在道路"
            :disabled="readonly"
          />
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div v-if="!readonly" class="flex justify-end mt-10">
        <el-button
          type="primary"
          class="primary-btn"
          @click="handleChangePosition"
        >
          重新绘制
        </el-button>
        <el-button
          type="primary"
          class="primary-btn"
          @click="handleSave"
          :loading="saving"
        >
          保存
        </el-button>
        <el-button class="clear-btn" @click="handleCancel">取消</el-button>
        <el-button
          type="danger"
          @click="handleDelete"
          v-if="!isNew"
          :loading="deleting"
        >
          删除
        </el-button>
      </div>
    </div>
  </CustomCard>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from "vue";
import { ElForm, ElMessage, ElMessageBox } from "element-plus";
import type { FormItemRule } from "element-plus";
import { Location } from "@element-plus/icons-vue";
import CustomCard from "@/components/dialog/CustomCard.vue";
import BaseSelect from "@/components/baseSelect/index.vue";
import CustomInput from "@/components/input/index.vue";
import type { Map as MapLibreMap } from "maplibre-gl";
import {
  pipeLineDetail,
  pipeLineAdd,
  pipeLineEdit,
  pipeLineDelete,
  type GsLnDto,
} from "@/api/pipeLine";
import {
  PipeLineTempManager,
  type TempLineNode,
} from "@/lib/maplibre/pipeNetwork/services/PipeLineTempManager";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";

/**
 * @interface Props
 * @description 组件属性接口
 */
interface Props {
  /** 管线GID (编辑/查看时使用) */
  lineGid?: number | string;
  /** 管线数据 (编辑模式时传入) */
  lineData?: any;
  /** 是否为新建模式 */
  isNew?: boolean;
  /** 是否为只读模式 */
  readonly?: boolean;
  /** 起始点信息 */
  startPointInfo?: TempLineNode;
  /** 终止点信息 */
  endPointInfo?: TempLineNode;
  /** 当前选择的起始点（编辑模式下实时更新） */
  currentSelectedStart?: TempLineNode | null;
  /** 当前选择的终止点（编辑模式下实时更新） */
  currentSelectedEnd?: TempLineNode | null;
  /** 编辑模式 */
  editMode?: "creating" | "editing" | "viewing";
  /** 临时管线数据（从PipeEdit传递） */
  tempLineData?: any;
  /** 面板位置 */
  position?: {
    left?: string;
    top?: string;
    width?: string;
    right?: string;
  };
}

/**
 * @interface Emits
 * @description 组件事件接口
 */
interface Emits {
  /** 关闭面板事件 */
  (e: "close"): void;
  /** 保存成功事件 */
  (e: "saved", line: any): void;
  /** 删除成功事件 */
  (e: "deleted", lineId: string): void;
  /** 选择起始点事件 */
  (e: "selectStartNode"): void;
  /** 选择终止点事件 */
  (e: "selectEndNode"): void;
  /** 修改位置事件 */
  (e: "changePosition"): void;
}

const props = withDefaults(defineProps<Props>(), {
  isNew: false,
  readonly: false,
  position: () => ({
    left: "520px",
    top: "110px",
    width: "420px",
  }),
});

const emit = defineEmits<Emits>();

// 表单引用
const formRef = ref<InstanceType<typeof ElForm>>();

// 状态变量
const saving = ref(false);
const deleting = ref(false);

/**
 * @interface FormData
 * @description 表单数据接口，使用中文管线类型
 */
interface FormData {
  gid: number; //主键ID
  gl: string; //管类
  qdbh: string; //起点编号
  zdbh: string; //终点编号
  qdx: number; //起点X坐标
  qdy: number; //起点Y坐标
  zdx: number; //终点X坐标
  zdy: number; //终点Y坐标
  qdgc: number; //起点高程
  qdms: number; //起点埋深
  zdgc: number; //终点高程
  zdms: number; //终点埋深
  cz: string; //材质
  msfs: string; //埋深方式
  gxbm: string; //管线编码
  szdl: string; //所在道路
  gdcd: number; //管段长度
  gj: number; //管径
  geojson: any; //几何信息
}

// 表单数据
const formData = reactive<FormData>({
  gid: 1024, //主键ID
  gl: "给水", //管类
  qdbh: "", //起点编号
  zdbh: "", //终点编号
  qdx: 0, //起点X坐标
  qdy: 0, //起点Y坐标
  zdx: 0, //终点X坐标
  zdy: 0, //终点Y坐标
  qdgc: 0, //起点高程
  qdms: 0, //起点埋深
  zdgc: 0, //终点高程
  zdms: 0, //终点埋深
  cz: "PVC", //材质
  msfs: "直埋", //埋深方式
  gxbm: "", //管线编码
  szdl: "", //所在道路
  gdcd: 0, //管段长度
  gj: 100, //管径
  geojson: "", //几何信息
});

// 计算属性
const title = computed(() => {
  if (props.readonly) return "管线详情";
  return props.isNew ? "新建管线" : "编辑管线";
});

const cardWidth = computed(() => props.position.width || "420px");
const cardLeft = computed(() => props.position.left || "520px");
const cardTop = computed(() => props.position.top || "110px");

// 管线类型选项 - 使用中文作为value
const lineTypeOptions = computed(() => [
  { label: "给水", value: "JS" },
]);

// 管径选项
const diameterOptions = computed(() => [
  { label: "100mm", value: 100 },
  { label: "150mm", value: 150 },
  { label: "200mm", value: 200 },
  { label: "250mm", value: 250 },
  { label: "300mm", value: 300 },
  { label: "400mm", value: 400 },
  { label: "500mm", value: 500 },
  { label: "600mm", value: 600 },
  { label: "800mm", value: 800 },
  { label: "1000mm", value: 1000 },
  { label: "1200mm", value: 1200 },
]);

// 材质选项
const materialOptions = computed(() => [
  { label: "PVC", value: "PVC" },
  { label: "PE", value: "PE" },
  { label: "铸铁", value: "铸铁" },
  { label: "玻璃钢", value: "玻璃钢" },
]);

// 埋设方式选项
const installMethodOptions = computed(() => [
  { label: "直埋", value: "直埋" },
  { label: "明管", value: "明管" },
  { label: "架空", value: "架空" },
  { label: "其他", value: "其他" },
]);

// 道路选项
const roadOptions = computed(() => [
  { label: "双星村", value: "双星村" },
  { label: "建设路", value: "建设路" },
  { label: "人民路", value: "人民路" },
  { label: "解放路", value: "解放路" },
  { label: "中山路", value: "中山路" },
]);

// 表单验证规则
const formRules = computed(
  (): Record<string, FormItemRule[]> => ({
    qdbh: [{ required: true, message: "请选择起始点号", trigger: "change" }],
    zdbh: [
      { required: true, message: "请选择终止点号", trigger: "change" },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value && value === formData.qdbh) {
            callback(new Error("终止点不能与起始点相同"));
          } else {
            callback();
          }
        },
        trigger: "change",
      },
    ],
    gj: [{ required: true, message: "请选择管径", trigger: "change" }],
    cz: [{ required: true, message: "请选择材质", trigger: "change" }],
    qdgc: [
      {
        type: "number" as const,
        message: "起始高程必须为数字",
        trigger: "blur",
      },
    ],
    zdgc: [
      {
        type: "number" as const,
        message: "终止高程必须为数字",
        trigger: "blur",
      },
    ],
    qdms: [
      {
        type: "number" as const,
        message: "起始埋深必须为数字",
        trigger: "blur",
      },
    ],
    zdms: [
      {
        type: "number" as const,
        message: "终止埋深必须为数字",
        trigger: "blur",
      },
    ],
  })
);

/**
 * @function convertFormDataToApiData
 * @description 将表单数据转换为API所需格式，排除不需要的字段
 * @param formData 表单数据
 * @returns GsLnDto API数据格式，排除了qdx、qdy、zdx、zdy、geojson、gxcd字段
 */
const convertFormDataToApiData = (
  formData: FormData
): Omit<GsLnDto, "qdx" | "qdy" | "zdx" | "zdy" | "geojson"> => {
  const apiData: Omit<GsLnDto, "qdx" | "qdy" | "zdx" | "zdy" | "geojson"> = {
    gl: formData.gl,
    qdbh: formData.qdbh,
    zdbh: formData.zdbh,
    qdgc: formData.qdgc,
    qdms: formData.qdms,
    zdgc: formData.zdgc,
    zdms: formData.zdms,
    cz: formData.cz,
    msfs: formData.msfs,
    gj: formData.gj,
    szdl: formData.szdl,
    gdcd: formData.gdcd,
    gxbm: formData.gxbm,
  };

  // 编辑模式时需要传递gid
  if (!props.isNew && formData.gid) {
    apiData.gid = formData.gid;
  }

  return apiData;
};

/**
 * @function populateFormFromLineData
 * @description 从管线数据填充表单
 * @param lineData 管线数据
 */
const populateFormFromLineData = (lineData: any): void => {
  if (!lineData) return;

  try {
    formData.gid = lineData.gid || 0;
    formData.gl = lineData.gl || "给水";
    formData.qdbh = lineData.qdbh || "";
    formData.zdbh = lineData.zdbh || "";
    formData.qdx = lineData.qdx || 0;
    formData.qdy = lineData.qdy || 0;
    formData.zdx = lineData.zdx || 0;
    formData.zdy = lineData.zdy || 0;
    formData.qdgc = lineData.qdgc || 0;
    formData.qdms = lineData.qdms || 0;
    formData.zdgc = lineData.zdgc || 0;
    formData.zdms = lineData.zdms || 0;
    formData.cz = lineData.cz || "PVC";
    formData.msfs = lineData.msfs || "直埋";
    formData.gxbm = lineData.gxbm || "";
    formData.szdl = lineData.szdl || "";
    formData.gdcd = lineData.gdcd || 0;
    formData.gj = lineData.gj || 100;
    formData.geojson = lineData.geojson || "";

    console.log("管线数据已填充到表单:", {
      gid: formData.gid,
      qdbh: formData.qdbh,
      zdbh: formData.zdbh,
      gj: formData.gj,
      cz: formData.cz,
    });
  } catch (error) {
    console.error("填充管线数据到表单失败:", error);
    ElMessage.error("加载管线数据失败");
  }
};

/**
 * @function populateFormFromTempData
 * @description 从临时数据填充表单（新建模式）
 */
const populateFormFromTempData = (): void => {
  if (props.isNew && props.startPointInfo && props.endPointInfo) {
    try {
      formData.qdbh = props.startPointInfo.nodeCode;
      formData.zdbh = props.endPointInfo.nodeCode;
      formData.qdgc = props.startPointInfo.nodeData?.dmgc || 0;
      formData.zdgc = props.endPointInfo.nodeData?.dmgc || 0;

      console.log("临时数据已填充到表单:", {
        qdbh: formData.qdbh,
        zdbh: formData.zdbh,
        qdgc: formData.qdgc,
        zdgc: formData.zdgc,
      });
    } catch (error) {
      console.error("填充临时数据到表单失败:", error);
    }
  }
};

/**
 * @function updateFormFromSelectedNodes
 * @description 从当前选择的节点更新表单数据（编辑模式下重新选择时使用）
 */
const updateFormFromSelectedNodes = (): void => {
  try {
    // 只在编辑模式下处理
    if (props.editMode !== "editing") {
      console.log("非编辑模式，跳过起点终点更新");
      return;
    }

    let updated = false;

    // 强制更新起始点信息（编辑模式下重新选择时）
    if (props.currentSelectedStart) {
      const startNode = props.currentSelectedStart;
      // 移除了 formData.qdbh !== startNode.nodeCode 的判断，强制更新
      formData.qdbh = startNode.nodeCode;
      formData.qdgc = startNode.nodeData?.dmgc || formData.qdgc;
      // 同时更新坐标信息
      formData.qdx = startNode.coordinates[0];
      formData.qdy = startNode.coordinates[1];
      updated = true;
      console.log("编辑模式：强制更新起始点信息", {
        qdbh: formData.qdbh,
        qdgc: formData.qdgc,
        coordinates: [formData.qdx, formData.qdy],
      });
    }

    // 强制更新终止点信息（编辑模式下重新选择时）
    if (props.currentSelectedEnd) {
      const endNode = props.currentSelectedEnd;
      // 移除了 formData.zdbh !== endNode.nodeCode 的判断，强制更新
      formData.zdbh = endNode.nodeCode;
      formData.zdgc = endNode.nodeData?.dmgc || formData.zdgc;
      // 同时更新坐标信息
      formData.zdx = endNode.coordinates[0];
      formData.zdy = endNode.coordinates[1];
      updated = true;
      console.log("编辑模式：强制更新终止点信息", {
        zdbh: formData.zdbh,
        zdgc: formData.zdgc,
        coordinates: [formData.zdx, formData.zdy],
      });
    }

    if (updated) {
      console.log("编辑模式：表单数据已强制更新", {
        qdbh: formData.qdbh,
        zdbh: formData.zdbh,
        qdgc: formData.qdgc,
        zdgc: formData.zdgc,
        startCoords: [formData.qdx, formData.qdy],
        endCoords: [formData.zdx, formData.zdy],
      });
    } else {
      console.log("编辑模式：当前没有选择的起点或终点需要更新");
    }
  } catch (error) {
    console.error("编辑模式更新表单数据失败:", error);
  }
};

/**
 * @function initializeFormData
 * @description 初始化表单数据
 */
const initializeFormData = async () => {
  // 优先使用传入的管线数据（编辑模式）
  if (props.lineData && !props.isNew) {
    console.log("使用传入的管线数据进行初始化");
    populateFormFromLineData(props.lineData);
    props.readonly && addTempLine();
    return;
  }

  // 通过API获取管线数据（编辑模式，通过GID）
  if (props.lineGid && !props.isNew) {
    try {
      console.log("通过API获取管线数据:", props.lineGid);
      const response = await pipeLineDetail(props.lineGid);
      if (response.code === 200 && response.data) {
        populateFormFromLineData(response.data);
      } else {
        throw new Error(response.msg || "获取管线数据失败");
      }
    } catch (error) {
      console.error("加载管线数据失败:", error);
      ElMessage.error(
        `加载管线数据失败: ${
          error instanceof Error ? error.message : "未知错误"
        }`
      );
    }
    return;
  }

  // 新建模式：使用临时数据
  if (props.isNew) {
    console.log("新建模式：使用临时数据初始化");
    populateFormFromTempData();
    return;
  }

  console.log("管线面板：等待数据初始化");
};

/**
 * @function selectStartNodeFromMap
 * @description 从地图选择起始点
 */
const selectStartNodeFromMap = () => {
  emit("selectStartNode");
};

/**
 * @function selectEndNodeFromMap
 * @description 从地图选择终止点
 */
const selectEndNodeFromMap = () => {
  emit("selectEndNode");
};

/**
 * @function handleSave
 * @description 保存管线数据
 */
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    // 表单验证
    await formRef.value.validate();

    saving.value = true;

    // 转换表单数据为API格式
    const apiData = convertFormDataToApiData(formData);

    console.log("准备保存管线数据:", apiData);

    let result;
    if (props.isNew) {
      // 新建管线
      result = await pipeLineAdd(apiData);
      console.log("新建管线成功:", result);
    } else {
      // 编辑管线
      result = await pipeLineEdit(apiData);
      console.log("编辑管线成功:", result);
    }

    // 触发保存成功事件
    emit("saved", {
      ...apiData,
      gid: result.data, // 新建时返回生成的ID
    });
  } catch (error: any) {
    console.error("保存管线失败:", error);

    // 处理不同类型的错误
    let errorMessage = "保存失败";
    if (error?.response?.data?.msg) {
      errorMessage = error.response.data.msg;
    } else if (error?.message) {
      errorMessage = error.message;
    } else if (typeof error === "string") {
      errorMessage = error;
    }

    ElMessage.error(`保存失败: ${errorMessage}`);
  } finally {
    saving.value = false;
  }
};

/**
 * @function handleDelete
 * @description 删除管线
 */
const handleDelete = async () => {
  // 获取管线ID，优先使用传入的数据中的gid
  const lineGid = formData.gid || props.lineGid;
  const lineCode =
    formData.gxbm || formData.qdbh + "-" + formData.zdbh || "未知";

  if (!lineGid) {
    ElMessage.error("无法获取管线ID，删除失败");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除管线 "${lineCode}" 吗？此操作不可撤销。`,
      "确认删除",
      {
        confirmButtonText: "确定删除",
        cancelButtonText: "取消",
        type: "warning",
        confirmButtonClass: "el-button--danger",
      }
    );

    deleting.value = true;

    console.log("🗑️ 开始删除管线:", lineGid, lineCode);

    // 调用删除API
    const apiResponse = await pipeLineDelete(lineGid);

    if (apiResponse && apiResponse.code === 200) {
      console.log("✅ 管线删除成功");
      ElMessage.success(`管线 "${lineCode}" 删除成功`);

      // 触发删除成功事件
      emit("deleted", lineGid.toString());
    } else {
      throw new Error(apiResponse?.msg || "删除失败");
    }
  } catch (error: any) {
    if (error.message !== "cancel") {
      // 用户取消删除时不显示错误
      console.error("❌ 删除管线失败:", error);
      ElMessage.error(`删除失败: ${error.message || "未知错误"}`);
    }
  } finally {
    deleting.value = false;
  }
};

/**
 * @function handleChangePosition
 * @description 修改管线位置（重新绘制）
 */
const handleChangePosition = () => {
  emit("changePosition");
};

/**
 * @function handleCancel
 * @description 取消编辑
 */
const handleCancel = () => {
  emit("close");
};

/**
 * @function handleClose
 * @description 关闭面板
 */
const handleClose = () => {
  emit("close");
  useDialogStore().closeDialog("PipeLinePanel");
};

// 监听管线数据变化，实时更新表单
watch(
  () => props.lineData,
  (newLineData) => {
    if (newLineData && !props.isNew) {
      console.log("管线数据发生变化，重新填充表单");
      populateFormFromLineData(newLineData);
    }
  },
  { immediate: false, deep: true }
);

// 监听临时数据变化，用于新建模式
watch(
  [() => props.startPointInfo, () => props.endPointInfo],
  () => {
    if (props.isNew && props.startPointInfo && props.endPointInfo) {
      console.log("临时数据发生变化，重新填充表单");
      populateFormFromTempData();
    }
  },
  { immediate: false }
);

// 监听编辑模式下当前选择的节点变化
watch(
  [
    () => props.currentSelectedStart,
    () => props.currentSelectedEnd,
    () => props.editMode,
  ],
  () => {
    if (props.editMode === "editing") {
      console.log("编辑模式：选择的节点发生变化，立即更新表单数据", {
        hasStartNode: !!props.currentSelectedStart,
        hasEndNode: !!props.currentSelectedEnd,
        startNodeCode: props.currentSelectedStart?.nodeCode,
        endNodeCode: props.currentSelectedEnd?.nodeCode,
      });
      // 使用nextTick确保在DOM更新后执行
      nextTick(() => {
        updateFormFromSelectedNodes();
      });
    }
  },
  { immediate: true, deep: true }
);

// 监听埋深和地面高程变化，自动计算管底高程
watch([() => formData.qdgc, () => formData.qdms], () => {
  if (formData.qdgc && formData.qdms) {
    // 可以在这里添加管底高程计算逻辑
  }
});

watch(
  props,
  (newVal) => {
    console.log(newVal);
    initializeFormData();
  },
  { deep: true }
);

let map: MapLibreMap | null = null;

// 临时图层管理
const TEMP_NODE_LAYER_ID = "temp-pipe-line";
const TEMP_NODE_SOURCE_ID = "temp-pipe-line-source";
/**
 * @function clearTempNodes
 * @description 清空所有临时管点
 */
const clearTempNodes = (): void => {
  if (!map) return;

  try {
    const source = map.getSource(TEMP_NODE_SOURCE_ID) as any;
    if (source) {
      source.setData({
        type: "FeatureCollection",
        features: [],
      });
    }
    console.log("临时管点已清空");
  } catch (error) {
    console.error("清空临时管点失败:", error);
  }
};

/**
 * @function addTempNode
 * @description 添加临时管点到图层
 */
const addTempLine = (): void => {
  if (!map) {
    console.error("addTempNode: 地图实例不存在");
    return;
  }

  try {
    // 检查并确保临时图层存在
    if (!map.getSource(TEMP_NODE_SOURCE_ID)) {
      console.log("addTempNode: 临时图层数据源不存在，重新初始化");
      initializeTempLayer();
    }

    // 再次检查数据源
    const source = map.getSource(TEMP_NODE_SOURCE_ID) as any;
    if (!source) {
      console.error("addTempNode: 无法获取临时图层数据源");
      return;
    }

    const feature = {
      type: "Feature",
      properties: {
        id: `temp_${Date.now()}`,
        type: "temp-line",
      },
      geometry: JSON.parse(props.lineData.geojson),
    };

    source.setData({
      type: "FeatureCollection",
      features: [feature],
    });

    // 确保临时图层在最顶层
    ensureTempLayerOnTop();
  } catch (error) {
    console.error("添加临时管点失败:", error);
    // 如果添加失败，尝试重新初始化图层
    try {
      console.log("尝试重新初始化临时图层...");
      initializeTempLayer();
      // 重新尝试添加
      const source = map.getSource(TEMP_NODE_SOURCE_ID) as any;
      if (source) {
        const feature = {
          type: "Feature",
          properties: {
            id: `temp_${Date.now()}`,
            type: "temp-node",
          },
          geometry: JSON.parse(props.lineData.geojson),
        };

        source.setData({
          type: "FeatureCollection",
          features: [feature],
        });

        // 确保临时图层在最顶层
        ensureTempLayerOnTop();
      }
    } catch (retryError) {
      console.error("重新初始化临时图层失败:", retryError);
    }
  }
};
/**
 * @function initializeTempLayer
 * @description 初始化临时图层
 */
const initializeTempLayer = (): void => {
  if (!map) {
    console.error("initializeTempLayer: 地图实例不存在");
    return;
  }

  try {
    console.log("initializeTempLayer: 开始初始化临时图层");

    // 检查数据源是否已存在
    const existingSource = map.getSource(TEMP_NODE_SOURCE_ID);
    if (!existingSource) {
      console.log("initializeTempLayer: 添加临时图层数据源");
      map.addSource(TEMP_NODE_SOURCE_ID, {
        type: "geojson",
        data: {
          type: "FeatureCollection",
          features: [],
        },
      });
    } else {
      console.log("initializeTempLayer: 临时图层数据源已存在");
    }

    // 检查图层是否已存在
    const existingLayer = map.getLayer(TEMP_NODE_LAYER_ID);
    if (!existingLayer) {
      console.log("initializeTempLayer: 添加临时图层");
      map.addLayer({
        id: TEMP_NODE_LAYER_ID,
        type: "line",
        source: TEMP_NODE_SOURCE_ID,
        layout: {
          "line-join": "round",
          "line-cap": "round",
        },
        paint: {
          "line-color": "#ff6b35",
          "line-width": 3,
        },
      });
    } else {
      console.log("initializeTempLayer: 临时图层已存在");
    }

    // 确保临时图层在最顶层
    ensureTempLayerOnTop();

    console.log("initializeTempLayer: 临时图层初始化成功");
  } catch (error) {
    console.error("initializeTempLayer: 初始化失败", error);
  }
};

// ============ 地图交互方法 ============

/**
 * @function initializeMap
 * @description 初始化地图实例
 */
const initializeMap = async (): Promise<void> => {
  try {
    console.log("initializeMap: 开始初始化地图");
    map = AppMaplibre.getMap();

    if (map) {
      console.log("initializeMap: 地图实例获取成功");
      await nextTick(); // 确保DOM更新完成

      // 初始化临时图层
      console.log("initializeMap: 准备初始化临时图层");
      initializeTempLayer();
      console.log("initializeMap: 地图初始化完成");
    } else {
      console.error("initializeMap: 地图实例未准备就绪");
    }
  } catch (error) {
    console.error("initializeMap: 初始化失败", error);
  }
};

/**
 * @function ensureTempLayerOnTop
 * @description 确保临时图层在所有图层的最顶层
 */
const ensureTempLayerOnTop = (): void => {
  if (!map) {
    console.error("ensureTempLayerOnTop: 地图实例不存在");
    return;
  }

  try {
    // 检查临时图层是否存在
    if (!map.getLayer(TEMP_NODE_LAYER_ID)) {
      console.log("ensureTempLayerOnTop: 临时图层不存在，无需调整");
      return;
    }

    // 将临时图层移动到最顶层
    map.moveLayer(TEMP_NODE_LAYER_ID);
    console.log("ensureTempLayerOnTop: 临时图层已移动到最顶层");
  } catch (error) {
    console.error("ensureTempLayerOnTop: 调整图层顺序失败", error);
  }
};

// 组件挂载时初始化数据
onMounted(async () => {
  await initializeMap();
  initializeFormData();
});

onUnmounted(() => {
  clearTempNodes();
});
</script>

<style lang="scss" scoped>
.pipe-line-panel {
  // padding: 20px;
  max-height: 700px;
  overflow-y: auto;

  .pipe-form {
    .el-form-item {
      margin-bottom: 16px;

      :deep(.el-form-item__label) {
        color: black;
        font-weight: 500;
      }

      :deep(.el-input__inner) {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid #abdeff;
        color: black;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }

      :deep(.el-textarea__inner) {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid #abdeff;
        color: #fff;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
    }

    // .node-select-container {
    //   display: flex;
    //   gap: 8px;
    //   align-items: center;

    //   .node-select {
    //     flex: 1;
    //   }

    //   .el-button {
    //     flex-shrink: 0;
    //   }
    // }

    .length-container {
      display: flex;
      gap: 8px;
      align-items: center;

      .length-input {
        flex: 1;
      }

      .el-button {
        flex-shrink: 0;
        min-width: 60px;
      }
    }
  }
}

// 自定义滚动条样式
.pipe-line-panel::-webkit-scrollbar {
  width: 6px;
}

.pipe-line-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.pipe-line-panel::-webkit-scrollbar-thumb {
  background: rgba(171, 222, 255, 0.5);
  border-radius: 3px;

  &:hover {
    background: rgba(171, 222, 255, 0.8);
  }
}
</style>
