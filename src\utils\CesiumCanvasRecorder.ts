/**
 * @description Cesium Canvas录制服务
 * 专门用于录制Cesium三维场景的Canvas内容
 */

import { AppCesium } from '@/lib/cesium/AppCesium';
import { 
  RecordState
} from '@/types/recording';
import type { 
  CanvasRecordConfig, 
  RecordCallbacks, 
  RecordResult 
} from '@/types/recording';

/**
 * @description Cesium Canvas录制器类
 */
export class CesiumCanvasRecorder {
  private static instance: CesiumCanvasRecorder | null = null;
  
  // 录制相关
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];
  private canvasStream: MediaStream | null = null;
  private state: RecordState = RecordState.IDLE;
  private startTime: number = 0;
  private progressTimer: number = 0;
  
  // 配置和回调
  private config: Required<CanvasRecordConfig> = {
    frameRate: 30,
    videoBitsPerSecond: 8000000, // 8Mbps
    mimeType: 'video/webm;codecs=vp9',
    width: 1920,
    height: 1080,
    fileName: '地图漫游录制'
  };
  
  private callbacks: RecordCallbacks = {};
  
  // Cesium相关
  private cesiumCanvas: HTMLCanvasElement | null = null;
  private viewer: BC.Viewer | null = null;

  /**
   * @description 获取单例实例
   */
  public static getInstance(): CesiumCanvasRecorder {
    if (!CesiumCanvasRecorder.instance) {
      CesiumCanvasRecorder.instance = new CesiumCanvasRecorder();
    }
    return CesiumCanvasRecorder.instance;
  }

  /**
   * @description 初始化录制器
   */
  public async initialize(config?: Partial<CanvasRecordConfig>): Promise<boolean> {
    try {
      // 合并配置
      if (config) {
        this.config = { ...this.config, ...config };
      }

      // 获取Cesium Viewer和Canvas
      if (!this.getCesiumCanvas()) {
        throw new Error('无法获取Cesium Canvas元素');
      }

      // 检查浏览器支持
      if (!this.checkBrowserSupport()) {
        throw new Error('浏览器不支持Canvas录制功能');
      }

      console.log('🎨 Cesium Canvas录制器初始化成功');
      return true;
    } catch (error) {
      console.error('Cesium Canvas录制器初始化失败:', error);
      this.handleError(`初始化失败: ${error}`);
      return false;
    }
  }

  /**
   * @description 获取Cesium Canvas元素
   */
  private getCesiumCanvas(): boolean {
    try {
      this.viewer = AppCesium.getInstance().getViewer();
      this.cesiumCanvas = this.viewer.scene.canvas;
      
      if (!this.cesiumCanvas) {
        console.error('无法获取Cesium Canvas元素');
        return false;
      }

      console.log('✅ 成功获取Cesium Canvas:', {
        width: this.cesiumCanvas.width,
        height: this.cesiumCanvas.height,
        preserveDrawingBuffer: this.cesiumCanvas.getContext('webgl')?.getContextAttributes()?.preserveDrawingBuffer
      });

      return true;
    } catch (error) {
      console.error('获取Cesium Canvas失败:', error);
      return false;
    }
  }

  /**
   * @description 检查浏览器支持
   */
  private checkBrowserSupport(): boolean {
    // 检查Canvas captureStream支持
    if (!this.cesiumCanvas || typeof this.cesiumCanvas.captureStream !== 'function') {
      console.error('浏览器不支持Canvas.captureStream');
      return false;
    }

    // 检查MediaRecorder支持
    if (typeof MediaRecorder === 'undefined') {
      console.error('浏览器不支持MediaRecorder');
      return false;
    }

    // 检查支持的MIME类型 - 优先MP4格式
    const supportedTypes = [
      'video/mp4',
      'video/mp4;codecs=avc1.42E01E',
      'video/webm;codecs=vp9',
      'video/webm;codecs=vp8',
      'video/webm'
    ];

    for (const type of supportedTypes) {
      if (MediaRecorder.isTypeSupported(type)) {
        this.config.mimeType = type;
        console.log(`✅ 使用视频格式: ${type}`);
        break;
      }
    }

    return true;
  }

  /**
   * @description 开始录制
   */
  public async startRecording(): Promise<boolean> {
    if (this.state === RecordState.RECORDING) {
      console.warn('录制已在进行中');
      return false;
    }

    try {
      this.setState(RecordState.READY);

      // 获取Canvas流
      if (!this.setupCanvasStream()) {
        throw new Error('无法创建Canvas流');
      }

      // 创建MediaRecorder
      this.setupMediaRecorder();

      // 开始录制
      this.mediaRecorder!.start();
      this.startTime = Date.now();
      this.setState(RecordState.RECORDING);

      // 开始进度计时器
      this.startProgressTimer();

      console.log('🎥 开始录制Cesium Canvas');
      this.callbacks.onStart?.();

      return true;
    } catch (error) {
      console.error('开始录制失败:', error);
      this.handleError(`开始录制失败: ${error}`);
      this.cleanup();
      return false;
    }
  }

  /**
   * @description 设置Canvas流
   */
  private setupCanvasStream(): boolean {
    if (!this.cesiumCanvas) {
      console.error('Cesium Canvas未初始化');
      return false;
    }

    try {
      // 创建Canvas流
      this.canvasStream = this.cesiumCanvas.captureStream(this.config.frameRate);
      
      if (!this.canvasStream) {
        throw new Error('Canvas captureStream返回null');
      }

      console.log('✅ Canvas流创建成功:', {
        tracks: this.canvasStream.getTracks().length,
        frameRate: this.config.frameRate
      });

      return true;
    } catch (error) {
      console.error('创建Canvas流失败:', error);
      return false;
    }
  }

  /**
   * @description 设置MediaRecorder
   */
  private setupMediaRecorder(): void {
    if (!this.canvasStream) {
      throw new Error('Canvas流未初始化');
    }

    this.recordedChunks = [];

    // MediaRecorder选项
    const options: MediaRecorderOptions = {
      mimeType: this.config.mimeType,
      videoBitsPerSecond: this.config.videoBitsPerSecond
    };

    this.mediaRecorder = new MediaRecorder(this.canvasStream, options);

    // 数据可用事件
    this.mediaRecorder.addEventListener('dataavailable', (event) => {
      if (event.data && event.data.size > 0) {
        this.recordedChunks.push(event.data);
        console.log(`📊 录制数据块: ${event.data.size} bytes`);
      }
    });

    // 录制停止事件
    this.mediaRecorder.addEventListener('stop', () => {
      this.handleRecordingComplete();
    });

    // 错误事件
    this.mediaRecorder.addEventListener('error', (event: any) => {
      console.error('MediaRecorder错误:', event);
      this.handleError('录制过程中发生错误');
    });

    console.log('✅ MediaRecorder设置完成:', options);
  }

  /**
   * @description 开始进度计时器
   */
  private startProgressTimer(): void {
    this.progressTimer = window.setInterval(() => {
      if (this.state === RecordState.RECORDING) {
        const duration = Math.floor((Date.now() - this.startTime) / 1000);
        this.callbacks.onProgress?.(duration);
      }
    }, 1000);
  }

  /**
   * @description 停止录制
   */
  public async stopRecording(): Promise<RecordResult> {
    if (this.state !== RecordState.RECORDING) {
      return {
        success: false,
        error: '当前没有在录制'
      };
    }

    try {
      this.setState(RecordState.STOPPING);

      // 停止MediaRecorder
      if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
        this.mediaRecorder.stop();
      }

      // 停止进度计时器
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
        this.progressTimer = 0;
      }

      console.log('🎥 停止录制Cesium Canvas');
      return { success: true };
    } catch (error) {
      console.error('停止录制失败:', error);
      this.handleError(`停止录制失败: ${error}`);
      return {
        success: false,
        error: `停止录制失败: ${error}`
      };
    }
  }

  /**
   * @description 处理录制完成
   */
  private handleRecordingComplete(): void {
    try {
      if (this.recordedChunks.length === 0) {
        throw new Error('没有录制到数据');
      }

      // 创建视频文件
      const blob = new Blob(this.recordedChunks, {
        type: this.config.mimeType
      });

      // 生成文件名
      const fileName = this.generateFileName();

      // 下载文件
      this.downloadVideo(blob, fileName);

      const duration = Math.floor((Date.now() - this.startTime) / 1000);
      
      // 回调通知
      this.callbacks.onStop?.(blob, fileName);

      console.log(`🎥 录制完成: ${fileName}, 文件大小: ${(blob.size / 1024 / 1024).toFixed(2)}MB, 时长: ${duration}s`);

      // 清理资源
      this.cleanup();
      this.setState(RecordState.IDLE);

    } catch (error) {
      console.error('处理录制完成失败:', error);
      this.handleError('生成录制文件失败');
    }
  }

  /**
   * @description 生成文件名
   */
  private generateFileName(): string {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 19).replace(/:/g, '-');
    const duration = Math.floor((Date.now() - this.startTime) / 1000);
    
    // 根据MIME类型确定文件扩展名
    let extension = 'mp4'; // 默认MP4
    if (this.config.mimeType.includes('webm')) {
      extension = 'webm';
    } else if (this.config.mimeType.includes('mp4')) {
      extension = 'mp4';
    }
    
    return `${this.config.fileName}_${dateStr}_${duration}s.${extension}`;
  }

  /**
   * @description 下载视频文件
   */
  private downloadVideo(blob: Blob, fileName: string): void {
    try {
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.style.display = 'none';

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      // 释放URL对象
      setTimeout(() => URL.revokeObjectURL(url), 1000);

      console.log(`📁 视频文件已下载: ${fileName}`);
    } catch (error) {
      console.error('下载视频失败:', error);
      this.handleError('下载录制文件失败');
    }
  }

  /**
   * @description 设置状态
   */
  private setState(newState: RecordState): void {
    if (this.state !== newState) {
      this.state = newState;
      this.callbacks.onStateChange?.(newState);
      console.log(`🎥 录制状态变更: ${newState}`);
    }
  }

  /**
   * @description 处理错误
   */
  private handleError(message: string): void {
    this.setState(RecordState.ERROR);
    this.callbacks.onError?.(message);
    console.error('录制错误:', message);
  }

  /**
   * @description 清理资源
   */
  private cleanup(): void {
    if (this.canvasStream) {
      this.canvasStream.getTracks().forEach(track => track.stop());
      this.canvasStream = null;
    }

    if (this.mediaRecorder) {
      this.mediaRecorder = null;
    }

    if (this.progressTimer) {
      clearInterval(this.progressTimer);
      this.progressTimer = 0;
    }

    this.recordedChunks = [];
  }

  /**
   * @description 设置配置
   */
  public setConfig(config: Partial<CanvasRecordConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * @description 设置回调
   */
  public setCallbacks(callbacks: RecordCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * @description 获取当前状态
   */
  public getState(): RecordState {
    return this.state;
  }

  /**
   * @description 是否正在录制
   */
  public isRecording(): boolean {
    return this.state === RecordState.RECORDING;
  }

  /**
   * @description 获取录制时长（秒）
   */
  public getRecordingDuration(): number {
    if (this.state === RecordState.RECORDING && this.startTime > 0) {
      return Math.floor((Date.now() - this.startTime) / 1000);
    }
    return 0;
  }

  /**
   * @description 获取Cesium Canvas信息
   */
  public getCanvasInfo(): any {
    if (!this.cesiumCanvas) {
      return null;
    }

    return {
      width: this.cesiumCanvas.width,
      height: this.cesiumCanvas.height,
      clientWidth: this.cesiumCanvas.clientWidth,
      clientHeight: this.cesiumCanvas.clientHeight,
      preserveDrawingBuffer: this.cesiumCanvas.getContext('webgl')?.getContextAttributes()?.preserveDrawingBuffer
    };
  }

  /**
   * @description 销毁录制器
   */
  public destroy(): void {
    this.stopRecording();
    this.cleanup();
    this.setState(RecordState.IDLE);
    this.cesiumCanvas = null;
    this.viewer = null;
    CesiumCanvasRecorder.instance = null;
  }
} 