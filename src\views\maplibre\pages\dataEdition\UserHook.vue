<template>
  <page-card class="user-hook-panel" title="用户挂接" @closeCard="closeCard">
    <!-- 主要内容区域 -->
    <div class="panel-main">
      <!-- 表头 -->
      <div class="table-header">
        <div class="header-cell user-name">用户名</div>
        <div class="header-cell user-attr">用户号</div>
        <div class="header-cell user-attr">联系电话</div>
        <div class="header-cell water-meter">水表编号</div>
        <div class="header-cell actions">操作</div>
      </div>

      <!-- 可滚动的挂接列表容器 -->
      <div class="scrollable-container">
        <div class="hook-list">
          <div v-for="(item, index) in hookList" :key="item.id" class="hook-item">
            <!-- 用水户名称搜索框 -->
            <div class="item-cell user-name">
              <el-autocomplete
                v-model="item.userName"
                :fetch-suggestions="queryWaterUsers"
                placeholder="请输入用户名搜索"
                clearable
                @select="(selection) => handleUserSelect(index, selection)"
                class="user-search"
              >
                <template #default="{ item: suggestion }">
                  <div class="suggestion-item">
                    <div class="suggestion-name">{{ suggestion.userName }}</div>
                  </div>
                </template>
              </el-autocomplete>
            </div>

            <!-- 用水户属性显示 -->
            <div class="item-cell user-attr">{{ item.userNo || '-' }}</div>
            <div class="item-cell user-attr">{{ item.contactsPhone || '-' }}</div>

            <!-- 水表编号选择 -->
            <div class="item-cell water-meter">
              <el-input
                v-model="item.waterMeterCode"
                placeholder="请点击水表管点"
                readonly
                @click="selectWaterMeter(index)"
                class="water-meter-input"
              >
              </el-input>
            </div>

            <!-- 操作按钮 -->
            <div class="item-cell actions">
              <el-button
                type="text"
                size="small"
                @click="locateItem(item)"
                :disabled="!item.longitude || !item.latitude"
              >
                定位
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="deleteItem(index)"
                class="delete-btn"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加按钮 -->
      <div class="add-section">
        <el-button
          type="primary"
          @click="addHookItem"
          class="add-btn"
        >
          + 添加挂接
        </el-button>
      </div>
    </div>

    <!-- 固定在底部的操作按钮 -->
    <div class="footer-actions">
      <el-button type="primary" @click="handleConfirm" :loading="submitting">
        确定
      </el-button>
      <el-button @click="closeCard">取消</el-button>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Location } from '@element-plus/icons-vue';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import {
  getWaterUserList,
  batchAttachWaterUser,
  type WaterUserVo,
  type WaterUserPtDto,
} from '@/api/userHook';
// 移除未使用的Plus导入
/**
 * @interface HookItem
 * @description 挂接项接口
 */
interface HookItem {
  id: string;
  userName: string;
  userType: string;
  contactsPhone: string;
  waterAddress: string;
  waterMeterCode: string;
  waterUserId: string;
  userNo: string;
  ptId: number;
  longitude?: number;
  latitude?: number;
}

const emit = defineEmits<{
  close: [];
  updateHook: []
}>();

// ============ 响应式数据 ============

/** 挂接列表 */
const hookList = ref<HookItem[]>([]);

/** 提交状态 */
const submitting = ref(false);

/** 当前选择水表的索引 */
const selectingWaterMeterIndex = ref<number>(-1);

/** 地图点击事件处理器 */
let mapClickHandler: ((e: any) => void) | null = null;

// ============ 初始化 ============

/**
 * @function initHookList
 * @description 初始化挂接列表
 */
const initHookList = (): void => {
  hookList.value = [createEmptyHookItem()];
};

/**
 * @function createEmptyHookItem
 * @description 创建空的挂接项
 */
const createEmptyHookItem = (): HookItem => {
  return {
    id: `hook_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    userName: '',
    userType: '',
    contactsPhone: '',
    waterAddress: '',
    waterMeterCode: '',
    userNo: '',
    waterUserId: '',
    ptId: 0,
  };
};

// ============ 用水户搜索相关 ============

/** 默认用水户列表缓存 */
const defaultUserList = ref<any[]>([]);

/**
 * @function loadDefaultUserList
 * @description 加载默认用水户列表
 */
const loadDefaultUserList = async (): Promise<void> => {
  try {
    const response = await getWaterUserList({});

    if (response.code === 200 && response.data) {
      defaultUserList.value = response.data.map((user: WaterUserVo) => ({
        value: user.userName,
        userName: user.userName,
        userType: user.userType,
        userNo: user.userNo,
        contactsPhone: user.contactsPhone,
        waterAddress: user.waterAddress,
        id: user.id,
      }));
    }
  } catch (error) {
    console.error('加载默认用水户列表失败:', error);
  }
};

/**
 * @function queryWaterUsers
 * @description 查询用水户建议列表
 */
const queryWaterUsers = async (
  queryString: string,
  callback: (suggestions: any[]) => void
): Promise<void> => {
  try {
    // 如果没有输入内容，返回默认列表（限制前20条）
    if (!queryString || queryString.trim() === '') {
      callback(defaultUserList.value.slice(0, 20));
      return;
    }

    const response = await getWaterUserList({
      userName: queryString,
    });

    if (response.code === 200 && response.data) {
      const suggestions = response.data.map((user: WaterUserVo) => ({
        value: user.userName,
        userName: user.userName,
        userType: user.userType,
        userNo: user.userNo,
        contactsPhone: user.contactsPhone,
        waterAddress: user.waterAddress,
        id: user.id,
      }));
      callback(suggestions);
    } else {
      callback([]);
    }
  } catch (error) {
    console.error('查询用水户失败:', error);
    callback([]);
  }
};

/**
 * @function handleUserSelect
 * @description 处理用水户选择
 */
const handleUserSelect = (index: number, selection: any): void => {
  const item = hookList.value[index];
  if (item && selection) {
    item.userName = selection.userName;
    item.userType = selection.userType;
    item.userNo = selection.userNo;
    item.contactsPhone = selection.contactsPhone;
    item.waterAddress = selection.waterAddress;
    item.waterUserId = selection.id;
  }
};

// ============ 水表选择相关 ============

/**
 * @function selectWaterMeter
 * @description 选择水表（地图点击）
 */
const selectWaterMeter = (index: number): void => {
  try {
    selectingWaterMeterIndex.value = index;

    const map = AppMaplibre.getMap();
    if (!map) {
      ElMessage.error('无法获取地图实例');
      return;
    }

    // 修改鼠标样式
    map.getCanvas().style.cursor = 'crosshair';

    ElMessage.info('请在地图上点击选择水表管点');

    // 绑定地图点击事件
    mapClickHandler = handleMapClickForWaterMeter;
    map.once('click', mapClickHandler);
  } catch (error) {
    console.error('开始选择水表失败:', error);
    ElMessage.error('开始选择水表失败');
  }
};

/**
 * @function handleMapClickForWaterMeter
 * @description 处理地图点击选择水表
 */
const handleMapClickForWaterMeter = (e: any): void => {
  try {
    const map = AppMaplibre.getMap();

    // 恢复鼠标样式
    map.getCanvas().style.cursor = '';

    // 移除点击事件监听
    if (mapClickHandler) {
      map.off('click', mapClickHandler);
      mapClickHandler = null;
    }

    const offset = 10;
    // 查询点击位置的管点要素
    const features = map.queryRenderedFeatures(
      [
        [e.point.x - offset / 2, e.point.y - offset / 2],
        [e.point.x + offset / 2, e.point.y + offset / 2],
      ],
      {
        layers: ['mvt_pipeNode'],
      }
    );
    if (features.length === 0) {
      ElMessage.warning('请点击水表类型的管点');
      return;
    }
    const waterFeature = features.find((feature: any) => feature.properties.fsw === '水表');
    if (!waterFeature) {
      ElMessage.warning('请选择水表类型的管点');
      return;
    }
    const properties = waterFeature.properties;

    // 检查是否为水表
    if (properties.fsw !== '水表') {
      ElMessage.warning('请选择水表类型的管点');
      return;
    }

    // 更新选中的挂接项
    const index = selectingWaterMeterIndex.value;
    if (index >= 0 && index < hookList.value.length) {
      const item = hookList.value[index];
      item.waterMeterCode = properties.bm || properties.gxddh || '未知编号';
      item.ptId = properties.gid || properties.id;

      // 安全地获取坐标
      const geometry = waterFeature.geometry as any;
      if (geometry && geometry.coordinates) {
        item.longitude = geometry.coordinates[0];
        item.latitude = geometry.coordinates[1];
      }

      ElMessage.success(`已选择水表：${item.waterMeterCode}`);
    }

    selectingWaterMeterIndex.value = -1;
  } catch (error) {
    console.error('选择水表失败:', error);
    ElMessage.error('选择水表失败');

    // 清理状态
    const map = AppMaplibre.getMap();
    if (map) {
      map.getCanvas().style.cursor = '';
      if (mapClickHandler) {
        map.off('click', mapClickHandler);
        mapClickHandler = null;
      }
    }
    selectingWaterMeterIndex.value = -1;
  }
};

// ============ 列表操作 ============

/**
 * @function addHookItem
 * @description 添加挂接项
 */
const addHookItem = (): void => {
  hookList.value.push(createEmptyHookItem());
};

/**
 * @function deleteItem
 * @description 删除挂接项
 */
const deleteItem = (index: number): void => {
  if (hookList.value.length <= 1) {
    ElMessage.warning('至少保留一行数据');
    return;
  }
  hookList.value.splice(index, 1);
};

/**
 * @function locateItem
 * @description 定位到挂接项
 */
const locateItem = (item: HookItem): void => {
  try {
    if (!item.longitude || !item.latitude) {
      ElMessage.warning('该项目暂无位置信息');
      return;
    }

    const map = AppMaplibre.getMap();
    map.flyTo({
      center: [item.longitude, item.latitude],
      zoom: 18,
      duration: 2000,
    });

    ElMessage.success(`已定位到 ${item.userName || item.waterMeterCode}`);
  } catch (error) {
    console.error('定位失败:', error);
    ElMessage.error('定位失败');
  }
};

// ============ 表单提交 ============

/**
 * @function validateHookList
 * @description 验证挂接列表
 */
const validateHookList = (): boolean => {
  for (let i = 0; i < hookList.value.length; i++) {
    const item = hookList.value[i];

    if (!item.userName || !item.waterUserId) {
      ElMessage.error(`第${i + 1}行：请选择用水户`);
      return false;
    }

    if (!item.waterMeterCode || !item.ptId) {
      ElMessage.error(`第${i + 1}行：请选择水表`);
      return false;
    }
  }

  return true;
};

/**
 * @function handleConfirm
 * @description 确认提交挂接
 */
const handleConfirm = async (): Promise<void> => {
  try {
    if (!validateHookList()) {
      return;
    }

    await ElMessageBox.confirm(
      `确定要提交 ${hookList.value.length} 个用户挂接关系吗？`,
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    submitting.value = true;

    // 构建提交数据
    const submitData: WaterUserPtDto[] = hookList.value.map((item) => ({
      waterUserid: item.waterUserId,
      ptId: item.ptId,
    }));

    // 调用批量挂接API
    const response = await batchAttachWaterUser(submitData);

    if (response.code === 200) {
      ElMessage.success('用户挂接成功');
      emit('updateHook')
      closeCard();
    } else {
      ElMessage.error(response.msg || '挂接失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交挂接失败:', error);
      ElMessage.error('提交挂接失败');
    }
  } finally {
    submitting.value = false;
  }
};

/**
 * @function closeCard
 * @description 关闭面板
 */
const closeCard = (): void => {
  try {
    // 清理地图事件监听
    const map = AppMaplibre.getMap();
    if (map && mapClickHandler) {
      map.off('click', mapClickHandler);
      map.getCanvas().style.cursor = '';
      mapClickHandler = null;
    }

    // 通知父组件关闭面板
    emit('close');
  } catch (error) {
    console.error('关闭面板失败:', error);
    ElMessage.error('关闭面板失败');
  }
};

// ============ 生命周期 ============

onMounted(async () => {
  initHookList();
  // 加载默认用水户列表
  await loadDefaultUserList();
});

onUnmounted(() => {
  // 清理地图事件监听
  const map = AppMaplibre.getMap();
  if (map && mapClickHandler) {
    map.off('click', mapClickHandler);
    map.getCanvas().style.cursor = '';
  }
});
</script>

<style scoped lang="scss">
.user-hook-panel {
  width: 800px;
  // height: 600px; // 固定高度
  position: absolute;
  top: 50px;
  left: 10px;
  z-index: 1000;

  :deep(.page-card-content) {
    padding: 16px;
    display: flex;
    // height: 600px;
    flex-direction: column;
    height: calc(100% - 60px); // 减去标题栏高度
    overflow: hidden;
  }
}

// 主要内容区域
.panel-main {
  flex: 1;
  display: flex;
  height: 400px;
  flex-direction: column;
  overflow: hidden;
}

// 可滚动容器
.scrollable-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px solid #e4e7ed;
  border-top: none;
  border-radius: 0 0 4px 4px;
  background-color: #fff;
  max-height: 350px; // 最大高度限制

  // 隐藏滚动条但保持滚动功能
  scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // IE 和 Edge

  &::-webkit-scrollbar {
    display: none; // Chrome, Safari, Opera
  }

  // 确保内容不会因为隐藏的滚动条而错位
  .hook-list {
    margin-right: 0;
    padding-right: 0;
  }
}

// 表格容器样式 - 使用table布局确保对齐
.table-header,
.hook-list {
  display: table;
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed; // 固定表格布局，确保列宽一致
}

// 表头样式
.table-header {
  background-color: #f2f6fc;
  border: 1px solid #e4e7ed;
  border-radius: 4px 4px 0 0;
  font-weight: 500;
  color: #606266;
  font-size: 13px;

  .header-cell {
    display: table-cell;
    padding: 12px 8px;
    border-right: 1px solid #e4e7ed;
    text-align: center;
    vertical-align: middle;
    box-sizing: border-box;

    &:last-child {
      border-right: none;
    }

    &.user-name {
      width: 25%;
    }

    &.user-attr {
      width: 15%;
    }

    &.water-meter {
      width: 22.5%;
    }

    &.actions {
      width: 15%;
    }
  }
}

// 挂接列表样式
.hook-list {
  border: 1px solid #e4e7ed;
  border-top: none;
  // border-radius: 0 0 4px 4px;
  overflow: hidden;
}

.hook-item {
  display: table-row;
  border-bottom: 1px solid #e4e7ed;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f7fa;
  }

  .item-cell {
    display: table-cell;
    padding: 8px;
    border-right: 1px solid #e4e7ed;
    vertical-align: middle;
    box-sizing: border-box;

    &:last-child {
      border-right: none;
    }

    &.user-name {
      width: 25%;
    }

    &.user-attr {
      width: 15%;
      font-size: 12px;
      color: #606266;
    }

    &.water-meter {
      width: 22.5%;
    }

    &.actions {
      width: 15%;
      text-align: center;

      .el-button {
        margin: 0 2px;
        padding: 2px 6px;
        font-size: 12px;
        min-width: auto;
      }
    }
  }
}

// 用水户搜索框样式
.user-search {
  width: 100%;

  :deep(.el-input__inner) {
    font-size: 12px;
  }
}

// 建议项样式
.suggestion-item {
  .suggestion-name {
    font-size: 13px;
    color: #303133;
  }

  .suggestion-info {
    font-size: 11px;
    color: #909399;
    margin-top: 2px;
  }
}

// 水表输入框样式
.water-meter-input {
  width: 100%;
  cursor: pointer;

  :deep(.el-input__inner) {
    cursor: pointer;
    font-size: 12px;
  }

  :deep(.el-input__suffix) {
    cursor: pointer;
  }
}

// 添加按钮区域
.add-section {
  padding: 16px;
  text-align: center;
  border-top: 1px solid #e4e7ed;
  background-color: #f8f9fa;
  flex-shrink: 0; // 防止被压缩

  .add-btn {
    // min-width: 120px;
    height: 30px;
    font-size: 14px;
  }
}

// 底部操作按钮 - 固定在底部
.footer-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background-color: #fff;
  flex-shrink: 0; // 防止被压缩
  margin-top: auto; // 推到底部

  .el-button {
    min-width: 80px;
  }
}

// 操作按钮样式
.delete-btn {
  color: #f56c6c !important;

  &:hover {
    background-color: #fef0f0 !important;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .user-hook-panel {
    width: 700px;
  }
}

@media (max-width: 1000px) {
  .user-hook-panel {
    width: 600px;
  }

  .table-header,
  .hook-item {
    .header-cell,
    .item-cell {
      &.user-attr {
        width: 100px;
      }

      &.water-meter {
        width: 150px;
      }
    }
  }
}
</style>
