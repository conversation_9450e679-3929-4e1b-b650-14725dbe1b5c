import hRequest from "@/utils/http"
import type { DataType } from "@/utils/http/types"
import axios from "axios"
import localCache from '@/utils/auth'

// const BCSERVER_URL = 'http://36.138.75.137:8077'
const BCSERVER_URL = 'https://map.lshywater.cn'
// const BCSERVER_URL = 'http://192.168.9.12:8089'
/**
 * 水平净距分析
 * @param distance 距离
 * @returns 
 */
export const horizontalDistanceAnalysis = (distance: any) => {
  return axios.post(BCSERVER_URL + '/bcserver/lsgwfiles/rest/network/horizontal', {
    minDistance: distance
  })
}
/**
 * 垂直净距分析
 * @param distance 距离
 * @returns 
 */
export const verticalDistanceAnalysis = (distance: any) => {
  return axios.post(BCSERVER_URL + '/bcserver/lsgwfiles/rest/network/vertical', {
    minDistance: distance
  })
}
/**
 * 埋深分析
 * @param distance 距离
 * @returns 
 */
export const depthAnalysis = (distance: any) => {
  return axios.post(BCSERVER_URL + '/bcserver/lsgwfiles/rest/network/depth', {
    minDistance: distance
  })
}

/**
 * 横断面分析
 */
export const verticalProfileAnalysis = (wktStr: string) => {
  return axios.post(BCSERVER_URL + '/bcserver/lsgwfiles/rest/network/crossSectional', {
    section: wktStr
  })
}

/**
 * 上游分析
 */
export const queryTraceup = (nodeId: string) => {
  return axios.post(BCSERVER_URL + '/bcserver/lsgwfiles/rest/network/traceUp', {
    nodeId: nodeId
  })
}
/**
 * 下游分析
 */
export const queryTracedown = (nodeId: string) => {
  return axios.post(BCSERVER_URL + '/bcserver/lsgwfiles/rest/network/traceDown', {
    nodeId: nodeId
  })
}
/**
 * 纵断面分析
 */
export const queryCrossSectionalV = (data: any) => {
  return axios.post(BCSERVER_URL + '/bcserver/lsgwfiles/rest/network/crossSectionalV', data)
}
/**
 * 爆管分析
 */
export const queryExplosion = (data: any) => {
  return axios.post(BCSERVER_URL + '/bcserver/lsgwfiles/rest/network/explosion', data)
}
/**
 * 连通性分析
 */
export const queryConnectivity = (startNodeCode: string, endNodeCode: string) => {
  return axios.post(BCSERVER_URL + '/bcserver/lsgwfiles/rest/network/shortest', {
    startNodeCode: startNodeCode,
    endNodeCode: endNodeCode
  })
}
/**
 * 门户
 */
export const queryServiceStat = () => {
  console.log(localCache.getCache('Latias'));
  return axios.get(BCSERVER_URL + '/bcserver/rest/serviceManage/statistic', {
    headers: {
      'Latias': localCache.getCache('Latias') ?? ''
    }
  })
}

// export const queryServiceStat = () => {
//   return hRequest.get<DataType>({
//     url: '/geoserver/rest/serviceManage/statistic'
//   });
// };
//获取管线横截面相交
export const lineIntersect = (data: any) => {
  return hRequest.post<DataType>({
    url: `/analyse/gs/ln/intersect`,
    data: data
  });
};
//根据管线编码数组查询管线信息
export const lineListByBms = (data: any) => {
  return hRequest.post<DataType>({
    url: `/analyse/gs/ln/listByBm`,
    data: data
  });
};
//根据管点编码数组查询管线信息
export const pointListByBms = (data: any) => {
  return hRequest.post<DataType>({
    url: `/analyse/gs/pt/listByBm`,
    data: data
  });
};
//根据用水户id数组查询用水户信息
export const waterListByIds = (data: any) => {
  return hRequest.post<DataType>({
    url: `/analyse/water/user/listByIds`,
    data: data
  });
};