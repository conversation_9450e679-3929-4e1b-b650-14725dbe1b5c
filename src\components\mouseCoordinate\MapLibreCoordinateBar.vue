<!--
 * @Description: MapLibre地图坐标栏组件
 * @Date: 2023-08-15 10:00:00
 * @Author: 项目开发团队
 * @LastEditTime: 2023-08-15 10:00:00
 * @Version: 1.1.0
-->
<template>
  <MouseCoordinateDisplay 
    v-if="mapInstance"
    mapType="maplibre" 
    :mapInstance="mapInstance" 
    :visible="true"
    :precision="6"
  />
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import MouseCoordinateDisplay from '@/views/tinytools/mouseCoordinate/MouseCoordinateDisplay.vue';
import type { Map } from 'maplibre-gl';

// 地图实例引用
const mapInstance = ref<Map | null>(null);

// 在组件挂载后获取地图实例
onMounted(() => {
  // 延迟获取地图实例，确保地图已完成初始化
  setTimeout(() => {
    if (AppMaplibre._map) {
      mapInstance.value = AppMaplibre._map;
    } else {
      console.error('MapLibre地图实例未找到');
    }
  }, 500);
});
</script>

<style scoped>
/* 无需额外样式，样式由子组件提供 */
</style> 