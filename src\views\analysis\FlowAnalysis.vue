<template>
  <page-card class="flow-analysis" title="流向分析">
    <!-- 按钮区域 -->
    <el-row class="button-section" flex="~ row justify-start">
      <el-button
        type="primary"
        class="primary-btn h-9 w-100px"
        :loading="isSelectingStartPoint"
        @click="handleSelectStartPoint"
      >
        选择起点
      </el-button>
      <el-button
        type="primary"
        class="primary-btn h-9 w-100px"
        :loading="isSelectingEndPoint"
        @click="handleSelectEndPoint"
      >
        选择终点
      </el-button>
      <el-button
        type="primary"
        class="primary-btn h-9 w-100px"
        :loading="isAnalyzing"
        @click="handleAnalysis"
        :disabled="
          !startPoint ||
          !endPoint ||
          isSelectingStartPoint ||
          isSelectingEndPoint
        "
      >
        分析
      </el-button>
      <el-button
        class="clear-btn h-9 w-100px"
        @click="handleClear"
        :disabled="isAnalyzing || isSelectingStartPoint || isSelectingEndPoint"
      >
        清除
      </el-button>
    </el-row>

    <!-- 选择的点显示区域 -->
    <div
      v-if="startPoint || endPoint"
      grid="~ cols-2 gap-4"
      class="points-section"
    >
      <el-row v-if="startPoint" class="point-row">
        <el-col :span="24">
          <el-text class="point-label">已选择起点：</el-text>
          <div class="point-info">
            <span class="point-text">
              管点编号: {{ startPoint.gxddh || '无' }}
            </span>
            <span class="coordinate-text">
              坐标: {{ startPoint.lng.toFixed(6) }},
              {{ startPoint.lat.toFixed(6) }}
            </span>
          </div>
        </el-col>
      </el-row>
      <el-row v-if="endPoint" class="point-row">
        <el-col :span="24">
          <el-text class="point-label">已选择终点：</el-text>
          <div class="point-info">
            <span class="point-text">
              管点编号: {{ endPoint.gxddh || '无' }}
            </span>
            <span class="coordinate-text">
              坐标: {{ endPoint.lng.toFixed(6) }}, {{ endPoint.lat.toFixed(6) }}
            </span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 分析结果显示区域 -->
    <el-row v-if="showResult" class="result-section">
      <el-col :span="24">
        <el-text class="result-label">分析结果：</el-text>
        <span
          :class="[
            'result-text',
            analysisResult.includes('成功') ? 'success' : 'info',
          ]"
        >
          {{ analysisResult }}
        </span>
      </el-col>
    </el-row>

  </page-card>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, onMounted } from 'vue';
import {
  ElMessage,
  ElDivider,
  ElTag,
  ElButton,
  ElRow,
  ElCol,
  ElText,
} from 'element-plus';
import { useRoute } from 'vue-router';
import type { MapEngineType } from '@/components/BufferAnalysisDrawButtons.vue';
import { queryConnectivity } from '@/api/analysis';
import { AppCesium } from '@/lib/cesium/AppCesium';

/**
 * @interface AnalysisPoint 分析点坐标接口
 */
interface AnalysisPoint {
  /** 经度 */
  lng: number;
  /** 纬度 */
  lat: number;
  /** 高度，用于三维显示 */
  alt?: number;
  /** 管点编号 */
  gxddh?: string;
}

/**
 * @interface FlowPipeConfig 流动管道配置接口
 */
interface FlowPipeConfig {
  /** 管道半径（米） */
  radius: number;
  /** 流动颜色 */
  color: any;
  /** 流动速度 */
  speed: number;
  /** 流动段落长度比例 */
  percentage: number;
  /** 透明度 */
  alpha: number;
}

/**
 * @class FlowEffectManager 流动效果管理器
 * @description 专门管理Cesium流动效果的生命周期，包括创建、更新、销毁等操作
 */
class FlowEffectManager {
  private viewer: any;
  private scene: any;
  private Cesium: any;
  private isRunning: boolean = false;
  private animationCallbacks: (() => void)[] = [];
  private primitives: any[] = [];
  private frameCount: number = 0;
  private lastUpdateTime: number = 0;
  private config: FlowPipeConfig;
  private animationId: number | null = null;

  /**
   * @constructor
   * @param {any} cesiumViewer - Cesium viewer实例
   * @param {FlowPipeConfig} config - 流动效果配置
   */
  constructor(cesiumViewer: any, config: FlowPipeConfig) {
    this.viewer = cesiumViewer;
    this.scene = cesiumViewer.scene;
    this.Cesium = (window as any).BC?.Namespace?.Cesium;
    this.config = { ...config };
    
    if (!this.Cesium) {
      throw new Error('Cesium未正确加载');
    }
    
    console.log('FlowEffectManager初始化完成');
  }

  /**
   * @method createFlowPipe 创建流动管道
   * @param {number[][]} coordinates - 坐标数组 [[lng, lat, alt], ...]
   * @returns {boolean} 创建是否成功
   */
  createFlowPipe(coordinates: number[][]): boolean {
    try {
      // 清除之前的效果
      this.destroy();

      if (!coordinates || coordinates.length < 2) {
        console.warn('坐标数量不足，无法创建管道');
        return false;
      }

      // 转换坐标为Cesium格式
      const positions: number[] = [];
      coordinates.forEach(coord => {
        positions.push(coord[0], coord[1], coord[2] || 25);
      });

      // 创建管道几何体
      const geometry = new this.Cesium.PolylineVolumeGeometry({
        polylinePositions: this.Cesium.Cartesian3.fromDegreesArrayHeights(positions),
        vertexFormat: this.Cesium.VertexFormat.POSITION_NORMAL_AND_ST,
        shapePositions: this.computeCircle(this.config.radius),
        cornerType: this.Cesium.CornerType.MITERED,
      });

      // 创建流动材质
      const flowMaterial = new this.Cesium.Material({
        fabric: {
          uniforms: {
            color: this.config.color,
            percentage: this.config.percentage,
            offset: 0.0,
          },
          source: `
            uniform vec4 color;
            uniform float percentage;
            uniform float offset;
            czm_material czm_getMaterial(czm_materialInput materialInput) {
              czm_material material = czm_getDefaultMaterial(materialInput);
              vec2 st = materialInput.st;
              
              // 创建流动效果
              float flow = 1.0 - mod(st.s + offset, percentage) * (1.0 / percentage);
              
              // 添加径向渐变和发光效果
              float radialGradient = 1.0 - pow(abs(st.t - 0.5) * 2.0, 1.5);
              float glow = exp(-pow(abs(st.t - 0.5) * 4.0, 2.0)) * 0.3;
              
              float finalAlpha = (flow * radialGradient + glow) * color.a;
              
              material.diffuse = color.rgb;
              material.alpha = clamp(finalAlpha, 0.0, 1.0);
              material.emission = color.rgb * finalAlpha * 0.2;
              
              return material;
            }`,
        },
      });

      // 创建并添加primitive
      const primitive = this.scene.primitives.add(
        new this.Cesium.Primitive({
          geometryInstances: new this.Cesium.GeometryInstance({
            geometry: geometry,
            id: `flow-pipe-${Date.now()}`
          }),
          appearance: new this.Cesium.MaterialAppearance({
            material: flowMaterial,
            translucent: true,
            closed: false
          }),
          asynchronous: false
        })
      );

      this.primitives.push(primitive);

      // 创建动画回调
      const animationCallback = () => {
        if (!this.isRunning) return;
        
        try {
          if (primitive?.appearance?.material?.uniforms) {
            let offset = primitive.appearance.material.uniforms.offset;
            offset += this.config.speed;
            if (offset > 1.0) {
              offset = 0.0;
            }
            primitive.appearance.material.uniforms.offset = offset;
            this.frameCount++;
          }
        } catch (error) {
          console.warn('动画更新失败:', error);
          this.stopAnimation();
        }
      };

      this.animationCallbacks.push(animationCallback);
      
      // 开始动画
      this.startAnimation();

      console.log(`成功创建流动管道，坐标点数: ${coordinates.length}`);
      return true;

    } catch (error) {
      console.error('创建流动管道失败:', error);
      this.destroy(); // 确保清理资源
      return false;
    }
  }

  /**
   * @method computeCircle 计算圆形截面坐标
   * @param {number} radius - 半径
   * @returns {any[]} 圆形坐标数组
   */
  private computeCircle(radius: number): any[] {
    const positions = [];
    const segments = Math.max(8, Math.min(32, Math.floor(radius * 10))); // 根据半径动态调整精度
    
    for (let i = 0; i < segments; i++) {
      const angle = (i / segments) * 2 * Math.PI;
      positions.push(
        new this.Cesium.Cartesian2(
          radius * Math.cos(angle),
          radius * Math.sin(angle)
        )
      );
    }
    return positions;
  }

  /**
   * @method startAnimation 开始动画
   */
  startAnimation(): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.lastUpdateTime = Date.now();
    
    const animate = () => {
      if (!this.isRunning) return;
      
      // 节流：限制更新频率到60FPS
      const now = Date.now();
      if (now - this.lastUpdateTime < 16) {
        this.animationId = requestAnimationFrame(animate);
        return;
      }
      this.lastUpdateTime = now;
      
      // 执行所有动画回调
      this.animationCallbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          console.warn('动画回调执行失败:', error);
        }
      });
      
      this.animationId = requestAnimationFrame(animate);
    };
    
    animate();
    console.log('流动动画已启动');
  }

  /**
   * @method stopAnimation 停止动画
   */
  stopAnimation(): void {
    this.isRunning = false;
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    console.log('流动动画已停止');
  }

  /**
   * @method updateConfig 更新配置
   * @param {Partial<FlowPipeConfig>} newConfig - 新配置
   */
  updateConfig(newConfig: Partial<FlowPipeConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 更新现有材质
    this.primitives.forEach(primitive => {
      if (primitive?.appearance?.material?.uniforms) {
        if (newConfig.color) {
          primitive.appearance.material.uniforms.color = newConfig.color;
        }
        if (newConfig.percentage !== undefined) {
          primitive.appearance.material.uniforms.percentage = newConfig.percentage;
        }
      }
    });
  }

  /**
   * @method destroy 销毁所有资源
   */
  destroy(): void {
    try {
      // 停止动画
      this.stopAnimation();
      
      // 清除primitives
      this.primitives.forEach(primitive => {
        if (primitive && this.scene.primitives.contains(primitive)) {
          this.scene.primitives.remove(primitive);
        }
      });
      this.primitives = [];
      
      // 清除动画回调
      this.animationCallbacks = [];
      
      // 重置计数器
      this.frameCount = 0;
      
      console.log('FlowEffectManager资源已清理');
    } catch (error) {
      console.error('FlowEffectManager销毁失败:', error);
    }
  }

  /**
   * @method isActive 检查是否活跃
   * @returns {boolean} 是否活跃
   */
  isActive(): boolean {
    return this.isRunning && this.primitives.length > 0;
  }

  /**
   * @method getPipeCount 获取管道数量
   * @returns {number} 管道数量
   */
  getPipeCount(): number {
    return this.primitives.length;
  }

  /**
   * @method getFrameCount 获取动画帧数
   * @returns {number} 动画帧数
   */
  getFrameCount(): number {
    return this.frameCount;
  }
}

// 组件状态定义
const route = useRoute();
const mapEngine = computed((): MapEngineType => {
  return route.path.includes('cesium') ? 'cesium' : 'maplibre';
});

// 响应式数据状态
const startPoint = ref<AnalysisPoint | null>(null);
const endPoint = ref<AnalysisPoint | null>(null);
const isSelectingStartPoint = ref<boolean>(false);
const isSelectingEndPoint = ref<boolean>(false);
const isAnalyzing = ref<boolean>(false);
const showResult = ref<boolean>(false);
const analysisResult = ref<string>('');
const showDebugInfo = ref<boolean>(false); // 开发调试开关

// 图层管理
const cesiumPointsLayer = ref<any>(null);
const flowEffectManager = ref<FlowEffectManager | null>(null);

// 流动效果配置
const flowConfig: FlowPipeConfig = {
  radius: 0.3,
  color: null, // 将在组件挂载时初始化
  speed: 0.001,
  percentage: 0.15,
  alpha: 0.9
};

/**
 * @method initializeFlowEffectManager 初始化流动效果管理器
 */
const initializeFlowEffectManager = (): boolean => {
  try {
    if (mapEngine.value !== 'cesium') {
      console.warn('流动效果仅支持Cesium模式');
      return false;
    }

    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = (window as any).BC.Namespace;
    
    // 初始化颜色
    if (!flowConfig.color) {
      flowConfig.color = new Cesium.Color(0.0, 1.0, 0.0, flowConfig.alpha);
    }
    
    flowEffectManager.value = new FlowEffectManager(viewer, flowConfig);
    console.log('流动效果管理器初始化成功');
    return true;
  } catch (error) {
    console.error('初始化流动效果管理器失败:', error);
    return false;
  }
};

/**
 * @method handleClear 处理清除操作
 */
const handleClear = (): void => {
  try {
    // 清除数据状态
    startPoint.value = null;
    endPoint.value = null;
    showResult.value = false;
    analysisResult.value = '';

    // 重置状态标志
    isSelectingStartPoint.value = false;
    isSelectingEndPoint.value = false;
    isAnalyzing.value = false;

    // 清除流动效果
    if (flowEffectManager.value) {
      flowEffectManager.value.destroy();
    }

    // 清除点图层
    if (cesiumPointsLayer.value) {
      cesiumPointsLayer.value.clear();
    }

    ElMessage.info('已清除流向分析');
  } catch (error) {
    console.error('清除操作失败:', error);
    ElMessage.error('清除操作失败');
  }
};

/**
 * @method selectCesiumPipeNode 通用的Cesium节点选择函数
 * @param {string} type - 选择类型：'start' | 'end'
 * @param {Function} onSuccess - 选择成功回调函数
 * @param {Function} onError - 选择失败回调函数
 */
const selectCesiumPipeNode = (
  type: 'start' | 'end',
  onSuccess: (point: AnalysisPoint) => void,
  onError: (error: any) => void
): void => {
  try {
    const tipMessage = type === 'start' 
      ? '请在3D地图上点击管点选择起点' 
      : '请在3D地图上点击管点选择终点';
    ElMessage.info(tipMessage);

    AppCesium.getInstance().selectPipeComponent((res: any) => {
      try {
        if (res && res.type === 'node' && res.id && res.position) {
          const selectedPoint: AnalysisPoint = {
            lng: res.position.lng,
            lat: res.position.lat,
            alt: res.position.alt,
            gxddh: res.id,
          };

          // 检查重复选择
          if (type === 'end' && startPoint.value && selectedPoint.gxddh === startPoint.value.gxddh) {
            onError(new Error('终点不能与起点相同'));
            return;
          }
          if (type === 'start' && endPoint.value && selectedPoint.gxddh === endPoint.value.gxddh) {
            onError(new Error('起点不能与终点相同'));
            return;
          }

          onSuccess(selectedPoint);

          const pointName = type === 'start' ? '起点' : '终点';
          ElMessage.success(`已选择${pointName}：${selectedPoint.gxddh}`);
        } else {
          onError(new Error('请点击管点位置'));
        }
      } catch (error) {
        onError(error);
      }
    });
  } catch (error) {
    onError(error);
  }
};

/**
 * @method addSelectedPointLayer 添加选择点的图层显示
 * @param {AnalysisPoint} point - 选择的点
 * @param {string} type - 点类型：'start' | 'end'
 */
const addSelectedPointLayer = (point: AnalysisPoint, type: 'start' | 'end'): void => {
  try {
    if (mapEngine.value !== 'cesium') return;

    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = (window as any).BC.Namespace;

    // 创建或获取点图层
    if (!cesiumPointsLayer.value) {
      cesiumPointsLayer.value = new (window as any).BC.VectorLayer('flow-analysis-points-layer');
      viewer.addLayer(cesiumPointsLayer.value);
    }

    const color = type === 'start' ? Cesium.Color.GREEN : Cesium.Color.BLUE;
    const label = type === 'start' ? '起点' : '终点';
    const pointId = `flow-${type}-point-${point.gxddh}`;

    // 移除已存在的同类型点
    const existingOverlays = cesiumPointsLayer.value.getOverlaysByAttr('id', pointId);
    if (existingOverlays?.length > 0) {
      existingOverlays.forEach((overlay: any) => {
        cesiumPointsLayer.value.removeOverlay(overlay);
      });
    }

    // 创建点和标签
    const position = new (window as any).BC.Position(point.lng, point.lat, point.alt || 50);
    
    const pointOverlay = new (window as any).BC.Point(position);
    pointOverlay.id = pointId;
    pointOverlay.setStyle({
      pixelSize: 12,
      color: color,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 2,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    });

    const labelPosition = new (window as any).BC.Position(point.lng, point.lat, (point.alt || 50) + 2);
    const labelOverlay = new (window as any).BC.Label(labelPosition, `${label}\n${point.gxddh}`);
    labelOverlay.id = `${pointId}-label`;
    labelOverlay.setStyle({
      font: '14px Microsoft YaHei',
      fillColor: color,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 2,
      offsetX: 0,
      offsetY: -20,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    });

    cesiumPointsLayer.value.addOverlay(pointOverlay);
    cesiumPointsLayer.value.addOverlay(labelOverlay);

    console.log(`已创建${label}标记:`, point.gxddh);
  } catch (error) {
    console.error(`添加${type === 'start' ? '起点' : '终点'}图层失败:`, error);
  }
};

/**
 * @method handleSelectStartPoint 处理选择起点操作
 */
const handleSelectStartPoint = async (): Promise<void> => {
  if (mapEngine.value !== 'cesium') {
    ElMessage.warning('流向分析仅支持三维模式，请切换到Cesium视图');
    return;
  }

  isSelectingStartPoint.value = true;
  
  selectCesiumPipeNode(
    'start',
    (selectedPoint: AnalysisPoint) => {
      startPoint.value = selectedPoint;
      addSelectedPointLayer(selectedPoint, 'start');
      isSelectingStartPoint.value = false;
    },
    (error: any) => {
      console.error('选择起点失败:', error);
      ElMessage.error(`选择起点失败：${error instanceof Error ? error.message : '未知错误'}`);
      isSelectingStartPoint.value = false;
    }
  );
};

/**
 * @method handleSelectEndPoint 处理选择终点操作
 */
const handleSelectEndPoint = async (): Promise<void> => {
  if (mapEngine.value !== 'cesium') {
    ElMessage.warning('流向分析仅支持三维模式，请切换到Cesium视图');
    return;
  }

  isSelectingEndPoint.value = true;
  
  selectCesiumPipeNode(
    'end',
    (selectedPoint: AnalysisPoint) => {
      endPoint.value = selectedPoint;
      addSelectedPointLayer(selectedPoint, 'end');
      isSelectingEndPoint.value = false;
    },
    (error: any) => {
      console.error('选择终点失败:', error);
      ElMessage.error(`选择终点失败：${error instanceof Error ? error.message : '未知错误'}`);
      isSelectingEndPoint.value = false;
    }
  );
};

/**
 * @method sortFeaturesByFlow 根据QDBH和ZDBH对线段进行排序
 * @param {any[]} features - GeoJSON特征数组
 * @returns {any[]} 排序后的特征数组
 */
const sortFeaturesByFlow = (features: any[]): any[] => {
  if (!features?.length) return [];

  const segments = features.map((feature, index) => ({
    feature,
    index,
    startNode: feature.properties?.qdbh || feature.properties?.QDBH,
    endNode: feature.properties?.zdbh || feature.properties?.ZDBH
  }));

  console.log('线段信息:', segments.map(s => ({ 
    index: s.index, 
    start: s.startNode, 
    end: s.endNode 
  })));

  if (segments.length === 1) return [segments[0].feature];

  // 构建邻接关系
  const adjList = new Map<string, any[]>();
  const nodeConnections = new Map<string, number>();

  segments.forEach(segment => {
    const { startNode, endNode } = segment;
    if (startNode && endNode) {
      if (!adjList.has(startNode)) {
        adjList.set(startNode, []);
      }
      adjList.get(startNode)!.push(segment);
      
      nodeConnections.set(startNode, (nodeConnections.get(startNode) || 0) + 1);
      nodeConnections.set(endNode, (nodeConnections.get(endNode) || 0) + 1);
    }
  });

  // 寻找起始节点
  let startNode: string | null = null;
  
  for (const segment of segments) {
    const start = segment.startNode;
    if (start) {
      const isOnlyStart = !segments.some(s => s.endNode === start);
      if (isOnlyStart) {
        startNode = start;
        break;
      }
    }
  }

  if (!startNode) {
    console.warn('无法确定流向起点，使用第一个线段');
    return features;
  }

  console.log('确定的流向起点:', startNode);

  // 构建路径
  const sortedFeatures: any[] = [];
  const usedSegments = new Set<number>();
  let currentNode = startNode;

  while (sortedFeatures.length < segments.length) {
    const availableSegments = adjList.get(currentNode) || [];
    const nextSegment = availableSegments.find(s => !usedSegments.has(s.index));

    if (nextSegment) {
      sortedFeatures.push(nextSegment.feature);
      usedSegments.add(nextSegment.index);
      currentNode = nextSegment.endNode;
      console.log(`添加线段: ${nextSegment.startNode} -> ${nextSegment.endNode}`);
    } else {
      const remainingSegment = segments.find(s => !usedSegments.has(s.index));
      if (remainingSegment) {
        console.warn(`路径断开，强制添加: ${remainingSegment.startNode} -> ${remainingSegment.endNode}`);
        sortedFeatures.push(remainingSegment.feature);
        usedSegments.add(remainingSegment.index);
        currentNode = remainingSegment.endNode;
      } else {
        break;
      }
    }
  }

  console.log(`路径排序完成: ${sortedFeatures.length}/${features.length}`);
  return sortedFeatures;
};

/**
 * @method addFlowAnalysisResult 添加流向分析结果展示
 * @param {any} geojsonData - 分析结果GeoJSON数据
 */
const addFlowAnalysisResult = (geojsonData: any): void => {
  try {
    if (!geojsonData?.features?.length) {
      console.log('没有流向分析结果数据');
      return;
    }

    // 确保流动效果管理器已初始化
    if (!flowEffectManager.value && !initializeFlowEffectManager()) {
      ElMessage.error('流动效果初始化失败');
      return;
    }

    // 排序并合并坐标
    const sortedFeatures = sortFeaturesByFlow(geojsonData.features);
    const allCoordinates: number[][] = [];
    
    console.log('排序后的特征:', sortedFeatures.map(f => ({ 
      qdbh: f.properties?.qdbh || f.properties?.QDBH, 
      zdbh: f.properties?.zdbh || f.properties?.ZDBH 
    })));
    
    sortedFeatures.forEach((feature: any) => {
      if (feature.geometry?.coordinates) {
        const processCoordinates = (coords: number[][]) => {
          coords.forEach((coord: number[], index: number) => {
            const elevation = coord.length > 2 ? coord[2] : 
              (index === 0 ? feature?.properties?.QDGC : feature?.properties?.ZDGC) || 25;
            allCoordinates.push([coord[0], coord[1], elevation]);
          });
        };

        if (feature.geometry.type === 'MultiLineString') {
          feature.geometry.coordinates.forEach(processCoordinates);
        } else if (feature.geometry.type === 'LineString') {
          processCoordinates(feature.geometry.coordinates);
        }
      }
    });

    if (allCoordinates.length === 0) {
      console.log('没有有效的坐标数据');
      return;
    }

    console.log(`合并了 ${sortedFeatures.length} 个线段，共 ${allCoordinates.length} 个坐标点`);

    // 创建流动效果
    const success = flowEffectManager.value!.createFlowPipe(allCoordinates);
    if (success) {
      // 缩放到结果
      flyToFlowResults();
      ElMessage.success('流向分析完成，流动效果已启动');
    } else {
      ElMessage.error('流动效果创建失败');
    }

  } catch (error) {
    console.error('添加流向分析结果失败:', error);
    ElMessage.error('流向分析结果展示失败');
  }
};

/**
 * @method flyToFlowResults 缩放到流向分析结果
 */
const flyToFlowResults = async (): Promise<void> => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = (window as any).BC.Namespace;

    const positions: any[] = [];
    
    if (startPoint.value) {
      positions.push(Cesium.Cartesian3.fromDegrees(
        startPoint.value.lng,
        startPoint.value.lat,
        startPoint.value.alt || 50
      ));
    }

    if (endPoint.value) {
      positions.push(Cesium.Cartesian3.fromDegrees(
        endPoint.value.lng,
        endPoint.value.lat,
        endPoint.value.alt || 50
      ));
    }

    if (positions.length > 0) {
      if (positions.length === 1) {
        await viewer.camera.flyTo({
          destination: positions[0],
          duration: 2.0,
        });
      } else {
        const boundingSphere = Cesium.BoundingSphere.fromPoints(positions);
        await viewer.camera.flyToBoundingSphere(boundingSphere, {
          duration: 2.0,
          offset: new Cesium.HeadingPitchRange(0, -0.5, boundingSphere.radius * 3),
        });
      }
      console.log('成功缩放到流向分析结果');
    }
  } catch (error) {
    console.error('缩放到结果失败:', error);
  }
};

/**
 * @method handleAnalysis 处理分析操作
 */
const handleAnalysis = async (): Promise<void> => {
  if (!startPoint.value || !endPoint.value) {
    ElMessage.warning('请先选择起点和终点');
    return;
  }

  if (!startPoint.value.gxddh || !endPoint.value.gxddh) {
    ElMessage.warning('起点或终点缺少管点编号信息');
    return;
  }

  if (mapEngine.value !== 'cesium') {
    ElMessage.warning('流向分析仅支持三维模式');
    return;
  }

  isAnalyzing.value = true;
  
  try {
    ElMessage.info('正在进行流向分析...');

    const response = await queryConnectivity(startPoint.value.gxddh, endPoint.value.gxddh);
    console.log('流向分析API响应:', response);

    if (response?.status === 200) {
      const featureCollection = response.data;

      if (featureCollection?.features?.length > 0) {
        addFlowAnalysisResult(featureCollection);
        analysisResult.value = `流向分析完成，找到流向路径`;
      } else {
        analysisResult.value = '起点到终点无流向路径';
        ElMessage.info('起点到终点之间无流向路径');
      }

      showResult.value = true;
    } else {
      throw new Error(response?.data?.message || '流向分析失败');
    }
  } catch (error) {
    console.error('流向分析失败:', error);
    ElMessage.error(`流向分析失败：${error instanceof Error ? error.message : '未知错误'}`);
    showResult.value = false;
  } finally {
    isAnalyzing.value = false;
  }
};

// 生命周期钩子
onMounted(() => {
  console.log('FlowAnalysis组件已挂载');
  
  // 在开发环境启用调试信息
  if (import.meta.env.DEV) {
    showDebugInfo.value = true;
  }
});

onUnmounted(() => {
  console.log('FlowAnalysis组件卸载，开始清理资源');
  
  try {
    // 清理流动效果管理器
    if (flowEffectManager.value) {
      flowEffectManager.value.destroy();
      flowEffectManager.value = null;
    }

    // 清理点图层
    if (cesiumPointsLayer.value) {
      const viewer = AppCesium.getInstance().getViewer();
      cesiumPointsLayer.value.clear();
      viewer.removeLayer(cesiumPointsLayer.value);
      cesiumPointsLayer.value = null;
    }

    console.log('FlowAnalysis组件资源清理完成');
  } catch (error) {
    console.error('组件卸载清理失败:', error);
  }
});
</script>

<style scoped lang="scss">
.flow-analysis {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 550px;
  min-height: 130px;
  z-index: 1000;
}

// .button-section {
//   margin-bottom: 20px;
//   gap: 12px;

//   .primary-btn {
//     background-color: #1890ff;
//     border-color: #1890ff;
//     font-size: 13px;
//     padding: 8px 12px;

//     &:hover {
//       background-color: #40a9ff;
//       border-color: #40a9ff;
//     }

//     &:disabled {
//       background-color: #d9d9d9;
//       border-color: #d9d9d9;
//     }
//   }

//   .clear-btn {
//     background-color: #8c8c8c;
//     border-color: #8c8c8c;
//     color: white;
//     font-size: 13px;
//     padding: 8px 12px;

//     &:hover {
//       background-color: #a8a8a8;
//       border-color: #a8a8a8;
//     }

//     &:disabled {
//       background-color: #d9d9d9;
//       border-color: #d9d9d9;
//     }
//   }
// }

.points-section {
  margin: 20px 0;
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 6px;

  .point-row {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .point-label {
    color: #2c3037;
    font-size: 14px;
    margin-right: 8px;
  }

  .point-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .point-text {
    color: #1890ff;
    font-size: 14px;
    font-weight: 500;
  }

  .coordinate-text {
    color: #666;
    font-size: 12px;
  }
}

.result-section {
  margin-bottom: 20px;

  .result-label {
    color: #2c3037;
    font-size: 14px;
    margin-right: 8px;
  }

  .result-text {
    font-size: 14px;
    font-weight: 500;

    &.success {
      color: #52c41a;
    }

    &.info {
      color: #1890ff;
    }
  }
}

.debug-section {
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #f0f0f0;
  border-radius: 4px;
  border-left: 3px solid #ff6b00;

  .debug-label {
    color: #666;
    font-size: 12px;
    margin-right: 8px;
  }

  .debug-text {
    color: #333;
    font-size: 12px;
    font-family: 'Courier New', monospace;
  }
}
</style>
