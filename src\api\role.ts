import hRequest from '@/utils/http';
import type { DataType } from '@/utils/http/types';

// 列表
export const roleList = () => {
  return hRequest.get<DataType<any>>({
    url: '/sys/role/list'
  });
};
// 分页
export const rolePage = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/sys/role/page',
    params
  });
};
// 新增
export const addRole = (data: any) => {
  return hRequest.post<DataType>({
    url: "/sys/role",
    data: data
  });
};
// 修改
export const editRole = (data: any) => {
  return hRequest.put<DataType>({
    url: "/sys/role",
    data: data
  });
};
// 修改状态
export const editRoleState = (data: any) => {
  return hRequest.put<DataType>({
    url: "/sys/role/edit/status",
    data: data
  });
};
// 详情
export const detailsRole = (id: any) => {
  return hRequest.get<DataType>({
    url: `/sys/role/${id}`,
  });
};
// 删除
export const deleteRole = (id: any) => {
  return hRequest.delete<DataType>({
    url: `/sys/role/${id}`,
  });
};