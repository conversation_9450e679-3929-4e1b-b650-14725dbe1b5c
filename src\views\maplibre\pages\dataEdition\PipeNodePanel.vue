<!--
 * @Description: 管点属性编辑面板
 * @Date: 2024-01-10
 * @Author: AI Assistant
 * @LastEditTime: 2024-01-10
 -->
<template>
  <CustomCard
    :title="title"
    :width="cardWidth"
    :left="position.left"
    :top="position.top"
    :right="position.right"
    @closeHandler="handleClose"
  >
    <!-- 加载状态 -->
    <!-- <div v-if="loading" class="loading-container">
      <el-loading-directive 
        text="正在加载管点详情..." 
        class="loading-spinner"
      />
    </div> -->

    <!-- 功能按钮区 - 仅在非查看详情模式下显示 -->
    <template #header v-if="!isViewingDetail">
      <div class="header-actions">
        <el-button
          type="primary"
          size="small"
          @click="startPositionSelection"
          :loading="isSelectingPosition"
          :disabled="isDragMode"
        >
          {{ isSelectingPosition ? "选择位置中..." : "选择位置" }}
        </el-button>
        <el-button
          type="warning"
          size="small"
          @click="toggleDragMode"
          :loading="isDragMode && !isDragging"
        >
          {{
            isDragMode
              ? isDragging
                ? "拖拽中..."
                : "退出拖拽模式"
              : "移动管点"
          }}
        </el-button>
        <el-button v-if="draggedDistance > 0" type="info" size="small" disabled>
          移动距离: {{ draggedDistance.toFixed(2) }}米
        </el-button>
      </div>
    </template>

    <!-- 管点数据编辑表单 -->
    <div class="pipe-node-panel">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        class="admin-sub-form"
        label-width="auto"
      >
        <!-- 点号 - 新建时不显示，编辑时显示 -->
        <el-form-item v-if="!isNew" label="点号:" prop="gxddh">
          <custom-input
            v-model="formData.gxddh"
            placeholder="点号由系统自动分配"
            :disabled="true"
          />
        </el-form-item>

        <!-- 管类 -->
        <el-form-item label="管类:" prop="gl" required>
          <base-select
            v-model="formData.gl"
            :options="pipeTypeOptions"
            placeholder="请选择管类"
            :disabled="readonly || isViewingDetail"
          />
        </el-form-item>

        <!-- 属性 - 必填字段 -->
        <el-form-item label="属性:" prop="sx" required>
          <base-select
            v-model="formData.sx"
            :options="nodeTypeOptions"
            placeholder="请选择管点属性"
            :disabled="readonly || isViewingDetail"
          />
        </el-form-item>

        <!-- 附属物 -->
        <el-form-item label="附属物:" prop="fsw">
          <base-select
            v-model="formData.fsw"
            :options="accessoryOptions"
            placeholder="请选择附属物"
            :disabled="readonly || isViewingDetail"
          />
        </el-form-item>

        <!-- 地面高程 -->
        <el-form-item label="地面高程(m):" prop="dmgc">
          <custom-input
            v-model="formData.dmgc"
            type="number"
            placeholder="请输入地面高程"
            :disabled="readonly || isViewingDetail"
          />
        </el-form-item>

        <!-- 井深 - 仅阀门井时显示且必填 -->
        <el-form-item
          label="井深(m):"
          prop="js"
          :required="formData.fsw === '阀门井'"
        >
          <custom-input
            v-model="formData.js"
            type="number"
            placeholder="请输入井深"
            :disabled="readonly || isViewingDetail"
          />
        </el-form-item>

        <!-- 井盖规格 - 仅阀门井时显示且必填 -->
        <el-form-item
          label="井盖规格(mm):"
          prop="jggg"
          :required="formData.fsw === '阀门井'"
        >
          <base-select
            v-model="formData.jggg"
            :options="manholeCoverOptions"
            placeholder="请选择井盖规格"
            :disabled="readonly || isViewingDetail"
          />
        </el-form-item>

        <!-- 井盖材质 - 仅阀门井时显示且必填 -->
        <el-form-item
          label="井盖材质:"
          prop="jgcz"
          :required="formData.fsw === '阀门井'"
        >
          <base-select
            v-model="formData.jgcz"
            :options="manholeCoverMaterialOptions"
            placeholder="请选择井盖材质"
            :disabled="readonly || isViewingDetail"
          />
        </el-form-item>

        <!-- 所在道路 -->
        <el-form-item label="所在道路:" prop="szdl">
          <base-select
            v-model="formData.szdl"
            :options="roadOptions"
            placeholder="请选择所在道路"
            :disabled="readonly || isViewingDetail"
          />
        </el-form-item>

        <!-- 查看详情模式下的属性纠错按钮 -->
        <div v-if="isViewingDetail" class="detail-view-actions">
          <el-form-item label="">
            <el-button
              type="danger"
              @click="handlePropertyCorrection"
              :icon="WarningFilled"
            >
              属性纠错
            </el-button>
          </el-form-item>
        </div>

        <!-- 编辑模式下的操作按钮 -->
        <div v-if="!readonly && !isViewingDetail">
          <el-form-item label="管点操作：">
            <el-button
              type="warning"
              @click="dragChangePosition"
              :loading="isDragMode && !isDragging"
            >
              {{
                isDragMode
                  ? isDragging
                    ? "拖拽中..."
                    : "退出拖拽模式"
                  : "移动管点"
              }}
            </el-button>
            <el-button
              type="warning"
              @click="handleChangePosition"
              :loading="isSelectingPosition"
            >
              修改位置
            </el-button>
          </el-form-item>

          <div class="flex justify-end mt-10">
            <el-button
              type="primary"
              class="primary-btn w-80px"
              @click="handleSave"
              :loading="saving"
            >
              保存
            </el-button>

            <el-button class="w-80px clear-btn" @click="handleCancel"
              >取消</el-button
            >
            <el-button
              type="danger"
              @click="handleDelete"
              v-if="!isNew"
              :loading="deleting"
            >
              删除
            </el-button>
          </div>
        </div>
      </el-form>
    </div>
  </CustomCard>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
} from "vue";
import { ElForm, ElMessage, ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import type { FormItemRule, FormRules } from "element-plus";

import CustomCard from "@/components/dialog/CustomCard.vue";
import BaseSelect from "@/components/baseSelect/index.vue";
import CustomInput from "@/components/input/index.vue";
import { type PipeNode } from "@/lib/maplibre/pipeNetwork/types/PipeNetworkTypes";
import { EngineType } from "@/types/plotting";
import {
  type GsPtDto,
  type GsPtVo,
  pipeNodeAdd,
  pipeNodeEdit,
  pipeNodeDelete,
  pipeNodeDetail,
} from "@/api/pipeNode";
import { useDialogStore } from "@/stores/Dialogs";

// 导入地图相关功能
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import type { Map as MapLibreMap, MapMouseEvent } from "maplibre-gl";

/**
 * @interface Props
 * @description 组件属性接口
 */
interface Props {
  /** 管点GID (编辑/查看时使用) */
  nodeGid?: number | string;
  /** 是否为新建模式 */
  isNew?: boolean;
  /** 是否是打断线模式 */
  isBreaking?: boolean;
  /** 是否为只读模式 */
  readonly?: boolean;
  /** 是否为查看详情模式 */
  isViewingDetail?: boolean;
  /** 管点坐标 (新建时使用) */
  coordinates?: [number, number];
  /** 面板位置 */
  position?: {
    left?: string;
    top?: string;
    width?: string;
    right?: string;
    bottom?: string;
  };
}

/**
 * @interface Emits
 * @description 组件事件接口
 */
interface Emits {
  /** 关闭面板事件 */
  (e: "close"): void;
  /** 保存成功事件 */
  (e: "saved", node: any): void;
  /** 删除成功事件 */
  (e: "deleted", nodeId: string): void;
  /** 修改位置事件 */
  (e: "changePosition"): void;
  /** 更新图层事件 */
  (e: "updateLayer"): void;
}

const props = withDefaults(defineProps<Props>(), {
  isNew: false,
  readonly: false,
  isViewingDetail: false,
  position: () => ({
    left: "520px",
    top: "110px",
    width: "400px",
  }),
});

const emit = defineEmits<Emits>();

// 表单引用
const formRef = ref<InstanceType<typeof ElForm>>();

// 加载和保存状态
const loading = ref(false);
const saving = ref(false);
const deleting = ref(false);
const isDragging = ref(false);

// 地图交互状态
const isSelectingPosition = ref(false);
const isDragMode = ref(false);
const dragStartCoordinates = ref<[number, number] | null>(null);
const draggedDistance = ref(0);
const mode = ref<"create" | "edit">("create");
let map: MapLibreMap | null = null;
let mapClickHandler: ((e: MapMouseEvent) => void) | null = null;

// 临时图层管理
const TEMP_NODE_LAYER_ID = "temp-pipe-nodes";
const TEMP_NODE_SOURCE_ID = "temp-pipe-nodes-source";

/**
 * @interface FormData
 * @description 表单数据接口，扩展API数据结构并修改数据类型
 */
interface FormData {
  gl: string; // 管类
  gxddh: string; // 点号
  sx: string; // 属性
  fsw: string; // 附属物
  dmgc: number; // 地面高程 - 改为number类型
  x: number; // X坐标 - 改为number类型
  y: number; // Y坐标 - 改为number类型
  js: number; // 井深 - 改为number类型
  jggg: string; // 井盖规格
  jgcz: string; // 井盖材质
  szdl: string; // 所在道路
  longitude: number; // 经度 - 改为number类型
  latitude: number; // 纬度 - 改为number类型
  gid?: number | string; // 管点ID（编辑时使用）
}

// 表单数据，修改数据类型
const formData = reactive<FormData>({
  gl: "给水", // 使用中文名称
  gxddh: "", // 点号
  sx: "", // 属性
  fsw: "无", // 附属物
  dmgc: 0, // 地面高程 - number类型
  x: 0, // X坐标 - number类型
  y: 0, // Y坐标 - number类型
  js: 0, // 井深 - 可为空
  jggg: "600", // 井盖规格
  jgcz: "铸铁", // 井盖材质
  szdl: "", // 所在道路
  longitude: 0, // 经度 - number类型
  latitude: 0, // 纬度 - number类型
});

// 计算属性
const title = computed(() => {
  if (props.readonly) return "管点详情";
  return props.isNew ? "新建管点" : "编辑管点";
});

const cardWidth = computed(() => props.position.width || "400px");

// 管类选项 - 使用中文作为value
const pipeTypeOptions = computed(() => [{ label: "给水", value: "JS" }]);

// 管点类型选项 - 使用中文作为value
const nodeTypeOptions = computed(() => [
  { label: "直线点", value: "直线点" },
  { label: "转折点", value: "转折点" },
  { label: "变材", value: "变材" },
  { label: "变径", value: "变径" },
  { label: "出地", value: "出地" },
  { label: "出水口", value: "出水口" },
  { label: "多通", value: "多通" },
  { label: "三通", value: "三通" },
  { label: "四通", value: "四通" },
  { label: "起止点", value: "起止点" },
  { label: "终止点", value: "终止点" },
  { label: "入户", value: "入户" },
  { label: "弯头", value: "弯头" },
  { label: "预留口", value: "预留口" },
]);

// 附属物选项
const accessoryOptions = computed(() => [
  { label: "无", value: "无" },
  { label: "井盖", value: "井盖" },
  { label: "阀门井", value: "阀门井" },
  { label: "水表井", value: "水表井" },
  { label: "消火栓", value: "消火栓" },
  { label: "排气阀", value: "排气阀" },
  { label: "排污阀", value: "排污阀" },
  { label: "泵站", value: "泵站" },
  { label: "阀门", value: "阀门" },
  { label: "管帽", value: "管帽" },
  { label: "水表", value: "水表" },
  { label: "水塔", value: "水塔" },
  { label: "消防井", value: "消防井" },
  { label: "预留口", value: "预留口" },
  { label: "检修井", value: "检修井" },
]);

// 井盖规格选项
const manholeCoverOptions = computed(() => [
  { label: "1000", value: "1000" },
  { label: "800", value: "800" },
  { label: "700", value: "700" },
  { label: "600", value: "600" },
  { label: "600*600", value: "600*600" },
  { label: "800*800", value: "800*800" },
  { label: "1000*1000", value: "1000*1000" },
  { label: "1200*1000", value: "1200*1000" },
  { label: "1200*800", value: "1200*800" },
  { label: "1300*1200", value: "1300*1200" },
]);

// 井盖材质选项
const manholeCoverMaterialOptions = computed(() => [
  { label: "金属", value: "金属" },
  { label: "塑料", value: "塑料" },
  { label: "砼", value: "砼" },
  { label: "铸铁", value: "铸铁" },
  { label: "其他", value: "其他" },
]);

// 道路选项 (示例数据，实际应从系统配置获取)
const roadOptions = computed(() => [
  { label: "双星村", value: "双星村" },
  { label: "建设路", value: "建设路" },
  { label: "人民路", value: "人民路" },
  { label: "解放路", value: "解放路" },
  { label: "中山路", value: "中山路" },
]);

/**
 * 验证正数（大于等于0的数字，支持小数）
 */
const validatePositiveNumber = (rule: any, value: any, callback: any) => {
  if (value === "" || value === null || value === undefined) {
    callback(); // 允许为空，由required规则处理
    return;
  }

  const num = Number(value);
  if (isNaN(num)) {
    callback(new Error("必须为数字"));
    return;
  }

  if (num < 0) {
    callback(new Error("必须为大于等于0的正数"));
    return;
  }

  callback();
};

// 表单验证规则
const formRules = computed((): Record<string, FormItemRule[]> => {
  const rules: Record<string, FormItemRule[]> = {
    // 点号验证 - 编辑时必填
    ...(props.isNew
      ? {}
      : {
          gxddh: [
            { required: true, message: "点号不能为空", trigger: "blur" },
            {
              min: 1,
              max: 50,
              message: "点号长度应在1-50个字符",
              trigger: "blur",
            },
          ],
        }),

    // 管类必填
    gl: [{ required: true, message: "请选择管类", trigger: "change" }],

    // 属性必填
    sx: [{ required: true, message: "请选择管点属性", trigger: "change" }],

    // 地面高程验证
    dmgc: [
      {
        required: true,
        message: "地面高程为必填项",
        trigger: "blur",
      },
      { validator: validatePositiveNumber, trigger: "blur" },
    ],
    js: [{ validator: validatePositiveNumber, trigger: "blur" }],
  };

  // 当属性为阀门井时，井深、井盖规格、井盖材质为必填
  if (formData.fsw === "阀门井") {
    rules.js = [
      { required: true, message: "阀门井的井深为必填项", trigger: "blur" },
      { validator: validatePositiveNumber, trigger: "blur" },
    ];

    rules.jggg = [
      {
        required: true,
        message: "阀门井的井盖规格为必填项",
        trigger: "change",
      },
    ];

    rules.jgcz = [
      {
        required: true,
        message: "阀门井的井盖材质为必填项",
        trigger: "change",
      },
    ];
  }

  return rules;
});

// ============ 地图交互方法 ============

/**
 * @function initializeMap
 * @description 初始化地图实例
 */
const initializeMap = async (): Promise<void> => {
  try {
    console.log("initializeMap: 开始初始化地图");
    map = AppMaplibre.getMap();

    if (map) {
      console.log("initializeMap: 地图实例获取成功");
      await nextTick(); // 确保DOM更新完成

      // 初始化临时图层
      console.log("initializeMap: 准备初始化临时图层");
      initializeTempLayer();
      console.log("initializeMap: 地图初始化完成");
    } else {
      console.error("initializeMap: 地图实例未准备就绪");
    }
  } catch (error) {
    console.error("initializeMap: 初始化失败", error);
  }
};

/**
 * @function ensureTempLayerOnTop
 * @description 确保临时图层在所有图层的最顶层
 */
const ensureTempLayerOnTop = (): void => {
  if (!map) {
    console.error("ensureTempLayerOnTop: 地图实例不存在");
    return;
  }

  try {
    // 检查临时图层是否存在
    if (!map.getLayer(TEMP_NODE_LAYER_ID)) {
      console.log("ensureTempLayerOnTop: 临时图层不存在，无需调整");
      return;
    }

    // 将临时图层移动到最顶层
    map.moveLayer(TEMP_NODE_LAYER_ID);
    console.log("ensureTempLayerOnTop: 临时图层已移动到最顶层");
  } catch (error) {
    console.error("ensureTempLayerOnTop: 调整图层顺序失败", error);
  }
};

/**
 * @function initializeTempLayer
 * @description 初始化临时图层
 */
const initializeTempLayer = (): void => {
  if (!map) {
    console.error("initializeTempLayer: 地图实例不存在");
    return;
  }

  try {
    console.log("initializeTempLayer: 开始初始化临时图层");

    // 检查数据源是否已存在
    const existingSource = map.getSource(TEMP_NODE_SOURCE_ID);
    if (!existingSource) {
      console.log("initializeTempLayer: 添加临时图层数据源");
      map.addSource(TEMP_NODE_SOURCE_ID, {
        type: "geojson",
        data: {
          type: "FeatureCollection",
          features: [],
        },
      });
    } else {
      console.log("initializeTempLayer: 临时图层数据源已存在");
    }

    // 检查图层是否已存在
    const existingLayer = map.getLayer(TEMP_NODE_LAYER_ID);
    if (!existingLayer) {
      console.log("initializeTempLayer: 添加临时图层");
      map.addLayer({
        id: TEMP_NODE_LAYER_ID,
        type: "circle",
        source: TEMP_NODE_SOURCE_ID,
        paint: {
          "circle-radius": 8,
          "circle-color": "#ff6b35",
          "circle-stroke-width": 2,
          "circle-stroke-color": "#fff",
        },
      });
    } else {
      console.log("initializeTempLayer: 临时图层已存在");
    }

    // 确保临时图层在最顶层
    ensureTempLayerOnTop();

    console.log("initializeTempLayer: 临时图层初始化成功");
  } catch (error) {
    console.error("initializeTempLayer: 初始化失败", error);
  }
};

/**
 * @function addTempNode
 * @description 添加临时管点到图层
 */
const addTempNode = (coordinates: [number, number]): void => {
  if (!map) {
    console.error("addTempNode: 地图实例不存在");
    return;
  }

  try {
    // 检查并确保临时图层存在
    if (!map.getSource(TEMP_NODE_SOURCE_ID)) {
      console.log("addTempNode: 临时图层数据源不存在，重新初始化");
      initializeTempLayer();
    }

    // 再次检查数据源
    const source = map.getSource(TEMP_NODE_SOURCE_ID) as any;
    if (!source) {
      console.error("addTempNode: 无法获取临时图层数据源");
      return;
    }

    const feature = {
      type: "Feature",
      properties: {
        id: `temp_${Date.now()}`,
        type: "temp-node",
      },
      geometry: {
        type: "Point",
        coordinates: coordinates,
      },
    };

    source.setData({
      type: "FeatureCollection",
      features: [feature],
    });

    // 确保临时图层在最顶层
    ensureTempLayerOnTop();

    console.log("临时管点已添加:", coordinates);
  } catch (error) {
    console.error("添加临时管点失败:", error);
    // 如果添加失败，尝试重新初始化图层
    try {
      console.log("尝试重新初始化临时图层...");
      initializeTempLayer();
      // 重新尝试添加
      const source = map.getSource(TEMP_NODE_SOURCE_ID) as any;
      if (source) {
        const feature = {
          type: "Feature",
          properties: {
            id: `temp_${Date.now()}`,
            type: "temp-node",
          },
          geometry: {
            type: "Point",
            coordinates: coordinates,
          },
        };

        source.setData({
          type: "FeatureCollection",
          features: [feature],
        });

        // 确保临时图层在最顶层
        ensureTempLayerOnTop();

        console.log("重新初始化后临时管点已添加:", coordinates);
      }
    } catch (retryError) {
      console.error("重新初始化临时图层失败:", retryError);
    }
  }
};

/**
 * @function clearTempNodes
 * @description 清空所有临时管点
 */
const clearTempNodes = (): void => {
  if (!map) return;

  try {
    const source = map.getSource(TEMP_NODE_SOURCE_ID) as any;
    if (source) {
      source.setData({
        type: "FeatureCollection",
        features: [],
      });
    }

    console.log("临时管点已清空");
  } catch (error) {
    console.error("清空临时管点失败:", error);
  }
};

/**
 * @function addTempNodeForEditMode
 * @description 在编辑模式下添加临时管点显示当前位置
 */
const addTempNodeForEditMode = async (): Promise<void> => {
  try {
    console.log("addTempNodeForEditMode: 开始执行");

    // 确保地图已初始化
    if (!map) {
      console.log("addTempNodeForEditMode: 地图未初始化，等待...");
      await nextTick(); // 等待下一帧
      if (!map) {
        console.error(
          "addTempNodeForEditMode: 地图初始化失败，无法添加临时管点"
        );
        return;
      }
    }

    console.log("addTempNodeForEditMode: 地图已准备就绪");

    // 获取有效的坐标数据
    let coordinates: [number, number] | null = null;

    // 优先使用longitude/latitude
    if (formData.longitude && formData.latitude) {
      coordinates = [formData.longitude, formData.latitude];
      console.log(
        "addTempNodeForEditMode: 使用longitude/latitude坐标",
        coordinates
      );
    }

    // 检查坐标有效性
    if (!coordinates || coordinates[0] === 0 || coordinates[1] === 0) {
      console.error(
        "addTempNodeForEditMode: 无有效坐标数据，无法添加临时管点",
        {
          longitude: formData.longitude,
          latitude: formData.latitude,
          hasValidCoords: !!coordinates,
          coordsAreZero: coordinates
            ? coordinates[0] === 0 || coordinates[1] === 0
            : "no coordinates",
        }
      );
      return;
    }

    // 确保临时图层已初始化
    if (!map.getSource(TEMP_NODE_SOURCE_ID)) {
      console.log("addTempNodeForEditMode: 临时图层不存在，初始化...");
      initializeTempLayer();
    }

    // 添加临时管点
    console.log("addTempNodeForEditMode: 准备添加临时管点", coordinates);
    addTempNode(coordinates);
    console.log("addTempNodeForEditMode: 临时管点添加完成");
  } catch (error) {
    console.error("addTempNodeForEditMode: 执行失败", error);
  }
};

/**
 * @function startPositionSelection
 * @description 开始位置选择模式
 */
const startPositionSelection = (): void => {
  if (!map) {
    ElMessage.error("地图未初始化");
    return;
  }

  isSelectingPosition.value = true;
  map.getCanvas().style.cursor = "crosshair";

  // 绑定地图单次点击事件
  const onceClickHandler = (e: MapMouseEvent) => {
    handleMapClick(e);
  };

  mapClickHandler = onceClickHandler;
  map.once("click", onceClickHandler);

  ElMessage.info("请在地图上点击选择新的位置");
};

/**
 * @function handleMapClick
 * @description 处理地图点击事件
 */
const handleMapClick = (e: MapMouseEvent): void => {
  try {
    const coordinates: [number, number] = [e.lngLat.lng, e.lngLat.lat];

    // 更新表单数据中的坐标
    formData.x = coordinates[0];
    formData.y = coordinates[1];
    formData.longitude = coordinates[0];
    formData.latitude = coordinates[1];

    // 添加临时管点显示
    addTempNode(coordinates);

    // 结束位置选择模式
    stopPositionSelection();

    ElMessage.success(
      `位置已更新：${coordinates[0].toFixed(6)}, ${coordinates[1].toFixed(6)}`
    );
    console.log("位置已更新:", coordinates);
  } catch (error) {
    console.error("处理地图点击失败:", error);
    ElMessage.error("获取坐标失败");
  }
};

/**
 * @function stopPositionSelection
 * @description 停止位置选择模式
 */
const stopPositionSelection = (): void => {
  isSelectingPosition.value = false;

  if (map) {
    map.getCanvas().style.cursor = "";

    if (mapClickHandler) {
      map.off("click", mapClickHandler);
      mapClickHandler = null;
    }
  }
};

/**
 * @function cleanup
 * @description 清理地图相关资源
 */
const cleanup = (): void => {
  // 停止位置选择
  stopPositionSelection();

  // 停止拖拽模式
  if (isDragMode.value) {
    stopDragMode();
  }

  // 清理临时节点
  clearTempNodes();
  console.log("地图资源清理完成");
};

/**
 * @function initializeFormData
 * @description 初始化表单数据
 */
const initializeFormData = async () => {
  if (props.nodeGid && !props.isNew) {
    // 编辑模式：通过API查询管点详情
    try {
      loading.value = true;
      console.log("编辑模式：正在加载管点详情", props.nodeGid);

      const response = await pipeNodeDetail(props.nodeGid);
      if (response.code === 200 && response.data) {
        const nodeData = response.data;

        // 直接使用API返回的数据填充表单
        formData.gid = nodeData.gid;
        formData.gxddh = nodeData.gxddh || "";
        formData.gl = nodeData.gl || "给水";
        formData.sx = nodeData.sx || "";
        formData.fsw = nodeData.fsw || "无";
        formData.dmgc = nodeData.dmgc || 0;
        formData.x = parseFloat(nodeData.x || "0");
        formData.y = parseFloat(nodeData.y || "0");
        formData.js = nodeData.js || 0;
        formData.jggg = nodeData.jggg || "600";
        formData.jgcz = nodeData.jgcz || "铸铁";
        formData.szdl = nodeData.szdl || "";
        formData.longitude = nodeData.longitude || 0;
        formData.latitude = nodeData.latitude || 0;

        console.log("编辑模式：管点详情加载成功", formData);

        // 在编辑模式下自动添加临时管点显示当前位置
        await addTempNodeForEditMode();
      } else {
        throw new Error(response.msg || "获取管点详情失败");
      }
    } catch (error) {
      console.error("加载管点详情失败:", error);
      ElMessage.error(
        `加载失败: ${error instanceof Error ? error.message : "未知错误"}`
      );
    } finally {
      loading.value = false;
    }
  } else if (props.isNew) {
    // 新建模式：使用默认值
    formData.gxddh = ""; // 清空点号，由后端分配
    formData.gl = "给水";
    formData.sx = ""; // 清空属性，强制用户选择

    if (props.coordinates) {
      formData.x = props.coordinates[0];
      formData.y = props.coordinates[1];
      formData.longitude = props.coordinates[0];
      formData.latitude = props.coordinates[1];
    }

    console.log("新建模式：使用默认值", formData);
  }
};

// 监听附属物变化，当不是阀门井时清空相关字段
watch(
  () => formData.fsw,
  (newValue) => {
    if (newValue !== "阀门井") {
      formData.js = 0;
      formData.jggg = "600"; // 重置为默认值
      formData.jgcz = "铸铁"; // 重置为默认值
    }

    // 触发表单重新验证
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  }
);

/**
 * @function handleSave
 * @description 保存管点数据
 */
const handleSave = async () => {
  if (!formRef.value) return;
  try {
    // 表单验证
    await formRef.value.validate();

    saving.value = true;

    // 更新坐标信息
    if (props.coordinates) {
      formData.x = props.coordinates[0];
      formData.y = props.coordinates[1];
    }

    //打断线模式，直接返回给父组件保存
    if (props.isBreaking) {
      const apiData: GsPtDto = {
        gl: formData.gl,
        gxddh: formData.gxddh,
        sx: formData.sx,
        fsw: formData.fsw,
        dmgc: formData.dmgc,
        x: formData.x.toString(),
        y: formData.y.toString(),
        js: formData.js,
        jggg: formData.jggg,
        jgcz: formData.jgcz,
        szdl: formData.szdl,
        longitude: formData.longitude,
        latitude: formData.latitude,
      };
      delete apiData.gxddh;
      emit("saved", apiData);
      return;
    }

    if (props.isNew) {
      // 新建管点 - 准备数据并转换为API格式
      const apiData: GsPtDto = {
        gl: formData.gl,
        gxddh: formData.gxddh,
        sx: formData.sx,
        fsw: formData.fsw,
        dmgc: formData.dmgc,
        x: formData.x.toString(),
        y: formData.y.toString(),
        js: formData.js,
        jggg: formData.jggg,
        jgcz: formData.jgcz,
        szdl: formData.szdl,
        longitude: formData.longitude,
        latitude: formData.latitude,
      };
      delete apiData.gxddh; // 移除点号字段，由后端分配

      const apiResponse = await pipeNodeAdd(apiData);
      console.log("管点创建API响应:", apiResponse);

      if (apiResponse.code === 200) {
        // ElMessage.success("管点创建成功，点号已由系统自动分配");

        // 清理临时节点
        clearTempNodes();

        // 构造返回的管点数据
        const savedNodeData = {
          ...formData,
          gid: apiResponse.data, // 使用后端返回的ID
        };

        // 通知父组件保存成功
        emit("saved", savedNodeData);

        // 关闭面板
        emit("close");
      } else {
        throw new Error(apiResponse.msg || "创建管点失败");
      }
    } else if (props.nodeGid) {
      // 更新管点 - 转换为API格式并保存到数据库
      const apiData: GsPtDto = {
        gl: formData.gl,
        gxddh: formData.gxddh,
        sx: formData.sx,
        fsw: formData.fsw,
        dmgc: formData.dmgc,
        x: formData.x.toString(),
        y: formData.y.toString(),
        js: formData.js,
        jggg: formData.jggg,
        jgcz: formData.jgcz,
        szdl: formData.szdl,
        longitude: formData.longitude,
        latitude: formData.latitude,
        gid: formData.gid,
      };

      const apiResponse = await pipeNodeEdit(apiData);
      console.log("管点更新API响应:", apiResponse);

      if (apiResponse.code === 200) {
        ElMessage.success("管点更新成功，数据已保存到数据库");

        // 清理临时节点
        clearTempNodes();

        // 构造返回的管点数据
        const savedNodeData = {
          ...formData,
        };

        // 通知父组件保存成功
        emit("saved", savedNodeData);

        // 关闭面板
        emit("close");
      } else {
        throw new Error(apiResponse.msg || "更新管点失败");
      }
    } else {
      throw new Error("无效的操作状态");
    }

    // 通知父组件更新图层（矢量瓦片将自动同步数据库变更）
    emit("updateLayer");
  } catch (error) {
    console.error("保存管点失败:", error);
    ElMessage.error(
      `保存失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    saving.value = false;
  }
};

/**
 * @function handleDelete
 * @description 删除管点
 */
const handleDelete = async () => {
  if (!props.nodeGid) return;

  try {
    await ElMessageBox.confirm(
      `确定要删除管点 "${formData.gxddh}" 吗？此操作不可逆。`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    deleting.value = true;

    // 调用后端API删除（直接从数据库删除）
    if (props.nodeGid) {
      const apiResponse = await pipeNodeDelete(props.nodeGid);
      console.log("管点删除API响应:", apiResponse);

      if (apiResponse.code === 200) {
        ElMessage.success("管点删除成功，数据已从数据库移除");
      } else {
        throw new Error(apiResponse.msg || "删除管点失败");
      }
    } else {
      throw new Error("无法获取管点标识符");
    }

    // 通知父组件更新图层（矢量瓦片将自动同步数据库变更）
    emit("updateLayer");
    //清除临时节点
    clearTempNodes();
    //关闭面板
    emit("close");
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除管点失败:", error);
      ElMessage.error(
        `删除失败: ${error instanceof Error ? error.message : "未知错误"}`
      );
    }
  } finally {
    deleting.value = false;
  }
};

/**
 * @function handleChangePosition
 * @description 修改管点位置
 */
const handleChangePosition = () => {
  // 不关闭面板，直接启动位置选择模式
  startPositionSelection();
};

/**
 * @function dragChangePosition
 * @description 启动管点拖拽移动模式
 */
const dragChangePosition = async () => {
  try {
    if (isDragMode.value) {
      // 如果已经在拖拽模式，退出
      stopDragMode();
      return;
    }

    // 确保地图可用
    if (!map) {
      await initializeMap();
      if (!map) {
        ElMessage.error("地图不可用，无法启动拖拽模式");
        return;
      }
    }

    // 检查是否有当前管点坐标
    if (!formData.longitude || !formData.latitude) {
      ElMessage.warning("无法获取当前管点位置，请先设置坐标");
      return;
    }

    startDragMode();
    ElMessage.info("拖拽模式已启动，点击并拖拽管点图标来移动位置");
  } catch (error) {
    console.error("启动拖拽模式失败:", error);
    ElMessage.error("启动拖拽模式失败");
  }
};

/**
 * @function toggleDragMode
 * @description 切换拖拽模式
 */
const toggleDragMode = () => {
  if (isDragMode.value) {
    stopDragMode();
  } else {
    startDragMode();
  }
};

/**
 * @function startDragMode
 * @description 启动拖拽模式
 */
const startDragMode = () => {
  if (!map || isDragMode.value) return;

  try {
    isDragMode.value = true;
    isDragging.value = false;

    // 设置地图鼠标样式
    map.getCanvas().style.cursor = "grab";

    // 显示当前管点位置的临时节点
    const currentCoords: [number, number] = [
      formData.longitude,
      formData.latitude,
    ];
    addTempNode(currentCoords);

    // 绑定拖拽事件
    bindDragEvents();

    console.log("拖拽模式已启动");
  } catch (error) {
    console.error("启动拖拽模式失败:", error);
    stopDragMode();
  }
};

/**
 * @function stopDragMode
 * @description 停止拖拽模式
 */
const stopDragMode = () => {
  if (!map) return;

  try {
    // 重置所有拖拽相关状态
    isDragMode.value = false;
    isDragging.value = false;
    dragStartCoordinates.value = null;

    // 恢复地图鼠标样式
    map.getCanvas().style.cursor = "";

    // 恢复地图拖拽功能（如果被禁用的话）
    if (!map.dragPan.isEnabled()) {
      map.dragPan.enable();
    }

    // 解绑拖拽事件
    unbindDragEvents();

    // 清理临时节点
    // clearTempNodes();

    console.log("拖拽模式已停止，所有状态已重置");
  } catch (error) {
    console.error("停止拖拽模式失败:", error);
  }
};

/**
 * @function bindDragEvents
 * @description 绑定拖拽事件监听器
 */
const bindDragEvents = () => {
  if (!map) return;

  // 绑定鼠标按下事件
  map.on("mousedown", handleMouseDown);

  console.log("拖拽事件已绑定");
};

/**
 * @function unbindDragEvents
 * @description 解绑拖拽事件监听器
 */
const unbindDragEvents = () => {
  if (!map) return;

  // 解绑所有拖拽相关事件
  map.off("mousedown", handleMouseDown);
  map.off("mousemove", handleMouseMove);
  map.off("mouseup", handleMouseUp);

  console.log("拖拽事件已解绑");
};

/**
 * @function handleMouseDown
 * @description 处理鼠标按下事件 - 开始拖拽
 */
const handleMouseDown = (e: any) => {
  if (!isDragMode.value || isDragging.value) return;

  try {
    // 检查是否点击在临时节点上
    const features = map!.queryRenderedFeatures(e.point, {
      layers: [TEMP_NODE_LAYER_ID],
    });

    if (features.length === 0) return;

    // 开始拖拽
    isDragging.value = true;
    dragStartCoordinates.value = [e.lngLat.lng, e.lngLat.lat];

    // 设置拖拽中的鼠标样式
    map!.getCanvas().style.cursor = "grabbing";

    // 绑定移动和释放事件
    map!.on("mousemove", handleMouseMove);
    map!.on("mouseup", handleMouseUp);

    // 阻止地图默认拖拽行为
    map!.dragPan.disable();

    console.log("开始拖拽管点");
    ElMessage.info("拖拽中...松开鼠标完成位置修改");
  } catch (error) {
    console.error("处理鼠标按下事件失败:", error);
  }
};

/**
 * @function handleMouseMove
 * @description 处理鼠标移动事件 - 拖拽过程
 */
const handleMouseMove = (e: any) => {
  if (!isDragging.value || !dragStartCoordinates.value) return;

  try {
    const currentCoords: [number, number] = [e.lngLat.lng, e.lngLat.lat];

    // 更新临时节点位置
    updateTempNodePosition(currentCoords);

    // 实时更新表单坐标显示（但不保存）
    updateCoordinateDisplay(currentCoords);
  } catch (error) {
    console.error("处理鼠标移动事件失败:", error);
  }
};

/**
 * @function handleMouseUp
 * @description 处理鼠标释放事件 - 完成拖拽
 */
const handleMouseUp = (e: any) => {
  if (!isDragging.value) return;

  try {
    const finalCoords: [number, number] = [e.lngLat.lng, e.lngLat.lat];

    // 计算移动距离
    const distance = dragStartCoordinates.value
      ? calculateDistance(dragStartCoordinates.value, finalCoords)
      : 0;

    // 更新拖拽距离显示
    draggedDistance.value = distance;

    // 更新表单数据
    updateFormCoordinates(finalCoords);

    console.log("拖拽完成，移动距离:", distance.toFixed(2), "米");

    ElMessage.success(
      `位置已更新：${finalCoords[0].toFixed(6)}, ${finalCoords[1].toFixed(6)}`
    );

    // 完成拖拽后自动退出拖拽模式，恢复所有状态
    stopDragMode();
  } catch (error) {
    console.error("处理鼠标释放事件失败:", error);
    ElMessage.error("完成拖拽操作失败");

    // 发生错误时也要确保退出拖拽模式
    stopDragMode();
  }
};

/**
 * @function updateTempNodePosition
 * @description 更新临时节点位置
 */
const updateTempNodePosition = (coordinates: [number, number]) => {
  if (!map) return;

  try {
    const feature = {
      type: "Feature",
      properties: {
        id: `temp_${Date.now()}`,
        type: "temp-node",
      },
      geometry: {
        type: "Point",
        coordinates: coordinates,
      },
    };

    const source = map.getSource(TEMP_NODE_SOURCE_ID) as any;
    if (source) {
      source.setData({
        type: "FeatureCollection",
        features: [feature],
      });

      // 确保临时图层在最顶层
      ensureTempLayerOnTop();
    }
  } catch (error) {
    console.error("更新临时节点位置失败:", error);
  }
};

/**
 * @function updateCoordinateDisplay
 * @description 实时更新坐标显示（拖拽过程中）
 */
const updateCoordinateDisplay = (coordinates: [number, number]) => {
  // 暂时更新显示，但不触发验证
  formData.longitude = parseFloat(coordinates[0].toFixed(6));
  formData.latitude = parseFloat(coordinates[1].toFixed(6));
};

/**
 * @function updateFormCoordinates
 * @description 更新表单坐标数据（拖拽完成后）
 */
const updateFormCoordinates = (coordinates: [number, number]) => {
  try {
    // 更新经纬度
    formData.longitude = parseFloat(coordinates[0].toFixed(6));
    formData.latitude = parseFloat(coordinates[1].toFixed(6));

    // 更新投影坐标（如果需要）
    // 这里可以添加坐标转换逻辑

    console.log("表单坐标已更新:", {
      longitude: formData.longitude,
      latitude: formData.latitude,
    });
  } catch (error) {
    console.error("更新表单坐标失败:", error);
  }
};

/**
 * @function calculateDistance
 * @description 计算两点之间的距离（简单的欧几里得距离）
 */
const calculateDistance = (
  coord1: [number, number],
  coord2: [number, number]
): number => {
  const R = 6371000; // 地球半径（米）
  const dLat = ((coord2[1] - coord1[1]) * Math.PI) / 180;
  const dLon = ((coord2[0] - coord1[0]) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((coord1[1] * Math.PI) / 180) *
      Math.cos((coord2[1] * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

/**
 * @function handleCancel
 * @description 取消编辑
 */
const handleCancel = () => {
  emit("close");
};

/**
 * @function handleClose
 * @description 关闭面板
 */
const handleClose = () => {
  emit("close");
  useDialogStore().closeDialog("PipeNodePanel");
};

/**
 * @function handlePropertyCorrection
 * @description 处理属性纠错 - 打开属性纠错弹框
 */
const handlePropertyCorrection = () => {
  try {
    // 检查管点数据是否完整
    if (!props.nodeGid || !formData.gid) {
      ElMessage.warning("管点信息不完整，无法进行属性纠错");
      return;
    }

    // 准备传递给弹框的管点信息
    const nodeInfo = {
      gid: formData.gid,
      gxddh: formData.gxddh,
      gl: formData.gl,
      sx: formData.sx,
      fsw: formData.fsw,
      longitude: formData.longitude,
      latitude: formData.latitude,
      dmgc: formData.dmgc,
      js: formData.js,
      jggg: formData.jggg,
      jgcz: formData.jgcz,
      szdl: formData.szdl,
    };

    console.log("打开属性纠错弹框，管点信息:", nodeInfo);

    // 使用DialogStore打开属性纠错弹框
    useDialogStore().addDialog({
      name: "管点属性纠错",
      path: "PipePropertyCorrectionDialog",
      type: "maplibre",
      params: {
        nodeInfo: nodeInfo,
      },
    });

    ElMessage.success("属性纠错弹框已打开，请填写相关信息");
  } catch (error) {
    console.error("打开属性纠错弹框失败:", error);
    ElMessage.error("打开属性纠错弹框失败，请重试");
  }
};

watch(
  props,
  (newVal) => {
    console.log(newVal);
    initializeFormData();
  },
  { deep: true }
);

// 组件挂载时初始化
onMounted(async () => {
  console.log("PipeNodePanel组件挂载开始");
  await initializeMap();
  await initializeFormData();

  // 如果是新建模式且有坐标，添加临时管点显示
  if (props.isNew && props.coordinates) {
    addTempNode(props.coordinates);
    console.log("新建模式：已添加临时管点", props.coordinates);
  }

  console.log("PipeNodePanel组件挂载完成");
});

// 组件卸载时清理资源
onUnmounted(() => {
  // 停止拖拽模式
  if (isDragMode.value) {
    stopDragMode();
  }

  cleanup();
});
</script>

<style lang="scss" scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  font-size: 14px;
  color: #666;
}

.pipe-node-panel {
  // padding: 20px;
  max-height: 600px;
  overflow-y: auto;

  .pipe-form {
    .el-form-item {
      margin-bottom: 16px;

      :deep(.el-form-item__label) {
        color: black;
        font-weight: 500;
      }

      :deep(.el-input__inner) {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid #abdeff;
        color: #000;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }

      :deep(.el-textarea__inner) {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid #abdeff;
        color: #fff;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }

      :deep(.el-checkbox__label) {
        color: #fff;
      }

      :deep(.el-checkbox__inner) {
        background: rgba(255, 255, 255, 0.1);
        border-color: #abdeff;

        &:hover {
          border-color: #409eff;
        }
      }

      :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: #409eff;
        border-color: #409eff;
      }
    }
  }
}

// 自定义滚动条样式
.pipe-node-panel::-webkit-scrollbar {
  width: 6px;
}

.pipe-node-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.pipe-node-panel::-webkit-scrollbar-thumb {
  background: rgba(171, 222, 255, 0.5);
  border-radius: 3px;

  &:hover {
    background: rgba(171, 222, 255, 0.8);
  }
}

// 查看详情模式的样式优化
.detail-view-actions {
  padding: 16px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 16px;

  :deep(.el-button) {
    width: 100%;
    height: 40px;
    font-size: 14px;
    font-weight: 500;
  }
}
</style>
