{"code": "200", "message": "请求成功", "data": {"啥的": [[{"dictMap": {"1943192319004758016": "乐山站点", "111111": "龚电家园", "1943278388035399680": "乐山站点2", "1943191054883475456": "乐轧泵房", "1940608774704869376": "邦德加压站"}, "displayForm": "select", "fieldKey": "site_code", "fieldName": "所属站点", "fieldType": 1, "fieldValues": "1943192319004758016", "isMultiple": 0, "source": "site"}, {"displayForm": "text", "fieldKey": "device_name", "fieldName": "设备名称", "fieldType": 1, "fieldValues": "什么剂", "isMultiple": 0}, {"dictMap": {"start_meter": "电子围栏", "pressure_sensor": "压力传感器", "flowmeter": "流量计", "detector": "噪声听漏仪", "iot_device_type_wqtm": "水质检测仪"}, "displayForm": "select", "fieldKey": "second_device_type_code", "fieldName": "设备类型", "fieldType": 1, "fieldValues": "start_meter", "isMultiple": 0, "source": "iot_device_type"}, {"displayForm": "text", "fieldKey": "equipment_time", "fieldName": "设备生产日期", "fieldType": 1, "fieldValues": "2023.11.03", "isMultiple": 0}, {"dictMap": {"1943565950683693056": "1.jpeg::https://minio.zhiyou-tec.com/dev-iot/120250711150006_7870edae-09fd-47df-9487-ebe35b75dc7b.jpeg"}, "displayForm": "file", "fieldKey": "picture", "fieldName": "设备图片", "fieldType": 1, "fieldValues": "1943565950683693056", "isMultiple": 0}, {"displayForm": "text", "fieldKey": "describe", "fieldName": "设备描述", "fieldType": 1, "fieldValues": "啥的", "isMultiple": 0}, {"displayForm": "text", "fieldKey": "serial_number", "fieldName": "设备序列号", "fieldType": 1, "fieldValues": "v1111111", "isMultiple": 0}, {"dictMap": {"M.C_8148600153450807296": "重庆怡源水表公司", "M.C_8148290270440521728": "四川水利仪器设备有限公司\t", "M.C_8163119358158110720": "天津蓝瑄仪表"}, "displayForm": "select", "fieldKey": "factory_code", "fieldName": "所属厂商", "fieldType": 1, "fieldValues": "M.C_8148290270440521728", "isMultiple": 0, "source": "manufacturer"}, {"displayForm": "text", "fieldKey": "model_number", "fieldName": "型号", "fieldType": 1, "fieldValues": "", "isMultiple": 0}, {"displayForm": "text", "fieldKey": "address", "fieldName": "所属位置", "fieldType": 1, "fieldValues": "风格", "isMultiple": 0}, {"displayForm": "text", "fieldKey": "longitude", "fieldName": "经度", "fieldType": 1, "fieldValues": "103.637786", "isMultiple": 0}, {"displayForm": "text", "fieldKey": "latitude", "fieldName": "纬度", "fieldType": 1, "fieldValues": "29.494184", "isMultiple": 0}, {"displayForm": "switch", "fieldKey": "online_status", "fieldName": "设备在线状态", "fieldType": 1, "fieldValues": "", "isMultiple": 0}, {"displayForm": "switch", "fieldKey": "status", "fieldName": "设备使用状态", "fieldType": 1, "fieldValues": "0", "isMultiple": 0}, {"dictMap": {"PROTOCOL0001": "天津蓝瑄水表数据解析协议", "PROTOCOL0003": "西川府星水表数据解析协议", "PROTOCOL0002": "成都声立德克超声波大表数据解析协议"}, "displayForm": "select", "fieldKey": "protocol_code", "fieldName": "绑定协议", "fieldType": 4, "fieldValues": "PROTOCOL0003", "isMultiple": 0, "source": "protocol"}, {"displayForm": "text", "fieldKey": "device_code", "fieldName": "设备编码", "fieldType": 1, "fieldValues": "1945411788595544064", "isMultiple": 0}, {"dictMap": {"device_first_type_collector": "采集器", "device_first_type_trade": "行业设备", "device_first_type_concentrator": "集中器", "device_first_type_remote_spreadsheet": "远传表", "device_first_type_iot": "iot设备"}, "displayForm": "select", "fieldKey": "first_device_type_code", "fieldName": "设备大类", "fieldType": 1, "fieldValues": "device_first_type_iot", "isMultiple": 0, "source": "device_first_type"}]], "奇怪的奇": [[{"displayForm": "text", "fieldKey": "equipment_time", "fieldName": "设备生产日期", "fieldType": 1, "fieldValues": "2023.11.03", "isMultiple": 0}, {"dictMap": {"1943565950683693056": "1.jpeg::https://minio.zhiyou-tec.com/dev-iot/120250711150006_7870edae-09fd-47df-9487-ebe35b75dc7b.jpeg"}, "displayForm": "file", "fieldKey": "picture", "fieldName": "设备图片", "fieldType": 1, "fieldValues": "1943565950683693056", "isMultiple": 0}, {"displayForm": "text", "fieldKey": "describe", "fieldName": "设备描述", "fieldType": 1, "fieldValues": "奇怪的奇", "isMultiple": 0}, {"displayForm": "text", "fieldKey": "serial_number", "fieldName": "设备序列号", "fieldType": 1, "fieldValues": "", "isMultiple": 0}, {"dictMap": {"1943192319004758016": "乐山站点", "111111": "龚电家园", "1943278388035399680": "乐山站点2", "1943191054883475456": "乐轧泵房", "1940608774704869376": "邦德加压站"}, "displayForm": "select", "fieldKey": "site_code", "fieldName": "所属站点", "fieldType": 1, "fieldValues": "1943192319004758016", "isMultiple": 0, "source": "site"}, {"displayForm": "text", "fieldKey": "device_name", "fieldName": "设备名称", "fieldType": 1, "fieldValues": "奇怪剂", "isMultiple": 0}, {"dictMap": {"start_meter": "电子围栏", "pressure_sensor": "压力传感器", "flowmeter": "流量计", "detector": "噪声听漏仪", "iot_device_type_wqtm": "水质检测仪"}, "displayForm": "select", "fieldKey": "second_device_type_code", "fieldName": "设备类型", "fieldType": 1, "fieldValues": "start_meter", "isMultiple": 0, "source": "iot_device_type"}, {"displayForm": "text", "fieldKey": "longitude", "fieldName": "经度", "fieldType": 1, "fieldValues": "103.594883", "isMultiple": 0}, {"displayForm": "text", "fieldKey": "latitude", "fieldName": "纬度", "fieldType": 1, "fieldValues": "29.472302", "isMultiple": 0}, {"displayForm": "switch", "fieldKey": "online_status", "fieldName": "设备在线状态", "fieldType": 1, "fieldValues": "0", "isMultiple": 0}, {"displayForm": "switch", "fieldKey": "status", "fieldName": "设备使用状态", "fieldType": 1, "fieldValues": "0", "isMultiple": 0}, {"dictMap": {"PROTOCOL0001": "天津蓝瑄水表数据解析协议", "PROTOCOL0003": "西川府星水表数据解析协议", "PROTOCOL0002": "成都声立德克超声波大表数据解析协议"}, "displayForm": "select", "fieldKey": "protocol_code", "fieldName": "绑定协议", "fieldType": 4, "fieldValues": "PROTOCOL0001", "isMultiple": 0, "source": "protocol"}, {"displayForm": "text", "fieldKey": "device_code", "fieldName": "设备编码", "fieldType": 1, "fieldValues": "1945411610270515200", "isMultiple": 0}, {"dictMap": {"M.C_8148600153450807296": "重庆怡源水表公司", "M.C_8148290270440521728": "四川水利仪器设备有限公司\t", "M.C_8163119358158110720": "天津蓝瑄仪表"}, "displayForm": "select", "fieldKey": "factory_code", "fieldName": "所属厂商", "fieldType": 1, "fieldValues": "M.C_8148600153450807296", "isMultiple": 0, "source": "manufacturer"}, {"displayForm": "text", "fieldKey": "model_number", "fieldName": "型号", "fieldType": 1, "fieldValues": "", "isMultiple": 0}, {"displayForm": "text", "fieldKey": "address", "fieldName": "所属位置", "fieldType": 1, "fieldValues": "11是", "isMultiple": 0}, {"dictMap": {"device_first_type_collector": "采集器", "device_first_type_trade": "行业设备", "device_first_type_concentrator": "集中器", "device_first_type_remote_spreadsheet": "远传表", "device_first_type_iot": "iot设备"}, "displayForm": "select", "fieldKey": "first_device_type_code", "fieldName": "设备大类", "fieldType": 1, "fieldValues": "device_first_type_iot", "isMultiple": 0, "source": "device_first_type"}]]}}