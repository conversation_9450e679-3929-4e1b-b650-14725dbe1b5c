export interface IBookMark {
  id: string;
  name: string;
  type: 'cesium' | 'maplibre';
  visualAngle: {
    lng: number;
    lat: number;
    zoom?: number;
    alt?: number;
    heading?: number;
    pitch?: number;
    roll?: number;
  };
}
export interface QueryForm {
  name: string;
  type: string;
  pageSize: number;
  pageNum: number;
}
/**
 * @description 地图书签存储管理
 * @details 支持本地存储持久化，自动保存和恢复书签数据
 */
export const useBookMarkStore = defineStore("BookMarkStore", () => {
  const STORAGE_KEY = 'map_bookmarks';

  /**
   * 从本地存储加载书签数据
   */
  const loadFromStorage = (): IBookMark[] => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // 验证数据格式
        if (Array.isArray(parsed)) {
          return parsed.filter(item =>
            item &&
            typeof item.id === 'string' &&
            typeof item.name === 'string' &&
            ['cesium', 'maplibre'].includes(item.type) &&
            item.visualAngle &&
            typeof item.visualAngle.lng === 'number' &&
            typeof item.visualAngle.lat === 'number'
          );
        }
      }
    } catch (error) {
      console.warn('加载书签数据失败:', error);
    }
    return [];
  };

  /**
   * 保存书签数据到本地存储
   */
  const saveToStorage = (bookmarks: IBookMark[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(bookmarks));
    } catch (error) {
      console.warn('保存书签数据失败:', error);
    }
  };

  // 初始化书签列表，从本地存储加载
  const bookMarkList = ref<IBookMark[]>(loadFromStorage());

  /**
   * 添加书签
   */
  const addBookMark = (bookMark: IBookMark) => {
    // 检查是否已存在相同名称的书签
    const existingIndex = bookMarkList.value.findIndex(item => item.name === bookMark.name);
    if (existingIndex > -1) {
      // 替换现有书签
      bookMarkList.value[existingIndex] = { ...bookMark };
    } else {
      // 添加新书签
      bookMarkList.value.push({ ...bookMark });
    }

    // 保存到本地存储
    saveToStorage(bookMarkList.value);
  };

  /**
   * 删除书签
   */
  const removeBookMark = (id: string) => {
    const originalLength = bookMarkList.value.length;
    bookMarkList.value = bookMarkList.value.filter((bookMark) => bookMark.id !== id);

    // 只有在确实删除了数据时才保存
    if (bookMarkList.value.length < originalLength) {
      saveToStorage(bookMarkList.value);
    }
  };

  /**
   * 获取书签列表
   */
  const getBookMarkList = () => {
    return bookMarkList.value;
  };

  /**
   * 清空所有书签
   */
  const clearAllBookMarks = () => {
    bookMarkList.value = [];
    saveToStorage([]);
  };

  /**
   * 根据类型获取书签
   */
  const getBookMarksByType = (type: 'cesium' | 'maplibre') => {
    return bookMarkList.value.filter(bookmark => bookmark.type === type);
  };

  /**
   * 根据ID查找书签
   */
  const getBookMarkById = (id: string) => {
    return bookMarkList.value.find(bookmark => bookmark.id === id);
  };

  /**
   * 更新书签
   */
  const updateBookMark = (id: string, updates: Partial<IBookMark>) => {
    const index = bookMarkList.value.findIndex(bookmark => bookmark.id === id);
    if (index > -1) {
      bookMarkList.value[index] = { ...bookMarkList.value[index], ...updates };
      saveToStorage(bookMarkList.value);
      return true;
    }
    return false;
  };

  return {
    bookMarkList,
    addBookMark,
    removeBookMark,
    getBookMarkList,
    clearAllBookMarks,
    getBookMarksByType,
    getBookMarkById,
    updateBookMark,
  };
});
