<!--
 * @Description: 管网编辑主控制组件
 * @Date: 2024-01-10
 * @Author: AI Assistant
 * @LastEditTime: 2024-01-10
 -->
<template>
  <page-card :close-icon="false" class="tabulate-sta" title="管网编辑">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleNodeDraw"
        :loading="currentEditMode === 'drawing-node'"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/1.png')" alt="" srcset="" />
        </i>
        {{
          currentEditMode === "drawing-node" ? "点击地图选择位置" : "绘制管点"
        }}
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleLineDraw"
        :loading="currentEditMode === 'drawing-line'"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/2.png')" alt="" srcset="" />
        </i>
        {{ getLineDrawButtonText() }}
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleLineAdd"
        :loading="currentEditMode === 'line-add'"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/3.svg')" alt="" srcset="" />
        </i>
        {{
          currentEditMode === "line-add" ? "点击管线选择管点位置" : "线上加点"
        }}
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleLineBreak"
        :loading="currentEditMode === 'breaking-line'"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/4.png')" alt="" srcset="" />
        </i>
        {{
          currentEditMode === "breaking-line"
            ? "点击管线选择打断点"
            : "点打断线"
        }}
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleTwoPointHang"
        :loading="currentEditMode === 'two-point-hang'"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/5.png')" alt="" srcset="" />
        </i>
        两点栓点
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleTwoLineHang"
        :loading="currentEditMode === 'two-line-hang'"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/6.png')" alt="" srcset="" />
        </i>
        两边栓点
      </el-button>
      <el-button
        class="clear-btn"
        :icon="Close"
        @click="exitEditMode"
        :disabled="currentEditMode === 'idle'"
      >
        退出
      </el-button>
    </div>

    <!-- 状态指示器 -->
    <div class="status-indicator" v-if="currentEditMode !== 'idle'">
      <el-alert
        :title="getStatusText"
        :type="getStatusType"
        show-icon
        :closable="false"
        style="margin-top: 10px"
      />

      <!-- 查看详情模式的额外提示 -->
      <div v-if="currentEditMode === 'viewing-detail'" class="view-mode-tips">
        <el-tag type="success" size="small" style="margin-top: 8px">
          💡 提示：右键管网要素可进行编辑和删除操作
        </el-tag>
      </div>
    </div>

    <!-- 管线绘制进度 -->
    <div
      v-if="currentEditMode === 'drawing-line'"
      class="line-drawing-progress"
    >
      <el-steps
        :active="getLineDrawingStep()"
        align-center
        style="margin-top: 10px; max-width: 500px"
      >
        <el-step title="选择起始点" />
        <el-step title="选择终止点" />
        <el-step title="填写信息" />
      </el-steps>
    </div>
  </page-card>

  <!-- 管点属性编辑面板 -->
  <PipeNodePanel
    v-if="showNodePanel"
    :pipe-node="currentNodeData"
    :node-gid="currentNodeData?.gid || currentNodeData?.nodeGid"
    :is-new="nodeEditMode === 'creating'"
    :is-breaking="currentEditMode === 'breaking-line'"
    :readonly="nodeEditMode === 'viewing'"
    :is-viewing-detail="currentEditMode === 'viewing-detail'"
    :coordinates="tempClickCoordinates || undefined"
    :position="{ left: '950px', top: '110px', width: '400px' }"
    @close="handleNodePanelClose"
    @saved="handleNodeSaved"
    @deleted="handleNodeDeleted"
    @changePosition="handleChangeNodePosition"
    @update-layer="handleUpdatePipeLayer"
  />

  <!-- 管线属性编辑面板 -->
  <PipeLinePanel
    v-if="showLinePanel"
    :line-gid="currentLineData?.gid"
    :line-data="currentLineData"
    :is-new="lineEditMode === 'creating'"
    :readonly="lineEditMode === 'viewing'"
    :is-viewing-detail="currentEditMode === 'viewing-detail'"
    :start-point-info="selectedStartPoint || undefined"
    :end-point-info="selectedEndPoint || undefined"
    :current-selected-start="selectedStartPoint"
    :current-selected-end="selectedEndPoint"
    :edit-mode="lineEditMode"
    :temp-line-data="pipeLineTempManager?.getTempLineData()"
    :position="{ left: '950px', top: '110px', width: '420px' }"
    @close="handleLinePanelClose"
    @saved="handleLineSaved"
    @deleted="handleLineDeleted"
    @selectStartNode="handleSelectStartNode"
    @selectEndNode="handleSelectEndNode"
    @changePosition="handleLineChangePosition"
  />

  <!-- 右键上下文菜单 -->
  <ContextMenu
    :visible="contextMenu.visible"
    :position="contextMenu.position"
    :menu-items="contextMenu.menuItems"
    @close="closeContextMenu"
    @item-click="handleContextMenuClick"
  />

  <!-- 两点栓点面板 -->
  <TwoPointHangPanel
    v-if="showTwoPointHangPanel"
    @addNode="handleTwoPointHangAddNode"
    @close="handleTwoPointHangClose"
  />

  <!-- 两边栓点面板 -->
  <TwoLineHangPanel
    v-if="showTwoLineHangPanel"
    @addNode="handleTwoLineHangAddNode"
    @close="handleTwoLineHangClose"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Location,
  Connection,
  Edit,
  Close,
  Delete,
} from "@element-plus/icons-vue";
import PageCard from "@/components/PageCard.vue";
import PipeNodePanel from "./PipeNodePanel.vue";
import PipeLinePanel from "./PipeLinePanel.vue";
import ContextMenu from "@/components/ContextMenu.vue";
import type { MenuItem } from "@/components/ContextMenu.vue";
import TwoPointHangPanel from "./TwoPointHangPanel.vue";
import TwoLineHangPanel from "./TwoLineHangPanel.vue";
import { getImages } from "@/utils/getImages";
// 系统依赖
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import type { Map as MapLibreMap, MapMouseEvent } from "maplibre-gl";

// API接口
import {
  type GsPtVo,
  pipeNodeDelete,
  pipeNodeDetail,
  pipeNodeIsHangPt,
} from "@/api/pipeNode";
import { pipeLineDelete, pipeLineDetail, pipeLineBreak } from "@/api/pipeLine";

// 管理器服务
import {
  PipeLineTempManager,
  type TempLineNode,
} from "@/lib/maplibre/pipeNetwork/services/PipeLineTempManager";

// 几何计算库
import * as turf from "@turf/turf";

/**
 * @interface Props
 * @description 组件属性接口
 */
interface Props {
  mainItem?: {
    type: string;
    default: () => {};
  };
}

const props = defineProps<Props>();
const offset = 10;
// ============ 响应式状态 ============

/**
 * @type EditMode
 * @description 编辑模式类型
 */
type EditMode =
  | "idle"
  | "drawing-node"
  | "drawing-line"
  | "editing"
  | "breaking-line"
  | "line-add"
  | "two-point-hang"
  | "two-line-hang"
  | "viewing-detail";

/**
 * @type NodeEditMode
 * @description 管点编辑模式枚举
 */
type NodeEditMode = "viewing" | "editing" | "creating";

// 基础状态
const loading = ref(false);
const currentEditMode = ref<EditMode>("idle");
const showNodePanel = ref(false);
const showLinePanel = ref(false);

// 管点相关状态
const nodeEditMode = ref<NodeEditMode>("creating");
const currentNodeData = ref<any>(undefined);
const tempClickCoordinates = ref<[number, number] | null>(null);

// 管线相关状态
const selectedStartPoint = ref<TempLineNode | null>(null);
const selectedEndPoint = ref<TempLineNode | null>(null);
const selectingTarget = ref<"start" | "end" | null>(null); // 标记当前正在选择起点还是终点

// 管线编辑状态
const currentLineData = ref<any>(undefined); // 当前编辑的管线数据
const lineEditMode = ref<"viewing" | "editing" | "creating">("creating"); // 管线编辑模式

// 点打断线相关状态
const breakPoint = ref<{
  lng: number;
  lat: number;
  clickLng: number;
  clickLat: number;
  gxbm?: string;
  pipelineInfo?: any;
  geometry?: any;
} | null>(null);
const isSelectingBreakPoint = ref(false);
const selectedPipelineForBreak = ref<any>(null);

// 两点栓点相关状态
const showTwoPointHangPanel = ref(false);

// 两边栓点相关状态
const showTwoLineHangPanel = ref(false);

// 查看详情相关状态
const isViewingDetail = ref(false);
const detailClickHandler = ref<((e: MapMouseEvent) => void) | null>(null);

// 系统实例
let map: MapLibreMap | null = null;
let mapClickHandler: ((e: MapMouseEvent) => void) | null = null;
let pipeLineTempManager: PipeLineTempManager | null = null;

// 打断线模式的事件处理器
let breakPointMouseMoveHandler: ((e: any) => void) | null = null;
let breakPointClickHandler: ((e: any) => void) | null = null;

// 右键菜单状态
const contextMenu = reactive({
  visible: false,
  position: { x: 0, y: 0 },
  selectedNodeData: null as any,
  selectedLineData: null as any,
  featureType: null as "node" | "line" | null, // 要素类型：管点或管线
  menuItems: [] as MenuItem[],
});

// 管点菜单项配置
const nodeMenuItems: MenuItem[] = [
  {
    key: "edit-node",
    label: "编辑管点",
    icon: Edit,
  },
  {
    key: "delete-node",
    label: "删除管点",
    icon: Delete,
    type: "danger" as const,
  },
];

// 管线菜单项配置
const lineMenuItems: MenuItem[] = [
  {
    key: "edit-line",
    label: "编辑管线",
    icon: Edit,
  },
  {
    key: "delete-line",
    label: "删除管线",
    icon: Delete,
    type: "danger" as const,
  },
];

// ============ 计算属性 ============

/**
 * @computed getStatusText
 * @description 获取状态文本
 */
const getStatusText = computed(() => {
  switch (currentEditMode.value) {
    case "drawing-node":
      return "管点绘制模式 - 请在地图上点击选择管点位置";
    case "drawing-line":
      if (!selectedStartPoint.value) {
        return "管线绘制模式 - 请点击选择起始管点";
      } else if (!selectedEndPoint.value) {
        return "管线绘制模式 - 请点击选择终止管点";
      } else {
        return "管线绘制模式 - 起止点已选择，请填写管线信息";
      }
    case "breaking-line":
      return "点打断线模式 - 请在管线上点击选择打断点位置";
    case "line-add":
      return "线上加点模式 - 请在管线上点击选择新增管点位置";
    case "two-point-hang":
      return "两点栓点模式 - 请在面板中选择参照点并设置距离";
    case "two-line-hang":
      return "两边栓点模式 - 请在面板中选择参照边并设置偏移距离";
    case "editing":
      return "编辑模式 - 可以查看和修改管点管线信息";
    case "viewing-detail":
      return "查看详情模式 - 点击管网要素查看详细信息";
    default:
      return "查看模式 - 点击管网要素查看详情，或选择编辑操作";
  }
});

/**
 * @computed getStatusType
 * @description 获取状态类型
 */
const getStatusType = computed(() => {
  switch (currentEditMode.value) {
    case "drawing-node":
      return "primary";
    case "drawing-line":
      return "success";
    case "breaking-line":
      return "warning";
    case "line-add":
      return "success";
    case "two-point-hang":
      return "info";
    case "two-line-hang":
      return "info";
    case "editing":
      return "warning";
    case "viewing-detail":
      return "success";
    default:
      return "info";
  }
});

// ============ 生命周期 ============

/**
 * @function onMounted
 * @description 组件挂载时初始化
 */
onMounted(async () => {
  try {
    useDialogStore().changeDetailDialogEnable(false);
    await initializeSystem();
  } catch (error) {
    console.error("初始化管网编辑系统失败:", error);
    ElMessage.error("系统初始化失败，但基本功能仍可使用");
  }
});

/**
 * @function onUnmounted
 * @description 组件卸载时清理资源
 */
onUnmounted(() => {
  cleanup();
  useDialogStore().changeDetailDialogEnable(true);
});

// ============ 初始化方法 ============

/**
 * @function initializeSystem
 * @description 初始化系统
 */
const initializeSystem = async (): Promise<void> => {
  try {
    loading.value = true;
    console.log("开始初始化管网编辑系统...");

    // 等待地图实例就绪
    await waitForMapReady();

    if (map) {
      console.log("✅ 地图实例获取成功");

      // 初始化管线临时图层管理器
      initializePipeLineTempManager();

      // 绑定右键菜单事件
      bindRightClickEvent();

      // 启动要素查看功能
      enableDetailViewMode();

      // ElMessage.success("管网编辑系统已就绪");
    } else {
      console.warn("⚠️ 地图实例暂时不可用");
      // ElMessage.info("系统已就绪（地图将在使用时加载）");
    }
  } catch (error) {
    console.error("❌ 系统初始化失败:", error);
    ElMessage.warning("系统初始化失败，但基本功能仍可使用");
  } finally {
    loading.value = false;
    console.log("📍 系统初始化完成");
  }
};

/**
 * @function initializePipeLineTempManager
 * @description 初始化管线临时图层管理器
 */
const initializePipeLineTempManager = (): void => {
  if (!map) return;

  try {
    pipeLineTempManager = new PipeLineTempManager(map);
    console.log("✅ 管线临时图层管理器初始化完成");
  } catch (error) {
    console.error("❌ 管线临时图层管理器初始化失败:", error);
  }
};

/**
 * @function queryNodeDetail
 * @description 查询管点详情
 */
const queryNodeDetail = async (nodeGid: string): Promise<any | null> => {
  try {
    const response = await pipeNodeDetail(nodeGid);
    if (response.code === 200 && response.data) {
      return response.data;
    } else {
      console.error("查询管点详情失败:", response.msg);
      return null;
    }
  } catch (error) {
    console.error("查询管点详情异常:", error);
    return null;
  }
};

/**
 * @function waitForMapReady
 * @description 等待地图实例就绪
 */
const waitForMapReady = async (): Promise<void> => {
  const maxWaitTime = 3000; // 最大等待时间3秒
  const checkInterval = 100; // 检查间隔100ms
  const startTime = Date.now();

  console.log("开始等待地图实例就绪...");

  while (Date.now() - startTime < maxWaitTime) {
    try {
      // 尝试获取地图实例
      if (AppMaplibre._map) {
        map = AppMaplibre._map;
        console.log("通过内部属性获取到地图实例");
        return;
      }

      // 尝试通过getMap获取
      try {
        map = AppMaplibre.getMap();
        if (map) {
          console.log("通过getMap()获取到地图实例");
          return;
        }
      } catch (getMapError) {
        // 忽略getMap错误，继续尝试
      }

      // 等待一段时间后重试
      await new Promise((resolve) => setTimeout(resolve, checkInterval));
    } catch (error) {
      console.warn("等待地图实例时出错:", error);
    }
  }

  console.warn("等待地图实例超时，某些功能可能不可用");
};

// ============ 主要功能方法 ============

/**
 * @function handleNodeDraw
 * @description 处理管点绘制
 */
const handleNodeDraw = async (): Promise<void> => {
  try {
    if (currentEditMode.value === "drawing-node") {
      // 已经在管点绘制模式，退出
      exitEditMode();
      return;
    }

    // 🔧 新增：如果有其他功能激活，先清理
    if (currentEditMode.value !== "idle") {
      exitEditMode();
    }

    // 确保地图可用
    if (!map) {
      await waitForMapReady();
      if (!map) {
        ElMessage.error("地图不可用，无法进行绘制操作");
        return;
      }
    }

    // 禁用要素查看功能，避免冲突
    disableDetailViewMode();

    // 切换到管点绘制模式
    currentEditMode.value = "drawing-node";

    // 设置地图鼠标样式
    map.getCanvas().style.cursor = "crosshair";

    // 绑定地图单次点击事件
    bindMapOnceClickEvent();

    // ElMessage.info("请在地图上点击选择管点位置");
  } catch (error) {
    console.error("启动管点绘制失败:", error);
    ElMessage.error("启动管点绘制失败");
    exitEditMode();
  }
};

/**
 * @function handleLineDraw
 * @description 处理管线绘制
 */
const handleLineDraw = async (): Promise<void> => {
  try {
    if (currentEditMode.value === "drawing-line") {
      // 已经在管线绘制模式，退出
      exitEditMode();
      return;
    }

    // 🔧 新增：如果有其他功能激活，先清理
    if (currentEditMode.value !== "idle") {
      exitEditMode();
    }

    // 确保地图可用
    if (!map) {
      await waitForMapReady();
      if (!map) {
        ElMessage.error("地图不可用，无法进行绘制操作");
        return;
      }
    }

    // 禁用要素查看功能，避免冲突
    disableDetailViewMode();

    // 切换到管线绘制模式
    currentEditMode.value = "drawing-line";

    // 设置为新建模式
    lineEditMode.value = "creating";
    currentLineData.value = undefined;

    // 重置选择的起止点
    selectedStartPoint.value = null;
    selectedEndPoint.value = null;
    selectingTarget.value = null;

    // 清除并准备管线临时图层
    if (pipeLineTempManager) {
      pipeLineTempManager.clearAll();
    }

    // 设置地图鼠标样式
    map.getCanvas().style.cursor = "crosshair";

    // 绑定地图点击事件用于选择管点
    bindMapClickForLineDrawing();

    // ElMessage.info("请先点击起始管点，再点击终止管点");
  } catch (error) {
    console.error("启动管线绘制失败:", error);
    ElMessage.error("启动管线绘制失败");
    exitEditMode();
  }
};

/**
 * @function handleLineBreak
 * @description 处理点打断线功能
 */
const handleLineBreak = async (): Promise<void> => {
  try {
    if (currentEditMode.value === "breaking-line") {
      // 已经在打断线模式，退出
      exitEditMode();
      return;
    }

    // 🔧 新增：如果有其他功能激活，先清理
    if (currentEditMode.value !== "idle") {
      exitEditMode();
    }

    // 确保地图可用
    if (!map) {
      await waitForMapReady();
      if (!map) {
        ElMessage.error("地图不可用，无法进行打断线操作");
        return;
      }
    }

    // 切换到打断线模式
    currentEditMode.value = "breaking-line";
    isSelectingBreakPoint.value = true;

    // 重置打断线相关状态
    breakPoint.value = null;
    selectedPipelineForBreak.value = null;

    // 设置地图鼠标样式
    map.getCanvas().style.cursor = "crosshair";

    // 开始选择打断点
    await selectBreakPointOnMapLibre();

    // ElMessage.info("请在管线上点击选择打断点位置");
  } catch (error) {
    console.error("启动点打断线失败:", error);
    ElMessage.error("启动点打断线失败");
    exitEditMode();
  }
};

/**
 * @function handleLineAdd
 * @description 处理线上加点功能
 */
const handleLineAdd = async (): Promise<void> => {
  try {
    if (currentEditMode.value === "line-add") {
      // 已经在线上加点模式，退出
      exitEditMode();
      return;
    }

    // 🔧 新增：如果有其他功能激活，先清理
    if (currentEditMode.value !== "idle") {
      exitEditMode();
    }

    // 确保地图可用
    if (!map) {
      await waitForMapReady();
      if (!map) {
        ElMessage.error("地图不可用，无法进行线上加点操作");
        return;
      }
    }

    // 切换到线上加点模式
    currentEditMode.value = "line-add";
    isSelectingBreakPoint.value = true;

    // 重置打断线相关状态（复用状态变量）
    breakPoint.value = null;
    selectedPipelineForBreak.value = null;

    // 设置地图鼠标样式
    map.getCanvas().style.cursor = "crosshair";

    // 开始选择加点位置（复用selectBreakPointOnMapLibre方法）
    await selectBreakPointOnMapLibre();

    // ElMessage.info("请在管线上点击选择新增管点位置");
  } catch (error) {
    console.error("启动线上加点模式失败:", error);
    ElMessage.error(
      `启动线上加点模式失败: ${
        error instanceof Error ? error.message : "未知错误"
      }`
    );
    exitEditMode();
  }
};

/**
 * @function selectBreakPointOnMapLibre
 * @description 在MapLibre地图上选择打断点（复用爆管分析的方法）
 */
const selectBreakPointOnMapLibre = async (): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      if (!map) {
        reject(new Error("地图实例不可用"));
        return;
      }

      const modeText =
        currentEditMode.value === "breaking-line" ? "打断点" : "新增管点";
      // ElMessage.info(`请在管线上点击选择${modeText}位置`);

      // 鼠标移动事件处理器 - 实时检测管线并显示提示点
      breakPointMouseMoveHandler = (e: any) => {
        // 查询鼠标位置的管线要素
        const features = map!.queryRenderedFeatures(
          [
            [e.point.x - offset / 2, e.point.y - offset / 2],
            [e.point.x + offset / 2, e.point.y + offset / 2],
          ],
          {
            layers: ["mvt_pipeLine"], // 管线矢量瓦片图层
          }
        );

        if (features.length > 0) {
          // 鼠标在管线上，显示提示点
          showBreakHintPoint(e.lngLat);
          // 改变鼠标样式
          map!.getCanvas().style.cursor = "crosshair";
        } else {
          // 鼠标不在管线上，隐藏提示点
          hideBreakHintPoint();
          // 恢复默认鼠标样式
          map!.getCanvas().style.cursor = "";
        }
      };

      // 点击事件处理器 - 确定打断点位置
      breakPointClickHandler = async (e: any) => {
        // 查询点击位置的管线要素
        const features = map!.queryRenderedFeatures(
          [
            [e.point.x - offset / 2, e.point.y - offset / 2],
            [e.point.x + offset / 2, e.point.y + offset / 2],
          ],
          {
            layers: ["mvt_pipeLine"], // 管线矢量瓦片图层
          }
        );

        if (features.length > 0) {
          // 获取管线信息
          const pipelineFeature = features[0];
          const gxbm =
            pipelineFeature.properties?.gxbm ||
            pipelineFeature.properties?.GXBM ||
            "";

          try {
            // 计算管线上的精确点位
            const preciseLngLat = calculateNearestPointOnLine(
              e.lngLat,
              pipelineFeature.geometry
            );

            // 点击在管线上，确定打断点（使用计算后的精确坐标）
            breakPoint.value = {
              lng: preciseLngLat.lng,
              lat: preciseLngLat.lat,
              clickLng: e.lngLat.lng, // 保存原始点击坐标
              clickLat: e.lngLat.lat,
              gxbm: gxbm,
              pipelineInfo: pipelineFeature.properties,
              geometry: pipelineFeature.geometry,
            };

            selectedPipelineForBreak.value = pipelineFeature.properties;

            // 添加打断点显示图层（使用精确坐标）
            addBreakPointLayer(preciseLngLat, modeText);

            // 清理事件监听器
            cleanupMapEvents();

            // const distanceToLine = turf.distance(
            //   turf.point([e.lngLat.lng, e.lngLat.lat]),
            //   turf.point([preciseLngLat.lng, preciseLngLat.lat]),
            //   { units: "meters" }
            // );

            // const successText =
            //   currentEditMode.value === "line-add" ? "新增管点位置" : "打断点";
            // ElMessage.success(
            //   `已选择${successText}${
            //     gxbm ? `（管线：${gxbm}）` : ""
            //   }，计算精度: ${distanceToLine.toFixed(2)}米`
            // );
            isSelectingBreakPoint.value = false;

            // 处理选择打断点后的逻辑
            handleBreakPointSelected();

            resolve();
          } catch (error) {
            console.error("计算管线上精确点位失败:", error);
            const warningText =
              currentEditMode.value === "line-add" ? "新增管点" : "打断点";
            ElMessage.warning(`计算精确位置失败，请重新选择${warningText}`);
          }
        } else {
          // 点击不在管线上，提示用户
          const warningText =
            currentEditMode.value === "line-add" ? "新增管点" : "打断点";
          ElMessage.warning(`请在管线上点击选择${warningText}`);
        }
      };

      // 清理事件监听器和提示点的函数
      const cleanupMapEvents = () => {
        if (breakPointMouseMoveHandler) {
          map!.off("mousemove", breakPointMouseMoveHandler);
          breakPointMouseMoveHandler = null;
        }
        if (breakPointClickHandler) {
          map!.off("click", breakPointClickHandler);
          breakPointClickHandler = null;
        }
        hideBreakHintPoint();
        map!.getCanvas().style.cursor = "";
      };

      // 绑定事件监听器
      map.on("mousemove", breakPointMouseMoveHandler);
      map.on("click", breakPointClickHandler);

      // 设置超时机制（可选）
      setTimeout(() => {
        if (isSelectingBreakPoint.value) {
          cleanupMapEvents();
          isSelectingBreakPoint.value = false;
          reject(new Error("选择打断点超时"));
        }
      }, 60000); // 60秒超时
    } catch (error) {
      console.error("MapLibre地图点选择失败:", error);
      isSelectingBreakPoint.value = false;
      reject(error);
    }
  });
};

/**
 * @function exitEditMode
 * @description 退出编辑模式
 */
const exitEditMode = (): void => {
  // 重置状态
  currentEditMode.value = "idle";
  selectedStartPoint.value = null;
  selectedEndPoint.value = null;
  selectingTarget.value = null;
  tempClickCoordinates.value = null;
  currentNodeData.value = undefined;
  nodeEditMode.value = "creating";
  lineEditMode.value = "creating";
  currentLineData.value = undefined;

  // 重置打断线和线上加点相关状态
  breakPoint.value = null;
  isSelectingBreakPoint.value = false;
  selectedPipelineForBreak.value = null;

  // 清理相关图层和事件
  unbindMapClickEvent();
  unbindBreakPointEvents(); // 清理打断线模式的事件监听器
  removeBreakPointLayer();
  hideBreakHintPoint();

  // 清理管线临时图层
  if (pipeLineTempManager) {
    pipeLineTempManager.clearAll();
  }

  // 重新启用要素查看功能
  enableDetailViewMode();

  // 重置地图鼠标样式
  if (map) {
    map.getCanvas().style.cursor = "";
  }

  // 关闭面板
  showNodePanel.value = false;
  showLinePanel.value = false;
  showTwoPointHangPanel.value = false;
  showTwoLineHangPanel.value = false;

  console.log("退出编辑模式");
};

// ============ 地图事件处理 ============

/**
 * @function bindMapOnceClickEvent
 * @description 绑定地图单次点击事件（用于管点绘制）
 */
const bindMapOnceClickEvent = (): void => {
  if (!map) return;

  const onceClickHandler = (e: MapMouseEvent) => {
    if (currentEditMode.value === "drawing-node") {
      handleMapClickForNode(e);
    }
  };

  map.once("click", onceClickHandler);
};

/**
 * @function bindMapClickForLineDrawing
 * @description 绑定地图点击事件用于管线绘制（选择起止点）
 */
const bindMapClickForLineDrawing = (): void => {
  if (!map) return;

  const clickHandler = async (e: MapMouseEvent) => {
    if (currentEditMode.value !== "drawing-line") return;

    try {
      // 查询点击位置的管点要素
      const features = map!.queryRenderedFeatures(
        [
          [e.point.x - offset / 2, e.point.y - offset / 2],
          [e.point.x + offset / 2, e.point.y + offset / 2],
        ],
        {
          layers: ["mvt_pipeNode"], // 管点图层ID
        }
      );

      if (features.length === 0) {
        ElMessage.warning("请点击现有的管点");
        return;
      }

      const feature = features[0];
      const nodeCode =
        feature.properties?.nodeCode || feature.properties?.gxddh;
      const nodeGid = feature.properties?.gid;

      if (!nodeCode || !nodeGid) {
        ElMessage.warning("该管点缺少必要信息");
        return;
      }

      // 查询管点详情
      const nodeDetail = await queryNodeDetail(nodeGid);
      if (!nodeDetail) {
        ElMessage.error("获取管点详情失败");
        return;
      }
      const coordinates: [number, number] = [
        nodeDetail.longitude,
        nodeDetail.latitude,
      ];
      const tempNodeInfo: TempLineNode = {
        nodeId: nodeGid,
        nodeCode,
        coordinates,
        nodeData: nodeDetail,
      };

      // 根据selectingTarget状态决定操作
      if (selectingTarget.value === "start") {
        // 重新选择起始点
        if (
          selectedEndPoint.value &&
          nodeCode === selectedEndPoint.value.nodeCode
        ) {
          ElMessage.warning("起始点不能与终止点相同");
          return;
        }

        selectedStartPoint.value = tempNodeInfo;

        // 更新临时图层中的起始点
        if (pipeLineTempManager) {
          pipeLineTempManager.updateStartNode(tempNodeInfo);
        }

        ElMessage.success(`已重新选择起始点：${nodeCode}`);

        // 重置选择状态
        selectingTarget.value = null;

        // 如果两个点都已选择，显示面板
        if (selectedEndPoint.value) {
          showLinePanel.value = true;
          unbindMapClickEvent();
          map!.getCanvas().style.cursor = "default";
        }
      } else if (selectingTarget.value === "end") {
        // 重新选择终止点
        if (
          selectedStartPoint.value &&
          nodeCode === selectedStartPoint.value.nodeCode
        ) {
          ElMessage.warning("终止点不能与起始点相同");
          return;
        }

        selectedEndPoint.value = tempNodeInfo;

        // 更新临时图层中的终止点
        if (pipeLineTempManager) {
          pipeLineTempManager.updateEndNode(tempNodeInfo);
        }

        ElMessage.success(`已重新选择终止点：${nodeCode}`);

        // 重置选择状态
        selectingTarget.value = null;

        // 如果两个点都已选择，显示面板
        if (selectedStartPoint.value) {
          showLinePanel.value = true;
          unbindMapClickEvent();
          map!.getCanvas().style.cursor = "default";
        }
      } else {
        // 原始的逐步选择逻辑
        if (!selectedStartPoint.value) {
          // 选择起始点
          selectedStartPoint.value = tempNodeInfo;

          // 在临时图层中添加起始点
          if (pipeLineTempManager) {
            pipeLineTempManager.addStartNode(tempNodeInfo);
          }

          ElMessage.success(`已选择起始点：${nodeCode}`);
        } else if (!selectedEndPoint.value) {
          // 选择终止点
          if (nodeCode === selectedStartPoint.value.nodeCode) {
            ElMessage.warning("终止点不能与起始点相同");
            return;
          }

          selectedEndPoint.value = tempNodeInfo;

          // 在临时图层中添加终止点和连线
          if (pipeLineTempManager) {
            pipeLineTempManager.addEndNode(tempNodeInfo);
          }

          ElMessage.success(`已选择终止点：${nodeCode}`);

          // 两个点都选择完毕，显示管线编辑面板
          showLinePanel.value = true;

          // 解绑点击事件
          unbindMapClickEvent();
          // 修改鼠标样式
          map!.getCanvas().style.cursor = "default";
        }
      }
    } catch (error) {
      console.error("处理管点选择失败:", error);
      ElMessage.error("选择管点失败");
    }
  };

  // 绑定点击事件
  mapClickHandler = clickHandler;
  map.on("click", clickHandler);
};

/**
 * @function handleMapClickForNode
 * @description 处理地图点击创建管点
 */
const handleMapClickForNode = (e: MapMouseEvent): void => {
  try {
    const coordinates: [number, number] = [e.lngLat.lng, e.lngLat.lat];
    tempClickCoordinates.value = coordinates;

    // 设置为创建模式
    nodeEditMode.value = "creating";
    currentNodeData.value = undefined;

    // 显示编辑面板，传递坐标
    showNodePanel.value = true;

    // console.log("点击地图获取坐标:", coordinates);
    // ElMessage.info(
    //   `已获取坐标：${coordinates[0].toFixed(6)}, ${coordinates[1].toFixed(6)}`
    // );
  } catch (error) {
    console.error("处理地图点击失败:", error);
    ElMessage.error("获取坐标失败");
  }
};

/**
 * @function unbindMapClickEvent
 * @description 解绑地图点击事件
 */
const unbindMapClickEvent = (): void => {
  if (map && mapClickHandler) {
    map.off("click", mapClickHandler);
    mapClickHandler = null;
  }
};

/**
 * @function unbindBreakPointEvents
 * @description 解绑打断线模式的事件监听器
 */
const unbindBreakPointEvents = (): void => {
  if (map) {
    if (breakPointMouseMoveHandler) {
      map.off("mousemove", breakPointMouseMoveHandler);
      breakPointMouseMoveHandler = null;
    }
    if (breakPointClickHandler) {
      map.off("click", breakPointClickHandler);
      breakPointClickHandler = null;
    }
    // 恢复默认鼠标样式
    map.getCanvas().style.cursor = "";
  }
};

// ============ 面板事件处理 ============

/**
 * @function handleNodePanelClose
 * @description 处理管点面板关闭
 */
const handleNodePanelClose = (): void => {
  const wasViewingDetail = currentEditMode.value === "viewing-detail";

  showNodePanel.value = false;
  // 清理临时坐标和管点数据
  tempClickCoordinates.value = null;
  currentNodeData.value = undefined;
  nodeEditMode.value = "creating";

  if (wasViewingDetail) {
    // 如果是从查看详情模式关闭，重置为空闲状态并保持要素查看功能
    currentEditMode.value = "idle";
    console.log("✅ 管点详情面板已关闭，返回要素查看模式");
  } else {
    // 否则正常退出编辑模式
    exitEditMode();
  }
};

/**
 * @function handleNodeSaved
 * @description 处理管点保存成功
 */
const handleNodeSaved = async (node: any): Promise<void> => {
  console.log("管点保存成功:", node);

  // 根据当前模式处理不同的保存逻辑
  if (
    currentEditMode.value === "breaking-line" &&
    breakPoint.value &&
    selectedPipelineForBreak.value
  ) {
    // 点打断线模式：调用打断线接口
    try {
      await handlePipeLineBreakSave(node);
    } catch (error) {
      console.error("点打断线保存失败:", error);
      ElMessage.error(
        `点打断线失败: ${error instanceof Error ? error.message : "未知错误"}`
      );
      return;
    }
  } else if (currentEditMode.value === "line-add") {
    // 线上加点模式：只需要保存管点，不需要额外操作
    console.log("线上加点完成，管点已保存:", {
      nodeData: node,
      pipelineInfo: selectedPipelineForBreak.value,
      precisedCoordinates: breakPoint.value
        ? [breakPoint.value.lng, breakPoint.value.lat]
        : null,
    });

    // 更新地图图层
    handleUpdatePipeLayer();

    // 清除临时图层
    removeBreakPointLayer();

    // ElMessage.success("线上加点成功");
  }

  // 通用的成功处理逻辑
  if (currentEditMode.value !== "breaking-line") {
    ElMessage.success("管点保存成功");
  }

  showNodePanel.value = false;
  // 清理状态
  tempClickCoordinates.value = null;
  currentNodeData.value = undefined;
  nodeEditMode.value = "creating";
  exitEditMode();
};

/**
 * @function handlePipeLineBreakSave
 * @description 处理打断线保存操作
 */
const handlePipeLineBreakSave = async (nodeData: any): Promise<void> => {
  try {
    if (!breakPoint.value || !selectedPipelineForBreak.value) {
      throw new Error("打断点或管线信息不完整");
    }

    // 获取管线gid
    const pipelineGid = selectedPipelineForBreak.value.gid;
    if (!pipelineGid) {
      throw new Error("管线缺少GID信息");
    }

    // 构造打断线请求数据（使用计算后的精确坐标）
    const breakData = {
      gid: pipelineGid, // 管线GID
      gl: nodeData.gl, // 管类
      sx: nodeData.sx, // 属性
      fsw: nodeData.fsw, // 附属物
      dmgc: nodeData.dmgc || 0, // 地面高程
      js: nodeData.js || 0, // 井深
      jggg: nodeData.jggg, // 井盖规格
      jgcz: nodeData.jgcz, // 井盖材质
      szdl: nodeData.szdl, // 所在道路
      longitude: breakPoint.value.lng, // 计算后的精确经度
      latitude: breakPoint.value.lat, // 计算后的精确纬度
      x: breakPoint.value.lng, // 计算后的精确X坐标
      y: breakPoint.value.lat, // 计算后的精确Y坐标
    };

    console.log("调用打断线接口，请求数据:", breakData);

    // 调用打断线API
    const response = await pipeLineBreak(breakData);

    if (response.code === 200) {
      console.log("打断线操作成功:", response.data);
      ElMessage.success("管线打断成功");

      // 更新地图图层
      handleUpdatePipeLayer();

      // 清除打断点图层
      removeBreakPointLayer();
    } else {
      throw new Error(response.msg || "打断线操作失败");
    }
  } catch (error) {
    console.error("打断线保存失败:", error);
    throw error;
  }
};

/**
 * @function handleNodeDeleted
 * @description 处理管点删除成功
 */
const handleNodeDeleted = (nodeId: string): void => {
  console.log("管点删除成功:", nodeId);
  ElMessage.success("管点删除成功");
  showNodePanel.value = false;
  // 清理状态
  tempClickCoordinates.value = null;
  currentNodeData.value = undefined;
  nodeEditMode.value = "creating";
  exitEditMode();
};

/**
 * @function handleChangeNodePosition
 * @description 处理修改管点位置
 */
const handleChangeNodePosition = (): void => {
  // 现在由PipeNodePanel内部处理位置修改
  // 不需要关闭面板或重新启动绘制模式
  console.log("管点位置修改事件已触发，由PipeNodePanel内部处理");
};

/**
 * @function handleUpdatePipeLayer
 * @description 处理更新管网图层
 */
const handleUpdatePipeLayer = (): void => {
  console.log("通知更新管网图层（矢量瓦片将自动同步）");
  // 在新架构中，矢量瓦片会自动同步数据库变更，无需手动刷新
  try {
    const layerManager = AppMaplibre.getLayerManager();
    if (layerManager && layerManager.refreshMvt) {
      layerManager.refreshMvt();
    } else {
      console.warn("LayerManager或refreshMvt方法不可用");
    }
  } catch (error) {
    console.error("刷新MVT图层失败:", error);
  }
};

/**
 * @function handleLinePanelClose
 * @description 处理管线面板关闭
 */
const handleLinePanelClose = (): void => {
  const wasViewingDetail = currentEditMode.value === "viewing-detail";

  showLinePanel.value = false;

  // 根据当前模式决定如何重置状态
  if (wasViewingDetail) {
    // 查看详情模式：重置为空闲状态并保持要素查看功能
    currentEditMode.value = "idle";
    lineEditMode.value = "creating";
    currentLineData.value = undefined;
    selectedStartPoint.value = null;
    selectedEndPoint.value = null;
    selectingTarget.value = null;
    console.log("✅ 管线详情面板已关闭，返回要素查看模式");
  } else if (lineEditMode.value === "creating") {
    // 新建模式：完全重置绘制状态
    exitEditMode();
  } else {
    // 编辑模式：清理临时图层并重置编辑状态
    if (pipeLineTempManager) {
      pipeLineTempManager.clearAll();
      console.log("📍 编辑模式关闭，已清理临时图层");
    }

    // 重置编辑模式相关状态
    currentLineData.value = undefined;
    lineEditMode.value = "creating";
    selectedStartPoint.value = null;
    selectedEndPoint.value = null;
    selectingTarget.value = null;
    currentEditMode.value = "idle";

    // 解绑地图事件
    unbindMapClickEvent();

    // 重置鼠标样式
    if (map) {
      map.getCanvas().style.cursor = "";
    }

    // 重新启用要素查看功能
    enableDetailViewMode();

    console.log("📍 编辑模式面板关闭，已解绑所有事件并重置状态");
  }
};

/**
 * @function handleLineSaved
 * @description 处理管线保存成功
 */
const handleLineSaved = (line: any): void => {
  console.log("管线保存成功:", line);
  ElMessage.success("管线保存成功");

  // 检查当前是否为编辑模式
  const isEditingMode = lineEditMode.value === "editing";

  // 重置管线编辑状态
  showLinePanel.value = false;
  currentLineData.value = undefined;
  lineEditMode.value = "creating";

  // 清理临时图层
  if (pipeLineTempManager) {
    pipeLineTempManager.clearAll();
    console.log("📍 管线保存完成，已清理临时图层");
  }

  // 刷新图层
  handleUpdatePipeLayer();

  // 统一处理状态重置和事件解绑（无论新建还是编辑模式）
  selectedStartPoint.value = null;
  selectedEndPoint.value = null;
  selectingTarget.value = null;
  currentEditMode.value = "idle";

  // 解绑地图事件
  unbindMapClickEvent();

  // 重置鼠标样式
  if (map) {
    map.getCanvas().style.cursor = "";
  }

  // 重新启用要素查看功能
  enableDetailViewMode();

  console.log("📍 管线保存完成，已解绑所有事件并重置状态");
};

/**
 * @function handleLineDeleted
 * @description 处理管线删除成功
 */
const handleLineDeleted = (lineId: string): void => {
  console.log("管线删除成功:", lineId);
  ElMessage.success("管线删除成功");

  // 重置管线编辑状态
  showLinePanel.value = false;
  currentLineData.value = undefined;
  lineEditMode.value = "creating";

  // 清理临时图层
  if (pipeLineTempManager) {
    pipeLineTempManager.clearAll();
    console.log("📍 管线删除完成，已清理临时图层");
  }

  // 重置状态变量
  selectedStartPoint.value = null;
  selectedEndPoint.value = null;
  selectingTarget.value = null;
  currentEditMode.value = "idle";

  // 解绑地图事件
  unbindMapClickEvent();

  // 重置鼠标样式
  if (map) {
    map.getCanvas().style.cursor = "";
  }

  // 刷新图层
  handleUpdatePipeLayer();

  // 重新启用要素查看功能
  enableDetailViewMode();

  console.log("📍 管线删除完成，已解绑所有事件并重置状态");
};

/**
 * @function handleSelectStartNode
 * @description 处理选择起始点
 */
const handleSelectStartNode = (): void => {
  ElMessage.info("请在地图上点击选择起始管点");

  // 设置选择目标为起点
  selectingTarget.value = "start";

  // 进入管线绘制模式并重新绑定点击事件
  currentEditMode.value = "drawing-line";
  bindMapClickForLineDrawing();
};

/**
 * @function handleSelectEndNode
 * @description 处理选择终止点
 */
const handleSelectEndNode = (): void => {
  ElMessage.info("请在地图上点击选择终止管点");

  // 设置选择目标为终点
  selectingTarget.value = "end";

  // 进入管线绘制模式并重新绑定点击事件
  currentEditMode.value = "drawing-line";
  bindMapClickForLineDrawing();
};

/**
 * @function handleLineChangePosition
 * @description 处理管线重新绘制
 */
const handleLineChangePosition = (): void => {
  const isEditingMode = lineEditMode.value === "editing";
  const originalLineData = currentLineData.value;

  if (isEditingMode) {
    ElMessage.info("开始重新绘制管线，请重新选择起始点和终止点（编辑模式）");
    console.log("🔧 编辑模式下的管线重新绘制", { originalLineData });
  } else {
    ElMessage.info("开始重新绘制管线，请选择起始点和终止点（新增模式）");
    console.log("🆕 新增模式下的管线重新绘制");
  }

  // 关闭管线面板
  showLinePanel.value = false;

  // 根据当前模式决定状态处理
  if (isEditingMode) {
    // 编辑模式：保持编辑状态和管线数据，只重置选择的点
    // lineEditMode.value 保持为 'editing'
    // currentLineData.value 保持原值
    console.log("📝 编辑模式：保持lineEditMode=editing，保留currentLineData");
  } else {
    // 新增模式：重置为创建状态
    lineEditMode.value = "creating";
    currentLineData.value = undefined;
    console.log("🆕 新增模式：设置lineEditMode=creating，清空currentLineData");
  }

  // 重置选择的起止点（编辑和新增模式都需要重新选择）
  selectedStartPoint.value = null;
  selectedEndPoint.value = null;
  selectingTarget.value = null;

  // 清除所有临时显示
  if (pipeLineTempManager) {
    pipeLineTempManager.clearAll();
    console.log("📍 重新绘制模式：已清理临时图层");
  }

  // 启动管线绘制模式
  currentEditMode.value = "drawing-line";

  // 设置地图鼠标样式
  if (map) {
    map.getCanvas().style.cursor = "crosshair";
  }

  // 绑定地图点击事件
  bindMapClickForLineDrawing();

  console.log("管线重新绘制模式已启动", {
    mode: isEditingMode ? "editing" : "creating",
    lineEditMode: lineEditMode.value,
    hasCurrentLineData: !!currentLineData.value,
  });
};

// ============ 右键菜单功能 ============

/**
 * @function bindRightClickEvent
 * @description 绑定右键事件监听器
 */
const bindRightClickEvent = (): void => {
  if (!map) return;

  try {
    console.log("🔗 开始绑定右键事件");

    // 等待图层加载完成
    setTimeout(() => {
      // 检查管点图层是否存在
      const mvtPipeNodeLayer = map!.getLayer("mvt_pipeNode");
      if (!mvtPipeNodeLayer) {
        console.warn(
          "⚠️ 管点图层 mvt_pipeNode 不存在，右键功能可能无法正常工作"
        );

        // 监听数据源加载事件
        const sourceDataHandler = (e: any) => {
          if (e.sourceId === "mvt_pipeNode" && e.isSourceLoaded) {
            const layer = map!.getLayer("mvt_pipeNode");
            if (layer) {
              console.log("✅ 管点图层已加载，重新绑定右键事件");
              map!.off("sourcedata", sourceDataHandler); // 移除临时监听器
            }
          }
        };
        map!.on("sourcedata", sourceDataHandler);
      } else {
        console.log(
          "✅ 管点图层 mvt_pipeNode 已找到，图层类型:",
          mvtPipeNodeLayer.type
        );
      }

      // 绑定右键事件
      map!.on("contextmenu", rightClickHandler);
      console.log("✅ 右键事件监听器已绑定");
    }, 500); // 延迟500ms确保图层加载
  } catch (error) {
    console.error("❌ 绑定右键事件失败:", error);
  }
};

/**
 * @function rightClickHandler
 * @description 处理地图右键点击事件
 */
const rightClickHandler = (e: MapMouseEvent) => {
  console.log("🖱️ 地图右键点击事件触发:", {
    point: e.point,
    lngLat: e.lngLat,
  });

  try {
    // 查询点击位置的所有要素
    const allFeatures = map!.queryRenderedFeatures([
      [e.point.x - offset / 2, e.point.y - offset / 2],
      [e.point.x + offset / 2, e.point.y + offset / 2],
    ]);
    console.log("🔍 点击位置的所有要素:", allFeatures);

    // 按优先级检测要素：先检测管点，再检测管线
    // 1. 查询管点要素
    const nodeFeatures = map!.queryRenderedFeatures(
      [
        [e.point.x - offset / 2, e.point.y - offset / 2],
        [e.point.x + offset / 2, e.point.y + offset / 2],
      ],
      {
        layers: ["mvt_pipeNode"], // 管点图层ID
      }
    );

    if (nodeFeatures.length > 0) {
      const nodeFeature = nodeFeatures[0];
      console.log("✅ 选中的管点要素:", nodeFeature);

      // 设置管点右键菜单
      setContextMenuForFeatureType("node", nodeFeature.properties);

      // 设置菜单位置
      contextMenu.position = {
        x: e.originalEvent.clientX,
        y: e.originalEvent.clientY,
      };

      // 显示上下文菜单
      contextMenu.visible = true;

      console.log(
        "📋 管点右键菜单已显示，位置:",
        contextMenu.position,
        "数据:",
        nodeFeature.properties
      );
      return;
    }

    // 2. 查询管线要素
    const lineFeatures = map!.queryRenderedFeatures(
      [
        [e.point.x - offset / 2, e.point.y - offset / 2],
        [e.point.x + offset / 2, e.point.y + offset / 2],
      ],
      {
        layers: ["mvt_pipeLine"], // 管线图层ID
      }
    );

    if (lineFeatures.length > 0) {
      const lineFeature = lineFeatures[0];
      console.log("✅ 选中的管线要素:", lineFeature);

      // 设置管线右键菜单
      setContextMenuForFeatureType("line", lineFeature.properties);

      // 设置菜单位置
      contextMenu.position = {
        x: e.originalEvent.clientX,
        y: e.originalEvent.clientY,
      };

      // 显示上下文菜单
      contextMenu.visible = true;

      console.log(
        "📋 管线右键菜单已显示，位置:",
        contextMenu.position,
        "数据:",
        lineFeature.properties
      );
      return;
    }

    // 3. 没有找到任何要素
    console.log("❌ 未找到管网要素");
    // 如果没有点击到任何要素，关闭可能已打开的菜单
    if (contextMenu.visible) {
      closeContextMenu();
    }
  } catch (error) {
    console.error("❌ 处理右键点击失败:", error);
    ElMessage.error("右键操作失败");
  }
};

/**
 * @function setContextMenuForFeatureType
 * @description 根据要素类型设置右键菜单
 * @param featureType 要素类型
 * @param featureData 要素数据
 */
const setContextMenuForFeatureType = (
  featureType: "node" | "line",
  featureData: any
): void => {
  contextMenu.featureType = featureType;

  if (featureType === "node") {
    contextMenu.selectedNodeData = featureData;
    contextMenu.selectedLineData = null;
    contextMenu.menuItems = [...nodeMenuItems];
    console.log("🎯 设置管点右键菜单:", featureData);
  } else if (featureType === "line") {
    contextMenu.selectedLineData = featureData;
    contextMenu.selectedNodeData = null;
    contextMenu.menuItems = [...lineMenuItems];
    console.log("🎯 设置管线右键菜单:", featureData);
  }
};

/**
 * @function closeContextMenu
 * @description 关闭右键菜单
 */
const closeContextMenu = (): void => {
  contextMenu.visible = false;
  contextMenu.selectedNodeData = null;
  contextMenu.selectedLineData = null;
  contextMenu.featureType = null;
  contextMenu.menuItems = [];
};

/**
 * @function handleContextMenuClick
 * @description 处理右键菜单项点击
 */
const handleContextMenuClick = (item: MenuItem): void => {
  console.log("📱 右键菜单项点击:", item, "要素类型:", contextMenu.featureType);
  switch (item.key) {
    // 管点相关操作
    case "edit-node":
      handleEditNode();
      break;
    case "delete-node":
      handleDeleteNode();
      break;

    // 管线相关操作
    case "edit-line":
      handleEditPipeLine();
      break;
    case "delete-line":
      handleDeletePipeLine();
      break;

    // 兼容旧版本（可选，用于向后兼容）
    case "edit":
      if (contextMenu.featureType === "node") {
        handleEditNode();
      } else {
        console.warn("旧版本菜单项edit，但要素类型不明确");
      }
      break;
    case "delete":
      if (contextMenu.featureType === "node") {
        handleDeleteNode();
      } else {
        console.warn("旧版本菜单项delete，但要素类型不明确");
      }
      break;

    default:
      console.warn("未知的菜单项:", item);
  }
};

/**
 * @function handleEditNode
 * @description 处理编辑管点
 */
const handleEditNode = async (): Promise<void> => {
  try {
    if (!contextMenu.selectedNodeData) {
      ElMessage.error("未选择管点数据");
      return;
    }

    console.log("✏️ 开始编辑管点:", contextMenu.selectedNodeData);

    const nodeData = contextMenu.selectedNodeData;

    // 提取管点坐标（尝试多种字段名）
    let longitude = 0;
    let latitude = 0;

    // 优先使用longitude/latitude字段
    if (nodeData.longitude && nodeData.latitude) {
      longitude = parseFloat(nodeData.longitude);
      latitude = parseFloat(nodeData.latitude);
    }
    // 备用：使用x/y字段
    else if (nodeData.x && nodeData.y) {
      longitude = parseFloat(nodeData.x);
      latitude = parseFloat(nodeData.y);
    }
    // 如果都没有，尝试从geometry或其他字段获取
    else {
      console.warn("管点坐标信息不完整:", nodeData);
      ElMessage.warning("管点坐标信息不完整，某些功能可能不可用");
    }

    console.log("📍 提取的管点坐标:", { longitude, latitude });

    // 设置当前管点数据
    currentNodeData.value = nodeData;

    // 设置管点坐标给临时坐标，这样PipeNodePanel可以生成临时管点
    if (longitude && latitude) {
      tempClickCoordinates.value = [longitude, latitude];
    }

    // 设置为编辑模式
    nodeEditMode.value = "editing";
    currentEditMode.value = "editing";

    // 显示编辑面板
    showNodePanel.value = true;

    // 关闭右键菜单
    closeContextMenu();

    ElMessage.success("管点数据已加载到表单，可以开始编辑");
    console.log("🎯 管点编辑模式已激活，数据:", {
      nodeData,
      coordinates: tempClickCoordinates.value,
      nodeGid: nodeData.gid || nodeData.nodeGid,
    });
  } catch (error) {
    console.error("❌ 编辑管点失败:", error);
    ElMessage.error("编辑管点失败");
  }
};

/**
 * @function handleDeleteNode
 * @description 处理删除管点
 */
const handleDeleteNode = async (): Promise<void> => {
  try {
    if (!contextMenu.selectedNodeData) {
      ElMessage.error("未选择管点数据");
      return;
    }

    const nodeData = contextMenu.selectedNodeData;
    const nodeGid = nodeData.gid || nodeData.nodeGid;
    const nodeCode = nodeData.gxddh || nodeData.nodeCode || "未知";

    if (!nodeGid) {
      ElMessage.error("管点缺少必要的ID信息，无法删除");
      return;
    }
    const { code, data } = await pipeNodeIsHangPt(nodeCode);
    if (code === 200 && !data) {
      ElMessage.error("管点不是悬挂点，无法删除！请先删除管线。");
      return;
    }

    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除管点 "${nodeCode}" 吗？此操作不可撤销。`,
      "确认删除",
      {
        confirmButtonText: "确定删除",
        cancelButtonText: "取消",
        type: "warning",
        confirmButtonClass: "el-button--danger",
      }
    );

    console.log("🗑️ 开始删除管点:", nodeGid, nodeCode);
    // 调用删除API
    const apiResponse = await pipeNodeDelete(nodeGid);

    if (apiResponse && apiResponse.code === 200) {
      console.log("✅ 管点删除成功");
      ElMessage.success(`管点 "${nodeCode}" 删除成功`);

      // 关闭右键菜单
      closeContextMenu();
      //更新mvt图层
      handleUpdatePipeLayer();
      // 如果当前正在编辑这个管点，关闭编辑面板
      if (currentNodeData.value && currentNodeData.value.gid === nodeGid) {
        showNodePanel.value = false;
        currentNodeData.value = undefined;
      }
    } else {
      throw new Error(apiResponse?.msg || "删除失败");
    }
  } catch (error: any) {
    if (error.message !== "cancel") {
      // 用户取消删除时不显示错误
      console.error("❌ 删除管点失败:", error);
      ElMessage.error(`删除失败: ${error.message || "未知错误"}`);
    }
  }
};

/**
 * @function handleEditPipeLine
 * @description 处理编辑管线
 */
const handleEditPipeLine = async (): Promise<void> => {
  try {
    if (!contextMenu.selectedLineData) {
      ElMessage.error("未选择管线数据");
      return;
    }

    console.log("✏️ 开始编辑管线:", contextMenu.selectedLineData);

    const lineData = contextMenu.selectedLineData;
    const lineGid = lineData.gid;

    if (!lineGid) {
      ElMessage.error("管线缺少必要的ID信息，无法编辑");
      return;
    }

    // 查询管线详情
    const lineDetailResponse = await pipeLineDetail(lineGid);
    if (lineDetailResponse.code !== 200 || !lineDetailResponse.data) {
      throw new Error(lineDetailResponse.msg || "获取管线详情失败");
    }

    console.log("📄 获取到管线详情:", lineDetailResponse.data);

    // 设置管线编辑状态
    currentLineData.value = lineDetailResponse.data;
    lineEditMode.value = "editing";

    // 在临时图层中显示被编辑的管线
    if (pipeLineTempManager) {
      pipeLineTempManager.setLineForEdit(lineDetailResponse.data);
      console.log("📍 已在临时图层中显示编辑管线");
    }
    console.log(currentEditMode.value);
    console.log(lineEditMode.value);
    // 打开管线编辑面板
    showLinePanel.value = true;

    // 关闭右键菜单
    closeContextMenu();

    ElMessage.success("管线数据已加载，可以开始编辑");
    console.log("🎯 管线编辑模式已激活，数据:", lineDetailResponse.data);
  } catch (error) {
    console.error("❌ 编辑管线失败:", error);
    ElMessage.error(
      `编辑管线失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};

/**
 * @function handleDeletePipeLine
 * @description 处理删除管线
 */
const handleDeletePipeLine = async (): Promise<void> => {
  try {
    if (!contextMenu.selectedLineData) {
      ElMessage.error("未选择管线数据");
      return;
    }

    const lineData = contextMenu.selectedLineData;
    const lineGid = lineData.gid;
    const lineCode = lineData.gxbm || lineData.lineCode || lineGid || "未知";

    if (!lineGid) {
      ElMessage.error("管线缺少必要的ID信息，无法删除");
      return;
    }

    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除管线 "${lineCode}" 吗？此操作不可撤销。`,
      "确认删除",
      {
        confirmButtonText: "确定删除",
        cancelButtonText: "取消",
        type: "warning",
        confirmButtonClass: "el-button--danger",
      }
    );

    console.log("🗑️ 开始删除管线:", lineGid, lineCode);
    // 调用删除API
    const apiResponse = await pipeLineDelete(lineGid);

    if (apiResponse && apiResponse.code === 200) {
      console.log("✅ 管线删除成功");
      ElMessage.success(`管线 "${lineCode}" 删除成功`);

      // 关闭右键菜单
      closeContextMenu();

      // 更新mvt图层
      handleUpdatePipeLayer();

      // 如果当前正在编辑这条管线，关闭编辑面板
      if (showLinePanel.value) {
        showLinePanel.value = false;
      }
    } else {
      throw new Error(apiResponse?.msg || "删除失败");
    }
  } catch (error: any) {
    if (error.message !== "cancel") {
      // 用户取消删除时不显示错误
      console.error("❌ 删除管线失败:", error);
      ElMessage.error(`删除失败: ${error.message || "未知错误"}`);
    }
  }
};

// ============ 要素查看功能 ============

/**
 * @function enableDetailViewMode
 * @description 启用要素查看模式（左键点击查看详情）
 */
const enableDetailViewMode = (): void => {
  try {
    if (!map) {
      console.warn("地图实例不可用，无法启用要素查看功能");
      return;
    }
    // 创建左键点击事件处理器
    const clickHandler = (e: MapMouseEvent) => {
      // 只在空闲状态或查看详情状态下响应左键点击
      if (
        currentEditMode.value !== "idle" &&
        currentEditMode.value !== "viewing-detail"
      ) {
        return;
      }

      handleFeatureDetailClick(e);
    };

    // 保存处理器引用
    detailClickHandler.value = clickHandler;

    // 绑定左键点击事件
    map.on("click", clickHandler);

    // 设置查看详情状态
    isViewingDetail.value = true;

    console.log("✅ 要素查看功能已启用");
  } catch (error) {
    console.error("❌ 启用要素查看功能失败:", error);
  }
};

/**
 * @function disableDetailViewMode
 * @description 禁用要素查看模式
 */
const disableDetailViewMode = (): void => {
  try {
    if (map && detailClickHandler.value) {
      map.off("click", detailClickHandler.value);
      detailClickHandler.value = null;
    }
    showNodePanel.value = false;
    isViewingDetail.value = false;
    console.log("✅ 要素查看功能已禁用");
  } catch (error) {
    console.error("❌ 禁用要素查看功能失败:", error);
  }
};

/**
 * @function handleFeatureDetailClick
 * @description 处理要素详情查看点击
 */
const handleFeatureDetailClick = async (e: MapMouseEvent): Promise<void> => {
  try {
    console.log("🔍 要素详情查看点击:", { point: e.point, lngLat: e.lngLat });

    // 按优先级查询要素：先管点，后管线
    // 1. 查询管点要素
    const nodeFeatures = map!.queryRenderedFeatures(
      [
        [e.point.x - offset / 2, e.point.y - offset / 2],
        [e.point.x + offset / 2, e.point.y + offset / 2],
      ],
      {
        layers: ["mvt_pipeNode"], // 管点图层ID
      }
    );

    if (nodeFeatures.length > 0) {
      const nodeFeature = nodeFeatures[0];
      await handleViewNodeDetail(nodeFeature.properties);
      return;
    }
    // 2. 查询管线要素
    const lineFeatures = map!.queryRenderedFeatures(
      [
        [e.point.x - offset / 2, e.point.y - offset / 2],
        [e.point.x + offset / 2, e.point.y + offset / 2],
      ],
      {
        layers: ["mvt_pipeLine"], // 管线图层ID
      }
    );

    if (lineFeatures.length > 0) {
      const lineFeature = lineFeatures[0];
      await handleViewLineDetail(lineFeature.properties);
      return;
    }

    // 没有点击到任何要素，如果当前有打开的详情面板，则关闭
    if (currentEditMode.value === "viewing-detail") {
      closeDetailPanels();
    }
  } catch (error) {
    console.error("❌ 处理要素详情查看失败:", error);
    ElMessage.error("查看要素详情失败");
  }
};

/**
 * @function handleViewNodeDetail
 * @description 处理查看管点详情
 */
const handleViewNodeDetail = async (nodeData: any): Promise<void> => {
  try {
    if (!nodeData || !nodeData.gid) {
      ElMessage.warning("管点数据不完整，无法查看详情");
      return;
    }

    console.log("👁️ 查看管点详情:", nodeData);

    // 查询管点详情数据
    const nodeDetail = await queryNodeDetail(nodeData.gid);
    if (!nodeDetail) {
      ElMessage.error("获取管点详情失败");
      return;
    }

    // 设置查看模式
    currentEditMode.value = "viewing-detail";
    nodeEditMode.value = "viewing";
    currentNodeData.value = nodeDetail;

    // 设置坐标（用于显示临时点）
    if (nodeDetail.longitude && nodeDetail.latitude) {
      tempClickCoordinates.value = [nodeDetail.longitude, nodeDetail.latitude];
    }

    // 关闭其他面板
    showLinePanel.value = false;

    // 显示管点详情面板
    showNodePanel.value = true;

    console.log("✅ 管点详情面板已打开（查看模式）");
  } catch (error) {
    console.error("❌ 查看管点详情失败:", error);
    ElMessage.error(
      `查看管点详情失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};

/**
 * @function handleViewLineDetail
 * @description 处理查看管线详情
 */
const handleViewLineDetail = async (lineData: any): Promise<void> => {
  try {
    if (!lineData || !lineData.gid) {
      ElMessage.warning("管线数据不完整，无法查看详情");
      return;
    }

    console.log("👁️ 查看管线详情:", lineData);

    // 查询管线详情数据
    const lineDetailResponse = await pipeLineDetail(lineData.gid);
    if (lineDetailResponse.code !== 200 || !lineDetailResponse.data) {
      throw new Error(lineDetailResponse.msg || "获取管线详情失败");
    }

    // 设置查看模式
    currentEditMode.value = "viewing-detail";
    lineEditMode.value = "viewing";
    currentLineData.value = lineDetailResponse.data;

    // 清理选择状态
    selectedStartPoint.value = null;
    selectedEndPoint.value = null;

    // 关闭其他面板
    showNodePanel.value = false;

    // 显示管线详情面板
    showLinePanel.value = true;

    console.log("✅ 管线详情面板已打开（查看模式）");
  } catch (error) {
    console.error("❌ 查看管线详情失败:", error);
    ElMessage.error(
      `查看管线详情失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};

/**
 * @function closeDetailPanels
 * @description 关闭详情面板
 */
const closeDetailPanels = (): void => {
  try {
    // 关闭所有面板
    showNodePanel.value = false;
    showLinePanel.value = false;

    // 重置状态
    currentEditMode.value = "idle";
    nodeEditMode.value = "creating";
    lineEditMode.value = "creating";
    currentNodeData.value = undefined;
    currentLineData.value = undefined;
    tempClickCoordinates.value = null;

    console.log("✅ 详情面板已关闭");
  } catch (error) {
    console.error("❌ 关闭详情面板失败:", error);
  }
};

// ============ 辅助方法 ============

/**
 * @function getLineDrawButtonText
 * @description 获取管线绘制按钮文本
 */
const getLineDrawButtonText = (): string => {
  if (currentEditMode.value === "drawing-line") {
    if (!selectedStartPoint.value) {
      return "选择起始点...";
    } else if (!selectedEndPoint.value) {
      return "选择终止点...";
    } else {
      return "填写管线信息";
    }
  }
  return "绘制管线";
};

/**
 * @function getLineDrawingStep
 * @description 获取管线绘制步骤
 */
const getLineDrawingStep = (): number => {
  if (!selectedStartPoint.value) return 0;
  if (!selectedEndPoint.value) return 1;
  return 2;
};

/**
 * @function cleanup
 * @description 清理资源
 */
const cleanup = (): void => {
  unbindMapClickEvent();
  unbindBreakPointEvents(); // 清理打断线模式的事件监听器

  // 禁用要素查看功能
  disableDetailViewMode();

  // 销毁管线临时图层管理器
  if (pipeLineTempManager) {
    pipeLineTempManager.destroy();
    pipeLineTempManager = null;
  }

  // 清理打断线相关图层
  removeBreakPointLayer();
  hideBreakHintPoint();

  // 解绑右键事件
  if (map) {
    map.off("contextmenu", rightClickHandler);
    map.getCanvas().style.cursor = "";
  }

  // 关闭右键菜单
  closeContextMenu();

  console.log("资源清理完成");
};

/**
 * @function showBreakHintPoint
 * @description 显示打断点提示点
 */
const showBreakHintPoint = (lngLat: any) => {
  try {
    if (!map) return;

    // 移除已存在的提示点
    hideBreakHintPoint();

    // 添加提示点数据源
    if (!map.getSource("break-hint-point")) {
      map.addSource("break-hint-point", {
        type: "geojson",
        data: {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [lngLat.lng, lngLat.lat],
          },
          properties: {},
        },
      });
    } else {
      // 更新提示点位置
      const source = map.getSource("break-hint-point") as any;
      if (source && source.setData) {
        source.setData({
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [lngLat.lng, lngLat.lat],
          },
          properties: {},
        });
      }
    }

    // 添加提示点图层
    if (!map.getLayer("break-hint-point-layer")) {
      map.addLayer({
        id: "break-hint-point-layer",
        type: "circle",
        source: "break-hint-point",
        paint: {
          "circle-radius": 8,
          "circle-color": "#fa8c16",
          "circle-opacity": 0.8,
          "circle-stroke-width": 2,
          "circle-stroke-color": "#ffffff",
          "circle-stroke-opacity": 1,
        },
      });
    }
  } catch (error) {
    console.error("显示打断点提示点失败:", error);
  }
};

/**
 * @function hideBreakHintPoint
 * @description 隐藏打断点提示点
 */
const hideBreakHintPoint = () => {
  try {
    if (!map) return;

    // 移除提示点图层
    if (map.getLayer("break-hint-point-layer")) {
      map.removeLayer("break-hint-point-layer");
    }

    // 移除提示点数据源
    if (map.getSource("break-hint-point")) {
      map.removeSource("break-hint-point");
    }
  } catch (error) {
    console.error("隐藏打断点提示点失败:", error);
  }
};

/**
 * @function addBreakPointLayer
 * @description 添加打断点显示图层
 */
const addBreakPointLayer = (lngLat: any, text: string) => {
  try {
    if (!map) return;

    // 先移除已存在的打断点图层
    removeBreakPointLayer();

    // 添加打断点数据源
    map.addSource("break-point-source", {
      type: "geojson",
      data: {
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [lngLat.lng, lngLat.lat],
        },
        properties: {},
      },
    });

    // 添加打断点外圈图层（橙色圆圈）
    map.addLayer({
      id: "break-point-outer",
      type: "circle",
      source: "break-point-source",
      paint: {
        "circle-radius": 12,
        "circle-color": "#fa8c16",
        "circle-opacity": 0.6,
        "circle-stroke-width": 0,
      },
    });

    // 添加打断点内圈图层（白色圆圈）
    map.addLayer({
      id: "break-point-inner",
      type: "circle",
      source: "break-point-source",
      paint: {
        "circle-radius": 6,
        "circle-color": "#ffffff",
        "circle-opacity": 1,
        "circle-stroke-width": 2,
        "circle-stroke-color": "#fa8c16",
        "circle-stroke-opacity": 1,
      },
    });

    // 添加打断点中心点图层（橙色小点）
    map.addLayer({
      id: "break-point-center",
      type: "circle",
      source: "break-point-source",
      paint: {
        "circle-radius": 2,
        "circle-color": "#fa8c16",
        "circle-opacity": 1,
      },
    });

    // 添加打断点文字标签图层
    map.addLayer({
      id: "break-point-label",
      type: "symbol",
      source: "break-point-source",
      layout: {
        "text-field": text || "打断点",
        "text-font": ["Open Sans Regular"],
        "text-size": 12,
        "text-offset": [0, 2],
        "text-anchor": "top",
      },
      paint: {
        "text-color": "#fa8c16",
        "text-halo-color": "#ffffff",
        "text-halo-width": 1,
      },
    });

    console.log("打断点图层已添加");
  } catch (error) {
    console.error("添加打断点图层失败:", error);
  }
};

/**
 * @function removeBreakPointLayer
 * @description 移除打断点显示图层
 */
const removeBreakPointLayer = () => {
  try {
    if (!map) return;

    // 移除所有打断点相关图层
    const layerIds = [
      "break-point-label",
      "break-point-center",
      "break-point-inner",
      "break-point-outer",
    ];
    layerIds.forEach((layerId) => {
      if (map!.getLayer(layerId)) {
        map!.removeLayer(layerId);
      }
    });

    // 移除打断点数据源
    if (map.getSource("break-point-source")) {
      map.removeSource("break-point-source");
    }

    console.log("打断点图层已移除");
  } catch (error) {
    console.error("移除打断点图层失败:", error);
  }
};

/**
 * @function handleBreakPointSelected
 * @description 处理选择打断点后的逻辑
 */
const handleBreakPointSelected = (): void => {
  try {
    if (!breakPoint.value) {
      ElMessage.error("打断点信息不完整");
      return;
    }

    // 设置临时坐标（使用计算后的精确坐标）
    tempClickCoordinates.value = [breakPoint.value.lng, breakPoint.value.lat];

    // 设置为新建管点模式
    nodeEditMode.value = "creating";
    currentNodeData.value = undefined;

    // 显示管点编辑面板，传递精确坐标
    showNodePanel.value = true;

    console.log("打断点已选择，弹出管点编辑弹框:", {
      precisedCoordinates: tempClickCoordinates.value,
      originalClickCoordinates: [
        breakPoint.value.clickLng,
        breakPoint.value.clickLat,
      ],
      pipelineInfo: selectedPipelineForBreak.value,
    });

    // ElMessage.success("请填写新增管点信息（已使用精确坐标）");
  } catch (error) {
    console.error("处理选择打断点失败:", error);
    ElMessage.error("处理选择打断点失败");
  }
};

/**
 * @function calculateNearestPointOnLine
 * @description 计算管线上最近的点
 * @param clickPoint 点击点坐标
 * @param lineGeometry 管线几何对象
 * @returns 管线上最近的点坐标 {lng, lat}
 */
const calculateNearestPointOnLine = (
  clickPoint: any,
  lineGeometry: any
): { lng: number; lat: number } => {
  try {
    // 创建点要素
    const point = turf.point([clickPoint.lng, clickPoint.lat]);

    // 根据几何类型处理
    let line;
    if (lineGeometry.type === "LineString") {
      line = turf.lineString(lineGeometry.coordinates);
    } else if (lineGeometry.type === "MultiLineString") {
      // 对于MultiLineString，找到距离最近的线段
      let minDistance = Infinity;
      let nearestPoint = null;

      for (const coordinates of lineGeometry.coordinates) {
        const currentLine = turf.lineString(coordinates);
        const currentNearestPoint = turf.nearestPointOnLine(currentLine, point);
        const distance = turf.distance(point, currentNearestPoint, {
          units: "meters",
        });

        if (distance < minDistance) {
          minDistance = distance;
          nearestPoint = currentNearestPoint;
        }
      }

      if (nearestPoint) {
        return {
          lng: nearestPoint.geometry.coordinates[0],
          lat: nearestPoint.geometry.coordinates[1],
        };
      }
    } else {
      throw new Error(`不支持的几何类型: ${lineGeometry.type}`);
    }

    // 确保line变量已定义
    if (!line) {
      throw new Error("无法创建线要素");
    }

    // 计算线上最近点
    const nearestPoint = turf.nearestPointOnLine(line, point);

    return {
      lng: nearestPoint.geometry.coordinates[0],
      lat: nearestPoint.geometry.coordinates[1],
    };
  } catch (error) {
    console.error("计算管线最近点失败:", error);
    throw new Error(
      `几何计算失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};

/**
 * @function handleTwoPointHang
 * @description 处理两点栓点功能
 */
const handleTwoPointHang = (): void => {
  try {
    if (currentEditMode.value === "two-point-hang") {
      // 已经在两点栓点模式，退出
      exitEditMode();
      return;
    }

    // 🔧 新增：如果有其他功能激活，先清理
    if (currentEditMode.value !== "idle") {
      exitEditMode();
    }

    // 切换到两点栓点模式
    currentEditMode.value = "two-point-hang";
    showTwoPointHangPanel.value = true;

    // ElMessage.info("已进入两点栓点模式，请在面板中操作");
  } catch (error) {
    console.error("启动两点栓点模式失败:", error);
    ElMessage.error(
      `启动两点栓点模式失败: ${
        error instanceof Error ? error.message : "未知错误"
      }`
    );
    exitEditMode();
  }
};

/**
 * @function handleTwoPointHangAddNode
 * @description 处理两点栓点新增管点事件
 * @param data 栓点数据
 */
const handleTwoPointHangAddNode = (data: any): void => {
  try {
    console.log("两点栓点新增管点:", data);

    // 设置临时坐标
    tempClickCoordinates.value = [data.lng, data.lat];

    // 设置为新建管点模式
    nodeEditMode.value = "creating";
    currentNodeData.value = undefined;

    // 显示管点编辑面板
    showNodePanel.value = true;

    // ElMessage.success("请填写新增管点信息");
  } catch (error) {
    console.error("处理两点栓点新增管点失败:", error);
    ElMessage.error("处理新增管点失败");
  }
};

/**
 * @function handleTwoPointHangClose
 * @description 处理两点栓点面板关闭
 */
const handleTwoPointHangClose = (): void => {
  try {
    // 关闭面板
    showTwoPointHangPanel.value = false;

    // 🔧 修改：使用统一的退出逻辑
    exitEditMode();

    console.log("两点栓点面板已关闭，编辑模式已重置");
  } catch (error) {
    console.error("关闭两点栓点面板失败:", error);
    ElMessage.error("关闭面板失败");
  }
};

/**
 * @function handleTwoLineHang
 * @description 处理两边栓点功能
 */
const handleTwoLineHang = (): void => {
  try {
    if (currentEditMode.value === "two-line-hang") {
      // 已经在两边栓点模式，退出
      exitEditMode();
      return;
    }

    // 🔧 新增：如果有其他功能激活，先清理
    if (currentEditMode.value !== "idle") {
      exitEditMode();
    }

    // 切换到两边栓点模式
    currentEditMode.value = "two-line-hang";
    showTwoLineHangPanel.value = true;

    // ElMessage.info("已进入两边栓点模式，请在面板中操作");
  } catch (error) {
    console.error("启动两边栓点模式失败:", error);
    ElMessage.error(
      `启动两边栓点模式失败: ${
        error instanceof Error ? error.message : "未知错误"
      }`
    );
    exitEditMode();
  }
};

/**
 * @function handleTwoLineHangAddNode
 * @description 处理两边栓点新增管点事件
 * @param data 栓点数据
 */
const handleTwoLineHangAddNode = (data: any): void => {
  try {
    console.log("两边栓点新增管点:", data);

    // 设置临时坐标
    tempClickCoordinates.value = [data.lng, data.lat];

    // 设置为新建管点模式
    nodeEditMode.value = "creating";
    currentNodeData.value = undefined;

    // 显示管点编辑面板
    showNodePanel.value = true;

    // ElMessage.success("请填写新增管点信息");
  } catch (error) {
    console.error("处理两边栓点新增管点失败:", error);
    ElMessage.error("处理新增管点失败");
  }
};

/**
 * @function handleTwoLineHangClose
 * @description 处理两边栓点面板关闭
 */
const handleTwoLineHangClose = (): void => {
  try {
    // 关闭面板
    showTwoLineHangPanel.value = false;

    // 🔧 修改：使用统一的退出逻辑
    exitEditMode();

    console.log("两边栓点面板已关闭，编辑模式已重置");
  } catch (error) {
    console.error("关闭两边栓点面板失败:", error);
    ElMessage.error("关闭面板失败");
  }
};
</script>

<style scoped lang="scss">
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  // width: 550px;
  min-height: 120px;
  z-index: 1000;
}

.toolbar {
  margin-bottom: 16px;
}

.status-indicator {
  margin-top: 10px;
}

.view-mode-tips {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.line-drawing-progress {
  margin-top: 10px;
}

.el-button-group .el-button {
  margin-right: 0;
}

/* .el-steps {
  max-width: 400px;
} */
:deep(.el-step__title) {
  font-size: 14px;
}
:deep(.el-step__title.is-process) {
  color: #98a8c9;
  font-weight: 400;
}
:deep(.el-step__head.is-process) {
  color: #98a8c9;
  border-color: #98a8c9;
}
</style>
