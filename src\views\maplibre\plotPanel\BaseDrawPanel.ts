/**
 * @fileoverview 标绘面板基类 - 抽取通用逻辑
 * @description 为所有绘制Panel组件提供通用的状态管理、事件处理和生命周期逻辑
 * <AUTHOR>
 * @version 1.0.0
 */

import { ref, computed, onMounted, onUnmounted, watch, type Ref } from 'vue';
import { ElMessage } from 'element-plus';
import { useDebounceFn } from '@vueuse/core';
import { Subscription } from 'rxjs';

import { DrawData, DrawStatus, DrawModule } from './DrawModule';
import type { PlotService } from './PlotService';
import { useDialogStore } from '@/stores/Dialogs';
import {
  type PlotFeature,
  DEFAULT_FEATURE_STYLE,
} from '@/lib/maplibre/layer/types/LayerTypes';
import { EngineType } from '@/types/plotting';

/**
 * @description 基础Panel配置接口
 */
export interface BaseDrawPanelConfig {
  featureType: string;           // 要素类型：'point', 'linestring', 'polygon', 'rectangle'
  featureTypeName: string;       // 要素类型显示名称：'点', '线', '多边形', '矩形'
  drawMessage: string;           // 绘制提示消息
  successMessage: string;        // 绘制成功消息
  placeholder: string;           // 名称输入框占位符
  descriptionPlaceholder: string; // 描述输入框占位符
}

/**
 * @description Props接口
 */
export interface BaseDrawPanelProps {
  drawData?: DrawData | null;
  plotService?: PlotService | null;
}

/**
 * @description Emits接口
 */
export interface BaseDrawPanelEmits {
  (e: 'draw-start'): void;
  (e: 'draw-cancel'): void;
  (e: 'feature-save', feature: PlotFeature): void;
}

/**
 * @description 基础Panel状态接口
 */
export interface BaseDrawPanelState {
  // 核心响应式状态
  submitLoading: Ref<boolean>;
  isDrawing: Ref<boolean>;
  isUserCustomName: Ref<boolean>;
  formData: Ref<PlotFeature>;
  showChangeState: Ref<any>;
  
  // 计算属性
  hasGeometry: any;
  isEditing: any;
  
  // 订阅管理
  statusSubscription: Subscription | null;
  showChangeSubscription: Subscription | null;
  
  // 核心方法
  initializeData: () => void;
  setupSubscriptions: () => void;
  cleanupSubscriptions: () => void;
  startDraw: () => Promise<void>;
  onSubmit: () => Promise<void>;
  onCancel: () => void;
  closeHandler: () => void;
  setGeometry: (geometry: any) => void;
}

/**
 * @description 创建基础Panel逻辑的组合函数
 * @param config - Panel配置
 * @param props - 组件Props
 * @param emit - 组件Emits
 * @param customLogic - 自定义逻辑钩子
 */
export function useBaseDrawPanel(
  config: BaseDrawPanelConfig,
  props: BaseDrawPanelProps,
  emit: BaseDrawPanelEmits,
  customLogic?: {
    /**
     * @description 自定义初始化逻辑（在基础初始化完成后调用）
     */
    onInitialized?: (formData: Ref<PlotFeature>) => void;
    
    /**
     * @description 自定义几何数据更新逻辑
     */
    onGeometryUpdated?: (formData: Ref<PlotFeature>) => void;
    
    /**
     * @description 自定义绘制前逻辑
     */
    onBeforeDraw?: (formData: Ref<PlotFeature>) => void;
    
         /**
      * @description 自定义提交前验证逻辑
      */
     onBeforeSubmit?: (formData: Ref<PlotFeature>) => boolean | Promise<boolean>;
  }
): BaseDrawPanelState {
  
  // === 响应式状态 ===
  const submitLoading: Ref<boolean> = ref(false);
  const isDrawing = ref(false);
  const isUserCustomName = ref(false);
  
  // 表单数据
  const formData = ref<PlotFeature>({
    id: -1, // 新建要素使用-1作为临时ID
    name: '',
    remark: '',
    geojson: {
      type: 'Feature',
      geometry: { type: 'Point', coordinates: [0, 0] },
      properties: {
        type: 'plot',
        geometryType: config.featureType as any,
        style: { ...DEFAULT_FEATURE_STYLE }
      }
    },
    engineType: EngineType.MAPLIBRE
  });

  // 响应式引用DrawModule的showChange状态
  const showChangeState = ref(DrawModule.showChange.value);

  // === 计算属性 ===
  const hasGeometry = computed(() => {
    return formData.value.geojson?.geometry && 
           !(formData.value.geojson.geometry.type === 'Point' && 
             formData.value.geojson.geometry.coordinates[0] === 0 && 
             formData.value.geojson.geometry.coordinates[1] === 0);
  });
  
  const isEditing = computed(() => {
    const hasEditFeature = !!(showChangeState.value && showChangeState.value.editFeature);
    const isEditingStatus = DrawModule.getInstance().getStatus() === DrawStatus.EDITING;
    
    return hasEditFeature || isEditingStatus || 
           (props.drawData?.feature && props.drawData?.isEditing === true);
  });

  // === 订阅管理 ===
  let statusSubscription: Subscription | null = null;
  let showChangeSubscription: Subscription | null = null;

  /**
   * @description 初始化数据
   */
  function initializeData(): void {

    
    // 重置状态变量
    isUserCustomName.value = false;
    
    const drawModule = DrawModule.getInstance();
    let editFeature: PlotFeature | null = null;
    
    // 获取编辑要素数据的优先级：showChange > DrawModule.getCurrentFeature > props
    try {
      const currentShowState = DrawModule.showChange.value;
      if (currentShowState && currentShowState.editFeature) {
        editFeature = currentShowState.editFeature;

      }
    } catch (error) {
      console.warn('从showChange获取编辑要素失败:', error);
    }
    
    if (!editFeature) {
      const currentStatus = drawModule.getStatus();
      if (currentStatus === DrawStatus.EDITING) {
        editFeature = drawModule.getCurrentFeature();

      }
    }
    
    if (!editFeature && props.drawData?.feature) {
      editFeature = props.drawData.feature;
    }

    if (editFeature) {
      // 编辑模式：加载编辑要素数据
      Object.assign(formData.value, editFeature);
      isUserCustomName.value = true;
    } else {
      // 新建模式：生成全新的要素数据
      formData.value = {
        id: -1, // 新建要素使用-1作为临时ID
        name: drawModule.generateDefaultFeatureName(config.featureType),
        remark: '',
        geojson: {
          type: 'Feature',
          geometry: { type: 'Point', coordinates: [0, 0] },
          properties: {
            type: 'plot',
            geometryType: config.featureType as any,
            style: { ...DEFAULT_FEATURE_STYLE }
          }
        },
        engineType: EngineType.MAPLIBRE
      };
      isUserCustomName.value = false;
    }

    // 调用自定义初始化逻辑
    customLogic?.onInitialized?.(formData);
  }

  /**
   * @description 设置订阅
   */
  function setupSubscriptions(): void {
    if (props.plotService) {
      // 监听绘制状态变化
      statusSubscription = DrawModule.statusChange.subscribe(
        (status: DrawStatus) => {
          isDrawing.value = status === DrawStatus.DRAWING;
        }
      );

      // 监听showChange状态变化
      showChangeSubscription = DrawModule.showChange.subscribe(
        (showState) => {
          showChangeState.value = showState;
        }
      );
    }
  }

  /**
   * @description 清理订阅
   */
  function cleanupSubscriptions(): void {
    statusSubscription?.unsubscribe();
    statusSubscription = null;
    showChangeSubscription?.unsubscribe();
    showChangeSubscription = null;
  }

  /**
   * @description 开始绘制
   */
  async function startDraw(): Promise<void> {
    if (!props.plotService) {
      ElMessage.error('绘制服务未初始化');
      return;
    }

    try {


      // 执行自定义绘制前逻辑
      customLogic?.onBeforeDraw?.(formData);

      // 如果是重新绘制，先清除已有几何图形
      if (hasGeometry.value) {
        props.plotService.clearCurrentDrawing();

        // 编辑模式下重新绘制时，从图层中临时移除原要素
        if (isEditing.value && formData.value.id) {
          try {
            const drawModule = DrawModule.getInstance();
            const featureLayer = (drawModule as any).featureLayer;
            if (featureLayer && typeof featureLayer.removeFeature === 'function') {
              featureLayer.removeFeature(formData.value.id);
            }
          } catch (error) {
            console.warn('从图层移除原要素失败:', error);
          }
        }

        // 清除表单中的几何数据
        formData.value.geojson.geometry = { type: 'Point', coordinates: [0, 0] };
      }

      emit('draw-start');

      // 开始绘制
      if (isEditing.value) {
        await props.plotService.startDraw(config.featureType, formData.value, formData.value);
      } else {
        await props.plotService.startDraw(config.featureType, formData.value);
      }

      ElMessage.info(config.drawMessage);
    } catch (error) {
      console.error(`启动${config.featureTypeName}绘制失败:`, error);
      ElMessage.error('启动绘制失败');
    }
  }

  /**
   * @description 提交保存
   */
  const onSubmit = useDebounceFn(async () => {
    if (!hasGeometry.value) {
      ElMessage.error(`请先绘制${config.featureTypeName}`);
      return;
    }

         // 执行自定义提交前验证
     if (customLogic?.onBeforeSubmit) {
       const validationResult = await customLogic.onBeforeSubmit(formData);
       if (!validationResult) {
         return;
       }
     }

    try {
      submitLoading.value = true;

      if (isEditing.value) {
        if (props.plotService) {
          const success = await props.plotService.updateFeature({ ...formData.value });
          if (success) {
            ElMessage.success(`${config.featureTypeName}更新成功`);
            props.plotService.stopEdit();
            setTimeout(() => closeHandler(), 1000);
          }
        }
      } else {
        emit('feature-save', { ...formData.value });
      }
    } catch (error) {
      console.error('提交失败:', error);
      ElMessage.error('操作失败');
    } finally {
      submitLoading.value = false;
    }
  }, 300);

  /**
   * @description 取消操作
   */
  function onCancel(): void {
    if (!props.plotService) {
      emit('draw-cancel');
      closeHandler();
      return;
    }

    try {
      if (isEditing.value) {
        props.plotService.cancelEdit();
      } else if (hasGeometry.value) {
        props.plotService.clearCurrentDrawing();
      }
    } catch (error) {
      console.error('取消操作失败:', error);
      ElMessage.error('取消操作失败');
    }

    emit('draw-cancel');
    closeHandler();
  }

  /**
   * @description 关闭面板
   */
  function closeHandler(): void {
    useDialogStore().closeDialog('DrawPanel');
  }

  // === 生命周期管理 ===
  onMounted(() => {

    isDrawing.value = false;
    initializeData();
    setupSubscriptions();
  });

  onUnmounted(() => {

    cleanupSubscriptions();
  });

  // === 监听绘制数据变化 ===
  watch(
    () => props.drawData,
    (newData) => {
      if (newData?.feature && newData.geometry) {
        formData.value.geojson.geometry = newData.geometry;

        // 如果用户没有自定义名称，则生成新的默认名称
        if (!isUserCustomName.value) {
          const drawModule = DrawModule.getInstance();
          formData.value.name = drawModule.generateDefaultFeatureName(config.featureType);
        }

        // 调用自定义几何数据更新逻辑
        customLogic?.onGeometryUpdated?.(formData);

  
        // ElMessage.success(config.successMessage);
      }
    },
    { deep: true }
  );

  // === 监听名称输入变化 ===
  watch(
    () => formData.value.name,
    (newName, oldName) => {
      if (newName !== oldName && newName.trim() !== '') {
        isUserCustomName.value = true;
      }
    }
  );

  /**
   * @description 设置几何图形数据
   * @param geometry - 几何图形数据
   */
  function setGeometry(geometry: any): void {
    if (geometry) {
      formData.value.geojson.geometry = geometry;
  
      
      // 调用自定义几何数据更新逻辑
      customLogic?.onGeometryUpdated?.(formData);
    }
  }

  return {
    // 响应式状态
    submitLoading,
    isDrawing,
    isUserCustomName,
    formData,
    showChangeState,
    
    // 计算属性
    hasGeometry,
    isEditing,
    
    // 订阅管理
    statusSubscription,
    showChangeSubscription,
    
    // 核心方法
    initializeData,
    setupSubscriptions,
    cleanupSubscriptions,
    startDraw,
    onSubmit,
    onCancel,
    closeHandler,
    setGeometry,
  };
} 