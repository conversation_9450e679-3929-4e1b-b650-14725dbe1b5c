import hRequest from '@/utils/http'
import type { DataType } from '@/utils/http/types'
// 管径
export const gjStat = () => {
  return hRequest.get<DataType>({
    url: '/analyse/gs/ln/gj/length/stat'
  })
}
// 管材
export const gcStat = () => {
  return hRequest.get<DataType>({
    url: '/analyse/gs/ln/cz/length/stat'
  })
}
// 消防井
export const xfStat = () => {
  return hRequest.get<DataType>({
    url: '/analyse/gs/pt/fsw/xfj/stat'
  })
}
// 阀门
export const fmStat = () => {
  return hRequest.get<DataType>({
    url: '/analyse/gs/pt/fsw/fmj/stat'
  })
}
export const gjLengthStat = () => {
  return hRequest.get<DataType>({
    url: '/analyse/gs/ln/gj/interval/length/stat'
  })
}