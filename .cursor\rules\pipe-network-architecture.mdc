---
description: 
globs: 
alwaysApply: false
---
# 管网渲染系统架构指南

## 核心组件架构

### 主要渲染组件
- **[PipeRenderer.ts](mdc:src/lib/maplibre/pipeNetwork/render/PipeRenderer.ts)** - 核心管网渲染器，负责临时要素的渲染和交互
- **[PipeNetworkLayer.ts](mdc:src/lib/maplibre/layer/layers/PipeNetworkLayer.ts)** - 持久化管网图层，集成到图层管理系统
- **[PipeEdit.vue](mdc:src/views/maplibre/pages/dataEdition/PipeEdit.vue)** - 管网编辑主界面组件

### 类型定义和数据结构
- **[PipeNetworkTypes.ts](mdc:src/lib/maplibre/pipeNetwork/types/PipeNetworkTypes.ts)** - 管网相关的所有类型定义
  - `PipeNode` - 管点数据结构
  - `PipeLine` - 管线数据结构  
  - `PipeNodeType`, `PipeLineType`, `PipeMaterial` - 枚举类型

## 渲染器设计模式

### PipeRenderer 独立架构
PipeRenderer 采用**独立架构设计**，不继承基类，专注于：
- 临时要素渲染（编辑过程中的管点/管线显示）
- 交互状态管理（选中、悬停、错误状态）
- 性能优化（更新队列、防抖机制）
- 事件系统（渲染事件的发布订阅）

### 图层管理策略
```typescript
// 图层按 z-index 分层
const LAYER_IDS = {
  LINES: 'pipe-lines',           // z-index: 1
  NODES: 'pipe-nodes',           // z-index: 2  
  SELECTED_LINES: 'pipe-selected-lines',  // z-index: 3
  SELECTED_NODES: 'pipe-selected-nodes',  // z-index: 4
  HOVER_LINES: 'pipe-hover-lines',        // z-index: 5
  HOVER_NODES: 'pipe-hover-nodes',        // z-index: 6
  NODE_LABELS: 'pipe-node-labels'         // z-index: 7
}
```

### 数据源管理
- `pipe-nodes-source` - GeoJSON 数据源，管理所有管点要素
- `pipe-lines-source` - GeoJSON 数据源，管理所有管线要素
- 使用 MapLibre GL 的 `setData()` 方法进行实时数据更新

## 关键开发模式

### 1. 防抖更新机制
```typescript
// 使用队列防抖避免频繁更新
private queueUpdate(key: string, updateFn: () => void): void {
  this.updateQueue.add(key);
  // 16ms 防抖，对应 60fps
}
```

### 2. 状态管理模式
```typescript
interface RenderState {
  initialized: boolean;
  activeLayerCount: number;
  renderedFeatureCount: { nodes: number; lines: number };
  currentStyleState: Record<string, 'default' | 'selected' | 'hover' | 'error'>;
}
```

### 3. 事件驱动架构
```typescript
enum RenderEventType {
  LAYER_ADDED = 'layer_added',
  FEATURES_RENDERED = 'features_rendered',
  STYLE_UPDATED = 'style_updated'
}
```

## 调试和故障排除

### 常见问题排查
1. **临时图层不显示**
   - 检查数据源是否正确创建
   - 验证图层是否成功添加到地图
   - 确认要素数据格式正确

2. **样式不生效**
   - 检查样式配置的属性名称
   - 验证数据属性是否匹配过滤器
   - 确认图层层级顺序

3. **性能问题**
   - 检查更新队列是否工作正常
   - 监控图层数量和要素数量
   - 验证缩放级别限制设置

### 调试工具和日志
- 使用 `console.log` 追踪图层创建过程
- 检查 `map.getLayer()` 验证图层存在性
- 监控 `RenderState` 了解渲染器状态

## 样式配置模式

### 图层样式结构
```typescript
interface StyleConfig {
  default: MapLibre Paint Properties;
  selected: MapLibre Paint Properties;
  hover: MapLibre Paint Properties;
}
```

### 动态样式更新
- 使用 `map.setPaintProperty()` 动态更新样式
- 通过特征属性 (`selected`, `hovered`) 控制图层过滤器
- 支持运行时主题切换

## 集成注意事项

### Vue.js 集成
- 在 `onMounted` 中初始化渲染器
- 在 `onUnmounted` 中调用 `destroy()` 清理资源
- 使用 `ref` 管理渲染器实例

### MapLibre GL 集成
- 确保地图加载完成后再初始化渲染器
- 正确处理数据源和图层的生命周期
- 注意图层顺序对视觉效果的影响

### 错误处理策略
- 所有异步操作都应包含 try-catch
- 提供详细的错误日志用于调试
- 优雅降级，避免整个应用崩溃

