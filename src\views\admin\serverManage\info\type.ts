
// import type { Tree<PERSON>ey } from "element-plus/es/components/tree-v2/src/types"
export interface QueryForm {
  name: string;
  pageSize: number;
  pageNum: number;
}
export interface SysServer {
  /**
   * 租户
   */
  tenant: string
  /**
   * ID
   */
  id: string
  /**
   * 图标名称
   */
  name: string
  /**
   * 标识
   */
  sign: string
  /**
   * 地址
   */
  databaseAddress: string
  /**
   * 端口号
   */
  databasePort: string
  /**
   * 数据库名称
   */
  databaseUserName: string
  /**
   * 数据库用户名
   */
  databaseName: string
  /**
   * 数据库密码
   */
  databasePassword: string

}