// excel导出
export function exportFile(res: any) {
  let fileName = res.headers["content-disposition"]
    .split(";")[1]
    .split("filename=")[1];
  fileName = decodeURIComponent(fileName);
  const a = document.createElement("a");//创建a标签
  const blob = new Blob([res.data], {
    type: "application/vnd.ms-excel,charset=utf-8",
  });
  a.style.display = "none";
  a.href = URL.createObjectURL(blob);
  a.download = fileName; //下载的文件名
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(a.href);
}
// 压缩包下载
export function exportFileZip(res: any) {
  console.log(res);
  let fileName = res.headers["content-disposition"]
    .split(";")[1]
    .split("filename=")[1];
  fileName = decodeURIComponent(fileName);
  const a = document.createElement("a");//创建a标签
  const blob = new Blob([res.data], {
    type: "application/zip",
  });
  a.style.display = "none";
  a.href = URL.createObjectURL(blob);
  a.download = fileName; //下载的文件名
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(a.href);
}
// word 导出
export function exportWord(res: any) {
  console.log(res)
  const blob = new Blob([res.data], {
    type: `application/msword` //word文档为msword,pdf文档为pdf
  });
  const objectUrl = URL.createObjectURL(blob);
  const link = document.createElement("a");
  //let fname = `电网负荷预测报告2021-10-11`; //下载文件的名字
  let fname = res.headers["content-disposition"]
    .split(";")[1]
    .split("filename=")[1];
  fname = decodeURIComponent(fname);
  link.href = objectUrl;
  link.setAttribute("download", fname);
  document.body.appendChild(link);
  link.click();
}

/**
 * @description DXF文件导出
 * @param res 响应对象，包含二进制数据和headers
 * @param defaultFileName 默认文件名（当无法从headers中获取时使用）
 */
export function exportDxfFile(res: any, defaultFileName: string = 'pipeline_data.dxf') {
  try {
    // 创建DXF文件的Blob对象
    const blob = new Blob([res.data], {
      type: 'application/dxf' // DXF文件的MIME类型
    });

    // 尝试从响应头获取文件名
    let fileName = defaultFileName;
    if (res.headers && res.headers["content-disposition"]) {
      try {
        const contentDisposition = res.headers["content-disposition"];
        const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (fileNameMatch && fileNameMatch[1]) {
          fileName = decodeURIComponent(fileNameMatch[1].replace(/['"]/g, ''));
        }
      } catch (error) {
        console.warn('无法从响应头解析文件名，使用默认文件名:', error);
      }
    }

    // 确保文件名有.dxf扩展名
    if (!fileName.toLowerCase().endsWith('.dxf')) {
      fileName = fileName.replace(/\.[^/.]+$/, '') + '.dxf';
    }

    // 创建下载链接
    const objectUrl = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = objectUrl;
    link.download = fileName;
    
    // 执行下载
    document.body.appendChild(link);
    link.click();
    
    // 清理资源
    document.body.removeChild(link);
    window.URL.revokeObjectURL(objectUrl);

    console.log(`DXF文件下载成功: ${fileName}`);
    return { success: true, fileName };
  } catch (error) {
    console.error('DXF文件导出失败:', error);
    return { success: false, error: error instanceof Error ? error.message : '未知错误' };
  }
}

/**
 * @description SHP文件导出（现已支持zip格式，自动识别文件名和MIME类型）
 * @param res 响应对象，包含二进制数据和headers
 * @param defaultFileName 默认文件名（当无法从headers中获取时使用，默认为pipe_node_data.zip）
 * @returns { success: boolean, fileName?: string, error?: string }
 */
export function exportShpFile(res: any, defaultFileName: string = 'pipe_node_data.zip') {
  try {
    // 自动识别MIME类型，优先content-type，其次强制zip
    let mimeType = 'application/zip';
    // 尝试从响应头获取文件名
    let fileName = defaultFileName;
    if (res.headers && res.headers["content-disposition"]) {
      try {
        const contentDisposition = res.headers["content-disposition"];
        const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (fileNameMatch && fileNameMatch[1]) {
          fileName = decodeURIComponent(fileNameMatch[1].replace(/['"]/g, ''));
        }
      } catch (error) {
        console.warn('无法从响应头解析文件名，使用默认文件名:', error);
      }
    }

    // 创建Blob对象
    const blob = new Blob([res.data], { type: mimeType });


    // 创建下载链接
    const objectUrl = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = objectUrl;
    link.download = fileName || defaultFileName;
    
    // 执行下载
    document.body.appendChild(link);
    link.click();
    
    // 清理资源
    document.body.removeChild(link);
    window.URL.revokeObjectURL(objectUrl);

    console.log(`SHP(ZIP)文件下载成功: ${fileName}`);
    return { success: true, fileName: fileName };
  } catch (error) {
    console.error('SHP(ZIP)文件导出失败:', error);
    return { success: false, error: error instanceof Error ? error.message : '未知错误' };
  }
}

/**
 * 通用文件下载函数
 * @param blob 文件内容的Blob对象
 * @param filename 要保存的文件名
 */
export function downloadFile(blob: Blob, filename: string): void {
  try {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', filename);
    
    document.body.appendChild(link);
    link.click();
    
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('文件下载时发生错误:', error);
    // 可以在这里添加用户反馈，例如使用ElMessage
  }
}