import hRequest from '@/utils/http';
import type { DataType } from '@/utils/http/types';

// 分页
export const userPage = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/sys/user/page',
    params
  });
};
// 新增
export const addUser = (data: any) => {
  return hRequest.post<DataType>({
    url: "/sys/user",
    data: data
  });
};
// 修改
export const editUser = (data: any) => {
  return hRequest.put<DataType>({
    url: "/sys/user",
    data: data
  });
};
// 详情
export const detailsUser = (id: any) => {
  return hRequest.get<DataType>({
    url: `/sys/user/${id}`,
  });
};
// 删除
export const deleteUser = (id: any) => {
  return hRequest.delete<DataType>({
    url: `/sys/user/${id}`,
  });
};
// 获取当前登录人
export const userCurrent = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/sys/user/current',
    params
  });
};