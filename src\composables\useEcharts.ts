import { echarts, type ECOption } from '@/lib/echarts/echarts'
import type { ECharts } from 'echarts'

export const useEchart = () => {
  // const myChart = ref()
  const initChart = (ele: HTMLElement, option: ECOption) => {
    const myChart = echarts.init(ele)
    myChart.setOption(option)
    window.addEventListener('resize', function () {
      myChart.resize()
    })
    return myChart
  }

  const updateChart = (chart: ECharts, option: ECOption) => {
    chart.setOption(option)
  }
  // const myChart = chart ? chart : echarts.init(ele)
  // myChart.setOption(option)
  // window.addEventListener('resize', function () {
  //   myChart.resize()
  // })
  return {
    initChart,
    updateChart
  }
}
