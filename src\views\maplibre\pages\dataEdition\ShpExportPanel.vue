<template>
  <page-card class="shp-export-panel" title="选择导出附属物类型" @closeCard="closeCard">
    <el-form>
      <el-form-item label="附属物类型" required>
        <el-select v-model="selectedFsw" placeholder="请选择附属物类型" style="width: 100%">
          <el-option v-for="item in fswOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="shp-export-panel-footer">
      <el-button @click="closeCard">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm">{{ loading ? '导出中...' : '导出' }}</el-button>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import PageCard from '@/components/PageCard.vue';
import { ElMessage } from 'element-plus';
import { pipeNodeExportShp, queryPipeFswList } from '@/api/pipeNode';
import { exportShpFile } from '@/utils/file';
/**
 * @interface Emits
 * @description 组件事件接口
 */
 interface Emits {
  (e: 'close'): void;
}

const emit = defineEmits<Emits>();
onMounted(async () => {
  await nextTick();
  await fetchFswOptions();
});

const fswOptions = ref<{ label: string; value: string }[]>([]);
const selectedFsw = ref('全部');
const loading = ref(false);

async function fetchFswOptions() {
  try {
    fswOptions.value = [];
    // selectedFsw.value = '';
    const res = await queryPipeFswList();
    if (res.code === 200 && Array.isArray(res.data)) {
      fswOptions.value = res.data.map((item: any) => ({ label: item, value: item }));
      fswOptions.value.unshift({label: '全部', value: '全部'});
    } else {
      throw new Error(res.msg || '获取附属物类型失败');
    }
  } catch (error) {
    ElMessage.error('获取附属物类型失败');
  }
}

async function handleConfirm() {
  if (!selectedFsw.value) {
    ElMessage.warning('请选择附属物类型');
    return;
  }
  loading.value = true;
  try {
    const fswParams = selectedFsw.value === '全部' ? '' : selectedFsw.value;
    const res = await pipeNodeExportShp(fswParams);
    const result = exportShpFile(res);
    if (result.success) {
      ElMessage.success(`SHP文件导出成功: ${result.fileName}`);
    } else {
      throw new Error(result.error || 'SHP文件处理失败');
    }
  } catch (error: any) {
    ElMessage.error(`导出失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
}


/**
 * @function closeCard
 * @description 处理关闭面板
 */
 const closeCard = (): void => {
  try {
    console.log('关闭SHP导出面板');
    emit('close');
    ElMessage.info('已关闭SHP导出功能');
  } catch (error) {
    console.error('关闭面板失败:', error);
    ElMessage.error('关闭面板失败');
  }
};
</script>

<style scoped lang="scss">
.shp-export-panel {
  position: absolute;
  left: 10px;
  top: 150px;
  width: 350px;
  min-height: 120px;
  z-index: 1000;
}
.shp-export-panel-footer {
  margin-top: 16px;
  text-align: right;
}
</style> 