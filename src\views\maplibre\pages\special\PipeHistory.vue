<!--
 * @Description: 管网历史专题
 * @Date: 2024-01-16
 * @Author: AI Assistant
 * @LastEditTime: 2024-01-16
 -->
<template>
  <page-card
    class="pipe-history-theme"
    title="管网历史专题"
    :closeIcon="false"
  >
    <!-- 加载状态显示 -->
    <el-row v-if="loading" class="loading-section">
      <el-col :span="24">
        <div class="loading-content">
          <el-icon class="is-loading"><Loading /></el-icon>
          <el-text class="loading-text">正在加载历史专题图...</el-text>
        </div>
      </el-col>
    </el-row>

    <!-- 专题图说明 -->
    <el-row v-else class="description-section">
      <el-col :span="24">
        <el-text class="description-text">
          按时间轴显示管网建设历史演变过程，不同颜色代表不同的建设年代。
        </el-text>
      </el-col>
    </el-row>

    <!-- 时间轴控制器 -->
    <el-row v-if="!loading && historyYears.length > 0" class="timeline-section">
      <el-col :span="24">
        <div class="timeline-container">
          <!-- 时间轴标题 -->
          <div class="timeline-header">
            <el-text class="timeline-title">建设时间轴</el-text>
            <el-text class="current-year">当前年份: {{ currentYear }}</el-text>
          </div>
          
          <!-- 时间轴滑块 -->
          <div class="timeline-slider">
            <el-slider
              v-model="currentYearIndex"
              :min="0"
              :max="historyYears.length - 1"
              :marks="timelineMarks"
              :step="1"
              show-tooltip
              :format-tooltip="formatTooltip"
              @change="onTimelineChange"
            />
          </div>
          
          <!-- 播放控制 -->
          <div class="timeline-controls">
            <el-button-group>
              <el-button 
                :icon="isPlaying ? VideoPause : VideoPlay" 
                @click="togglePlayback"
                size="small"
                type="primary"
              >
                {{ isPlaying ? '暂停' : '播放' }}
              </el-button>
              <el-button 
                icon="Refresh" 
                @click="resetTimeline"
                size="small"
              >
                重置
              </el-button>
            </el-button-group>
          </div>
        </div>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, onBeforeUnmount, nextTick } from 'vue';
import { ElMessage, ElIcon } from 'element-plus';
import { Loading, VideoPlay, VideoPause } from '@element-plus/icons-vue';
import { onBeforeRouteLeave } from 'vue-router';
import PageCard from '@/components/PageCard.vue';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import {
  useLegend,
  type LegendItem,
  type LegendObj,
} from '@/components/legend';
import type { Map as MapLibreMap } from 'maplibre-gl';
import { queryPipeDiameterList, type ApiResult } from '@/api/pipeLine';

/**
 * @interface Emits
 * @description 组件事件接口
 */
interface Emits {
  (e: 'close'): void;
}

const emit = defineEmits<Emits>();

// ============ 响应式状态 ============

/** 加载状态 */
const loading = ref(false);

/** 图例store */
const legendStore = useLegend();

/** 管线图层ID常量 */
const PIPELINE_LAYER_ID = 'mvt_pipeLine';

/** 管点图层ID常量 */
const PIPE_NODE_LAYER_ID = 'mvt_pipeNode';

/** 原始管线颜色备份 */
const originalLineColor = ref<string>('#2b78fe');

/** 原始管点图层最小缩放级别备份 */
const originalNodeMinZoom = ref<number | null>(null);

/** 历史年份数据 */
const historyYears = ref<number[]>([]);

/** 当前选中的年份索引 */
const currentYearIndex = ref(0);

/** 当前年份 */
const currentYear = computed(() => historyYears.value[currentYearIndex.value] || 0);

/** 播放状态 */
const isPlaying = ref(false);

/** 播放定时器 */
let playTimer: ReturnType<typeof setInterval> | null = null;

/** 悬浮提示框元素 */
const hoverTooltip = ref<any>(null);

/** 鼠标移动事件处理器 */
let mouseMoveHandler: ((e: any) => void) | null = null;

/** 管径数据列表 */
const diameterList = ref<number[]>([]);

// ============ 计算属性 ============

/** 时间轴标记点 */
const timelineMarks = computed(() => {
  const marks: { [key: number]: string } = {};
  historyYears.value.forEach((year, index) => {
    // 只在特定索引位置显示标记，避免过于密集
    if (index === 0 || index === historyYears.value.length - 1 || index % Math.ceil(historyYears.value.length / 5) === 0) {
      marks[index] = year.toString();
    }
  });
  return marks;
});

/** 当前年份应该显示的管径数量 */
const currentVisibleDiameterCount = computed(() => {
  const yearIndex = currentYearIndex.value;
  return Math.min((yearIndex + 1) * 2, diameterList.value.length);
});

/** 当前应该显示的管径列表 */
const currentVisibleDiameters = computed(() => {
  return diameterList.value.slice(0, currentVisibleDiameterCount.value);
});

// ============ 事件处理方法 ============

/**
 * @function formatTooltip
 * @description 格式化滑块提示文字
 */
const formatTooltip = (value: number): string => {
  return `${historyYears.value[value]}年`;
};

/**
 * @function onTimelineChange
 * @description 时间轴变化处理
 */
const onTimelineChange = (value: number | number[]): void => {
  const index = Array.isArray(value) ? value[0] : value;
  currentYearIndex.value = index;
  applyHistoryFilter();
};

/**
 * @function togglePlayback
 * @description 切换播放状态
 */
const togglePlayback = (): void => {
  if (isPlaying.value) {
    stopPlayback();
  } else {
    startPlayback();
  }
};

/**
 * @function startPlayback
 * @description 开始播放
 */
const startPlayback = (): void => {
  if (currentYearIndex.value >= historyYears.value.length - 1) {
    currentYearIndex.value = 0;
  }
  
  isPlaying.value = true;
  playTimer = setInterval(() => {
    if (currentYearIndex.value < historyYears.value.length - 1) {
      currentYearIndex.value++;
      applyHistoryFilter();
    } else {
      stopPlayback();
    }
  }, 1000);
};

/**
 * @function stopPlayback
 * @description 停止播放
 */
const stopPlayback = (): void => {
  isPlaying.value = false;
  if (playTimer) {
    clearInterval(playTimer);
    playTimer = null;
  }
};

/**
 * @function resetTimeline
 * @description 重置时间轴
 */
const resetTimeline = (): void => {
  stopPlayback();
  currentYearIndex.value = 0;
  applyHistoryFilter();
};


/**
 * @function getDiameterYear
 * @description 根据管径获取对应的建设年份
 * @param diameter 管径值
 * @returns 建设年份
 */
const getDiameterYear = (diameter: number): number | null => {
  const diameterIndex = diameterList.value.indexOf(diameter);
  if (diameterIndex === -1) return null;
  
  const yearIndex = Math.floor(diameterIndex / 2);
  return historyYears.value[yearIndex] || null;
};

/**
 * @function showTooltip
 * @description 显示提示框
 * @param x 鼠标X坐标
 * @param y 鼠标Y坐标
 * @param diameter 管径值
 */
const showTooltip = (x: number, y: number, diameter: number | string): void => {
  if (!hoverTooltip.value) return;

  const diameterNum = typeof diameter === 'string' ? parseInt(diameter) : diameter;
  const buildYear = getDiameterYear(diameterNum);
  
  let content = '';
  if (buildYear) {
    content = `<i style="color: #3498db;">📅</i> 建设年份: <strong>${buildYear}年</strong><br/>
               <i style="color: #666;">📏</i> 管径: <span style="color: #666;">${diameter}mm</span>`;
  } else {
    content = `<i style="color: #3498db;">📏</i> 管径: <strong>${diameter}mm</strong>`;
  }

  hoverTooltip.value.innerHTML = content;
  hoverTooltip.value.style.left = `${x + 15}px`;
  hoverTooltip.value.style.top = `${y - 40}px`;
  hoverTooltip.value.style.display = 'block';

  setTimeout(() => {
    if (hoverTooltip.value) {
      hoverTooltip.value.style.opacity = '1';
      hoverTooltip.value.style.transform = 'translateY(-5px)';
    }
  }, 10);
};

/**
 * @function hideTooltip
 * @description 隐藏提示框
 */
const hideTooltip = (): void => {
  if (!hoverTooltip.value) return;

  hoverTooltip.value.style.opacity = '0';
  hoverTooltip.value.style.transform = 'translateY(0)';

  setTimeout(() => {
    if (hoverTooltip.value) {
      hoverTooltip.value.style.display = 'none';
    }
  }, 200);
};

/**
 * @function setupMouseHover
 * @description 设置鼠标悬停事件
 */
const setupMouseHover = (): void => {
  try {
    const map = AppMaplibre.getMap();

    createHoverTooltip();

    mouseMoveHandler = (e: any) => {
      try {
        const degree = 10;
        const features = map.queryRenderedFeatures(
          [
            [e.point.x - degree / 2, e.point.y - degree / 2],
            [e.point.x + degree / 2, e.point.y + degree / 2],
          ],
          {
            layers: [PIPELINE_LAYER_ID],
          }
        );

        if (features.length > 0) {
          const feature = features[0];
          const diameter = feature.properties?.gj || feature.properties?.GJ || '未知';

          showTooltip(
            e.originalEvent.clientX,
            e.originalEvent.clientY,
            diameter
          );

          map.getCanvas().style.cursor = 'pointer';
        } else {
          hideTooltip();
          map.getCanvas().style.cursor = '';
        }
      } catch (error) {
        console.error('处理鼠标移动事件失败:', error);
      }
    };

    map.on('mousemove', mouseMoveHandler);
    map.on('mouseleave', hideTooltip);

    console.log('鼠标悬停事件已设置');
  } catch (error) {
    console.error('设置鼠标悬停事件失败:', error);
  }
};

/**
 * @function cleanupMouseHover
 * @description 清理鼠标悬停事件
 */
const cleanupMouseHover = (): void => {
  try {
    const map = AppMaplibre.getMap();

    if (mouseMoveHandler) {
      map.off('mousemove', mouseMoveHandler);
      map.off('mouseleave', hideTooltip);
      mouseMoveHandler = null;
    }

    if (hoverTooltip.value) {
      document.body.removeChild(hoverTooltip.value);
      hoverTooltip.value = null;
    }

    map.getCanvas().style.cursor = '';

    console.log('鼠标悬停事件已清理');
  } catch (error) {
    console.error('清理鼠标悬停事件失败:', error);
  }
};

/**
 * @function createHoverTooltip
 * @description 创建悬浮提示框
 */
const createHoverTooltip = (): void => {
  const tooltip = document.createElement('div');
  tooltip.className = 'pipe-history-tooltip';
  tooltip.style.cssText = `
    position: absolute;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(52, 73, 94, 0.9));
    color: white;
    padding: 10px 14px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    pointer-events: none;
    z-index: 10000;
    display: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateY(0);
    transition: opacity 0.2s ease, transform 0.2s ease;
    opacity: 0;
    line-height: 1.4;
    min-width: 120px;
  `;

  document.body.appendChild(tooltip);
  hoverTooltip.value = tooltip;
};

/**
 * @function generateYearColorMapping
 * @description 根据年份生成颜色映射
 * @param currentYearIndex 当前年份索引
 * @param diameterList 管径列表
 * @returns 颜色映射数组
 */
const generateYearColorMapping = (currentYearIndex: number, diameterList: number[]): any[] => {
  if (diameterList.length === 0) {
    return ['case', 'rgba(0,0,0,0)'];
  }

  // 生成年份对应的渐变色：从蓝色到红色
  const generateYearColor = (yearIndex: number, totalYears: number): string => {
    if (totalYears === 1) return '#2196f3';
    const ratio = yearIndex / (totalYears - 1);
    const hue = 240 - (ratio * 60); // 从蓝色(240°)到红色(180°)
    const saturation = 70 + (ratio * 30); // 饱和度从70%到100%
    const lightness = 45 + (ratio * 10); // 亮度从45%到55%
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  const mapping: any[] = ['case'];
  
  // 为每个年份的管径分配对应年份的颜色
  for (let yearIndex = 0; yearIndex <= currentYearIndex; yearIndex++) {
    const year = historyYears.value[yearIndex];
    const yearColor = generateYearColor(yearIndex, historyYears.value.length);
    
    // 计算这个年份对应的管径范围
    const startDiameterIndex = yearIndex * 2;
    const endDiameterIndex = Math.min(startDiameterIndex + 2, diameterList.length);
    
    // 为这个年份范围内的管径分配相同的年份颜色
    for (let i = startDiameterIndex; i < endDiameterIndex; i++) {
      if (i < diameterList.length) {
        const diameter = diameterList[i];
        mapping.push(['==', ['get', 'gj'], diameter]);
        mapping.push(yearColor);
      }
    }
  }

  // 对于不在当前显示范围内的管径，设置为透明
  mapping.push('rgba(0,0,0,0)');

  return mapping;
};

/**
 * @function generateLegendData
 * @description 生成图例数据
 * @param currentYearIndex 当前年份索引
 * @returns 图例数据
 */
const generateLegendData = (currentYearIndex: number): LegendItem[] => {
  const generateYearColor = (yearIndex: number, totalYears: number): string => {
    if (totalYears === 1) return '#2196f3';
    const ratio = yearIndex / (totalYears - 1);
    const hue = 240 - (ratio * 60);
    const saturation = 70 + (ratio * 30);
    const lightness = 45 + (ratio * 10);
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  const legendItems: LegendItem[] = [];
  
  // 为每个已显示的年份创建图例项
  for (let yearIndex = 0; yearIndex <= currentYearIndex; yearIndex++) {
    const year = historyYears.value[yearIndex];
    const yearColor = generateYearColor(yearIndex, historyYears.value.length);
    
    legendItems.push({
      name: `${year}年建设`,
      value: yearColor,
    });
  }

  return legendItems;
};

/**
 * @function applyHistoryFilter
 * @description 应用历史过滤器
 */
const applyHistoryFilter = (): void => {
  try {
    const map = AppMaplibre.getMap();
    
    // 生成当前年份的颜色映射
    const colorMapping = generateYearColorMapping(currentYearIndex.value, diameterList.value);
    
    // 应用样式到管线图层
    (map as any).setPaintProperty(PIPELINE_LAYER_ID, 'line-color', colorMapping);
    
    // 更新图例
    const legendData = generateLegendData(currentYearIndex.value);
    const legendObj: LegendObj = {
      title: `管网建设历史`,
      data: legendData,
    };
    
    legendStore.setLegend(legendObj);
    
    console.log(`已应用${currentYear.value}年的历史过滤器，显示${currentVisibleDiameterCount.value}种管径`);
  } catch (error) {
    console.error('应用历史过滤器失败:', error);
  }
};

/**
 * @function initPipeHistoryTheme
 * @description 初始化历史专题图
 */
const initPipeHistoryTheme = async (): Promise<void> => {
  try {
    loading.value = true;
    console.log('开始初始化历史专题图...');

    const map = AppMaplibre.getMap();

    // 备份原始样式
    const layer = map.getLayer(PIPELINE_LAYER_ID);
    if (layer && 'paint' in layer && layer.paint) {
      const paintStyle = layer.paint as any;
      originalLineColor.value = paintStyle['line-color'] || '#2b78fe';
      console.log('已备份原始管线颜色:', originalLineColor.value);
    }

    // 备份管点图层的原始minzoom并调整
    const nodeLayer = map.getLayer(PIPE_NODE_LAYER_ID);
    if (nodeLayer && 'minzoom' in nodeLayer) {
      originalNodeMinZoom.value = nodeLayer.minzoom || 12;
      map.setLayerZoomRange(PIPE_NODE_LAYER_ID, 16, nodeLayer.maxzoom || 24);
      console.log(
        `管点图层可见层级已调整: minzoom从${originalNodeMinZoom.value}调整为16`
      );
    }

    // 获取管径数据
    const response: ApiResult<any> = await queryPipeDiameterList();
    if (!response || !response.data) {
      throw new Error('获取管径数据失败');
    }

    // 处理管径数据并排序
    const diameters: number[] = Array.isArray(response.data) 
      ? response.data.filter((item: any) => typeof item === 'number')
      : Object.keys(response.data).map(Number).filter(n => !isNaN(n));
    
    if (diameters.length === 0) {
      throw new Error('未找到有效的管径数据');
    }

    // 对管径进行排序（从小到大）
    diameterList.value = [...new Set(diameters)].sort((a, b) => a - b);
    
    console.log('获取到的管径数据:', diameterList.value);

    // 使用固定的年份数据
    const yearData: number[] = [2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025];

    console.log('获取到的年份数据:', yearData);

    // 设置历史年份
    historyYears.value = yearData;
    currentYearIndex.value = historyYears.value.length - 1; // 默认显示2025年（最后一年）

    // 应用初始过滤器
    applyHistoryFilter();

    // 设置鼠标悬停事件
    setupMouseHover();

    // ElMessage.success('历史专题图加载完成');
  } catch (error) {
    console.error('初始化历史专题图失败:', error);
    ElMessage.error(
      `加载失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  } finally {
    loading.value = false;
  }
};

/**
 * @function restorePipelineStyle
 * @description 还原管线样式
 */
const restorePipelineStyle = (): void => {
  try {
    const map = AppMaplibre.getMap();
    console.log('开始还原管线样式...');
    
    try {
      const colorToRestore = originalLineColor.value || '#2b78fe';
      console.log('正在还原管线颜色为:', colorToRestore);
      
      (map as any).setPaintProperty(PIPELINE_LAYER_ID, 'line-color', colorToRestore);
      console.log('管线颜色已还原');
    } catch (colorError) {
      console.warn('还原管线颜色失败，使用默认颜色:', colorError);
      try {
        (map as any).setPaintProperty(PIPELINE_LAYER_ID, 'line-color', '#2b78fe');
        console.log('已应用默认管线颜色');
      } catch (defaultError) {
        console.error('应用默认颜色也失败:', defaultError);
      }
    }

    // 还原管点图层的原始minzoom
    if (originalNodeMinZoom.value !== null) {
      try {
        const nodeLayer = map.getLayer(PIPE_NODE_LAYER_ID);
        if (nodeLayer) {
          map.setLayerZoomRange(
            PIPE_NODE_LAYER_ID,
            originalNodeMinZoom.value,
            (nodeLayer as any).maxzoom || 24
          );
          console.log(
            `管点图层可见层级已还原: minzoom还原为${originalNodeMinZoom.value}`
          );
        }
        originalNodeMinZoom.value = null;
      } catch (zoomError) {
        console.warn('还原管点图层缩放级别失败:', zoomError);
        originalNodeMinZoom.value = null;
      }
    }

    try {
      legendStore.clearLegend();
      console.log('图例已清除');
    } catch (legendError) {
      console.warn('清除图例失败:', legendError);
    }

    try {
      cleanupMouseHover();
      console.log('鼠标悬停事件已清理');
    } catch (hoverError) {
      console.warn('清理鼠标悬停事件失败:', hoverError);
    }

    console.log('管线样式还原完成');
  } catch (error) {
    console.error('还原管线样式失败:', error);
    try {
      legendStore.clearLegend();
      cleanupMouseHover();
    } catch (cleanupError) {
      console.error('基本清理工作也失败:', cleanupError);
    }
  }
};

// ============ 生命周期钩子 ============

onMounted(async () => {
  await nextTick();
  initPipeHistoryTheme();
});

onUnmounted(() => {
  stopPlayback();
  cleanupMouseHover();
  restorePipelineStyle();
});

onBeforeUnmount(() => {
  stopPlayback();
});

onBeforeRouteLeave(() => {
  stopPlayback();
  restorePipelineStyle();
});
</script>

<style scoped lang="scss">
.pipe-history-theme {
  position: fixed;
  left: 50%;
  bottom: 20px;
  transform: translateX(-50%);
  width: 600px;
  min-height: 200px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.loading-section {
  .loading-content {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;

    .loading-text {
      color: #409eff;
      font-size: 14px;
    }
  }
}

.description-section {
  margin-bottom: 16px;
  
  .description-text {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
    display: block;
  }
}

.timeline-section {
  .timeline-container {
    padding: 8px 0;
    
    .timeline-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .timeline-title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
      }
      
      .current-year {
        font-size: 14px;
        color: #3498db;
        font-weight: 500;
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        padding: 4px 12px;
        border-radius: 20px;
        border: 1px solid #90caf9;
      }
    }
    
    .timeline-slider {
      margin: 20px 0;
      padding: 0 12px;
      
      :deep(.el-slider__runway) {
        background-color: #e1f5fe;
        height: 8px;
      }
      
      :deep(.el-slider__bar) {
        background: linear-gradient(90deg, #2196f3, #1976d2);
        height: 8px;
      }
      
      :deep(.el-slider__button) {
        border: 3px solid #1976d2;
        width: 20px;
        height: 20px;
        background-color: white;
      }
      
      :deep(.el-slider__marks-text) {
        font-size: 12px;
        color: #546e7a;
        font-weight: 500;
      }
    }
    
    .timeline-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 16px 0;
    }
  }
}
</style> 