
export interface QueryForm {
  operName: string;
  startTime: string;
  endTime: string;
  businessType: string;
  pageSize: number;
  pageNum: number;
}
export interface SysUser {
  /**
   * 操作人
   */
  operName: string
  /**
   * ID
   */
  id: string
  /**
   * operIp
   */
  operIp: string
  /**
   * 标题
   */
  title: string
  /**
   * 操作类型
   */
  businessType: string
  /**
   * 方法名
   */
  method: string
  /**
   * 请求方式
   */
  requestMethod: string
  /**
   * 部门名称
   */
  deptName: string
  /**
   * 请求参数
   */
  operParam: string
  /**
   * 返回参数
   */
  jsonResult: string
  /**
   * 操作状态
   */
  status: string
  /**
   * 请求url
   */
  operUrl: string
  /**
   * 操作时间
   */
  operTime: string
  /**
   * 错误信息
   */
  errorMsg: string
  /**
   * 消耗时间
   */
  costTime: string

}