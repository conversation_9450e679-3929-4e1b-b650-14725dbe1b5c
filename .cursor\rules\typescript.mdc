---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: false
---
---
description: TypeScript 编码规则和最佳实践
globs: **/*.ts, **/*.tsx, **/*.d.ts
---

# TypeScript 规则

## 类型系统
- 对于对象定义，优先使用接口而非类型
- 对于联合类型、交叉类型和映射类型，使用 type
- 避免使用 `any`，对于未知类型优先使用 `unknown`
- 使用严格的 TypeScript 配置
- 充分利用 TypeScript 的内置工具类型
- 使用泛型实现可复用的类型模式

## 命名约定
- 类型名称和接口使用 PascalCase
- 变量和函数使用 camelCase
- 常量使用 UPPER_CASE
- 使用带有辅助动词的描述性名称（例如，isLoading, hasError）
- React props 的接口前缀使用 'Props'（例如，ButtonProps）

## 代码组织
- 类型定义应靠近使用它们的地方
- 共享的类型和接口从专用类型文件导出
- 使用桶导出（index.ts）组织导出
- 将共享类型放在 `types` 目录中
- 组件 props 与其组件共同放置

## 函数
- 为公共函数使用显式返回类型
- 回调和方法使用箭头函数
- 实现带有自定义错误类型的适当错误处理
- 复杂类型场景使用函数重载
- 优先使用 async/await 而非 Promises

## 最佳实践
- 在 tsconfig.json 中启用严格模式
- 不可变属性使用 readonly
- 利用可辨识联合类型提高类型安全性
- 使用类型守卫进行运行时类型检查
- 实现适当的空值检查
- 避免不必要的类型断言

## 错误处理
- 为领域特定错误创建自定义错误类型
- 对可能失败的操作使用 Result 类型
- 实现适当的错误边界
- 使用带有类型化 catch 子句的 try-catch 块
- 正确处理 Promise 拒绝

## 模式
- 复杂对象创建使用构建者模式
- 数据访问实现仓储模式
- 对象创建使用工厂模式
- 利用依赖注入
- 使用模块模式实现封装