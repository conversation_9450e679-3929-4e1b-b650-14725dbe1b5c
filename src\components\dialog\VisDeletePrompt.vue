<!--
 * @Description: 
 * @Date: 2023-01-06 15:01:41
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-10-17 14:28:35
-->
<template>
  <div class="box">
    <el-dialog title="提示" :model-value="isShow" :before-close="cancle" width="30%">
      <div class="pop-header">
        <div class="waring">
          <img src="../../../assets/img/pop-filled.png" alt="" />
        </div>
        <div class="title">删除提示</div>
        <div @click="cancle" class="close"></div>
      </div>
      <p class="delTitle" v-html="lightWeightData.title"></p>
      <template #footer>
        <span class="dialog-footer">
          <div class="cancle" @click="cancle" v-show="lightWeightData.isCancel">取 消</div>
          <div class="delsubmit" type="primary" @click="define">删除</div>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue'
const emit = defineEmits(['delItem', 'cancleDel'])
const props = defineProps({
  lightWeightData: Object,
  isShow: Boolean
})
let lightWeightData: any = ref(props.lightWeightData)
const isShow = ref(props.isShow)
watch(
  () => props.isShow,
  (val) => {
    isShow.value = val
  }
)
watch(
  () => props.lightWeightData,
  (val) => {
    lightWeightData.value = val
  }
)
const define = () => {
  emit('delItem')
  isShow.value = false
}
const cancle = () => {
  emit('cancleDel')
}
</script>
<style lang="scss" scoped>
.pop-header {
  height: 50px;
  width: 96%;
  margin-left: 2%;
  display: flex;
  align-items: center;
  margin-top: -10px;
  .waring {
    width: 24px;
    height: 24px;
    img {
      width: 24px;
      height: 24px;
    }
  }
  .title {
    width: 100px;
    font-size: 16px;
    font-family:
      Source Han Sans CN,
      Source Han Sans CN-Regular;
    font-weight: 400;
    color: #ffffff;
    margin-left: 12px;
    margin-right: 58%;
  }
  .close {
    width: 24px;
    height: 24px;
    background: url('../../../assets/img/pop-close.png') no-repeat;
    background-size: 100% 100%;
  }
}
.delTitle {
  width: 300px;
  margin-left: 45px;
  font-size: 14px;
  font-family:
    Source Han Sans CN,
    Source Han Sans CN-Regular;
  font-weight: 400;
  text-align: left;
  color: #b9ccdf;
  margin-bottom: 10px;
}
:deep(.el-dialog) {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 8px;
  width: 480px !important;
  height: 178px !important;
  background: rgba(9, 27, 49, 0.8);
  border: 1px solid #2698b1;
  border-radius: 2px;
}

:deep(.dialog-footer) {
  width: 420px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .cancle {
    width: 68px;
    height: 32px;
    background: rgba(255, 255, 255, 0);
    border: 1px solid #41aaff;
    font-size: 14px;
    font-family:
      Source Han Sans CN,
      Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: center;
    color: #ecf4ff;
    line-height: 32px;
    margin-right: 10px;
  }
  .delsubmit {
    width: 68px;
    height: 32px;
    background: #ff5866;
    font-size: 14px;
    font-family:
      Source Han Sans CN,
      Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: center;
    color: #ecf4ff;
    line-height: 32px;
  }
}
:deep(.el-dialog__header) {
  display: none;
}
</style>
