/**
 * @fileoverview MapLibre制图打印管理器
 * @description 提供MapLibre地图的打印功能，将地图canvas转换为可打印的页面
 * <AUTHOR>
 * @version 1.0.0
 */

import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";

/**
 * @interface PrintConfig
 * @description 打印配置接口
 */
export interface PrintConfig {
  /** 打印页面标题 */
  title?: string;
  /** 图片质量(0-1) */
  quality?: number;
  /** 是否包含地图信息（坐标、缩放级别等） */
  includeMapInfo?: boolean;
  /** 页面方向 */
  orientation?: 'portrait' | 'landscape';
  /** 自定义样式 */
  customStyles?: string;
  /** 打印方式 */
  printMethod?: 'newWindow' | 'iframe' | 'direct';
}

/**
 * @interface PrintResult
 * @description 打印结果接口
 */
export interface PrintResult {
  success: boolean;
  dataUrl?: string;
  error?: string;
  mapInfo?: {
    center: [number, number];
    zoom: number;
    timestamp: string;
  };
}

/**
 * @class MapLibrePrintManager
 * @description MapLibre制图打印管理器类
 */
export class MapLibrePrintManager {
  private static instance: MapLibrePrintManager | null = null;
  
  private defaultConfig: Required<PrintConfig> = {
    title: '地图打印',
    quality: 0.9,
    includeMapInfo: true,
    orientation: 'landscape',
    customStyles: '',
    printMethod: 'iframe'
  };

  /**
   * @description 获取单例实例
   * @returns {MapLibrePrintManager} 打印管理器实例
   */
  public static getInstance(): MapLibrePrintManager {
    if (!MapLibrePrintManager.instance) {
      MapLibrePrintManager.instance = new MapLibrePrintManager();
    }
    return MapLibrePrintManager.instance;
  }

  /**
   * @description 执行制图打印
   * @param {Partial<PrintConfig>} config - 打印配置
   * @returns {Promise<PrintResult>} 打印结果
   */
  public async printMap(config?: Partial<PrintConfig>): Promise<PrintResult> {
    try {
      const finalConfig = { ...this.defaultConfig, ...config };
      
      console.log('🖨️ 开始制图打印...', finalConfig);
      
      // 获取MapLibre Canvas
      const canvas = this.getMapCanvas();
      if (!canvas) {
        throw new Error('无法获取地图Canvas元素');
      }

      // 验证Canvas状态
      if (!this.validateCanvas(canvas)) {
        throw new Error('Canvas状态无效，无法进行打印');
      }

      // 等待地图渲染完成
      await this.waitForMapRender();

      // 将Canvas转换为图片数据
      const dataUrl = await this.canvasToDataUrl(canvas, finalConfig.quality);
      
      // 获取地图信息
      const mapInfo = this.getMapInfo();
      
      // 根据配置选择打印方式
      switch (finalConfig.printMethod) {
        case 'iframe':
          await this.createIframePrint(dataUrl, finalConfig, mapInfo);
          break;
        case 'direct':
          await this.createDirectPrint(dataUrl, finalConfig, mapInfo);
          break;
        case 'newWindow':
        default:
          await this.createPrintPage(dataUrl, finalConfig, mapInfo);
          break;
      }
      
      console.log('✅ 制图打印准备完成');
      
      return {
        success: true,
        dataUrl,
        mapInfo
      };
      
    } catch (error) {
      console.error('❌ 制图打印失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * @description 获取地图Canvas元素
   * @returns {HTMLCanvasElement | null} Canvas元素
   */
  private getMapCanvas(): HTMLCanvasElement | null {
    try {
      const map = AppMaplibre.getMap();
      if (!map) {
        console.error('MapLibre地图未初始化');
        return null;
      }

      const canvas = map.getCanvas();
      if (!canvas) {
        console.error('无法获取MapLibre Canvas');
        return null;
      }

      console.log('✅ 成功获取MapLibre Canvas:', {
        width: canvas.width,
        height: canvas.height,
        clientWidth: canvas.clientWidth,
        clientHeight: canvas.clientHeight
      });

      return canvas;
    } catch (error) {
      console.error('获取MapLibre Canvas失败:', error);
      return null;
    }
  }

  /**
   * @description 验证Canvas状态
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @returns {boolean} 是否有效
   */
  private validateCanvas(canvas: HTMLCanvasElement): boolean {
    // 检查尺寸
    if (canvas.width === 0 || canvas.height === 0) {
      console.error('Canvas尺寸无效:', { width: canvas.width, height: canvas.height });
      return false;
    }

    // 检查WebGL上下文
    const gl = canvas.getContext('webgl') || canvas.getContext('webgl2');
    if (gl) {
      const contextAttributes = gl.getContextAttributes();
      if (!contextAttributes?.preserveDrawingBuffer) {
        console.warn('Canvas未启用preserveDrawingBuffer，打印可能为空白');
      }
    }

    return true;
  }

  /**
   * @description 等待地图渲染完成
   * @returns {Promise<void>}
   */
  private async waitForMapRender(): Promise<void> {
    return new Promise((resolve) => {
      const map = AppMaplibre.getMap();
      if (!map) {
        resolve();
        return;
      }

      if (map.loaded()) {
        // 等待两个渲染帧，确保内容完全渲染
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            resolve();
          });
        });
      } else {
        map.once('load', () => {
          requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              resolve();
            });
          });
        });
      }
    });
  }

  /**
   * @description 将Canvas转换为DataURL
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @param {number} quality - 图片质量
   * @returns {Promise<string>} DataURL
   */
  private async canvasToDataUrl(canvas: HTMLCanvasElement, quality: number): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // 使用PNG格式确保最佳质量
        const dataUrl = canvas.toDataURL('image/png', quality);
        
        if (!dataUrl || dataUrl === 'data:,') {
          reject(new Error('Canvas转换失败，生成空白图片'));
          return;
        }
        
        resolve(dataUrl);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * @description 获取地图信息
   * @returns {Object} 地图信息对象
   */
  private getMapInfo() {
    try {
      const map = AppMaplibre.getMap();
      if (!map) {
        return undefined;
      }

      const center = map.getCenter();
      const zoom = map.getZoom();
      const timestamp = new Date().toLocaleString('zh-CN');

      return {
        center: [center.lng, center.lat] as [number, number],
        zoom: Number(zoom.toFixed(2)),
        timestamp
      };
    } catch (error) {
      console.error('获取地图信息失败:', error);
      return undefined;
    }
  }

  /**
   * @description 创建打印页面（改进的新窗口方案）
   * @param {string} dataUrl - 图片数据URL
   * @param {Required<PrintConfig>} config - 打印配置
   * @param {Object} mapInfo - 地图信息
   */
  private async createPrintPage(
    dataUrl: string, 
    config: Required<PrintConfig>, 
    mapInfo: any
  ): Promise<void> {
    // 创建新的打印窗口
    const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
    
    if (!printWindow) {
      throw new Error('无法创建打印窗口，请检查浏览器弹窗设置');
    }

    // 构建包含关闭按钮的HTML内容
    const htmlContent = this.generateEnhancedPrintHtml(dataUrl, config, mapInfo);
    
    // 写入HTML内容
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // 设置多重关闭机制
    this.setupPrintWindowCloseHandlers(printWindow);

    // 等待图片加载完成后自动打印
    const img = printWindow.document.querySelector('img') as HTMLImageElement;
    if (img) {
      img.onload = () => {
        setTimeout(() => {
          printWindow.print();
        }, 500);
      };
      
      img.onerror = () => {
        console.error('打印页面图片加载失败');
        printWindow.close();
      };
    } else {
      // 如果没有图片元素，直接打印
      setTimeout(() => {
        printWindow.print();
      }, 1000);
    }
  }

  /**
   * @description 设置打印窗口的多重关闭处理机制
   * @param {Window} printWindow - 打印窗口对象
   */
  private setupPrintWindowCloseHandlers(printWindow: Window): void {
    let isClosed = false;

    // 安全关闭函数
    const safeClose = () => {
      if (!isClosed && !printWindow.closed) {
        try {
          printWindow.close();
          isClosed = true;
          console.log('🗑️ 打印窗口已关闭');
        } catch (error) {
          console.warn('关闭打印窗口时出错:', error);
        }
      }
    };

    // 1. 标准的打印完成事件
    printWindow.onafterprint = () => {
      console.log('📄 打印操作完成');
      setTimeout(safeClose, 500);
    };

    // 2. 窗口失去焦点时关闭（用户切换到其他窗口）
    printWindow.onblur = () => {
      setTimeout(() => {
        if (!printWindow.closed && !printWindow.document.hasFocus()) {
          console.log('🔄 窗口失焦，准备关闭');
          setTimeout(safeClose, 1000);
        }
      }, 2000);
    };

    // 3. 页面卸载前事件
    printWindow.onbeforeunload = () => {
      console.log('📋 打印窗口即将关闭');
      isClosed = true;
    };

    // 4. 超时自动关闭机制（30秒后）
    setTimeout(() => {
      if (!isClosed) {
        console.log('⏰ 超时自动关闭打印窗口');
        safeClose();
      }
    }, 30000);

    // 5. 定期检查窗口状态
    const checkInterval = setInterval(() => {
      if (printWindow.closed || isClosed) {
        clearInterval(checkInterval);
        isClosed = true;
        console.log('🔍 检测到窗口已关闭');
      }
    }, 1000);

    // 6. 清理定时器（最长运行35秒）
    setTimeout(() => {
      clearInterval(checkInterval);
    }, 35000);
  }

  /**
   * @description 生成增强版打印页面HTML（包含关闭按钮和用户提示）
   * @param {string} dataUrl - 图片数据URL  
   * @param {Required<PrintConfig>} config - 打印配置
   * @param {Object} mapInfo - 地图信息
   * @returns {string} HTML字符串
   */
  private generateEnhancedPrintHtml(
    dataUrl: string, 
    config: Required<PrintConfig>, 
    mapInfo: any
  ): string {
    const orientationClass = config.orientation === 'landscape' ? 'landscape' : 'portrait';
    
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${config.title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        @page {
            margin: 1cm;
            size: ${config.orientation === 'landscape' ? 'A4 landscape' : 'A4 portrait'};
        }
        
        body {
            font-family: 'Microsoft YaHei', SimSun, sans-serif;
            background: white;
            color: #333;
        }
        
        .no-print {
            display: block;
        }
        
        .print-container {
            width: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            page-break-inside: avoid;
            padding: 15px;
        }
        
        .control-bar {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .control-btn {
            margin: 0 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .print-btn {
            background: #007bff;
            color: white;
        }
        
        .close-btn {
            background: #dc3545;
            color: white;
        }
        
        .control-btn:hover {
            opacity: 0.8;
        }
        
        .print-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
        }
        
        .print-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .print-subtitle {
            font-size: 16px;
            color: #7f8c8d;
        }
        
        .map-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            max-width: 100%;
            max-height: 80%;
        }
        
        .map-image {
            max-width: 100%;
            max-height: 100%;
            border: 2px solid #bdc3c7;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            object-fit: contain;
        }
        
        .map-info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }
        
        .info-item {
            margin: 5px 15px;
        }
        
        .info-label {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .info-value {
            color: #34495e;
            margin-left: 5px;
        }
        
        .print-footer {
            margin-top: 5px;
            text-align: center;
            font-size: 12px;
            color: #95a5a6;
        }
        
        .user-tip {
            position: fixed;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 14px;
            color: #856404;
        }
        
        /* 自定义样式 */
        ${config.customStyles}
        
        /* 打印专用样式 */
        @media print {
            .no-print {
                display: none !important;
            }
            
            .control-bar,
            .user-tip {
                display: none !important;
            }
            
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .print-container {
                height: auto;
                min-height: 100vh;
            }
        }
    </style>
</head>
<body>
    <!-- 控制栏 -->
    <div class="control-bar no-print">
        <button class="control-btn print-btn" onclick="window.print()">🖨️ 打印</button>
        <button class="control-btn close-btn" onclick="window.close()">❌ 关闭</button>
    </div>
    
    <!-- 用户提示 -->
    <div class="user-tip no-print">
        💡 提示: 打印完成后，本窗口将自动关闭，您也可以手动点击右上角的关闭按钮
    </div>

    <div class="print-container ${orientationClass}">
        <div class="print-header">
            <h1 class="print-title">${config.title}</h1>
            <p class="print-subtitle">四川省乐山市沙湾区管线地图</p>
        </div>
        
        <div class="map-container">
            <img 
                class="map-image" 
                src="${dataUrl}" 
                alt="地图打印图片"
                style="display: block;"
            />
        </div>
        
        ${config.includeMapInfo && mapInfo ? this.generateMapInfoHtml(mapInfo) : ''}
        
        <div class="print-footer">
            <p>打印时间: ${new Date().toLocaleString('zh-CN')} | 四川省乐山市沙湾区供水管网地理信息系统</p>
        </div>
    </div>
    
    <script>
        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            if (e.key === 'Escape') {
                window.close();
            }
        });
        
        // 自动聚焦以启用键盘事件
        window.focus();
    </script>
</body>
</html>
    `;
  }

  /**
   * @description 生成地图信息HTML
   * @param {Object} mapInfo - 地图信息
   * @returns {string} 地图信息HTML
   */
  private generateMapInfoHtml(mapInfo: any): string {
    if (!mapInfo) return '';
    
    return `
        <div class="map-info">
            <div class="info-item">
                <span class="info-label">中心坐标:</span>
                <span class="info-value">${mapInfo.center[0].toFixed(6)}, ${mapInfo.center[1].toFixed(6)}</span>
            </div>
            <div class="info-item">
                <span class="info-label">缩放级别:</span>
                <span class="info-value">${mapInfo.zoom}</span>
            </div>
            <div class="info-item">
                <span class="info-label">生成时间:</span>
                <span class="info-value">${mapInfo.timestamp}</span>
            </div>
        </div>
    `;
  }

  /**
   * @description 使用iframe进行打印（推荐方案）
   * @param {string} dataUrl - 图片数据URL
   * @param {Required<PrintConfig>} config - 打印配置
   * @param {Object} mapInfo - 地图信息
   */
  private async createIframePrint(
    dataUrl: string, 
    config: Required<PrintConfig>, 
    mapInfo: any
  ): Promise<void> {
    // 创建隐藏的iframe
    const iframe = document.createElement('iframe');
    iframe.style.position = 'absolute';
    iframe.style.top = '-10000px';
    iframe.style.left = '-10000px';
    iframe.style.width = '0px';
    iframe.style.height = '0px';
    iframe.style.border = 'none';
    
    document.body.appendChild(iframe);

    try {
      // 获取iframe的文档对象
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) {
        throw new Error('无法访问iframe文档');
      }

      // 构建打印HTML内容
      const htmlContent = this.generateEnhancedPrintHtml(dataUrl, config, mapInfo);
      
      // 写入HTML内容
      iframeDoc.open();
      iframeDoc.write(htmlContent);
      iframeDoc.close();

      // 等待图片加载完成
      const img = iframeDoc.querySelector('img') as HTMLImageElement;
      if (img) {
        await new Promise<void>((resolve, reject) => {
          img.onload = () => {
            console.log('✅ iframe中的图片加载完成');
            resolve();
          };
          img.onerror = () => {
            reject(new Error('iframe中的图片加载失败'));
          };
          
          // 5秒超时
          setTimeout(() => {
            reject(new Error('iframe图片加载超时'));
          }, 5000);
        });
      }

      // 等待一小段时间确保内容完全渲染
      await new Promise(resolve => setTimeout(resolve, 500));

      // 执行打印
      const iframeWindow = iframe.contentWindow;
      if (iframeWindow) {
        // 聚焦到iframe窗口
        iframeWindow.focus();
        
        // 执行打印
        iframeWindow.print();
        
        console.log('✅ iframe打印执行完成');
      } else {
        throw new Error('无法访问iframe窗口对象');
      }

    } catch (error) {
      console.error('❌ iframe打印失败:', error);
      throw error;
    } finally {
      // 延迟移除iframe，确保打印操作完成
      setTimeout(() => {
        if (iframe && iframe.parentNode) {
          document.body.removeChild(iframe);
          console.log('🗑️ iframe已清理');
        }
      }, 2000);
    }
  }

  /**
   * @description 直接在当前页面打印
   * @param {string} dataUrl - 图片数据URL
   * @param {Required<PrintConfig>} config - 打印配置
   * @param {Object} mapInfo - 地图信息
   */
  private async createDirectPrint(
    dataUrl: string, 
    config: Required<PrintConfig>, 
    mapInfo: any
  ): Promise<void> {
    // 保存当前页面内容
    const originalContent = document.body.innerHTML;
    const originalTitle = document.title;
    
    try {
      // 构建打印HTML内容
      const htmlContent = this.generateEnhancedPrintHtml(dataUrl, config, mapInfo);
      
      // 提取body内容
      const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*)<\/body>/i);
      const printBodyContent = bodyMatch ? bodyMatch[1] : htmlContent;
      
      // 提取样式内容
      const styleMatch = htmlContent.match(/<style[^>]*>([\s\S]*)<\/style>/i);
      const printStyles = styleMatch ? styleMatch[1] : '';
      
      // 创建临时样式元素
      const tempStyleElement = document.createElement('style');
      tempStyleElement.innerHTML = printStyles;
      tempStyleElement.setAttribute('data-print-temp', 'true');
      document.head.appendChild(tempStyleElement);
      
      // 替换页面内容
      document.body.innerHTML = printBodyContent;
      document.title = config.title;
      
      // 等待图片加载
      const img = document.querySelector('img') as HTMLImageElement;
      if (img) {
        await new Promise<void>((resolve, reject) => {
          if (img.complete) {
            resolve();
          } else {
            img.onload = () => resolve();
            img.onerror = () => reject(new Error('图片加载失败'));
            
            // 5秒超时
            setTimeout(() => reject(new Error('图片加载超时')), 5000);
          }
        });
      }
      
      // 等待渲染完成
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 执行打印
      window.print();
      
      console.log('✅ 直接打印执行完成');
      
    } catch (error) {
      console.error('❌ 直接打印失败:', error);
      throw error;
    } finally {
      // 恢复原始页面内容
      setTimeout(() => {
        document.body.innerHTML = originalContent;
        document.title = originalTitle;
        
        // 移除临时样式
        const tempStyles = document.querySelectorAll('style[data-print-temp="true"]');
        tempStyles.forEach(style => style.remove());
        
        console.log('🔄 页面内容已恢复');
      }, 1000);
    }
  }
}

/**
 * @description 快捷打印函数
 * @param {Partial<PrintConfig>} config - 打印配置
 * @returns {Promise<PrintResult>} 打印结果
 */
export const printMapLibreMap = (config?: Partial<PrintConfig>) => {
  return MapLibrePrintManager.getInstance().printMap(config);
};

/**
 * @description 使用iframe方式打印（推荐）
 * @param {Partial<PrintConfig>} config - 打印配置  
 * @returns {Promise<PrintResult>} 打印结果
 */
export const printMapLibreMapWithIframe = (config?: Partial<PrintConfig>) => {
  return MapLibrePrintManager.getInstance().printMap({
    ...config,
    printMethod: 'iframe'
  });
};

/**
 * @description 使用新窗口方式打印
 * @param {Partial<PrintConfig>} config - 打印配置
 * @returns {Promise<PrintResult>} 打印结果  
 */
export const printMapLibreMapWithNewWindow = (config?: Partial<PrintConfig>) => {
  return MapLibrePrintManager.getInstance().printMap({
    ...config,
    printMethod: 'newWindow'
  });
};

/**
 * @description 直接在当前页面打印
 * @param {Partial<PrintConfig>} config - 打印配置
 * @returns {Promise<PrintResult>} 打印结果
 */
export const printMapLibreMapDirect = (config?: Partial<PrintConfig>) => {
  return MapLibrePrintManager.getInstance().printMap({
    ...config,
    printMethod: 'direct'
  });
}; 