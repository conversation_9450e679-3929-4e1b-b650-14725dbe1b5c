/**
 * @fileoverview 标绘服务类 - 封装绘制逻辑和业务功能
 * @description 提供统一的绘制服务接口，处理要素的创建、编辑、保存等操作
 * <AUTHOR>
 * @version 1.0.0
 */

import { ElMessage } from 'element-plus';
import { DrawModule, DrawData, DrawStatus, type DrawBar } from './DrawModule';
import type { PlotFeature } from '@/lib/maplibre/layer/types/LayerTypes';
import { Subscription } from 'rxjs';

/**
 * @description 绘制服务选项
 */
interface PlotServiceOptions {
  autoSave?: boolean;
  showMessages?: boolean;
  validateGeometry?: boolean;
}

/**
 * @description 绘制事件回调
 */
interface PlotEventCallbacks {
  onDrawStart?: () => void;
  onDrawFinish?: (feature: PlotFeature) => void;
  onDrawCancel?: () => void;
  onSaveSuccess?: (feature: PlotFeature) => void;
  onSaveError?: (error: Error) => void;
  onStatusChange?: (status: DrawStatus) => void;
  onEditStart?: (feature: PlotFeature) => void;
  onEditError?: (error: Error) => void;
}

/**
 * @description 标绘服务类
 * 封装绘制逻辑，提供统一的业务接口
 */
export class PlotService {
  private static instance: PlotService | null = null;
  private drawModule: DrawModule;
  private options: Required<PlotServiceOptions>;
  private callbacks: PlotEventCallbacks = {};
  
  // 订阅管理
  private dataSubscription: Subscription | null = null;
  private statusSubscription: Subscription | null = null;
  private showSubscription: Subscription | null = null;

  // 当前状态
  private currentDrawData: DrawData | null = null;
  private isInitialized: boolean = false;

  /**
   * @constructor
   * @param options - 服务配置选项
   */
  constructor(options: PlotServiceOptions = {}) {
    this.options = {
      autoSave: options.autoSave ?? false,
      showMessages: options.showMessages ?? true,
      validateGeometry: options.validateGeometry ?? true,
    };

    this.drawModule = DrawModule.getInstance();
  }

  /**
   * @description 获取单例实例
   * @param options - 服务配置选项
   */
  static getInstance(options?: PlotServiceOptions): PlotService {
    if (!PlotService.instance) {
      PlotService.instance = new PlotService(options);
    }
    return PlotService.instance;
  }

  /**
   * @description 初始化绘制服务
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // 初始化 DrawModule
      const initialized = await this.drawModule.initialize();
      if (!initialized) {
        throw new Error('DrawModule 初始化失败');
      }

      // 设置事件监听
      this.setupEventListeners();

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('PlotService 初始化失败:', error);
      
      if (this.options.showMessages) {
        ElMessage.error('标绘服务初始化失败');
      }
      
      return false;
    }
  }

  /**
   * @description 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听数据变化
    this.dataSubscription = DrawModule.dataChange.subscribe((data: DrawData) => {
      this.handleDataChange(data);
    });

    // 监听状态变化
    this.statusSubscription = DrawModule.statusChange.subscribe((status: DrawStatus) => {
      this.handleStatusChange(status);
    });

    // 监听面板显示变化
    this.showSubscription = DrawModule.showChange.subscribe((drawBar: DrawBar) => {
      this.handleShowChange(drawBar);
    });


  }

  /**
   * @description 处理数据变化
   */
  private handleDataChange(data: DrawData): void {
    this.currentDrawData = data;
    
    if (data.feature && data.geometry) {
      this.callbacks.onDrawFinish?.(data.feature);

      // 自动保存模式
      if (this.options.autoSave && data.feature.geojson?.geometry) {
        this.saveFeature(data.feature);
      }
    }
  }

  /**
   * @description 处理状态变化
   */
  private handleStatusChange(status: DrawStatus): void {
    this.callbacks.onStatusChange?.(status);

    // 状态提示消息（仅保留错误提示）
    if (this.options.showMessages && status === DrawStatus.ERROR) {
      ElMessage.error('操作出现错误');
    }
  }

  /**
   * @description 处理面板显示变化
   */
  private handleShowChange(drawBar: DrawBar): void {
    // 面板显示状态变化处理
  }

  /**
   * @description 开始绘制要素
   * @param type - 要素类型
   * @param featureData - 预设要素数据
   * @param preserveFeature - 是否保持现有要素信息（用于编辑模式下的重绘）
   */
  async startDraw(type: string, featureData?: Partial<PlotFeature>, preserveFeature?: PlotFeature): Promise<void> {
    try {
      await this.drawModule.startDraw(type, featureData, preserveFeature);
      this.callbacks.onDrawStart?.();

    } catch (error) {
      console.error('启动绘制失败:', error);
      
      if (this.options.showMessages) {
        ElMessage.error('启动绘制失败');
      }
      
      throw error;
    }
  }

  /**
   * @description 开始编辑要素
   * @param feature - 要编辑的要素
   */
  async startEdit(feature: PlotFeature): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('PlotService 未初始化');
    }

    try {
      // 调用DrawModule的编辑方法
      await this.drawModule.startEdit(feature);
      
      // 触发编辑开始回调
      if (this.callbacks.onEditStart) {
        this.callbacks.onEditStart(feature);
      }
      
    } catch (error) {
      console.error('PlotService: 启动编辑失败:', error);
      
      // 触发编辑错误回调
      if (this.callbacks.onEditError) {
        this.callbacks.onEditError(error as Error);
      }
      
      throw error;
    }
  }

  /**
   * @description 停止编辑模式
   */
  stopEdit(): void {
    this.drawModule.stopEdit();
  }

  /**
   * @description 取消编辑并恢复原要素
   */
  cancelEdit(): void {
    try {
      this.drawModule.cancelEdit();
      
      if (this.options.showMessages) {
        ElMessage.info('已取消编辑');
      }
    } catch (error) {
      console.error('PlotService: 取消编辑失败:', error);
      
      if (this.options.showMessages) {
        ElMessage.error(`取消编辑失败: ${(error as Error).message}`);
      }
      
      throw error;
    }
  }

  /**
   * @description 停止绘制
   */
  stopDraw(): void {
    this.clearCurrentDrawing();
    this.drawModule.stopDraw();
    this.callbacks.onDrawCancel?.();
    
    if (this.options.showMessages) {

    }
  }

  /**
   * @description 清除当前绘制的图形
   */
  clearCurrentDrawing(): void {
    this.drawModule.clearCurrentDrawing();
    
    if (this.options.showMessages) {

    }
  }

  /**
   * @description 保存要素
   * @param feature - 要素数据
   */
  async saveFeature(feature: PlotFeature): Promise<boolean> {
    try {
      // 几何图形验证
      if (this.options.validateGeometry && !this.validateFeatureGeometry(feature)) {
        throw new Error('要素几何图形无效');
      }

      const success = await this.drawModule.saveFeature(feature);
      
      if (success) {
        this.callbacks.onSaveSuccess?.(feature);
        
        if (this.options.showMessages) {
          ElMessage.success('要素保存成功');
        }
      } else {
        throw new Error('保存失败');
      }

      return success;
    } catch (error) {
      console.error('保存要素失败:', error);
      this.callbacks.onSaveError?.(error as Error);
      
      if (this.options.showMessages) {
        ElMessage.error(`保存失败: ${(error as Error).message}`);
      }
      
      return false;
    }
  }

  /**
   * @description 更新要素
   * @param feature - 更新后的要素数据
   */
  async updateFeature(feature: PlotFeature): Promise<boolean> {
    try {
      // 几何图形验证
      if (this.options.validateGeometry && !this.validateFeatureGeometry(feature)) {
        throw new Error('要素几何图形无效');
      }

      const success = await this.drawModule.updateFeature(feature);
      
      if (success && this.options.showMessages) {
        ElMessage.success('要素更新成功');
      }

      return success;
    } catch (error) {
      console.error('更新要素失败:', error);
      
      if (this.options.showMessages) {
        ElMessage.error(`更新失败: ${(error as Error).message}`);
      }
      
      return false;
    }
  }

  /**
   * @description 删除要素
   * @param featureId - 要素ID
   */
  async deleteFeature(featureId: number): Promise<boolean> {
    try {
      const success = await this.drawModule.deleteFeature(featureId);
      
      if (success && this.options.showMessages) {
        ElMessage.success('要素删除成功');
      }

      return success;
    } catch (error) {
      console.error('删除要素失败:', error);
      
      if (this.options.showMessages) {
        ElMessage.error(`删除失败: ${(error as Error).message}`);
      }
      
      return false;
    }
  }

  /**
   * @description 获取所有要素
   */
  async getAllFeatures(): Promise<PlotFeature[]> {
    return await this.drawModule.getAllFeatures();
  }

  /**
   * @description 获取当前绘制状态
   */
  getStatus(): DrawStatus {
    return this.drawModule.getStatus();
  }

  /**
   * @description 获取当前绘制数据
   */
  getCurrentDrawData(): DrawData | null {
    return this.currentDrawData;
  }

  /**
   * @description 设置事件回调
   * @param callbacks - 事件回调函数
   */
  setCallbacks(callbacks: PlotEventCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * @description 更新服务选项
   * @param options - 新的服务选项
   */
  updateOptions(options: Partial<PlotServiceOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * @description 验证要素几何图形
   * @param feature - 要素数据
   */
  private validateFeatureGeometry(feature: PlotFeature): boolean {
    if (!feature.geojson?.geometry) {
      console.warn('要素缺少几何图形');
      return false;
    }

    const { type, coordinates } = feature.geojson.geometry;

    // 基础坐标验证
    if (!coordinates || !Array.isArray(coordinates)) {
      console.warn('要素坐标数据无效');
      return false;
    }

    // 根据类型验证具体坐标结构
    switch (type) {
      case 'Point':
        return this.validatePointCoordinates(coordinates);
      case 'LineString':
        return this.validateLineStringCoordinates(coordinates);
      case 'Polygon':
        return this.validatePolygonCoordinates(coordinates);
      default:
        console.warn(`未知的几何类型: ${type}`);
        return false;
    }
  }

  /**
   * @description 验证点坐标
   */
  private validatePointCoordinates(coordinates: any): boolean {
    return Array.isArray(coordinates) && 
           coordinates.length >= 2 && 
           typeof coordinates[0] === 'number' && 
           typeof coordinates[1] === 'number';
  }

  /**
   * @description 验证线坐标
   */
  private validateLineStringCoordinates(coordinates: any): boolean {
    return Array.isArray(coordinates) && 
           coordinates.length >= 2 && 
           coordinates.every((coord: any) => this.validatePointCoordinates(coord));
  }

  /**
   * @description 验证面坐标
   */
  private validatePolygonCoordinates(coordinates: any): boolean {
    return Array.isArray(coordinates) && 
           coordinates.length >= 1 && 
           Array.isArray(coordinates[0]) && 
           coordinates[0].length >= 4 && 
           coordinates[0].every((coord: any) => this.validatePointCoordinates(coord));
  }

  /**
   * @description 获取要素类型名称
   */
  private getFeatureTypeName(type: string): string {
    const names: Record<string, string> = {
      point: '点',
      linestring: '线',
      polygon: '多边形',
      rectangle: '矩形',
    };
    return names[type] || type;
  }

  /**
   * @description 重新加载localStorage中的要素数据到地图
   */
  async reloadStoredFeatures(): Promise<void> {
    await this.drawModule.reloadStoredFeatures();
  }

  /**
   * @description 获取调试信息
   */
  async getDebugInfo(): Promise<{ 
    layerFeatureCount: number;
    storedFeatureCount: number;
    isInitialized: boolean;
  }> {
    return {
      layerFeatureCount: this.drawModule.getLayerFeatureCount(),
      storedFeatureCount: await this.drawModule.getStoredFeatureCount(),
      isInitialized: this.isInitialized
    };
  }

  /**
   * @description 清理资源
   */
  destroy(): void {
    // 取消订阅
    this.dataSubscription?.unsubscribe();
    this.statusSubscription?.unsubscribe();
    this.showSubscription?.unsubscribe();

    // 清理状态
    this.currentDrawData = null;
    this.callbacks = {};
    this.isInitialized = false;
    
    // 清理单例
    PlotService.instance = null;


  }
} 