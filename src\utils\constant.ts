// 图标类型
export const iconType = [
  {
    value: 'layer_icon',
    label: '图层图标'
  },
  {
    value: 'app_function_icon',
    label: 'App功能图标'
  },
  {
    value: 'coordinate_icon',
    label: '坐标类型图标'
  },
]
// 系统
export const system = [
  {
    value: 'two_pipe_network',
    label: '二维管网平台'
  },
  {
    value: 'three_pipe_network',
    label: '三维管网平台'
  },
  {
    value: 'gis_information_service',
    label: 'GIS信息服务平台'
  },
  {
    value: 'background_manage',
    label: '后台管理系统'
  },
]
// 系统内置
export const sysBuilt = [
  {
    label: '否',
    value: '0'
  },
  {
    label: '是',
    value: '1'
  }
]
// 操作状态
export const logStatus = [
  {
    label: '异常',
    value: '0'
  },
  {
    label: '正常',
    value: '1'
  }
]
// 菜单类型
export const menuState = [
  {
    value: 'S',
    label: '系统'
  },
  {
    value: 'C',
    label: '目录'
  },
  {
    value: 'M',
    label: '菜单'
  },
  {
    value: 'B',
    label: '按钮'
  }
];
// 角色状态
export const userState = [
  {
    label: '可用',
    value: '0'
  },
  {
    label: '禁用',
    value: '1'
  }
]
// 来源
export const sourceState = [
  {
    label: '页面新增',
    value: 'latias'
  },
  {
    label: '导入',
    value: 'import'
  },
  {
    label: '接口上报',
    value: 'report'
  }
]

// iot first type 接口文档提取
// export const iotFirstType = [
//   {
//     key: 'device_first_type_iot',
//     label: 'iot设备'
//   },
//   {
//     key: 'device_first_type_trade',
//     label: '行业设备'
//   },
//   {
//     key: 'device_first_type_remote_spreadsheet',
//     label: '远传表'
//   }
// ]

// // iot second type 接口文档提取
// export const iotSecondType = [
//   {
//     key: 'iot_device_type_wqtm',
//     label: '水质检测仪'
//   },
//   {
//     key: 'iot_device_type_flow',
//     label: '流量计'
//   },
//   {
//     key: 'iot_device_type_pressure',
//     label: '压力计传感器'
//   },
//   {
//     key: 'iot_device_type_level',
//     label: '液位传感器'
//   }
// ]
// 设备类型
export const deviceType = [
  {
    label: '噪声设备',
    value: '1568299636966359043'
  },
  {
    label: '压力表',
    value: '1568299636966359049'
  },
  {
    label: '流量计',
    value: '1568299636966359093'
  },
  {
    label: '远传水表',
    value: '1568299636966359044'
  },
  {
    label: '入户远传表',
    value: '1568299636966359042'
  },
]
// 用户类型
export const userType = [
  {
    label: '政府用户',
    value: 'gov'
  },
  {
    label: '企业用户',
    value: 'ent'
  },
  {
    label: '虚拟',
    value: 'virtually'
  },
  {
    label: '实体',
    value: 'entity'
  },
]