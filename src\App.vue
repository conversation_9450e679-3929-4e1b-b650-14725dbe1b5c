<!--
  @fileoverview 应用程序根组件
  @description 作为整个Vue应用的根组件，负责配置Element Plus国际化并提供路由视图容器
  <AUTHOR>
  @version 1.0.0
-->

<script setup lang="ts">
// 导入Element Plus中文语言包
// import { useUserInfo, useToken } from '@latias/vue';
import locale from 'element-plus/dist/locale/zh-cn';
onMounted(() => {
  // const userInfo = useUserInfo()
  // const token = useToken()
  // console.log(userInfo)
  // console.log(token)
})
</script>

<template>
  <!-- Element Plus配置提供器，设置中文语言环境 -->
  <el-config-provider :locale="locale">
    <!-- 路由视图容器，用于渲染当前路由对应的组件 -->
    <router-view />
  </el-config-provider>
</template>

<style scoped></style>
