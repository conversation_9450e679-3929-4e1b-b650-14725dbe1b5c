<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑设备' : '设备详情'"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="!isEdit"
      v-loading="loading"
    >
      <el-row :gutter="20" v-for="(rowFields, rowIndex) in fieldRows" :key="rowIndex">
        <el-col :span="12" v-for="field in rowFields" :key="field.fieldKey">
          <el-form-item :label="field.fieldName" :prop="field.fieldKey">
            <!-- 文本输入框 -->
            <el-input
              v-if="field.displayForm === 'text'"
              v-model="formData[field.fieldKey]"
              :placeholder="`请输入${field.fieldName}`"
            />

            <!-- 下拉选择框 -->
            <el-select
              v-else-if="field.displayForm === 'select'"
              v-model="formData[field.fieldKey]"
              :placeholder="`请选择${field.fieldName}`"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="(label, value) in field.dictMap"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>

            <!-- 开关组件 -->
            <el-switch
              v-else-if="field.displayForm === 'switch'"
              v-model="formData[field.fieldKey]"
              :active-value="'1'"
              :inactive-value="'0'"
            />

            <!-- 文件上传 -->
            <div v-else-if="field.displayForm === 'file'" class="file-display">
              <!-- 详情状态：只显示图片 -->
              <div v-if="!isEdit">
                <div v-if="getFilePreview(field)" class="file-preview">
                  <el-image
                    :src="getFilePreview(field)!"
                    style="width: 120px; height: 120px"
                    fit="cover"
                    :preview-src-list="[getFilePreview(field)!]"
                  />
                </div>
                <div v-else class="no-file-hint">
                  <span class="text-gray-400">暂无图片</span>
                </div>
              </div>

              <!-- 编辑状态：显示图片和上传按钮 -->
              <div v-else class="edit-mode">
                <!-- 如果有图片，显示图片预览 -->
                <div v-if="getFilePreview(field)" class="file-preview-edit">
                  <el-image
                    :src="getFilePreview(field)!"
                    style="width: 120px; height: 120px"
                    fit="cover"
                    :preview-src-list="[getFilePreview(field)!]"
                  />
                </div>
                <!-- 上传组件（编辑模式下始终显示） -->
                <div class="upload-area">
                  <UploadFile
                    :type="['jpg', 'png', 'jpeg', 'webp']"
                    :file-url="getFilePreview(field) || ''"
                    :btn-label="getFilePreview(field) ? '更换图片' : '上传图片'"
                    :disabled="false"
                    @update:file-url="handleFileUrlUpdate(field.fieldKey, $event)"
                    @upload-success="handleUploadSuccess(field.fieldKey, $event)"
                  />
                </div>
              </div>
            </div>

            <!-- 默认文本输入 -->
            <el-input
              v-else
              v-model="formData[field.fieldKey]"
              :placeholder="`请输入${field.fieldName}`"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button class="clear-btn" @click="handleClose">取消</el-button>
        <el-button v-if="isEdit" type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { type FormInstance } from 'element-plus';
import type { IotDeviceField } from './SpatialQuery.vue';
import UploadFile from './UploadFile.vue'

// Props 定义
interface Props {
  modelValue: boolean;
  isEdit: boolean;
  loading?: boolean;
  data?: IotDeviceField[];
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  data: () => []
});

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'submit': [data: Record<string, any>];
}>();

// 响应式数据
const dialogVisible = ref(false);
const formRef = ref<FormInstance>();
const submitLoading = ref(false);

// 动态表单数据
const formData = ref<Record<string, any>>({});

// 存储图片上传成功后的完整信息
const imageDataMap = ref<Record<string, { code: string; url: string; fileName: string }>>({});

// 存储图片显示URL（用于界面显示）
const imageUrlMap = ref<Record<string, string>>({});

// 计算属性：字段配置
const fieldConfigs = computed(() => {
  if (!props.data || props.data.length === 0) return [];
  return props.data;
});

// 计算属性：将字段分组为行（每行2个字段）
const fieldRows = computed(() => {
  let fields = fieldConfigs.value;

  // 在编辑模式下，过滤掉 device_code 和 first_device_type_code 字段
  if (props.isEdit) {
    fields = fields.filter(field =>
      field.fieldKey !== 'device_code' &&
      field.fieldKey !== 'first_device_type_code'
    );
  }

  const rows: IotDeviceField[][] = [];

  for (let i = 0; i < fields.length; i += 2) {
    rows.push(fields.slice(i, i + 2));
  }

  return rows;
});

// 动态表单校验规则
const formRules = computed(() => {
  const rules: Record<string, any[]> = {};

  fieldConfigs.value.forEach(field => {
    const fieldRules: any[] = [];

    // 根据字段类型添加基本校验
    if (field.fieldKey === 'device_name' || field.fieldKey === 'device_code') {
      fieldRules.push({ required: true, message: `请输入${field.fieldName}`, trigger: 'blur' });
    }

    // 经纬度特殊校验
    if (field.fieldKey === 'longitude') {
      fieldRules.push(
        { required: true, message: '请输入经度', trigger: 'blur' },
        { pattern: /^-?((1[0-7]\d)|(\d{1,2}))(\.\d+)?$/, message: '请输入有效的经度值(-180到180)', trigger: 'blur' }
      );
    }

    if (field.fieldKey === 'latitude') {
      fieldRules.push(
        { required: true, message: '请输入纬度', trigger: 'blur' },
        { pattern: /^-?([0-8]?\d)(\.\d+)?$/, message: '请输入有效的纬度值(-90到90)', trigger: 'blur' }
      );
    }

    // 文本长度限制
    if (field.displayForm === 'text' && field.fieldKey !== 'longitude' && field.fieldKey !== 'latitude') {
      fieldRules.push({ max: 200, message: `${field.fieldName}长度不能超过 200 个字符`, trigger: 'blur' });
    }

    if (fieldRules.length > 0) {
      rules[field.fieldKey] = fieldRules;
    }
  });

  return rules;
});

// 初始化表单数据
const initFormData = () => {
  const newFormData: Record<string, any> = {};

  if (props.data && props.data.length > 0) {

    // 从设备数据中提取表单值
    props.data.forEach(field => {
      newFormData[field.fieldKey] = field.fieldValues || '';
    });
  } else {
    // 初始化空表单
    fieldConfigs.value.forEach(field => {
      newFormData[field.fieldKey] = '';
    });
  }
  formData.value = newFormData;

  // 清除表单验证状态
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 处理文件URL更新（用于界面显示）
const handleFileUrlUpdate = (fieldKey: string, fileUrl: string) => {
  // 存储显示用的URL
  imageUrlMap.value[fieldKey] = fileUrl;
};

// 处理图片上传成功
const handleUploadSuccess = (fieldKey: string, uploadData: any) => {
  // 存储完整的图片信息
  imageDataMap.value[fieldKey] = {
    code: uploadData.code,
    url: uploadData.url,
    fileName: uploadData.fileName
  };
  // 同时更新显示URL
  imageUrlMap.value[fieldKey] = uploadData.url;
};

// 获取文件预览
const getFilePreview = (field: IotDeviceField): string | undefined => {
  if (field.displayForm === 'file') {
    // 优先使用新上传的图片URL
    if (imageUrlMap.value[field.fieldKey]) {
      return imageUrlMap.value[field.fieldKey];
    }

    // 检查是否有文件值和字典映射
    if (field.fieldValues && field.dictMap) {
      // 从 dictMap 中获取对应的文件信息
      const fileInfo = field.dictMap[field.fieldValues];

      if (fileInfo) {
        // 文件信息格式: "文件名::图片URL"
        const parts = fileInfo.split('::');
        if (parts.length === 2) {
          // 返回图片URL部分
          return parts[1];
        }
        // 如果没有 :: 分隔符，直接返回整个值
        return fileInfo;
      }
    }
  }
  return undefined;
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  const newFormData: Record<string, any> = {};
  fieldConfigs.value.forEach(field => {
    newFormData[field.fieldKey] = '';
  });
  formData.value = newFormData;
  // 清空图片相关映射
  imageDataMap.value = {};
  imageUrlMap.value = {};
  formRef.value?.clearValidate();
};


// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    submitLoading.value = true;
    // 准备提交数据，将图片的 code 更新到对应字段
    const submitData = { ...formData.value };

    // 遍历 imageDataMap，将图片的 code 更新到对应的字段中
    Object.keys(imageDataMap.value).forEach(fieldKey => {
      if (imageDataMap.value[fieldKey]) {
        submitData[fieldKey] = imageDataMap.value[fieldKey].code;
      }
    });

    // 发送数据给父组件，包含图片数据信息
    emit('submit', {
      formData: submitData,
      imageData: imageDataMap.value
    });
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitLoading.value = false;
  }
};

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
      initFormData();
    }
  },
  { immediate: true }
);

// 监听对话框内部状态变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false);
  }
});

// 监听数据变化，重新初始化表单
watch(
  () => props.data,
  () => {
    if (dialogVisible.value) {
      initFormData();
    }
  },
  { deep: true }
);

// 暴露方法给父组件
defineExpose({
  resetForm,
  validate: () => formRef.value?.validate()
});
</script>

<style scoped>
.file-display {
  width: 100%;
}

/* 详情模式的图片预览 */
.file-preview {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.file-preview .el-image {
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-preview .el-image:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

/* 编辑模式 */
.edit-mode {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 编辑模式的图片预览 */
.file-preview-edit {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.file-preview-edit .el-image {
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-preview-edit .el-image:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

/* 上传按钮区域 */
.upload-area {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

/* 无文件提示 */
.no-file-hint {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  min-height: 40px;
  color: var(--el-color-info);
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-select) {
  width: 100%;
}
</style>
