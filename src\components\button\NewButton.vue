<template>
  <el-button class="primary-btn" :loading="props.loading" color="#00A7FF">
    <slot>请填充内容</slot>
  </el-button>
</template>

<script setup lang="ts">
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
});
</script>

<style scoped lang="scss">
.primary-btn {
  min-width: 80px;
  height: 34px;
  color: #fff;
  font: 400 16px SansCN-Regular;
  border: 1px solid #bcf4ff;

  &:hover,
  &:focus {
    color: #fff;
    border: 1px solid #bcf4ff;
  }
}
</style>
