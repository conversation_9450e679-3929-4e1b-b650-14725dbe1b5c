export const adminRouter = {
  path: '/admin',
  name: 'adminModule',
  meta: {
    title: '后台模块'
  },
  redirect: '/admin/icon',
  component: () => import('@/views/admin/index.vue'),
  children: [
    {
      path: 'icon',
      name: 'IconManage',
      meta: {
        title: '图标管理',
        icon: 'icon',
        acIcon: 'icon-ac'
      },
      component: () => import('@/views/admin/iconManage/index.vue'),
    },
    {
      path: 'app',
      name: 'AppManage',
      meta: {
        title: 'App管理',
        icon: 'app',
        acIcon: 'app-ac'
      },
      component: () => import('@/views/admin/appManage/index.vue'),
    },
    {
      path: 'dict',
      name: 'DictManage',
      meta: {
        title: '字典管理',
        icon: 'dict',
        acIcon: 'dict-ac'
      },
      component: () => import('@/views/admin/dictManage/index.vue'),
    },
    {
      path: 'user',
      name: 'userManage',
      meta: {
        title: '用户管理',
        icon: 'user',
        acIcon: 'user-ac'
      },
      component: () => import('@/views/admin/userManage/index.vue'),
    },
    {
      path: 'role',
      name: 'roleManage',
      meta: {
        title: '角色管理',
        icon: 'role',
        acIcon: 'role-ac'
      },
      component: () => import('@/views/admin/roleManage/index.vue'),
    },
    {
      path: 'log',
      name: 'logManage',
      meta: {
        title: '日志中心',
        icon: 'log',
        acIcon: 'log-ac'
      },
      component: () => import('@/views/admin/logManage/index.vue'),
      redirect: '/log/stat',
      children: [
        {
          path: 'stat',
          name: 'LogStat',
          meta: { title: '日志统计' },
          component: () => import('@/views/admin/logManage/stat/index.vue')
        },
        {
          path: 'user',
          name: 'LogUser',
          meta: { title: '用户日志' },
          component: () => import('@/views/admin/logManage/user/index.vue')
        },
        {
          path: 'data',
          name: 'LogData',
          meta: { title: '数据日志' },
          component: () => import('@/views/admin/logManage/data/index.vue')
        },
      ]
    },
    {
      path: 'server',
      name: 'serverManage',
      meta: {
        title: '服务器管理',
        icon: 'server',
        acIcon: 'server-ac'
      },
      component: () => import('@/views/admin/serverManage/index.vue'),
      redirect: '/server/info',
      children: [
        {
          path: 'info',
          name: 'InfoManage',
          meta: { title: '信息管理' },
          component: () => import('@/views/admin/serverManage/info/index.vue')
        },
        {
          path: 'monitor',
          name: 'Monitor',
          meta: { title: '服务器监控' },
          component: () => import('@/views/admin/serverManage/JavaMonitoring.vue')
        },
        {
          path: 'cache',
          name: 'Cache',
          meta: { title: '缓存监控' },
          component: () => import('@/views/admin/serverManage/CacheMonitoring.vue')
        },
      ]
    }
  ]
}