<template>
  <BaseCard title="设备分类统计">
    <div class="chart" ref="categoryRef"></div>
  </BaseCard>
</template>
<script lang="ts" setup>
import { initCategoryEchart } from "@/lib/echarts";
const { initChart } = useEchart();
const categoryRef = ref();
const getChart = () => {
  categoryRef.value && initChart(categoryRef.value, initCategoryEchart());
};
onMounted(() => {
  getChart();
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 180px;
  overflow: hidden;
}
</style>
