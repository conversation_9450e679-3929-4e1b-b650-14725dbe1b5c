<template>
  <div>
    <div class="drawer-lt" v-if="drawer">
      <DeviceStat />
      <DeviceOnline />
      <DeviceType />
      <ValveWellhead />
    </div>
    <div
      class="lt flex-c"
      @click="toggle"
      :class="drawer ? 'show-card' : 'hidden-card'"
    >
      <img :src="getImages('cesium/right-icon.png')" alt="" srcset="" />
    </div>
    <div class="drawer-rt" v-if="drawerView">
      <FireWellhead />
      <PipeDiameter />
      <PipeStat />
    </div>
    <div
      class="rt flex-c"
      @click="toggle"
      :class="drawerView ? 'show-card-rt' : 'hidden-card-rt'"
    >
      <img :src="getImages('cesium/left-icon.png')" alt="" srcset="" />
    </div>
  </div>
</template>

<script setup lang="ts">
import DeviceStat from "./compontent/DeviceStat.vue";
import DeviceOnline from "./compontent/DeviceOnline.vue";
import DeviceType from "./compontent/DeviceType.vue";
import ValveWellhead from "./compontent/ValveWellhead.vue";
import FireWellhead from "./compontent/FireWellhead.vue";
import PipeDiameter from "./compontent/PipeDiameter.vue";
import PipeStat from "./compontent/PipeStat.vue";
import { getImages } from "@/utils/getImages";
import { useDrawerStore } from "@/stores/drawer";
const drawer = ref(true);
const drawerView = ref(true);
const drawerStore = useDrawerStore();
const toggle = () => {
  drawer.value = !drawer.value;
  drawerView.value = !drawerView.value;
  drawerStore.getDrawer(drawerView.value);
};
// const toggleRt = () => {
//   drawerView.value = !drawerView.value;
//   drawer.value = !drawer.value;
//   drawerStore.getDrawer(drawerView.value);
// };
</script>

<style lang="scss" scoped>
.drawer-lt {
  width: 390px;
  background: #fff;
  position: absolute;
  border-radius: 6px;
  top: 14px;
  bottom: 14px;
  left: 14px;
  z-index: 1;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 5px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: transparent;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}
.lt {
  width: 16px;
  height: 48px;
  border-radius: 0px 2px 2px 0px;
  background: rgba(236, 244, 255, 0.8);
  position: absolute;
  left: 404px;
  top: 50%;
  z-index: 1;
  cursor: pointer;
}
.rt {
  width: 16px;
  height: 48px;
  border-radius: 0px 2px 2px 0px;
  background: rgba(236, 244, 255, 0.8);
  position: absolute;
  right: 404px;
  top: 50%;
  z-index: 1;
  cursor: pointer;
}
.drawer-rt {
  width: 390px;
  background: #fff;
  border-radius: 6px;
  position: absolute;
  top: 14px;
  bottom: 14px;
  right: 14px;
  z-index: 1;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 5px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: transparent;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}
.show-card {
  animation: fade-in 0.2s linear forwards;
}
.hidden-card {
  animation: fade-out 0.2s linear forwards;
}
@keyframes fade-out {
  0% {
    left: 0;
  }

  100% {
    left: 5px;
  }
}
@keyframes fade-in {
  0% {
    left: 0;
  }
  100% {
    left: 404px;
  }
}
.show-card-rt {
  animation: fade-in-rt 0.2s linear forwards;
}
.hidden-card-rt {
  animation: fade-out-rt 0.2s linear forwards;
}
@keyframes fade-out-rt {
  0% {
    right: 0;
  }

  100% {
    right: 5px;
  }
}
@keyframes fade-in-rt {
  0% {
    right: 0;
  }
  100% {
    right: 404px;
  }
}
</style>
