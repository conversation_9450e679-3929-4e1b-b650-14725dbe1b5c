
import type { TreeKey } from "element-plus/es/components/tree-v2/src/types"
export interface QueryForm {
  configName: string;
  configKey: string;
  type: string;
  pageSize: number;
  pageNum: number;
}
export interface SysRole {
  /**
   * 参数名
   */
  configName: string
  /**
   * ID
   */
  id: string
  /**
   * 键名
   */
  configKey: string
  /**
   * 键值
   */
  configValue: string
  /**
   * 系统内置
   */
  type: string
  /**
   * 备注
   */
  remark?: string

}