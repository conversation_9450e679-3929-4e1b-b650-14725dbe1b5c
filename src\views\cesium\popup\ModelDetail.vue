<template>
  <custom-card
    @closeHandler="close"
    :width="'600px'"
    :top="'90px'"
    :left="'600px'"
    :title="'模型详情'"
  >
    <!-- <div grid="~ cols-2 gap-y-3 gap-x-3">
      <base-key-value label="类型" :value="dataInfo?.gl" />
      <base-key-value label="管线点编号" :value="dataInfo?.gxddh" />
      <base-key-value label="属性" :value="dataInfo?.sx" />
      <base-key-value label="附属物" :value="dataInfo?.fsw" />
      <base-key-value label="地面高程" :value="dataInfo?.dmgc" />
      <base-key-value label="井深" :value="dataInfo?.js" />
      <base-key-value label="经度" :value="dataInfo?.longitude" />
      <base-key-value label="纬度" :value="dataInfo?.latitude" />
      <base-key-value label="井盖材质" :value="dataInfo?.jgcz" />
      <base-key-value label="所在道路" :value="dataInfo?.szdl" />
      <base-key-value label="井盖规格" :value="dataInfo?.jggg" />
    </div> -->
    <el-form
      ref="form"
      :model="formData"
      class="admin-sub-form"
      label-width="auto"
      :disabled="true"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类型：">
            <el-input
              class=""
              v-model.trim="formData.gl"
              placeholder="请输入类型"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="属性：">
            <el-input
              class=""
              v-model.trim="formData.sx"
              placeholder="请输入属性"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="附属物：">
            <el-input
              class=""
              v-model.trim="formData.fsw"
              placeholder="请输入附属物"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="井深：">
            <el-input
              class=""
              v-model.trim="formData.js"
              placeholder="请输入井深"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="经度：">
            <el-input
              class=""
              v-model.trim="formData.longitude"
              placeholder="请输入经度"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度：">
            <el-input
              class=""
              v-model.trim="formData.latitude"
              placeholder="请输入纬度"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管线点编号：">
            <el-input
              class=""
              v-model.trim="formData.gxddh"
              placeholder="请输入管线点编号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地面高程：">
            <el-input
              class=""
              v-model.trim="formData.dmgc"
              placeholder="请输入地面高程"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井盖材质：">
            <el-input
              class=""
              v-model.trim="formData.jgcz"
              placeholder="请输入井盖材质"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在道路：">
            <el-input
              class=""
              v-model.trim="formData.szdl"
              placeholder="请输入所在道路"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井盖规格：">
            <el-input
              class=""
              v-model.trim="formData.jggg"
              placeholder="请输入井盖规格"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-card>
</template>
<script lang="ts" setup>
import { pointListByBms } from "@/api/analysis";
import { pipeNodeDetail } from "@/api/pipeNode";
/**
 * @interface Props
 * @description 组件属性接口
 */
interface Props {
  /** 管点基本信息 */
  id?: string;
}

const props = withDefaults(defineProps<Props>(), {
  id: () => "",
});
interface FormData {
  gl: any;
  sx: string;
  fsw: string;
  js: string;
  longitude: string;
  latitude: string;
  gxddh: string;
  dmgc: string;
  jgcz: string;
  szdl: string;
  jggg: string;
}
const initFormData = () => {
  return {
    gl: "",
    sx: "",
    fsw: "",
    js: "",
    longitude: "",
    latitude: "",
    gxddh: "",
    dmgc: "",
    jgcz: "",
    szdl: "",
    jggg: "",
  };
};
const formData = ref<FormData>(initFormData());
const dataInfo = ref<any>();
const getDetail = async () => {
  // const result = await pipeNodeDetail(props.id);
  const {code, data} = await pointListByBms([props.id])
  if(code === 200) {
    if(data.length > 0) {
      dataInfo.value = data[0];
      formData.value = data[0];
    }
  }
};

const close = () => {
  useDialogStore().closeDialog("ModelDetail");
};
onMounted(() => {
  getDetail();
});
</script>
<style lang="scss" scoped></style>
