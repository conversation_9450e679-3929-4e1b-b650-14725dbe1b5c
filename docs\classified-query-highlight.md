# 分类查询点位高亮功能说明

## 功能概述

为 `ClassifiedQuery.vue` 组件实现了查询结果点位高亮功能，支持在MapLibre地图上高亮显示查询结果的点位，提供更好的用户体验。

## 功能特性

### 🎯 核心功能

1. **多点位高亮**：支持同时高亮显示多个查询结果点位
2. **视觉效果**：使用红色闪烁圆点，明显区别于普通点位
3. **交互增强**：点击表格行可飞行到对应点位并临时高亮
4. **生命周期管理**：完善的创建和清理机制

### 🔄 生命周期管理

#### **组件销毁时**
- ✅ 自动清空所有高亮点位
- ✅ 移除相关图层和数据源
- ✅ 避免内存泄漏

#### **二次查询时**
- ✅ 先清空之前的高亮点位
- ✅ 再显示新的查询结果高亮
- ✅ 确保界面状态一致

#### **重置操作时**
- ✅ 清空所有高亮点位
- ✅ 重置查询条件
- ✅ 清理相关状态

#### **切换查询条件时**
- ✅ 清空当前高亮
- ✅ 重新查询并高亮
- ✅ 保持界面响应性

## 技术实现

### 🗺️ MapLibre实现

#### **高亮图层管理**
```typescript
// 图层标识符
let maplibreHighlightSourceId = 'classified-query-highlight-source';
let maplibreHighlightLayerId = 'classified-query-highlight-layer';

// 高亮要素数据
const highlightedFeatures = ref<any[]>([]);
```

#### **高亮样式配置**
```typescript
// 高亮图层样式
{
  id: maplibreHighlightLayerId,
  type: 'circle',
  source: maplibreHighlightSourceId,
  paint: {
    'circle-radius': [
      'interpolate', ['linear'], ['zoom'],
      8, 6,    // 缩放级别8时半径6px
      15, 12   // 缩放级别15时半径12px
    ],
    'circle-color': '#ff4444',  // 红色
    'circle-opacity': 0.7,      // 半透明
    'circle-stroke-width': 2,
    'circle-stroke-color': '#ffffff',
    'circle-stroke-opacity': 0.8
  }
}
```

#### **闪烁动画效果**
```typescript
const startMapLibreBlinkAnimation = () => {
  let opacity = 0.7;
  let increasing = true;
  
  const animate = () => {
    if (increasing) {
      opacity += 0.05;
      if (opacity >= 1.0) {
        opacity = 1.0;
        increasing = false;
      }
    } else {
      opacity -= 0.05;
      if (opacity <= 0.3) {
        opacity = 0.3;
        increasing = true;
      }
    }
    
    map.setPaintProperty(maplibreHighlightLayerId, 'circle-opacity', opacity);
    requestAnimationFrame(animate);
  };
  
  animate();
};
```

### 🚀 Cesium实现（已完成）

#### **使用PipeLocation工具**
```typescript
import { pipeLocationByBms, clearPipeLocationHighlight } from '@/utils/PipeLocation';

const highlightResultsOnCesium = async (results: any[]) => {
  try {
    console.log('🎯 开始Cesium高亮查询结果，点位数量:', results.length);

    // 提取管点编码用于高亮
    const pipeCodes = results
      .map(item => item.gxddh || item.bm || item.gid)
      .filter(code => code); // 过滤掉空值

    if (pipeCodes.length === 0) {
      console.warn('⚠️ 没有有效的管点编码可以高亮');
      return;
    }

    // 使用pipeLocationByBms方法进行高亮
    await pipeLocationByBms(pipeCodes, 'cesium', 'point', {
      autoFit: false, // 不自动缩放，保持当前视角
      highlightColor: '#ff4444', // 红色高亮
      pointRadius: 8
    });

    console.log('✅ Cesium高亮完成');

  } catch (error) {
    console.error('❌ Cesium高亮失败:', error);
  }
};
```

#### **清除高亮功能**
```typescript
const clearHighlights = () => {
  if (mapEngine.value === 'cesium') {
    // 使用PipeLocation工具清除高亮
    clearPipeLocationHighlight('cesium');
    cesiumHighlightLayer = null;
  }
  // ... MapLibre清除逻辑
};
```

#### **单点位飞行和高亮**
```typescript
const flyToPointOnCesium = async (row: any) => {
  try {
    // 先清除之前的高亮
    clearPipeLocationHighlight('cesium');

    // 使用原有的locPipePoint逻辑进行飞行
    locPipePoint(row);

    // 使用pipeLocationByBms进行高亮
    const pipeCode = row.gxddh || row.bm || row.gid;
    if (pipeCode) {
      await pipeLocationByBms([pipeCode], 'cesium', 'point', {
        autoFit: false, // 不自动缩放，使用locPipePoint的飞行逻辑
        highlightColor: '#ffff00', // 黄色临时高亮
        pointRadius: 10
      });

      // 3秒后清除临时高亮
      setTimeout(() => {
        clearPipeLocationHighlight('cesium');
      }, 3000);
    }
  } catch (error) {
    console.error('❌ Cesium飞行失败:', error);
  }
};
```

## 核心函数说明

### 📍 highlightQueryResults(results)
**功能**：高亮查询结果点位
**参数**：
- `results`: 查询结果数组
**流程**：
1. 清除之前的高亮
2. 过滤有效坐标数据
3. 根据地图引擎类型进行高亮
4. 保存高亮要素数据

### 🧹 clearHighlights()
**功能**：清除所有高亮点位
**流程**：
1. 移除高亮图层
2. 移除数据源
3. 清空要素数组
4. 停止动画

### 🎯 highlightSinglePoint(row, duration)
**功能**：临时高亮单个点位
**参数**：
- `row`: 行数据
- `duration`: 高亮持续时间（毫秒）
**特点**：
- 使用黄色大圆点
- 自动闪烁效果
- 定时自动清除

### ✈️ flyToPointOnMapLibre(row)
**功能**：飞行到指定点位
**参数**：
- `row`: 行数据
**流程**：
1. 解析坐标数据
2. 执行地图飞行
3. 临时高亮点位

## 使用场景

### 🔍 查询结果展示
```typescript
// 查询完成后自动高亮
const result = await classifiedQuery(queryForm.value);
if (result && result.code === 200) {
  tableData.value = result.data.list || [];
  
  // 高亮查询结果点位
  if (tableData.value.length > 0) {
    highlightQueryResults(tableData.value);
  }
}
```

### 📋 表格行交互
```typescript
// 点击表格行定位并高亮
const handleRowClick = async (row: any) => {
  if (mapEngine.value === 'maplibre') {
    await flyToPointOnMapLibre(row);
  } else {
    // Cesium处理逻辑
  }
};
```

### 🔄 状态重置
```typescript
// 重置时清除高亮
const handleReset = () => {
  clearBuffer();
  clearHighlights(); // 清除高亮点位
  // ... 其他重置逻辑
};
```

## 错误处理

### 📊 数据验证
- 检查geojson字段存在性
- 验证坐标数据格式
- 过滤无效数据记录

### 🗺️ 地图实例检查
- 验证地图实例存在性
- 处理图层操作异常
- 提供友好错误提示

### 🔧 资源清理
- 自动清理临时图层
- 防止内存泄漏
- 优雅处理组件卸载

## 性能优化

### 🎨 动画优化
- 使用requestAnimationFrame
- 检查图层存在性
- 自动停止无效动画

### 📦 资源管理
- 及时清理临时资源
- 避免重复创建图层
- 优化数据结构

### 🔄 状态同步
- 响应式数据管理
- 统一的状态清理
- 防止状态不一致

## 扩展计划

### 🚀 Cesium支持
- [x] 实现Cesium高亮功能（已完成）
- [x] 统一API接口（已完成）
- [ ] 性能优化

### 🎨 视觉增强
- [ ] 更多高亮样式选项
- [ ] 自定义动画效果
- [ ] 主题适配

### 🔧 功能扩展
- [ ] 高亮点位聚合
- [ ] 批量操作支持
- [ ] 导出高亮结果

## 注意事项

1. **数据格式**：确保查询结果包含有效的geojson字段
2. **性能考虑**：大量点位时考虑分页或聚合显示
3. **浏览器兼容**：动画效果在低端设备上可能影响性能
4. **内存管理**：及时清理不需要的图层和数据源
