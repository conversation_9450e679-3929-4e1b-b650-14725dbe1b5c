<!--
 * @Description: 重复属性检查面板
 * @Date: 2024-07-04
 * @Author: AI Assistant
 -->
<template>
  <page-card
    class="repeat-attr-check-panel"
    title="重复属性检查"
    @closeCard="closeCard"
  >
    <!-- 属性选择区域 -->
    <div class="section-label mb-2.5">选择检查属性</div>
    <div class="attr-selection-section">
      <el-checkbox-group v-model="selectedFields" class="attr-checkboxes">
        <el-checkbox
          v-for="field in availableFields"
          :key="field.key"
          :value="field.key"
          :label="field.label"
          class="attr-checkbox"
        >
          {{ field.label }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="mb-5">
      <el-button
        type="primary"
        class="check-btn h-9"
        :icon="Search"
        @click="handleCheck"
        :loading="loading"
        :disabled="selectedFields.length === 0"
      >
        开始检查
      </el-button>
    </div>
    <!-- 统计信息 -->
    <div class="stats-section" v-if="!loading && hasSearched">
      <el-alert
        :title="getStatsMessage()"
        :type="total > 0 ? 'warning' : 'success'"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 操作按钮区域 -->
    <!-- <el-row class="button-section" flex="~ row justify-start" v-if="hasSearched">
      <el-button
        type="primary"
        class="select-btn h-9"
        :icon="Refresh"
        @click="handleRefresh"
        :loading="loading"
        :disabled="selectedFields.length === 0"
      >
        刷新检查
      </el-button>
    </el-row> -->

    <!-- 重复属性列表表格 -->
    <el-row v-if="repeatAttrs.length > 0" class="table-section">
      <el-col :span="24">
        <el-text class="table-label">重复属性详细信息</el-text>
        <el-table
          :data="repeatAttrs"
          v-loading="loading"
          class="routeCt"
          :row-class-name="tableRowClassName"
          stripe
          size="small"
          height="300"
          empty-text="暂无重复属性数据"
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            :index="(index) => (currentPage - 1) * pageSize + index + 1"
          />

          <el-table-column
            prop="gid"
            label="GID"
            width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="gxddh"
            label="管点编码"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="gl"
            label="管类"
            width="100"
            show-overflow-tooltip
          />

          <!-- 动态显示选中的属性列 -->
          <el-table-column
            v-for="fieldKey in selectedFields"
            :key="fieldKey"
            :prop="fieldKey"
            :label="getFieldLabel(fieldKey)"
            :width="getColumnWidth(fieldKey)"
            show-overflow-tooltip
          />

          <el-table-column
            prop="szdl"
            label="所在道路"
            min-width="100"
            show-overflow-tooltip
            v-if="!selectedFields.includes('szdl')"
          />
          <el-table-column
            label="操作"
            width="120"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleLocate(row)"
                title="定位到地图"
              >
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center mt-5">
          <div>
            <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
          </div>
          <!-- 分页组件 -->
          <el-pagination
            v-if="total > 0"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :total="total"
            :pager-count="5"
            layout="prev, pager, next, jumper"
            class="pagination"
            background
            small
          />
        </div>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import { Refresh, Position, Search } from "@element-plus/icons-vue";
import PageCard from "@/components/PageCard.vue";
import { queryRepeatAttr, REPEAT_ATTR_FIELDS } from "@/api/pipeCheck";
import type {
  RepeatAttrPageQuery,
  RepeatAttrVo,
  RepeatAttrDetail,
  RepeatAttrField,
} from "@/api/pipeCheck";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import type { Map as MapLibreMap } from "maplibre-gl";

const REPEAT_ATTR_LAYER_IDS = {
  SOURCE: "repeat-attr-highlight-source",
  LAYER: "repeat-attr-highlight-layer",
} as const;

interface Emits {
  (e: "close"): void;
}
const emit = defineEmits<Emits>();

const loading = ref(false);
const hasSearched = ref(false);
const repeatAttrs = ref<RepeatAttrVo[]>([]);
const selectedFields = ref<string[]>([]);
const availableFields = ref<RepeatAttrField[]>(REPEAT_ATTR_FIELDS);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const pageSizes = ref([5, 10, 20, 50]);
let map: MapLibreMap | null = null;

onMounted(async () => {
  try {
    map = AppMaplibre.getMap();
  } catch (error) {
    console.warn("获取地图实例失败:", error);
  }
});

onUnmounted(() => {
  clearRepeatAttrHighlight();
  resetData();
});

const clearRepeatAttrHighlight = (): void => {
  if (!map) return;
  if (map.getLayer(REPEAT_ATTR_LAYER_IDS.LAYER)) {
    map.removeLayer(REPEAT_ATTR_LAYER_IDS.LAYER);
  }
  if (map.getSource(REPEAT_ATTR_LAYER_IDS.SOURCE)) {
    map.removeSource(REPEAT_ATTR_LAYER_IDS.SOURCE);
  }
};

const addRepeatAttrHighlight = (geometry: any): void => {
  if (!map) return;
  clearRepeatAttrHighlight();
  map.addSource(REPEAT_ATTR_LAYER_IDS.SOURCE, {
    type: "geojson",
    data: {
      type: "FeatureCollection",
      features: [{ type: "Feature", geometry, properties: {} }],
    },
  });
  map.addLayer({
    id: REPEAT_ATTR_LAYER_IDS.LAYER,
    type: "circle",
    source: REPEAT_ATTR_LAYER_IDS.SOURCE,
    paint: {
      "circle-radius": 8,
      "circle-color": "#ff4d4f",
      "circle-stroke-width": 2,
      "circle-stroke-color": "#ffffff",
    },
  });
};

/**
 * 获取统计信息消息
 * @returns 统计信息字符串
 */
const getStatsMessage = (): string => {
  if (!hasSearched.value) return "";

  const selectedFieldLabels = selectedFields.value
    .map((key) => getFieldLabel(key))
    .join("、");
  if (total.value === 0) {
    return `检查属性 [${selectedFieldLabels}] 未发现重复数据`;
  } else {
    return `检查属性 [${selectedFieldLabels}] 发现 ${total.value} 条重复数据`;
  }
};

/**
 * 获取字段标签
 * @param fieldKey 字段键
 * @returns 字段标签
 */
const getFieldLabel = (fieldKey: string): string => {
  const field = availableFields.value.find((f) => f.key === fieldKey);
  return field?.label || fieldKey;
};

/**
 * 获取列宽度
 * @param fieldKey 字段键
 * @returns 列宽度
 */
const getColumnWidth = (fieldKey: string): number => {
  const widthMap: { [key: string]: number } = {
    fsw: 100,
    sx: 80,
    dmgc: 100,
    js: 80,
    jggg: 100,
    jgcz: 100,
    szdl: 120,
  };
  return widthMap[fieldKey] || 100;
};

const handleCurrentChange = (page: number): void => {
  currentPage.value = page;
  handleRefresh();
};

const handleSizeChange = (size: number): void => {
  pageSize.value = size;
  currentPage.value = 1;
  handleRefresh();
};

const closeCard = (): void => {
  clearRepeatAttrHighlight();
  resetData();
  emit("close");
};

/**
 * 转换API数据为表格显示数据
 * @param apiData API返回的原始数据
 * @returns 转换后的表格数据
 */
const convertApiDataToTableData = (
  apiData: RepeatAttrDetail[]
): RepeatAttrVo[] => {
  return apiData.map((item) => ({
    gid: item.gid,
    gl: item.gl || "",
    gxddh: item.gxddh || "",
    sx: item.sx || "",
    fsw: item.fsw || "",
    dmgc: item.dmgc || 0,
    js: item.js || 0,
    jggg: item.jggg || "",
    jgcz: item.jgcz || "",
    szdl: item.szdl || "",
    x: item.x || 0,
    y: item.y || 0,
    longitude: item.longitude,
    latitude: item.latitude,
    geojson: item.geojson || "",
    geom: item.geom || "",
    selectedAttrValues: {},
  }));
};

/**
 * 解析点位坐标信息
 * @param point 重复属性数据
 * @returns 坐标数组或null
 */
const parseAttrCoordinates = (point: RepeatAttrVo): [number, number] | null => {
  // 优先尝试解析geojson
  if (point.geojson) {
    try {
      const geometry = JSON.parse(point.geojson);
      if (geometry.type === "Point" && geometry.coordinates) {
        return geometry.coordinates;
      }
    } catch (geoError) {
      console.warn("解析geojson失败:", geoError);
    }
  }

  // 如果geojson解析失败，使用x,y坐标
  if (point.x && point.y) {
    return [point.x, point.y];
  }

  return null;
};

const handleCheck = async (): Promise<void> => {
  if (selectedFields.value.length === 0) {
    ElMessage.warning("请至少选择一个属性进行检查");
    return;
  }

  currentPage.value = 1;
  await handleRefresh();
};

const handleRefresh = async (): Promise<void> => {
  if (selectedFields.value.length === 0) {
    ElMessage.warning("请至少选择一个属性进行检查");
    return;
  }

  loading.value = true;
  clearRepeatAttrHighlight();

  try {
    const fieldName = selectedFields.value.join(",");
    const params: RepeatAttrPageQuery = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      fieldName: fieldName,
    };

    const response = await queryRepeatAttr(params);

    if (response.code === 200 && response.data) {
      // 转换数据
      const rawData: RepeatAttrDetail[] = response.data.list || [];
      const convertedData = convertApiDataToTableData(rawData);
      repeatAttrs.value = convertedData;

      // 更新分页信息
      total.value = response.data.totalCount || 0;
      hasSearched.value = true;

      // 用户反馈
      if (total.value === 0) {
        ElMessage.success("数据健康！未发现重复属性");
      } else {
        // const selectedFieldLabels = selectedFields.value
        //   .map((key) => getFieldLabel(key))
        //   .join("、");
        // ElMessage.info(
        //   `发现 ${total.value} 条具有重复属性 [${selectedFieldLabels}] 的数据`
        // );
      }
    } else {
      // 错误处理
      const errorMsg = response.msg || "未知错误";
      ElMessage.error(`查询重复属性失败: ${errorMsg}`);
      resetData();
    }
  } catch (error) {
    console.error("查询重复属性异常:", error);
    const errorMsg = error instanceof Error ? error.message : "网络或系统异常";
    ElMessage.error(`查询重复属性异常: ${errorMsg}`);
    resetData();
  } finally {
    loading.value = false;
  }
};

/**
 * 重置数据状态
 */
const resetData = (): void => {
  repeatAttrs.value = [];
  total.value = 0;
};

const handleLocate = (point: RepeatAttrVo): void => {
  if (!map) {
    ElMessage.error("地图实例不可用");
    return;
  }
  try {
    const coordinates = parseAttrCoordinates(point);

    if (!coordinates) {
      ElMessage.error("无法获取有效的位置坐标");
      return;
    }

    // 创建用于高亮的几何对象
    const geometry = {
      type: "Point",
      coordinates: coordinates,
    };

    addRepeatAttrHighlight(geometry);
    map.flyTo({
      center: coordinates,
      zoom: 18,
      duration: 1500,
    });

    // const pointName = point.gxddh || point.gid || "未知";
    // const selectedFieldLabels = selectedFields.value
    //   .map((key) => getFieldLabel(key))
    //   .join("、");
    // ElMessage.success(
    //   `已定位到管点: ${pointName} (GID: ${point.gid})，重复属性: [${selectedFieldLabels}]`
    // );
  } catch (error) {
    console.error("定位失败:", error);
    ElMessage.error("定位失败，请检查数据格式");
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
</script>

<style scoped lang="scss">
.repeat-attr-check-panel {
  position: absolute;
  left: 10px;
  top: 150px;
  width: 724px;
  max-height: 85vh;
  z-index: 999;
}
.section-label {
  color: #5c5f66;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  // display: block;
}
.attr-selection-section {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;

  .attr-checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 12px;

    .attr-checkbox {
      margin-right: 0;
    }
  }

  .check-btn {
    width: 120px;
  }
}

.stats-section,
.button-section,
.table-section {
  margin-bottom: 16px;
}

.table-label {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}
</style>
