/**
 * @fileoverview 标绘要素数据适配器
 * @description 处理前端PlotFeature与后端API数据格式的双向转换
 * <AUTHOR>
 * @version 2.0.0
 */

import type { PlotFeature } from '@/lib/maplibre/layer/types/LayerTypes'
import { 
  type PlottingInfoVo,
  type PlottingCreateRequest,
  type PlottingUpdateRequest,
  type PlottingPageVo,
  EngineType
} from '@/types/plotting'

/**
 * @interface PlotFeatureWithTime
 * @description 包含时间信息的标绘要素（用于显示）
 */
export interface PlotFeatureWithTime extends PlotFeature {
  /** 创建时间字符串 */
  createTime?: string;
  /** 更新时间字符串 */
  updateTime?: string;
}

/**
 * @class PlotFeatureAdapter
 * @description 标绘要素数据适配器，处理前后端数据格式转换
 */
export class PlotFeatureAdapter {
  
  /**
   * @description 将前端PlotFeature转换为后端创建请求格式
   * @param feature 前端要素数据
   * @returns 后端创建请求数据
   */
  static toCreateRequest(feature: PlotFeature): PlottingCreateRequest {
    return {
      name: feature.name,
      type: feature.geojson.properties.geometryType,
      graphicsJson: JSON.stringify(feature.geojson),
      remark: feature.remark,
      engineType: feature.engineType
    }
  }

  /**
   * @description 将前端PlotFeature转换为后端更新请求格式
   * @param feature 前端要素数据
   * @returns 后端更新请求数据
   */
  static toUpdateRequest(feature: PlotFeature): PlottingUpdateRequest {
    return {
      id: feature.id, // 直接使用number类型ID
      name: feature.name,
      engineType: feature.engineType,
      type: feature.geojson.properties.geometryType,
      graphicsJson: JSON.stringify(feature.geojson),
      remark: feature.remark
    }
  }

  /**
   * @description 将后端PlottingInfoVo转换为前端PlotFeature格式
   * @param vo 后端详情数据
   * @returns 前端要素数据
   */
  static fromInfoVo(vo: PlottingInfoVo): PlotFeature {
    let geojson: any
    try {
      geojson = JSON.parse(vo.graphicsJson)
    } catch (error) {
      console.error('解析graphicsJson失败:', error)
      // 创建默认的GeoJSON
      geojson = {
        type: 'Feature',
        geometry: { type: 'Point', coordinates: [0, 0] },
        properties: {
          type: 'plot',
          geometryType: vo.type,
          style: {}
        }
      }
    }

    return {
      id: vo.id, // 直接使用后端number类型ID
      name: vo.name,
      remark: vo.remark || '',
      geojson: geojson,
      engineType: EngineType.MAPLIBRE
    }
  }

  /**
   * @description 将后端PlottingInfoVo转换为包含时间信息的要素格式（用于显示）
   * @param vo 后端详情数据
   * @returns 包含时间信息的前端要素数据
   */
  static fromInfoVoWithTime(vo: PlottingInfoVo): PlotFeatureWithTime {
    const feature = this.fromInfoVo(vo);
    return {
      ...feature,
      createTime: vo.createTime,
      updateTime: vo.updateTime
    };
  }

  /**
   * @description 将后端PlottingPageVo转换为前端PlotFeature格式
   * @param pageVo 后端分页数据
   * @returns 前端要素数据
   */
  static fromPageVo(pageVo: PlottingPageVo): PlotFeature {
    let geojson: any
    try {
      geojson = JSON.parse(pageVo.graphicsJson)
    } catch (error) {
      console.error('解析graphicsJson失败:', error)
      // 创建默认的GeoJSON
      geojson = {
        type: 'Feature',
        geometry: { type: 'Point', coordinates: [0, 0] },
        properties: {
          type: 'plot',
          geometryType: pageVo.type,
          style: {}
        }
      }
    }
    return {
      id: pageVo.id, // 直接使用后端number类型ID
      name: pageVo.name,
      remark: pageVo.remark, // PageVo中没有remark字段
      geojson: geojson,
      engineType: EngineType.MAPLIBRE,
      updateTime: pageVo.updateTime
    } 
  }

  /**
   * @description 将后端PlottingPageVo转换为包含时间信息的要素格式（用于显示）
   * @param pageVo 后端分页数据
   * @returns 包含时间信息的前端要素数据
   */
  static fromPageVoWithTime(pageVo: PlottingPageVo): PlotFeatureWithTime {
    const feature = this.fromPageVo(pageVo);
    return {
      ...feature,
      createTime: pageVo.createTime,
      updateTime: pageVo.createTime // PageVo中没有updateTime，使用createTime
    };
  }

  /**
   * @description 批量转换分页数据
   * @param pageVoList 后端分页数据列表
   * @returns 前端要素数据列表
   */
  static fromPageVoList(pageVoList: PlottingPageVo[]): PlotFeature[] {
    return pageVoList.map(vo => this.fromPageVo(vo))
  }

  /**
   * @description 批量转换分页数据（包含时间信息）
   * @param pageVoList 后端分页数据列表
   * @returns 包含时间信息的前端要素数据列表
   */
  static fromPageVoListWithTime(pageVoList: PlottingPageVo[]): PlotFeatureWithTime[] {
    return pageVoList.map(vo => this.fromPageVoWithTime(vo))
  }


} 