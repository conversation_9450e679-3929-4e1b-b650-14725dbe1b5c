export const initGisStatEchart = (data: any) => {
  // const data = [
  //   { value: 1048, name: '服务管理' },
  //   { value: 735, name: '风格样式' },
  //   { value: 580, name: '空间分析' },
  //   { value: 484, name: '网络分析' },
  //   { value: 300, name: '地理编码' },
  //   { value: 320, name: 'CAD图纸' }
  // ]
  let total = 0
  data.forEach((e: any) => {
    total += parseInt(e.value)
  })
  const option: any = {
    color: ['#FF7031', '#317DFF', '#FFC400', '#45B5F0', '#A4CAF6', '#13CB89'],
    title: [

      {
        text: '服务调用总次数',
        subtext: total,
        textStyle: {
          fontSize: 16,
          color: "#000000",
        },
        subtextStyle: {
          fontSize: 28,
          fontWeight: 600,
          color: "#000000",
        },
        textAlign: "center",
        x: "29%",
        y: "40%",
      },
    ],
    legend: {
      // top: '5%',
      type: 'scroll',
      orient: 'vertical',
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 20,
      right: 10,
      top: 40,
      bottom: 20,
      textStyle: {
        rich: {
          uname: {
            width: 120,
          },
        },
      },
      formatter: function (name: any) {
        for (let i = 0; i < data.length; i++) {
          if (name == data[i].name) {
            return `{uname|${name}}${data[i].value}次`
          }
        }
      },
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        center: ["30%", "50%"],
        radius: ['55%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }

  return option
}