import { Map as glMap } from 'maplibre-gl';
import { BaseLayer } from './BaseLayer';
import { getImages } from '@/utils/getImages';
import { fetchDeviceAlarmRecord, fetchDeviceSummary } from '@/utils/deviceUtils';
import { mockLngLatInPolygon } from '@/utils/mockUtil';
// 设备状态
export const deviceStatus = [
  {
    label: '停用',
    value: '0',
  },
  {
    label: '在线',
    value: '1',
  },
  {
    label: '离线',
    value: '2',
  },
  {
    label: '故障',
    value: '3',
  },
];
export default class AlarmLayer extends BaseLayer {
  protected declare _delegate: any;
  constructor(options: any) {
    super(options);
  }

  /**
   * @description 从 URL 加载 GeoJSON 数据
   * @param url - GeoJSON 数据文件路径
   * @returns Promise<GeoJSON.FeatureCollection>
   */
  private async loadGeoJsonData(
    url: string
  ): Promise<GeoJSON.FeatureCollection> {
    try {
      const response = await fetchDeviceAlarmRecord({
        size: -1,
        current: 1,
      });
      //基于deviceCode字段进行分组
      //todo：基于查询结果筛选当日告警记录出来
      const groupedData = response.reduce((acc: any, item: any) => {
        acc[item.deviceCode] = acc[item.deviceCode] || [];
        acc[item.deviceCode].push(item);
        return acc;
      }, {});
      const features: any[] = [];
      Object.keys(groupedData).forEach((key) => {
        const item = groupedData[key];
        const [lng, lat] = mockLngLatInPolygon();
        features.push({
          type: 'Feature',
          properties: {
            deviceCode: key,
            alarmLists: item,
          },
          geometry: {
            type: 'Point',
            coordinates: [lng, lat],
          },
        });
      });


      // const features = groupedData.map((item: any) => {
      //   //mock经纬度数据
      //   const [lng, lat] = mockLngLatInPolygon();
      //   return {
      //     type: 'Feature',
      //     properties: item,
      //     geometry: {
      //       type: 'Point',
      //       coordinates: [lng, lat],
      //     },
      //   };
      // });
      // debugger;
      return {
        type: 'FeatureCollection',
        features: features as any,
      };
      // if (!response.ok) {
      //   throw new Error(`HTTP error! status: ${response.status}`);
      // }
      // const data = await response.json();
      // return response as any;
    } catch (error) {
      console.error(`加载 GeoJSON 数据失败: ${url}`, error);
      throw error;
    }
  }
  async addToInternal(map: glMap) {
    this._map = map;
    let delegate: any;
    const geojson = await this.loadGeoJsonData(this.options.url as string);

    // 为GeoJSON数据添加默认status字段（如果缺失）
    geojson.features.forEach((feature, index) => {
      // 确保properties对象存在
      if (!feature.properties) {
        feature.properties = {};
      }

      if (!feature.properties.status) {
        // 默认状态为在线(1)，可以根据需要随机分配状态
        feature.properties.status = '1'; // 在线状态
      }

      // 确保每个feature都有唯一的id
      if (!feature.properties.id) {
        feature.properties.id = `${this.options.icon}_${index}`;
      }
    });

    console.log(
      `📊 加载设备数据: ${geojson.features.length} 个 ${this.options.icon} 设备`
    );

    const status = deviceStatus.map((item) => item.value);

    // 并行加载所有状态对应的图标
    await Promise.all(
      status.map((statusValue) =>
        this.loadImage(
          `${this.options.icon}_${statusValue}`,
          getImages(`device/${this.options.icon}2x.png`)
        )
      )
    );

    if (geojson.features.length > 0) {
      delegate = this.loadDevice(status, geojson);
    }

    return delegate;
  }
  /**
   * @description 异步加载图片资源并添加到地图
   * @param id - 图片ID
   * @param imageUrl - 图片URL路径
   * @returns Promise<void>
   */
  async loadImage(id: string, imageUrl: string): Promise<void> {
    // 如果图片已存在，直接返回
    if (this._map.hasImage(id)) {
      return;
    }

    try {
      // 使用MapLibre GL的Promise API加载图片
      const response = await this._map.loadImage(imageUrl);

      // 将图片添加到地图样式中
      this._map.addImage(id, response.data);

      // console.log(`✅ 成功加载设备图标: ${id} from ${imageUrl}`);
    } catch (error) {
      console.error(`❌ 加载设备图标失败: ${id} from ${imageUrl}`, error);
      throw error;
    }
  }
  loadDevice(status: string[], geojson: any) {
    // const sourceId = `${this.options.id}_source`
    const sourceId = `device_${this.options.icon}_source`;
    this._map.addSource(sourceId, {
      type: 'geojson',
      data: geojson,
      promoteId: 'id',
    });
    const matchStatus = status.flatMap((item) => [
      [item],
      `${this.options.icon}_${item}`,
    ]);
    const image = [
      'match',
      ['get', 'status'],
      ...matchStatus,
      `${this.options.icon}_${status[0]}`,
    ] as any;
    this._map.addLayer({
      id: this._id,
      type: 'symbol',
      source: sourceId,
      layout: {
        'icon-image': image,
        // 'text-field': '{name}',
        'icon-size': 0.8,
        'text-offset': [0, 0.5],
        'icon-offset': [0, -28],
        'text-anchor': 'top',
        'text-size': 16,
      },
      paint: {
        'text-color': '#2a71af',
        'icon-opacity': [
          'case',
          ['boolean', ['feature-state', 'flash'], false],
          0,
          1,
        ],
      },
    });
    return this._map.getLayer(this._id);
  }
  protected showInternal(show: boolean): void {
    // 主图层由父类BaseLayer控制，这里只需要调用父类方法处理delegate
    super.showInternal(show);
  }
  removeInternal() {
    this._map.removeLayer(this._id);
    const sourceId = `device_${this.options.icon}_source`;
    this._map.removeSource(sourceId);
  }
  metersToPixelsAtMaxZoom(meters: number, latitude: number) {
    return meters / 0.075 / Math.cos((latitude * Math.PI) / 180);
  }
}


