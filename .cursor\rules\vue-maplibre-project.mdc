---
description: 
globs: 
alwaysApply: false
---
# Vue + MapLibre 地图项目结构指南

## 项目概述
这是一个基于Vue 3 + TypeScript的地图应用，支持双引擎模式：
- **MapLibre引擎**：用于二维地图展示和交互
- **Cesium引擎**：用于三维地球展示和交互

## 核心目录结构

### 地图引擎库 (`src/lib/`)
- [cesium/](mdc:src/lib/cesium) - Cesium三维引擎封装
  - [AppCesium.ts](mdc:src/lib/cesium/AppCesium.ts) - Cesium应用核心管理类
  - [utils/Coord.ts](mdc:src/lib/cesium/utils/Coord.ts) - 坐标转换工具
- [maplibre/](mdc:src/lib/maplibre) - MapLibre二维引擎封装
  - [AppMaplibre.ts](mdc:src/lib/maplibre/AppMaplibre.ts) - MapLibre应用核心管理类
  - [draw/](mdc:src/lib/maplibre/draw) - 绘制工具模块
  - [measure/](mdc:src/lib/maplibre/measure) - 测量工具模块

### 组件系统 (`src/components/`)
- [SpatialQueryButtons.vue](mdc:src/components/SpatialQueryButtons.vue) - 空间查询按钮组件
- [baseSelect/](mdc:src/components/baseSelect) - 基础选择组件
- [dialog/](mdc:src/components/dialog) - 对话框组件
- [input/](mdc:src/components/input) - 输入组件

### 视图页面 (`src/views/`)
- [maplibre/](mdc:src/views/maplibre) - MapLibre相关页面
- [cesium/](mdc:src/views/cesium) - Cesium相关页面
- [query/](mdc:src/views/query) - 查询相关页面
  - [SpatialQuery.vue](mdc:src/views/query/SpatialQuery.vue) - 空间查询页面

## 技术栈特点

### Vue 3 Composition API
项目使用Vue 3的Composition API模式，配合TypeScript提供类型安全。

### 双引擎架构
通过抽象化接口，同一套业务逻辑可以在MapLibre和Cesium引擎间切换：
- 统一的绘制接口
- 统一的查询接口
- 统一的事件系统

### 响应式设计
使用Element Plus组件库，支持响应式布局和现代UI设计。

## 开发规范

### TypeScript集成
- 使用JSDoc注释提供详细的API文档
- 严格的类型定义和接口约束
- 完善的错误处理机制

### 代码组织
- 模块化设计，职责分离
- 统一的错误处理和日志输出
- 完整的生命周期管理

### 性能考虑
- 单例模式管理地图实例
- 事件监听器的正确添加和清理
- 内存泄漏预防机制

