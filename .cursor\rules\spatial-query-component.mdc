---
description: 
globs: 
alwaysApply: false
---
# SpatialQueryButtons 组件使用指南

## 组件概述
[SpatialQueryButtons.vue](mdc:src/components/SpatialQueryButtons.vue) 是一个高度可复用的空间查询按钮组件，提供四种查询方式：
- **全区范围**：预定义的区域边界查询
- **当前范围**：地图当前视野范围查询  
- **多边形查询**：用户自由绘制多边形区域
- **矩形查询**：用户绘制矩形区域

## 基本使用

### 最简单的使用方式
```vue
<template>
  <SpatialQueryButtons 
    :map-engine="'maplibre'"
    @query-complete="handleQueryComplete"
  />
</template>

<script setup>
import SpatialQueryButtons from '@/components/SpatialQueryButtons.vue'

const handleQueryComplete = (result) => {
  console.log('查询结果:', result)
}
</script>
```

### 自定义配置
```vue
<SpatialQueryButtons 
  :map-engine="'cesium'"
  :config="{
    buttonSize: 'large',
    buttonLayout: 'vertical',
    enabledQueries: ['polygon', 'rectangle'],
    showResult: true,
    regionBounds: {
      west: 103.42,
      south: 29.19, 
      east: 103.74,
      north: 29.53,
      name: '自定义区域'
    }
  }"
  @query-start="handleQueryStart"
  @query-complete="handleQueryComplete"
  @query-error="handleQueryError"
/>
```

## 重要事件

### query-complete
查询完成时触发，包含完整的GeoJSON几何体数据：
```typescript
interface QueryResult {
  type: 'all' | 'current' | 'polygon' | 'rectangle'
  geometry: GeoJSONGeometry
  bounds?: { west: number; south: number; east: number; north: number }
  timestamp: number
  success: boolean
}
```

### query-error
查询失败或用户取消时触发：
```typescript
interface QueryError {
  type: QueryType
  message: string
}
```

## 状态管理特性

### 自动清理机制
组件在销毁时会自动：
1. 停止正在进行的绘制操作
2. 清理绘制要素和图层
3. 恢复地图交互状态
4. 重置组件内部状态

### 错误恢复
组件具备完善的错误恢复机制：
- Terra Draw状态异常自动恢复
- 绘制工具重新初始化
- 多次开关组件的状态一致性保证

## 实际应用示例
参考 [SpatialQuery.vue](mdc:src/views/query/SpatialQuery.vue) 了解在实际页面中的集成方式。

