/**
 * @fileoverview Cesium地图坐标拾取适配器
 * @description 基于Cesium三维引擎实现坐标拾取功能
 * <AUTHOR>
 * @version 2.0.0
 */

import { CoordinatePickerBase, type CoordinatePoint, type CoordinateResult, type PickerOptions } from './CoordinatePickerBase';
import { CoordinateTransform } from '@/utils/coordinate/CoordinateTransform';

/**
 * @class CesiumCoordinatePicker
 * @description Cesium地图坐标拾取适配器，实现三维地球的坐标拾取功能
 * @extends CoordinatePickerBase
 */
export class CesiumCoordinatePicker extends CoordinatePickerBase {
  /** Cesium查看器实例 */
  private _viewer: any; // 使用any类型以避免直接依赖Cesium类型
  
  /** 拾取事件处理器 */
  private _handler?: any;
  
  /** 拾取标记实体 */
  private _marker?: any;
  
  /**
   * 构造函数
   * @param viewer - Cesium查看器实例
   * @param options - 拾取配置选项
   * @throws {Error} 如果查看器实例无效
   */
  constructor(viewer: any, options: PickerOptions = {}) {
    super(options);
    
    if (!viewer) {
      throw new Error('Cesium查看器实例不能为空');
    }
    
    this._viewer = viewer;
  }
  
  /**
   * 启动拾取处理
   * 创建事件处理器，设置点击事件监听
   * @protected
   * @override
   */
  protected onStart(): void {
    try {
      const { Cesium } = BC.Namespace;
      
      // 创建事件处理器
      if (!this._handler) {
        this._handler = new Cesium.ScreenSpaceEventHandler(this._viewer.scene.canvas);
      }
      
      // 注册左键点击事件
      this._handler.setInputAction((movement: any) => {
        try {
          if (!movement || !movement.position) {
            throw new Error('无效的点击事件数据');
          }
          
          // 拾取射线与地球表面的交点
          const cartesian = this._viewer.scene.pickPosition(
            movement.position,
            // this._viewer.scene.globe.ellipsoid
          );
          
          if (cartesian) {
            // 转换为经纬度坐标
            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            const coord: CoordinatePoint = {
              lng: Cesium.Math.toDegrees(cartographic.longitude),
              lat: Cesium.Math.toDegrees(cartographic.latitude),
              alt: cartographic.height
            };
            
            // 显示标记
            if (this._options.showMarker) {
              this.showMarker(coord);
            }
            
            // 处理坐标
            const result = this.processCoordinate(coord, {
              x: movement.position.x,
              y: movement.position.y
            });
            // 触发拾取成功事件
            this.handlePickSuccess(result);
          } else {
            // 如果没有拾取到地球表面，尝试拾取地形或3D对象
            const ray = this._viewer.camera.getPickRay(movement.position);
            const cartesian = this._viewer.scene.globe.pick(ray, this._viewer.scene);
            
            if (cartesian) {
              const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
              const coord: CoordinatePoint = {
                lng: Cesium.Math.toDegrees(cartographic.longitude),
                lat: Cesium.Math.toDegrees(cartographic.latitude),
                alt: cartographic.height
              };
              
              // 显示标记
              if (this._options.showMarker) {
                this.showMarker(coord);
              }
              
              // 处理坐标
              const result = this.processCoordinate(coord, {
                x: movement.position.x,
                y: movement.position.y
              });
              
              // 触发拾取成功事件
              this.handlePickSuccess(result);
            } else {
              console.warn('未能拾取到地球表面或地形的有效坐标');
            }
          }
        } catch (error) {
          this.handlePickError(error as Error);
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      
      // 修改鼠标样式为十字准星，提示用户可以点击
      this._viewer.canvas.style.cursor = 'crosshair';
    } catch (error) {
      console.error('启动Cesium坐标拾取失败:', error);
      this.handlePickError(error as Error);
    }
  }
  
  /**
   * 停止拾取处理
   * 移除事件处理器，恢复鼠标样式
   * @protected
   * @override
   */
  protected onStop(): void {
    try {
      const { Cesium } = BC.Namespace;
      
      // 移除事件处理器
      if (this._handler) {
        this._handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      }
      
      // 恢复默认鼠标样式
      if (this._viewer.canvas) {
        this._viewer.canvas.style.cursor = '';
      }
      
      // 如果不需要保留标记，则清除
      if (!this._options.showMarker) {
        this.clearMarker();
      }
    } catch (error) {
      console.error('停止Cesium坐标拾取失败:', error);
      // 这里不抛出错误，因为即使清理失败也应该继续进行其他清理操作
    }
  }
  
  /**
   * 配置更新处理
   * 当选项更新时，更新标记样式
   * @protected
   * @override
   */
  protected onOptionsUpdate(): void {
    try {
      // 更新标记样式
      if (this._marker && this._options.showMarker) {
        const position = this._marker.position?.getValue(new Date());
        if (position) {
          const { Cesium } = BC.Namespace;
          const cartographic = Cesium.Cartographic.fromCartesian(position);
          
          // 清除并重新创建标记，应用新样式
          this.clearMarker();
          this.showMarker({
            lng: Cesium.Math.toDegrees(cartographic.longitude),
            lat: Cesium.Math.toDegrees(cartographic.latitude),
            alt: cartographic.height
          });
        } else {
          // 如果无法获取当前位置，使用地图中心点
          const center = this.getMapCenter();
          this.clearMarker();
          this.showMarker(center);
        }
      } else if (!this._options.showMarker) {
        // 如果关闭了标记显示，清除标记
        this.clearMarker();
      }
    } catch (error) {
      console.error('更新Cesium坐标拾取选项失败:', error);
    }
  }
  
  /**
   * 销毁拾取器
   * 清理所有资源，使拾取器不可用
   * @override
   */
  public destroy(): void {
    try {
      const { Cesium } = BC.Namespace;
      
      // 停止拾取
      this.stop();
      
      // 销毁事件处理器
      if (this._handler) {
        this._handler.destroy();
        this._handler = undefined;
      }
      
      // 清除标记
      this.clearMarker();
    } catch (error) {
      console.error('销毁Cesium坐标拾取器失败:', error);
    }
  }
  
  /**
   * 获取地图中心点坐标
   * @returns 地图中心点坐标
   * @override
   * @throws {Error} 如果获取中心点失败
   */
  public getMapCenter(): CoordinatePoint {
    try {
      const { Cesium } = BC.Namespace;
      
      if (!this._viewer || !this._viewer.scene) {
        throw new Error('Cesium查看器实例无效');
      }
      
      // 获取相机位置和场景
      const scene = this._viewer.scene;
      const camera = scene.camera;
      
      // 获取视图中心点的射线
      const windowPosition = new Cesium.Cartesian2(
        scene.canvas.clientWidth / 2,
        scene.canvas.clientHeight / 2
      );
      
      // 射线与地球表面的交点
      const ray = camera.getPickRay(windowPosition);
      const cartesian = scene.globe.pick(ray, scene);
      
      if (cartesian) {
        // 转换为经纬度坐标
        const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
        return {
          lng: Cesium.Math.toDegrees(cartographic.longitude),
          lat: Cesium.Math.toDegrees(cartographic.latitude),
          alt: cartographic.height
        };
      }
      
      // 如果没有交点，返回相机位置
      const cameraCartographic = Cesium.Cartographic.fromCartesian(camera.position);
      return {
        lng: Cesium.Math.toDegrees(cameraCartographic.longitude),
        lat: Cesium.Math.toDegrees(cameraCartographic.latitude),
        alt: cameraCartographic.height
      };
    } catch (error) {
      console.error('获取Cesium地图中心点失败:', error);
      throw error;
    }
  }
  
  /**
   * 飞行到指定坐标
   * 控制地图视图平滑飞行到指定坐标
   * @param coord - 目标坐标
   * @param duration - 飞行时长（毫秒），默认为2000毫秒
   * @returns Promise，飞行完成时解析
   * @override
   * @throws {Error} 如果飞行操作失败
   */
  public async flyTo(coord: CoordinatePoint, duration: number = 2000): Promise<void> {
    const { Cesium } = BC.Namespace;
    
    return new Promise((resolve, reject) => {
      try {
        if (!this._viewer) {
          throw new Error('Cesium查看器实例无效');
        }
        
        if (!coord || typeof coord.lng !== 'number' || typeof coord.lat !== 'number') {
          throw new Error('无效的坐标');
        }
        
        // 设置默认高度，如果未提供
        const altitude = coord.alt || 1000;
        
        // 创建目标位置
        const destination = Cesium.Cartesian3.fromDegrees(
          coord.lng,
          coord.lat,
          altitude
        );
        
        // 飞行到目标位置
        this._viewer.camera.flyTo({
          destination,
          duration: duration / 1000, // Cesium使用秒作为单位
          complete: () => {
            resolve();
          },
          cancel: () => {
            reject(new Error('飞行操作被取消'));
          }
        });
      } catch (error) {
        console.error('Cesium飞行到指定坐标失败:', error);
        reject(error);
      }
    });
  }
  
  /**
   * 显示拾取标记
   * 在指定坐标位置创建并显示标记
   * @param coord - 坐标点
   * @private
   */
  private showMarker(coord: CoordinatePoint): void {
    try {
      const { Cesium } = BC.Namespace;
      
      // 清除现有标记
      this.clearMarker();
      
      // 获取标记样式
      const size = this._options.markerStyle?.size || 8;
      const color = this._options.markerStyle?.color || '#ff0000';
      const opacity = this._options.markerStyle?.opacity || 0.8;
      
      // 创建标记实体
      this._marker = this._viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(coord.lng, coord.lat, coord.alt || 0),
        point: {
          pixelSize: size,
          color: Cesium.Color.fromCssColorString(color).withAlpha(opacity),
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2,
          disableDepthTestDistance: Number.POSITIVE_INFINITY // 确保标记始终可见
        }
      });
    } catch (error) {
      console.error('显示Cesium坐标拾取标记失败:', error);
      // 标记显示失败不应影响主要功能
    }
  }
  
  /**
   * 清除拾取标记
   * 移除地图上的标记
   * @private
   */
  private clearMarker(): void {
    try {
      if (this._marker && this._viewer.entities) {
        this._viewer.entities.remove(this._marker);
        this._marker = undefined;
      }
    } catch (error) {
      console.error('清除Cesium坐标拾取标记失败:', error);
      // 清除失败不抛出异常，继续执行
    }
  }
  
  /**
   * 处理坐标
   * 将原始坐标转换为多种坐标系的坐标
   * @param originalCoord - 原始坐标
   * @param screenCoord - 屏幕坐标
   * @returns 坐标处理结果，包含多种坐标系的值
   * @private
   */
  private processCoordinate(
    originalCoord: CoordinatePoint,
    screenCoord?: { x: number; y: number }
  ): CoordinateResult {
    try {
      // 格式化原始坐标精度
      const formattedCoord = this.formatCoordinatePrecision(originalCoord);
      
      // 创建基础结果，默认坐标系为WGS84
      const result: CoordinateResult = {
        originalCoord: formattedCoord,
        wgs84: formattedCoord, // Cesium默认使用WGS84
        gcj02: CoordinateTransform.wgs84ToGcj02(formattedCoord),
        bd09: CoordinateTransform.wgs84ToBd09(formattedCoord),
        timestamp: Date.now(),
        screenCoord
      };
      
      // 如果需要，添加投影坐标
      if (this._options.includeProjection) {
        // 计算EPSG:3857墨卡托投影坐标
        const x = formattedCoord.lng * Math.PI * 6378137.0 / 180.0;
        let y = Math.log(Math.tan((90.0 + formattedCoord.lat) * Math.PI / 360.0)) * 6378137.0;
        
        // 处理极端情况
        if (!Number.isFinite(y)) {
          y = 0;
        }
        
        result.projectedCoord = {
          epsg3857: {
            lng: Number(x.toFixed(2)),
            lat: Number(y.toFixed(2)),
            alt: formattedCoord.alt
          }
        };
      }
      
      return result;
    } catch (error) {
      console.error('处理Cesium坐标失败:', error);
      throw new Error(`坐标处理失败: ${(error as Error).message}`);
    }
  }
}
