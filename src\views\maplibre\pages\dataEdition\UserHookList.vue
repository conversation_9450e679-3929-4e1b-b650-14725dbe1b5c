<template>
  <page-card :close-icon="false" class="tabulate-sta" title="挂接列表">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form
        ref="formRef"
        :model="queryForm"
        :rules="formRules"
        label-width="80px"
        class="search-form"
        inline
      >
        <!-- 搜索表单 -->
        <!-- <el-form-item label="水表编号:" prop="waterNo">
          <el-input
            v-model="queryForm.waterNo"
            placeholder="请输入水表编号"
            clearable
            style="width: 180px;"
          />
        </el-form-item> -->
        <el-form-item label="用户名:" prop="userName">
          <el-input
            v-model="queryForm.userName"
            placeholder="请输入用户名"
            clearable
            style="width: 180px;"
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button type="primary" @click="handleBatchDelete">批量移除</el-button>
          <el-button type="primary" @click="userHook">用户挂接</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格区域 -->
    <div class="table-section">
      <div class="table-container">
        <el-table
          :data="hookData"
          v-loading="loading"
          class="routeCt"
          stripe
          height="350"
          empty-text="暂无挂接数据"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <!-- 多选框列 -->
          <el-table-column
            type="selection"
            width="55"
            align="center"
          />
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            :index="getTableIndex"
          />

          <el-table-column
            prop="gxddh"
            label="水表编号"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="userName"
            label="用户名"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="userNo"
            label="用户号"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="contactsPhone"
            label="联系电话"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="waterAddress"
            label="用水地址"
            width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            label="操作"
            width="150"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleLocate(row)"
                title="定位到地图"
              >
                定位
              </el-button>
              <el-button
                type="text"
                style="color: #1966ff"
                @click="deleteHook(row)"
                title="移除挂接"
              >
                移除挂接
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center p-2" >
          <div>
            <div class="font-size-3.5 color-#323233">
              共{{ totalCount }}条数据
            </div>
          </div>
          <!-- 分页组件 -->
          <el-pagination
            v-if="totalCount > 0"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :pager-count="5"
            :page-size="queryForm.pageSize"
            :total="totalCount"
            layout="prev, pager, next, jumper"
            class="pagination"
            background
            small
          />
        </div>
      </div>
    </div>
  </page-card>
  <UserHook v-if="isUserHookVisible" @close="closeUserHook" @updateHook="updateQuery" />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import PageCard from '@/components/PageCard.vue';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import UserHook from './UserHook.vue'
import { batchCancelAttach, getWaterUserPage } from '@/api/userHook';
/**
 * @interface Emits
 * @description 组件事件接口
 */
interface Emits {
  (e: 'close'): void;
}
/**
 * @interface QueryForm
 * @description 查询表单接口
 */
interface QueryForm {
  isHang: number;
  userName: string;
  pageNum: number;
  pageSize: number;
}
const totalCount = ref(0)
/**
 * @interface HookDataItem
 * @description 挂接数据项接口
 */
interface HookDataItem {
  gid: any;
  id: string;
  gxddh: string;
  userName: string;
  userNo: string;
  userType: string;
  contactsPhone: string;
  status: string;
  waterAddress: string;
  longitude?: number;
  latitude?: number;
  isHang?: number;
  geojson?: any;
}

/**
 * @function initQueryForm
 * @description 初始化查询表单
 */
const initQueryForm = (): QueryForm => {
  return {
    isHang: 1,
    userName: '',
    pageNum: 1,
    pageSize: 10,
  };
};

const updateQuery = () => {
  queryForm.value = initQueryForm();
  getList()
  //更新专题图
  updateWaterMeterTheme()
}

const closeUserHook = () => {
  isUserHookVisible.value = false;
}
const isUserHookVisible = ref(false);

// ============ 响应式数据 ============

/** 查询表单数据 */
const queryForm = ref<QueryForm>(initQueryForm());


/** 表单验证规则 */
const formRules = ref({
  waterNo: [],
  userName: []
});

/** 挂接数据列表 */
const hookData = ref<HookDataItem[]>([]);

/** 选中的数据 */
const selectedData = ref<HookDataItem[]>([]);

/** 当前页码 */
const currentPage = ref(1);

/** 页面大小选项 */
const pageSizes = ref([10, 20, 50, 100]);

// 使用服务端分页，直接显示hookData数据

/** 表格索引计算 */
const getTableIndex = (index: number): number => {
  return (currentPage.value - 1) * queryForm.value.pageSize + index + 1;
};

// ============ 方法定义 ============

/**
 * @function getList
 * @description 获取挂接列表数据
 */
const getList = async (): Promise<void> => {
  try {
    loading.value = true;
    console.log('获取挂接列表数据...');

    // TODO: 调用实际的API接口
    const {code, data} = await getWaterUserPage(queryForm.value);
    if (code === 200) {
      hookData.value = data.list;
      totalCount.value = data.totalCount;
      
    } else {
      ElMessage.error('获取挂接列表失败');
    }
    

    

    console.log('✅ 挂接列表数据获取成功');
  } catch (error) {
    console.error('获取挂接列表失败:', error);
    ElMessage.error('获取挂接列表失败');
  } finally {
    loading.value = false;
  }
};

/**
 * @function handleQuery
 * @description 处理查询操作
 */
const handleQuery = (): void => {
  // 将搜索表单数据同步到查询表单
  // queryForm.value.waterNo = formData.value.waterNo;
  // queryForm.value.userName = formData.value.userName;
  // queryForm.value.pageNum = 1; // 重置页码
  // currentPage.value = 1;

  getList();
};

/**
 * @function handleSelectionChange
 * @description 处理表格选择变化
 */
const handleSelectionChange = (selection: HookDataItem[]): void => {
  selectedData.value = selection;
  console.log('选中的数据:', selection);
};

/**
 * @function handleBatchDelete
 * @description 处理批量删除
 */
const handleBatchDelete = async (): Promise<void> => {
  if (selectedData.value.length === 0) {
    ElMessage.warning('请先选择要删除的数据');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedData.value.length} 条挂接关系吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    console.log('批量删除:', selectedData.value);
    const gids = selectedData.value.map(item => item.gid);
    await batchCancelAttach(gids);
    // TODO: 调用删除API
    ElMessage.success('删除成功');
    getList(); // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

/**
 * @function userHook
 * @description 用户挂接操作
 */
const userHook = (): void => {
  console.log('用户挂接操作');
  isUserHookVisible.value = true;
};

/**
 * @function handleLocate
 * @description 处理定位操作
 */
const handleLocate = (row: HookDataItem): void => {
  try {
    console.log('定位到:', row.geojson);
    if(!row.geojson || row.geojson === '') {
      console.log('无法定位');
      return;
    }
    const geojson = JSON.parse(row.geojson);
    if (geojson.coordinates) {
      const map = AppMaplibre.getMap();
      map.flyTo({
        center: geojson.coordinates,
        zoom: 18,
        duration: 2000
      });

      ElMessage.success(`已定位到 ${row.userName}`);
    } else {
      ElMessage.warning('该用户暂无位置信息');
    }
  } catch (error) {
    console.error('定位失败:', error);
    ElMessage.error('定位失败');
  }
};

/**
 * @function deleteHook
 * @description 删除单个挂接
 */
const deleteHook = async (row: HookDataItem): Promise<void> => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.userName}" 的挂接关系吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    console.log('删除挂接:', row);
    await batchCancelAttach([row.gid])
    ElMessage.success('删除成功');
    getList(); // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

/**
 * @function handleCurrentChange
 * @description 处理页码变化
 */
const handleCurrentChange = (page: number): void => {
  currentPage.value = page;
  queryForm.value.pageNum = page;
  getList(); // 重新获取数据
};

/**
 * @function handleSizeChange
 * @description 处理页面大小变化
 */
const handleSizeChange = (size: number): void => {
  queryForm.value.pageSize = size;
  currentPage.value = 1;
  queryForm.value.pageNum = 1;
  getList(); // 重新获取数据
};


const emit = defineEmits<Emits>();

// ============ 响应式状态 ============

/** 加载状态 */
const loading = ref(false);

/** 管点图层ID常量 */
const PIPE_NODE_LAYER_ID = 'mvt_pipeNode';

/** 水表专题图层ID */
const WATER_METER_LAYER_ID = 'water-meter-theme-layer';

/** 水表图标数据源ID */
const WATER_METER_SOURCE_ID = 'water-meter-source';

/** 原始管点图层样式备份 */
const originalNodePaint = ref<Record<string, any> | null>(null);

/** 原始管点图层最小缩放级别备份 */
const originalNodeMinZoom = ref<number | null>(null);

/** 水表图标是否已加载 */
const waterMeterIconLoaded = ref(false);

// ============ 事件处理方法 ============

/**
 * @function closeCard
 * @description 关闭专题面板
 */
const closeCard = (): void => {
  try {
    console.log('关闭水表专题面板');
    
    // 在关闭前先还原样式
    restoreNodeStyle();
    
    emit('close');
    ElMessage.info('已关闭水表专题');
  } catch (error) {
    console.error('关闭面板失败:', error);
    ElMessage.error('关闭面板失败');
  }
};

/**
 * @function loadWaterMeterIcon
 * @description 加载水表图标到地图
 */
const loadWaterMeterIcon = async (iconName: string): Promise<void> => {
  try {
    const map = AppMaplibre.getMap();
    // debugger;
    // 检查图标是否已存在
    // if (map.hasImage(iconName + '-icon')) {
    //   waterMeterIconLoaded.value = true;
    //   console.log('水表图标已存在，跳过加载');
    //   return;
    // }

    // 创建图片元素加载SVG
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    await new Promise<void>((resolve, reject) => {
      img.onload = () => {
        try {
          // 添加图标到地图
          map.addImage(iconName + '-icon', img, { sdf: false });
          waterMeterIconLoaded.value = true;
          console.log('水表图标加载成功');
          resolve();
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => {
        reject(new Error('水表图标加载失败'));
      };
      
      // 加载SVG图标 - 使用动态import获取正确的资源路径
      const waterMeterIconUrl = new URL(`/src/assets/icon-svg/${iconName}.svg`, import.meta.url).href;
      img.src = waterMeterIconUrl;
    });

  } catch (error) {
    console.error('加载水表图标失败:', error);
    throw error;
  }
};

/**
 * @function createWaterMeterLayer
 * @description 创建水表专题图层
 */
const createWaterMeterLayer = (): void => {
  try {
    const map = AppMaplibre.getMap();

    // 获取原始管点数据源
    const originalSource = map.getSource('mvt_pipeNode') as any;
    if (!originalSource) {
      throw new Error('未找到管点数据源');
    }

    // 添加水表数据源（使用相同的MVT源）
    if (!map.getSource(WATER_METER_SOURCE_ID)) {
      // 使用与原始管点图层相同的配置
      const sourceConfig = {
        type: 'vector' as const,
        minzoom: originalSource.minzoom || 0,
        maxzoom: originalSource.maxzoom || 22
      };

      // 根据原始数据源类型添加相应配置
      if (originalSource.tiles) {
        (sourceConfig as any).tiles = originalSource.tiles;
      } else if (originalSource.url) {
        (sourceConfig as any).url = originalSource.url;
      } else {
        // 使用管点图层的默认MVT URL
        (sourceConfig as any).tiles = ['https://gis.lshywater.cn/api/analyse/gs/pt/mvt/{z}/{x}/{y}.mvt'];
      }

      map.addSource(WATER_METER_SOURCE_ID, sourceConfig);
    }

    // 添加水表图层（symbol类型用于显示图标）
    if (!map.getLayer(WATER_METER_LAYER_ID)) {
      map.addLayer({
        id: WATER_METER_LAYER_ID,
        type: 'symbol',
        source: WATER_METER_SOURCE_ID,
        'source-layer': 'point', // 与原管点图层相同的source-layer
        minzoom: 8, // 较早显示水表
        filter: ['==', ['get', 'fsw'], '水表'], // 只显示fsw='水表'的要素
        layout: {
          // 根据water_user_id是否有值动态选择图标
          'icon-image': [
            'case',
            ['has', 'water_user_id'], [
              'case',
              ['!=', ['get', 'water_user_id'], ''], 'watermeter-icon',  // 已挂接用户（有值且不为空）
              'watermeter_noHook-icon'  // 未挂接（空值）
            ],
            'watermeter_noHook-icon'  // 未挂接（无该字段）
          ],
          'icon-size': 0.8,
          'icon-allow-overlap': true,
          'icon-ignore-placement': true
        },
        paint: {
          'icon-opacity': 1
        }
      });
    }

    console.log('水表专题图层创建成功');
  } catch (error) {
    console.error('创建水表专题图层失败:', error);
    throw error;
  }
};

/**
 * @function modifyNodeLayerStyle
 * @description 修改管点图层样式，降低非水表管点的显示
 */
const modifyNodeLayerStyle = (): void => {
  try {
    const map = AppMaplibre.getMap();
    
    // 备份原始样式
    const nodeLayer = map.getLayer(PIPE_NODE_LAYER_ID);
    if (nodeLayer && 'paint' in nodeLayer && nodeLayer.paint) {
      originalNodePaint.value = { ...nodeLayer.paint };
      console.log('已备份原始管点样式:', originalNodePaint.value);
    }

    // 备份并调整管点图层的缩放级别
    if (nodeLayer && 'minzoom' in nodeLayer) {
      originalNodeMinZoom.value = nodeLayer.minzoom || 12;
      map.setLayerZoomRange(PIPE_NODE_LAYER_ID, 12, (nodeLayer as any).maxzoom || 24);
      console.log(`管点图层可见层级保持: minzoom=${originalNodeMinZoom.value}`);
    }

    // 修改管点样式，使非水表管点更加低调
    map.setPaintProperty(PIPE_NODE_LAYER_ID, 'circle-opacity', 0);
    map.setPaintProperty(PIPE_NODE_LAYER_ID, 'circle-stroke-opacity', 0);
    
    console.log('管点图层样式已调整');
  } catch (error) {
    console.error('修改管点图层样式失败:', error);
    throw error;
  }
};

const updateWaterMeterTheme = () => {
  try {
    const map = AppMaplibre.getMap();

    // 重新创建水表图层
    if (map.getLayer(WATER_METER_LAYER_ID)) {
      map.removeLayer(WATER_METER_LAYER_ID);
    }
    if (map.getSource(WATER_METER_SOURCE_ID)) {
      map.removeSource(WATER_METER_SOURCE_ID);
    }

    setTimeout(() => {
      createWaterMeterLayer();
    }, 500)
    

    console.log('水表专题图已更新');
  } catch (error) {
    console.error('更新水表专题图失败:', error);
  }
}

/**
 * @function initWaterMeterTheme
 * @description 初始化水表专题图
 */
const initWaterMeterTheme = async (): Promise<void> => {
  try {
    loading.value = true;
    console.log('开始初始化水表专题图...');

    // 1. 加载水表图标
    await loadWaterMeterIcon('watermeter');
    await loadWaterMeterIcon('watermeter_noHook');

    // 2. 创建水表专题图层
    createWaterMeterLayer();

    // 3. 调整原管点图层样式
    modifyNodeLayerStyle();

    // ElMessage.success('水表专题图加载完成');
    console.log('水表专题图初始化完成');
  } catch (error) {
    console.error('初始化水表专题图失败:', error);
    ElMessage.error(`加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

/**
 * @function restoreNodeStyle
 * @description 还原管点图层样式
 */
const restoreNodeStyle = (): void => {
  try {
    const map = AppMaplibre.getMap();
    console.log('开始还原管点样式...');
    
    // 移除水表专题图层
    if (map.getLayer(WATER_METER_LAYER_ID)) {
      map.removeLayer(WATER_METER_LAYER_ID);
      console.log('水表专题图层已移除');
    }

    // 移除水表数据源
    if (map.getSource(WATER_METER_SOURCE_ID)) {
      map.removeSource(WATER_METER_SOURCE_ID);
      console.log('水表数据源已移除');
    }

    // 还原管点图层样式
    if (originalNodePaint.value) {
      try {
        const nodeLayer = map.getLayer(PIPE_NODE_LAYER_ID);
        if (nodeLayer) {
          // 逐个还原paint属性
          Object.entries(originalNodePaint.value).forEach(([key, value]) => {
            map.setPaintProperty(PIPE_NODE_LAYER_ID, key, value);
          });
          console.log('管点图层样式已还原');
        }
        originalNodePaint.value = null;
      } catch (paintError) {
        console.warn('还原管点样式失败:', paintError);
        // 如果还原失败，使用默认样式
        map.setPaintProperty(PIPE_NODE_LAYER_ID, 'circle-opacity', 1);
        map.setPaintProperty(PIPE_NODE_LAYER_ID, 'circle-stroke-opacity', 1);
        originalNodePaint.value = null;
      }
    }

    // 还原管点图层的缩放级别
    if (originalNodeMinZoom.value !== null) {
      try {
        const nodeLayer = map.getLayer(PIPE_NODE_LAYER_ID);
        if (nodeLayer) {
          map.setLayerZoomRange(
            PIPE_NODE_LAYER_ID,
            originalNodeMinZoom.value,
            (nodeLayer as any).maxzoom || 24
          );
          console.log(`管点图层可见层级已还原: minzoom还原为${originalNodeMinZoom.value}`);
        }
        originalNodeMinZoom.value = null;
      } catch (zoomError) {
        console.warn('还原管点图层缩放级别失败:', zoomError);
        originalNodeMinZoom.value = null;
      }
    }

    console.log('管点样式还原完成');
  } catch (error) {
    console.error('还原管点样式失败:', error);
  }
};

// ============ 生命周期钩子 ============

/**
 * @description 组件挂载时初始化
 */
onMounted(() => {
  try {
    // 延迟初始化，确保地图完全加载
    setTimeout(() => {
      initWaterMeterTheme();
      // 初始化挂接列表数据
      getList();
    }, 2000);
  } catch (error) {
    console.error('组件挂载时初始化失败:', error);
    ElMessage.error('初始化失败');
  }
});

/**
 * @description 组件卸载时清理资源
 */
onUnmounted(() => {
  try {
    restoreNodeStyle();
    console.log('水表专题资源已清理');
  } catch (error) {
    console.error('清理水表专题资源失败:', error);
  }
});
</script>

<style scoped lang="scss">
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  // width: 1000px;
  min-height: 350px;
  z-index: 1000;

  :deep(.page-card-content) {
    padding: 16px;
  }
}

// 搜索容器样式
.search-container {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

// 搜索表单样式
.search-form {
  .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;

    .el-form-item__label {
      font-size: 13px;
      color: #606266;
      font-weight: 500;
      line-height: 32px;
    }

    .el-input {
      :deep(.el-input__inner) {
        height: 32px;
        line-height: 32px;
        font-size: 13px;
      }
    }

    // 按钮组样式
    &:last-child {
      margin-right: 0;

      .el-button {
        height: 32px;
        padding: 0 16px;
        font-size: 13px;
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

// 兼容旧的按钮样式
.el-row {
  .el-button {
    margin-left: 8px;
    font-size: 13px;

    &:first-child {
      margin-left: 0;
    }
  }
}

// 表格区域样式
.table-section {
  .table-container {
    background-color: #ffffff;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    overflow: hidden;
  }

  .table-label {
    color: #2c3037;
    font-size: 14px;
    margin-bottom: 8px;
    display: block;
  }

  .el-table {
    font-size: 13px;
    border-radius: 4px;
    overflow: hidden;

    :deep(th) {
      background-color: #f2f6fc;
      color: #606266;
      font-weight: 500;
      font-size: 13px;
      border-bottom: 1px solid #ebeef5;
    }

    :deep(td) {
      padding: 2px 0;
      border-bottom: 1px solid #ebeef5;
    }

    :deep(.el-button--text) {
      padding: 2px 2px;
      font-size: 12px;
      min-width: 50px;
      color: #1966ff;

      &:hover {
        background-color: #f0f9ff;
      }
    }

    :deep(.el-table__row) {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

// 分页区域样式
.flex {
  display: flex;

  &.justify-between {
    justify-content: space-between;
  }

  &.items-center {
    align-items: center;
  }
}

.font-size-3\.5 {
  font-size: 14px;
}

.color-\#323233 {
  color: #323233;
}

.pagination {
  :deep(.el-pagination__total) {
    font-size: 13px;
    color: #606266;
  }

  :deep(.el-pager li) {
    font-size: 13px;
    min-width: 28px;
    height: 28px;
    line-height: 28px;
  }

  :deep(.el-pagination__jump) {
    font-size: 13px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .tabulate-sta {
    width: 800px;
  }
}

@media (max-width: 1000px) {
  .tabulate-sta {
    width: 700px;
  }
}
</style>