import { type LayerItem } from "../type/LayerItem";
import type { LayerGroup } from "./LayerGroup";

/*
 * @Description: 图层基类
 * @Autor: silei
 * @Date: 2023-08-02 15:17:55
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-09-14 16:27:21
 */
export abstract class BaseLayer {
  protected _viewer: any;
  protected _delegate: any;
  protected _show: boolean;
  protected _transparency: number;
  protected _type: string;
  protected _id: string;
  protected _title: string;
  protected _options: any;
  protected _parent: any;
  constructor(protected options: any) {
    this._id = options.id ?? this.guid();
    this._title = options.name ?? "未命名";
    this._show = options.show === undefined ? true : options.show;
    this._transparency = 100;
    this._type = options.type;
    this._options = options.options;
  }
  protected guid() {
    return `xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx`
      .replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0,
          v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      })
      .toUpperCase();
  }
  async addTo(viewer: BC.Viewer, parent?: LayerGroup) {
    this._viewer = viewer;
    this._parent = parent;
    if (this._show) {
      this._delegate = await this.addToInternal(viewer);
    }
    // this.show = this._show;
  }
  abstract addToInternal(viewer: BC.Viewer): any;
  flyTo() {
    if (this._viewer && this._delegate) {
      this._viewer.flyTo(this._delegate);
    }
  }
  get delegate() {
    return this._delegate;
  }
  get parent() {
    return this._parent;
  }
  set parent(parent: LayerGroup) {
    this._parent = parent;
  }
  get id() {
    return this._id;
  }

  get type() {
    return this._type
  }

  set transparency(transparency: number) {
    this._transparency = transparency * 100;
    if (this._delegate) {
      this.setTransparency(transparency < 1.0, transparency)
    }
  }
  get transparency() {
    return this._transparency;
  }

  /**
   * 是否显示
   */
  set show(show: boolean) {
    this._show = show;
    if (this._delegate) {
      this.showInternal(show);
    } else if (show) {
      this._delegate = this.addToInternal(this._viewer);
    }
  }
  get show(): any {
    return this._show;
  }

  protected showInternal(show: boolean) {
    this._delegate && (this._delegate.show = show);
  }
  remove() {
    if (this._parent) {
      this._parent.removeChild(this._id);
    } else {
      this.removeInternal();
    }
  }
  abstract setTransparency(transpant: boolean, alphaValue: number): any;
  abstract removeInternal(): any;
  /**
   * 查找图层
   * @param layerId
   * @returns
   */
  findLayer(layerId: string): BaseLayer | null {
    if (this._id === layerId) {
      return this;
    }
    return null;
  }

  getLayerTree(): LayerItem {
    return {
      id: this._id,
      title: this._title,
      type: this._type,
      show: this._show,
      options: this.options,
      transparency: this._transparency
    };
  }

  getOverlays() {}
}
