<!--
  @fileoverview MapLibre模块布局组件
  @description 作为MapLibre二维地图模块的布局容器，提供子路由视图渲染
  <AUTHOR>
  @version 1.0.0
-->

<template>
  <div class="maplibre-container">
    <!-- <div class="child-router">
    </div> -->
    <router-view />
    <MapContainer />
    <TinyTools :type="'maplibre'" v-if="!isAnalysisOnly"></TinyTools>
    <div v-if="!isAnalysisOnly">
      <template v-for="item in mainComponents" :key="item.name">
        <component :is="item.component" :mainItem="item"></component>
      </template>
    </div>
    <BaseMapToggle ref="baseMapToggleRef" :tiandituToken="tiandituToken" v-if="!isAnalysisOnly"/>
    <LegendCp v-if="!isAnalysisOnly"/>
  </div>

</template>

<script setup lang="ts">
// 无需额外逻辑，仅作为路由布局容器
import TinyTools from '@/views/tinytools/index.vue';
import BaseMapToggle from './basemap/BaseMapToggle.vue';
import { useDialogStore } from '@/stores/Dialogs';
import MapContainer from './MapContainer.vue'
import LegendCp from '@/components/legend/LegendCp.vue'
const Dialogs = useDialogStore();
const { mainComponents } = storeToRefs(Dialogs);
const tiandituToken = ref('b254904598a72fd14661de55fa70511d')
const analysisModeStore = useAnalysisModeStore();
const { isAnalysisOnly } = storeToRefs(analysisModeStore);
</script>

<style scoped lang="scss">
.maplibre-container {
  width: 100%;
  height: 100%;
  position: relative;
}

// .child-router {
//   position: absolute;
//   z-index: 1;
// }</style>
