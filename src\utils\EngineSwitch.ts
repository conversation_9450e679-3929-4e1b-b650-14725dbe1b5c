/**
 * @fileoverview 二三维引擎切换管理器
 * @description 提供统一的MapLibre和Cesium引擎切换管理，解决切换时的数据加载和状态管理问题
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-12-19
 */

import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import { AppCesium } from '@/lib/cesium/AppCesium';
import type { Router } from 'vue-router';

/**
 * @description 引擎类型枚举
 */
export type EngineType = 'maplibre' | 'cesium';

/**
 * @description 切换状态枚举
 */
export type SwitchState = 'idle' | 'switching' | 'success' | 'error';

/**
 * @description 切换配置接口
 */
export interface SwitchConfig {
  /** 是否启用过渡动画 */
  enableTransition: boolean;
  /** 切换超时时间(毫秒) */
  timeout: number;
  /** 是否启用调试日志 */
  debug: boolean;
  /** 切换前是否显示加载指示 */
  showLoading: boolean;
}

/**
 * @description 切换事件回调接口
 */
export interface SwitchCallbacks {
  /** 切换开始回调 */
  onSwitchStart?: (from: EngineType, to: EngineType) => void;
  /** 切换成功回调 */
  onSwitchSuccess?: (engine: EngineType) => void;
  /** 切换失败回调 */
  onSwitchError?: (error: Error, engine: EngineType) => void;
  /** 清理完成回调 */
  onCleanupComplete?: (engine: EngineType) => void;
}

/**
 * @class EngineSwitch
 * @description 二三维引擎切换管理器
 * @details 提供统一的引擎切换API，包括状态管理、资源清理、错误恢复等功能
 */
export class EngineSwitch {
  /** @description 单例实例 */
  private static instance: EngineSwitch | null = null;
  
  /** @description 当前引擎类型 */
  private currentEngine: EngineType | null = null;
  
  /** @description 切换状态 */
  private switchState: SwitchState = 'idle';
  
  /** @description 路由实例 */
  private router: Router | null = null;
  
  /** @description 切换配置 */
  private config: SwitchConfig = {
    enableTransition: true,
    timeout: 10000,
    debug: true,
    showLoading: true
  };
  
  /** @description 回调函数 */
  private callbacks: SwitchCallbacks = {};
  
  /** @description 切换锁，防止并发切换 */
  private switchLock = false;
  
  /** @description 清理任务队列 */
  private cleanupTasks: Array<() => Promise<void>> = [];
  
  /**
   * @description 获取单例实例
   * @returns EngineSwitch实例
   */
  public static getInstance(): EngineSwitch {
    if (!this.instance) {
      this.instance = new EngineSwitch();
    }
    return this.instance;
  }
  
  /**
   * @description 初始化管理器
   * @param router Vue Router实例
   * @param config 切换配置
   * @param callbacks 事件回调
   */
  public initialize(
    router: Router,
    config?: Partial<SwitchConfig>,
    callbacks?: SwitchCallbacks
  ): void {
    this.router = router;
    this.config = { ...this.config, ...config };
    this.callbacks = { ...this.callbacks, ...callbacks };
    
    // 根据当前路由检测引擎类型
    this.detectCurrentEngine();
    
    this.log('EngineSwitch管理器初始化完成', {
      currentEngine: this.currentEngine,
      config: this.config
    });
  }
  
  /**
   * @description 检测当前引擎类型
   */
  private detectCurrentEngine(): void {
    if (!this.router) return;
    
    const currentPath = this.router.currentRoute.value.path;
    if (currentPath.includes('/cesium')) {
      this.currentEngine = 'cesium';
    } else if (currentPath.includes('/maplibre')) {
      this.currentEngine = 'maplibre';
    }
    
    this.log(`检测到当前引擎: ${this.currentEngine}`);
  }
  
  /**
   * @description 切换到二维模式
   * @returns Promise<boolean> 切换是否成功
   */
  public async switchTo2D(): Promise<boolean> {
    return this.switchEngine('maplibre');
  }
  
  /**
   * @description 切换到三维模式
   * @returns Promise<boolean> 切换是否成功
   */
  public async switchTo3D(): Promise<boolean> {
    return this.switchEngine('cesium');
  }
  
  /**
   * @description 核心切换逻辑
   * @param targetEngine 目标引擎类型
   * @returns Promise<boolean> 切换是否成功
   */
  private async switchEngine(targetEngine: EngineType): Promise<boolean> {
    // 检查是否已经是目标引擎
    if (this.currentEngine === targetEngine) {
      this.log(`已经是${targetEngine}模式，无需切换`);
      return true;
    }
    
    // 检查切换锁
    if (this.switchLock) {
      this.log('切换正在进行中，请稍候...');
      return false;
    }
    
    this.switchLock = true;
    this.switchState = 'switching';
    
    try {
      this.log(`开始切换: ${this.currentEngine} -> ${targetEngine}`);
      
      // 触发切换开始回调
      this.callbacks.onSwitchStart?.(this.currentEngine!, targetEngine);
      
      // 显示加载指示
      if (this.config.showLoading) {
        this.showLoadingIndicator();
      }
      
      // 第一步：清理当前引擎
      await this.cleanupCurrentEngine();
      
      // 第二步：预初始化目标引擎环境
      await this.prepareTargetEngine(targetEngine);
      
      // 第三步：执行路由切换
      await this.performRouteSwitch(targetEngine);
      
      // 第四步：等待新引擎完全初始化
      await this.waitForEngineReady(targetEngine);
      
      // 更新状态
      this.currentEngine = targetEngine;
      this.switchState = 'success';
      
      // 隐藏加载指示
      if (this.config.showLoading) {
        this.hideLoadingIndicator();
      }
      
      // 触发成功回调
      this.callbacks.onSwitchSuccess?.(targetEngine);
      
      this.log(`切换成功: 当前引擎为 ${targetEngine}`);
      return true;
      
    } catch (error) {
      this.switchState = 'error';
      this.log('切换失败:', error);
      
      // 隐藏加载指示
      if (this.config.showLoading) {
        this.hideLoadingIndicator();
      }
      
      // 触发错误回调
      this.callbacks.onSwitchError?.(error as Error, targetEngine);
      
      // 尝试恢复到之前的状态
      await this.recoverFromError();
      
      return false;
      
    } finally {
      this.switchLock = false;
      this.switchState = 'idle';
    }
  }
  
  /**
   * @description 清理当前引擎
   */
  private async cleanupCurrentEngine(): Promise<void> {
    if (!this.currentEngine) return;
    
    this.log(`开始清理 ${this.currentEngine} 引擎...`);
    
    try {
      if (this.currentEngine === 'maplibre') {
        await this.cleanupMapLibre();
      } else if (this.currentEngine === 'cesium') {
        await this.cleanupCesium();
      }
      
      // 执行自定义清理任务
      for (const task of this.cleanupTasks) {
        await task();
      }
      
      // 强制垃圾回收（如果支持）
      if ((window as any).gc) {
        (window as any).gc();
      }
      
      this.callbacks.onCleanupComplete?.(this.currentEngine);
      this.log(`${this.currentEngine} 引擎清理完成`);
      
    } catch (error) {
      this.log(`清理 ${this.currentEngine} 引擎时出错:`, error);
      throw error;
    }
  }
  
  /**
   * @description 清理MapLibre引擎
   */
  private async cleanupMapLibre(): Promise<void> {
    try {
      this.log('开始清理MapLibre引擎...');
      
      // 检查AppMaplibre状态，避免重复清理
      try {
        if (!AppMaplibre.isDestroyed()) {
          // 调用AppMaplibre的销毁方法
          AppMaplibre.destroy();
          this.log('✅ AppMaplibre.destroy()调用成功');
        } else {
          this.log('⚠️ AppMaplibre已销毁，跳过destroy调用');
        }
      } catch (destroyError) {
        this.log('⚠️ 调用AppMaplibre.destroy()时出错:', destroyError);
        // 继续执行后续清理，不中断流程
      }
      
      // 重置AppMaplibre实例状态
      try {
        if (typeof (AppMaplibre as any).resetInstance === 'function') {
          (AppMaplibre as any).resetInstance();
          this.log('✅ AppMaplibre实例状态已重置');
        }
      } catch (resetError) {
        this.log('⚠️ 重置AppMaplibre实例状态时出错:', resetError);
      }
      
      // 等待DOM清理完成
      try {
        await this.waitForDOM(() => {
          const mapElement = document.getElementById('map');
          return !mapElement || mapElement.children.length === 0;
        }, 3000); // 缩短等待时间到3秒
        
        this.log('✅ DOM清理验证完成');
      } catch (domError) {
        this.log('⚠️ DOM清理验证失败:', domError);
        // DOM清理失败不阻断切换流程
      }
      
      this.log('✅ MapLibre引擎清理完成');
      
    } catch (error) {
      this.log('❌ 清理MapLibre引擎时出错:', error);
      // MapLibre清理失败不阻断切换流程，但记录详细错误
      console.warn('[EngineSwitch] MapLibre清理失败，但不影响切换流程:', error);
    }
  }
  
  /**
   * @description 清理Cesium引擎
   */
  private async cleanupCesium(): Promise<void> {
    try {
      this.log('开始清理Cesium引擎...');
      
      // 使用AppCesium的标准销毁方法，避免直接操作viewer
      try {
        const cesiumInstance = AppCesium.getInstance();
        
        // 检查实例状态，避免重复清理
        if (!cesiumInstance.isDestroyed()) {
          // 使用AppCesium的销毁方法，它会安全地处理viewer
          cesiumInstance.destroy();
          this.log('✅ AppCesium实例已销毁');
        } else {
          this.log('⚠️ AppCesium实例已销毁，跳过重复清理');
        }
        
      } catch (instanceError) {
        this.log('⚠️ 清理AppCesium实例时出错:', instanceError);
        // 继续执行DOM清理
      }
      
      // 重置AppCesium单例状态
      try {
        if (typeof (AppCesium as any).resetInstance === 'function') {
          (AppCesium as any).resetInstance();
          this.log('✅ AppCesium单例状态已重置');
        }
      } catch (resetError) {
        this.log('⚠️ 重置AppCesium单例状态时出错:', resetError);
      }
      
      // 清理DOM容器（作为保险措施）
      try {
        const cesiumContainer = document.getElementById('cesium-container');
        if (cesiumContainer) {
          // 不直接清空innerHTML，让CesiumContainer组件自己处理
          this.log('📋 Cesium容器DOM将由组件自身清理');
        }
      } catch (domError) {
        this.log('⚠️ 处理Cesium容器DOM时出错:', domError);
      }
      
      this.log('✅ Cesium引擎清理完成');
      
    } catch (error) {
      this.log('❌ 清理Cesium引擎时出错:', error);
      // Cesium清理失败不阻断切换流程，但记录详细错误
      console.warn('[EngineSwitch] Cesium清理失败，但不影响切换流程:', error);
    }
  }
  
  /**
   * @description 准备目标引擎环境
   * @param targetEngine 目标引擎类型
   */
  private async prepareTargetEngine(targetEngine: EngineType): Promise<void> {
    this.log(`准备 ${targetEngine} 引擎环境...`);
    
    // 可以在这里进行预加载资源等操作
    if (targetEngine === 'cesium') {
      // 预加载Cesium相关资源
      await this.preloadCesiumResources();
    } else if (targetEngine === 'maplibre') {
      // 预加载MapLibre相关资源
      await this.preloadMapLibreResources();
    }
  }
  
  /**
   * @description 预加载Cesium资源
   */
  private async preloadCesiumResources(): Promise<void> {
    // 可以预加载地形、影像等资源
    this.log('预加载Cesium资源...');
  }
  
  /**
   * @description 预加载MapLibre资源
   */
  private async preloadMapLibreResources(): Promise<void> {
    // 可以预加载样式、字体等资源
    this.log('预加载MapLibre资源...');
  }
  
  /**
   * @description 执行路由切换
   * @param targetEngine 目标引擎类型
   */
  private async performRouteSwitch(targetEngine: EngineType): Promise<void> {
    if (!this.router) {
      throw new Error('Router实例未初始化');
    }
    
    const targetRoute = targetEngine === 'cesium' ? '/cesium' : '/maplibre';
    
    this.log(`执行路由切换: ${targetRoute}`);
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('路由切换超时'));
      }, this.config.timeout);
      
      this.router!.push(targetRoute).then(() => {
        clearTimeout(timeout);
        resolve();
      }).catch((error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }
  
  /**
   * @description 等待引擎准备就绪
   * @param targetEngine 目标引擎类型
   */
  private async waitForEngineReady(targetEngine: EngineType): Promise<void> {
    this.log(`等待 ${targetEngine} 引擎就绪...`);
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`${targetEngine}引擎初始化超时`));
      }, this.config.timeout);
      
      let retryCount = 0;
      const maxRetries = Math.floor(this.config.timeout / 100); // 基于超时时间计算最大重试次数
      
      const checkReady = () => {
        retryCount++;
        
        try {
          if (targetEngine === 'cesium') {
            // 检查AppCesium状态而不是直接调用getViewer
            const cesiumInstance = AppCesium.getInstance();
            if (!cesiumInstance.isDestroyed() && cesiumInstance.isInitialized()) {
              try {
                const viewer = cesiumInstance.getViewer();
                if (viewer && viewer.scene && viewer.scene.canvas) {
                  this.log(`✅ Cesium引擎就绪 (重试${retryCount}次)`);
                  clearTimeout(timeout);
                  resolve();
                  return;
                }
              } catch (viewerError) {
                const errorMessage = viewerError instanceof Error ? viewerError.message : String(viewerError);
                this.log(`⚠️ Cesium viewer未就绪: ${errorMessage}`);
              }
            }
          } else if (targetEngine === 'maplibre') {
            // 检查AppMaplibre状态而不是直接调用getMap
            if (!AppMaplibre.isDestroyed() && AppMaplibre.isInitialized()) {
              try {
                const map = AppMaplibre.getMap();
                if (map && map.loaded && map.loaded()) {
                  this.log(`✅ MapLibre引擎就绪 (重试${retryCount}次)`);
                  clearTimeout(timeout);
                  resolve();
                  return;
                }
              } catch (mapError) {
                const errorMessage = mapError instanceof Error ? mapError.message : String(mapError);
                this.log(`⚠️ MapLibre map未就绪: ${errorMessage}`);
              }
            }
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          this.log(`⚠️ 检查${targetEngine}引擎状态时出错: ${errorMessage}`);
        }
        
        // 检查是否超过最大重试次数
        if (retryCount >= maxRetries) {
          clearTimeout(timeout);
          reject(new Error(`${targetEngine}引擎初始化失败：超过最大重试次数${maxRetries}`));
          return;
        }
        
        // 继续检查
        setTimeout(checkReady, 100);
      };
      
      // 首次检查前稍微延迟，给路由切换一点时间
      setTimeout(checkReady, 200);
    });
  }
  
  /**
   * @description 从错误中恢复
   */
  private async recoverFromError(): Promise<void> {
    this.log('尝试从切换错误中恢复...');
    
    try {
      // 可以尝试恢复到默认状态或之前的状态
      // 这里暂时只记录日志
      this.log('错误恢复完成');
    } catch (error) {
      this.log('错误恢复失败:', error);
    }
  }
  
  /**
   * @description 显示加载指示器
   */
  private showLoadingIndicator(): void {
    // 可以显示全屏加载指示器
    this.log('显示加载指示器');
  }
  
  /**
   * @description 隐藏加载指示器
   */
  private hideLoadingIndicator(): void {
    // 隐藏加载指示器
    this.log('隐藏加载指示器');
  }
  
  /**
   * @description 等待DOM条件满足
   * @param condition 条件函数
   * @param timeout 超时时间
   */
  private async waitForDOM(
    condition: () => boolean,
    timeout: number = 5000
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('DOM条件等待超时'));
      }, timeout);
      
      const checkCondition = () => {
        if (condition()) {
          clearTimeout(timeoutId);
          resolve();
        } else {
          setTimeout(checkCondition, 50);
        }
      };
      
      checkCondition();
    });
  }
  
  /**
   * @description 添加清理任务
   * @param task 清理任务函数
   */
  public addCleanupTask(task: () => Promise<void>): void {
    this.cleanupTasks.push(task);
  }
  
  /**
   * @description 获取当前引擎类型
   * @returns 当前引擎类型
   */
  public getCurrentEngine(): EngineType | null {
    return this.currentEngine;
  }
  
  /**
   * @description 获取切换状态
   * @returns 切换状态
   */
  public getSwitchState(): SwitchState {
    return this.switchState;
  }
  
  /**
   * @description 是否正在切换
   * @returns 是否正在切换
   */
  public isSwitching(): boolean {
    return this.switchState === 'switching';
  }
  
  /**
   * @description 日志输出
   * @param message 日志消息
   * @param data 附加数据
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log(`[EngineSwitch] ${message}`, data || '');
    }
  }
  
  /**
   * @description 销毁管理器
   */
  public destroy(): void {
    this.router = null;
    this.callbacks = {};
    this.cleanupTasks = [];
    this.switchLock = false;
    this.switchState = 'idle';
    EngineSwitch.instance = null;
    
    this.log('EngineSwitch管理器已销毁');
  }
}

/**
 * @description 获取全局EngineSwitch实例
 * @returns EngineSwitch单例实例
 */
export const getEngineSwitch = (): EngineSwitch => {
  return EngineSwitch.getInstance();
}; 