<template>
  <BaseCard title="设备统计">
    <div class="flex items-center justify-between pb-2.5 pt-14px px-5">
      <div v-for="item in list" :key="item.name" class="text-center">
        <img :src="item.icon" alt="" srcset="" />
        <div class="font-size-3.5 color-#5C5F66">{{ item.name }}</div>
        <div class="color-#2C3037">{{ item.value }}{{ item.unit }}</div>
      </div>
    </div>
  </BaseCard>
</template>
<script lang="ts" setup>
import { getImages } from "@/utils/getImages";
const list = ref([
  {
    name: "管线长度",
    value: "288.32",
    unit: "km",
    icon: getImages("cesium/1.png"),
  },
  {
    name: "管点数量",
    value: "25008",
    unit: "个",
    icon: getImages("cesium/2.png"),
  },
  {
    name: "设备总数",
    value: "254",
    unit: "个",
    icon: getImages("cesium/3.png"),
  },
]);
</script>
<style lang="scss" scoped></style>
