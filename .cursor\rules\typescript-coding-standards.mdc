---
description: 
globs: 
alwaysApply: false
---
# TypeScript 编码标准与约定

## 项目特定的 TypeScript 规范

### 类型定义模式
```typescript
// 优先使用 interface 定义对象结构
interface PipeNode {
  id: string;
  name: string;
  geojson: GeoJSON.Feature<GeoJSON.Point>;
}

// 使用 type 定义联合类型和映射类型
type RenderEventType = 'layer_added' | 'features_rendered' | 'style_updated';
type StyleState = 'default' | 'selected' | 'hover' | 'error';
```

### 枚举使用规范
```typescript
// 使用具体值的枚举，便于调试和存储
export enum PipeNodeType {
  INSPECTION_WELL = 'inspection_well',
  JUNCTION = 'junction', 
  OUTLET = 'outlet',
  INLET = 'inlet'
}
```

### 异步操作处理
```typescript
// 优先使用 async/await 模式
async initialize(): Promise<void> {
  try {
    await this.createDataSources();
    await this.createLayers();
  } catch (error: any) {
    console.error('初始化失败:', error);
    throw error;
  }
}
```

### 泛型使用模式
```typescript
// 在数据转换和工具函数中使用泛型
private updateFeatureProperty<T extends string>(
  property: T,
  featureIds: string[],
  value: boolean
): void {
  // 实现
}
```

## 错误处理标准

### 异常类型声明
```typescript
// 明确声明 error 类型为 any 以访问 message 属性
} catch (error: any) {
  console.error('操作失败:', error.message);
}
```

### 防御性编程
```typescript
// 在访问可能为空的对象前进行检查
if (this.map && this.map.getLayer(layerId)) {
  this.map.removeLayer(layerId);
}
```

## MapLibre GL 集成模式

### 类型安全的地图操作
```typescript
// 使用类型断言处理 MapLibre GL 源对象
const source = this.map.getSource(sourceId);
if (source && source.type === 'geojson') {
  (source as any).setData(data);
}
```

### 图层规范类型
```typescript
import type { LayerSpecification } from 'maplibre-gl';

interface LayerConfig {
  id: string;
  sourceId: string;
  layerSpec: LayerSpecification;
  enabled: boolean;
  zIndex: number;
}
```

## Vue.js 组合式 API 集成

### 响应式状态管理
```typescript
import { ref, onMounted, onUnmounted } from 'vue';

// 使用 ref 管理渲染器实例
const pipeRenderer = ref<PipeRenderer | null>(null);

// 在生命周期钩子中管理资源
onMounted(async () => {
  if (map) {
    pipeRenderer.value = new PipeRenderer(map, options);
    await pipeRenderer.value.initialize();
  }
});

onUnmounted(() => {
  pipeRenderer.value?.destroy();
});
```

### 类型化的组件 Props
```typescript
interface PipeEditProps {
  initialMode?: 'node' | 'line';
  showLabels?: boolean;
  minZoom?: number;
  maxZoom?: number;
}
```

## 性能优化模式

### 防抖更新机制
```typescript
private updateTimer: number | null = null;
private readonly UPDATE_DEBOUNCE_MS = 16; // 60fps

private queueUpdate(key: string, updateFn: () => void): void {
  this.updateQueue.add(key);
  
  if (this.updateTimer) {
    clearTimeout(this.updateTimer);
  }
  
  this.updateTimer = window.setTimeout(() => {
    updateFn();
    this.updateQueue.clear();
    this.updateTimer = null;
  }, this.UPDATE_DEBOUNCE_MS);
}
```

### 类型化的事件系统
```typescript
import { Subject } from 'rxjs';

interface RenderEventData {
  type: RenderEventType;
  layerId?: string;
  featureIds?: string[];
  timestamp: number;
}

private eventBus = new Subject<RenderEventData>();
```

## 调试和开发工具

### 详细的日志类型
```typescript
// 使用结构化日志便于调试
console.log(`⚙️ 处理图层配置: ${config.id}`, {
  enabled: config.enabled,
  sourceId: config.sourceId,
  layerType: config.layerSpec.type
});
```

### 状态跟踪接口
```typescript
interface RenderState {
  initialized: boolean;
  activeLayerCount: number;
  renderedFeatureCount: {
    nodes: number;
    lines: number;
  };
  currentStyleState: Record<string, StyleState>;
}
```

## 命名约定

### 常量命名
```typescript
// 使用 UPPER_SNAKE_CASE 命名常量
private static readonly LAYER_IDS = {
  NODES: 'pipe-nodes',
  LINES: 'pipe-lines'
} as const;

private readonly UPDATE_DEBOUNCE_MS = 16;
```

### 方法命名
```typescript
// 使用描述性动词前缀
async initialize(): Promise<void>
private createDataSources(): Promise<void>
private addLayerConfig(config: LayerConfig): void
public renderFeatures(nodes: PipeNode[], lines: PipeLine[]): void
```

### 事件和回调命名
```typescript
// 事件监听器使用 on 前缀
this.map.on('zoom', this.onZoomChange);

// 回调函数使用 handle 前缀
const handleRenderComplete = (event: RenderEventData) => {
  // 处理逻辑
};
```

