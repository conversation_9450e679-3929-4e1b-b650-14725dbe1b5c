/**
 * @fileoverview 坐标拾取功能业务逻辑
 * @description 提供坐标拾取功能的核心业务逻辑，支持MapLibre和Cesium两种地图引擎
 * <AUTHOR>
 * @version 2.0.0
 */

import { ref, onMounted, onUnmounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useCalcCoordStore } from '@/stores/calcCoord';
import { MaplibreCoordinatePicker } from '@/lib/coordinate/MaplibreCoordinatePicker';
import { CesiumCoordinatePicker } from '@/lib/coordinate/CesiumCoordinatePicker';
import type { CoordinatePoint } from '@/utils/coordinate/CoordinateTransform';
import { ElMessage } from 'element-plus';
import { copyCoordinateToClipboard, copy3DCoordinateToClipboard } from '@/utils/clipboard';

/**
 * @interface UseCoordinatePickerOptions
 * @description 坐标拾取选项接口，定义坐标拾取的配置参数
 */
interface UseCoordinatePickerOptions {
  /** 地图类型，支持MapLibre和Cesium两种引擎 */
  mapType: 'maplibre' | 'cesium';
  /** 地图实例，根据mapType类型提供对应引擎的地图实例 */
  mapInstance: any;
  /** 是否显示拾取点标记，默认为true */
  showMarker?: boolean;
  /** 标记样式配置 */
  markerStyle?: {
    /** 标记颜色，默认为红色 */
    color?: string;
    /** 标记大小，默认为8像素 */
    size?: number;
    /** 标记透明度，默认为0.8 */
    opacity?: number;
  };
}

/**
 * @description 将WGS84坐标转换为EPSG:3857(墨卡托投影)坐标
 * @param coord - WGS84坐标
 * @returns EPSG:3857坐标
 */
function wgs84ToEpsg3857(coord: CoordinatePoint): CoordinatePoint {
  // 地球半径
  const EARTH_RADIUS = 6378137.0;
  
  // 经度转换为X坐标
  const x = coord.lng * Math.PI * EARTH_RADIUS / 180.0;
  
  // 纬度转换为Y坐标（墨卡托投影公式）
  let y = Math.log(Math.tan((90.0 + coord.lat) * Math.PI / 360.0)) * EARTH_RADIUS;
  
  // 处理极端情况
  if (!Number.isFinite(y)) {
    y = 0;
  }
  
  return {
    lng: Number(x.toFixed(2)), // 保留2位小数
    lat: Number(y.toFixed(2)), // 保留2位小数
    alt: coord.alt
  };
}

/**
 * @description 坐标拾取组件业务逻辑
 * @param options - 坐标拾取配置选项
 * @returns 坐标拾取相关状态和方法
 */
export function useCoordinatePicker(options: UseCoordinatePickerOptions) {
  // 从store获取坐标数据
  const calcCoordStore = useCalcCoordStore();
  const { currentCoord } = storeToRefs(calcCoordStore);
  
  // 创建默认坐标对象
  const defaultCoord: CoordinatePoint = { lng: 0, lat: 0 };
  
  /**
   * 各坐标系坐标
   * 所有坐标系的坐标值都是响应式的，在拾取成功后会更新
   */
  const wgs84 = ref<CoordinatePoint>({ ...defaultCoord });
  const gcj02 = ref<CoordinatePoint>({ ...defaultCoord });
  const bd09 = ref<CoordinatePoint>({ ...defaultCoord });
  const epsg3857 = ref<CoordinatePoint>({ ...defaultCoord });
  
  // 坐标拾取器实例
  const coordinatePicker = ref<MaplibreCoordinatePicker | CesiumCoordinatePicker | null>(null);
  
  // 是否正在拾取坐标
  const isPicking = ref(false);
  
  // 上次拾取的时间戳，用于防止频繁点击
  const lastPickTime = ref(0);
  
  /**
   * @description 创建坐标拾取器
   * @param mapInstance - 地图实例
   */
  const createPicker = (mapInstance: any) => {
    try {
      // 销毁现有实例
      if (coordinatePicker.value) {
        coordinatePicker.value.destroy();
        coordinatePicker.value = null;
      }
      
      // 根据地图类型创建对应的拾取器
      if (options.mapType === 'maplibre') {
        coordinatePicker.value = new MaplibreCoordinatePicker(mapInstance, {
          showMarker: options.showMarker ?? true,
          includeProjection: true,
          continuous: true, // 启用连续拾取模式
          markerStyle: options.markerStyle
        });
      } else if (options.mapType === 'cesium') {
        coordinatePicker.value = new CesiumCoordinatePicker(mapInstance, {
          showMarker: options.showMarker ?? true,
          includeProjection: true,
          continuous: true, // 启用连续拾取模式
          markerStyle: options.markerStyle
        });
      } else {
        throw new Error(`不支持的地图类型: ${options.mapType}`);
      }
      
      return true;
    } catch (error) {
      console.error('创建坐标拾取器失败:', error);
      ElMessage.error(`创建坐标拾取器失败: ${(error as Error).message}`);
      return false;
    }
  };
  
  /**
   * @description 开始坐标拾取
   * @returns 是否成功开始拾取
   */
  const startPicking = (): boolean => {
    if (!coordinatePicker.value) {
      // 如果拾取器不存在，尝试创建
      if (!createPicker(options.mapInstance)) {
        ElMessage.error('无法开始坐标拾取: 拾取器初始化失败');
        return false;
      }
    }
    
    try {
      isPicking.value = true;
      calcCoordStore.startPicking();
      
      // 设置防抖阈值，避免频繁更新
      const DEBOUNCE_THRESHOLD = 200; // 毫秒
      
      // 开始坐标拾取，并设置回调函数
      coordinatePicker.value!.start((result) => {
        const now = Date.now();
        
        // 防抖：忽略过于频繁的更新
        if (now - lastPickTime.value < DEBOUNCE_THRESHOLD) {
          return;
        }
        
        lastPickTime.value = now;
        
        // 更新各坐标系坐标
        wgs84.value = result.wgs84;
        gcj02.value = result.gcj02;
        bd09.value = result.bd09;
        
        // 计算 EPSG:3857 投影坐标
        epsg3857.value = wgs84ToEpsg3857(result.wgs84);
        // 更新 store 中的坐标
        calcCoordStore.setCurrentCoord(result.wgs84);
      }, (error) => {
        // 错误处理
        console.error('坐标拾取错误:', error);
        ElMessage.error(`坐标拾取错误: ${error.message}`);
        stopPicking();
      });
      
      return true;
    } catch (error) {
      console.error('开始坐标拾取失败:', error);
      ElMessage.error(`开始坐标拾取失败: ${(error as Error).message}`);
      isPicking.value = false;
      return false;
    }
  };
  
  /**
   * @description 停止坐标拾取
   */
  const stopPicking = () => {
    if (!coordinatePicker.value || !isPicking.value) {
      return;
    }
    
    try {
      coordinatePicker.value.stop();
      isPicking.value = false;
      calcCoordStore.stopPicking();
    } catch (error) {
      console.error('停止坐标拾取失败:', error);
    }
  };
  
  /**
   * @description 获取地图中心点坐标
   * @returns 地图中心点坐标
   */
  const getMapCenter = (): CoordinatePoint => {
    if (!coordinatePicker.value) {
      return { ...defaultCoord };
    }
    
    try {
      return coordinatePicker.value.getMapCenter();
    } catch (error) {
      console.error('获取地图中心点失败:', error);
      return { ...defaultCoord };
    }
  };
  
  /**
   * @description 飞行到指定坐标
   * @param coord - 目标坐标
   * @param duration - 飞行时长（毫秒），默认为1000毫秒
   * @returns 飞行操作的Promise
   */
  const flyTo = async (coord: CoordinatePoint, duration: number = 1000): Promise<boolean> => {
    if (!coordinatePicker.value) {
      ElMessage.error('地图实例未初始化');
      return false;
    }
    
    try {
      await coordinatePicker.value.flyTo(coord, duration);
      return true;
    } catch (error) {
      console.error('飞行到指定坐标失败:', error);
      ElMessage.error(`飞行到指定坐标失败: ${(error as Error).message}`);
      return false;
    }
  };
  
  /**
   * @description 重置坐标数据
   */
  const resetCoordinates = () => {
    wgs84.value = { ...defaultCoord };
    gcj02.value = { ...defaultCoord };
    bd09.value = { ...defaultCoord };
    epsg3857.value = { ...defaultCoord };
    calcCoordStore.clearCurrentCoord();
  };
  
  /**
   * @description 拷贝坐标到剪贴板
   * @param coordType - 坐标系类型
   * @returns 是否成功复制
   */
  const copyCoordinate = async (coordType: 'wgs84' | 'gcj02' | 'bd09' | 'epsg3857'): Promise<boolean> => {
    try {
      let lng = 0;
      let lat = 0;
      let alt: number | undefined;
      let precision = 6;
      let coordName = '';

      switch(coordType) {
        case 'wgs84':
          lng = wgs84.value.lng;
          lat = wgs84.value.lat;
          alt = wgs84.value.alt;
          precision = 6;
          coordName = 'WGS84坐标';
          break;
        case 'gcj02':
          lng = gcj02.value.lng;
          lat = gcj02.value.lat;
          alt = gcj02.value.alt;
          precision = 6;
          coordName = 'GCJ02坐标';
          break;
        case 'bd09':
          lng = bd09.value.lng;
          lat = bd09.value.lat;
          alt = bd09.value.alt;
          precision = 6;
          coordName = 'BD09坐标';
          break;
        case 'epsg3857':
          lng = epsg3857.value.lng;
          lat = epsg3857.value.lat;
          alt = epsg3857.value.alt;
          precision = 2;
          coordName = 'EPSG:3857坐标';
          break;
      }

      // 检查坐标是否有效
      if (lng === 0 && lat === 0) {
        ElMessage.warning('请先拾取坐标');
        return false;
      }

      // 根据地图类型决定是否包含高程
      if (options.mapType === 'cesium' && typeof alt === 'number' && !isNaN(alt)) {
        // Cesium三维地图，包含高程信息
        return await copy3DCoordinateToClipboard(lng, lat, alt, precision, 2, coordName);
      } else {
        // MapLibre二维地图，只包含经纬度
        return await copyCoordinateToClipboard(lng, lat, precision, coordName);
      }

    } catch (error) {
      console.error('复制坐标失败:', error);
      ElMessage.error(`复制坐标失败: ${(error as Error).message}`);
      return false;
    }
  };
  
  // 在组件挂载时初始化
  onMounted(() => {
    if (options.mapInstance) {
      createPicker(options.mapInstance);
    } else {
      console.warn('未提供地图实例，坐标拾取功能不可用');
    }
  });
  
  // 在组件卸载时清理资源
  onUnmounted(() => {
    stopPicking();
    
    if (coordinatePicker.value) {
      coordinatePicker.value.destroy();
      coordinatePicker.value = null;
    }
  });
  
  return {
    // 坐标数据
    wgs84,
    gcj02,
    bd09,
    epsg3857,
    currentCoord,
    
    // 状态
    isPicking,
    coordinatePicker,
    
    // 方法
    startPicking,
    stopPicking,
    getMapCenter,
    flyTo,
    resetCoordinates,
    copyCoordinate
  };
} 