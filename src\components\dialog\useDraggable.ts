/*
 * @Description:
 * @Date: 2022-09-28 16:00:02
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-09-20 16:53:44
 */
import { onBeforeUnmount, onMounted, watchEffect } from "vue";
import type { ComputedRef, Ref } from "vue";
const addUnit = (value?: string | number, defaultUnit = "px") => {
  if (!value) return "";
  if (typeof value === "string") {
    return value;
  } else if (typeof value === "number") {
    return `${value}${defaultUnit}`;
  }
};
export const useDraggable = (
  targetRef: Ref<HTMLElement | undefined>,
  dragRef: Ref<HTMLElement | undefined>,
  draggable: Ref<boolean>
) => {
  let transform = {
    offsetX: 0,
    offsetY: 0,
  };

  const clearTransform = () => {
    transform = {
      offsetX: 0,
      offsetY: 0,
    };
  };
  const onMousedown = (e: MouseEvent) => {
    const downX = e.clientX;
    const downY = e.clientY;
    const { offsetX, offsetY } = transform;

    const targetRect = targetRef.value!.getBoundingClientRect();
    const targetLeft = targetRect.left;
    const targetTop = targetRect.top;
    const targetWidth = targetRect.width;
    const targetHeight = targetRect.height;

    const clientWidth = document.documentElement.clientWidth;
    const clientHeight = document.documentElement.clientHeight;

    const minLeft = -targetLeft + offsetX;
    const minTop = -targetTop + offsetY;
    const maxLeft = clientWidth - targetLeft - targetWidth + offsetX;
    const maxTop = clientHeight - targetTop - targetHeight + offsetY;

    const onMousemove = (e: MouseEvent) => {
      const moveX = Math.min(
        Math.max(offsetX + e.clientX - downX, minLeft),
        maxLeft
      );
      const moveY = Math.min(
        Math.max(offsetY + e.clientY - downY, minTop),
        maxTop
      );

      transform = {
        offsetX: moveX,
        offsetY: moveY,
      };
      targetRef.value!.style.transform = `translate(${addUnit(
        moveX
      )}, ${addUnit(moveY)})`;
    };

    const onMouseup = () => {
      document.removeEventListener("mousemove", onMousemove);
      document.removeEventListener("mouseup", onMouseup);
    };

    document.addEventListener("mousemove", onMousemove);
    document.addEventListener("mouseup", onMouseup);
  };

  const onDraggable = () => {
    if (dragRef.value && targetRef.value) {
      dragRef.value.addEventListener("mousedown", onMousedown);
    }
  };

  const offDraggable = () => {
    if (dragRef.value && targetRef.value) {
      dragRef.value.removeEventListener("mousedown", onMousedown);
    }
  };

  onMounted(() => {
    watchEffect(() => {
      if (draggable.value) {
        onDraggable();
      } else {
        offDraggable();
      }
    });
  });

  onBeforeUnmount(() => {
    offDraggable();
  });

  return {
    clearTransform,
  };
};
export const useDraggableByParent = (
  targetRef: Ref<HTMLElement | undefined>,
  dragRef: Ref<HTMLElement | undefined>,
  draggable: ComputedRef<boolean>,
  parent: Ref<HTMLElement | undefined>
) => {
  let transform = {
    offsetX: 0,
    offsetY: 0,
  };
  const onMousedown = (e: MouseEvent) => {
    const downX = e.clientX;
    const downY = e.clientY;
    const { offsetX, offsetY } = transform;

    const targetRect = targetRef.value!.getBoundingClientRect();
    let targetLeft = targetRect.left;
    let targetTop = targetRect.top;
    const targetWidth = targetRect.width;
    const targetHeight = targetRect.height;
    let parentElement = document.documentElement;
    if (parent.value) {
      parentElement = parent.value;
      const parentRect = parent.value!.getBoundingClientRect();
      targetLeft -= parentRect.left;
      targetTop -= parentRect.top;
    }
    const clientWidth = parentElement.clientWidth;
    const clientHeight = parentElement.clientHeight;

    const minLeft = -targetLeft + offsetX;
    const minTop = -targetTop + offsetY;
    const maxLeft = clientWidth - targetLeft - targetWidth + offsetX;
    const maxTop = clientHeight - targetTop - targetHeight + offsetY;

    const onMousemove = (e: MouseEvent) => {
      const moveX = Math.min(
        Math.max(offsetX + e.clientX - downX, minLeft),
        maxLeft
      );
      const moveY = Math.min(
        Math.max(offsetY + e.clientY - downY, minTop),
        maxTop
      );

      transform = {
        offsetX: moveX,
        offsetY: moveY,
      };
      targetRef.value!.style.transform = `translate(${addUnit(
        moveX
      )}, ${addUnit(moveY)})`;
    };

    const onMouseup = () => {
      parentElement.removeEventListener("mousemove", onMousemove);
      parentElement.removeEventListener("mouseup", onMouseup);
    };

    parentElement.addEventListener("mousemove", onMousemove);
    parentElement.addEventListener("mouseup", onMouseup);
  };

  const onDraggable = () => {
    if (dragRef.value && targetRef.value) {
      dragRef.value.addEventListener("mousedown", onMousedown);
    }
  };

  const offDraggable = () => {
    if (dragRef.value && targetRef.value) {
      dragRef.value.removeEventListener("mousedown", onMousedown);
    }
  };

  onMounted(() => {
    watchEffect(() => {
      if (draggable.value) {
        onDraggable();
      } else {
        offDraggable();
      }
    });
  });

  onBeforeUnmount(() => {
    offDraggable();
  });
};
