
import { BaseLayer } from './BaseLayer'
import { Map as glMap } from 'maplibre-gl'
export class CustomXY<PERSON><PERSON>ayer extends BaseLayer {
  constructor(options: any) {
    super(options)
  }
  async addToInternal(map: glMap) {
    this._map.addSource(this.id, {
      type: 'raster',
      tiles: [
        this._url
      ],
      tileSize: 256
    })
    this._map.addLayer({
      id: this.id,
      type: 'raster',
      source: this.id
    })

    let delegate = this._map.getLayer(this.id)
    return delegate
  }

  removeInternal() {
    // this._viewer.imageryLayers.remove(this._delegate);
  }
}
