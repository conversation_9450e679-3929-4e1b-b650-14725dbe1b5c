<template>
  <page-card :close-icon="false" class="tabulate-sta" title="垂直净距分析">
    <!-- 输入区域 -->
    <div class="input-section">
      <div class="label-text">建设要求：</div>
      <div class="input-container">
        <el-input
          :model-value="buildingRequirement"
          @input="buildingRequirement = validateInput($event)"
          @blur="handleInputBlur"
          placeholder="请输入建设要求(正数)"
          clearable
          class="requirement-input"
          :disabled="isLoading"
        />
        <span class="unit-text">(m)</span>
      </div>
    </div>

    <!-- 按钮区域 -->
    <el-button
      type="primary"
      class="primary-btn h-9 w-130px"
      :loading="isLoading"
      @click="handleAnalysis"
      :disabled="!buildingRequirement"
    >
      分析
    </el-button>
    <el-button
      class="clear-btn w-130px"
      @click="handleClear"
      :disabled="isLoading"
    >
      清除
    </el-button>

    <!-- 结果显示区域 -->
    <!-- <el-row v-if="showResult" class="result-section">
      <el-col :span="24">
        <el-text class="result-label">分析结果：</el-text>
        <span
          :class="[
            'result-text',
            analysisResult === '满足建设要求' ? 'success' : 'error',
          ]"
          >{{ analysisResult }}</span
        >
      </el-col>
    </el-row> -->
    <div
      v-if="showResult"
      class="result-section"
      :class="analysisResult === '满足建设要求' ? 'success' : 'error'"
    >
      <el-icon v-if="analysisResult" class="color-#FF7373"
        ><CircleCloseFilled
      /></el-icon>
      <el-icon v-else class="color-#1966FF"><CircleCheckFilled /></el-icon>
      <el-text class="result-label">分析结果：</el-text>
      <span :class="'result-text'">{{ analysisResult }}</span>
    </div>
    <!-- 表格区域 -->
    <el-row v-if="showTable" class="table-section">
      <el-col :span="24">
        <el-text class="table-label">问题管网</el-text>
        <el-table
          class="routeCt"
          :row-class-name="tableRowClassName"
          :data="getCurrentPageData()"
          style="width: 100%"
        >
          <el-table-column
            prop="index"
            label="序号"
            min-width="50"
          ></el-table-column>
          <el-table-column
            prop="pipeCode1"
            label="管网编码1"
            min-width="90"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="pipeCode2"
            label="管网编码2"
            min-width="90"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="requirement"
            label="建设要求(m)"
            min-width="100"
          ></el-table-column>
          <el-table-column
            prop="actualDistance"
            label="实际距离(m)"
            min-width="100"
          ></el-table-column>
          <el-table-column prop="result" label="分析结果"></el-table-column>
          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleLocate(scope.row)"
              >
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center">
          <div>
            <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
          </div>
          <el-pagination
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :total="total"
            :pager-count="5"
            layout="prev, pager, next, jumper"
            class="pagination"
            small
            background
          ></el-pagination>
        </div>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import {
  Position,
  CircleCheckFilled,
  CircleCloseFilled,
} from "@element-plus/icons-vue";
import {
  horizontalDistanceAnalysis,
  verticalDistanceAnalysis,
} from "@/api/analysis";
import {
  pipeLocationByBms,
  clearPipeLocationHighlight,
} from "@/utils/PipeLocation";
import type { MapEngineType } from "@/components/BufferAnalysisDrawButtons.vue";

const route = useRoute();
const mapEngine = computed((): MapEngineType => {
  return route.path.includes("cesium") ? "cesium" : "maplibre";
});

/**
 * 定义垂直净距分析不满足要求的数据项接口
 */
interface VerticalDistanceAnalysisItem {
  index: number;
  pipeCode1: string; // 管网编码1
  pipeCode2: string; // 管网编码2
  requirement: number; // 建设要求(m)
  actualDistance: number; // 实际距离(m)
  result: string; // 分析结果
}

/**
 * 响应式数据状态
 */
const buildingRequirement = ref<string>("0.01");
const isLoading = ref<boolean>(false);
const analysisResult = ref<string>("");
const showResult = ref<boolean>(false);

// 表格相关状态
const tableData = ref<VerticalDistanceAnalysisItem[]>([]);
const currentPage = ref<number>(1);
const pageSize = ref<number>(5);
const pageSizes = ref<number[]>([5, 10, 20, 50]);
const total = ref<number>(0);
const showTable = ref<boolean>(false);

/**
 * 验证输入仅允许正数
 * @param value 输入值
 * @returns 验证后的值
 */
const validateInput = (value: string): string => {
  // 使用正则表达式验证：匹配正数（可以是整数或小数）
  const reg = /^[+]?\d*\.?\d*$/;
  if (value === "" || reg.test(value)) {
    return value;
  }
  // 如果不符合要求，则返回之前的有效值
  return buildingRequirement.value;
};

/**
 * 处理输入框失焦事件
 */
const handleInputBlur = () => {
  if (buildingRequirement.value) {
    const num = parseFloat(buildingRequirement.value);
    if (!isNaN(num) && num > 0) {
      buildingRequirement.value = num.toString();
    } else {
      buildingRequirement.value = "";
      ElMessage.warning("请输入有效的正数");
    }
  }
};

/**
 * 处理分析操作
 */
const handleAnalysis = async () => {
  if (!buildingRequirement.value || !buildingRequirement.value.trim()) {
    ElMessage.warning("请输入建设要求");
    return;
  }

  const distance = parseFloat(buildingRequirement.value);
  if (isNaN(distance) || distance <= 0) {
    ElMessage.error("请输入有效的数字");
    return;
  }

  isLoading.value = true;
  try {
    const { status, data } = await verticalDistanceAnalysis(distance);
    if (status === 200) {
      const list = data.data;
      if (list.length > 0) {
        analysisResult.value = "不满足建设要求";
        // 处理表格数据
        tableData.value = list.map((item: any, index: number) => {
          return {
            index: index + 1,
            pipeCode1: item.edgeId1 || item.pipeCode1 || "N/A", // 根据实际接口调整字段名
            pipeCode2: item.edgeId2 || item.pipeCode2 || "N/A", // 根据实际接口调整字段名
            requirement: distance,
            actualDistance: Number(
              item.distance || item.actualDistance || 0
            ).toFixed(2), // 格式化为两位小数
            result: "不满足",
          };
        });
        total.value = tableData.value.length;
        showTable.value = true;
        //todo: 1、地图展示不满足建设要求的数据
      } else {
        analysisResult.value = "满足建设要求";
        tableData.value = [];
        showTable.value = false;
      }
    }
    showResult.value = true;
    ElMessage.success("分析完成");
  } catch (error) {
    console.error("垂直净距分析失败:", error);
    ElMessage.error("分析失败，请重试");
    analysisResult.value = "分析失败";
    showResult.value = true;
    tableData.value = [];
    showTable.value = false;
  } finally {
    isLoading.value = false;
  }
};

/**
 * 处理清除操作
 */
const handleClear = () => {
  buildingRequirement.value = "";
  analysisResult.value = "";
  showResult.value = false;
  tableData.value = [];
  showTable.value = false;
  currentPage.value = 1;
  clearPipeLocationHighlight(mapEngine.value);
  ElMessage.info("已清除");
};

/**
 * 处理页码变化
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

/**
 * 处理每页条数变化
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  // 切换每页显示数量时，如果当前页已经没有数据，则跳转到第一页
  if (Math.ceil(total.value / pageSize.value) < currentPage.value) {
    currentPage.value = 1;
  }
};

/**
 * 获取当前页数据
 */
const getCurrentPageData = () => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = Math.min(
    startIndex + pageSize.value,
    tableData.value.length
  );
  return tableData.value.slice(startIndex, endIndex);
};

/**
 * 处理定位操作
 * @param row 当前行数据
 */
const handleLocate = async (row: VerticalDistanceAnalysisItem) => {
  try {
    console.log("正在定位管网:", row.pipeCode1, "和", row.pipeCode2);
    ElMessage.info("正在定位管网...");

    await pipeLocationByBms(
      [row.pipeCode1, row.pipeCode2],
      mapEngine.value,
      "line",
      {
        autoFit: true,
        padding: 100,
        highlightColor: "#ff4444",
        lineWidth: 5,
      }
    );

    // ElMessage.success("定位成功");
  } catch (error) {
    console.error("定位失败:", error);
    ElMessage.error("定位失败，请检查管线编码");
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
/**
 * 组件注销时清理定位高亮图层
 */
onUnmounted(() => {
  clearPipeLocationHighlight(mapEngine.value);
});
</script>

<style scoped lang="scss">
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 550px;
  min-height: 130px;
  z-index: 1000;
}

.input-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .label-text {
    color: #2c3037;
    font-size: 14px;
  }

  .input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 350px;
    .requirement-input {
      flex: 1;
    }

    .unit-text {
      color: #606266;
      font-size: 14px;
      min-width: 20px;
    }
  }
}

.button-section {
  margin-bottom: 20px;

  .analysis-btn {
    width: 40%;
    background-color: #1890ff;
    border-color: #1890ff;
    font-size: 13px;
    padding: 8px 12px;

    &:hover {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }

    &:disabled {
      background-color: #d9d9d9;
      border-color: #d9d9d9;
    }
  }

  .clear-btn {
    width: 40%;
    background-color: #8c8c8c;
    border-color: #8c8c8c;
    color: white;
    font-size: 13px;
    padding: 8px 12px;

    &:hover {
      background-color: #a8a8a8;
      border-color: #a8a8a8;
    }

    &:disabled {
      background-color: #d9d9d9;
      border-color: #d9d9d9;
    }
  }
}

.result-section {
  margin: 20px 0;
  color: #333333;
  padding: 5px 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  .result-item {
    margin-bottom: 10px;
  }
  .result-label {
    font-size: 14px;
    color: #333333;
    margin-right: 5px;
    margin-left: 10px;
  }

  .result-text {
    color: #333333;
    font-size: 14px;
    font-weight: 500;
  }
}
.success {
  background: #f1f8fe;
  border: 1px solid #92b7ff;
}

.error {
  background: #fef1f1;
  border: 1px solid #ff9292;
}
.table-section {
  margin-top: 20px;

  .table-label {
    color: #2c3037;
    font-size: 14px;
    margin-bottom: 8px;
    display: block;
  }

  .el-table {
    margin-bottom: 15px;
    font-size: 13px;

    :deep(th) {
      background-color: #f2f6fc;
      color: #606266;
      font-weight: 500;
      font-size: 13px;
    }

    :deep(td) {
      padding: 6px 0;
    }

    :deep(.el-button--small) {
      padding: 4px 8px;
      font-size: 12px;
      min-width: 50px;
    }
  }
}
</style>
