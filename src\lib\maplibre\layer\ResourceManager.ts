/**
 * @fileoverview 资源管理器
 * @description 统一管理标绘系统中的所有资源，提供生命周期管理和内存优化
 * <AUTHOR>
 * @version 1.0.0
 */

import { DrawModule } from '@/views/maplibre/plotPanel/DrawModule';
import { PlotService } from '@/views/maplibre/plotPanel/PlotService';
import { PlotDataManager } from './PlotDataManager';
import { LayerManager } from './LayerManager';
import type { DrawManager } from '@/lib/maplibre/draw/DrawManager';
import { Subscription } from 'rxjs';

/**
 * @description 资源类型枚举
 */
export enum ResourceType {
  DRAW_MODULE = 'draw_module',
  PLOT_SERVICE = 'plot_service',
  DATA_MANAGER = 'data_manager',
  LAYER_MANAGER = 'layer_manager',
  DRAW_MANAGER = 'draw_manager'
}

/**
 * @description 资源状态
 */
export enum ResourceStatus {
  UNINITIALIZED = 'uninitialized',
  INITIALIZING = 'initializing',
  READY = 'ready',
  ERROR = 'error',
  DESTROYED = 'destroyed'
}

/**
 * @description 资源信息
 */
interface ResourceInfo {
  type: ResourceType;
  instance: any;
  status: ResourceStatus;
  createdAt: number;
  lastUsed: number;
  dependencies: ResourceType[];
  cleanupMethods: string[];
}

/**
 * @description 内存监控信息
 */
interface MemoryInfo {
  usedResources: number;
  totalResources: number;
  memoryUsage: number;
  lastCleanup: number;
}

/**
 * @description 资源管理器配置
 */
interface ResourceManagerConfig {
  autoCleanup: boolean;
  cleanupInterval: number;
  maxIdleTime: number;
  enableLogging: boolean;
}

/**
 * @class ResourceManager
 * @description 统一资源管理器，负责标绘系统所有资源的生命周期管理
 */
export class ResourceManager {
  private static instance: ResourceManager | null = null;
  
  // 资源注册表
  private resources: Map<ResourceType, ResourceInfo> = new Map();
  
  // 配置
  private config: ResourceManagerConfig = {
    autoCleanup: true,
    cleanupInterval: 30000, // 30秒
    maxIdleTime: 300000,    // 5分钟
    enableLogging: true
  };

  // 清理定时器
  private cleanupTimer: number | null = null;
  
  // 订阅管理
  private subscriptions: Map<string, Subscription> = new Map();

  /**
   * @description 获取单例实例
   */
  static getInstance(): ResourceManager {
    if (!ResourceManager.instance) {
      ResourceManager.instance = new ResourceManager();
    }
    return ResourceManager.instance;
  }

  /**
   * @description 私有构造函数
   */
  private constructor() {
    this.log('资源管理器初始化');
    this.setupCleanupTimer();
    this.setupResourceDependencies();
  }

  /**
   * @description 设置资源依赖关系
   */
  private setupResourceDependencies(): void {
    // 定义资源依赖关系
    const dependencies = new Map<ResourceType, ResourceType[]>([
      [ResourceType.DRAW_MODULE, [ResourceType.DATA_MANAGER, ResourceType.DRAW_MANAGER]],
      [ResourceType.PLOT_SERVICE, [ResourceType.DRAW_MODULE]],
      [ResourceType.LAYER_MANAGER, []],
      [ResourceType.DATA_MANAGER, []],
      [ResourceType.DRAW_MANAGER, [ResourceType.LAYER_MANAGER]]
    ]);

    dependencies.forEach((deps, type) => {
      if (this.resources.has(type)) {
        const resource = this.resources.get(type)!;
        resource.dependencies = deps;
      }
    });
  }

  /**
   * @description 设置清理定时器
   */
  private setupCleanupTimer(): void {
    if (this.config.autoCleanup) {
      this.cleanupTimer = window.setInterval(() => {
        this.performCleanup();
      }, this.config.cleanupInterval);
    }
  }

  /**
   * @description 注册资源
   * @param type - 资源类型
   * @param instance - 资源实例
   * @param cleanupMethods - 清理方法名称数组
   */
  registerResource(
    type: ResourceType, 
    instance: any, 
    cleanupMethods: string[] = []
  ): void {
    const resourceInfo: ResourceInfo = {
      type,
      instance,
      status: ResourceStatus.READY,
      createdAt: Date.now(),
      lastUsed: Date.now(),
      dependencies: [],
      cleanupMethods
    };

    this.resources.set(type, resourceInfo);
    this.log(`资源已注册: ${type}`);
  }

  /**
   * @description 获取资源
   * @param type - 资源类型
   * @returns 资源实例或null
   */
  getResource<T = any>(type: ResourceType): T | null {
    const resourceInfo = this.resources.get(type);
    if (resourceInfo && resourceInfo.status === ResourceStatus.READY) {
      resourceInfo.lastUsed = Date.now();
      return resourceInfo.instance as T;
    }
    return null;
  }

  /**
   * @description 检查资源是否存在且可用
   * @param type - 资源类型
   * @returns 是否可用
   */
  isResourceAvailable(type: ResourceType): boolean {
    const resourceInfo = this.resources.get(type);
    return resourceInfo?.status === ResourceStatus.READY;
  }

  /**
   * @description 更新资源状态
   * @param type - 资源类型
   * @param status - 新状态
   */
  updateResourceStatus(type: ResourceType, status: ResourceStatus): void {
    const resourceInfo = this.resources.get(type);
    if (resourceInfo) {
      resourceInfo.status = status;
      this.log(`资源状态更新: ${type} -> ${status}`);
    }
  }

  /**
   * @description 销毁指定资源
   * @param type - 资源类型
   */
  destroyResource(type: ResourceType): void {
    const resourceInfo = this.resources.get(type);
    if (!resourceInfo) {
      this.log(`资源不存在: ${type}`);
      return;
    }

    try {
      this.log(`开始销毁资源: ${type}`);

      // 执行清理方法
      for (const methodName of resourceInfo.cleanupMethods) {
        if (typeof resourceInfo.instance[methodName] === 'function') {
          try {
            resourceInfo.instance[methodName]();
            this.log(`执行清理方法: ${type}.${methodName}()`);
          } catch (error) {
            console.error(`清理方法执行失败: ${type}.${methodName}()`, error);
          }
        }
      }

      // 清理订阅
      this.cleanupSubscriptionsForResource(type);

      // 更新状态
      resourceInfo.status = ResourceStatus.DESTROYED;
      
      // 从注册表中移除
      this.resources.delete(type);
      
      this.log(`资源销毁完成: ${type}`);
    } catch (error) {
      console.error(`销毁资源失败: ${type}`, error);
      resourceInfo.status = ResourceStatus.ERROR;
    }
  }

  /**
   * @description 销毁所有资源
   */
  destroyAllResources(): void {
    this.log('开始销毁所有资源');

    // 按依赖关系反向销毁
    const destroyOrder = this.calculateDestroyOrder();
    
    for (const type of destroyOrder) {
      this.destroyResource(type);
    }

    // 清理定时器
    if (this.cleanupTimer) {
      window.clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // 清理所有订阅
    this.cleanupAllSubscriptions();

    this.log('所有资源销毁完成');
  }

  /**
   * @description 计算销毁顺序（依赖资源后销毁）
   */
  private calculateDestroyOrder(): ResourceType[] {
    const order: ResourceType[] = [];
    const visited = new Set<ResourceType>();

    const visit = (type: ResourceType) => {
      if (visited.has(type)) return;
      visited.add(type);

      const resourceInfo = this.resources.get(type);
      if (resourceInfo) {
        // 先访问依赖的资源
        for (const dep of resourceInfo.dependencies) {
          if (this.resources.has(dep)) {
            visit(dep);
          }
        }
        // 再访问当前资源
        order.push(type);
      }
    };

    // 访问所有资源
    for (const type of this.resources.keys()) {
      visit(type);
    }

    // 反转顺序，使依赖资源后销毁
    return order.reverse();
  }

  /**
   * @description 执行定期清理
   */
  private performCleanup(): void {
    const now = Date.now();
    const resourcesToCleanup: ResourceType[] = [];

    // 查找长时间未使用的资源
    this.resources.forEach((resourceInfo, type) => {
      const idleTime = now - resourceInfo.lastUsed;
      if (idleTime > this.config.maxIdleTime) {
        resourcesToCleanup.push(type);
      }
    });

    if (resourcesToCleanup.length > 0) {
      this.log(`执行定期清理，清理 ${resourcesToCleanup.length} 个空闲资源`);
      resourcesToCleanup.forEach(type => {
        this.destroyResource(type);
      });
    }
  }

  /**
   * @description 添加订阅
   * @param key - 订阅标识
   * @param subscription - 订阅对象
   */
  addSubscription(key: string, subscription: Subscription): void {
    // 如果已存在同名订阅，先取消旧的
    if (this.subscriptions.has(key)) {
      this.subscriptions.get(key)?.unsubscribe();
    }
    
    this.subscriptions.set(key, subscription);
    this.log(`订阅已添加: ${key}`);
  }

  /**
   * @description 移除订阅
   * @param key - 订阅标识
   */
  removeSubscription(key: string): void {
    const subscription = this.subscriptions.get(key);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(key);
      this.log(`订阅已移除: ${key}`);
    }
  }

  /**
   * @description 清理指定资源的订阅
   * @param type - 资源类型
   */
  private cleanupSubscriptionsForResource(type: ResourceType): void {
    const prefix = `${type}_`;
    const keysToRemove: string[] = [];
    
    this.subscriptions.forEach((subscription, key) => {
      if (key.startsWith(prefix)) {
        subscription.unsubscribe();
        keysToRemove.push(key);
      }
    });

    keysToRemove.forEach(key => {
      this.subscriptions.delete(key);
    });

    if (keysToRemove.length > 0) {
      this.log(`清理资源订阅: ${type}, 共 ${keysToRemove.length} 个`);
    }
  }

  /**
   * @description 清理所有订阅
   */
  private cleanupAllSubscriptions(): void {
    this.subscriptions.forEach((subscription, key) => {
      subscription.unsubscribe();
    });
    this.subscriptions.clear();
    this.log('所有订阅已清理');
  }

  /**
   * @description 获取内存使用情况
   */
  getMemoryInfo(): MemoryInfo {
    return {
      usedResources: this.resources.size,
      totalResources: Object.keys(ResourceType).length,
      memoryUsage: this.calculateMemoryUsage(),
      lastCleanup: Date.now()
    };
  }

  /**
   * @description 计算内存使用量（估算）
   */
  private calculateMemoryUsage(): number {
    // 简单的内存使用量估算
    let usage = 0;
    this.resources.forEach((resourceInfo) => {
      usage += JSON.stringify(resourceInfo).length;
    });
    return usage;
  }

  /**
   * @description 获取资源列表
   */
  getResourceList(): Array<{
    type: ResourceType;
    status: ResourceStatus;
    createdAt: number;
    lastUsed: number;
    idleTime: number;
  }> {
    const now = Date.now();
    const list: Array<{
      type: ResourceType;
      status: ResourceStatus;
      createdAt: number;
      lastUsed: number;
      idleTime: number;
    }> = [];

    this.resources.forEach((resourceInfo, type) => {
      list.push({
        type,
        status: resourceInfo.status,
        createdAt: resourceInfo.createdAt,
        lastUsed: resourceInfo.lastUsed,
        idleTime: now - resourceInfo.lastUsed
      });
    });

    return list.sort((a, b) => b.lastUsed - a.lastUsed);
  }

  /**
   * @description 强制垃圾回收
   */
  forceGarbageCollection(): void {
    this.log('执行强制垃圾回收');
    
    // 清理无效资源
    const invalidResources: ResourceType[] = [];
    this.resources.forEach((resourceInfo, type) => {
      if (resourceInfo.status === ResourceStatus.ERROR || 
          resourceInfo.status === ResourceStatus.DESTROYED) {
        invalidResources.push(type);
      }
    });

    invalidResources.forEach(type => {
      this.resources.delete(type);
    });

    // 清理失效订阅
    this.subscriptions.forEach((subscription, key) => {
      if (subscription.closed) {
        this.subscriptions.delete(key);
      }
    });

    this.log(`垃圾回收完成，清理了 ${invalidResources.length} 个无效资源`);
  }

  /**
   * @description 更新配置
   * @param newConfig - 新配置
   */
  updateConfig(newConfig: Partial<ResourceManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重新设置清理定时器
    if (this.cleanupTimer) {
      window.clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.setupCleanupTimer();
    
    this.log('资源管理器配置已更新');
  }

  /**
   * @description 日志输出
   * @param message - 日志信息
   */
  private log(message: string): void {
    if (this.config.enableLogging) {
      console.log(`[ResourceManager] ${message}`);
    }
  }

  /**
   * @description 销毁资源管理器
   */
  destroy(): void {
    this.destroyAllResources();
    ResourceManager.instance = null;
    this.log('资源管理器已销毁');
  }

  /**
   * @description 检测绘制工具状态冲突
   * @param requestingComponent - 请求使用绘制工具的组件名
   * @returns 冲突检测结果
   */
  detectDrawToolConflict(requestingComponent: string): {
    hasConflict: boolean;
    conflictsWith: string[];
    canResolve: boolean;
  } {
    const result = {
      hasConflict: false,
      conflictsWith: [] as string[],
      canResolve: true
    };

    try {
      // 检查当前是否有其他组件在使用绘制工具
      const drawManager = this.getResource(ResourceType.DRAW_MANAGER);
      if (!drawManager) {
        this.log('DrawManager不存在，无冲突');
        return result;
      }

      // 检查绘制工具状态
      const isEnabled = drawManager.isEnabled();
      const currentState = drawManager.getState();

      if (isEnabled && (currentState.isDrawing || currentState.isEditing)) {
        result.hasConflict = true;
        
        // 检查哪些组件可能在使用
        if (currentState.isDrawing) {
          result.conflictsWith.push('绘制工具');
        }
        if (currentState.isEditing) {
          result.conflictsWith.push('编辑工具');
        }

        this.log(`绘制工具冲突检测: ${requestingComponent} 请求使用，但冲突与: ${result.conflictsWith.join(', ')}`);
      }

      return result;
    } catch (error) {
      this.log(`绘制工具冲突检测失败: ${error}`);
      result.canResolve = false;
      return result;
    }
  }

  /**
   * @description 解决绘制工具冲突
   * @param requestingComponent - 请求组件名
   * @param strategy - 解决策略
   * @returns 是否成功解决
   */
  resolveDrawToolConflict(
    requestingComponent: string, 
    strategy: 'force' | 'wait' | 'cancel' = 'force'
  ): boolean {
    try {
      this.log(`开始解决绘制工具冲突: ${requestingComponent}, 策略: ${strategy}`);

      const drawManager = this.getResource(ResourceType.DRAW_MANAGER);
      if (!drawManager) {
        this.log('DrawManager不存在，无需解决冲突');
        return true;
      }

      switch (strategy) {
        case 'force':
          return this.forceResolveDrawToolConflict(drawManager, requestingComponent);
        
        case 'wait':
          // 等待策略（可以扩展为异步等待）
          this.log('等待策略暂未实现，使用强制策略');
          return this.forceResolveDrawToolConflict(drawManager, requestingComponent);
        
        case 'cancel':
          this.log('取消策略：不解决冲突，返回失败');
          return false;
        
        default:
          this.log('未知的冲突解决策略，使用强制策略');
          return this.forceResolveDrawToolConflict(drawManager, requestingComponent);
      }
    } catch (error) {
      this.log(`解决绘制工具冲突失败: ${error}`);
      return false;
    }
  }

  /**
   * @description 强制解决绘制工具冲突
   * @param drawManager - 绘制管理器实例
   * @param requestingComponent - 请求组件名
   * @returns 是否成功
   * @private
   */
  private forceResolveDrawToolConflict(drawManager: any, requestingComponent: string): boolean {
    try {
      this.log(`强制解决绘制工具冲突: ${requestingComponent}`);

      // 1. 清除当前绘制状态
      if (typeof drawManager.clearAllFeatures === 'function') {
        drawManager.clearAllFeatures();
        this.log('已清除绘制工具中的所有要素');
      }

      // 2. 重置到选择模式
      if (typeof drawManager.setMode === 'function') {
        drawManager.setMode('select');
        this.log('已重置绘制工具到选择模式');
      }

      // 3. 检查绘制工具状态
      if (typeof drawManager.resolveStateConflict === 'function') {
        const resolved = drawManager.resolveStateConflict(requestingComponent);
        if (!resolved) {
          this.log('绘制工具状态冲突解决失败');
          return false;
        }
      }

      // 4. 验证冲突是否已解决
      const state = drawManager.getState();
      const isStillConflicted = state.isDrawing || state.isEditing;
      
      if (isStillConflicted) {
        this.log('冲突解决后状态检查失败，尝试强制重新初始化');
        
        if (typeof drawManager.forceReinitialize === 'function') {
          const success = drawManager.forceReinitialize(false);
          this.log(`强制重新初始化结果: ${success}`);
          return success;
        }
      }

      this.log('绘制工具冲突解决成功');
      return true;
    } catch (error) {
      this.log(`强制解决绘制工具冲突失败: ${error}`);
      return false;
    }
  }

  /**
   * @description 注册绘制工具使用者
   * @param componentName - 组件名称
   * @param usage - 使用类型
   */
  registerDrawToolUsage(componentName: string, usage: 'drawing' | 'editing' | 'idle'): void {
    try {
      this.log(`注册绘制工具使用: ${componentName} - ${usage}`);
      
      // 可以在这里记录使用历史，用于冲突分析
      const key = `draw_tool_usage_${componentName}`;
      this.addSubscription(key, {
        unsubscribe: () => {
          this.log(`清理绘制工具使用记录: ${componentName}`);
        },
        closed: false
      } as any);
      
    } catch (error) {
      this.log(`注册绘制工具使用失败: ${error}`);
    }
  }

  /**
   * @description 取消绘制工具使用注册
   * @param componentName - 组件名称
   */
  unregisterDrawToolUsage(componentName: string): void {
    try {
      this.log(`取消绘制工具使用注册: ${componentName}`);
      
      const key = `draw_tool_usage_${componentName}`;
      this.removeSubscription(key);
      
    } catch (error) {
      this.log(`取消绘制工具使用注册失败: ${error}`);
    }
  }

  /**
   * @description 获取绘制工具状态摘要
   */
  getDrawToolStatusSummary(): {
    isAvailable: boolean;
    currentState: any;
    registeredUsers: string[];
    conflictCount: number;
  } {
    try {
      const drawManager = this.getResource(ResourceType.DRAW_MANAGER);
      
      if (!drawManager) {
        return {
          isAvailable: false,
          currentState: null,
          registeredUsers: [],
          conflictCount: 0
        };
      }

      const state = drawManager.getState();
      const registeredUsers: string[] = [];
      
      // 统计注册的使用者
      this.subscriptions.forEach((_, key) => {
        if (key.startsWith('draw_tool_usage_')) {
          registeredUsers.push(key.replace('draw_tool_usage_', ''));
        }
      });

      return {
        isAvailable: drawManager.isEnabled(),
        currentState: state,
        registeredUsers,
        conflictCount: state.isDrawing || state.isEditing ? 1 : 0
      };
    } catch (error) {
      this.log(`获取绘制工具状态摘要失败: ${error}`);
      return {
        isAvailable: false,
        currentState: null,
        registeredUsers: [],
        conflictCount: -1
      };
    }
  }
} 