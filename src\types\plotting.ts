/**
 * @fileoverview 标绘信息相关类型定义
 * @description 定义标绘功能相关的数据结构和类型接口
 * <AUTHOR>
 * @version 1.0.0
 */

export enum EngineType {
  /** Cesium */
  CESIUM = 'Cesium',
  /** Maplibre */
  MAPLIBRE = 'Maplibre', 
}

/**
 * @enum PlottingType
 * @description 标绘类型枚举
 */
export enum PlottingType {
  /** 点 */
  POINT = 'Point',
  /** 线 */
  POLYLINE = 'Polyline', 
  /** 圆 */
  POLYGON = 'Polygon',
  /** 矩形 */
  RECTANGLE = 'Rectangle'
}

/**
 * @interface PlottingInfo
 * @description 标绘信息基础接口
 */
export interface PlottingInfo {
  /** 主键ID */
  id?: number;
  /** 租户ID */
  tenantId?: string;
  /** 标绘名称 */
  name: string;
  /** 标绘类型 */
  type: PlottingType | string;
  /** 引擎类型 */
  engineType: EngineType;
  /** 图形JSON数据 */
  graphicsJson: string;
  /** 标绘描述信息 */
  remark: string;
  /** 创建人ID */
  createBy?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人ID */
  updateBy?: number;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * @interface PlottingInfoDto
 * @description 标绘信息数据传输对象（用于新增和修改操作）
 */
export interface PlottingInfoDto {
  /** 主键ID（修改时必需） */
  id: number;
  /** 租户ID */
  tenantId?: string;
  /** 标绘名称 */
  name: string;
  /** 标绘类型 */
  type: PlottingType | string;
  /** 引擎类型 */
  engineType: EngineType;
  /** 图形JSON数据 */
  graphicsJson: string;
  /** 标绘描述信息 */
  remark: string;
  /** 创建人ID */
  createBy?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人ID */
  updateBy?: number;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * @interface PlottingInfoVo
 * @description 标绘信息视图对象（用于查询响应）
 */
export interface PlottingInfoVo {
  /** 主键ID */
  id: number;
  /** 租户ID */
  tenantId?: string;
  /** 标绘名称 */
  name: string;
  /** 标绘类型 */
  type: PlottingType | string;
  /** 引擎类型 */
  engineType: EngineType;
  /** 图形JSON数据 */
  graphicsJson: string;
  /** 标绘描述信息 */
  remark: string;
  /** 创建人ID */
  createBy?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人ID */
  updateBy?: number;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * @interface PlottingPageQuery
 * @description 标绘信息分页查询参数
 */
export interface PlottingPageQuery {
  /** 分页大小 */
  pageSize?: number;
  /** 当前页数 */
  pageNum?: number;
  /** 排序列 */
  orderByColumn?: string;
  /** 排序方向（desc或asc） */
  isAsc?: string;
  /** 首页记录数 */
  firstNum?: number;
  /** 标绘名称（模糊查询） */
  name?: string;
  /** 标绘类型 */
  type?: PlottingType | string;
  /** 租户ID */
  tenantId?: string;
  /** 引擎类型 */
  engineType?: EngineType;
}

/**
 * @interface PlottingPageVo
 * @description 标绘信息分页查询结果
 */
export interface PlottingPageVo {
  /** 主键ID */
  id: number;
  /** 租户ID */
  tenantId?: string;
  /** 标绘名称 */
  name: string;
  /** 标绘类型 */
  type: PlottingType | string;
  /** 引擎类型 */
  engineType: EngineType;
  /** 图形JSON数据 */
  graphicsJson: string;
  /** 创建人ID */
  createBy?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  updateTime?: string;
  remark: string;
}

/**
 * @interface PlottingFormData
 * @description 标绘表单数据接口（用于前端表单绑定）
 */
export interface PlottingFormData {
  /** 标绘名称 */
  name: string;
  /** 标绘类型 */
  type: PlottingType;
  /** 引擎类型 */
  engineType: EngineType;
  /** 图形JSON数据 */
  graphicsJson: string;
  /** 标绘描述信息 */
  remark: string;
}

/**
 * @interface PlottingCreateRequest
 * @description 标绘创建请求接口
 */
export interface PlottingCreateRequest {
  /** 标绘名称 */
  name: string;
  /** 标绘类型 */
  type: PlottingType | string;
  /** 引擎类型 */
  engineType: EngineType;
  /** 图形JSON数据 */
  graphicsJson: string;
  /** 标绘描述信息 */
  remark: string;
}

/**
 * @interface PlottingUpdateRequest
 * @description 标绘更新请求接口
 */
export interface PlottingUpdateRequest extends PlottingCreateRequest {
  /** 主键ID */
  id: number;
} 