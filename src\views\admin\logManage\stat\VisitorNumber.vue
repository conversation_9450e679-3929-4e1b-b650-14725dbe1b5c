<template>
  <div>
    <div class="h-30 card px-4">
      <div>
        <img :src="getImages('admin/1.png')" alt="" srcset="" />
      </div>
      <div class="ml-10 color-#2C3037 w-40">今日访客数量：</div>
      <div class="font-size-5 color-#1966FF">{{ dataInfo?.today }}人</div>
    </div>
    <div class="h-30 card red px-4 mt-4">
      <div>
        <img :src="getImages('admin/2.png')" alt="" srcset="" />
      </div>
      <div class="ml-10 color-#2C3037 w-40">总访客数量：</div>
      <div class="font-size-5 color-#FF6719">{{ dataInfo?.total }}人</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getImages } from "@/utils/getImages";
import { visitorLog } from "@/api/log";
const dataInfo = ref<any>();
const getList = async () => {
  const result = await visitorLog();
  dataInfo.value = result.data;
};
onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.card {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border-radius: 8px;
  background: linear-gradient(
    45deg,
    rgba(244, 247, 255, 1),
    rgba(220, 232, 255, 1) 100%
  );
}
.red {
  background: linear-gradient(
    45deg,
    rgba(255, 246, 244, 1),
    rgba(255, 243, 241, 1) 100%
  );
}
</style>
