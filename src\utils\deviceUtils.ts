/**
 * 设备数据处理工具函数
 * @description 提供设备数据获取、字段提取、统计等通用方法
 */

import { getMockIotDevices, getDevicePage, getWarnRecordByDeviceType, getDeviceWarnRecord, queryDevice, getDevice, queryDeviceField, deviceSummaryPage } from '@/api/query';
import type { IotDeviceField, IotDeviceResponse } from '@/views/maplibre/pages/query/SpatialQuery.vue';

/**
 * 获取设备字段值
 * @param device 设备数据
 * @param fieldKey 字段键
 * @returns 字段值
 */
export const getFieldValue = (device: IotDeviceField[], fieldKey: string): string => {
  const field = device.find(f => f.fieldKey === fieldKey);
  return field ? field.fieldValues : '';
};

/** 判断 在线状态, 可用状态 */
export const isOnlineStatusAvailable = (fieldKey:string, fieldValue: string): string => {
  if(fieldKey === 'online_status'){
    if(fieldValue == '1') return '在线';
    return '离线';
  } else if(fieldKey === 'status'){
    if(fieldValue == '1') return '可用';
    return '禁用';
  }
  return ''
};

/**
 * 获取设备字段显示值（处理字典映射）
 * @param device 设备数据
 * @param fieldKey 字段键
 * @returns 字段显示值
 */
export const getFieldDisplayValue = (device: IotDeviceField[], fieldKey: string): string => {
  const field = device.find(f => f.fieldKey === fieldKey);
  if (!field) return '';
  
  if (field.displayForm === 'select' && field.dictMap && field.fieldValues) {
    return field.dictMap[field.fieldValues] || field.fieldValues;
  }
  return field.fieldValues;
};

/**
 * 统计设备数据按设备大类分组
 * @param devices 设备数据数组
 * @returns 统计结果
 */
export const statisticsDeviceByType = (devices: IotDeviceField[][]): ISeriesData[] => {
  const typeCount: Record<string, number> = {};
  
  devices.forEach(device => {
    const deviceType = getFieldDisplayValue(device, 'first_device_type_code');
    if (deviceType) {
      typeCount[deviceType] = (typeCount[deviceType] || 0) + 1;
    }
  });
  
  return Object.entries(typeCount).map(([name, value]) => ({
    name,
    value
  }));
};

/**
 * 统计设备数据按设备小类分组
 * @param devices 设备数据数组
 * @returns 统计结果
 */
// export const statisticsDeviceBySubType = (devices: IotDeviceField[][]): ISeriesData[] => {
//   const typeCount: Record<string, number> = {};
  
//   devices.forEach(device => {
//     const deviceType = getFieldDisplayValue(device, 'second_device_type_code');
//     if (deviceType) {
//       typeCount[deviceType] = (typeCount[deviceType] || 0) + 1;
//     }
//   });
  
//   return Object.entries(typeCount).map(([name, value]) => ({
//     name,
//     value
//   }));
// };

/**
 * 获取设备数据
 * @returns Promise<IotDeviceField[][]> 设备数据数组
 */
export const fetchDeviceData = async (): Promise<IotDeviceField[][]> => {
  try {
    const response = await getMockIotDevices();
    const deviceResult = response.data as IotDeviceResponse;

    if (deviceResult.code === '200') {
      return deviceResult.data;
    } else {
      console.error('获取设备数据失败:', deviceResult);
      return [];
    }
  } catch (err) {
    console.error('获取物联网设备数据出错:', err);
    throw err;
  }
};

/**
 * 根据字段对设备数据进行分组
 * @param devices 设备数据数组
 * @param fieldKey 分组字段键
 * @returns 分组后的数据
 */
export const groupDevicesByField = (devices: IotDeviceField[][], fieldKey: string) => {
  const grouped: Record<string, IotDeviceField[][]> = {};
  
  devices.forEach(device => {
    const fieldValue = getFieldDisplayValue(device, fieldKey);
    if (fieldValue) {
      if (!grouped[fieldValue]) {
        grouped[fieldValue] = [];
      }
      grouped[fieldValue].push(device);
    }
  });
  
  return grouped;
};

/**
 * 根据统计方式计算数据
 * @param groupedData 分组后的数据 (来自 iot_spatial_device.json 或 iot_fields.json)
 * @param method 统计方式 ('计数' | '求和')
 * @param fieldKey fieldKey
 * @param statisticsField 统计字段 (求和时使用)
 * @returns 统计结果
 */
export const calculateStatistics = (
  groupedData: Record<string, IotDeviceField[][]>,
  method: string,
  fieldKey: string,
  statisticsField?: string,
) => {
  const result: { name: string; value: number }[] = [];
  const specialKeys = ['online_status','status']
  Object.entries(groupedData).forEach(([groupName, devices]) => {
    let value = 0;

    switch (method) {
      case '计数':
        // 计数：统计每个分组中的设备数量
        value = devices.length;
        break;
      case '求和':
        // 求和：对指定字段的数值进行求和
        if (statisticsField) {
          value = devices.reduce((sum, device) => {
            const fieldValue = getFieldValue(device, statisticsField);
            const numValue = parseFloat(fieldValue) || 0;
            return sum + numValue;
          }, 0);
        } else {
          // 如果没有指定统计字段，默认使用计数
          value = devices.length;
        }
        break;
      default:
        value = devices.length;
    }

    console.log(isOnlineStatusAvailable(fieldKey,groupName))
    result.push({ name: specialKeys.includes(fieldKey) ? isOnlineStatusAvailable(fieldKey,groupName) : groupName, value });
  });

  return result.sort((a, b) => b.value - a.value); // 按值降序排列
};




/**
 * 获取设备小类的 Map 集合数据
 * @param devices 设备数据数组
 * @returns Map<string, string> 设备小类的键值对映射 (code -> name)
 */
export const getDeviceSubTypeMap = (devices: IotDeviceField[][]): Map<string, string> => {
  const subTypeMap = new Map<string, string>();

  devices.forEach(device => {
    const field = device.find(f => f.fieldKey === 'second_device_type_code');
    if (field && field.dictMap && field.fieldValues) {
      // 使用字段值作为 key，字典映射值作为 value
      const code = field.fieldValues;
      const name = field.dictMap[field.fieldValues] || field.fieldValues;
      subTypeMap.set(code, name);
    }
  });

  return subTypeMap;
};

/**
 * 获取设备大类的 Map 集合数据
 * @param devices 设备数据数组
 * @returns Map<string, string> 设备大类的键值对映射 (code -> name)
 */
export const getDeviceTypeMap = (devices: IotDeviceField[][]): Map<string, string> => {
  const typeMap = new Map<string, string>();

  devices.forEach(device => {
    const field = device.find(f => f.fieldKey === 'first_device_type_code');
    if (field && field.dictMap && field.fieldValues) {
      // 使用字段值作为 key，字典映射值作为 value
      const code = field.fieldValues;
      const name = field.dictMap[field.fieldValues] || field.fieldValues;
      typeMap.set(code, name);
    }
  });

  return typeMap;
};

/**
 * 获取设备小类选项数据（用于下拉框等）
 * @param devices 设备数据数组
 * @returns Array<{value: string, name: string}> 设备小类选项数组
 */
export const getDeviceSubTypeOptions = (devices: IotDeviceField[][]): Array<{value: string, name: string}> => {
  const subTypeMap = getDeviceSubTypeMap(devices);
  return Array.from(subTypeMap.entries()).map(([value, name]) => ({
    value,
    name
  }));
};

/**
 * 获取设备大类选项数据（用于下拉框等）
 * @param devices 设备数据数组
 * @returns Array<{value: string, name: string}> 设备大类选项数组
 */
export const getDeviceTypeOptions = (devices: IotDeviceField[][]): Array<{value: string, name: string}> => {
  const typeMap = getDeviceTypeMap(devices);
  return Array.from(typeMap.entries()).map(([value, name]) => ({
    value,
    name
  }));
};

/**
 * 设备类型响应数据接口
 */
interface DeviceTypeResponse {
  code: string;
  name: string;
  parentCode: string;
  sort: number;
  status: number;
}

interface DeviceReq {
  size?: number;
  current?: number;
  code?: string;
  parentCode?: string;
  name?: string;
  status?: number;
}

interface DeviceSummaryReq {
  size?: number;
  current?: number;
  firstDeviceTypeCode?: string;
  secondDeviceTypeCode?: string;
  name?: string;
  status?: number;
  siteCode?: string;
  onlineStatus?: any;
}

interface DeviceWarnRecordReq {
  size?: number;
  current?: number;
  levelCode?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 获取设备类型(小类)请求方法
 * @param parentCode 父级设备类型编码，默认为 'iot_device_type'
 * @returns Promise<DeviceTypeResponse[]> 设备类型列表
 */
export const fetchDeviceSubTypes = async (params:DeviceReq): Promise<DeviceTypeResponse[]> => {
  try {
    const response = await getWarnRecordByDeviceType(params)

    if (response.code == 200 && response.data) {
      return response.data;
    } else {
      console.error('获取设备类型失败:', response);
      return [];
    }
  } catch (error) {
    console.error('获取设备类型请求失败:', error);
    throw error;
  }
};

// 空间查询
interface SpatialQuery {
  coordinate?: string;
  fieldGroup?: any;
}
/**
 * 处理查询结果的坐标数据
 * @param result 查询结果对象，包含geometry.coordinates
 * @returns 处理后的坐标字符串，格式为经纬度用分号分隔
 */
export const processQueryCoordinates = (result: any): string => {
  let targetCoords;
  try {
    const coordinates = result?.geometry.coordinates[0]
    if (coordinates) {
      // 保留两位小数
      // const coors = coordinates.map(([lng, lat]: number[]) => {
      //   return [Number(lng.toFixed(2)), Number(lat.toFixed(2))];
      // })
      targetCoords = coordinates.join(';')
    } else {
      targetCoords = ''
    }
  } catch (error) {
    console.error('获取坐标错误:', error)
    targetCoords = ''
    throw new Error('获取坐标错误')
  }
  return targetCoords;
};

/** 空间查询 */
export const spatialQuery = async (data: SpatialQuery) => {
  try {
    const response = await queryDevice(data);
    if (response.code == 200 && response.data) {
      return response.data;
    } else {
      ElMessage.error('空间查询设备失败');
      console.error('空间查询设备失败:', response);
      return [];
    }
  } catch (error) {
    console.error('空间查询设备请求失败:', error);
    throw error;
  }
};


/** 设备汇总分页 */
export const fetchDeviceSummary = async (params:DeviceSummaryReq): Promise<any[]> => {
  try {
    const response = await getDevicePage(params)
    if (response.code == 200 && response.data) {
      return response.data;
    } else {
      console.error('获取设备类型失败:', response);
      return [];
    }
  } catch (error) {
    console.error('获取设备类型请求失败:', error);
    throw error;
  }
};
/**
 * 获取设备预警记录
 * @param params 查询条件
 * @returns Promise<any[]> 设备预警记录列表
 */
export const fetchDeviceAlarmRecord = async (params:DeviceWarnRecordReq): Promise<any[]> => {
  try {
    const response = await getDeviceWarnRecord(params)
    return response.data;
  } catch (error) {
    console.error('获取设备类型请求失败:', error);
    throw error;
  }
};

/**
 * 根据设备类型筛选设备数据
 * @param devices 设备数据数组
 * @param deviceTypeCode 设备类型编码（second_device_type_code的值）
 * @returns 筛选后的设备数据数组
 */
export const filterDevicesByType = (devices: IotDeviceField[][], deviceTypeCode: string): IotDeviceField[][] => {
  if (!deviceTypeCode || deviceTypeCode === '') {
    return devices; // 如果没有选择设备类型，返回所有数据
  }

  return devices.filter(device => {
    const typeField = device.find(f => f.fieldKey === 'second_device_type_code');
    return typeField && typeField.fieldValues === deviceTypeCode;
  });
};

/**
 * 设备详情
 * @param code 唯一编码
 * @returns
 */
export const getDeivceDetailByCode = async (code: string): Promise<Record<string, any>> => {
  try {
    const response = await getDevice(code)
    if (response.code == 200 && response.data) {
      return response.data;
    } else {
      console.error('获取设备详细数据失败:', response);
      return [];
    }
  } catch (error) {
    console.error('获取设备详细数据失败:', error);
    throw error;
  }
};

export interface FieldsParams {
  current?: number;
  size?: number;
  firstDeviceTypeCode?: string;
  secondDeviceTypeCode?: string;
}
export interface ResFields {
  fieldKey: string;
  fieldName: string;
}

/** 查询设备字段 */
export const fetchDeviceFields = async (params: FieldsParams): Promise<ResFields[]> => {
  try {
    const response = await queryDeviceField(params)
    if (response.code == 200 && response.data) {
      return response.data;
    } else {
      console.error('获取设备字段失败:', response);
      return [];
    }
  } catch (error) {
    console.error('获取设备字段失败:', error);
    throw error;
  }
};

/** 设备汇总 */
export const fetchDeviceSummaryPage = async (params: any): Promise<any[]> => {
  try {
    const response = await deviceSummaryPage(params)
    if (response.code == 200 && response.data) {
      return response.data;
    } else {
      console.error('获取设备汇总失败:', response);
      return [];
    }
  } catch (error) {
    console.error('获取设备汇总失败:', error);
    throw error;
  }

};

/**
 * 针对分类字段为设备小类进行求和统计(统计图)
 * @param data 分组后的设备数据 (iot_fields.json格式)
 * @returns 统计结果数组，包含设备类型名称和数量
 */
export const calculateDeviceTypeStatistics = (data: Record<string, IotDeviceField[][]>, fieldKey = 'second_device_type_code'): { name: string; value: number }[] => {
  const deviceStats: { name: string; value: number }[] = [];

  Object.entries(data).forEach(([groupName, devices]) => {
    if (Array.isArray(devices) && devices.length > 0) {
      // 获取设备类型显示名称
      let displayName = groupName;

      // 尝试从第一个设备的dictMap中获取显示名称
      const firstDevice = devices[0];
      if (Array.isArray(firstDevice)) {
        const typeField = firstDevice.find(field =>
          field.fieldKey === fieldKey
        );
        if (typeField && typeField.dictMap && typeField.dictMap[groupName]) {
          displayName = typeField.dictMap[groupName];
        } else {
          displayName = groupName;
        }
      }

      deviceStats.push({
        name: displayName,
        value: devices.length
      });
    }
  });
  return deviceStats;
};