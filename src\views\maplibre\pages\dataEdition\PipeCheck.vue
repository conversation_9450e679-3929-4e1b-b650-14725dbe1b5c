<template>
  <page-card :close-icon="false" class="tabulate-sta" title="管网数据检查">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleHangingLineCheck"
        :loading="isCheckingHangingLine"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/7.png')" alt="" srcset="" />
        </i>
        悬挂线检查
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="handleOldDeviceCheck"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/8.png')" alt="" srcset="" /> </i
        >老化设备检查</el-button
      >
      <el-button
        type="primary"
        class="primary-btn"
        @click="openRepeatAttrPanel"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/9.png')" alt="" srcset="" /> </i
        >重复属性检查</el-button
      >
      <el-button
        type="primary"
        class="primary-btn"
        @click="openRepeatPointPanel"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/10.png')" alt="" srcset="" /> </i
        >重复点检查
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="openRepeatLinePanel"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/11.png')" alt="" srcset="" /> </i
        >重复线检查
      </el-button>
      <el-button
        type="primary"
        class="primary-btn"
        @click="handlePipePropertyCorrectionCheck"
      >
        <i class="pr-2">
          <img :src="getImages('basemap/12.png')" alt="" srcset="" /> </i
        >纠错审核</el-button
      >
    </div>
  </page-card>

  <!-- 悬挂线检查面板 -->
  <HangingLineCheckPanel
    v-if="showHangingLinePanel"
    @close="handleHangingLineClose"
  />

  <!-- 纠错审核面板 -->
  <PipePropertyCorrectionPanel
    v-if="showPipePropertyCorrectionPanel"
    @close="handlePipePropertyCorrectionClose"
  />

  <!-- 重复属性检查面板 -->
  <RepeatAttrCheckPanel
    v-if="showRepeatAttrPanel"
    @close="showRepeatAttrPanel = false"
  />

  <!-- 重复点检查面板 -->
  <RepeatPointCheckPanel
    v-if="showRepeatPointPanel"
    @close="showRepeatPointPanel = false"
  />

  <!-- 重复线检查面板 -->
  <RepeatLineCheckPanel
    v-if="showRepeatLinePanel"
    @close="showRepeatLinePanel = false"
  />

  <!-- 老化设备检查面板 -->
  <OldDeviceCheckPanel
    v-if="showOldDevicePanel"
    @close="showOldDevicePanel = false"
  />
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { Tools, Search } from "@element-plus/icons-vue";
import PageCard from "@/components/PageCard.vue";
import HangingLineCheckPanel from "./HangingLineCheckPanel.vue";
import PipePropertyCorrectionPanel from "./PipePropertyCorrectionPanel.vue";
import RepeatAttrCheckPanel from "./RepeatAttrCheckPanel.vue";
import RepeatPointCheckPanel from "./RepeatPointCheckPanel.vue";
import RepeatLineCheckPanel from "./RepeatLineCheckPanel.vue";
import OldDeviceCheckPanel from "./OldDeviceCheckPanel.vue";
import { getImages } from "@/utils/getImages";
// ============ 响应式状态 ============

/** 悬挂线检查相关状态 */
const showHangingLinePanel = ref(false);
const isCheckingHangingLine = ref(false);

/** 纠错审核相关状态 */
const showPipePropertyCorrectionPanel = ref(false);

/** 重复属性检查相关状态 */
const showRepeatAttrPanel = ref(false);

/** 重复点检查相关状态 */
const showRepeatPointPanel = ref(false);

/** 重复线检查相关状态 */
const showRepeatLinePanel = ref(false);

/** 老化设备检查相关状态 */
const showOldDevicePanel = ref(false);

// ============ 事件处理方法 ============

/**
 * @function handlePipePropertyCorrectionCheck
 * @description 处理纠错审核
 */
const handlePipePropertyCorrectionCheck = (): void => {
  showRepeatAttrPanel.value = false;
  showRepeatPointPanel.value = false;
  showRepeatLinePanel.value = false;
  showHangingLinePanel.value = false;
  showOldDevicePanel.value = false;
  showPipePropertyCorrectionPanel.value = true;
};

/**
 * @function handlePipePropertyCorrectionClose
 * @description 处理纠错审核面板关闭
 */
const handlePipePropertyCorrectionClose = (): void => {
  showPipePropertyCorrectionPanel.value = false;
};

/**
 * @function handleHangingLineCheck
 * @description 处理悬挂线检查
 */
const handleHangingLineCheck = (): void => {
  try {
    if (showHangingLinePanel.value) {
      // 如果面板已打开，直接关闭
      showHangingLinePanel.value = false;
      return;
    }
    showRepeatAttrPanel.value = false;
    showRepeatPointPanel.value = false;
    showRepeatLinePanel.value = false;
    showOldDevicePanel.value = false;
    showPipePropertyCorrectionPanel.value = false;
    // 打开悬挂线检查面板
    showHangingLinePanel.value = true;
    // ElMessage.info("正在启动悬挂线检查功能...");
  } catch (error) {
    console.error("启动悬挂线检查失败:", error);
    ElMessage.error(
      `启动悬挂线检查失败: ${
        error instanceof Error ? error.message : "未知错误"
      }`
    );
  }
};

/**
 * @function handleHangingLineClose
 * @description 处理悬挂线检查面板关闭
 */
const handleHangingLineClose = (): void => {
  try {
    showHangingLinePanel.value = false;
    console.log("悬挂线检查面板已关闭");
  } catch (error) {
    console.error("关闭悬挂线检查面板失败:", error);
    ElMessage.error("关闭面板失败");
  }
};

/**
 * @function handleOldDeviceCheck
 * @description 处理老化设备检查
 */
const handleOldDeviceCheck = (): void => {
  console.log("老化设备检查");

  showRepeatAttrPanel.value = false;
  showRepeatPointPanel.value = false;
  showRepeatLinePanel.value = false;
  showHangingLinePanel.value = false;
  showPipePropertyCorrectionPanel.value = false;

  showOldDevicePanel.value = true;
  // ElMessage.info("正在启动老化设备检查功能...");
};

/**
 * @function openRepeatAttrPanel
 * @description 打开重复属性检查面板
 */
const openRepeatAttrPanel = (): void => {
  // 关闭其他检查面板，确保同时只有一个面板打开
  showRepeatPointPanel.value = false;
  showRepeatLinePanel.value = false;
  showHangingLinePanel.value = false;
  showPipePropertyCorrectionPanel.value = false;
  showOldDevicePanel.value = false;
  showRepeatAttrPanel.value = true;
  // ElMessage.info("正在启动重复属性检查功能...");
};

/**
 * @function openRepeatPointPanel
 * @description 打开重复点检查面板
 */
const openRepeatPointPanel = () => {
  // 关闭其他检查面板
  showRepeatAttrPanel.value = false;
  showRepeatLinePanel.value = false;
  showHangingLinePanel.value = false;
  showPipePropertyCorrectionPanel.value = false;
  showOldDevicePanel.value = false;
  showRepeatPointPanel.value = true;
};

/**
 * @function openRepeatLinePanel
 * @description 打开重复线检查面板
 */
const openRepeatLinePanel = () => {
  // 关闭其他检查面板
  showRepeatAttrPanel.value = false;
  showRepeatPointPanel.value = false;
  showHangingLinePanel.value = false;
  showPipePropertyCorrectionPanel.value = false;
  showOldDevicePanel.value = false;
  showRepeatLinePanel.value = true;
};
</script>

<style scoped lang="scss">
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  // width: 550px;
  min-height: 120px;
  z-index: 1000;
}

.toolbar {
  margin-bottom: 16px;
}
</style>
