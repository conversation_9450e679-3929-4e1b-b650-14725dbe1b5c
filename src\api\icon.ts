import hRequest from '@/utils/http';
import type { DataType } from '@/utils/http/types';

// 分页
export const iconPage = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/business/icon/info/page',
    params
  });
};
// 新增
export const addIcon = (data: any) => {
  return hRequest.post<DataType>({
    url: "/business/icon/info",
    data: data
  });
};
// 修改
export const editIcon = (data: any) => {
  return hRequest.put<DataType>({
    url: "/business/icon/info",
    data: data
  });
};
// 详情
export const detailsIcon = (id: any) => {
  return hRequest.get<DataType>({
    url: `/business/icon/info/${id}`,
  });
};
// 删除
export const deleteIcon = (id: any) => {
  return hRequest.delete<DataType>({
    url: `/business/icon/info/${id}`,
  });
};