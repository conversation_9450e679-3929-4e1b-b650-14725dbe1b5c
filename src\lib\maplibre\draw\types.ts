/**
 * @fileoverview MapLibre 绘制工具类型定义
 * @description 定义绘制工具相关的接口、类型和枚举
 * <AUTHOR>
 * @version 1.0.0
 */

import type { GeoJSONStoreFeatures } from 'terra-draw';

/**
 * @description 绘制模式枚举
 */
export enum DrawMode {
  /** 选择模式 */
  SELECT = 'select',
  /** 点绘制模式 */
  POINT = 'point',
  /** 线段绘制模式 */
  LINESTRING = 'linestring',
  /** 多边形绘制模式 */
  POLYGON = 'polygon',
  /** 矩形绘制模式 */
  RECTANGLE = 'rectangle',
  /** 圆形绘制模式 */
  CIRCLE = 'circle',
  /** 自由绘制模式 */
  FREEHAND = 'freehand'
}

/**
 * @description 绘制工具配置接口
 */
export interface DrawConfig {
  /** 是否启用绘制工具 */
  enabled?: boolean;
  /** 默认绘制模式 */
  defaultMode?: DrawMode;
  /** 是否显示控制按钮 */
  showControls?: boolean;
  /** 控制按钮位置 */
  controlPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  /** 样式配置 */
  styles?: DrawStyleConfig;
  /** 编辑配置 */
  editing?: DrawEditConfig;
}

/**
 * @description 绘制样式配置接口
 */
export interface DrawStyleConfig {
  /** 点样式 */
  point?: {
    color?: string;
    outlineColor?: string;
    outlineWidth?: number;
    radius?: number;
  };
  /** 线样式 */
  linestring?: {
    color?: string;
    width?: number;
    opacity?: number;
  };
  /** 多边形样式 */
  polygon?: {
    fillColor?: string;
    fillOpacity?: number;
    outlineColor?: string;
    outlineWidth?: number;
  };
  /** 选中状态样式 */
  selected?: {
    color?: string;
    outlineColor?: string;
    outlineWidth?: number;
  };
}

/**
 * @description 编辑配置接口
 */
export interface DrawEditConfig {
  /** 是否允许拖拽移动 */
  allowDrag?: boolean;
  /** 是否允许调整大小 */
  allowResize?: boolean;
  /** 是否允许旋转 */
  allowRotate?: boolean;
  /** 是否允许在线上添加点 */
  allowAddPointOnLine?: boolean;
  /** 是否允许删除点 */
  allowDeletePoint?: boolean;
  /** 最小点数限制 */
  minPoints?: number;
  /** 最大点数限制 */
  maxPoints?: number;
}

/**
 * @description 绘制事件类型
 */
export enum DrawEventType {
  /** 开始绘制 */
  DRAW_START = 'draw.start',
  /** 绘制完成 */
  DRAW_FINISH = 'draw.finish',
  /** 绘制取消 */
  DRAW_CANCEL = 'draw.cancel',
  /** 要素选中 */
  FEATURE_SELECT = 'feature.select',
  /** 要素取消选中 */
  FEATURE_DESELECT = 'feature.deselect',
  /** 要素更新 */
  FEATURE_UPDATE = 'feature.update',
  /** 要素删除 */
  FEATURE_DELETE = 'feature.delete',
  /** 模式改变 */
  MODE_CHANGE = 'mode.change'
}

/**
 * @description 绘制事件数据接口
 */
export interface DrawEventData {
  /** 事件类型 */
  type: DrawEventType;
  /** 相关要素 */
  features?: GeoJSONStoreFeatures;
  /** 当前模式 */
  mode?: DrawMode;
  /** 额外数据 */
  data?: any;
}

/**
 * @description 绘制事件回调函数类型
 */
export type DrawEventCallback = (event: DrawEventData) => void;

/**
 * @description 绘制工具状态接口
 */
export interface DrawState {
  /** 当前模式 */
  currentMode: DrawMode;
  /** 是否正在绘制 */
  isDrawing: boolean;
  /** 是否正在编辑 */
  isEditing: boolean;
  /** 选中的要素ID列表 */
  selectedFeatureIds: string[];
  /** 总要素数量 */
  featureCount: number;
}

/**
 * @description 几何体验证结果接口
 */
export interface GeometryValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息 */
  errors?: string[];
  /** 警告信息 */
  warnings?: string[];
}

/**
 * @description 绘制工具选项接口
 */
export interface DrawToolOptions {
  /** 工具配置 */
  config?: DrawConfig;
  /** 事件回调 */
  onEvent?: DrawEventCallback;
  /** 自定义样式 */
  customStyles?: Record<string, any>;
} 