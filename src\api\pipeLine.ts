/**
 * @fileoverview 管线API接口
 * @description 管线数据的增删改查操作接口
 * <AUTHOR>
 * @version 1.0.0
 */

import hRequest from '@/utils/http'
import type { DataType } from '@/utils/http/types'

/**
 * @description 管线业务对象
 */
export interface GsLnDto {
  /** 主键ID */
  gid?: number;
  /** 管类 */
  gl?: string;
  /** 起点编号 */
  qdbh?: string;
  /** 终点编号 */
  zdbh?: string;
  /** 起点X坐标 */
  qdx?: number;
  /** 起点Y坐标 */
  qdy?: number;
  /** 起点高程 */
  qdgc?: number;
  /** 起点埋深 */
  qdms?: number;
  /** 终点X坐标 */
  zdx?: number;
  /** 终点Y坐标 */
  zdy?: number;
  /** 终点高程 */
  zdgc?: number;
  /** 终点埋深 */
  zdms?: number;
  /** 材质 */
  cz?: string;
  /** 埋深方式 */
  msfs?: string;
  /** 管径 */
  gj?: number;
  /** 所在道路 */
  szdl?: string;
  /** 管段长度 */
  gdcd?: number;
  /** 管线编码 */
  gxbm?: string;
  /** GeoJSON格式的几何数据 */
  geojson?: string;
}

/**
 * @description 管线视图对象
 */
export interface GsLnVo extends GsLnDto {
  /** 管线长度（计算值） */
  length?: number;
}

/**
 * @description 管线分页视图对象
 */
export interface GsLnPageVo extends Omit<GsLnDto, 'geojson' | 'length'> {
  // 分页视图对象继承业务对象，但不包含geojson和length字段
}

/**
 * @description 管线分页查询参数接口
 */
export interface PipeLinePageQuery {
  /** 分页大小 */
  pageSize?: number;
  /** 当前页数 */
  pageNum?: number;
  /** 排序列 */
  orderByColumn?: string;
  /** 排序方向（desc或asc） */
  isAsc?: string;
  /** 首页记录数 */
  firstNum?: number;
}

/**
 * @description 悬挂线分页查询参数接口
 */
export interface HangingLinePageQuery {
  /** 分页大小 */
  pageSize?: number;
  /** 当前页数 */
  pageNum?: number;
  /** 排序列 */
  orderByColumn?: string;
  /** 排序方向（desc或asc） */
  isAsc?: string;
  /** 首页记录数 */
  firstNum?: number;
}

/**
 * @description 统计数据返回结果
 */
export interface StatisticsData {
  [key: string]: number;
}

/**
 * @description 管线分页返回结果
 */
export interface PageInfoGsLnPageVo {
  /** 总记录数 */
  total: number;
  /** 数据列表 */
  list: GsLnPageVo[];
  /** 分页信息 */
  pageNum: number;
  pageSize: number;
  size: number;
  startRow: number;
  endRow: number;
  pages: number;
  prePage: number;
  nextPage: number;
  isFirstPage: boolean;
  isLastPage: boolean;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  navigatePages: number;
  navigatepageNums: number[];
  navigateFirstPage: number;
  navigateLastPage: number;
}

/**
 * @description 悬挂线分页返回结果
 */
export interface PageInfoGsLnVo {
  /** 总记录数 */
  totalCount: number;
  /** 数据列表 */
  list: GsLnVo[];
  /** 分页信息 */
  pageNum: number;
  pageSize: number;
  size: number;
  startRow: number;
  endRow: number;
  pages: number;
  prePage: number;
  nextPage: number;
  isFirstPage: boolean;
  isLastPage: boolean;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  navigatePages: number;
  navigatepageNums: number[];
  navigateFirstPage: number;
  navigateLastPage: number;
}

/**
 * @description 通用返回结果包装
 */
export interface ApiResult<T> {
  /** 状态码 */
  code: number;
  /** 返回消息 */
  msg: string;
  /** 返回数据 */
  data: T;
}

/**
 * @description 管线列表查询
 * @returns Promise<ApiResult<GsLnVo[]>> 返回管线列表数据
 */
export const pipeLineList = () => {
  return hRequest.get<ApiResult<GsLnVo[]>>({
    url: '/analyse/gs/ln/list'
  })
}

/**
 * @description 管线分页查询
 * @param params 分页查询参数
 * @returns Promise<ApiResult<PageInfoGsLnPageVo>> 返回分页查询结果
 */
export const pipeLinePage = (params: PipeLinePageQuery) => {
  return hRequest.get<ApiResult<PageInfoGsLnPageVo>>({
    url: '/analyse/gs/ln/page',
    params
  })
}

/**
 * @description 新增管线
 * @param data 管线数据对象
 * @returns Promise<ApiResult<number>> 返回新增结果，数据为生成的ID
 */
export const pipeLineAdd = (data: GsLnDto) => {
  return hRequest.post<ApiResult<number>>({
    url: '/analyse/gs/ln',
    data
  })
}

/**
 * @description 修改管线
 * @param data 管线数据对象
 * @returns Promise<ApiResult<number>> 返回修改结果，数据为影响的记录数
 */
export const pipeLineEdit = (data: GsLnDto) => {
  return hRequest.put<ApiResult<number>>({
    url: '/analyse/gs/ln',
    data
  })
}
/**
 * @description 点打断线
 * @param data 管线数据对象
 * @returns Promise<ApiResult<number>> 返回修改结果，数据为影响的记录数
 */
export const pipeLineBreak = (data: any) => {
  return hRequest.put<ApiResult<number>>({
    url: '/analyse/gs/ln/editByGd',
    data
  })
}

/**
 * @description 管线详情查询
 * @param gid 管线GID
 * @returns Promise<ApiResult<GsLnVo>> 返回管线详情数据
 */
export const pipeLineDetail = (gid: number | string) => {
  return hRequest.get<ApiResult<GsLnVo>>({
    url: `/analyse/gs/ln/${gid}`,
  })
}

/**
 * @description 删除管线
 * @param gid 管线GID
 * @returns Promise<ApiResult<void>> 返回删除结果
 */
export const pipeLineDelete = (gid: number | string) => {
  return hRequest.delete<ApiResult<void>>({
    url: `/analyse/gs/ln/${gid}`,
  })
}

/**
 * @description 通过编码查询管线
 * @param bm 管线编码
 * @returns Promise<ApiResult<GsLnDto>> 返回管线数据
 */
export const pipeLineQueryByCode = (bm: string) => {
  return hRequest.get<ApiResult<GsLnDto>>({
    url: '/analyse/gs/ln',
    params: { bm }
  })
}

/**
 * @description 查询悬挂线（分页查询）
 * @param params 分页查询参数
 * @returns Promise<ApiResult<PageInfoGsLnVo>> 返回悬挂线分页查询结果
 */
export const queryHangingLines = (params?: HangingLinePageQuery) => {
  return hRequest.get<ApiResult<PageInfoGsLnVo>>({
    url: '/analyse/gs/ln/hang/lines',
    params
  })
}

/**
 * @description 根据管径查询列表
 * @returns Promise<ApiResult<StatisticsData>> 返回管径列表数据
 */
export const queryPipeDiameterList = () => {
  return hRequest.get<ApiResult<any>>({
    url: '/analyse/gs/ln/gj/list'
  })
}

/**
 * @description 按管材查询列表
 * @returns Promise<ApiResult<StatisticsData>> 返回管材列表数据
 */
export const queryPipeMaterialList = () => {
  return hRequest.get<ApiResult<any>>({
    url: '/analyse/gs/ln/cz/list'
  })
}
/**
 * @description 导出dxf文件
 * @returns Promise<any> 返回dxf文件二进制流
 */
export const exportDxf = () => {
  return hRequest.get<any>({
    url: '/analyse/gs/ln/dxf/export',
    responseType: 'blob'
  })
}