@use "./maplibre.scss";
@use "./cesium.scss";
@use "./admin.scss";
/**
 * @fileoverview 全局样式文件
 * @description 定义应用程序的全局CSS样式，包括基础重置和布局设置
 * <AUTHOR>
 * @version 1.0.0
 */

/* 全局基础样式重置 */
html,
body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
  font-family: SansCN-Regular;
}

/* 应用根容器样式 */
#app {
  height: 100%;
}

.layer-btn {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  // column-gap: 4px;
  // width: 40px;
  height: 36px;
  padding-right: 2px;
  // padding-right: 10px;
  background: rgba(22, 67, 126, 0.70);
  border-radius: 2px;
  box-shadow: 0px 0px 12px 0px #00b1ff inset;
  border: 1px solid #aae8ff;
  margin-left: auto;
  cursor: pointer;
}

.expand-button {
  width: 36px;
  overflow: hidden;
  transition: width 0.3s ease;
}

.expand-button .icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  margin-left: 10px;
}

.expand-button .text {
  opacity: 0;
  white-space: nowrap;
  transition: opacity 0.2s ease;

}

.expand-button:hover {
  width: 100%;
  padding-left: 20px;
}

.expand-button:hover .text {
  opacity: 1;
}

// 字体
@font-face {
  font-family: DouYu;
  font-display: fallback;
  src: local('DOUYUZHUIGUANGTI'), url('../font/DOUYUZHUIGUANGTI.ttf');
}

@font-face {
  font-family: SansCN-Regular;
  font-style: normal;
  font-display: swap;
  /* 使用swap策略提高性能 fallback/optional */
  src: local('思源黑体 CN Regular'),
    url('@/assets/font/SourceHanSansCN-Regular.otf') format('opentype');
}

@font-face {
  font-family: SansCN-Medium;
  font-style: normal;
  font-display: swap;
  /* 使用swap策略提高性能 fallback/optional */
  src: local('思源黑体 CN Regular'),
    url('@/assets/font/SourceHanSansCN-Medium.otf') format('opentype');
}

@font-face {
  font-family: SansCN-Bold;
  font-style: normal;
  font-display: swap;
  /* 使用swap策略提高性能 fallback/optional */
  src: local('思源黑体 CN Regular'),
    url('@/assets/font/SourceHanSansCN-Bold.otf') format('opentype');
}

@font-face {
  font-family: YousheBiaoTiHei;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  /* 使用swap策略提高性能 fallback/optional */
  src: local('优设标题黑'), url('@/assets/font/YouSheBiaoTiHei-2.ttf') format('truetype');
}

.font-douyu {
  font-family: Douyu;
}

.primary-btn.el-button {
  height: 36px;
  background: #1966FF;
  border-radius: 4px;
}

.danger-btn.el-button {
  height: 36px;
  background: #FF7373;
  border-radius: 4px;
}

.clear-btn.el-button {

  height: 36px;
  background: #A3B7DE;
  border-radius: 4px;
  color: #fff;

  &:hover {
    background: rgba(#A3B7DE, 0.75);
    color: #fff;
    border-color: #bac9e6;
  }
}

.clear-btn.el-button.is-disabled,
.el-button.is-disabled:hover {
  background: rgba(#A3B7DE, 0.75) !important;
  color: #fff !important;
  border-color: #bac9e6 !important;
}

.icon-wrapper {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-item-label {
  margin-left: 15px;
}

/** 折叠后的popper */
.menu-popper {
  .el-menu--popup {
    height: 200px;
    min-width: 150px;
    overflow-y: auto;
    scrollbar-color: rgba(177, 185, 197, 1) transparent;
    scrollbar-width: thin;
  }
  --el-menu-hover-bg-color: #f6f6f9;
}