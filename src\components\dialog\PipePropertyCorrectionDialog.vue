<!--
 * @Description: 管点属性纠错弹框组件
 * @Date: 2024-01-15
 * @Author: AI Assistant
 * @LastEditTime: 2024-01-15
 -->
<template>
  <CustomCard
    :title="'管点属性纠错'"
    :width="'400px'"
    :left="'600px'"
    :top="'200px'"
    @closeHandler="handleClose"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading
        element-loading-text="正在提交纠错信息..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      />
    </div>

    <!-- 纠错表单 -->
    <div class="correction-form-section">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="correction-form"
      >
        <!-- 错误类型 -->
        <el-form-item label="错误类型:" prop="type" required>
          <el-select
            v-model="formData.type"
            placeholder="请选择错误类型"
            style="width: 100%"
          >
            <el-option label="属性错误" value="sx" />
            <el-option label="附属物错误" value="fsw" />
            <el-option label="高程信息错误" value="gc" />
            <el-option label="井深信息错误" value="js" />
            <el-option label="井盖规格错误" value="jggg" />
            <el-option label="井盖材质错误" value="jgcz" />
            <el-option label="所在道路错误" value="szdl" />
          </el-select>
        </el-form-item>
        <!-- 更新值 -->
        <el-form-item label="更新值:" prop="updateValue" required>
          <el-input v-model="formData.updateValue" placeholder="请输入更新值" />
        </el-form-item>
        <!-- 错误描述 -->
        <el-form-item label="错误描述:" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="5"
            placeholder="请描述发现的错误问题，例如：实际坐标与图上位置不符，偏移约50米；管点属性标注错误，应为三通而非直线点等..."
            maxlength="500"
            resize="none"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="handleClose" :disabled="loading"> 取消 </el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ loading ? "提交中..." : "提交纠错" }}
      </el-button>
    </div>
  </CustomCard>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElForm, ElMessage, ElMessageBox } from "element-plus";
import type { FormItemRule, FormRules } from "element-plus";

import CustomCard from "@/components/dialog/CustomCard.vue";
import { useDialogStore } from "@/stores/Dialogs";
import {
  submitPipeCorrection,
  PIPE_ERROR_TYPE_LABEL_MAP,
} from "@/api/pipeCorrection";

/**
 * @interface Props
 * @description 组件属性接口
 */
interface Props {
  /** 管点基本信息 */
  nodeInfo?: {
    gid?: number | string;
    gxddh?: string;
    gl?: string;
    sx?: string;
    fsw?: string;
    longitude?: number;
    latitude?: number;
    dmgc?: number;
    [key: string]: any;
  };
  /** DialogStore中的主要组件项 */
  mainItem?: {
    title?: string;
    uuid?: string;
    params?: any;
  };
}

const props = withDefaults(defineProps<Props>(), {
  nodeInfo: () => ({}),
  mainItem: () => ({}),
});

// 从mainItem.params中获取nodeInfo（DialogStore传递参数的方式）
const nodeInfo = computed(() => {
  return props.mainItem?.params?.nodeInfo || props.nodeInfo || {};
});

// 引用和状态
const formRef = ref<InstanceType<typeof ElForm>>();
const loading = ref(false);
const dialogStore = useDialogStore();

/**
 * @typedef CorrectionFormData
 * @property {string} type 错误类型
 * @property {string} remark 错误描述
 * @property {string} updateValue 更新值
 */
interface CorrectionFormData {
  type: string;
  remark: string;
  updateValue: string;
}

// 表单数据
const formData = reactive<CorrectionFormData>({
  type: "",
  remark: "",
  updateValue: "",
});

// 校验规则常量
const validateNumber = (rule: any, value: any, callback: any) => {
  if (value === "") {
    callback(new Error("请输入更新值"));
  } else if (isNaN(Number(value))) {
    callback(new Error("请输入有效的数字"));
  } else {
    callback();
  }
};

const formRules = computed((): FormRules => {
  const rules: FormRules = {
    type: [{ required: true, message: "请选择错误类型", trigger: "change" }],
    remark: [
      { max: 500, message: "错误描述不能超过500个字符", trigger: "blur" },
    ],
    updateValue: [],
  };
  if (formData.type === "gc" || formData.type === "js") {
    rules.updateValue = [
      { required: true, validator: validateNumber, trigger: "blur" },
    ];
  } else {
    rules.updateValue = [
      { required: true, message: "请输入更新值", trigger: "blur" },
    ];
  }
  return rules;
});

/**
 * @function handleSubmit
 * @description 提交纠错信息
 */
const handleSubmit = async (): Promise<void> => {
  if (!formRef.value) return;

  try {
    // 表单验证
    await formRef.value.validate();

    // 检查管点信息
    if (!nodeInfo.value.gid) {
      ElMessage.error("管点信息不完整，无法提交纠错");
      return;
    }

    // 确认提交
    await ElMessageBox.confirm(`确定要提交此纠错信息吗？`, "确认提交", {
      confirmButtonText: "确定提交",
      cancelButtonText: "取消",
      type: "warning",
    });

    loading.value = true;

    // 获取当前用户ID（这里需要根据实际的用户管理系统获取）
    const currentUserId = await getCurrentUserId();

    // 提交纠错信息
    const response = await submitPipeCorrection(
      Number(nodeInfo.value.gid),
      formData.type,
      formData.updateValue,
      buildFullRemark(),
      currentUserId
    );

    if (response.code === 200) {
      ElMessage.success("纠错信息提交成功，感谢您的反馈！");

      // 清空表单
      resetForm();

      // 关闭弹框
      handleClose();
    } else {
      throw new Error(response.msg || "提交失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("提交纠错信息失败:", error);
      ElMessage.error(
        `提交失败: ${error instanceof Error ? error.message : "未知错误"}`
      );
    }
  } finally {
    loading.value = false;
  }
};

/**
 * @function buildFullRemark
 * @description 构建完整的备注信息
 */
const buildFullRemark = (): string => {
  return formData.remark;
};

/**
 * @function getCurrentUserId
 * @description 获取当前用户ID
 * @todo 需要根据实际的用户管理系统实现
 */
const getCurrentUserId = async (): Promise<number> => {
  // 这里应该从用户Store或认证系统获取当前用户ID
  // 暂时返回一个默认值，实际项目中需要替换为真实的用户ID获取逻辑

  try {
    // 示例：从localStorage获取用户信息
    const userInfo = localStorage.getItem("userInfo");
    if (userInfo) {
      const user = JSON.parse(userInfo);
      return user.id || 1;
    }

    // 或者从用户Store获取
    // const userStore = useUserStore()
    // return userStore.currentUser?.id || 1

    return 1; // 默认用户ID
  } catch (error) {
    console.warn("获取用户ID失败，使用默认值:", error);
    return 1;
  }
};

/**
 * @function resetForm
 * @description 重置表单
 */
const resetForm = (): void => {
  formData.type = "";
  formData.remark = "";

  formRef.value?.clearValidate();
};

/**
 * @function handleClose
 * @description 关闭弹框
 */
const handleClose = (): void => {
  dialogStore.closeDialog("PipePropertyCorrectionDialog");
};

// 组件挂载时的初始化
onMounted(() => {
  console.log("属性纠错弹框已加载，管点信息:", nodeInfo.value);
});
</script>

<style lang="scss" scoped>
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}

.correction-form-section {
  margin-bottom: 20px;

  .correction-form {
    :deep(.el-form-item) {
      margin-bottom: 18px;

      .el-form-item__label {
        color: #606266;
        font-weight: 500;
      }

      .el-input__inner,
      .el-textarea__inner {
        border-radius: 4px;
        transition: border-color 0.2s;

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }

      .el-select {
        width: 100%;
      }
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;

  .el-button {
    min-width: 80px;
    padding: 10px 20px;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column-reverse;
    gap: 8px;

    .el-button {
      width: 100%;
    }
  }
}
</style>
