/**
 * @fileoverview 全屏管理工具类
 * @description 提供浏览器全屏功能的封装，处理兼容性和状态管理
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

/**
 * @description 全屏操作结果接口
 */
export interface FullScreenResult {
  success: boolean;
  isFullScreen: boolean;
  message: string;
  error?: string;
}

/**
 * @description 全屏管理器类
 * @class FullScreenManager
 */
export class FullScreenManager {
  private static instance: FullScreenManager | null = null;

  /**
   * @description 获取单例实例
   * @returns {FullScreenManager} 全屏管理器实例
   */
  public static getInstance(): FullScreenManager {
    if (!FullScreenManager.instance) {
      FullScreenManager.instance = new FullScreenManager();
    }
    return FullScreenManager.instance;
  }

  /**
   * @description 检查浏览器是否支持全屏API
   * @returns {boolean} 是否支持全屏
   */
  public isSupported(): boolean {
    return !!(
      document.documentElement.requestFullscreen ||
      (document.documentElement as any).webkitRequestFullscreen ||
      (document.documentElement as any).mozRequestFullScreen ||
      (document.documentElement as any).msRequestFullscreen
    );
  }

  /**
   * @description 检查当前是否处于全屏状态
   * @returns {boolean} 是否全屏
   */
  public isFullScreen(): boolean {
    return !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );
  }

  /**
   * @description 进入全屏模式
   * @returns {Promise<FullScreenResult>} 操作结果
   */
  public async enterFullScreen(): Promise<FullScreenResult> {
    try {
      if (!this.isSupported()) {
        return {
          success: false,
          isFullScreen: false,
          message: '当前浏览器不支持全屏功能',
          error: 'Fullscreen API not supported'
        };
      }

      if (this.isFullScreen()) {
        return {
          success: true,
          isFullScreen: true,
          message: '已处于全屏状态'
        };
      }

      const documentElement = document.documentElement;

      // 尝试使用标准API
      if (documentElement.requestFullscreen) {
        await documentElement.requestFullscreen();
      }
      // WebKit (Safari, 旧版Chrome)
      else if ((documentElement as any).webkitRequestFullscreen) {
        await (documentElement as any).webkitRequestFullscreen();
      }
      // Firefox
      else if ((documentElement as any).mozRequestFullScreen) {
        await (documentElement as any).mozRequestFullScreen();
      }
      // IE/Edge
      else if ((documentElement as any).msRequestFullscreen) {
        await (documentElement as any).msRequestFullscreen();
      }

      return {
        success: true,
        isFullScreen: true,
        message: '已进入全屏模式，按ESC键可退出'
      };

    } catch (error: any) {
      console.error('进入全屏失败:', error);
      return {
        success: false,
        isFullScreen: false,
        message: '进入全屏失败',
        error: error.message || '未知错误'
      };
    }
  }

  /**
   * @description 退出全屏模式
   * @returns {Promise<FullScreenResult>} 操作结果
   */
  public async exitFullScreen(): Promise<FullScreenResult> {
    try {
      if (!this.isSupported()) {
        return {
          success: false,
          isFullScreen: false,
          message: '当前浏览器不支持全屏功能',
          error: 'Fullscreen API not supported'
        };
      }

      if (!this.isFullScreen()) {
        return {
          success: true,
          isFullScreen: false,
          message: '已退出全屏状态'
        };
      }

      // 尝试使用标准API
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      }
      // WebKit (Safari, 旧版Chrome)
      else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen();
      }
      // Firefox
      else if ((document as any).mozCancelFullScreen) {
        await (document as any).mozCancelFullScreen();
      }
      // IE/Edge
      else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen();
      }

      return {
        success: true,
        isFullScreen: false,
        message: '已退出全屏模式'
      };

    } catch (error: any) {
      console.error('退出全屏失败:', error);
      return {
        success: false,
        isFullScreen: this.isFullScreen(),
        message: '退出全屏失败',
        error: error.message || '未知错误'
      };
    }
  }

  /**
   * @description 切换全屏状态
   * @returns {Promise<FullScreenResult>} 操作结果
   */
  public async toggleFullScreen(): Promise<FullScreenResult> {
    if (this.isFullScreen()) {
      return await this.exitFullScreen();
    } else {
      return await this.enterFullScreen();
    }
  }

  /**
   * @description 监听全屏状态变化
   * @param {Function} callback - 状态变化回调函数
   * @returns {Function} 取消监听的函数
   */
  public onFullScreenChange(callback: (isFullScreen: boolean) => void): () => void {
    const handleChange = () => {
      callback(this.isFullScreen());
    };

    // 添加多种事件监听器以确保兼容性
    document.addEventListener('fullscreenchange', handleChange);
    document.addEventListener('webkitfullscreenchange', handleChange);
    document.addEventListener('mozfullscreenchange', handleChange);
    document.addEventListener('MSFullscreenChange', handleChange);

    // 返回取消监听的函数
    return () => {
      document.removeEventListener('fullscreenchange', handleChange);
      document.removeEventListener('webkitfullscreenchange', handleChange);
      document.removeEventListener('mozfullscreenchange', handleChange);
      document.removeEventListener('MSFullscreenChange', handleChange);
    };
  }

  /**
   * @description 获取全屏状态信息
   * @returns {object} 全屏状态详细信息
   */
  public getFullScreenInfo(): {
    isSupported: boolean;
    isFullScreen: boolean;
    fullscreenElement: Element | null;
    supportedAPI: string;
  } {
         let supportedAPI = 'none';
     
     if (typeof document.documentElement.requestFullscreen === 'function') {
       supportedAPI = 'standard';
     } else if (typeof (document.documentElement as any).webkitRequestFullscreen === 'function') {
       supportedAPI = 'webkit';
     } else if (typeof (document.documentElement as any).mozRequestFullScreen === 'function') {
       supportedAPI = 'mozilla';
     } else if (typeof (document.documentElement as any).msRequestFullscreen === 'function') {
       supportedAPI = 'microsoft';
     }

    return {
      isSupported: this.isSupported(),
      isFullScreen: this.isFullScreen(),
      fullscreenElement: document.fullscreenElement || 
                       (document as any).webkitFullscreenElement || 
                       (document as any).mozFullScreenElement || 
                       (document as any).msFullscreenElement,
      supportedAPI
    };
  }
}

/**
 * @description 导出全屏管理器单例实例的便捷函数
 * @returns {FullScreenManager} 全屏管理器实例
 */
export const fullScreenManager = FullScreenManager.getInstance();

/**
 * @description 全屏切换的便捷函数
 * @returns {Promise<FullScreenResult>} 操作结果
 */
export const toggleFullScreen = async (): Promise<FullScreenResult> => {
  return await fullScreenManager.toggleFullScreen();
}; 