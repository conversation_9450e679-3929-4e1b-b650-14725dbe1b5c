<template>
  <custom-card
    :width="'596px'"
    @closeHandler="close"
    v-show="mainItem.show"
    :main-item="mainItem"
    :title="mainItem.title ?? (mainItem.title = '三维漫游')"
  >
    <el-select
      v-model="currRouteId"
      popper-class="custom-select-popper"
      @change="handleRouteSelect"
      placeholder="请选择路线"
    >
      <el-option
        v-for="item in routeList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
      </el-option>
    </el-select>
    <div class="mt-5 mb-7.5">
      <el-button class="btn" @click="handleRouteManage('add')"
        >新增路线</el-button
      >
      <el-button
        class="btn"
        @click="handleRouteManage('edit')"
        :disabled="!currRoute"
      >
        编辑路线
      </el-button>
      <el-button class="btn" @click="handleDeleteRoute" :disabled="!currRoute">
        删除路线
      </el-button>
      <el-button class="btn" @click="handleStartRoaming" :disabled="!currRoute">
        开始漫游
      </el-button>
      <el-button class="btn" @click="handleStopRoaming">停止漫游</el-button>
    </div>
    <div class="flex-items">
      <div class="label w-76px">视频录制：</div>
      <el-switch
        v-model="isVideoRecord"
        @change="handleVideoRecordToggle"
        :disabled="videoRecordService?.getState() === 'stopping'"
      />
      <span v-if="recordingStatus" :class="recordingStatusClass">{{
        recordingStatus
      }}</span>
    </div>
    <div
      v-if="isVideoRecord && recordingState === 'recording'"
      class="flex-items"
    >
      <div class="label">录制时长：</div>
      <div style="font-size: 14px; color: #409eff; font-weight: bold">
        {{ recordingDuration }}s
      </div>
    </div>
    <div class="flex-items">
      <div class="label">漫游进度：</div>
      <el-slider
        @input="handleProgressChange"
        :min="0"
        :max="100"
        :step="1"
        v-model="progress"
      />
    </div>
    <div class="flex-items">
      <div class="label">漫游速度：</div>
      <el-slider
        @input="handleSpeedChange"
        :min="1"
        :max="8"
        :step="1"
        v-model="speed"
      />
    </div>
  </custom-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { AppCesium } from "@/lib/cesium/AppCesium";
import { useDialogStore } from "@/stores/Dialogs";
import {
  RouteService,
  RouteEventManager,
  ROUTE_EVENTS,
  type RouteData,
} from "@/utils/routeService";
import { CesiumCanvasRecorder } from "@/utils/CesiumCanvasRecorder";
import {
  RecordState,
  type CanvasRecordConfig,
  type RecordCallbacks,
} from "@/types/recording";
import { routeInfoList, routeDetail, routeDelete } from "@/api/roam";
import type { Subscription } from "rxjs";
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});
const isVideoRecord = ref<boolean>(false);

// === 响应式数据定义 ===
const currRouteId = ref<string>("");
const currRoute = ref<RouteData | any>();
const routeList = ref<RouteData[]>([]);
const progress = ref<number>(20);
const speed = ref<number>(3);

// === 视频录制相关 ===
const videoRecordService = ref<CesiumCanvasRecorder | null>(null);
const recordingState = ref<RecordState>(RecordState.IDLE);
const isRoaming = ref<boolean>(false);
const recordingDuration = ref<number>(0);

// === 计算属性 ===
/**
 * @description 录制状态显示文本
 */
const recordingStatus = computed(() => {
  switch (recordingState.value) {
    case RecordState.READY:
      return "录制就绪";
    case RecordState.RECORDING:
      return "正在录制...";
    case RecordState.STOPPING:
      return "正在停止...";
    case RecordState.ERROR:
      return "录制错误";
    default:
      return "";
  }
});

/**
 * @description 录制状态样式类
 */
const recordingStatusClass = computed(() => {
  switch (recordingState.value) {
    case RecordState.READY:
      return "recording-ready";
    case RecordState.RECORDING:
      return "recording-active";
    case RecordState.STOPPING:
      return "recording-stopping";
    case RecordState.ERROR:
      return "recording-error";
    default:
      return "";
  }
});

const RoamingStore = useRoamingStore();
watch(() => RoamingStore.updateUid, () => {
  loadRouteList();
})

// === 生命周期管理 ===
onMounted(() => {
  initializeComponent();
  setupEventListeners();
  
});

onUnmounted(() => {
  cleanupEventListeners();
});

// === 初始化方法 ===
/**
 * @description 初始化组件
 */
const initializeComponent = async (): Promise<void> => {
  loadRouteList();
  await initializeVideoRecording();
};

/**
 * @description 初始化视频录制服务
 */
const initializeVideoRecording = async (): Promise<void> => {
  try {
    videoRecordService.value = CesiumCanvasRecorder.getInstance();

    // 设置录制回调
    const callbacks: RecordCallbacks = {
      onStart: () => {
        ElMessage.success("开始录制Cesium Canvas");
      },
      onStop: (blob: Blob, fileName: string) => {
        ElMessage.success(
          `录制完成: ${fileName}, 文件大小: ${(blob.size / 1024 / 1024).toFixed(
            2
          )}MB`
        );
      },
      onError: (error: string) => {
        ElMessage.error(`录制错误: ${error}`);
        isVideoRecord.value = false;
      },
      onStateChange: (state: RecordState) => {
        recordingState.value = state;
      },
      onProgress: (duration: number) => {
        recordingDuration.value = duration;
      },
    };

    videoRecordService.value.setCallbacks(callbacks);

    // 初始化录制服务
    const config: Partial<CanvasRecordConfig> = {
      frameRate: 30,
      videoBitsPerSecond: 8000000,
      fileName: "地图漫游录制",
    };

    const success = await videoRecordService.value.initialize(config);
    if (success) {
      console.log("Cesium Canvas录制器初始化成功");
      const canvasInfo = videoRecordService.value.getCanvasInfo();
      console.log("Canvas信息:", canvasInfo);
    } else {
      console.warn("Cesium Canvas录制器初始化失败");
    }
  } catch (error) {
    console.error("初始化Canvas录制失败:", error);
  }
};

/**
 * @description 设置事件监听器
 */
const setupEventListeners = (): void => {
  RouteEventManager.on(ROUTE_EVENTS.ROUTE_SAVED, handleRouteDataChanged);
  RouteEventManager.on(ROUTE_EVENTS.ROUTE_DELETED, handleRouteDataChanged);
  RouteEventManager.on(ROUTE_EVENTS.ROUTE_UPDATED, handleRouteDataChanged);
};

/**
 * @description 清理事件监听器
 */
const cleanupEventListeners = (): void => {
  RouteEventManager.off(ROUTE_EVENTS.ROUTE_SAVED, handleRouteDataChanged);
  RouteEventManager.off(ROUTE_EVENTS.ROUTE_DELETED, handleRouteDataChanged);
  RouteEventManager.off(ROUTE_EVENTS.ROUTE_UPDATED, handleRouteDataChanged);
};

// === 数据管理方法 ===
/**
 * @description 加载路线列表
 */
const loadRouteList = async () => {
  try {
    const routes = await routeInfoList();
    routeList.value = routes.data;

    // 验证当前选中的路线是否仍然存在
    validateCurrentRoute();

    // 如果没有选中路线且有可用路线，选择第一个
    selectDefaultRoute();

    console.log("已加载路线列表:", routeList.value);
  } catch (error) {
    console.error("加载路线列表失败:", error);
    ElMessage.error("加载路线列表失败");
    resetRouteSelection();
  }
};

/**
 * @description 验证当前选中的路线
 */
const validateCurrentRoute = (): void => {
  if (currRoute.value) {
    const exists = routeList.value.find(
      (route) => route.id === currRoute.value?.id
    );
    if (!exists) {
      resetRouteSelection();
    }
  }
};

/**
 * @description 选择默认路线
 */
const selectDefaultRoute = (): void => {
  if (!currRoute.value && routeList.value.length > 0) {
    setCurrentRoute(routeList.value[0]);
  }
};

/**
 * @description 重置路线选择
 */
const resetRouteSelection = (): void => {
  currRoute.value = undefined;
  currRouteId.value = "";
};

/**
 * @description 设置当前路线
 */
const setCurrentRoute = (route: RouteData): void => {
  currRoute.value = route;
  currRouteId.value = route.id;
};

/**
 * @description 路线数据变化处理
 */
const handleRouteDataChanged = (): void => {
  loadRouteList();
  RouteEventManager.emit(ROUTE_EVENTS.ROUTES_REFRESHED);
};

// === 事件处理方法 ===
/**
 * @description 处理路线选择
 */
const handleRouteSelect = (routeId: string): void => {
  const selectedRoute = routeList.value.find((route) => route.id === routeId);
  if (selectedRoute) {
    setCurrentRoute(selectedRoute);
    handleStopRoaming(); // 切换路线时停止当前漫游
    console.log("选择路线:", selectedRoute);
  }
};

/**
 * @description 关闭面板
 */
const close = async (): Promise<void> => {
  // 如果正在录制视频，停止录制
  if (videoRecordService.value?.isRecording()) {
    await stopVideoRecording();
  }

  // 停止漫游
  if (isRoaming.value) {
    await handleStopRoaming();
  }

  useDialogStore().closeDialog("RoamingPanel");
};

/**
 * @description 处理路线管理
 */
const handleRouteManage = async (type: "add" | "edit"): void => {
  const result = await routeDetail(currRouteId.value);
  if (result.code === 200) {
    currRoute.value = result.data;
    if (type === "edit") {
      if (!currRoute.value) {
        ElMessage.warning("请先选择要编辑的路线");
        return;
      }
    }
    //   // 验证路线是否仍然存在
    //   if (!RouteService.routeExists(currRoute.value.id)) {
    //     ElMessage.error("选中的路线不存在，请刷新路线列表");
    //     loadRouteList();
    //     return;
    //   }
    // }

    const routeParams =
      type === "edit"
        ? {
            routeId: currRoute.value!.id,
            name: currRoute.value!.name,
            value: currRoute.value!.value,
          }
        : {};

    useDialogStore().addDialog({
      name: type === "add" ? "新增路线" : "编辑路线",
      path: "RouteManagerPanel",
      type: "cesium",
      params: {
        type,
        ...routeParams,
      },
    });
  }
};

/**
 * @description 处理删除路线
 */
const handleDeleteRoute = async (): Promise<void> => {
  if (!currRoute.value) {
    ElMessage.warning("请先选择要删除的路线");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除路线 "${currRoute.value.name}" 吗？此操作不可撤销。`,
      "删除确认",
      {
        confirmButtonText: "确定删除",
        cancelButtonText: "取消",
        type: "warning",
      }
    ).then(async () => {
      routeDelete(currRoute.value.id)
        .then((res) => {
          if (res.code == 200) {
            ElMessage({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            loadRouteList();
          }
        })
        .catch((error) => {
          console.log(error);
        });
    });
    // const success = RouteService.deleteRoute(currRoute.value.id);
    // if (success) {
    //   ElMessage.success(`路线 "${currRoute.value.name}" 已删除`);
    //   console.log("已删除路线:", currRoute.value);

    //   // 触发删除事件
    //   RouteEventManager.emit(ROUTE_EVENTS.ROUTE_DELETED, currRoute.value.id);
    // } else {
    //   ElMessage.error("删除路线失败");
    // }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除路线失败:", error);
      ElMessage.error("删除路线失败");
    }
  }
};

// === 视频录制控制方法 ===
/**
 * @description 处理视频录制开关切换
 */
const handleVideoRecordToggle = (val: string | number | boolean): void => {
  const enabled = Boolean(val);
  if (!videoRecordService.value) {
    ElMessage.error("视频录制服务未初始化");
    isVideoRecord.value = false;
    return;
  }

  if (enabled) {
    console.log("🎥 视频录制已启用，将在漫游开始时自动开始录制");
    // 如果当前正在漫游，立即开始录制
    if (isRoaming.value) {
      startVideoRecording();
    }
  } else {
    console.log("🎥 视频录制已禁用");
    // 如果正在录制，停止录制
    if (videoRecordService.value.isRecording()) {
      stopVideoRecording();
    }
  }
};

/**
 * @description 开始视频录制
 */
const startVideoRecording = async (): Promise<void> => {
  if (!videoRecordService.value || !isVideoRecord.value) {
    return;
  }

  try {
    const success = await videoRecordService.value.startRecording();
    if (!success) {
      console.error("启动视频录制失败");
      isVideoRecord.value = false;
    }
  } catch (error) {
    console.error("启动视频录制失败:", error);
    ElMessage.error("启动视频录制失败");
    isVideoRecord.value = false;
  }
};

/**
 * @description 停止视频录制
 */
const stopVideoRecording = async (): Promise<void> => {
  if (!videoRecordService.value) {
    return;
  }

  try {
    await videoRecordService.value.stopRecording();
  } catch (error) {
    console.error("停止视频录制失败:", error);
  }
};

// === 漫游控制方法 ===
/**
 * @description 处理开始漫游
 */
const handleStartRoaming = async (): Promise<void> => {
  console.log(currRoute.value);
  const data = JSON.parse(currRoute.value.value as any);
  console.log(data, "12313");
  if (!currRoute.value) {
    ElMessage.warning("请先选择要漫游的路线");
    return;
  }

  // 验证路线数据
  if ((!data as any) || data.length < 2) {
    ElMessage.error("选中的路线数据无效或锚点不足");
    return;
  }

  try {
    // 启动漫游，传入完整的路线数据
    AppCesium.getInstance()
      .getRoamTool()
      .startRoaming(currRoute.value, (progressVal: number) => {
        progress.value = Math.floor(progressVal * 100);
      });

    isRoaming.value = true;
    ElMessage.success(`开始漫游路线: ${currRoute.value.name}`);
    console.log("开始漫游:", currRoute.value);

    // 如果视频录制开关开启，开始录制
    if (isVideoRecord.value) {
      await startVideoRecording();
    }
  } catch (error) {
    console.error("启动漫游失败:", error);
    ElMessage.error(`启动漫游失败: ${error}`);
    isRoaming.value = false;
  }
};

/**
 * @description 处理停止漫游
 */
const handleStopRoaming = async (): Promise<void> => {
  try {
    AppCesium.getInstance().getRoamTool().stopRoaming();
    isRoaming.value = false;
    console.log("已停止漫游");

    // 如果正在录制视频，停止录制
    if (videoRecordService.value?.isRecording()) {
      await stopVideoRecording();
    }
  } catch (error) {
    console.error("停止漫游失败:", error);
  }
};

/**
 * @description 处理漫游进度变化
 */
const handleProgressChange = (value: number | number[]): void => {
  if (!currRoute.value) return;

  const numValue = Array.isArray(value) ? value[0] : value;

  try {
    AppCesium.getInstance()
      .getRoamTool()
      .changeProgress(currRoute.value.id, numValue);
  } catch (error) {
    console.error("改变漫游进度失败:", error);
  }
};

/**
 * @description 处理漫游速度变化
 */
const handleSpeedChange = (value: number | number[]): void => {
  if (!currRoute.value) return;

  const numValue = Array.isArray(value) ? value[0] : value;

  try {
    AppCesium.getInstance()
      .getRoamTool()
      .changeSpeed(currRoute.value.id, numValue);
  } catch (error) {
    console.error("改变漫游速度失败:", error);
  }
};

// 暴露方法供外部调用
defineExpose({
  loadRouteList,
  handleRouteDataChanged,
});
</script>

<style lang="scss" scoped>
/* 录制状态样式 */
.recording-ready {
  color: #409eff;
  font-size: 12px;
  margin-left: 8px;
}

.recording-active {
  color: #f56c6c;
  font-size: 12px;
  margin-left: 8px;
  animation: blink 1s infinite;
}

.recording-stopping {
  color: #e6a23c;
  font-size: 12px;
  margin-left: 8px;
}

.recording-error {
  color: #f56c6c;
  font-size: 12px;
  margin-left: 8px;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0.3;
  }
}
.flex-items {
  display: flex;
  align-items: center;
}
.label {
  width: 90px;
  font-size: 14px;
  color: #5c5f66;
}
:deep(.el-slider__runway) {
  background: #bdd9fe;
}
.btn {
  height: 36px;
  background: #f2f6ff;
  border-color: #f2f6ff;
  color: #2c3037;
  box-sizing: border-box;

  &:hover {
    border-color: #409eff;
    background: rgba(#f2f6ff, 0.75);
  }
}
:deep(.is-disabled) {
  background-color: #fcfdff;
  color: #a8abb2;
}
</style>
