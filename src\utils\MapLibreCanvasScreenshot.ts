/**
 * @description MapLibre Canvas截图工具类
 * 直接基于MapLibre Canvas元素进行高质量截图
 */

import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";

/**
 * @description 截图配置接口
 */
export interface ScreenshotConfig {
  /** 图片格式 */
  format?: 'png' | 'jpeg' | 'webp';
  /** 图片质量(0-1)，仅对jpeg/webp有效 */
  quality?: number;
  /** 文件名前缀 */
  fileName?: string;
  /** 是否自动下载 */
  autoDownload?: boolean;
  /** 是否复制到剪切板 */
  copyToClipboard?: boolean;
}

/**
 * @description 截图结果接口
 */
export interface ScreenshotResult {
  success: boolean;
  dataUrl?: string;
  blob?: Blob;
  fileName?: string;
  error?: string;
  canvasInfo?: {
    width: number;
    height: number;
    format: string;
    fileSize: number;
  };
}

/**
 * @description MapLibre Canvas截图工具类
 */
export class MapLibreCanvasScreenshot {
  private static instance: MapLibreCanvasScreenshot | null = null;
  
  private defaultConfig: Required<ScreenshotConfig> = {
    format: 'png',
    quality: 0.9,
    fileName: 'maplibre_screenshot',
    autoDownload: true,
    copyToClipboard: true
  };

  /**
   * @description 获取单例实例
   */
  public static getInstance(): MapLibreCanvasScreenshot {
    if (!MapLibreCanvasScreenshot.instance) {
      MapLibreCanvasScreenshot.instance = new MapLibreCanvasScreenshot();
    }
    return MapLibreCanvasScreenshot.instance;
  }

  /**
   * @description 执行截图
   * @param config 截图配置
   * @returns 截图结果
   */
  public async takeScreenshot(config?: Partial<ScreenshotConfig>): Promise<ScreenshotResult> {
    try {
      const finalConfig = { ...this.defaultConfig, ...config };
      
      // 获取MapLibre Canvas
      const canvas = this.getMapLibreCanvas();
      if (!canvas) {
        throw new Error('无法获取MapLibre Canvas元素');
      }

      // 验证Canvas状态
      if (!this.validateCanvas(canvas)) {
        throw new Error('Canvas状态无效，无法进行截图');
      }

      // 等待地图渲染完成
      await this.waitForMapRender();

      // 生成截图数据
      const { dataUrl, blob } = await this.captureCanvas(canvas, finalConfig);
      
      // 验证截图内容（检查是否为空白图片）
      if (!this.isValidScreenshot(blob)) {
        console.warn('⚠️ 截图可能为空白，尝试诊断问题...');
        await this.diagnoseCanvasProblem(canvas);
        // 仍然返回结果，但添加警告
      }

      // 生成文件名
      const fileName = this.generateFileName(finalConfig.fileName, finalConfig.format);
      
      // 处理截图结果
      const result: ScreenshotResult = {
        success: true,
        dataUrl,
        blob,
        fileName,
        canvasInfo: {
          width: canvas.width,
          height: canvas.height,
          format: finalConfig.format,
          fileSize: blob.size
        }
      };

      // 自动下载
      if (finalConfig.autoDownload) {
        await this.downloadScreenshot(blob, fileName);
      }

      // 复制到剪切板
      if (finalConfig.copyToClipboard) {
        await this.copyToClipboard(blob);
      }

      console.log('🗺️ MapLibre截图完成:', {
        fileName,
        format: finalConfig.format,
        size: `${canvas.width}x${canvas.height}`,
        fileSize: `${(blob.size / 1024).toFixed(2)}KB`
      });

      return result;
    } catch (error) {
      console.error('📸 MapLibre截图失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * @description 获取MapLibre Canvas元素
   */
  private getMapLibreCanvas(): HTMLCanvasElement | null {
    try {
      const map = AppMaplibre.getMap();
      if (!map) {
        console.error('MapLibre地图未初始化');
        return null;
      }

      // 获取MapLibre的Canvas元素
      const canvas = map.getCanvas();
      if (!canvas) {
        console.error('无法获取MapLibre Canvas');
        return null;
      }

      console.log('✅ 成功获取MapLibre Canvas:', {
        width: canvas.width,
        height: canvas.height,
        clientWidth: canvas.clientWidth,
        clientHeight: canvas.clientHeight
      });

      return canvas;
    } catch (error) {
      console.error('获取MapLibre Canvas失败:', error);
      return null;
    }
  }

  /**
   * @description 验证Canvas状态
   */
  private validateCanvas(canvas: HTMLCanvasElement): boolean {
    // 检查尺寸
    if (canvas.width === 0 || canvas.height === 0) {
      console.error('Canvas尺寸无效:', { width: canvas.width, height: canvas.height });
      return false;
    }

    // MapLibre使用WebGL渲染，检查WebGL上下文
    const gl = canvas.getContext('webgl') || canvas.getContext('webgl2');
    if (!gl) {
      // 如果没有WebGL，尝试2D上下文
      const ctx2d = canvas.getContext('2d');
      if (!ctx2d) {
        console.error('无法获取任何Canvas渲染上下文');
        return false;
      }
      console.log('✅ 使用2D渲染上下文');
      return true;
    }

    // 检查WebGL上下文属性
    const contextAttributes = gl.getContextAttributes();
    if (!contextAttributes?.preserveDrawingBuffer) {
      console.warn('Canvas未启用preserveDrawingBuffer，截图可能为空白');
      // MapLibre可能需要特殊处理，但仍然尝试截图
    }

    console.log('✅ WebGL上下文验证通过');
    return true;
  }

  /**
   * @description 截取Canvas内容
   */
  private async captureCanvas(
    canvas: HTMLCanvasElement, 
    config: Required<ScreenshotConfig>
  ): Promise<{ dataUrl: string; blob: Blob }> {
    return new Promise((resolve, reject) => {
      try {
        // 准备MIME类型
        const mimeType = this.getMimeType(config.format);
        const quality = config.format === 'png' ? undefined : config.quality;

        // 生成DataURL
        const dataUrl = canvas.toDataURL(mimeType, quality);

        // 转换为Blob
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error('Canvas toBlob failed'));
            return;
          }
          resolve({ dataUrl, blob });
        }, mimeType, quality);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * @description 获取MIME类型
   */
  private getMimeType(format: string): string {
    switch (format) {
      case 'jpeg':
        return 'image/jpeg';
      case 'webp':
        return 'image/webp';
      case 'png':
      default:
        return 'image/png';
    }
  }

  /**
   * @description 生成文件名
   */
  private generateFileName(prefix: string, format: string): string {
    const timestamp = new Date().toISOString()
      .replace(/[:.]/g, '-')
      .slice(0, 19);
    return `${prefix}_${timestamp}.${format}`;
  }

  /**
   * @description 下载截图
   */
  private async downloadScreenshot(blob: Blob, fileName: string): Promise<void> {
    try {
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 延迟释放URL
      setTimeout(() => URL.revokeObjectURL(url), 1000);
      
      console.log('📁 截图文件已下载:', fileName);
    } catch (error) {
      console.error('下载截图失败:', error);
      throw error;
    }
  }

  /**
   * @description 复制到剪切板
   */
  private async copyToClipboard(blob: Blob): Promise<void> {
    try {
      // 检查是否支持剪贴板API
      if (!navigator.clipboard) {
        console.warn('当前环境不支持剪切板API（可能是HTTP环境）');
        return;
      }

      // 检查是否支持写入操作
      if (!navigator.clipboard.write) {
        console.warn('当前环境不支持剪切板写入操作');
        return;
      }

      // 检查是否为安全上下文
      if (!window.isSecureContext) {
        console.warn('当前环境不是安全上下文，剪切板功能受限');
        return;
      }

      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob })
      ]);

      console.log('📋 截图已复制到剪切板');
    } catch (error) {
      console.warn('复制到剪切板失败:', error);
      // 不抛出错误，因为这不是关键功能
      // 在HTTP环境下这是预期的行为
    }
  }

  /**
   * @description 获取Canvas信息
   */
  public getCanvasInfo(): any {
    const canvas = this.getMapLibreCanvas();
    if (!canvas) {
      return null;
    }

    const gl = canvas.getContext('webgl') || canvas.getContext('webgl2');
    const ctx2d = canvas.getContext('2d');
    const contextAttributes = gl?.getContextAttributes();

    return {
      width: canvas.width,
      height: canvas.height,
      clientWidth: canvas.clientWidth,
      clientHeight: canvas.clientHeight,
      hasWebGL: !!gl,
      has2D: !!ctx2d,
      preserveDrawingBuffer: contextAttributes?.preserveDrawingBuffer,
      antialias: contextAttributes?.antialias,
      alpha: contextAttributes?.alpha
    };
  }

  /**
   * @description 测试Canvas状态和截图能力
   * @returns 测试结果
   */
  public async testCanvasCapability(): Promise<{
    canvasAvailable: boolean;
    webglConfigured: boolean;
    preserveDrawingBuffer: boolean;
    mapLoaded: boolean;
    recommendedAction?: string;
  }> {
    const map = AppMaplibre.getMap();
    const canvas = this.getMapLibreCanvas();
    
    if (!canvas) {
      return {
        canvasAvailable: false,
        webglConfigured: false,
        preserveDrawingBuffer: false,
        mapLoaded: false,
        recommendedAction: '地图未初始化或Canvas不可用'
      };
    }

    const gl = canvas.getContext('webgl') || canvas.getContext('webgl2');
    const contextAttributes = gl?.getContextAttributes();
    const preserveDrawingBuffer = contextAttributes?.preserveDrawingBuffer || false;
    const mapLoaded = map?.loaded() || false;

    let recommendedAction = '';
    if (!preserveDrawingBuffer) {
      recommendedAction = '需要在MapLibre初始化时设置canvasContextOptions.preserveDrawingBuffer = true';
    } else if (!mapLoaded) {
      recommendedAction = '等待地图完全加载后再进行截图';
    } else {
      recommendedAction = 'Canvas配置正确，可以尝试截图';
    }

    console.log('🧪 Canvas能力测试结果:', {
      canvasAvailable: !!canvas,
      webglConfigured: !!gl,
      preserveDrawingBuffer,
      mapLoaded,
      recommendedAction
    });

    return {
      canvasAvailable: !!canvas,
      webglConfigured: !!gl,
      preserveDrawingBuffer,
      mapLoaded,
      recommendedAction
    };
  }

  /**
   * @description 检查截图能力
   */
  public checkCapability(): { canScreenshot: boolean; reason?: string } {
    const canvas = this.getMapLibreCanvas();
    if (!canvas) {
      return { canScreenshot: false, reason: '无法获取MapLibre Canvas' };
    }

    if (!this.validateCanvas(canvas)) {
      return { canScreenshot: false, reason: 'Canvas状态无效' };
    }

    return { canScreenshot: true };
  }

  /**
   * @description 等待地图渲染完成
   */
  private async waitForMapRender(): Promise<void> {
    return new Promise((resolve) => {
      const map = AppMaplibre.getMap();
      if (!map) {
        resolve();
        return;
      }

      // 如果地图已经加载完成，立即执行
      if (map.loaded()) {
        // 额外等待一个渲染帧，确保所有内容都已渲染
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            resolve();
          });
        });
      } else {
        // 等待地图加载完成
        map.once('load', () => {
          // 等待渲染完成
          requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              resolve();
            });
          });
        });
      }
    });
  }

  /**
   * @description 验证截图内容（检查是否为空白图片）
   */
  private isValidScreenshot(blob: Blob): boolean {
    // 基本验证：文件大小不能太小（小于1KB可能是空白图片）
    const minValidSize = 1024; // 1KB
    if (blob.size < minValidSize) {
      console.warn(`截图文件过小: ${blob.size} bytes，可能为空白图片`);
      return false;
    }
    return true;
  }

  /**
   * @description 诊断Canvas问题
   */
  private async diagnoseCanvasProblem(canvas: HTMLCanvasElement): Promise<void> {
    console.log('🔍 开始诊断Canvas问题...');
    
    const map = AppMaplibre.getMap();
    
    // 检查Canvas基本信息
    console.log('Canvas状态:', {
      width: canvas.width,
      height: canvas.height,
      clientWidth: canvas.clientWidth,
      clientHeight: canvas.clientHeight,
      style: {
        width: canvas.style.width,
        height: canvas.style.height
      }
    });

    // 检查WebGL上下文
    const gl = canvas.getContext('webgl') || canvas.getContext('webgl2');
    if (gl) {
      const contextAttributes = gl.getContextAttributes();
      console.log('WebGL上下文属性:', contextAttributes);
      
      if (!contextAttributes?.preserveDrawingBuffer) {
        console.error('❌ 关键问题：Canvas未启用preserveDrawingBuffer！');
        console.log('💡 解决方案：需要在MapLibre初始化时设置canvasContextOptions.preserveDrawingBuffer = true');
      }
    }

    // 检查地图状态
    if (map) {
      console.log('地图状态:', {
        loaded: map.loaded(),
        style: map.getStyle(),
        zoom: map.getZoom(),
        center: map.getCenter(),
        layers: map.getStyle()?.layers?.length || 0
      });
    }

    // 尝试读取Canvas像素数据进行验证
    try {
      const imageData = canvas.getContext('2d')?.getImageData(0, 0, 1, 1);
      if (imageData && imageData.data.every(val => val === 0)) {
        console.error('❌ Canvas像素数据全为0，确认为空白图片');
      }
    } catch (error) {
      console.log('无法通过2D上下文读取像素数据（正常，WebGL Canvas）');
    }

    console.log('🔍 Canvas问题诊断完成');
  }
}

/**
 * @description 导出便捷函数
 */
export const takeMapLibreScreenshot = (config?: Partial<ScreenshotConfig>) => {
  return MapLibreCanvasScreenshot.getInstance().takeScreenshot(config);
};

export default MapLibreCanvasScreenshot; 