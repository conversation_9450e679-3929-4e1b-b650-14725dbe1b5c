/**
 * @fileoverview 坐标拾取基础抽象类
 * @description 定义坐标拾取功能的通用接口和事件处理机制
 * <AUTHOR>
 * @version 2.0.0
 */

import type { CoordinatePoint } from '@/utils/coordinate/CoordinateTransform';

/**
 * @type CoordinateSystemType
 * @description 支持的坐标系类型枚举
 * - WGS84: 全球定位系统坐标系
 * - GCJ02: 国测局坐标系（火星坐标系）
 * - BD09: 百度坐标系
 * - EPSG3857: Web墨卡托投影坐标系
 */
export type CoordinateSystemType = 'WGS84' | 'GCJ02' | 'BD09' | 'EPSG3857';

/**
 * @interface CoordinateResult
 * @description 坐标拾取结果接口，包含多种坐标系的坐标值
 */
export interface CoordinateResult {
  /** 原始坐标（地图引擎返回的原始坐标） */
  originalCoord: CoordinatePoint;
  /** WGS84坐标（全球定位系统坐标系） */
  wgs84: CoordinatePoint;
  /** GCJ02坐标（国测局坐标系/火星坐标系） */
  gcj02: CoordinatePoint;
  /** BD09坐标（百度坐标系） */
  bd09: CoordinatePoint;
  /** 投影坐标（可选，根据需要包含不同投影的坐标值） */
  projectedCoord?: {
    /** EPSG:3857墨卡托投影坐标 */
    epsg3857?: CoordinatePoint;
  };
  /** 拾取时间戳（毫秒） */
  timestamp: number;
  /** 拾取点的屏幕坐标（像素值） */
  screenCoord?: { x: number; y: number };
}

/**
 * @type PickEventCallback
 * @description 坐标拾取事件回调函数类型，当成功拾取坐标时触发
 */
export type PickEventCallback = (result: CoordinateResult) => void;

/**
 * @type ErrorEventCallback
 * @description 错误事件回调函数类型，当拾取过程中发生错误时触发
 */
export type ErrorEventCallback = (error: Error) => void;

/**
 * @interface PickerOptions
 * @description 坐标拾取器配置选项接口
 */
export interface PickerOptions {
  /** 
   * 是否启用连续拾取模式
   * - true: 点击地图后继续拾取
   * - false: 点击地图拾取一次后自动停止
   * @default false
   */
  continuous?: boolean;
  
  /** 
   * 是否显示拾取点标记
   * @default true
   */
  showMarker?: boolean;
  
  /** 
   * 标记样式配置
   */
  markerStyle?: {
    /** 
     * 标记颜色，CSS颜色字符串
     * @default '#ff0000'
     */
    color?: string;
    
    /** 
     * 标记大小（像素）
     * @default 8
     */
    size?: number;
    
    /** 
     * 标记透明度 (0-1)
     * @default 0.8
     */
    opacity?: number;
  };
  
  /** 
   * 坐标精度（小数位数）
   * @default 6
   */
  precision?: number;
  
  /** 
   * 是否自动计算投影坐标
   * @default false
   */
  includeProjection?: boolean;
}

/**
 * @abstract CoordinatePickerBase
 * @description 坐标拾取器基础抽象类，定义通用接口和行为
 * 
 * 该类提供了坐标拾取的核心功能和事件处理机制，所有具体的坐标拾取实现
 * （如MapLibre和Cesium）都应该继承此类并实现相应的抽象方法。
 */
export abstract class CoordinatePickerBase {
  /** 是否处于激活状态 */
  protected _isActive: boolean = false;
  
  /** 配置选项 */
  protected _options: PickerOptions;
  
  /** 拾取成功回调函数 */
  protected _pickCallback?: PickEventCallback;
  
  /** 错误回调函数 */
  protected _errorCallback?: ErrorEventCallback;

  /**
   * 构造函数
   * @param options - 配置选项，会与默认选项合并
   */
  constructor(options: PickerOptions = {}) {
    // 合并默认选项和用户提供的选项
    this._options = {
      continuous: false,
      showMarker: true,
      precision: 6,
      includeProjection: false,
      markerStyle: {
        color: '#ff0000',
        size: 8,
        opacity: 0.8
      },
      ...options
    };
  }

  /**
   * 获取激活状态
   * @returns 当前拾取器是否处于激活状态
   */
  get isActive(): boolean {
    return this._isActive;
  }

  /**
   * 启动坐标拾取
   * 激活拾取器，设置回调函数并调用具体实现的onStart方法
   * 
   * @param callback - 拾取成功回调函数，当成功拾取坐标时触发
   * @param errorCallback - 拾取错误回调函数，当拾取过程中发生错误时触发
   */
  start(callback?: PickEventCallback, errorCallback?: ErrorEventCallback): void {
    // 如果已经激活，先停止
    if (this._isActive) {
      this.stop();
    }

    // 设置回调函数
    this._pickCallback = callback;
    this._errorCallback = errorCallback;
    this._isActive = true;

    // 调用具体实现的启动方法
    this.onStart();
  }

  /**
   * 停止坐标拾取
   * 停止拾取器活动，清除回调函数并调用具体实现的onStop方法
   */
  stop(): void {
    if (!this._isActive) return;

    this._isActive = false;
    this._pickCallback = undefined;
    this._errorCallback = undefined;

    // 调用具体实现的停止方法
    this.onStop();
  }

  /**
   * 更新配置选项
   * 更新拾取器的配置，并调用具体实现的onOptionsUpdate方法
   * 
   * @param options - 新的配置选项，会与现有选项合并
   */
  updateOptions(options: Partial<PickerOptions>): void {
    this._options = { ...this._options, ...options };
    
    // 调用具体实现的选项更新方法
    this.onOptionsUpdate();
  }

  /**
   * 处理拾取成功事件
   * 当成功拾取到坐标时，调用此方法处理结果并触发回调
   * 
   * @param result - 拾取结果，包含多种坐标系的坐标值
   * @protected
   */
  protected handlePickSuccess(result: CoordinateResult): void {
    try {
      // 如果有回调函数，调用它
      if (this._pickCallback) {
        this._pickCallback(result);
      }

      // 如果不是连续模式，拾取一次后自动停止
      if (!this._options.continuous) {
        this.stop();
      }
    } catch (error) {
      // 如果处理过程中发生错误，调用错误处理方法
      this.handlePickError(error as Error);
    }
  }

  /**
   * 处理拾取错误事件
   * 当拾取过程中发生错误时，调用此方法处理错误并触发回调
   * 
   * @param error - 错误对象
   * @protected
   */
  protected handlePickError(error: Error): void {
    console.error('坐标拾取错误:', error);
    
    // 如果有错误回调函数，调用它
    if (this._errorCallback) {
      this._errorCallback(error);
    }

    // 错误时停止拾取
    this.stop();
  }

  /**
   * 创建坐标结果对象
   * 根据原始坐标和屏幕坐标创建标准格式的坐标结果对象
   * 
   * @param originalCoord - 原始坐标
   * @param screenCoord - 屏幕坐标（可选）
   * @returns 标准格式的坐标结果对象
   * @protected
   */
  protected createCoordinateResult(
    originalCoord: CoordinatePoint, 
    screenCoord?: { x: number; y: number }
  ): CoordinateResult {
    // 创建基本结果对象
    const result: CoordinateResult = {
      originalCoord,
      wgs84: originalCoord, // 默认假设原始坐标为WGS84，子类可以重写
      gcj02: originalCoord,
      bd09: originalCoord,
      timestamp: Date.now(),
      screenCoord
    };

    // 如果需要包含投影坐标，添加投影坐标对象
    if (this._options.includeProjection) {
      result.projectedCoord = {
        epsg3857: originalCoord
      };
    }

    return result;
  }

  /**
   * 格式化坐标精度
   * 根据配置的精度格式化坐标值的小数位数
   * 
   * @param coord - 坐标点
   * @returns 格式化后的坐标点
   * @protected
   */
  protected formatCoordinatePrecision(coord: CoordinatePoint): CoordinatePoint {
    const precision = this._options.precision || 6;
    return {
      lng: Number(coord.lng.toFixed(precision)),
      lat: Number(coord.lat.toFixed(precision)),
      alt: coord.alt ? Number(coord.alt.toFixed(3)) : coord.alt
    };
  }

  // 抽象方法，子类必须实现

  /**
   * 启动拾取时的具体实现
   * 子类必须实现此方法，添加必要的事件监听或其他初始化逻辑
   * 
   * @abstract
   * @protected
   */
  protected abstract onStart(): void;

  /**
   * 停止拾取时的具体实现
   * 子类必须实现此方法，移除事件监听或执行其他清理工作
   * 
   * @abstract
   * @protected
   */
  protected abstract onStop(): void;

  /**
   * 配置更新时的具体实现
   * 子类必须实现此方法，处理配置选项变更后的逻辑
   * 
   * @abstract
   * @protected
   */
  protected abstract onOptionsUpdate(): void;

  /**
   * 销毁拾取器
   * 子类必须实现此方法，完全清理资源并使拾取器不可用
   * 
   * @abstract
   */
  abstract destroy(): void;

  /**
   * 获取地图中心点坐标
   * 子类必须实现此方法，返回当前地图视图中心点的坐标
   * 
   * @abstract
   * @returns 地图中心点坐标
   */
  abstract getMapCenter(): CoordinatePoint;

  /**
   * 飞行到指定坐标
   * 子类必须实现此方法，控制地图视图平滑飞行到指定坐标
   * 
   * @abstract
   * @param coord - 目标坐标
   * @param duration - 飞行时长（毫秒）
   * @returns Promise，飞行完成时解析
   */
  abstract flyTo(coord: CoordinatePoint, duration?: number): Promise<void>;
}

// 重新导出坐标点类型以供其他文件使用
export type { CoordinatePoint } 