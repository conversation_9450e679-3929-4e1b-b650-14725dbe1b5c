<template>
  <div class="base-main" grid="~ rows-[72px_1fr] gap-y-3">
    <div class="query-form">
      <el-form :model="queryForm">
        <el-row :gutter="40">
          <el-col :span="6">
            <el-form-item label="操作人">
              <el-input
                v-model="queryForm.operName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作时间">
              <el-date-picker
                v-model="timePicker"
                type="daterange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作状态">
              <el-select
                class="w-full admin-select"
                popper-class="admin-popper-select"
                v-model="queryForm.businessType"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in logStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button
                class="admin-query-btn"
                type="primary"
                @click="queryData"
                :icon="Search"
              >
                查询
              </el-button>
              <el-button
                class="admin-reset-btn"
                :icon="Refresh"
                @click="resetSearch"
              >
                重置</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="pb-unset! box-border [&_>div]:overflow-hidden">
      <div class="table-height">
        <el-table
          v-loading="loading"
          class="routeCt"
          :row-class-name="tableRowClassName"
          :data="tableData"
          style="width: 100%"
          height="100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="100"
            align="center"
          />
          <el-table-column prop="operName" label="操作人" align="center" />
          <el-table-column
            prop="operIp"
            label="ip地址"
            min-width="150"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="operUrl"
            label="请求url"
            min-width="100"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column prop="operTime" label="操作时间" align="center" />
          <!-- <el-table-column prop="costTime" label="消耗时间" align="center" /> -->
          <el-table-column
            prop="status"
            label="操作状态"
            show-overflow-tooltip
            align="center"
          >
            <template v-slot="scope">
              <div v-if="scope.row.status == '0'">异常</div>
              <div v-else>正常</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="100" align="center">
            <template v-slot="{ row }">
              <el-button
                link
                class="primary-link color-#1966FF"
                @click="handleCheck(row)"
              >
                详情</el-button
              >

              <!-- <el-button
                link
                class="danger-link"
                :icon="Delete"
                @click="handleDelete(row)"
                >删除</el-button
              > -->
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagibox box-border">
        <div class="pagitotal">共{{ tableSize }}条数据</div>
        <pagination
          class="custom-pagi-card"
          :total="tableSize"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          @pagination="getList"
        ></pagination>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :key="dialogKey"
      :model-value="visible"
      width="35%"
      class="admin-dialog"
      :before-close="handleClose"
      :close-on-click-modal="false"
      ref="test"
    >
      <data-info
        ref="userRef"
        v-model:formData="formData"
        v-model:editable="editable"
      />
      <template v-slot:footer v-if="dialogTitle != '数据日志查看'">
        <div>
          <el-button
            @click="handleClose"
            class="custom-close-button"
            :loading="subBtnLoading"
            >取 消</el-button
          >
          <el-button
            type="primary"
            :loading="subBtnLoading"
            class="custom-sub-button"
            @click="eventSubmit"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import {
  Search,
  Refresh,
  Plus,
  View,
  Edit,
  Delete,
} from "@element-plus/icons-vue";
import { logStatus } from "@/utils/constant";
import DataInfo from "./DataInfo.vue";
import { logDataPage, detailsLog, deleteLog } from "@/api/log";
import type { SysUser, QueryForm } from "./type";
const initQueryForm = () => {
  return {
    operName: "",
    startTime: "",
    endTime: "",
    businessType: "",
    pageNum: 1,
    pageSize: 20,
  };
};
const queryForm = ref<QueryForm>(initQueryForm());
const initFormData = () => {
  return {
    id: "",
    operName: "",
    operIp: "",
    title: "",
    businessType: "",
    method: "",
    requestMethod: "",
    deptName: "",
    operParam: "",
    jsonResult: "",
    status: "",
    operUrl: "",
    operTime: "",
    errorMsg: "",
    costTime: "",
  };
};
const formData = ref<SysUser>(initFormData());
const timePicker = ref([]);
const loading = ref(false);
const tableData = ref<SysUser[]>([]);
const dialogTitle = ref("");
const dialogKey = ref(0);
const visible = ref(false);
const editable = ref(false);
const subBtnLoading = ref(false);
const tableSize = ref(0);
const userRef = ref();
const queryData = () => {
  getList();
};
const resetSearch = () => {
  queryForm.value = initQueryForm();
  timePicker.value = [];
  getList();
};
const handleCheck = async (row: SysUser) => {
  editable.value = true;
  dialogTitle.value = "数据日志查看";
  const result = await detailsLog(row.id);
  formData.value = result.data;
  formData.value.status = result.data.status == "1" ? "正常" : "异常";
  visible.value = true;
};
const handleDelete = (row: SysUser) => {
  ElMessageBox.confirm("确定要删除当前数据日志吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox",
  }).then(() => {
    deleteLog(row.id).then((res) => {
      if (res.code == 200) {
        ElMessage({
          showClose: true,
          message: "删除成功",
          type: "success",
        });
        getList();
      }
    });
  });
};
const handleClose = () => {
  visible.value = false;
};
const eventSubmit = async () => {
  subBtnLoading.value = false;
};
const getList = async () => {
  try {
    loading.value = true;
    if (timePicker.value?.length) {
      queryForm.value.startTime = timePicker.value[0];
      queryForm.value.endTime = timePicker.value[1];
    } else {
      queryForm.value.startTime = "";
      queryForm.value.endTime = "";
    }
    const result = await logDataPage(queryForm.value);
    tableData.value = result.data.list;
    tableSize.value = result.data.totalCount;
  } finally {
    loading.value = false;
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.table-height {
  height: calc(100vh - 295px);
}
</style>
