<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    label-width="auto"
    class="admin-sub-form"
    :disabled="editable"
  >
    <el-form-item label="参数名称" prop="configName">
      <el-input
        class="custom-input"
        v-model.trim="formData.configName"
        placeholder="请输入参数名称"
      />
    </el-form-item>
    <el-form-item label="参数键名" prop="configKey">
      <el-input
        class="custom-input"
        v-model.trim="formData.configKey"
        placeholder="请输入参数键名"
      />
    </el-form-item>
    <el-form-item label="参数键值" prop="configValue">
      <el-input
        class="custom-input"
        v-model.trim="formData.configValue"
        placeholder="请输入参数键值"
      />
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input
        placeholder="请输入备注"
        v-model="formData.remark"
        type="textarea"
        resize="none"
        :rows="3"
        maxlength="500"
      ></el-input>
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
const props = defineProps({
  formData: Object,
  editable: Boolean,
});
let formData: any = ref(props.formData);
let editable = ref(props.editable);
watch(
  () => props.formData,
  (val) => {
    resetForm();
    formData.value = val;
  }
);
watch(
  () => props.editable,
  (val) => {
    editable.value = val;
  }
);
const checkCif = (rule: any, value: any, callback: any) => {
  const regMobile = /^[A-Za-z0-9_-]+$/;
  if (regMobile.test(value)) {
    return callback();
  }
  // 返回一个错误提示
  callback(new Error("只能含英文大小写、下划线"));
};

const rules = {
  configName: [
    { required: true, message: "请输入参数名称", trigger: "blur" },
    { max: 20, message: "长度不超过20个字符", trigger: "blur" },
  ],
  configKey: [
    { required: true, message: "请输入参数键名", trigger: "blur" },
    { validator: checkCif, trigger: "blur" },
  ],
  configValue: [
    { required: true, message: "请输入参数键值", trigger: "blur" },
    { max: 900, message: "长度不超过900个字符", trigger: "blur" },
  ],
};
let form: any = ref(null);
const submitForm = async (): Promise<any> => {
  let result: boolean = await form.value.validate();
  return result ? formData.value : null;
};
const resetForm = () => {
  form.value.resetFields();
};
onMounted(async () => {
  resetForm();
});
defineExpose({
  submitForm,
  resetForm,
});
</script>
<style lang="scss" scoped>
.custom-div {
  border: 1px solid #ebebeb;
  border-radius: 5px;
  min-height: 80px;
  max-height: 230px;
  overflow-y: auto;
  width: 100%;
}
.custom-div::-webkit-scrollbar {
  width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.custom-div::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background: #ebebeb;
}
</style>
