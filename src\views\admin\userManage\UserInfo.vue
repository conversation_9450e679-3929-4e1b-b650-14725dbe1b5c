<template>
  <div class="bg-#F5F5F5 box-border p-2.5">
    <div class="flex justify-between">
      <div class="w-254px bg-#fff rounded-1 box-border p-5">
        <div class="flex-c">
          <img
            v-if="formData.photo"
            :src="formData.photo"
            alt=""
            class="w-20 h-20 rounded-50%"
          />
          <div
            v-else
            class="w-20 h-20 flex-c font-size-9 rounded-50% bg-#7C40AA color-#fff"
          >
            {{ formData.name[0] }}
          </div>
        </div>
        <div class="f-bt">
          <span>用户名称：</span>
          <span>{{ formData.name }}</span>
        </div>
        <div class="f-bt">
          <span>用户性别：</span>
          <span>{{ formData.gender }}</span>
        </div>
        <div class="f-bt">
          <span>平台账号：</span>
          <span>{{ formData.username }}</span>
        </div>
        <div class="f-bt">
          <span>电子邮箱：</span>
          <span>{{ formData.email }}</span>
        </div>
        <div class="f-bt">
          <span>手机号码：</span>
          <span>{{ formData.phone }}</span>
        </div>
        <div class="f-bt">
          <span>出生年月：</span>
          <span>{{ formData.birthday }}</span>
        </div>
      </div>
      <div class="bg-#fff w-640px rounded-1 box-border p-5">
        <div class="color-#2C3037 font-size-3.5">基础信息</div>
        <div class="line"></div>
        <div>
          <el-form
            ref="form"
            :model="formData"
            :rules="rules"
            class="admin-sub-form"
            label-width="auto"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户编码" prop="code">
                  <el-input
                    class=""
                    v-model.trim="formData.code"
                    disabled
                    placeholder="请输入用户编码"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户昵称" prop="nickname">
                  <el-input
                    class=""
                    disabled
                    v-model.trim="formData.nickname"
                    placeholder="请输入用户昵称"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="认证状态" prop="realStatus">
                  <el-input
                    class=""
                    disabled
                    v-model.trim="formData.realStatus"
                    placeholder="请输入实名认证"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户类型" prop="type">
                  <el-input
                    class=""
                    disabled
                    v-model.trim="formData.type"
                    placeholder="请输入用户类型"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户来源" prop="source">
                  <el-input
                    disabled
                    class=""
                    v-model.trim="formData.source"
                    placeholder="请输入用户来源"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="来源平台" prop="sourcePlatform">
                  <el-input
                    class=""
                    disabled
                    v-model.trim="formData.sourcePlatform"
                    placeholder="请输入来源平台"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="创建时间" prop="createTime">
                  <el-input
                    disabled
                    class=""
                    v-model.trim="formData.createTime"
                    placeholder="请输入创建时间"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="更新时间" prop="updateTime">
                  <el-input
                    class=""
                    disabled
                    v-model.trim="formData.updateTime"
                    placeholder="请输入更新时间"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="激活状态" prop="activation">
                  <el-input
                    disabled
                    class=""
                    v-model.trim="formData.activation"
                    placeholder="请输入激活状态"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户角色" prop="roleIds">
                  <el-select
                    v-model="formData.roleIds"
                    class="w-full"
                    multiple
                    :disabled="dialogTitle === '用户查看' ? true : false"
                    placeholder="请选择用户角色"
                  >
                    <el-option
                      v-for="item in roleoptions"
                      :key="item['id']"
                      :label="item['name']"
                      :value="item['id']"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { userType } from "@/utils/constant";
import { useConstValue } from "@/utils/utils";
import { getImages } from "@/utils/getImages";
import { roleList } from "@/api/role";
const props = defineProps({
  formData: Object,
  editable: Boolean,
  dialogTitle: String,
});
let formData: any = ref(props.formData);
let editable = ref(props.editable);
let dialogTitle = ref(props.dialogTitle);
watch(
  () => props.formData,
  (val: any) => {
    resetForm();
    formData.value = val;
  }
);
watch(
  () => props.editable,
  (val) => {
    editable.value = val;
  }
);
watch(
  () => props.dialogTitle,
  (val) => {
    dialogTitle.value = val;
  }
);
const rules = {
  roleIds: [{ required: true, message: "请选择用户角色", trigger: "change" }],
};
const roleoptions = ref([]);
const getRoleTree = async () => {
  const result = await roleList();
  roleoptions.value = result.data;
};
const submitForm = async (): Promise<any> => {
  return (await form.value?.validate()) ? formData.value : null;
};
let form: any = ref(null);
const resetForm = () => {
  form.value?.resetFields();
};
onMounted(async () => {
  resetForm();
  await getRoleTree();
});
defineExpose({
  submitForm,
  resetForm,
});
</script>
<style lang="scss" scoped>
.f-bt {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}
.line {
  border: 1px solid #eeeeee;
  margin: 10px 0;
}
</style>
