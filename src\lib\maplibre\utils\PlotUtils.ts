/**
 * @fileoverview 标绘系统工具函数库
 * @description 提供标绘系统常用的工具函数，包括几何计算、数据转换、验证等
 * <AUTHOR>
 * @version 1.0.0
 */

import type { PlotFeature } from '@/lib/maplibre/layer/types/LayerTypes';
import type { GeoJSON } from 'geojson';

/**
 * @description 几何计算工具类
 */
export class GeometryUtils {
  /**
   * @description 计算两点之间的距离（米）
   * @param coord1 - 坐标1 [经度, 纬度]
   * @param coord2 - 坐标2 [经度, 纬度]
   * @returns 距离（米）
   */
  static calculateDistance(coord1: [number, number], coord2: [number, number]): number {
    const R = 6371000; // 地球半径（米）
    const lat1Rad = (coord1[1] * Math.PI) / 180;
    const lat2Rad = (coord2[1] * Math.PI) / 180;
    const deltaLatRad = ((coord2[1] - coord1[1]) * Math.PI) / 180;
    const deltaLonRad = ((coord2[0] - coord1[0]) * Math.PI) / 180;

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  /**
   * @description 计算线段长度（米）
   * @param coordinates - 线段坐标数组
   * @returns 总长度（米）
   */
  static calculateLineLength(coordinates: number[][]): number {
    if (coordinates.length < 2) return 0;

    let totalLength = 0;
    for (let i = 1; i < coordinates.length; i++) {
      totalLength += this.calculateDistance(
        [coordinates[i - 1][0], coordinates[i - 1][1]],
        [coordinates[i][0], coordinates[i][1]]
      );
    }
    return totalLength;
  }

  /**
   * @description 计算多边形面积（平方米）
   * @param coordinates - 多边形坐标数组（外环）
   * @returns 面积（平方米）
   */
  static calculatePolygonArea(coordinates: number[][]): number {
    if (coordinates.length < 3) return 0;

    // 使用Shoelace公式计算面积
    let area = 0;
    const numPoints = coordinates.length;

    for (let i = 0; i < numPoints; i++) {
      const j = (i + 1) % numPoints;
      area += coordinates[i][0] * coordinates[j][1];
      area -= coordinates[j][0] * coordinates[i][1];
    }

    return Math.abs(area / 2) * 111319.9 * 111319.9; // 转换为平方米（近似）
  }

  /**
   * @description 计算要素的中心点
   * @param feature - 要素对象
   * @returns 中心点坐标 [经度, 纬度]
   */
  static calculateFeatureCenter(feature: PlotFeature): [number, number] {
    const { geometry } = feature.geojson;

    switch (geometry.type) {
      case 'Point':
        return [geometry.coordinates[0], geometry.coordinates[1]];

      case 'LineString':
        return this.calculateLinestringCenter(geometry.coordinates);

      case 'Polygon':
        return this.calculatePolygonCenter(geometry.coordinates[0]);

      default:
        throw new Error(`不支持的几何类型: ${geometry.type}`);
    }
  }

  /**
   * @description 计算线段中心点
   * @param coordinates - 线段坐标数组
   * @returns 中心点坐标
   */
  private static calculateLinestringCenter(coordinates: number[][]): [number, number] {
    if (coordinates.length === 1) {
      return [coordinates[0][0], coordinates[0][1]];
    }

    const totalLength = this.calculateLineLength(coordinates);
    const halfLength = totalLength / 2;
    let currentLength = 0;

    for (let i = 1; i < coordinates.length; i++) {
      const segmentLength = this.calculateDistance(
        [coordinates[i - 1][0], coordinates[i - 1][1]],
        [coordinates[i][0], coordinates[i][1]]
      );

      if (currentLength + segmentLength >= halfLength) {
        const ratio = (halfLength - currentLength) / segmentLength;
        return [
          coordinates[i - 1][0] + (coordinates[i][0] - coordinates[i - 1][0]) * ratio,
          coordinates[i - 1][1] + (coordinates[i][1] - coordinates[i - 1][1]) * ratio
        ];
      }

      currentLength += segmentLength;
    }

    return [coordinates[0][0], coordinates[0][1]];
  }

  /**
   * @description 计算多边形中心点
   * @param coordinates - 多边形坐标数组
   * @returns 中心点坐标
   */
  private static calculatePolygonCenter(coordinates: number[][]): [number, number] {
    let totalX = 0;
    let totalY = 0;
    let totalArea = 0;

    for (let i = 0; i < coordinates.length - 1; i++) {
      const x0 = coordinates[i][0];
      const y0 = coordinates[i][1];
      const x1 = coordinates[i + 1][0];
      const y1 = coordinates[i + 1][1];

      const a = x0 * y1 - x1 * y0;
      totalX += (x0 + x1) * a;
      totalY += (y0 + y1) * a;
      totalArea += a;
    }

    totalArea *= 0.5;
    return [totalX / (6 * totalArea), totalY / (6 * totalArea)];
  }

  /**
   * @description 判断点是否在多边形内
   * @param point - 点坐标 [经度, 纬度]
   * @param polygon - 多边形坐标数组
   * @returns 是否在多边形内
   */
  static isPointInPolygon(point: [number, number], polygon: number[][]): boolean {
    const x = point[0];
    const y = point[1];
    let inside = false;

    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i][0];
      const yi = polygon[i][1];
      const xj = polygon[j][0];
      const yj = polygon[j][1];

      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }

    return inside;
  }
}

/**
 * @description 数据转换工具类
 */
export class DataUtils {
  /**
   * @description 将PlotFeature转换为GeoJSON Feature
   */
  static plotFeatureToGeoJSON(plotFeature: PlotFeature): GeoJSON.Feature {
    return {
      type: 'Feature',
      id: plotFeature.id,
      geometry: plotFeature.geojson.geometry,
      properties: {
        name: plotFeature.name,
        description: plotFeature.description,
        type: plotFeature.geojson.properties.type,
        geometryType: plotFeature.geojson.properties.geometryType,
        createTime: plotFeature.createTime,
        modifyTime: plotFeature.modifyTime,
        visible: plotFeature.visible,
        locked: plotFeature.locked,
        engineType: plotFeature.engineType,
        ...plotFeature.geojson.properties.style
      }
    };
  }

  /**
   * @description 将GeoJSON Feature转换为PlotFeature
   */
  static geoJSONToPlotFeature(geoJsonFeature: GeoJSON.Feature): PlotFeature {
    const props = geoJsonFeature.properties || {};
    
    // 提取样式信息
    const style = this.extractStyleFromProperties(props);
    
    // 创建GeoJSON Feature
    const geojson: any = {
      type: 'Feature',
      geometry: geoJsonFeature.geometry,
      properties: {
        type: 'plot',
        geometryType: props.geometryType || props.type || 'point',
        style: style
      }
    };
    
    return {
      id: String(geoJsonFeature.id || this.generateId()),
      name: props.name || '未命名要素',
      description: props.description || '',
      geojson: geojson,
      engineType: props.engineType || 'maplibre',
      createTime: props.createTime || Date.now(),
      modifyTime: props.modifyTime || Date.now(),
      visible: props.visible !== false,
      locked: props.locked === true
    };
  }

  /**
   * @description 从属性中提取样式
   * @param properties - GeoJSON属性
   * @returns 样式对象
   */
  private static extractStyleFromProperties(properties: any): any {
    const styleKeys = [
      'fillColor', 'fillOpacity', 'strokeColor', 'strokeWidth', 
      'strokeOpacity', 'pointColor', 'pointRadius', 'pointOpacity'
    ];
    
    const style: any = {};
    styleKeys.forEach(key => {
      if (properties[key] !== undefined) {
        style[key] = properties[key];
      }
    });
    
    return style;
  }

  /**
   * @description 生成唯一ID
   * @returns 唯一标识符
   */
  static generateId(): string {
    return `feature_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * @description 深度克隆对象
   * @param obj - 要克隆的对象
   * @returns 克隆后的对象
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepClone(item)) as unknown as T;
    }

    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(obj[key]);
      }
    }

    return cloned;
  }

  /**
   * @description 格式化文件大小
   * @param bytes - 字节数
   * @returns 格式化后的文件大小
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * @description 格式化距离
   * @param meters - 距离（米）
   * @returns 格式化后的距离
   */
  static formatDistance(meters: number): string {
    if (meters < 1000) {
      return `${meters.toFixed(2)} m`;
    } else if (meters < 1000000) {
      return `${(meters / 1000).toFixed(2)} km`;
    } else {
      return `${(meters / 1000000).toFixed(2)} Mm`;
    }
  }

  /**
   * @description 格式化面积
   * @param squareMeters - 面积（平方米）
   * @returns 格式化后的面积
   */
  static formatArea(squareMeters: number): string {
    if (squareMeters < 10000) {
      return `${squareMeters.toFixed(2)} m²`;
    } else if (squareMeters < 1000000) {
      return `${(squareMeters / 10000).toFixed(2)} 公顷`;
    } else {
      return `${(squareMeters / 1000000).toFixed(2)} km²`;
    }
  }
}

/**
 * @description 验证工具类
 */
export class ValidationUtils {
  /**
   * @description 验证坐标是否有效
   * @param coordinates - 坐标数组 [经度, 纬度]
   * @returns 是否有效
   */
  static isValidCoordinate(coordinates: [number, number]): boolean {
    if (!Array.isArray(coordinates) || coordinates.length !== 2) {
      return false;
    }

    const [lng, lat] = coordinates;
    return typeof lng === 'number' && typeof lat === 'number' &&
           lng >= -180 && lng <= 180 &&
           lat >= -90 && lat <= 90 &&
           !isNaN(lng) && !isNaN(lat);
  }

  /**
   * @description 验证要素名称
   * @param name - 要素名称
   * @returns 验证结果
   */
  static validateFeatureName(name: string): { isValid: boolean; error?: string } {
    if (!name || typeof name !== 'string') {
      return { isValid: false, error: '名称不能为空' };
    }

    if (name.length < 1 || name.length > 100) {
      return { isValid: false, error: '名称长度必须在1-100个字符之间' };
    }

    // 检查特殊字符
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(name)) {
      return { isValid: false, error: '名称包含无效字符' };
    }

    return { isValid: true };
  }

  /**
   * @description 验证要素描述
   * @param description - 要素描述
   * @returns 验证结果
   */
  static validateFeatureDescription(description: string): { isValid: boolean; error?: string } {
    if (description && typeof description !== 'string') {
      return { isValid: false, error: '描述必须是字符串类型' };
    }

    if (description && description.length > 1000) {
      return { isValid: false, error: '描述长度不能超过1000个字符' };
    }

    return { isValid: true };
  }

  /**
   * @description 验证几何对象
   * @param geometry - 几何对象
   * @returns 验证结果
   */
  static validateGeometry(geometry: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!geometry || typeof geometry !== 'object') {
      return { isValid: false, errors: ['几何对象不能为空'] };
    }

    if (!geometry.type) {
      errors.push('几何对象缺少type属性');
    }

    if (!geometry.coordinates || !Array.isArray(geometry.coordinates)) {
      errors.push('几何对象缺少有效的coordinates属性');
    }

    if (errors.length > 0) {
      return { isValid: false, errors };
    }

    // 根据类型验证坐标
    switch (geometry.type) {
      case 'Point':
        if (!this.isValidCoordinate(geometry.coordinates)) {
          errors.push('点坐标无效');
        }
        break;

      case 'LineString':
        if (geometry.coordinates.length < 2) {
          errors.push('线段至少需要2个点');
        } else {
          for (const coord of geometry.coordinates) {
            if (!this.isValidCoordinate(coord)) {
              errors.push('线段包含无效坐标');
              break;
            }
          }
        }
        break;

      case 'Polygon':
        if (!Array.isArray(geometry.coordinates[0]) || geometry.coordinates[0].length < 4) {
          errors.push('多边形环至少需要4个点');
        } else {
          for (const coord of geometry.coordinates[0]) {
            if (!this.isValidCoordinate(coord)) {
              errors.push('多边形包含无效坐标');
              break;
            }
          }
        }
        break;

      default:
        errors.push(`不支持的几何类型: ${geometry.type}`);
    }

    return { isValid: errors.length === 0, errors };
  }
}

/**
 * @description 性能优化工具类
 */
export class PerformanceUtils {
  private static timers: Map<string, number> = new Map();

  /**
   * @description 开始性能计时
   * @param name - 计时器名称
   */
  static startTimer(name: string): void {
    this.timers.set(name, performance.now());
  }

  /**
   * @description 结束性能计时并返回耗时
   * @param name - 计时器名称
   * @returns 耗时（毫秒）
   */
  static endTimer(name: string): number {
    const startTime = this.timers.get(name);
    if (startTime === undefined) {
      console.warn(`计时器 ${name} 不存在`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(name);
    return duration;
  }

  /**
   * @description 防抖函数
   * @param func - 要防抖的函数
   * @param wait - 等待时间（毫秒）
   * @returns 防抖后的函数
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: number | null = null;

    return (...args: Parameters<T>) => {
      if (timeout) {
        clearTimeout(timeout);
      }

      timeout = setTimeout(() => {
        func(...args);
      }, wait);
    };
  }

  /**
   * @description 节流函数
   * @param func - 要节流的函数
   * @param limit - 时间限制（毫秒）
   * @returns 节流后的函数
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle = false;

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => {
          inThrottle = false;
        }, limit);
      }
    };
  }

  /**
   * @description 批处理函数
   * @param items - 要处理的项目数组
   * @param processor - 处理函数
   * @param batchSize - 批处理大小
   * @returns Promise
   */
  static async batchProcess<T, R>(
    items: T[],
    processor: (item: T) => Promise<R> | R,
    batchSize: number = 50
  ): Promise<R[]> {
    const results: R[] = [];

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch.map(processor));
      results.push(...batchResults);

      // 让出控制权，避免阻塞UI
      if (i + batchSize < items.length) {
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }

    return results;
  }
}

/**
 * @description 统一导出所有工具函数
 */
export const PlotUtils = {
  Geometry: GeometryUtils,
  Data: DataUtils,
  Validation: ValidationUtils,
  Performance: PerformanceUtils
}; 