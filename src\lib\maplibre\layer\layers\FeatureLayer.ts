/**
 * @fileoverview 要素图层实现
 * @description 支持标绘要素的显示、编辑和管理，基于GeoJSON数据
 * <AUTHOR>
 * @version 1.0.0
 */

import { BaseLayer } from './BaseLayer';
import { Map as GlMap } from 'maplibre-gl';
import { 
  LayerType, 
  type FeatureLayerConfig, 
  type PlotFeature,
  type FeatureStyle,
  DEFAULT_FEATURE_STYLE
} from '../types/LayerTypes';

/**
 * @class FeatureLayer
 * @description 要素图层类，支持标绘要素的显示和管理
 * @extends BaseLayer
 */
export class FeatureLayer extends BaseLayer {
  private features: PlotFeature[] = [];
  private defaultStyle: FeatureStyle;
  private editable: boolean = true;
  private showLabels: boolean = true;
  private sourceId: string;

  /**
   * @constructor
   * @param options - 要素图层配置
   */
  constructor(options: FeatureLayerConfig) {
    super(options);
    this.features = options.features || [];
    this.defaultStyle = { ...DEFAULT_FEATURE_STYLE, ...options.defaultStyle };
    this.editable = options.editable !== false;
    this.showLabels = options.showLabels !== false;
    this.sourceId = `${this.id}-source`;
  }

  /**
   * @description 将图层添加到地图
   * @param map - MapLibre地图实例
   */
  async addToInternal(map: GlMap): Promise<any> {
    this._map = map;

    // 创建GeoJSON数据源
    const geoJsonData = this.createGeoJSONData();
    console.log('FeatureLayer: 创建GeoJSON数据源', geoJsonData);
    
    this._map.addSource(this.sourceId, {
      type: 'geojson',
      data: geoJsonData
    });
    
    // 添加不同几何类型的图层
    this.addPointLayer();
    this.addLineLayer();
    this.addPolygonLayer();
    
    // 如果启用标签，添加标签图层
    // if (this.showLabels) {
    //   this.addLabelLayer();
    // }

    console.log(`要素图层 ${this.id} 已添加到地图，包含 ${this.features.length} 个要素`);
    return this._map.getLayer(`${this.id}-polygon-fill`);
  }

  /**
   * @description 从地图中移除图层
   */
  removeInternal(): void {
    if (!this._map) return;

    const layerIds = [
      `${this.id}-point`,
      `${this.id}-line`, 
      `${this.id}-polygon-fill`,
      `${this.id}-polygon-stroke`,
      `${this.id}-label`
    ];

    layerIds.forEach(layerId => {
      if (this._map.getLayer(layerId)) {
        this._map.removeLayer(layerId);
      }
    });

    if (this._map.getSource(this.sourceId)) {
      this._map.removeSource(this.sourceId);
    }

    console.log(`要素图层 ${this.id} 已从地图中移除`);
  }

  /**
   * @description 创建GeoJSON数据
   */
  private createGeoJSONData(): any {
    const featureCollection = {
      type: 'FeatureCollection',
      features: this.features
        .map(feature => ({
          type: 'Feature',
          id: feature.id,
          geometry: feature.geojson.geometry,
          properties: {
            id: feature.id,
            name: feature.name,
            type: feature.geojson.properties.geometryType,
            remark: feature.remark,
            style: feature.geojson.properties.style
          }
        }))
    };
    return featureCollection;
  }

  /**
   * @description 添加点图层
   */
  private addPointLayer(): void {
    // 获取应该插入的位置（在基础图层之上）
    const beforeId = this.getInsertBeforeId()
    
    const layerConfig: any = {
      id: `${this.id}-point`,
      type: 'circle',
      source: this.sourceId,
      filter: ['==', ['get', 'type'], 'point'],
      paint: {
        'circle-color': this.defaultStyle.pointColor,
        'circle-radius': this.defaultStyle.pointSize,
        'circle-stroke-color': this.defaultStyle.pointOutlineColor,
        'circle-stroke-width': this.defaultStyle.pointOutlineWidth
      }
    }
    
    // 如果有参考图层，添加beforeId参数
    if (beforeId) {
      this._map.addLayer(layerConfig, beforeId)
      console.log(`点图层 ${this.id}-point 已添加到 ${beforeId} 之前`)
    } else {
      this._map.addLayer(layerConfig)
      console.log(`点图层 ${this.id}-point 已添加到顶层`)
    }
  }

  /**
   * @description 添加线图层
   */
  private addLineLayer(): void {
    // 获取应该插入的位置（在基础图层之上）
    const beforeId = this.getInsertBeforeId()
    
    const layerConfig: any = {
      id: `${this.id}-line`,
      type: 'line',
      source: this.sourceId,
      filter: ['==', ['get', 'type'], 'linestring'],
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': this.defaultStyle.lineColor,
        'line-width': this.defaultStyle.lineWidth
      }
    }
    
    // 如果有参考图层，添加beforeId参数
    if (beforeId) {
      this._map.addLayer(layerConfig, beforeId)
      console.log(`线图层 ${this.id}-line 已添加到 ${beforeId} 之前`)
    } else {
      this._map.addLayer(layerConfig)
      console.log(`线图层 ${this.id}-line 已添加到顶层`)
    }
  }

  /**
   * @description 添加多边形图层
   */
  private addPolygonLayer(): void {
    // 获取应该插入的位置（在基础图层之上）
    const beforeId = this.getInsertBeforeId()
    
    // 多边形填充
    const fillLayerConfig: any = {
      id: `${this.id}-polygon-fill`,
      type: 'fill',
      source: this.sourceId,
      filter: ['in', ['get', 'type'], ['literal', ['polygon', 'rectangle']]],
      paint: {
        'fill-color': this.defaultStyle.fillColor,
        'fill-opacity': this.defaultStyle.fillOpacity
      }
    }
    
    // 多边形边框
    const strokeLayerConfig: any = {
      id: `${this.id}-polygon-stroke`,
      type: 'line',
      source: this.sourceId,
      filter: ['in', ['get', 'type'], ['literal', ['polygon', 'rectangle']]],
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': this.defaultStyle.strokeColor,
        'line-width': this.defaultStyle.strokeWidth
      }
    }
    
    // 添加填充图层
    if (beforeId) {
      this._map.addLayer(fillLayerConfig, beforeId)
      console.log(`多边形填充图层 ${this.id}-polygon-fill 已添加到 ${beforeId} 之前`)
    } else {
      this._map.addLayer(fillLayerConfig)
      console.log(`多边形填充图层 ${this.id}-polygon-fill 已添加到顶层`)
    }
    
    // 添加边框图层（边框应该在填充之上）
    this._map.addLayer(strokeLayerConfig)
    console.log(`多边形边框图层 ${this.id}-polygon-stroke 已添加`)
  }

  /**
   * @description 添加标签图层
   */
  private addLabelLayer(): void {
    this._map.addLayer({
      id: `${this.id}-label`,
      type: 'symbol',
      source: this.sourceId,
      layout: {
        'text-field': ['get', 'name'],
        'text-font': ['Open Sans Regular'],
        'text-size': 12,
        'text-offset': [0, 1.5],
        'text-anchor': 'center'
      },
      paint: {
        'text-color': '#ffffff',
        'text-halo-color': '#000000',
        'text-halo-width': 1
      }
    });
  }

  /**
   * @description 添加要素
   * @param feature - 要素数据
   */
  addFeature(feature: PlotFeature): void {
    const existingIndex = this.features.findIndex(f => f.id === feature.id);
    if (existingIndex >= 0) {
      this.features[existingIndex] = feature;
    } else {
      this.features.push(feature);
    }
    this.updateMapData();
  }

  /**
   * @description 移除要素
   * @param featureId - 要素ID
   */
  removeFeature(featureId: number): boolean {
    const index = this.features.findIndex(f => f.id === featureId);
    if (index >= 0) {
      this.features.splice(index, 1);
      this.updateMapData();
      return true;
    }
    return false;
  }

  /**
   * @description 更新要素
   * @param feature - 更新的要素数据
   */
  updateFeature(feature: PlotFeature): boolean {
    const index = this.features.findIndex(f => f.id === feature.id);
    if (index >= 0) {
      this.features[index] = feature;
      this.updateMapData();
      return true;
    }
    return false;
  }

  /**
   * @description 获取要素
   * @param featureId - 要素ID
   */
  getFeature(featureId: number): PlotFeature | undefined {
    return this.features.find(f => f.id === featureId);
  }

  /**
   * @description 获取所有要素
   */
  getAllFeatures(): PlotFeature[] {
    return [...this.features];
  }

  /**
   * @description 更新地图数据
   */
  private updateMapData(): void {
    if (this._map && this._map.getSource(this.sourceId)) {
      const geoJsonData = this.createGeoJSONData();
      console.log('FeatureLayer: 更新地图数据', {
        sourceId: this.sourceId,
        featuresCount: this.features.length,
        geoJsonFeatures: geoJsonData.features.length,
        sampleFeature: geoJsonData.features[0]
      });
      
      const source = this._map.getSource(this.sourceId) as any;
      source.setData(geoJsonData);
      
      // 检查图层是否存在
      const pointLayer = this._map.getLayer(`${this.id}-point`);
      const lineLayer = this._map.getLayer(`${this.id}-line`);
      const polygonLayer = this._map.getLayer(`${this.id}-polygon-fill`);
      
      console.log('FeatureLayer: 图层状态检查', {
        pointLayer: !!pointLayer,
        lineLayer: !!lineLayer,
        polygonLayer: !!polygonLayer
      });
    } else {
      console.warn('FeatureLayer: 无法更新地图数据，地图或数据源不存在', {
        hasMap: !!this._map,
        hasSource: this._map ? !!this._map.getSource(this.sourceId) : false
      });
    }
  }

  /**
   * @description 设置要素可见性（已简化，所有要素默认可见）
   * @param featureId - 要素ID
   * @param visible - 是否可见
   */
  setFeatureVisibility(featureId: number, visible: boolean): boolean {
    // 由于PlotFeature不再包含visible字段，该方法暂时保留接口兼容性
    console.log(`要素可见性设置请求: ${featureId} -> ${visible}（当前所有要素默认可见）`);
      return true;
  }

  /**
   * @description 清空所有要素
   */
  clearAllFeatures(): void {
    this.features = [];
    this.updateMapData();
  }

  /**
   * @description 缩放到要素范围
   * @param featureId - 要素ID，如果不指定则缩放到所有要素
   */
  zoomToFeature(featureId?: number): void {
    if (!this._map) return;

    let targetFeatures: PlotFeature[];
    if (featureId) {
      const feature = this.getFeature(featureId);
      if (!feature) return;
      targetFeatures = [feature];
    } else {
      targetFeatures = this.features; // 所有要素都默认可见
    }

    if (targetFeatures.length === 0) return;

    // 计算边界框
    let minLng = Infinity, minLat = Infinity;
    let maxLng = -Infinity, maxLat = -Infinity;

    targetFeatures.forEach(feature => {
      const coordinates = this.extractCoordinates(feature.geojson.geometry);
      coordinates.forEach(coord => {
        minLng = Math.min(minLng, coord[0]);
        maxLng = Math.max(maxLng, coord[0]);
        minLat = Math.min(minLat, coord[1]);
        maxLat = Math.max(maxLat, coord[1]);
      });
    });

    // 添加边距
    const padding = 0.001;
    const bounds: [[number, number], [number, number]] = [
      [minLng - padding, minLat - padding],
      [maxLng + padding, maxLat + padding]
    ];

    this._map.fitBounds(bounds, { padding: 50 });
  }

  /**
   * @description 提取几何体的坐标
   * @param geometry - GeoJSON几何体
   */
  private extractCoordinates(geometry: any): number[][] {
    const coords: number[][] = [];
    
    switch (geometry.type) {
      case 'Point':
        coords.push(geometry.coordinates);
        break;
      case 'LineString':
        coords.push(...geometry.coordinates);
        break;
      case 'Polygon':
        coords.push(...geometry.coordinates[0]);
        break;
    }
    
    return coords;
  }

  /**
   * @description 获取图层应该插入的位置
   * @returns 应该插入在哪个图层之前的ID，如果应该插入到顶层则返回undefined
   * @private
   */
  private getInsertBeforeId(): string | undefined {
    if (!this._map) return undefined
    
    console.log('=== 标绘图层插入位置分析 ===')
    
    const allLayers = this._map.getStyle().layers || []
    console.log(`地图中共有 ${allLayers.length} 个图层`)
    
    // 记录所有图层信息用于调试
    allLayers.forEach((layer, index) => {
      const isBase = this.isBaseLayer(layer.id)
      console.log(`图层 ${index}: ${layer.id} (${layer.type}) - ${isBase ? '基础图层' : '其他图层'}`)
    })
    
    // 策略：标绘图层总是添加到最顶层
    // 这样可以确保标绘图层在所有基础图层之上
    console.log('策略：标绘图层将添加到最顶层')
    console.log('=== 插入位置分析完成 ===')
    
    // 返回 undefined 表示添加到最顶层
    return undefined
  }

  /**
   * @description 判断是否为基础图层
   * @param layerId - 图层ID
   * @returns 是否为基础图层
   * @private
   */
  private isBaseLayer(layerId: string): boolean {
    // 精确匹配基础图层的ID格式
    const baseLayerIds = [
      // 天地图图层
      'tdt-vector', 'tdt-satellite', 'tdt-terrain',
      // 高德地图图层
      'amap-normal', 'amap-satellite',
      // 百度地图图层
      'baidu-normal', 'baidu-satellite',
    ]
    
    // 精确匹配基础图层ID
    if (baseLayerIds.includes(layerId)) {
      return true
    }
    
    // 匹配注记图层（基础图层的附属图层）
    if (layerId.endsWith('-annotation')) {
      return true
    }
    
    // 匹配高德地图的道路和标注图层
    if (layerId.endsWith('_road') || layerId.endsWith('_label')) {
      return true
    }
    
    console.log(`图层 ${layerId} 被识别为: ${baseLayerIds.includes(layerId) || layerId.endsWith('-annotation') ? '基础图层' : '非基础图层'}`)
    return false
  }
} 