<template>
  <div class="common-layout">
    <el-container>
      <el-header v-if="!isAnalysisOnly">
        <Header />
      </el-header>
      <el-container>
        <el-aside v-if="!isOnlyPipeHistoryMode" :class="{ collapse: isCollapse }">
          <Aside />
        </el-aside>
        <el-main>
          <div class="bread-container" v-if="route.name != 'CesiumHome' && !isAnalysisOnly">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                class="bread"
                :class="[index == tabs.length - 1 ? 'last-bread' : '']"
                v-for="(item, index) in tabs"
                :key="index"
                :to="item.path"
                >{{ item.meta.title }}</el-breadcrumb-item
              >
            </el-breadcrumb>
          </div>
          <div :class="isAnalysisOnly ? 'h-full' : route.name != 'CesiumHome' ? 'view' : 'h-full'">
            <router-view />
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
// import { login } from "@/api/login";
// import localCache from "@/utils/auth";
import type { RouteLocationMatched } from "vue-router";

const layoutStore = useLayoutStore();
const isCollapse = computed(() => layoutStore.isCollapse);
const analysisModeStore = useAnalysisModeStore();


const route = useRoute();
const isAnalysisOnly = computed(() => analysisModeStore.isAnalysisOnly);
const isOnlyPipeHistoryMode = computed(() => analysisModeStore.isPipeHistoryMode)
const tabs: Ref<RouteLocationMatched[]> = ref([]);
const getBreadCom = () => {
  let mached = route.matched.filter((item) => item.meta.title);
  tabs.value = mached;
};

watch(
  () => route.path,
  () => getBreadCom()
);

onMounted(() => {
  getBreadCom();
});
</script>

<style scoped lang="scss">
.common-layout,
.el-container {
  height: 100%;
}
.el-header {
  padding: unset;
}
.el-aside {
  width: 240px;
  // box-shadow: 2px 0 12px 0 rgba($color: #c4d8ff, $alpha: 0.4);
  animation: aside-expand 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}
.collapse {
  width: 60px;
  animation: aside-collapse 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes aside-expand {
  from {
    width: 60px;
  }
  to {
    width: 240px;
  }
}
@keyframes aside-collapse {
  from {
    width: 240px;
  }
  to {
    width: 60px;
  }
}

.el-main {
  position: relative;
  padding: unset;
}
.bread-container {
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 20px;
}
.view {
  height: calc(100% - 40px);
  // height: 100%;
}

:deep(.el-breadcrumb) {
  .bread {
    font-size: 12px;
    .el-breadcrumb__inner {
      font-weight: normal;
      color: #97999D
    }
  }
  .last-bread {
    .el-breadcrumb__inner {
      color: var(--el-text-color-primary);
    }
  }
}
</style>
