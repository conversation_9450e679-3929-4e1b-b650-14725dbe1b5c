import hRequest from '@/utils/http'
import type { DataType } from '@/utils/http/types'
// 列表
export const routeInfoList = () => {
  return hRequest.get<DataType>({
    url: '/business/route/info/list'
  })
}
// 新增
export const routeAdd = (data: any) => {
  return hRequest.post<DataType>({
    url: '/business/route/info',
    data
  })
}
// 修改
export const routeEdit = (data: any) => {
  return hRequest.put<DataType>({
    url: '/business/route/info',
    data
  })
}
// 详情
export const routeDetail = (id: string) => {
  return hRequest.get<DataType>({
    url: `/business/route/info/${id}`,
  })
}
// 删除
export const routeDelete = (id: string) => {
  return hRequest.delete<DataType>({
    url: `/business/route/info/${id}`,
  })
}