<!--
 * @Author: xiao
 * @Date: 2024-07-03 14:52:08
 * @LastEditors: xiao
 * @LastEditTime: 2024-07-23 09:08:48
 * @Description: 
-->
<template>
  <el-sub-menu
    ref="subMenu"
    :index="routerInfo?.name"
    class="gva-sub-menu dark:text-slate-300"
  >
    <template #title>
      <div class="icon-wrapper">
        <img
          v-if="isFirstMenu() && routerInfo?.icon"
          class="size-22px"
          :src="getImages(`aside/${routerInfo?.icon}.png`)"
          alt=""
          v-show="!isActive()"
        />
        <img
          v-if="isFirstMenu() && routerInfo?.icon"
          class="size-22px"
          :src="getImages(`aside/${routerInfo?.icon}-ac.png`)"
          alt=""
          v-show="isActive()"
        />
      </div>
      <span class="menu-item-label">
        {{ routerInfo?.name }}
      </span>
    </template>
    <slot />
  </el-sub-menu>
</template>

<script setup lang="ts">
import { getImages } from "@/utils/getImages";
defineOptions({
  name: "AsyncSubmenu",
});

const props = withDefaults(
  defineProps<{
    routerInfo: {
      [key: string]: any;
    } | null;
  }>(),
  {
    routerInfo: null,
  }
);

const route = useRoute();

const paths = [
  "/query",
  "/pipeNetwork",
  "/special",
  "/dataEdition",
  "/log",
  "/server",
];

const isFirstMenu = () => {
  const path = route.fullPath.split("/")[1];
  const currPaths = paths.map((item) => `/${path}${item}`);
  return currPaths.includes(props.routerInfo?.path);
};

const isActive = () => {
  return route.path.includes(props.routerInfo?.path);
};

const setIcon = (val: string) => {
  return getImages(`aside/${val}.png`);
};
</script>

<style scoped lang="scss">
.gva-sub-menu {
  .el-sub-menu__title {
    height: 48px;
  }
}
</style>
