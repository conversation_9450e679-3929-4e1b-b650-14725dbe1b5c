<template>
  <custom-card
    @closeHandler="close"
    v-show="mainItem.show"
    :main-item="mainItem"
    :title="mainItem.title"
    :top="'16px'"
  >
    <div class="layer-manage">
      <el-tree
        :data="layerdata"
        :props="defaultProps"
        @node-click="handleNodeClick"
        node-key="id"
        default-expand-all
        class="layer-tree custom-tree-default"
        highlight-current
        ref="treeRef"
      >
        <template v-slot="{ node, data }">
          <div
            class="custom-tree-node"
            @mouseenter="currNode = node.id"
            @mouseleave="currNode = null"
          >
            <el-tooltip placement="top" :show-after="500">
              <span
                class="layer-name"
                tabindex="-1"
                @blur="() => onBlur(data)"
                >{{ data.title }}</span
              >
              <template #content>
                {{ data.title }}
              </template>
            </el-tooltip>

            <div class="layer-operate">
              <!-- 透明度滑动条 -->
              <div
                class="transparency-control"
                @click.stop
                v-if="data.type === 'reality' || data.type === 'pipeline'"
              >
                <el-tooltip content="透明度" placement="left" :show-after="500">
                  <div class="transparency-wrapper">
                    <!-- <el-icon class="transparency-icon">
                    <View />
                  </el-icon> -->
                    <el-slider
                      v-model="data.transparency"
                      :min="0"
                      :max="100"
                      :step="5"
                      :show-tooltip="true"
                      :format-tooltip="(val: number) => `${val}%`"
                      size="small"
                      class="transparency-slider"
                      @change="(value: any) => setLayerTransparency(data, node, Array.isArray(value) ? value[0] : value)"
                    />
                  </div>
                </el-tooltip>
              </div>

              <!-- 显示/隐藏开关 -->
              <el-switch
                class="layer-switch custom-switch"
                v-model="data.show"
                :active-value="true"
                :inactive-value="false"
                @click.stop="() => {}"
                @change="(value: any) => setLayerVisible(data, node, value)"
              >
              </el-switch>
            </div>
          </div>
        </template>
      </el-tree>
    </div>
  </custom-card>
</template>
<script lang="ts" setup>
import {
  onMounted,
  onUnmounted,
  type PropType,
  reactive,
  type Ref,
  ref,
} from "vue";
import type Node from "element-plus/es/components/tree/src/model/node";
import { AppCesium } from "@/lib/cesium/AppCesium";
import { Subscription } from "rxjs";
import type { LayerItem } from "@/lib/cesium/layer/type/LayerItem";
import { View } from "@element-plus/icons-vue";
let defaultProps: any = reactive({
  children: "children",
  label: "title",
  class: "custom-class",
});

/**
 * 组件属性定义
 */
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
  onTreeClick: Function,
});

const close = () => {
  useDialogStore().closeDialog("LayerTree");
};

const emits = defineEmits(["onLoseFocus", "onDblClick"]);
let currNode: Ref<any> = ref();
let layerdata = ref([] as LayerItem[]);
const treeRef = ref();
const onBlur = (data: any) => {
  setTimeout(() => {
    let key = treeRef.value.getCurrentKey();
    if (key === data.id) {
      treeRef.value.setCurrentKey(null);
      emits("onLoseFocus", key);
    }
  }, 200);
};
let currParentId: number | null = null;
const setLayerVisible = (data: any, node: Node, visible: boolean) => {
  checkedChange(data, node, "show", true, false);
  AppCesium.getInstance().getLayerManager().setLayerVisible(data.id, visible);
};

/**
 * 设置图层透明度
 * @param data 图层数据
 * @param node 树节点
 * @param transparency 透明度值 (0-100)
 */
const setLayerTransparency = (data: any, node: Node, transparency: number) => {
  console.log(`设置图层 ${data.title} 透明度: ${transparency}%`);

  // 将百分比转换为0-1的透明度值
  const alphaValue = transparency / 100;

  // TODO: 这里将来实现具体的透明度设置逻辑
  // 根据图层类型调用不同的透明度设置方法
  // 例如：
  AppCesium.getInstance()
    .getLayerManager()
    .setLayerTransparency(data.id, alphaValue);

  // 暂时只在控制台输出，实际功能待实现
  console.log(
    `图层ID: ${data.id}, 透明度: ${transparency}%, Alpha值: ${alphaValue}`
  );
};

let nodeCount = 0;
let preNodeId: any = null;
let curNodeId = null;
let nodeTimer: any = null;
const handleNodeClick = (data: any, node: any, prop: any) => {
  nodeCount++;
  if (preNodeId && nodeCount >= 2) {
    curNodeId = data.id;
    nodeCount = 0;
    if (curNodeId == preNodeId) {
      //第一次点击的节点和第二次点击的节点id相同
      emits("onDblClick", data);
      AppCesium.getInstance().getLayerManager().flyToLayer(curNodeId!);
      curNodeId = null;
      preNodeId = null;
      return;
    }
  }
  preNodeId = data.id;
  nodeTimer = setTimeout(() => {
    //300ms内没有第二次点击就把第一次点击的清空
    if (nodeCount === 1) {
      // if (props.onTreeClick) {
      //   let layer = LayerManager.Manager().findLayerById(data.id);
      //   props.onTreeClick(layer);
      // }
    }
    preNodeId = null;
    nodeCount = 0;
  }, 300);
};

const parentVisible = (
  node: Node,
  prop: string,
  trueValue: any,
  falseValue: any
) => {
  const parent = node.parent;
  if (parent?.data.children) {
    let res = parent.data.children.find(
      (layer: any) => layer[prop] === trueValue
    );
    parent.data[prop] = res ? trueValue : falseValue;
    parentVisible(parent, prop, trueValue, falseValue);
  }
};
const childrenVisible = (data: any, prop: string, value: any) => {
  data.children?.forEach((layer: any) => {
    layer[prop] = value;
    childrenVisible(layer, prop, value);
  });
};
/**
 * 控制选中状态
 */
const checkedChange = (
  data: any,
  node: any,
  prop: string,
  trueValue: any,
  falseValue: any
) => {
  if (data) {
    parentVisible(node, prop, trueValue, falseValue);
    // 控制父级显示
    // let parent: LayerItem = LayerManager.Manager().findLayerByIdRecursion(data.parentId, layerdata.value as any) as any;
    // if (parent) {
    //   //let res = parent.children.find(layer => layer.visible === 1)
    //   //parent.visible = !!res ? 1 : 0;
    //   parent.visible = data.visible
    //   // let layer: LayerItem = LayerManager.Manager().findLayerById(parent.id);
    //   // LayerManager.Manager().setLayerVisible(layer, parent.visible === 1);
    // }
    // 控制子级显示
    childrenVisible(data, prop, data[prop]);
  }
};

/**
 * 初始化图层透明度属性
 * @param layers 图层数组
 */
const initLayerTransparency = (layers: LayerItem[]) => {
  layers.forEach((layer) => {
    // 如果图层没有透明度属性，初始化为100（完全不透明）
    if (layer.transparency === undefined) {
      layer.transparency = 100;
    }
    // 递归处理子图层
    if (layer.children && layer.children.length > 0) {
      initLayerTransparency(layer.children);
    }
  });
};

let subscription: Subscription | null = null;
// 加载图层
onMounted(() => {
  const layerManager = AppCesium.getInstance().getLayerManager();
  if (layerManager) {
    subscription = layerManager.layerChanged.subscribe((layers: any) => {
      const layersData = layers ?? [];
      // 初始化透明度属性
      initLayerTransparency(layersData);
      layerdata.value = layersData;
    });
  }
});
onUnmounted(() => {
  if (subscription) {
    subscription.unsubscribe();
  }
});
</script>
<style lang="scss" scoped>
.layer-manage {
  width: inherit;
  height: 476px;
  width: 250px;
  background: transparent;
  pointer-events: auto;
}

.layer-name {
  line-height: 32px;
}

.layer-name-invalid {
  line-height: 32px;
  color: #f00;
}

.layer-icon-buttom {
  width: 16px;
  height: 16px;
  border: 1px solid #059af2;
  border-radius: 8px;
  color: #059af2;
  margin: 0 5px;
  vertical-align: middle;
}

.layer-operate {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  // line-height: 32px;
}

.custom-tree-node {
  display: flex;
  width: 100%;
  height: 100%;
  // justify-content: space-between;
  align-items: center;
  position: relative;
}

.layer-switch {
  margin-left: 10px;
}

// 透明度控制样式
.transparency-control {
  display: flex;
  align-items: center;
  min-width: 80px;
}

.transparency-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
  width: 100%;
}

.transparency-icon {
  color: #409eff;
  font-size: 14px;
  flex-shrink: 0;
}

.transparency-slider {
  width: 60px;
  margin: 0;

  :deep(.el-slider__runway) {
    height: 4px;
    background-color: #e4e7ed;
  }

  :deep(.el-slider__bar) {
    background-color: #409eff;
  }

  :deep(.el-slider__button) {
    width: 12px;
    height: 12px;
    border: 2px solid #409eff;
    background-color: #fff;
  }

  :deep(.el-slider__button:hover) {
    transform: scale(1.1);
  }
}
</style>
