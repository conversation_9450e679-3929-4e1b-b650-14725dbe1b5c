/** * @description: maplibre首页 */
<template>
  <div class="device-control-panel">
    <div class="device-list">
      <div
        v-for="layer in layerdata"
        :key="layer.id"
        class="device-item"
        :style="{
          width: layer.name.length > 6 ? '250px' : '120px'
        }"
        :class="{ active: layer.show }"
        @click="toggleLayerVisible(layer)"
      >
        <div class="device-icon">
          <img
            :src="getLayerIcon(layer)"
            :alt="layer.name"
            class="icon-image"
          />
        </div>
        <span class="device-name">{{ layer.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import type { Subscription } from 'rxjs';
import type { LayerItem } from '@/lib/maplibre/layer/type/LayerItem';
import { getImages } from '@/utils/getImages';

/**
 * 获取图层图标
 * @param layer - 图层对象
 * @returns 图标路径
 */
const getLayerIcon = (layer: LayerItem) => {
  // 如果是设备类图层，使用原有的图标系统
  if (layer.type === 'device' && layer.options?.icon) {
    return getImages(`device-svg/${layer.options.icon}${layer.show ? '-ac' : ''}.svg`)
  }
  
  // 如果是管线类图层，使用配置的图标或默认图标
  if (layer.type === 'mvt') {
    const iconName = layer.options?.icon || 'flow'; // 使用配置的图标或默认为 flow
    return getImages(`device-svg/${iconName}${layer.show ? '-ac' : ''}.svg`)
  }
  
  // 对于其他类型的图层，也检查是否有 icon 配置
  if (layer.icon) {
    return getImages(`device-svg/${layer.icon}${layer.show ? '-ac' : ''}.svg`)
  }
  
  // 最后的默认图标
  return getImages(`device-svg/flow${layer.show ? '-ac' : ''}.svg`)
}

/**
 * 切换图层可见性
 * @param layer - 图层对象
 */
const toggleLayerVisible = (layer: LayerItem) => {
  const layerManager = AppMaplibre.getLayerManager()
  if (layerManager) {
    // 如果图层有子图层，需要同时控制所有子图层
    if (layer.children && layer.children.length > 0) {
      const newVisibility = !layer.show;
      
      // 控制父级图层
      layerManager.setLayerVisible(layer.id, newVisibility);
      
      // 递归控制所有子图层
      const toggleChildrenLayers = (children: LayerItem[]) => {
        for (const child of children) {
          layerManager.setLayerVisible(child.id, newVisibility);
          if (child.children && child.children.length > 0) {
            toggleChildrenLayers(child.children);
          }
        }
      };
      
      toggleChildrenLayers(layer.children);
    } else {
      // 普通图层，直接切换
      layerManager.setLayerVisible(layer.id, !layer.show)
    }
  }
}

/**
 * 递归收集所有带有 showLayerControl 标记的图层
 * @param layers - 图层数组
 * @returns 带有控制标记的图层数组
 */
const collectLayersWithControl = (layers: LayerItem[]): LayerItem[] => {
  const result: LayerItem[] = [];
  
  const traverse = (layerList: LayerItem[]) => {
    for (const layer of layerList) {
      // 检查图层的 showLayerControl 标记（支持在 options 中或直接在图层上）
      const hasLayerControl = layer.showLayerControl || 
                             (layer.options && layer.options.showLayerControl);
      
      if (hasLayerControl) {
        // 如果父级标记了 showLayerControl，将父级图层添加到控制面板
        // 同时更新图层状态以反映子图层的总体状态
        const layerWithStatus = { ...layer };
        
        if (layer.children && layer.children.length > 0) {
          // 计算子图层的整体可见状态
          layerWithStatus.show = calculateGroupVisibility(layer.children);
        }
        
        result.push(layerWithStatus);
      } else {
        // 如果当前层级没有 showLayerControl，继续遍历子图层
        if (layer.children && layer.children.length > 0) {
          traverse(layer.children);
        }
      }
    }
  };
  
  traverse(layers);
  return result;
}

/**
 * 计算图层组的整体可见状态
 * @param children - 子图层数组
 * @returns 如果所有子图层都可见返回 true，否则返回 false
 */
const calculateGroupVisibility = (children: LayerItem[]): boolean => {
  const checkVisibility = (layerList: LayerItem[]): boolean => {
    for (const layer of layerList) {
      if (layer.children && layer.children.length > 0) {
        // 递归检查子图层
        if (!checkVisibility(layer.children)) {
          return false;
        }
      } else {
        // 叶子节点，检查其可见性
        if (!layer.show) {
          return false;
        }
      }
    }
    return true;
  };
  
  return checkVisibility(children);
}

// ============ 响应式状态 ============
let layerdata = ref([] as LayerItem[])

let subscription: Subscription | null = null

// 加载图层
onMounted(async () => {
  await nextTick()
  const layerManager = AppMaplibre.getLayerManager()
  if (layerManager) {
    subscription = layerManager.layerChanged.subscribe((layers: any) => {
      if (layers.length > 0) {
        // 收集所有带有 showLayerControl 标记的图层
        const controlLayers = collectLayersWithControl(layers);
        layerdata.value = controlLayers;
        console.log('图层控制面板加载的图层:', controlLayers.map(l => ({ id: l.id, name: l.name, type: l.type })));
      } else {
        layerdata.value = []
      }
    })
  }
})

onUnmounted(() => {
  if (subscription) {
    subscription.unsubscribe()
  }
})
</script>

<style scoped lang="scss">
.device-control-panel {
  position: absolute;
  left: 10px;
  top: 10px;
  // width: 160px;
  background: transparents;
  z-index: 1000;
  overflow: hidden;
}

.panel-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);

  .panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    text-align: center;
  }
}

.device-list {
  // padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 2px;
  background: #ffffff;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;

  &:hover {
    background: #f8f9fa;
    border-color: rgba(66, 139, 202, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.active {
    border-radius: 2px;

    box-shadow: 0px 0px 4px 0px rgba(196, 216, 255, 0.7);
    background: rgba(255, 255, 255, 1);

    .device-name {
      color: #2b78fe;
      font-weight: 400;
    }

    .device-icon {
      transform: scale(1.1);
    }
  }

  &:active {
    transform: translateY(0);
  }
}

.device-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;

  .icon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.device-name {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 1;
}

// 响应式设计
@media (max-width: 768px) {
  .device-control-panel {
    width: 260px;
    left: 8px;
    top: 8px;
  }

  .device-item {
    padding: 10px 14px;
  }

  .device-icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }

  .device-name {
    font-size: 13px;
  }
}

// 暗色模式适配（可选）
@media (prefers-color-scheme: dark) {
  .device-control-panel {
    background: rgba(33, 37, 41, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .panel-header {
    background: linear-gradient(135deg, #495057, #343a40);
    border-color: rgba(255, 255, 255, 0.1);

    .panel-title {
      color: #f8f9fa;
    }
  }

  .device-item {
    background: #495057;

    &:hover {
      background: #6c757d;
    }

    &.active {
      background: linear-gradient(135deg, #1a365d, #2c5282);
    }
  }

  .device-name {
    color: #e9ecef;
  }
}
</style>
