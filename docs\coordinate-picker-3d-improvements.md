# 坐标拾取组件三维高程支持改进

## 概述

本文档记录了对坐标拾取组件的改进，主要为Cesium三维地图拾取添加高程信息支持，使用户在复制坐标时能够获得包含经度、纬度、高程的完整三维坐标。

## 改进内容

### 🎯 核心问题：Cesium三维拾取缺少高程信息

**问题描述**：
- ❌ Cesium三维拾取已获取高程，但复制时未包含
- ❌ 用户无法获得完整的三维坐标信息
- ❌ 界面显示不区分二维和三维模式
- ❌ 复制格式不支持高程数据

**解决方案**：
- ✅ 新增三维坐标复制函数
- ✅ 根据地图类型自动选择复制格式
- ✅ 界面显示区分二维/三维模式
- ✅ 提示信息根据地图类型调整

## 技术实现

### 🔧 1. 新增三维坐标复制函数

**文件**：`src/utils/clipboard.ts`

**新增函数**：
```typescript
/**
 * @description 复制三维坐标到剪贴板（包含高程）
 * @param lng - 经度
 * @param lat - 纬度
 * @param alt - 高程（可选）
 * @param precision - 精度（小数位数），默认为6
 * @param altPrecision - 高程精度（小数位数），默认为2
 * @param coordSystem - 坐标系名称，用于提示消息
 * @returns Promise<boolean> 是否成功复制
 */
export async function copy3DCoordinateToClipboard(
  lng: number,
  lat: number,
  alt?: number,
  precision: number = 6,
  altPrecision: number = 2,
  coordSystem: string = '坐标'
): Promise<boolean>
```

**功能特点**：
- 支持可选的高程参数
- 独立的高程精度控制（默认2位小数）
- 自动判断是否包含高程信息
- 兼容二维坐标格式

### 🎨 2. 智能复制逻辑

**文件**：`src/views/tinytools/coordPicker/useCoordinatePicker.ts`

**核心逻辑**：
```typescript
// 根据地图类型决定是否包含高程
if (options.mapType === 'cesium' && typeof alt === 'number' && !isNaN(alt)) {
  // Cesium三维地图，包含高程信息
  return await copy3DCoordinateToClipboard(lng, lat, alt, precision, 2, coordName);
} else {
  // MapLibre二维地图，只包含经纬度
  return await copyCoordinateToClipboard(lng, lat, precision, coordName);
}
```

**判断条件**：
1. **地图类型**：必须是 `cesium`
2. **高程有效性**：`alt` 必须是有效数字
3. **自动降级**：无效高程时自动使用二维格式

### 📊 3. 界面显示优化

**文件**：`src/views/tinytools/coordPicker/index.vue`

#### **坐标格式显示**：
```typescript
const formatCoord = (coord: CoordinatePoint, precision: number): string => {
  // 如果是Cesium三维地图且有高程信息，显示三维坐标
  if (mapType === 'cesium' && typeof coord.alt === 'number' && !isNaN(coord.alt)) {
    return `${coord.lng.toFixed(precision)}, ${coord.lat.toFixed(precision)}, ${coord.alt.toFixed(2)}`;
  }
  
  // 否则只显示经纬度
  return `${coord.lng.toFixed(precision)}, ${coord.lat.toFixed(precision)}`;
};
```

#### **标签动态显示**：
```html
<!-- 根据地图类型显示不同标签 -->
<el-descriptions-item
  :label="mapType === 'cesium' ? 'WGS84 (经度,纬度,高程)' : 'WGS84'"
  class-name="coord-value"
  label-class-name="coord-label"
>
```

#### **提示信息优化**：
```html
<span class="tip-text">
  {{ mapType === 'cesium' ? '点击三维地球获取坐标（包含高程）' : '点击地图获取坐标' }}
</span>
```

## 详细修改内容

### 📝 1. 剪贴板工具函数扩展

| 函数名 | 功能 | 参数 | 返回格式 |
|--------|------|------|----------|
| `copyCoordinateToClipboard` | 二维坐标复制 | lng, lat, precision | `116.123456,39.123456` |
| `copy3DCoordinateToClipboard` | 三维坐标复制 | lng, lat, alt, precision, altPrecision | `116.123456,39.123456,123.45` |

### 🔧 2. 坐标拾取逻辑增强

**高程数据流**：
1. **Cesium拾取**：获取 `cartographic.height`
2. **坐标转换**：保持高程信息传递
3. **存储更新**：所有坐标系保留高程
4. **复制输出**：根据地图类型选择格式

**数据结构**：
```typescript
interface CoordinatePoint {
  lng: number;    // 经度
  lat: number;    // 纬度
  alt?: number;   // 高程（可选）
}
```

### 🎨 3. 用户界面改进

#### **标签显示对比**：

| 地图类型 | WGS84标签 | 显示格式 |
|----------|-----------|----------|
| MapLibre | `WGS84` | `116.123456, 39.123456` |
| Cesium | `WGS84 (经度,纬度,高程)` | `116.123456, 39.123456, 123.45` |

#### **提示信息对比**：

| 地图类型 | 提示信息 |
|----------|----------|
| MapLibre | `点击地图获取坐标` |
| Cesium | `点击三维地球获取坐标（包含高程）` |

### 📐 4. 精度控制

**精度设置**：
- **经纬度精度**：6位小数（约0.1米精度）
- **高程精度**：2位小数（厘米级精度）
- **投影坐标精度**：2位小数（厘米级精度）

**精度对比**：
```typescript
// 经纬度：6位小数
lng: 116.123456
lat: 39.123456

// 高程：2位小数  
alt: 123.45

// 最终格式
"116.123456,39.123456,123.45"
```

## 使用场景

### 🌍 MapLibre二维地图
- **拾取结果**：经度、纬度
- **复制格式**：`116.123456,39.123456`
- **适用场景**：平面地图应用、GIS分析

### 🌐 Cesium三维地球
- **拾取结果**：经度、纬度、高程
- **复制格式**：`116.123456,39.123456,123.45`
- **适用场景**：三维可视化、地形分析、航空应用

## 兼容性说明

### 📱 向后兼容
- ✅ 原有二维坐标功能完全保留
- ✅ MapLibre地图行为不变
- ✅ 现有API接口不受影响

### 🔄 自动适配
- ✅ 根据地图类型自动选择格式
- ✅ 无效高程时自动降级为二维
- ✅ 界面显示自动调整

### 🌐 浏览器支持
- ✅ 所有现代浏览器
- ✅ 移动端浏览器
- ✅ 剪贴板API兼容性处理

## 测试建议

### 🧪 功能测试

#### **MapLibre二维测试**：
1. 点击地图拾取坐标
2. 验证显示格式：`经度, 纬度`
3. 复制坐标验证格式：`经度,纬度`
4. 确认标签显示：`WGS84`

#### **Cesium三维测试**：
1. 点击三维地球拾取坐标
2. 验证显示格式：`经度, 纬度, 高程`
3. 复制坐标验证格式：`经度,纬度,高程`
4. 确认标签显示：`WGS84 (经度,纬度,高程)`

### 📊 数据验证

#### **高程数据测试**：
1. 在不同地形高度点击
2. 验证高程数据合理性
3. 测试海平面附近（高程接近0）
4. 测试高海拔地区（高程较大）

#### **精度测试**：
1. 验证经纬度6位小数精度
2. 验证高程2位小数精度
3. 测试极值情况处理

### 🔄 兼容性测试

#### **地图切换测试**：
1. 从MapLibre切换到Cesium
2. 从Cesium切换到MapLibre
3. 验证界面正确更新
4. 验证功能正常工作

#### **异常情况测试**：
1. 高程数据无效时的处理
2. 网络异常时的行为
3. 剪贴板权限被拒绝的处理

## 后续优化建议

### 🚀 功能增强

#### **1. 高程数据源选择**：
```typescript
interface ElevationOptions {
  source: 'terrain' | 'ellipsoid' | 'auto';
  precision: number;
}
```

#### **2. 坐标格式选项**：
```typescript
interface CoordinateFormat {
  separator: ',' | ' ' | '\t';
  includeLabels: boolean;
  decimalPlaces: number;
}
```

#### **3. 批量坐标拾取**：
```typescript
interface BatchPickingOptions {
  maxPoints: number;
  exportFormat: 'csv' | 'json' | 'txt';
}
```

### 📱 用户体验优化

#### **1. 高程单位显示**：
```html
<span>高程: {{ alt.toFixed(2) }}m</span>
```

#### **2. 坐标预览**：
```html
<div class="coord-preview">
  复制格式预览: {{ previewFormat }}
</div>
```

#### **3. 快捷键支持**：
```typescript
// Ctrl+C 快速复制当前坐标
// Ctrl+R 重置坐标
// Esc 停止拾取
```

## 总结

通过这次改进，坐标拾取组件获得了：

- 🌐 **完整的三维支持**：Cesium模式下包含高程信息
- 🎯 **智能格式选择**：根据地图类型自动选择复制格式
- 🎨 **清晰的界面提示**：用户明确知道当前模式和数据格式
- 🔄 **完全向后兼容**：不影响现有二维地图功能
- 📱 **良好的用户体验**：直观的操作和明确的反馈

这些改进使得坐标拾取组件能够更好地服务于三维GIS应用场景，为用户提供完整准确的空间位置信息。
