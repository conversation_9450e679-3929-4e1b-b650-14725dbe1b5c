
export const useAnalysisModeStore = defineStore("AnalysisModeStore", () => {
  const isAnalysisOnly = ref(false);
  const isPipeHistoryMode = ref(false);

  function setAnalysisMode(value: boolean) {
    isAnalysisOnly.value = value;
  }
  function setPipeHistoryMode(value: boolean) {
    isPipeHistoryMode.value = value;
  }
  

  return {
    isAnalysisOnly,
    setAnalysisMode,
    isPipeHistoryMode,
    setPipeHistoryMode
  };
});