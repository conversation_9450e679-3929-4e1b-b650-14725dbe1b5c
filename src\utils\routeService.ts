/**
 * @description 路线锚点数据接口
 */
export interface RouteStop {
  id: string;
  name: string;
  lon: number;
  lat: number;
  height: number;
  index: number;
}

/**
 * @description 路线数据接口
 */
export interface RouteData {
  id: string;
  name: string;
  value: RouteStop[];
  // geoJson: any;
  // createTime: number;
  // modifyTime: number;
}

/**
 * @description 路线数据服务类
 */
export class RouteService {
  private static readonly STORAGE_KEY = 'cesium_routes';

  /**
   * @description 获取所有路线数据
   */
  static getAllRoutes(): RouteData[] {
    try {
      const routes = localStorage.getItem(this.STORAGE_KEY);
      return routes ? JSON.parse(routes) : [];
    } catch (error) {
      console.error('获取路线数据失败:', error);
      return [];
    }
  }

  /**
   * @description 根据ID获取路线数据
   */
  static getRouteById(id: string): RouteData | null {
    try {
      const routes = this.getAllRoutes();
      return routes.find(route => route.id === id) || null;
    } catch (error) {
      console.error('根据ID获取路线失败:', error);
      return null;
    }
  }

  /**
   * @description 根据名称获取路线数据
   */
  static getRouteByName(name: string): RouteData | null {
    try {
      const routes = this.getAllRoutes();
      return routes.find(route => route.name === name) || null;
    } catch (error) {
      console.error('根据名称获取路线失败:', error);
      return null;
    }
  }

  /**
   * @description 保存路线数据
   */
  static saveRoute(routeData: RouteData): boolean {
    try {
      const routes = this.getAllRoutes();
      const existingIndex = routes.findIndex(route => route.id === routeData.id);
      
      if (existingIndex >= 0) {
        // 更新现有路线
        routes[existingIndex] = routeData;
      } else {
        // 添加新路线
        routes.push(routeData);
      }
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(routes));
      return true;
    } catch (error) {
      console.error('保存路线失败:', error);
      return false;
    }
  }

  /**
   * @description 删除路线
   */
  static deleteRoute(id: string): boolean {
    try {
      const routes = this.getAllRoutes();
      const filteredRoutes = routes.filter(route => route.id !== id);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredRoutes));
      return true;
    } catch (error) {
      console.error('删除路线失败:', error);
      return false;
    }
  }

  /**
   * @description 检查路线是否存在
   */
  static routeExists(id: string): boolean {
    try {
      const routes = this.getAllRoutes();
      return routes.some(route => route.id === id);
    } catch (error) {
      console.error('检查路线存在性失败:', error);
      return false;
    }
  }

  /**
   * @description 检查路线名称是否重复
   */
  static isNameDuplicate(name: string, excludeId?: string): boolean {
    try {
      const routes = this.getAllRoutes();
      return routes.some(route => 
        route.name === name && route.id !== excludeId
      );
    } catch (error) {
      console.error('检查名称重复失败:', error);
      return false;
    }
  }

  /**
   * @description 生成唯一路线ID
   */
  static generateRouteId(): string {
    return `route_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * @description 生成唯一锚点ID
   */
  static generateStopId(): string {
    return `stop_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * @description 生成锚点名称
   */
  static generateStopName(index: number): string {
    return `锚点${index + 1}`;
  }

  /**
   * @description 验证路线数据有效性
   */
  static validateRouteData(data: Partial<RouteData>): string[] {
    const errors: string[] = [];
    
    if (!data.name?.trim()) {
      errors.push('路线名称不能为空');
    }
    
    if (data.name && data.name.length > 50) {
      errors.push('路线名称不能超过50个字符');
    }
    
    if (!data.value || data.value.length < 2) {
      errors.push('路线至少需要2个锚点');
    }
    
    return errors;
  }

  /**
   * @description 更新锚点序号和名称
   */
  static updateStopIndexes(stops: RouteStop[]): RouteStop[] {
    return stops.map((stop, index) => ({
      ...stop,
      index,
      name: this.generateStopName(index)
    }));
  }
}

/**
 * @description 路线事件管理器
 */
export class RouteEventManager {
  private static listeners: Map<string, Function[]> = new Map();

  /**
   * @description 注册事件监听器
   */
  static on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  /**
   * @description 移除事件监听器
   */
  static off(event: string, callback: Function): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * @description 触发事件
   */
  static emit(event: string, data?: any): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('事件回调执行失败:', error);
        }
      });
    }
  }

  /**
   * @description 清理所有监听器
   */
  static clear(): void {
    this.listeners.clear();
  }
}

// 路线事件常量
export const ROUTE_EVENTS = {
  ROUTE_SAVED: 'route:saved',
  ROUTE_DELETED: 'route:deleted',
  ROUTE_UPDATED: 'route:updated',
  ROUTES_REFRESHED: 'routes:refreshed'
} as const; 