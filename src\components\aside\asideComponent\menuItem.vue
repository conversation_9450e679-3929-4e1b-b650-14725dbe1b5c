<!--
 * @Author: xiao
 * @Date: 2024-07-03 14:52:08
 * @LastEditors: xiao
 * @LastEditTime: 2024-07-23 09:08:59
 * @Description: 
-->
<template>
  <el-menu-item :index="routerInfo?.path" class="dark:text-slate-300 h-12">
    <!-- <el-icon v-if="routerInfo?.icon">
      <component :is="routerInfo.icon" />
    </el-icon> -->
    <div class="icon-wrapper" v-show="isFirstMenu() || !isCollapse">
      <img
        v-if="isFirstMenu() && routerInfo?.icon"
        v-show="!isActive"
        class="size-22px"
        :src="getImages(`aside/${routerInfo?.icon}.png`)"
        alt=""
      />
      <img
        v-if="isFirstMenu() && routerInfo?.icon"
        class="size-22px"
        :src="getImages(`aside/${routerInfo?.icon}-ac.png`)"
        alt=""
        v-show="isActive"
      />
      <span
        v-if="!isFirstMenu()"
        class="menu-item-icon"
        :class="isActive ? 'icon-ac' : 'icon-default'"
      ></span>
    </div>
    <template #title>
      <span class="menu-item-label">
        {{ routerInfo?.name }}
      </span>
    </template>
  </el-menu-item>
</template>

<script setup lang="ts">
import { getImages } from "@/utils/getImages";

defineOptions({
  name: "MenuItem",
});
const props = withDefaults(
  defineProps<{
    routerInfo: {
      [key: string]: any;
    } | null;
  }>(),
  {
    routerInfo: null,
  }
);

const layoutStore = useLayoutStore();
const isCollapse = computed(() => layoutStore.isCollapse);

const route = useRoute();

const paths = [
  "/home",
  "/special",
  "/icon",
  "/app",
  "/dict",
  "/user",
  "/role",
  "/dualmap",
];

const isFirstMenu = () => {
  const path = route.fullPath.split("/")[1];
  const currPaths = paths.map((item) => `/${path}${item}`);
  return currPaths.includes(props.routerInfo?.path);
};

const isActive = computed(() => {
  return route.path === props.routerInfo?.path;
});

const setIcon = (val: string) => {
  console.log(val);
  return getImages(`aside/${val}.png`);
};
</script>

<style lang="scss">
.menu-item-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border-width: 1px;
  border-style: solid;
}
.icon-default {
  border-color: var(--el-text-color-primary);
}
.icon-ac {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary);
}
</style>
