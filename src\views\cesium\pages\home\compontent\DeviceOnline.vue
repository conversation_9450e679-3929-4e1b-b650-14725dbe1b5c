<template>
  <BaseCard title="设备在线率">
    <div class="relative">
      <div class="chart" ref="onlineRef"></div>
      <div class="absolute top-12 left-36%">
        <img :src="getImages('cesium/4.png')" alt="" />
      </div>
      <div class="flex justify-between pb-2.5 px-5 box-border">
        <div class="bg-#F3F7FF px-5 box-border">
          <span class="label">在线设备：</span>
          <span class="color-#1966FF font-size-6">215</span>
          <span class="color-#1966FF font-size-2.5 ml-1">个</span>
        </div>
        <div class="bg-#FFF4F4 px-5 box-border">
          <span class="label">离线设备：</span>
          <span class="value">38</span>
          <span class="color-#FF7373 font-size-2.5 ml-1">个</span>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
<script lang="ts" setup>
import { initOnlineEchart } from "@/lib/echarts";
import { getImages } from "@/utils/getImages";
const { initChart } = useEchart();
const onlineRef = ref();
const getChart = () => {
  onlineRef.value && initChart(onlineRef.value, initOnlineEchart());
};
onMounted(() => {
  getChart();
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 180px;
  overflow: hidden;
}
.label {
  color: #2c3037;
  font-size: 14px;
}
.value {
  color: #ff7373;
  font-size: 24px;
}
</style>
