# 百度地图地名地址搜索功能使用指南

## 功能概述

地名地址搜索组件基于百度地图Place API，提供沙湾区地名、地址和兴趣点的智能搜索定位功能。

## 主要特性

### 🔍 智能搜索
- **实时搜索建议**：输入关键词时自动显示相关地点建议
- **模糊匹配**：支持地名、地址、兴趣点标签的模糊搜索
- **防抖优化**：500ms防抖延迟，优化搜索性能

### 📍 精准定位
- **快速定位**：点击搜索结果自动跳转到地图位置
- **平滑动画**：2秒飞行动画，缩放级别16倍
- **区域限制**：搜索范围限定在沙湾区及周边50公里

### 💾 便捷操作
- **搜索历史**：自动保存最近10次搜索记录
- **热门搜索**：预置沙湾区热门地点快速搜索
- **键盘导航**：支持方向键选择、回车确认、ESC取消

### 🎨 优雅界面
- **下拉建议**：实时显示搜索建议列表
- **高亮匹配**：搜索关键词高亮显示
- **标签分类**：地点类型标签，便于识别
- **距离显示**：显示搜索结果与当前位置的距离

## 配置说明

### 1. 百度地图API密钥配置

#### 方法一：环境变量配置（推荐）

在项目根目录创建 `.env` 文件：

```bash
# 百度地图API密钥
VITE_BAIDU_MAP_AK=你的百度地图API密钥
```

#### 方法二：直接配置

编辑 `src/utils/config/baiduMapConfig.ts` 文件：

```typescript
export const BAIDU_MAP_CONFIG: BaiduMapConfig = {
  ak: '你的百度地图API密钥', // 替换这里
  // ... 其他配置
};
```

### 2. 百度地图API密钥申请

1. 访问 [百度地图开放平台](https://lbsyun.baidu.com/apiconsole/key)
2. 注册/登录百度账号
3. 创建应用，获取API密钥(AK)
4. 在应用管理中配置域名白名单

### 3. API服务配置

确保已开启以下服务：
- ✅ 地点搜索服务
- ✅ 地点建议服务
- ✅ 地理编码服务

## 技术实现

### 核心组件

```
PlaceSearchBar.vue          # 主搜索组件
├── 搜索输入框               # 带防抖的输入处理
├── 搜索建议下拉框           # 实时搜索结果显示
├── 搜索历史面板             # 历史记录管理
└── 热门搜索标签             # 快捷搜索入口
```

### 配置管理

```
baiduMapConfig.ts           # 配置文件
├── BAIDU_MAP_CONFIG        # API基础配置
├── SEARCH_ENDPOINTS        # API端点定义
├── HOT_KEYWORDS            # 热门搜索关键词
├── POI_CATEGORIES          # 地点分类配置
└── 工具函数                # API调用辅助函数
```

### 数据流程

```
用户输入 → 防抖处理 → API调用 → 结果处理 → 界面更新 → 地图定位
```

## 使用示例

### 基础使用

```vue
<template>
  <!-- 地名搜索组件 -->
  <div class="place-search-wrapper">
    <PlaceSearchBar />
  </div>
</template>

<script setup>
import PlaceSearchBar from './searchBar/PlaceSearchBar.vue';
</script>

<style>
.place-search-wrapper {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}
</style>
```

### 开发模式

开发环境下使用模拟数据，包含沙湾区真实地名：

- 政府机构：沙湾区人民政府、政务服务中心
- 医疗机构：沙湾区人民医院、中医院
- 教育机构：沙湾实验小学、沙湾中学
- 交通设施：沙湾客运站、沙湾大桥
- 乡镇街道：太平镇、福禄镇、嘉农镇、轸溪乡
- 旅游景点：沙湾古镇、沙湾公园
- 自然地理：大渡河

### 生产模式

配置正确的API密钥后，自动调用百度地图真实API获取搜索结果。

## 自定义配置

### 搜索范围调整

编辑 `baiduMapConfig.ts`：

```typescript
export const BAIDU_MAP_CONFIG = {
  region: '四川省乐山市沙湾区',  // 搜索区域
  radius: 50000,                 // 搜索半径（米）
  center: {
    lng: 103.551,                // 中心经度
    lat: 29.413                  // 中心纬度
  }
};
```

### 热门搜索定制

```typescript
export const HOT_KEYWORDS = [
  '沙湾区政府',
  '沙湾古镇',
  // 添加更多关键词...
];
```

### 搜索结果数量

```typescript
export const SEARCH_OPTIONS = {
  PAGE_SIZE: {
    SUGGESTION: 8,   // 建议数量
    SEARCH: 20       // 搜索结果数量
  }
};
```

## 故障排除

### 常见问题

1. **搜索无结果**
   - 检查API密钥是否正确配置
   - 确认API服务是否开启
   - 查看控制台错误信息

2. **定位失效**
   - 确认MapLibre地图实例已正确初始化
   - 检查坐标系统是否匹配

3. **样式异常**
   - 确认Element Plus组件库正确引入
   - 检查CSS作用域和权重

### 调试方法

开启调试模式：

```javascript
// 在浏览器控制台中查看组件状态
window.mapContainerStatus();
```

## 扩展开发

### 添加新的搜索类型

1. 在 `POI_CATEGORIES` 中定义新分类
2. 在模拟数据中添加对应地点
3. 更新搜索逻辑和UI显示

### 集成其他地图服务

可以参考百度地图API的实现方式，集成高德地图、腾讯地图等其他服务。

## 性能优化

- 防抖输入处理，减少API调用
- 搜索结果缓存（可扩展）
- 组件懒加载
- 虚拟滚动（大量结果时）

## 安全考虑

- API密钥环境变量存储
- 域名白名单配置
- 请求频率限制
- 输入内容过滤

---

*更新时间：2024-01-16*
*版本：v1.0.0* 