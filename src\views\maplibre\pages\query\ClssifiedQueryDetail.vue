<template>
  <custom-card title="详情"
    width="300px"
    left="710px"
    top="10px"
    @closeHandler="handleClose">
    <el-form
        ref="formRef"
        disabled
        :model="detailInfo"
        class="admin-sub-form"
        label-width="auto"
        v-loading="loading"
      >

      <el-form-item label="点号" prop="gxddh">
        <el-input :value="formatValue(detailInfo?.gxddh)" readonly />
      </el-form-item>

      <el-form-item label="管类" prop="gl">
        <el-input :value="formatValue(detailInfo?.gl)" readonly />
      </el-form-item>

      <el-form-item label="属性" prop="sx">
        <el-input :value="formatValue(detailInfo?.sx)" readonly />
      </el-form-item>

      <el-form-item label="附属物" prop="fsw">
        <el-input :value="formatValue(detailInfo?.fsw)" readonly />
      </el-form-item>

      <el-form-item label="地面高程(m)" prop="dmgc">
        <el-input :value="formatValue(detailInfo?.dmgc)" readonly />
      </el-form-item>

      <!-- <el-form-item label="经度" prop="x">
        <el-input :value="detailInfo?.longitude.toFixed(6)" readonly />
      </el-form-item>

      <el-form-item label="纬度" prop="y">
        <el-input :value="detailInfo?.latitude.toFixed(6)" readonly />
      </el-form-item> -->

      <el-form-item label="井深(m)" prop="js">
        <el-input :value="formatValue(detailInfo?.js)" readonly />
      </el-form-item>

      <el-form-item label="所在道路" prop="szdl">
        <el-input :value="formatValue(detailInfo?.szdl)" readonly />
      </el-form-item>

      <el-form-item label="井盖规格(mm)" prop="jggg">
        <el-input :value="formatValue(detailInfo?.jggg)" readonly />
      </el-form-item>

      <el-form-item label="井盖材质" prop="jgcz">
        <el-input :value="formatValue(detailInfo?.jgcz)" readonly />
      </el-form-item>

      </el-form>
  </custom-card>
</template>

<script setup lang="ts">

defineProps<{
  loading: boolean,
  // detalInfo: Record<string,any> | undefined
}>()

const cardVisible = defineModel<boolean>('cardVisible', { required: true })
const detailInfo = defineModel<Record<string,any> | undefined>('detailInfo',{required: true})

/**
 * 格式化显示值，空值显示为"无"
 * @param value 原始值
 * @returns 格式化后的值
 */
const formatValue = (value: any): string => {
  if (value === null || value === undefined || value === '' || (typeof value === 'string' && value.trim() === '')) {
    return '无';
  }
  return String(value);
}

const handleClose = () => {
  cardVisible.value = false
}
</script>

<style scoped>

</style>