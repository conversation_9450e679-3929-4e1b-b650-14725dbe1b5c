/**
 * @fileoverview 天地图图层实现
 * @description 基于新架构的天地图图层类，支持矢量、影像、地形等多种图层类型
 * <AUTHOR>
 * @version 2.0.0
 */

import { BaseLayer } from './BaseLayer'
import { Map as glMap } from 'maplibre-gl'
import { 
  LayerType, 
  LayerStatus,
  type TdtLayerConfig, 
  type LayerLoadOptions,
  type LayerValidationResult
} from '../types/LayerTypes'

/**
 * @interface TdtLayerMapping
 * @description 天地图图层类型映射接口
 */
interface TdtLayerMapping {
  /** 图层代码 */
  code: string
  /** 图层名称 */
  name: string
  /** 是否为注记图层 */
  isAnnotation: boolean
  /** 支持的坐标系 */
  supportedCrs: string[]
}

/**
 * @class TdtImageryLayer
 * @description 天地图图层类，支持矢量、影像、地形等多种类型
 * @extends BaseLayer
 */
export class TdtImageryLayer extends BaseLayer {
  /** 天地图服务器域名列表 */
  private static readonly TDT_DOMAINS = [
    't0.tianditu.gov.cn',
    't1.tianditu.gov.cn', 
    't2.tianditu.gov.cn',
    't3.tianditu.gov.cn',
    't4.tianditu.gov.cn',
    't5.tianditu.gov.cn',
    't6.tianditu.gov.cn',
    't7.tianditu.gov.cn'
  ]

  /** 天地图图层类型映射 */
  private static readonly LAYER_MAPPINGS: Record<string, TdtLayerMapping> = {
    'vec_w': { code: 'vec', name: '矢量底图', isAnnotation: false, supportedCrs: ['3857', '4326'] },
    'cva_w': { code: 'cva', name: '矢量注记', isAnnotation: true, supportedCrs: ['3857', '4326'] },
    'img_w': { code: 'img', name: '影像底图', isAnnotation: false, supportedCrs: ['3857', '4326'] },
    'cia_w': { code: 'cia', name: '影像注记', isAnnotation: true, supportedCrs: ['3857', '4326'] },
    'ter_w': { code: 'ter', name: '地形底图', isAnnotation: false, supportedCrs: ['3857', '4326'] },
    'cta_w': { code: 'cta', name: '地形注记', isAnnotation: true, supportedCrs: ['3857', '4326'] }
  }

  /** 默认天地图令牌 */
  private static readonly DEFAULT_TOKEN = 'b254904598a72fd14661de55fa70511d'

  /** 当前图层配置 */
  private config: TdtLayerConfig
  
  /** 重试计数器 */
  private retryCount = 0

  /**
   * @constructor
   * @param options - 天地图图层配置
   */
  constructor(options: TdtLayerConfig) {
    super(options)
    this.config = options
    this._type = LayerType.TDT
  }

  /**
   * @description 验证图层配置
   * @returns 验证结果
   */
  validate(): LayerValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // 验证必需参数
    if (!this.config.layer) {
      errors.push('缺少必需参数: layer')
    }

    if (!this.config.crs) {
      errors.push('缺少必需参数: crs')
    }

    // 验证图层类型
    if (this.config.layer && !TdtImageryLayer.LAYER_MAPPINGS[this.config.layer]) {
      errors.push(`不支持的图层类型: ${this.config.layer}`)
    }

    // 验证坐标系
    if (this.config.crs && !['3857', '4326'].includes(this.config.crs)) {
      errors.push(`不支持的坐标系: ${this.config.crs}`)
    }

    // 验证令牌
    if (!this.config.token) {
      warnings.push('未提供天地图令牌，将使用默认令牌（可能有访问限制）')
    }

    // 验证图层和坐标系的兼容性
    if (this.config.layer && this.config.crs) {
      const mapping = TdtImageryLayer.LAYER_MAPPINGS[this.config.layer]
      if (mapping && !mapping.supportedCrs.includes(this.config.crs)) {
        errors.push(`图层 ${this.config.layer} 不支持坐标系 ${this.config.crs}`)
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * @description 构建天地图瓦片URL列表
   * @returns 瓦片URL数组
   */
  private buildTileUrls(): string[] {
    const mapping = TdtImageryLayer.LAYER_MAPPINGS[this.config.layer]
    if (!mapping) {
      throw new Error(`不支持的天地图图层类型: ${this.config.layer}`)
    }

    const layerCode = mapping.code
    const tileMatrixSetID = this.config.crs === '3857' ? 'w' : 'c'
    const token = this.config.token || TdtImageryLayer.DEFAULT_TOKEN

    const baseParams = [
      `tk=${token}`,
      'SERVICE=WMTS',
      'REQUEST=GetTile',
      'VERSION=1.0.0',
      `LAYER=${layerCode}`,
      'STYLE=default',
      `TILEMATRIXSET=${tileMatrixSetID}`,
      'FORMAT=tiles',
      'TILECOL={x}',
      'TILEROW={y}',
      'TILEMATRIX={z}'
    ].join('&')

    return TdtImageryLayer.TDT_DOMAINS.map(domain => 
      `https://${domain}/${layerCode}_${tileMatrixSetID}/wmts?${baseParams}`
    )
  }

  /**
   * @description 图层内部添加实现
   * @param map - MapLibre地图实例
   * @param isRefresh - 是否为刷新操作
   * @param options - 加载选项
   * @returns 图层代理对象
   */
  async addToInternal(map: glMap, isRefresh = false, options?: LayerLoadOptions): Promise<any> {
    try {
      // 验证配置
      const validation = this.validate()
      if (!validation.valid) {
        throw new Error(`天地图图层配置无效: ${validation.errors.join(', ')}`)
      }

      // 输出警告信息
      if (validation.warnings.length > 0) {
        console.warn('天地图图层配置警告:', validation.warnings.join(', '))
      }

      // 构建瓦片URL
      const tileUrls = this.buildTileUrls()
      
      // 记录性能指标
      const startTime = performance.now()

      // 添加数据源
      if (map.getSource(this._id)) {
        map.removeSource(this._id)
      }

      map.addSource(this._id, {
        type: 'raster',
        tiles: tileUrls,
        tileSize: this.config.tileSize || 256,
        minzoom: this._minZoom || 0,
        maxzoom: this._maxZoom || 18
      })

      // 添加图层
      if (map.getLayer(this._id)) {
        map.removeLayer(this._id)
      }

      map.addLayer({
        id: this._id,
        type: 'raster',
        source: this._id,
        paint: {
          'raster-opacity': this._opacity
        }
      })

      // 获取图层代理对象
      const delegate = map.getLayer(this._id)
      
      // 更新性能指标
      this._performanceMetrics.renderTime = performance.now() - startTime

      // 发射加载完成事件
      this.emit('loaded', {
        layerId: this._id,
        type: 'loaded',
        data: { 
          sourceId: this._id, 
          tileUrls,
          layer: this.config.layer,
          crs: this.config.crs,
          loadTime: this._performanceMetrics.renderTime
        },
        timestamp: Date.now()
      })

      console.log(`天地图图层 ${this._name} 添加成功`, {
        layer: this.config.layer,
        crs: this.config.crs,
        tileCount: tileUrls.length,
        loadTime: this._performanceMetrics.renderTime
      })

      return delegate

    } catch (error) {
      // 错误处理和重试逻辑
      this._performanceMetrics.errorCount = (this._performanceMetrics.errorCount || 0) + 1
      
      if (this.retryCount < (options?.retryCount || 3)) {
        this.retryCount++
        console.warn(`天地图图层加载失败，正在重试 (${this.retryCount}/3):`, error)
        
        // 延迟重试
        await new Promise(resolve => setTimeout(resolve, 1000 * this.retryCount))
        return this.addToInternal(map, isRefresh, options)
      }

      console.error(`天地图图层 ${this._name} 加载失败:`, error)
      
      // 发射错误事件
      this.emit('error', {
        layerId: this._id,
        type: 'error',
        data: { 
          error: error instanceof Error ? error : new Error(String(error)),
          message: error instanceof Error ? error.message : '天地图图层加载失败',
          layer: this.config.layer,
          crs: this.config.crs
        },
        timestamp: Date.now()
      })
      
      throw error
    }
  }

  /**
   * @description 移除图层内部实现
   */
  removeInternal(): void {
    if (!this._map) return
    try {
      // 移除图层
      if (this._map.getLayer(this._id)) {
        this._map.removeLayer(this._id)
      }

      // 移除数据源
      if (this._map.getSource(this._id)) {
        this._map.removeSource(this._id)
      }

      // 发射移除事件
      this.emit('removed', {
        layerId: this._id,
        type: 'removed',
        data: null,
        timestamp: Date.now()
      })

      console.log(`天地图图层 ${this._name} 移除成功`)

    } catch (error) {
      console.error(`天地图图层 ${this._name} 移除失败:`, error)
      throw error
    }
  }

  /**
   * @description 更新图层显示状态
   * @param show - 是否显示
   */
  protected showInternal(show: boolean): void {
    // 主图层由父类BaseLayer控制，这里只需要调用父类方法处理delegate
    // 父类已经会触发visibilityChanged事件，无需重复触发
    super.showInternal(show);
  }

  /**
   * @description 设置图层透明度
   * @param opacity - 透明度值 (0-1)
   */
  set opacity(opacity: number) {
    this._opacity = Math.max(0, Math.min(1, opacity))
    
    if (this._map && this._map.getLayer(this._id)) {
      this._map.setPaintProperty(this._id, 'raster-opacity', this._opacity)
    }

    this.emit('opacityChanged', {
      layerId: this._id,
      type: 'opacityChanged',
      data: { opacity: this._opacity },
      timestamp: Date.now()
    })
  }

  /**
   * @description 获取图层信息
   * @returns 图层信息对象
   */
  getLayerInfo(): Record<string, any> {
    const mapping = TdtImageryLayer.LAYER_MAPPINGS[this.config.layer]
    
    return {
      id: this._id,
      name: this._name,
      type: this._type,
      status: this._status,
      config: this.config,
      mapping: mapping,
      isAnnotation: mapping?.isAnnotation || false,
      performanceMetrics: this._performanceMetrics,
      retryCount: this.retryCount
    }
  }

  /**
   * @description 获取支持的图层类型列表
   * @static
   * @returns 支持的图层类型数组
   */
  static getSupportedLayers(): TdtLayerMapping[] {
    return Object.entries(TdtImageryLayer.LAYER_MAPPINGS).map(([key, value]) => ({
      ...value,
      layer: key
    })) as TdtLayerMapping[]
  }
}
