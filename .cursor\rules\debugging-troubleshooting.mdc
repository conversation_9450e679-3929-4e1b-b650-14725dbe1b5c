---
description: 
globs: 
alwaysApply: false
---
# 调试与故障排除指南

## 管网渲染器调试策略

### 临时图层不显示问题
当遇到管点或管线不显示时，按以下顺序检查：

1. **验证渲染器初始化**
   ```typescript
   // 检查渲染器是否正确初始化
   console.log('渲染器状态:', pipeRenderer?.getRenderStats());
   ```

2. **检查数据源创建**
   ```typescript
   // 验证数据源是否存在
   const nodeSource = map.getSource('pipe-nodes-source');
   const lineSource = map.getSource('pipe-lines-source');
   console.log('数据源状态:', { nodeSource, lineSource });
   ```

3. **验证图层添加**
   ```typescript
   // 检查图层是否成功添加到地图
   const nodeLayer = map.getLayer('pipe-nodes');
   const lineLayer = map.getLayer('pipe-lines');
   console.log('图层状态:', { nodeLayer, lineLayer });
   ```

4. **检查要素数据**
   ```typescript
   // 验证要素数据格式和内容
   console.log('要素数据:', {
     nodes: nodes.length,
     lines: lines.length,
     nodeData: nodes[0],
     lineData: lines[0]
   });
   ```

### MapLibre GL 相关问题

#### 图层顺序问题
```typescript
// 检查图层层级
const allLayers = map.getStyle().layers;
console.log('所有图层顺序:', allLayers.map(l => l.id));

// 确保管网图层在顶层
pipeRenderer.ensurePipeLayersOnTop();
```

#### 样式不生效问题
```typescript
// 检查图层样式配置
const layer = map.getLayer('pipe-nodes');
if (layer) {
  console.log('图层样式:', layer.paint);
  console.log('图层布局:', layer.layout);
}
```

### 数据格式验证

#### GeoJSON 格式检查
```typescript
// 验证 GeoJSON 要素格式
const validateGeoJSON = (feature: any) => {
  const required = ['type', 'geometry', 'properties'];
  const missing = required.filter(key => !feature[key]);
  if (missing.length > 0) {
    console.error('GeoJSON 格式错误，缺少字段:', missing);
  }
  return missing.length === 0;
};
```

#### 属性完整性检查
```typescript
// 检查管点属性
const validatePipeNode = (node: PipeNode) => {
  const required = ['id', 'name', 'geojson'];
  const geoRequired = ['nodeType', 'nodeCode', 'wellDepth'];
  
  // 检查基础属性
  // 检查 GeoJSON 属性
};
```

## 性能调试

### 渲染性能监控
```typescript
// 使用性能标记监控渲染时间
performance.mark('render-start');
pipeRenderer.renderFeatures(nodes, lines);
performance.mark('render-end');
performance.measure('render-duration', 'render-start', 'render-end');
```

### 内存泄漏检查
```typescript
// 在组件销毁时检查资源清理
onUnmounted(() => {
  console.log('销毁前图层数量:', map.getStyle().layers.length);
  pipeRenderer?.destroy();
  console.log('销毁后图层数量:', map.getStyle().layers.length);
});
```

### 更新频率监控
```typescript
// 监控更新队列大小
console.log('更新队列大小:', pipeRenderer.updateQueue.size);
console.log('活动图层数量:', pipeRenderer.getRenderStats().activeLayerCount);
```

## 常见错误处理

### MapLibre GL 错误
```typescript
// 监听地图错误事件
map.on('error', (error) => {
  console.error('地图错误:', error);
  // 根据错误类型进行处理
});
```

### 类型错误处理
```typescript
// 使用类型守卫确保对象类型
const isValidPipeNode = (obj: any): obj is PipeNode => {
  return obj && 
         typeof obj.id === 'string' &&
         typeof obj.name === 'string' &&
         obj.geojson && 
         obj.geojson.type === 'Feature';
};
```

### 异步操作错误
```typescript
// 完善的异步错误处理
async function safeRenderFeatures(nodes: PipeNode[], lines: PipeLine[]) {
  try {
    await pipeRenderer?.renderFeatures(nodes, lines);
  } catch (error: any) {
    console.error('渲染失败:', {
      message: error.message,
      stack: error.stack,
      nodes: nodes.length,
      lines: lines.length
    });
    // 提供降级方案
    notifyUser('渲染失败，请重试');
  }
}
```

## 调试工具和技巧

### 浏览器开发者工具
```typescript
// 使用 console.table 显示结构化数据
console.table(nodes.map(n => ({
  id: n.id,
  name: n.name,
  type: n.geojson.properties.attributes.nodeType,
  coordinates: n.geojson.geometry.coordinates
})));
```

### 自定义调试方法
```typescript
// 添加全局调试方法
(window as any).debugPipeRenderer = () => {
  if (pipeRenderer) {
    console.log('渲染器状态:', pipeRenderer.getRenderStats());
    console.log('图层配置:', Array.from(pipeRenderer.layers.entries()));
    console.log('数据源状态:', Array.from(pipeRenderer.dataSources.entries()));
  }
};
```

### 条件断点设置
```typescript
// 在特定条件下触发断点
if (nodes.length === 0 && process.env.NODE_ENV === 'development') {
  debugger; // 仅在开发环境中暂停
}
```

## 日志级别管理

### 结构化日志
```typescript
enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

class Logger {
  private level = LogLevel.INFO;
  
  debug(message: string, data?: any) {
    if (this.level <= LogLevel.DEBUG) {
      console.log(`🔍 ${message}`, data);
    }
  }
  
  info(message: string, data?: any) {
    if (this.level <= LogLevel.INFO) {
      console.log(`ℹ️ ${message}`, data);
    }
  }
  
  warn(message: string, data?: any) {
    if (this.level <= LogLevel.WARN) {
      console.warn(`⚠️ ${message}`, data);
    }
  }
  
  error(message: string, data?: any) {
    if (this.level <= LogLevel.ERROR) {
      console.error(`❌ ${message}`, data);
    }
  }
}
```

## 生产环境调试

### 安全的生产日志
```typescript
// 生产环境中有选择性地启用调试
const isDebugEnabled = () => {
  return process.env.NODE_ENV === 'development' || 
         localStorage.getItem('debug-pipe-renderer') === 'true';
};

const debugLog = (message: string, data?: any) => {
  if (isDebugEnabled()) {
    console.log(message, data);
  }
};
```

### 错误上报
```typescript
// 生产环境错误上报
const reportError = (error: Error, context: any) => {
  if (process.env.NODE_ENV === 'production') {
    // 发送到错误监控服务
    // errorReportingService.report(error, context);
  } else {
    console.error('错误详情:', { error, context });
  }
};
```

