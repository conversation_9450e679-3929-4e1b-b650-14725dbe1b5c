<template>
  <custom-card
    @closeHandler="close"
    :width="'400px'"
    :top="'90px'"
    :right="'120px'"
    :title="'漏水事件详情'"
  >
    <el-form
      ref="form"
      :model="formData"
      class="admin-sub-form"
      label-width="auto"
      :disabled="true"
    >
      <el-form-item label="漏电时间：">
        <el-input class="" v-model.trim="formData.confirmTime" placeholder="" />
      </el-form-item>
      <el-form-item label="听漏时间：">
        <el-input class="" v-model.trim="formData.listenTime" placeholder="" />
      </el-form-item>
      <el-form-item label="听漏类型：">
        <el-input
          class=""
          v-model.trim="formData.listenType_dictText"
          placeholder=""
        />
      </el-form-item>
      <el-form-item label="报销状态：">
        <el-input
          class=""
          v-model.trim="formData.repairStatus_dictText"
          placeholder=""
        />
      </el-form-item>
      <el-form-item label="所属分区：">
        <el-input
          class=""
          v-model.trim="formData.storeyId_dictText"
          placeholder=""
        />
      </el-form-item>
      <el-form-item label="漏点位置：">
        <el-input
          class=""
          type="textarea"
          resize="none"
          :rows="3"
          v-model.trim="formData.listenPlace"
          placeholder=""
        />
      </el-form-item>
    </el-form>
  </custom-card>
</template>
<script lang="ts" setup>
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});
interface FormData {
  confirmTime: string;
  listenTime: string;
  listenType_dictText: string;
  repairStatus_dictText: string;
  storeyId_dictText: string;
  listenPlace: string;
}
const initFormData = () => {
  return {
    confirmTime: "",
    listenTime: "",
    listenType_dictText: "",
    repairStatus_dictText: "",
    storeyId_dictText: "",
    listenPlace: "",
  };
};
const formData = ref<FormData>(initFormData());
const dataInfo = ref(props.mainItem.params.properties);
formData.value = dataInfo.value;
const close = () => {
  useDialogStore().closeDialog("LeakDetail");
};
</script>
<style lang="scss" scoped></style>
