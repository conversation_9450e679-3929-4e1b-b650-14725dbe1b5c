<!--
 * @Description: Cesium地图坐标栏组件
 * @Date: 2023-08-15 10:00:00
 * @Author: 项目开发团队
 * @LastEditTime: 2023-08-15 10:00:00
 * @Version: 1.1.0
-->
<template>
  <MouseCoordinateDisplay 
    v-if="cesiumViewer"
    mapType="cesium" 
    :mapInstance="cesiumViewer" 
    :visible="true"
    :precision="6"
  />
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { AppCesium } from '@/lib/cesium/AppCesium';
import MouseCoordinateDisplay from '@/views/tinytools/mouseCoordinate/MouseCoordinateDisplay.vue';

// Cesium查看器实例引用
const cesiumViewer = ref<any>(null);

// 在组件挂载后获取地图实例
onMounted(() => {
  // 延迟获取Cesium实例，确保地图已完成初始化
  setTimeout(() => {
    try {
      const app = AppCesium.getInstance();
      if (app && app.getViewer()) {
        cesiumViewer.value = app.getViewer();
      } else {
        console.error('Cesium查看器实例未找到');
      }
    } catch (error) {
      console.error('获取Cesium实例失败:', error);
    }
  }, 500);
});
</script>

<style scoped>
/* 无需额外样式，样式由子组件提供 */
</style> 