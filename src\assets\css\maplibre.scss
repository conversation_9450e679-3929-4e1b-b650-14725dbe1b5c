:root {
  --page-component: rgba(0, 0, 0, 0);
  --page-button: #178ff8;
  --page-bord: #21729e;
  --table-hover: rgba(0, 178, 255, 0.5);
  --transparent: rgba(0, 0, 0, 0);
  --border-color: #21729e;
}

.maplibregl-ctrl-bottom-right {
  display: none !important;
}

.maplibregl-ctrl-bottom-left {
  left: 288px !important;
  bottom: -5px !important;
}


//表格
.table-vis-bd {
  margin-top: 16px;
  border-radius: 8px;
  height: 330px;
  background-color: var(--transparent);
}

.custom-vis-table .el-table__row .cell {
  display: flex;
  align-items: center;
  flex-direction: row;
}

.custom-vis-table {
  --el-table-border-color: var(--transparent);
  --el-table-row-hover-bg-color: var(--transparent);
  --el-table-header-text-color: #e4f0ff;
  --el-table-header-bg-color: var(--transparent);
  --el-table-bg-color: var(--transparent);
  --el-table-tr-bg-color: (0, 0, 0, 0);

  .custom-vis-header-row {
    height: 34px;
    background: linear-gradient(90deg, #3086cb, rgba(24, 67, 102, 0.1));

    .custom-header-vis-cell {
      font: 400 15px SansCN-Regular;
      color: #e4f0ff;
      border-bottom-color: var(--transparent);
    }
  }

  .el-table__expand-icon {
    color: unset;
  }

  .el-table__body tr.current-row>td.el-table__cell {
    background-color: var(--table-hover);
  }

  .el-table__row {
    .cell {
      font: 400 15px SansCN-Regular;
      text-align: left;
      color: #b9ccdf;

      .el-button--text {
        width: 32px;
        height: 15px;
        font: 400 15px SansCN-Regular;
        text-align: left;
        color: #00fff7;
        line-height: 22px;
      }
    }
  }
}

// .el-popper.el-select__popper.custom-pagi-select {
//   background: rgba(16, 36, 61, 0.9);
//   border-color: #2698b1;
//   box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.2);

//   .el-select-dropdown__item.hover,
//   .el-select-dropdown__item:hover {
//     background: rgba(21, 119, 206, 0.2);
//     color: #178ff8;
//   }

//   .el-select-dropdown__item.is-hovering {
//     color: #178ff8;
//     background-color: rgba(21, 119, 206, 0.2);
//   }

//   .el-select-dropdown__item {
//     color: #fff;
//   }

//   .el-popper__arrow::before {
//     background: transparent;
//     border: none;
//   }
// }

.routeCt.el-table {

  --el-table-header-bg-color: #F8F9FA;

  .el-table__header {
    height: 40px;
  }

  th.el-table__cell.is-leaf,
  td.el-table__cell {
    border-bottom: none;
  }


}

.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
  background-color: #F7F8FA !important;
}

// .el-table th.el-table__cell.is-leaf,
// .el-table td.el-table__cell {
//   border-bottom: none;
// }

// 表格行
.success-row {
  height: 50px !important;
  // background: rgba(7, 59, 100, 0.4) !important;
}

.dft {
  height: 50px !important;
  background: #F7FAFF !important;
}

//分页
// .custom-pagi-vis-card {
//   width: 100%;
//   height: 30px;
//   font-size: 14px;
//   padding: 0 0 0 0;

//   .el-icon {
//     color: #fff;
//   }

//   .custom-pagi {

//     .btn-prev,
//     .btn-next {
//       border: 1px solid var(--page-bord);
//       background-color: var(--page-component);
//     }

//     .btn-quickprev,
//     .btn-quicknext {
//       border: 1px solid var(--page-bord);
//       background-color: var(--page-component) !important;
//     }

//     .el-pager {

//       .number,
//       .more.hover {
//         border: 1px solid var(--page-bord);
//         background-color: var(--page-component);
//         color: #fff;
//       }

//       .number.is-active {
//         background-color: var(--page-button);
//         border: 1px solid var(--page-button);
//       }
//     }

//     .btn-prev:disabled {
//       border: 1px solid var(--page-bord);
//       background-color: var(--page-component);
//     }

//     .btn-next:disabled {
//       border: 1px solid var(--page-bord);
//       background-color: var(--page-component);
//     }

//     .el-input__wrapper {
//       background: transparent;
//       box-shadow: none;
//       border: 1px solid var(--page-bord);
//     }

//     .el-select__wrapper {
//       background: transparent;
//       box-shadow: none;
//       border: 1px solid var(--page-bord);
//     }

//     .el-select__placeholder {
//       color: #a8d6ff;
//     }

//     .el-pagination__jump {
//       color: #fff;
//     }

//     .el-input__inner {
//       color: #fff;
//     }
//   }
// }

// .el-popper.el-select__popper.custom-pagi-select {
//   background: rgba(16, 36, 61, 0.9);
//   border-color: #2698b1;
//   box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.2);

//   .el-select-dropdown__item.hover,
//   .el-select-dropdown__item:hover {
//     background: rgba(21, 119, 206, 0.2);
//     color: #178ff8;
//   }

//   .el-select-dropdown__item.is-hovering {
//     color: #178ff8;
//     background-color: rgba(21, 119, 206, 0.2);
//   }

//   .el-select-dropdown__item {
//     color: #fff;
//   }

//   .el-popper__arrow::before {
//     background: transparent;
//     border: none;
//   }
// }

.vis-tabs {
  .el-tabs__header {
    .el-tabs__nav-wrap {
      .el-tabs__nav-scroll {
        .el-tabs__nav {
          border-radius: 2px;
          border: 1px solid #178ff8;
        }

        .is-active {
          color: #ffffff !important;
          background-color: #178ff8;
        }

        .el-tabs__active-bar {
          background-color: none;
          height: 0;
        }

        .el-tabs__item {
          font-size: 14px;
          color: #178ff8;
          padding: 0 20px;
        }

        .el-tabs__item:nth-child(n + 3) {
          border-left: 1px solid #178ff8;
        }

        /*         .el-tabs__item:nth-child(3){
          border-right: 1px solid #178ff8;
        } */
        /*         .el-tabs__item:nth-child(n + 2)::after {
          content: "";
          position: absolute;
          height: 40%;
          margin-left: 20px;
          border-left: 1px solid #178ff8;
          top: 30%;
        } */
        /*         .el-tabs__item::after {
          content: "";
          position: absolute;
          height: 40%;
          margin-left: 19px;
          border-left: 1px solid #178ff8;
          top: 30%;
        } */
      }
    }

    .el-tabs__nav-wrap::after {
      height: 0;
      background-color: none;
    }
  }

  .el-table__header-wrapper {
    .el-table__header {
      .custom-header-vis-row {
        background: url("../img/tab-header.png") no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .el-table__body-wrapper {
    .el-scrollbar {
      .el-scrollbar__wrap {
        .el-scrollbar__view {
          .el-table__body {
            .el-table__row {
              background: rgba(27, 130, 183, 0.22);
            }

            .el-table__row:hover {
              background: rgba(0, 178, 255, 0.5) !important;
            }
          }
        }
      }
    }
  }

  .el-table__cell {
    padding: 0;
  }
}

:deep(.el-table) {
  .el-table__indent {
    padding-left: 115px !important;
  }
}


// 提交表单
.admin-sub-form {
  .el-form-item__label {
    min-width: 84px;
    font-size: 14px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.9);
  }
}

.el-segmented.page-segmented {
  width: calc(100% - 70px);
  padding: unset;
  background: unset;

  .el-segmented--horizontal {
    column-gap: 20px;
  }

  .el-segmented__item {
    width: 130px;
    height: 36px;
    color: var(--el-text-color-primary);
    border-radius: 4px;
    background: #F2F6FF;
  }

  .el-segmented__item.is-selected {
    .el-text {
      color: #fff;
    }
  }

  .el-segmented__item-selected {
    border-radius: 4px;
  }
}

.page-form-item {
  .el-form-item__label {
    padding: unset;
  }
}

.el-button--info.query-button {
  width: 130px;
  height: 36px;
  --el-button-bg-color: #F2F6FF;
  --el-button-border-color: #F2F6FF;
  --el-button-text-color: var(--el-text-color-primary);
  --el-button-hover-bg-color: var(--el-color-primary);
  --el-button-hover-border-color: rgba($color: var(--el-color-primary), $alpha: 0.7);
  --el-button-active-bg-color: var(--el-color-primary);
  --el-button-disabled-bg-color: #F2F6FF;
  --el-button-disabled-border-color: #F2F6FF;
  --el-button-disabled-text-color: var(--el-text-color-primary);
}