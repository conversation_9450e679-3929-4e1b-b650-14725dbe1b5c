<!--
 * @Description: 空间查询按钮组件
 * @Date: 2025-01-10
 * @Author: 项目开发团队
 * @LastEditTime: 2025-01-10
-->
<template>
  <div class="spatial-query-buttons">

    <!-- 查询按钮组 -->
    <el-row>
      <el-text>查询方式：</el-text>
      <div class="buttons-row">
        <query-button v-for="button in enabledButtons" :key="button.type" type="info"
          :disabled="button.disabled || (isLoading && ['all', 'current'].includes(currentOperation || ''))" :loading="(currentOperation === button.type && isLoading)"
          @click="handleQuery(button.type)" :active="isActive(button.type).value">
          <i v-if="button.icon" :class="button.icon" class="mr-1"></i>
          {{ button.label }}
        </query-button>
        <slot name="other-buttons"></slot>
      </div>
    </el-row>

    <!-- 操作提示 -->
    <div v-if="showTips && currentTip" class="operation-tip">
      <el-alert :title="currentTip" type="info" :closable="false" :show-icon="true" effect="light" />
    </div>

    <!-- 结果显示区域 -->
    <!-- <div v-if="showResult && resultGeometry" class="result-display">
      <el-divider content-position="left">查询结果</el-divider>
      <div class="result-content">
        <el-tag type="success" size="small">{{ getResultSummary() }}</el-tag>
        <el-button v-if="config.showResultDetails" text type="primary" size="small" @click="toggleResultDetails">
          {{ showDetails ? '隐藏详情' : '查看详情' }}
        </el-button>
      </div>

      <el-collapse-transition>
        <div v-show="showDetails" class="result-details">
          <pre>{{ JSON.stringify(resultGeometry, null, 2) }}</pre>
        </div>
      </el-collapse-transition>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
import { AppCesium } from '@/lib/cesium/AppCesium';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import { DrawEventTypeEnum, type DrawMode, type DrawManager, type DrawEventData } from '@/lib/maplibre/draw';
import Coord from '@/lib/cesium/utils/Coord';

const {  isActive, setActive, clearActive } = useActiveButton<QueryType>();

/**
 * @description 查询类型枚举
 */
export type QueryType = 'all' | 'current' | 'polygon' | 'rectangle';

/**
 * @description 地图引擎类型
 */
export type MapEngineType = 'cesium' | 'maplibre';

/**
 * @description 按钮配置接口
 */
export interface ButtonConfig {
  type: QueryType;
  label: string;
  icon?: string;
  elType?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | '';
  disabled?: boolean;
  customClass?: string;
}

/**
 * @description 组件配置接口
 */
export interface SpatialQueryConfig {
  // 区域边界配置
  regionBounds?: {
    west: number;
    south: number;
    east: number;
    north: number;
    name?: string;
  };
  // 按钮样式配置
  buttonSize?: 'large' | 'default' | 'small';
  buttonLayout?: 'horizontal' | 'vertical';
  buttonSpacing?: number;
  // 功能配置
  enabledQueries?: QueryType[];
  showTips?: boolean;
  // showResult?: boolean;
  // showResultDetails?: boolean;
  // 绘制配置
  clearPreviousDrawing?: boolean;
  autoSwitchToSelect?: boolean;
}

/**
 * @description 查询结果接口
 */
export interface QueryResult {
  type: QueryType;
  geometry: any;
  bounds?: {
    west: number;
    south: number;
    east: number;
    north: number;
  };
  timestamp: number;
  success: boolean;
  error?: string;
}

/**
 * @description 组件属性
 */
interface Props {
  /** 地图引擎类型 */
  mapEngine: MapEngineType;
  /** 组件配置 */
  config?: Partial<SpatialQueryConfig>;
  /** 自定义按钮配置 */
  customButtons?: Partial<ButtonConfig>[];
  fetchLoading?: boolean
}

/**
 * @description 组件事件
 */
interface Emits {
  /** 查询开始事件 */
  (e: 'query-start', type: QueryType): void;
  /** 查询完成事件 */
  (e: 'query-complete', result: QueryResult): void;
  /** 查询错误事件 */
  (e: 'query-error', error: { type: QueryType; message: string }): void;
  /** 绘制开始事件 */
  (e: 'draw-start', type: QueryType): void;
  /** 绘制完成事件 */
  (e: 'draw-finish', result: QueryResult): void;
}

const props = withDefaults(defineProps<Props>(), {
  mapEngine: 'maplibre',
  config: () => ({}),
  customButtons: () => [],
  fetchLoading: false
});

const emit = defineEmits<Emits>();

// 组件状态
const isLoading = ref(false);
const currentOperation = ref<QueryType | null>(null);
const currentTip = ref('');
const resultGeometry = ref<any>(null);
// const showDetails = ref(false);

// 绘制工具相关
let drawTool: DrawManager | null = null;
let cesiumLayer: any = null;

/**
 * @description 默认配置
 */
const defaultConfig: Required<SpatialQueryConfig> = {
  regionBounds: {
    west: 103.42,
    south: 29.19,
    east: 103.74,
    north: 29.53,
    name: '沙湾区'
  },
  buttonSize: 'default',
  buttonLayout: 'horizontal',
  buttonSpacing: 8,
  enabledQueries: ['all', 'current', 'polygon', 'rectangle'],
  showTips: true,
  // showResult: true,
  // showResultDetails: true,
  clearPreviousDrawing: true,
  autoSwitchToSelect: true
};

/**
 * @description 合并后的配置
 */
const config = computed(() => ({
  ...defaultConfig,
  ...props.config,
  regionBounds: {
    ...defaultConfig.regionBounds,
    ...props.config?.regionBounds
  }
}));

/**
 * @description 计算属性 - 显示提示
 */
const showTips = computed(() => config.value.showTips);

/**
 * @description 计算属性 - 显示结果
 */
// const showResult = computed(() => config.value.showResult);

/**
 * @description 默认按钮配置
 */
const defaultButtons: ButtonConfig[] = [
  {
    type: 'all',
    label: '全区范围',
    icon: 'el-icon-map-location',
    elType: 'primary'
  },
  {
    type: 'current',
    label: '当前范围',
    icon: 'el-icon-view',
    elType: 'success'
  },
  {
    type: 'polygon',
    label: '多边形查询',
    icon: 'el-icon-edit',
    elType: 'warning'
  },
  {
    type: 'rectangle',
    label: '矩形查询',
    icon: 'el-icon-crop',
    elType: 'info'
  }
];

/**
 * @description 计算属性 - 启用的按钮
 */
const enabledButtons = computed(() => {
  return defaultButtons
    .filter(btn => config.value.enabledQueries.includes(btn.type))
    .map(btn => {
      // 应用自定义按钮配置
      const custom = props.customButtons?.find(c => c.type === btn.type);
      return custom ? { ...btn, ...custom } : btn;
    });
});

/**
 * @description 将边界矩形转换为GeoJSON Polygon格式
 * @param {number} west - 西边界经度
 * @param {number} south - 南边界纬度  
 * @param {number} east - 东边界经度
 * @param {number} north - 北边界纬度
 * @returns {object} GeoJSON Polygon geometry对象
 */
const boundsToGeoJSONPolygon = (west: number, south: number, east: number, north: number) => {
  return {
    type: 'Polygon',
    coordinates: [[
      [west, south],   // 西南角
      [east, south],   // 东南角
      [east, north],   // 东北角
      [west, north],   // 西北角
      [west, south]    // 闭合多边形 (回到起点)
    ]]
  };
};

/**
 * @description 将Cesium边界对象转换为GeoJSON Polygon格式
 * @param {object} bounds - Cesium边界对象，包含west, east, south, north属性
 * @returns {object} GeoJSON Polygon geometry对象
 */
const cesiumBoundsToGeoJSONPolygon = (bounds: any) => {
  if (!bounds || typeof bounds.west === 'undefined') {
    throw new Error('无效的Cesium边界对象');
  }

  return boundsToGeoJSONPolygon(bounds.west, bounds.south, bounds.east, bounds.north);
};

/**
 * @description 处理绘制完成事件
 * @param {DrawEventData} event - 绘制事件数据
 */
const handleDrawFinish = (event: DrawEventData) => {
  console.log('绘制完成事件:', event);

  try {
    if (event.features) {
      // 处理 GeoJSONStoreFeatures 类型
      const features = Array.isArray(event.features) ? event.features : [event.features];

      if (features.length > 0) {
        // 获取第一个要素的几何体
        const feature = features[0];
        if (feature && feature.geometry) {
          const geometry = feature.geometry;

          // 创建查询结果
          const result: QueryResult = {
            type: currentOperation.value!,
            geometry: geometry,
            timestamp: Date.now(),
            success: true
          };

          resultGeometry.value = geometry;

          // 停止绘制，切换回选择模式
          if (config.value.autoSwitchToSelect && drawTool) {
            drawTool.setMode('select' as DrawMode);
          }

          // 清除操作状态
          currentOperation.value = null;
          currentTip.value = '';
          isLoading.value = false;

          console.log('绘制几何体获取成功:', geometry);

          // 触发事件
          emit('draw-finish', result);
          emit('query-complete', result);
        } else {
          throw new Error('绘制要素无几何体数据');
        }
      } else {
        throw new Error('绘制完成但无要素数据');
      }
    } else {
      throw new Error('绘制完成事件无features数据');
    }
  } catch (error) {
    console.error('处理绘制完成事件失败:', error);

    const errorMsg = (error as Error).message;
    const queryType = currentOperation.value!;

    // 清除操作状态
    currentOperation.value = null;
    currentTip.value = '';
    isLoading.value = false;

    // 触发错误事件
    emit('query-error', { type: queryType, message: errorMsg });
  }
};

/**
 * @description 中断当前绘制操作
 */
const interruptCurrentDrawing = async () => {
  try {
    if (props.mapEngine === 'cesium') {
      // 中断Cesium绘制操作
      const plotUtil = AppCesium.getInstance().getPlotUtil();
      if (plotUtil && (plotUtil as any).deactivate) {
        (plotUtil as any).deactivate();
        console.log("✓ 停用Cesium绘制操作");
      } else if (plotUtil && (plotUtil as any).stop) {
        (plotUtil as any).stop();
        console.log("✓ 停止Cesium绘制操作");
      }

      // 恢复Cesium地图鼠标样式
      const viewer = AppCesium.getInstance().getViewer();
      if (viewer && viewer.canvas) {
        viewer.canvas.style.cursor = "";
        console.log("✓ 恢复Cesium鼠标样式");
      }
    } else {
      // 中断MapLibre绘制操作
      if (drawTool) {
        // 切换到选择模式以中断绘制
        drawTool.setMode("select" as DrawMode);
        console.log("✓ 切换MapLibre到选择模式");

        // 恢复MapLibre地图鼠标样式
        const map = AppMaplibre.getMap();
        if (map && map.getCanvas) {
          map.getCanvas().style.cursor = "";
          console.log("✓ 恢复MapLibre鼠标样式");
        }
      }
    }
  } catch (error) {
    console.warn("中断绘制操作失败:", error);
  }
};

/**
 * @description 主要查询处理函数
 * @param {QueryType} type - 查询类型
 */
const handleQuery = async (type: QueryType) => {
  console.log(`开始执行空间查询 - 类型: ${type}`);

  try {
    // 如果当前有绘制操作正在进行，先中断它
    if (currentOperation.value && ['polygon', 'rectangle'].includes(currentOperation.value)) {
      console.log(`中断当前绘制操作: ${currentOperation.value}`);
      await interruptCurrentDrawing();
    }

    setActive(type)
    // 设置操作状态
    isLoading.value = true;
    currentOperation.value = type;
    currentTip.value = '';

    // 触发查询开始事件
    emit('query-start', type);

    // 清除之前的绘制结果
    if (config.value.clearPreviousDrawing) {
      clearPreviousResults();
    }

    let result: QueryResult | null = null;

    switch (type) {
      case 'all':
        result = await handleAllQuery();
        break;
      case 'current':
        result = await handleCurrentQuery();
        break;
      case 'polygon':
        await handlePolygonQuery();
        return; // 绘制操作，等待用户完成
      case 'rectangle':
        await handleRectangleQuery();
        return; // 绘制操作，等待用户完成
      default:
        throw new Error(`未知的查询类型: ${type}`);
    }

    if (result) {
      resultGeometry.value = result.geometry;
      emit('query-complete', result);
    }

  } catch (error) {
    console.error(`空间查询执行失败 (${type}):`, error);
    emit('query-error', {
      type,
      message: (error as Error).message
    });
  } finally {
    // 对于非绘制操作，清除加载状态
    if (type === 'all' || type === 'current') {
      isLoading.value = false;
      currentOperation.value = null;
    }
    // 对于绘制操作，保持currentOperation但清除isLoading，允许切换按钮
    else if (type === 'polygon' || type === 'rectangle') {
      isLoading.value = false;
    }
  }
};

/**
 * @description 处理全区范围查询
 */
const handleAllQuery = async (): Promise<QueryResult> => {
  console.log(`执行全区范围查询 - ${config.value.regionBounds.name}`);

  const bounds = config.value.regionBounds;
  const geometry = boundsToGeoJSONPolygon(bounds.west, bounds.south, bounds.east, bounds.north);

  const result: QueryResult = {
    type: 'all',
    geometry,
    bounds: {
      west: bounds.west,
      south: bounds.south,
      east: bounds.east,
      north: bounds.north
    },
    timestamp: Date.now(),
    success: true
  };

  console.log('全区范围查询完成', result);
  return result;
};

/**
 * @description 处理当前范围查询
 */
const handleCurrentQuery = async (): Promise<QueryResult> => {
  console.log('执行当前范围查询');

  let bounds: { west: number; south: number; east: number; north: number };

  if (props.mapEngine === 'cesium') {
    console.log('获取Cesium当前视野范围');

    const viewer = AppCesium.getInstance().getViewer();
    const camera = viewer.camera;

    // 使用Coord工具获取相机地面边界
    const cesiumBounds = Coord.getCameraGroundBounds(camera);
    console.log('Cesium边界数据:', cesiumBounds);

    bounds = {
      west: cesiumBounds.west,
      south: cesiumBounds.south,
      east: cesiumBounds.east,
      north: cesiumBounds.north
    };

  } else {
    console.log('获取MapLibre当前视野范围');

    const map = AppMaplibre.getMap();
    const mapBounds = map.getBounds();

    bounds = {
      west: mapBounds.getWest(),
      south: mapBounds.getSouth(),
      east: mapBounds.getEast(),
      north: mapBounds.getNorth()
    };

    console.log('MapLibre边界数据:', bounds);
  }

  const geometry = boundsToGeoJSONPolygon(bounds.west, bounds.south, bounds.east, bounds.north);

  const result: QueryResult = {
    type: 'current',
    geometry,
    bounds,
    timestamp: Date.now(),
    success: true
  };

  console.log('当前范围查询完成', result);
  return result;
};

/**
 * @description 处理多边形查询
 */
const handlePolygonQuery = async (): Promise<void> => {
  console.log('多边形查询 - 开始绘制');

  if (props.mapEngine === 'cesium') {
    console.log('Cesium多边形查询');

    const plotUtil = AppCesium.getInstance().getPlotUtil();

    currentTip.value = '请在地图上绘制多边形';
    emit('draw-start', 'polygon');

    plotUtil.draw('polygon', (overlay: any) => {
      console.log('Cesium多边形绘制完成:', overlay);

      if (cesiumLayer) {
        cesiumLayer.addOverlay(overlay);
      }

      const positions = overlay.positions;
      const coordinates: number[][] = [];

      positions.forEach((position: any) => {
        coordinates.push([position.lng, position.lat]);
      });

      // 确保多边形闭合
      if (coordinates.length > 0) {
        coordinates.push(coordinates[0]);
      }

      const geometry = {
        type: 'Polygon',
        coordinates: [coordinates]
      };

      const result: QueryResult = {
        type: 'polygon',
        geometry,
        timestamp: Date.now(),
        success: true
      };

      resultGeometry.value = geometry;

      // 清除操作状态
      currentOperation.value = null;
      currentTip.value = '';
      isLoading.value = false;

      emit('draw-finish', result);
      emit('query-complete', result);
    }, {}, true);

  } else {
    console.log('MapLibre多边形查询 - 开始绘制');

    if (!drawTool) {
      throw new Error('绘制工具未初始化，无法执行多边形查询');
    }

    currentTip.value = '请在地图上绘制多边形，双击结束绘制';
    emit('draw-start', 'polygon');

    // 清除之前的绘制结果
    drawTool.clearAllFeatures();
    // 切换到多边形绘制模式
    drawTool.setMode('polygon' as DrawMode);

    console.log('已切换到多边形绘制模式');
  }
};

/**
 * @description 处理矩形查询
 */
const handleRectangleQuery = async (): Promise<void> => {
  console.log('矩形查询 - 开始绘制');

  if (props.mapEngine === 'cesium') {
    console.log('Cesium矩形查询');

    const plotUtil = AppCesium.getInstance().getPlotUtil();

    currentTip.value = '请在地图上绘制矩形';
    emit('draw-start', 'rectangle');

    plotUtil.draw('rectangle', (overlay: any) => {
      console.log('Cesium矩形绘制完成:', overlay);

      if (cesiumLayer) {
        cesiumLayer.addOverlay(overlay);
      }

      const positions = overlay.positions;
      // 西北角
      const northwest = positions[0];
      // 东南角
      const southeast = positions[1];

      const coordinates = [[
        [northwest.lng, northwest.lat],
        [southeast.lng, northwest.lat],
        [southeast.lng, southeast.lat],
        [northwest.lng, southeast.lat],
        [northwest.lng, northwest.lat]
      ]];

      const geometry = {
        type: 'Polygon',
        coordinates
      };

      const result: QueryResult = {
        type: 'rectangle',
        geometry,
        timestamp: Date.now(),
        success: true
      };

      resultGeometry.value = geometry;

      // 清除操作状态
      currentOperation.value = null;
      currentTip.value = '';
      isLoading.value = false;

      emit('draw-finish', result);
      emit('query-complete', result);
    }, {}, true);

  } else {
    console.log('MapLibre矩形查询 - 开始绘制');

    if (!drawTool) {
      throw new Error('绘制工具未初始化，无法执行矩形查询');
    }

    currentTip.value = '请在地图上绘制矩形，点击两个对角点';
    emit('draw-start', 'rectangle');

    // 清除之前的绘制结果
    drawTool.clearAllFeatures();
    // 切换到矩形绘制模式
    drawTool.setMode('rectangle' as DrawMode);

    console.log('已切换到矩形绘制模式');
  }
};

/**
 * @description 清除之前的结果
 */
const clearPreviousResults = (): void => {
  // 清除Cesium图层
  if (cesiumLayer) {
    cesiumLayer.clear();
  }

  // 清除MapLibre绘制
  if (drawTool) {
    drawTool.clearAllFeatures();
  }
};

/**
 * @description 获取结果摘要
 */
// const getResultSummary = (): string => {
//   if (!resultGeometry.value) return '';

//   const type = resultGeometry.value.type;
//   switch (type) {
//     case 'Polygon':
//       return '多边形区域';
//     case 'Point':
//       return '点位置';
//     case 'LineString':
//       return '线段';
//     default:
//       return `${type}几何体`;
//   }
// };

/**
 * @description 切换结果详情显示
 */
// const toggleResultDetails = (): void => {
//   showDetails.value = !showDetails.value;
// };

/**
 * @description 组件挂载
 */
onMounted(async () => {
  if (props.mapEngine === 'cesium') {
    // 初始化Cesium图层
    cesiumLayer = new BC.VectorLayer("spatialQueryLayer");
    AppCesium.getInstance().getViewer().addLayer(cesiumLayer);
  } else {
    // 初始化MapLibre绘制工具
    try {
      // === 新增：使用ResourceManager检测绘制工具冲突 ===
      import('@/lib/maplibre/layer/ResourceManager').then(({ ResourceManager }) => {
        const resourceManager = ResourceManager.getInstance();

        // 检测绘制工具冲突
        const conflictResult = resourceManager.detectDrawToolConflict('SpatialQueryButtons');
        if (conflictResult.hasConflict) {
          console.log('检测到绘制工具冲突，尝试解决...');
          const resolved = resourceManager.resolveDrawToolConflict('SpatialQueryButtons', 'force');
          if (!resolved) {
            console.error('无法解决绘制工具冲突');
          } else {
            console.log('绘制工具冲突已解决');
          }
        }

        // 注册绘制工具使用
        resourceManager.registerDrawToolUsage('SpatialQueryButtons', 'idle');
      }).catch(error => {
        console.warn('无法导入ResourceManager:', error);
      });
      await nextTick()
      drawTool = AppMaplibre.getDrawTool();
      console.log(drawTool)

      // 安全启动绘制工具，等待地图加载完成
      try {
        if (drawTool && !drawTool.isEnabled()) {
          console.log('绘制工具未启动，等待地图加载完成后启动...');
          await drawTool.start(true); // 等待地图加载完成
          console.log('绘制工具启动成功');
        }
      } catch (startError) {
        console.error('启动绘制工具失败:', startError);
        // 如果启动失败，尝试重新创建绘制工具
        console.log('尝试重新创建绘制工具...');
        AppMaplibre.recreateDrawTool();
        drawTool = AppMaplibre.getDrawTool();
        await drawTool.start(true);
        console.log('绘制工具重新创建并启动成功');
      }

      // 添加绘制事件监听器
      drawTool.addEventListener(DrawEventTypeEnum.DRAW_FINISH, handleDrawFinish);
      console.log('MapLibre绘制工具初始化成功');
    } catch (error) {
      console.error('MapLibre绘制工具初始化失败:', error);

      // 如果初始化失败，尝试重新创建绘制工具
      try {
        console.log('尝试重新创建绘制工具...');
        // 强制重新创建绘制工具实例
        AppMaplibre.recreateDrawTool();
        drawTool = AppMaplibre.getDrawTool();
        await drawTool.start(true); // 等待地图加载完成后启动
        drawTool.addEventListener(DrawEventTypeEnum.DRAW_FINISH, handleDrawFinish);
        console.log('绘制工具重新创建成功');
      } catch (recreateError) {
        console.error('重新创建绘制工具失败:', recreateError);
      }
    }
  }
});

/**
 * @description 组件卸载
 */
onUnmounted(() => {
  if (props.mapEngine === 'cesium') {
    // 清理Cesium图层和绘制工具
    console.log('组件卸载 - 开始清理Cesium绘制工具');

    try {
      // 1. 如果正在绘制过程中，先取消当前绘制
      if (currentOperation.value === 'polygon' || currentOperation.value === 'rectangle') {
        console.log(`正在绘制${currentOperation.value}，取消当前绘制操作`);

        // 停止Cesium绘制工具
        try {
          const plotUtil = AppCesium.getInstance().getPlotUtil();
          // 尝试调用可能存在的停止方法
          if (plotUtil && (plotUtil as any).deactivate) {
            (plotUtil as any).deactivate();
            console.log('✓ 停用Cesium绘制操作');
          } else if (plotUtil && (plotUtil as any).stop) {
            (plotUtil as any).stop();
            console.log('✓ 停止Cesium绘制操作');
          } else {
            console.log('Cesium绘制工具无可用的停止方法');
          }
        } catch (error) {
          console.warn('停止Cesium绘制工具失败:', error);
        }

        // 恢复Cesium地图鼠标样式
        const viewer = AppCesium.getInstance().getViewer();
        if (viewer && viewer.canvas) {
          viewer.canvas.style.cursor = '';
          console.log('✓ 恢复Cesium鼠标样式');
        }

        // 触发绘制取消事件
        emit('query-error', {
          type: currentOperation.value,
          message: '组件关闭，绘制操作已取消'
        });
      }

      // 2. 清理Cesium图层
      if (cesiumLayer) {
        cesiumLayer.clear();
        AppCesium.getInstance().getViewer().removeLayer(cesiumLayer);
        console.log('✓ 清理Cesium图层');
        cesiumLayer = null;
      }

      // 3. 重置组件状态
      currentOperation.value = null;
      currentTip.value = '';
      isLoading.value = false;
      resultGeometry.value = null;

      console.log('✓ 组件状态已重置');
      console.log('Cesium绘制工具已彻底清理和停止');
    } catch (error) {
      console.error('清理Cesium绘制工具失败:', error);
      // 即使清理失败，也要重置状态
      currentOperation.value = null;
      currentTip.value = '';
      isLoading.value = false;
      resultGeometry.value = null;

      if (cesiumLayer) {
        cesiumLayer = null;
      }
    }
  } else {
    // 清理MapLibre绘制工具
    console.log('组件卸载 - 开始清理MapLibre绘制工具');

    if (drawTool) {
      try {
        // 1. 如果正在绘制过程中，先取消当前绘制
        if (currentOperation.value === 'polygon' || currentOperation.value === 'rectangle') {
          console.log(`正在绘制${currentOperation.value}，取消当前绘制操作`);

          // 触发绘制取消事件
          emit('query-error', {
            type: currentOperation.value,
            message: '组件关闭，绘制操作已取消'
          });
        }

        // 2. 移除事件监听器
        drawTool.removeEventListener(DrawEventTypeEnum.DRAW_FINISH, handleDrawFinish);
        console.log('✓ 移除绘制事件监听器');

        // 3. 清除所有绘制要素
        drawTool.clearAllFeatures();
        console.log('✓ 清除所有绘制要素');

        // 4. 停止Terra Draw实例以彻底禁用绘制交互
        drawTool.stop();
        console.log('✓ 停止Terra Draw实例');

        // 5. 手动重置MapLibre地图画布的鼠标样式
        const map = AppMaplibre.getMap();
        if (map && map.getCanvas) {
          map.getCanvas().style.cursor = '';
          console.log('✓ 恢复MapLibre鼠标样式');
        }

        console.log('MapLibre绘制工具已彻底清理和停止');
      } catch (error) {
        console.error('清理MapLibre绘制工具失败:', error);
      } finally {
        // 6. 清理本地引用
        drawTool = null;

        // 7. 重置组件状态
        currentOperation.value = null;
        currentTip.value = '';
        isLoading.value = false;
        resultGeometry.value = null;

        console.log('✓ 组件状态已重置');
      }
    } else {
      console.log('绘制工具未初始化，无需清理');
    }
  }

  console.log('SpatialQueryButtons组件卸载完成');
});

// 暴露方法给父组件
defineExpose({
  clearActive,
  clearPreviousResults
});
</script>

<style lang="scss" scoped>
.spatial-query-buttons {
  .operation-tip {
    margin-top: 12px;
  }
}

.buttons-row {
  width: calc(100% - 70px);
  display: flex;
  gap: 8px;
  box-sizing: border-box;

  :deep(.el-button) {
    flex: 1;
    min-width: 0;
  }
}
</style>