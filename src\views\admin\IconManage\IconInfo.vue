<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    class="admin-sub-form"
    label-width="auto"
    :disabled="editable"
  >
    <el-form-item label="图标名称" prop="name">
      <el-input
        class=""
        v-model.trim="formData.name"
        placeholder="请输入图标名称"
      />
    </el-form-item>
    <el-form-item label="图标类型" prop="type">
      <el-select
        class="w-full"
        v-model="formData.type"
        placeholder="请选择"
        clearable
      >
        <el-option
          v-for="item in iconType"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="应用系统" prop="sys">
      <el-select
        class="w-full"
        v-model="formData.sys"
        placeholder="请选择"
        clearable
      >
        <el-option
          v-for="item in system"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <div v-if="editable">
      <el-form-item label="更新时间" prop="updateTime">
        <el-input
          class=""
          v-model.trim="formData.updateTime"
          placeholder="请输入更新时间"
        />
      </el-form-item>
      <!-- <el-form-item label="更新人员" prop="updateName">
        <el-input
          class=""
          v-model.trim="formData.updateName"
          placeholder="请输入更新人员"
        />
      </el-form-item> -->
    </div>
    <el-form-item label="图标图片" prop="fileInfoDto">
      <el-upload
        class="avatar-uploader"
        ref="upload"
        :limit="1"
        :action="reqUrl"
        :headers="headers"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        :data="infoData"
        accept=".jpeg,.png,.jpg"
        :disabled="editable"
      >
        <div v-if="!dialogImageUrl" class="avatar-uploader-icon">
          <el-icon>
            <Plus />
          </el-icon>
          <span>点击上传图片</span>
        </div>
        <div v-else class="avatar-uploader-icon">
          <img :src="dialogImageUrl" class="avatar" />
        </div>
      </el-upload>
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import { iconType, system } from "@/utils/constant";
import localCache from "@/utils/auth";
const props = defineProps({
  formData: Object,
  editable: Boolean,
});
let formData: any = ref(props.formData);
let editable = ref(props.editable);
watch(
  () => props.formData,
  (val: any) => {
    resetForm();
    formData.value = val;
    if (val.fileInfoDto != null) {
      dialogImageUrl.value =
        import.meta.env.VITE_IMG_PREFIX + "?fileId=" + val.fileInfoDto.id;
    } else {
      dialogImageUrl.value = "";
    }
  }
);
watch(
  () => props.editable,
  (val) => {
    editable.value = val;
  }
);
const rules = {
  name: [
    { required: true, message: "请输入图标名称", trigger: "blur" },
    { min: 2, message: "长度不低于2个字符", trigger: "blur" },
    { max: 20, message: "长度不超过20个字符", trigger: "blur" },
  ],
  type: [{ required: true, message: "请选择图标类型", trigger: "change" }],
  sys: [{ required: true, message: "请选择应用系统", trigger: "change" }],
  fileInfoDto: [
    { required: true, message: "请选择图标图片", trigger: "change" },
  ],
};
let form: any = ref(null);
const infoData = ref();
const fileList = ref([]);
const reqUrl = ref(import.meta.env.VITE_XHR_URL + "/common/upload");
const dialogImageUrl = ref("");
const headers = {
  Latias: localCache.getCache("Latias"),
};
const handleSuccess = (response: any, uploadFile: any) => {
  if (response.code === 200) {
    // formData.value.url = response.data.url;
    formData.value.fileInfoDto = response.data;
    dialogImageUrl.value = URL.createObjectURL(uploadFile.raw!);
  } else {
    fileList.value = [];
    ElMessage.error(response.msg);
  }
};
const beforeUpload = (rawFile: any) => {
  const formDatas = new FormData();
  infoData.value = formDatas.append("file", rawFile, rawFile.name);
};
const submitForm = async (): Promise<any> => {
  return (await form.value?.validate()) ? formData.value : null;
};
const resetForm = () => {
  form.value?.resetFields();
};

onMounted(async () => {
  resetForm();
  if (formData.value.fileInfoDto != null) {
    dialogImageUrl.value =
      import.meta.env.VITE_IMG_PREFIX +
      "?fileId=" +
      formData.value.fileInfoDto.id;
  }
});
defineExpose({
  submitForm,
  resetForm,
});
</script>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 160px;
  height: 94px;
  text-align: center;
}
.avatar-uploader {
  border: 1px dashed rgba(131, 131, 131, 0.5);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 160px;
  height: 94px;
  transition: var(--el-transition-duration-fast);
}
.uploader-file {
  border: 1px dashed rgba(131, 131, 131, 0.5);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 160px;
  height: 94px;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader .avatar {
  width: 160px;
  height: 94px;
}

.avatar-uploader-icon {
  width: 160px;
  height: 94px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.uploader-file {
  width: 370px;
  height: 80px;
}
.uploader-icon-file {
  width: 340px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.avatar-uploader-icon .el-icon {
  font-size: 16px;
  color: #cdd0d6;
  text-align: center;
}

.avatar-uploader-icon span {
  font-size: 12px;
  font-family: PingFangSC-Regular;
  text-align: center;
  color: #cdd0d6;
  line-height: 20px;
}
.uploader-icon-file .el-icon {
  font-size: 16px;
  color: #cdd0d6;
  text-align: center;
}

.uploader-icon-file span {
  font-size: 12px;
  font-family: PingFangSC-Regular;
  text-align: center;
  color: #cdd0d6;
  line-height: 20px;
}
</style>
