<template>
  <div
    class="admin-home [&_>div]:overflow-hidden"
    grid="~ cols-[1100px_1fr] gap-x-4"
  >
    <div>
      <div grid="~ cols-[456px_1fr] gap-4" class="overflow-hidden">
        <div class="bg">
          <VisitorNumber />
        </div>
        <div class="bg">
          <VisitorStat />
        </div>
        <div class="bg">
          <GisStat />
        </div>
        <div class="bg">
          <MonthlyAnalysis />
        </div>
      </div>
      <div class="bg mt-4">
        <AnnualAnalysis />
      </div>
    </div>
    <div class="bg">
      <UserDashboard />
    </div>
  </div>
</template>
<script lang="ts" setup>
import VisitorNumber from "./VisitorNumber.vue";
import VisitorStat from "./VisitorStat.vue";
import MonthlyAnalysis from "./MonthlyAnalysis.vue";
import GisStat from "./GisStat.vue";
import AnnualAnalysis from "./AnnualAnalysis.vue";
import UserDashboard from "./UserDashboard.vue";
</script>
<style lang="scss" scoped>
.admin-home {
  --at-apply: "box-border w-full h-full p-3 bg-#eef0f4 overflow-hidden";
}
.bg {
  border-radius: 4px;
  box-shadow: 0px 2px 12px 0px rgba(178, 203, 253, 0.3);
  background: rgba(255, 255, 255, 1);
  padding: 16px;
  box-sizing: border-box;
}
</style>
