/**
 * @fileoverview 地图标注存储管理器
 * @description 管理地图标注的持久化存储、CRUD操作，使用统一的GeoJSON Feature格式
 * <AUTHOR>
 * @version 3.0.0
 */

import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { 
  IMapAnnotation, 
  AnnotationDataStorage, 
  CreateAnnotationParams,
  AnnotationStyle,
  AnnotationGeoJSONFeature,
  AnnotationFeatureProperties
} from '@/types/annotation';
import { 
  plottingPage, 
  plottingAdd, 
  plottingEdit, 
  plottingDelete 
} from '@/api/plotting';
import { AnnotationAdapter } from '@/services/AnnotationAdapter';
import { EngineType, type PlottingPageQuery } from '@/types/plotting';
import { ElMessage } from 'element-plus';

/**
 * @description 地图标注存储管理器
 * @details 使用GeoJSON Feature格式，支持本地存储持久化
 */
export const useAnnotationStore = defineStore("AnnotationStore", () => {
  // 存储配置
  const STORAGE_KEY = 'map-annotations-v3';
  const CURRENT_VERSION = '3.0.0';
  
  /**
   * @description 创建默认样式配置
   */
  const createDefaultStyle = (): AnnotationStyle => ({
    color: '#FFFF00',
    fontSize: 14,
    offsetX: 0,
    offsetY: 0,
    opacity: 1.0
  });
  
  /**
   * @description 递归加载所有cesium点标注数据（处理分页）
   * @returns {Promise<IMapAnnotation[]>} 标注数据列表
   */
  const loadAllFromDatabase = async (): Promise<IMapAnnotation[]> => {
    const allAnnotations: IMapAnnotation[] = [];
    let currentPage = 1;
    const pageSize = 50; // 每页50条记录
    
    try {
      while (true) {
        const query: PlottingPageQuery = {
          pageSize: pageSize,
          pageNum: currentPage,
          engineType: EngineType.CESIUM
        };
        console.log(`正在加载第${currentPage}页cesium标注数据...`);
        const response = await plottingPage(query);
        
        if (response.code === 200 && response.data?.list) {
          // 过滤cesium引擎类型的标注
          const cesiumRecords = response.data.list.filter(record => 
            record.engineType === 'Cesium'
          );

          // 转换为标注对象
          const annotations = cesiumRecords.map(record => 
            AnnotationAdapter.dbRecordToAnnotation(record)
          );

          allAnnotations.push(...annotations);
          
          // 如果返回的数据少于pageSize，说明已经是最后一页
          if (response.data.list.length < pageSize) {
            console.log(`已到达最后一页，共加载${currentPage}页数据`);
            break;
          }
          
          currentPage++;
        } else {
          console.warn(`第${currentPage}页数据加载失败`);
          break;
        }
      }

      console.log(`成功从数据库加载 ${allAnnotations.length} 个cesium点标注`);
      return allAnnotations;
    } catch (error) {
      console.error('从数据库加载cesium标注失败:', error);
      return allAnnotations; // 返回已加载的部分数据
    }
  };


  
  // 初始化标注列表
  const annotationList = ref<IMapAnnotation[]>([]);

  /**
   * @description 初始化cesium标注数据（仅从数据库加载）
   */
  const initAnnotations = async (): Promise<void> => {
    try {
      const cesiumAnnotations = await loadAllFromDatabase();
      annotationList.value = cesiumAnnotations;
      console.log(`从数据库加载了 ${cesiumAnnotations.length} 个cesium标注`);
    } catch (error) {
      console.error('从数据库加载cesium标注失败:', error);
      annotationList.value = [];
    }
  };

  // 异步初始化数据
  initAnnotations();
  
  /**
   * @description 生成唯一ID
   */
  const generateId = (): string => {
    return `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };
  
  /**
   * @description 添加标注到数据库（仅支持cesium）
   */
  const addAnnotation = async (params: CreateAnnotationParams): Promise<IMapAnnotation> => {
    const now = Date.now();
    const defaultStyle = createDefaultStyle();
    
    // 固定使用cesium引擎类型
    const engineType = EngineType.CESIUM;
    
    // 合并样式配置
    const style: AnnotationStyle = {
      color: params.style.color || defaultStyle.color,
      fontSize: params.style.fontSize || defaultStyle.fontSize,
      offsetX: params.style.offsetX || defaultStyle.offsetX,
      offsetY: params.style.offsetY || defaultStyle.offsetY,
      opacity: params.style.opacity || defaultStyle.opacity
    };
    
    // 创建GeoJSON Feature Properties
    const properties: AnnotationFeatureProperties = {
      type: 'annotation',
      style: style
    };
    
    // 如果是三维标注，添加高程信息
    if (params.alt !== undefined) {
      properties.alt = params.alt;
    }
    
    // 创建标准GeoJSON Feature
    const geojson: AnnotationGeoJSONFeature = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [params.lng, params.lat]
      },
      properties: properties
    };

    try {
      // Cesium标注保存到数据库
      const dbRequest = AnnotationAdapter.createParamsToDbRequest(params, engineType);
      const response = await plottingAdd(dbRequest);
      
      if (response.code === 200 && response.data) {
        const annotation: IMapAnnotation = {
          id: response.data.toString(),
          name: params.name.trim(),
          description: params.description || '',
          geojson: geojson,
          engineType: engineType,
          createTime: now,
          modifyTime: now,
          visible: true,
          locked: false
        };

        // 检查是否已存在相同名称的标注
        const existingIndex = annotationList.value.findIndex(item => 
          item.name === annotation.name && item.engineType === annotation.engineType
        );
        
        if (existingIndex > -1) {
          // 替换现有标注
          annotationList.value[existingIndex] = { ...annotation };
          console.log(`标注"${annotation.name}"已更新`);
        } else {
          // 添加新标注
          annotationList.value.push({ ...annotation });
          console.log(`标注"${annotation.name}"已添加`);
        }
        
        console.log(`Cesium标注"${annotation.name}"已保存到数据库`);
        return annotation;
      } else {
        throw new Error(`数据库保存失败`);
      }
    } catch (error) {
      console.error('保存标注失败:', error);
      ElMessage.error('保存标注到数据库失败');
      throw error;
    }
  };
  
  /**
   * @description 从数据库删除标注（仅支持cesium）
   */
  const removeAnnotation = async (id: string): Promise<boolean> => {
    const targetAnnotation = annotationList.value.find(annotation => annotation.id === id);
    
    if (!targetAnnotation) {
      console.warn(`标注 ${id} 不存在，删除失败`);
      return false;
    }

    try {
      const response = await plottingDelete(id);
      
      if (response.code === 200) {
        annotationList.value = annotationList.value.filter(annotation => annotation.id !== id);
        console.log(`标注"${targetAnnotation.name}"已从数据库删除`);
        return true;
      } else {
        ElMessage.error('数据库删除失败');
        return false;
      }
    } catch (error) {
      console.error('删除标注失败:', error);
      ElMessage.error('删除标注失败');
      return false;
    }
  };
  
  /**
   * @description 在数据库中更新标注（仅支持cesium）
   */
  const updateAnnotation = async (id: string, updates: Partial<IMapAnnotation>): Promise<boolean> => {
    const index = annotationList.value.findIndex(annotation => annotation.id === id);
    
    if (index === -1) {
      console.warn(`标注 ${id} 不存在，更新失败`);
      return false;
    }

    const annotation = annotationList.value[index];
    const updatedAnnotation = { 
      ...annotation, 
      ...updates,
      modifyTime: Date.now()
    };

    try {
      // 更新数据库中的标注
      const updateRequest = AnnotationAdapter.annotationToDbUpdateRequest(updatedAnnotation);
      const response = await plottingEdit(updateRequest);
      
      if (response.code === 200) {
        annotationList.value[index] = updatedAnnotation;
        console.log(`标注"${updatedAnnotation.name}"已在数据库中更新`);
        return true;
      } else {
        ElMessage.error('数据库更新失败');
        return false;
      }
    } catch (error) {
      console.error('更新标注失败:', error);
      ElMessage.error('更新标注失败');
      return false;
    }
  };
  
  /**
   * @description 根据ID查找标注
   */
  const getAnnotationById = (id: string): IMapAnnotation | undefined => {
    return annotationList.value.find(annotation => annotation.id === id);
  };
  
  /**
   * @description 获取所有cesium标注
   */
  const getAnnotationsByType = (): IMapAnnotation[] => {
    // 专门为Cesium设计，直接返回所有标注
    return [...annotationList.value];
  };
  
  /**
   * @description 搜索标注
   */
  const searchAnnotations = (keyword: string): IMapAnnotation[] => {
    if (!keyword || !keyword.trim()) {
      return [...annotationList.value];
    }
    
    const searchTerm = keyword.toLowerCase().trim();
    
    // 按关键词搜索
    return annotationList.value.filter(annotation => 
      annotation.name.toLowerCase().includes(searchTerm) ||
      (annotation.description && annotation.description.toLowerCase().includes(searchTerm))
    );
  };
  
  /**
   * @description 清除所有标注
   */
  const clearAllAnnotations = (): number => {
    const originalLength = annotationList.value.length;
    annotationList.value = [];
    
    if (originalLength > 0) {
      console.log(`已清除 ${originalLength} 个标注`);
    }
    
    return originalLength;
  };
  
  /**
   * @description 获取所有标注
   */
  const getAllAnnotations = (): IMapAnnotation[] => {
    return [...annotationList.value];
  };
  
  /**
   * @description 检查名称是否已存在
   */
  const isNameExists = (name: string, excludeId?: string): boolean => {
    return annotationList.value.some(annotation => 
      annotation.name === name && 
      annotation.id !== excludeId
    );
  };
  
  /**
   * @description 设置标注可见性
   */
  const setAnnotationVisibility = async (id: string, visible: boolean): Promise<boolean> => {
    return updateAnnotation(id, { visible });
  };
  
  /**
   * @description 设置标注锁定状态
   */
  const setAnnotationLocked = async (id: string, locked: boolean): Promise<boolean> => {
    return updateAnnotation(id, { locked });
  };

  

  
  // 返回公开的API（简化为cesium数据库操作）
  return {
    // 状态
    annotationList,
    
    // 核心方法
    addAnnotation,
    removeAnnotation,
    updateAnnotation,
    getAnnotationById,
    getAnnotationsByType,
    searchAnnotations,
    clearAllAnnotations,
    getAllAnnotations,
    isNameExists,
    setAnnotationVisibility,
    setAnnotationLocked,
    
    // 数据库操作
    loadAllFromDatabase,
    initAnnotations,

  };
}); 