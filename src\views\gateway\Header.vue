<template>
  <div class="custom-header">
    <div class="header-left items-center">
      <!-- <div class="flex items-center">
        <img class="logo" src="@/assets/images/gateway/earth.png" alt="" />
        <div class="sys-title">GIS管网</div>
      </div> -->
      <div class="flex items-center">
        <img class="logo" src="@/assets/images/header/logo.svg" alt="" />
        <div class="sys-title">铜河集团智慧水务</div>
      </div>
      <!-- <el-button class="expand-btn" link @click="handleExpand">
        <el-icon size="18" :class="{ 'icon-rotated': isCollapse }">
          <SvgIcon name="menu-expand" />
        </el-icon>
      </el-button> -->
      <span class="mx-8px font-700">/</span>
      <el-text class="sys-des mt-0.5">平台门户</el-text>
    </div>
    <div class="header-rt flex items-center">
      <div
        class="mr-10 cursor-pointer flex items-center"
        v-if="allWeather.forecast"
        @mouseover="isShow = true"
        @mouseout="isShow = false"
      >
        <span class="color-#fff font-500 font-size-4">乐山市</span>
        <img
          :src="
            allWeather.forecast[0].type.includes('雨')
              ? imgMapper['雨']
              : imgMapper[allWeather.forecast[0].type]
          "
          class="w-8 h-8 mx-2.5"
          alt=""
        />
        <span class="text-white font-size-3.5">{{
          allWeather.forecast[0].type
        }}</span>
        <span class="text-white font-size-3.5 ml-1">{{
          Math.floor(allWeather.wendu) + "°"
        }}</span>
      </div>
      <el-button
        @click="toSdkDoc"
        type="text"
        class="color-#fff font-size-4"
        size="large"
        >开发文档</el-button
      >
      <el-dropdown>
        <div
          class="h-full w-full cursor-pointer el-dropdown-link"
          flex="~ items-center gap-x-2"
        >
          <img class="size-35px" :src="localCache.getCache('Photo') ?? user" />
          <span class="text-#ffffff" font="size-4 400">{{
            localCache.getCache("Name")
          }}</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="toApiDoc"> 二次开发API </el-dropdown-item>
            <el-dropdown-item @click="toLeaflet"> JSAPI for Leaflet </el-dropdown-item>
            <el-dropdown-item @click="toMapboxGL"> JSAPI for MapboxGL </el-dropdown-item>
            <el-dropdown-item @click="toVersion"> 版本说明 </el-dropdown-item>
            <el-dropdown-item @click="loginOut"> 登 出 </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div v-if="isShow" class="absolute top-30 right-30 z-1">
      <weather v-model:params="allWeather" />
    </div>

    <!-- 版本说明弹窗 -->
    <el-dialog
      v-model="versionDialogVisible"
      title="版本说明"
      width="900px"
      top="5vh"
      :before-close="handleCloseVersionDialog"
      class="version-dialog"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div v-if="versionLoading" class="loading-container">
        <el-icon class="is-loading loading-icon"><Loading /></el-icon>
        <span class="loading-text">正在加载版本信息...</span>
      </div>
      <div
        v-else-if="versionContent"
        v-html="versionContent"
        class="markdown-content"
      ></div>
      <div v-else class="error-container">
        <el-icon class="error-icon"><Warning /></el-icon>
        <span class="error-text">加载失败，请稍后重试</span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="versionDialogVisible = false">
            <el-icon><Check /></el-icon>
            知道了
          </el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { getImages } from "@/utils/getImages";
import weather from "@/views/maplibre/weather/index.vue";
import axios from "axios";
import localCache from "@/utils/auth";
import { Loading, Warning, Check } from '@element-plus/icons-vue';
import { marked } from 'marked';

const isShow = ref(false);
const user = getImages("header/user.png");

// 版本说明相关状态
const versionDialogVisible = ref(false);
const versionLoading = ref(false);
const versionContent = ref('');

const loginOut = () => {
  localStorage.clear();
  window.location.href = "https://engine.lshywater.cn/#/login";
  // router.replace("/login");
};

const toSdkDoc = () => {
  window.open("http://gis.lshywater.cn/sdkdoc/");
}

const toApiDoc = () => {
  window.open("http://gis.lshywater.cn/apidoc/apidoc/basic-services.html");
}

// Leaflet文档
const toLeaflet = () => {
  window.open("https://leafletjs.com/reference.html");
};

// MapboxGL文档
const toMapboxGL = () => {
  window.open("https://docs.mapbox.com/mapbox-gl-js/guides/");
}

const toVersion = async () => {
  versionDialogVisible.value = true;
  versionLoading.value = true;

  try {
    // 加载 MD 文件
    const response = await fetch('/VERSION.md');
    if (!response.ok) {
      throw new Error('Failed to load version file');
    }

    const markdownText = await response.text();
    // 使用 marked 渲染 markdown
    let html = await marked(markdownText);

    // 去掉所有 emoji 图标
    html = html.replace(/🚀|🔧|🐛|⚠️|📝/g, '');

    // 使用模拟 Element Plus 标签样式的 span 替换功能分类
    html = html.replace(/<h3>\s*新增功能<\/h3>/g, '<h3><span class="feature-tag feature-tag--success">新增功能</span></h3>');
    html = html.replace(/<h3>\s*功能优化<\/h3>/g, '<h3><span class="feature-tag feature-tag--warning">功能优化</span></h3>');
    html = html.replace(/<h3>\s*问题修复<\/h3>/g, '<h3><span class="feature-tag feature-tag--danger">问题修复</span></h3>');

    // 清理多余的空格
    html = html.replace(/\s+/g, ' ').replace(/>\s+</g, '><');

    versionContent.value = html;
  } catch (error) {
    console.error('加载版本文件失败:', error);
    versionContent.value = '';
  } finally {
    versionLoading.value = false;
  }
}

const handleCloseVersionDialog = () => {
  versionDialogVisible.value = false;
  versionContent.value = '';
}

const allWeather = ref<any>([]);
const imgMapper: any = {
  阴: getImages("weather/yin.png"),
  雨: getImages("weather/rain.png"),
  多云: getImages("weather/csun.png"),
  晴: getImages("weather/sunny.png"),
};
const getWeather = () => {
  axios.get("/weather/api/weather/city/101271401").then((res: any) => {
    allWeather.value = res.data.data;
    console.log(res.data.data);
  });
};
getWeather();
</script>

<style scoped lang="scss">
.custom-header {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 13px;
  background: url("@/assets/images/header/header-bg.png") left top / 100% 100%
    no-repeat;
}
.header-left {
  display: flex;
  color: #fff;

  .logo {
    width: 40px;
    height: 40px;
  }
  .sys-title {
    font: 24px YousheBiaoTiHei;
    box-shadow: 0 2px 4px 0 rgba($color: #4f87f8, $alpha: 0.4);
    margin-left: 10px;
  }
  .expand-btn {
    color: #fff;
    margin-left: 10px;
    --el-button-active-color: rgba($color: #fff, $alpha: 0.8);
  }
  .sys-des {
    color: #fff;
    // margin-left: 5px;
    font: 400 22px YousheBiaoTiHei;
  }
  .expand-btn .el-icon {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .icon-rotated {
    transform: rotate(180deg);
  }
}
.line {
  width: 2px;
  height: 20px;
  // line-height: 60px;
  background: #fff;
  margin: 10px 10px 0 10px;
}

/* 版本弹窗样式 */
:deep(.version-dialog) {
  .el-dialog {
    max-height: 90vh;
    margin: 5vh auto;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__header {
    padding: 20px 24px;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        font-size: 20px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 0;
    max-height: 60vh;
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    text-align: center;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  .loading-icon {
    font-size: 32px;
    color: #409eff;
    margin-bottom: 16px;
  }

  .loading-text {
    font-size: 16px;
    color: #666;
  }
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  .error-icon {
    font-size: 32px;
    color: #f56c6c;
    margin-bottom: 16px;
  }

  .error-text {
    font-size: 16px;
    color: #666;
  }
}

/* Markdown 内容样式 */
:deep(.markdown-content) {
  padding: 24px;
  line-height: 1.7;
  color: var(--el-text-color-primary);
  font-size: 14px;

  h1 {
    font-size: 28px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    margin: 0 0 24px 0;
    padding-bottom: 12px;
    border-bottom: 3px solid var(--el-color-primary);
    text-align: center;
  }

  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #34495e;
    margin: 32px 0 16px 0;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid var(--el-color-primary);
    border-radius: 0 6px 6px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 12px 0;
    padding: 0;
  }

  /* 模拟 Element Plus 标签样式 */
  // .feature-tag {
  //   display: inline-block;
  //   padding: 4px 12px;
  //   font-size: 12px;
  //   font-weight: 500;
  //   line-height: 1.5;
  //   border-radius: 4px;
  //   border: 1px solid;

  //   &.feature-tag--success {
  //     color: #67c23a;
  //     background-color: #f0f9ff;
  //     border-color: #67c23a;
  //   }

  //   &.feature-tag--warning {
  //     color: #e6a23c;
  //     background-color: #fdf6ec;
  //     border-color: #e6a23c;
  //   }

  //   &.feature-tag--danger {
  //     color: #f56c6c;
  //     background-color: #fef0f0;
  //     border-color: #f56c6c;
  //   }
  // }

  ul {
    margin: 12px 0;
    padding-left: 0;
  }

  li {
    list-style: none;
    margin: 4px 0 4px 32px;
    padding: 0;
    line-height: 1.6;
    position: relative;

    &:before {
      content: "•";
      color: var(--el-text-color-regular);
      position: absolute;
      left: -16px;
      top: 0;
    }

    strong {
      font-weight: 600;
      margin-right: 8px;
    }
  }

  hr {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, transparent, #dee2e6, transparent);
    margin: 32px 0;
  }

  p {
    margin: 12px 0;

    &:last-child {
      text-align: center;
      font-style: italic;
      color: #6c757d;
      font-size: 13px;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #dee2e6;
    }
  }
}
</style>
