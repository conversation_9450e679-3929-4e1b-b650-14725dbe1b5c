[{"id": "pipes", "name": "pipes", "type": "mvt", "children": [{"id": "mvt_pipeLine_34", "name": "城乡供水一体化项目首期配水工程", "type": "mvt", "showLayerControl": true, "icon": "pipe", "options": {"devurl": "http://************:9111/api/analyse/gs/ln/zg/mvt/{z}/{x}/{y}.mvt", "url": "https://gis.lshywater.cn/api/analyse/gs/ln/zg/mvt/{z}/{x}/{y}.mvt", "type": "line", "source-layer": "line", "layout": {"line-join": "round", "line-cap": "round"}, "paint": {"line-color": "#e84133", "line-width": 3}}}, {"id": "mvt_pipeLine_33", "name": "智慧水务提升改造项目一期", "type": "mvt", "showLayerControl": true, "icon": "pipe", "children": [{"id": "mvt_pipeLine", "name": "管线", "type": "mvt", "options": {"devurl": "http://************:9111/api/analyse/gs/ln/mvt/{z}/{x}/{y}.mvt", "url": "https://gis.lshywater.cn/api/analyse/gs/ln/mvt/{z}/{x}/{y}.mvt", "type": "line", "source-layer": "line", "layout": {"line-join": "round", "line-cap": "round"}, "paint": {"line-color": "#2b78fe", "line-width": 3}}}, {"id": "mvt_pipeNode", "name": "管点", "type": "mvt", "options": {"devurl": "http://************:9111/api/analyse/gs/pt/mvt/{z}/{x}/{y}.mvt", "url": "https://gis.lshywater.cn/api/analyse/gs/pt/mvt/{z}/{x}/{y}.mvt", "type": "circle", "source-layer": "point", "minzoom": 15, "paint": {"circle-color": "#FFFFFF", "circle-radius": 5, "circle-stroke-width": 3, "circle-stroke-color": "#2b78fe"}}}]}]}]