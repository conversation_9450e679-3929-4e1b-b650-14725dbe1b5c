/*
 * @Author: xiao
 * @Date: 2022-07-20 17:28:38
 * @LastEditors: xiao
 * @LastEditTime: 2024-10-09 14:09:40
 * @Description: 浏览器本地存储
 */
class LocalCache {
  setCache(key: string, value: any) {
    window.localStorage.setItem(key, JSON.stringify(value))
  }

  getCache(key: string) {
    // obj => string => obj
    const value = window.localStorage.getItem(key)
    try {
      if (value) {
        return JSON.parse(value)
      }
    } catch (error) {
      this.deleteCache(key)
      return undefined
    }
  }

  deleteCache(key: string) {
    window.localStorage.removeItem(key)
  }

  clearCache() {
    window.localStorage.clear()
  }
}

export default new LocalCache()
