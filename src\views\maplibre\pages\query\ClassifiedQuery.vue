<template>
  <page-card class="classified-query" title="分类查询" :close-icon="false">
    <el-row>
      <el-text>查询方式：</el-text>
      <div class="buttons-row">
        <query-button v-for="button in deviceStatisticsTypes" :key="button.value" type="info"
          :loading="isLoading && currType === button.value" :disabled="isLoading && currType !== button.value"
          :active="isActive(button.value).value"
          @click="handleDeviceStatistics(button.value)">
          {{ button.label }}
        </query-button>
      </div>
    </el-row>
    <el-form justify="space-between" class="mt-16px" label-width="auto">
      <el-form-item v-if="!isActive('hydrantQuery').value" class="page-form-item" label-width="70px" label="附属物：">
        <el-select v-model="queryForm.fsw" placeholder="请选择">
          <el-option v-for="item in fswOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item class="page-form-item">
      <el-row class="gap-12px flex-nowrap items-center">
        <el-form-item v-if="isActive('roundRoad').value" class="page-form-item radius-form-item" label="查询半径：">
          <el-input v-model="radiusValue" placeholder="请输入" >
            <template #append>米</template>
          </el-input>
          <query-button class="ml-8px" type="primary" @click="handlePickLocation">拾取</query-button>
        </el-form-item>
        <el-form-item v-if="isActive('quickQuery').value" class="page-form-item" label="编号查询：">
          <el-input v-model="queryForm.bh" placeholder="请输入" />
        </el-form-item>
        <el-form-item v-if="isActive('hydrantQuery').value" class="page-form-item" label="消防栓编码：">
          <el-input v-model="queryForm.bh" placeholder="请输入" />
        </el-form-item>
        <el-form-item v-if="isActive('locationQuery').value || isActive('hydrantQuery').value" class="page-form-item" label="所属道路：">
          <el-select v-model="queryForm.szdl" filterable :filter-method="roadFilterFc" placeholder="选择道路">
            <el-option v-for="item in roadOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item class="page-form-item query-buttons ml-auto justify-end">
          <query-button :loading="false" type="primary" @click="handleQuery">查询</query-button>
          <query-button class="reset-button" :loading="false" type="info" @click="handleReset">重置</query-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table
      :data="tableData"
       class="routeCt h-70"
      style="width: 100%"
      v-loading="tableLoading"
      empty-text= "暂无数据"
         :row-class-name="tableRowClassName"
    >
      <el-table-column
        label="序号"
        width="60"
        align="center"
      >
        <template #default="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="gxddh"
        label="管点编号"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="gl"
        label="管类"
      >
    <template #default="scope">
      给水
        </template></el-table-column>
      <el-table-column
        prop="sx"
        label="属性"
      ></el-table-column>
      <el-table-column
        prop="fsw"
        label="附属物"
        show-overflow-tooltip
      ></el-table-column>
      
      <el-table-column
        prop="szdl"
        label="所在道路"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            type="text"
            style="color: #1966ff"
            @click="handleCheck(scope.row)"
          >
            详情
          </el-button>
          <el-button
            type="text"
            style="color: #1966ff"
            @click="handleLocation(scope.row)"
          >
            定位
          </el-button>
        </template>
      </el-table-column>
    </el-table>
        <div class="flex justify-between items-center mt-5">
          <div>
            <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
          </div>
    <el-pagination
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      :current-page="currentPage"
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :total="total"
      :pager-count="5"
            layout="prev, pager, next, jumper"
            class="pagination"
            background
            small
    ></el-pagination>
    </div>
  </page-card>
  <ClssifiedQueryDetail v-if="cardVisible" v-model:card-visible="cardVisible" :loading="detailLoading" v-model:detail-info="detailInfo" />
</template>
<script setup lang="ts">
import { classifiedQuery,roadList } from '@/api/query';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import { AppCesium } from '@/lib/cesium/AppCesium';
import { DrawEventTypeEnum, type DrawMode, type DrawManager, type DrawEventData } from '@/lib/maplibre/draw';
import * as turf from '@turf/turf';
import ClssifiedQueryDetail from './ClssifiedQueryDetail.vue';
import { pipeNodeDetail, queryPipeFswList } from '@/api/pipeNode';
import { pipeLocationByBms, clearPipeLocationHighlight } from '@/utils/PipeLocation';

const fswOptions = ref<string[]>([]);
const getFswOptions = async () => {
  try {
    const res = await queryPipeFswList();
    if (res.code === 200 && Array.isArray(res.data)) {
      fswOptions.value = res.data;
    } else {
      throw new Error(res.msg || '获取附属物类型失败');
    }
  } catch (error) {
    ElMessage.error('获取附属物类型失败');
  }
}
getFswOptions()


const roadOptions = ref<string[]>([]);
let allRoadOptions: string[] = []; // 保存完整的道路列表

const getRoadList = async () => {
  try {
    const result = await roadList() // ['***','***','...']
    if (result.code === 200) {
      allRoadOptions = result.data;
      roadOptions.value = result.data; // 初始显示所有选项
    }
  } catch (error) {
    console.error("获取道路列表失败", error);
  }
}
getRoadList();

/**
 * 道路选择器过滤函数
 * @param query 用户输入的查询字符串
 */
const roadFilterFc = (query: string) => {
  if (!query) {
    // 如果查询为空，显示所有选项
    roadOptions.value = allRoadOptions;
  } else {
    // 根据查询字符串过滤道路选项（支持模糊匹配）
    roadOptions.value = allRoadOptions.filter(road =>
      road.toLowerCase().includes(query.toLowerCase())
    );
  }
};

const deviceStatisticsTypes = [
  { label: '周边查询', value: 'roundRoad' },
  { label: '快速查询', value: 'quickQuery' },
  { label: '位置查询', value: 'locationQuery' },
  { label: '消防栓查询', value: 'hydrantQuery' }
] as const;
type DeviceStatisticsType = typeof deviceStatisticsTypes[number]['value'];

// 默认选中"周边查询"
const { setActive, isActive, activeValue } = useActiveButton<DeviceStatisticsType>();

const currType = ref('');
const isLoading = ref(false);

// 拾取功能相关状态
const isPicking = ref(false);
const isProcessingDraw = ref(false); // 防止重复处理绘制完成事件
const pickedLocation = ref<{ lng: number; lat: number } | null>(null);
const pickedGeometry = ref<any>(null);

// 地图引擎类型
const route = useRoute();
const mapEngine = computed(() => {
  return route.path.includes('cesium') ? 'cesium' : 'maplibre';
});

// 绘制工具
let drawTool: DrawManager | null = null;

// 缓冲区图层管理
let cesiumBufferLayer: any = null;
let maplibreBufferSourceId = 'classified-query-buffer-source';
let maplibreBufferFillLayerId = 'classified-query-buffer-fill-layer';
let maplibreBufferLineLayerId = 'classified-query-buffer-line-layer';

// 高亮点位管理
let cesiumHighlightLayer: any = null;
let maplibreHighlightSourceId = 'classified-query-highlight-source';
let maplibreHighlightLayerId = 'classified-query-highlight-layer';
const highlightedFeatures = ref<any[]>([]);

const initForm = () => {
  return {
    pageSize: 10,
    pageNum: 1,
    json: '', // 半径
    fsw: '',  // 模糊查询
    szdl: '', // 所在道路
    bh: '', // 消防栓编号
    flag: ''  // "消防栓"选项时默认 1, 其他为 0 
  }
}

const queryForm = ref(initForm())

const handleDeviceStatistics = (type: DeviceStatisticsType) => {
  currType.value = type;
  setActive(type);
  queryForm.value = initForm()
  clearBuffer()
  clearHighlights() // 清除高亮点位
  tableData.value = []

  // 清空拾取相关的状态
  pickedLocation.value = null;
  pickedGeometry.value = null;
  isPicking.value = false;
  isProcessingDraw.value = false;
  radiusValue.value = 200;

  handleQuery()
  cardVisible.value = false
};

const radiusValue = ref(200);

const handleQuery = () => {
  // 检查是否选中了查询方式
  if (!activeValue.value) {
    ElMessage.warning('请先选择一个查询方式');
    return;
  }
  switch (activeValue.value) {
    case 'roundRoad':
      // 周边查询需要先拾取点
      if (!pickedGeometry.value) {
        ElMessage.warning('请先在地图上拾取查询点');
        return;
      }
      executeClassifiedQuery()
      break;
    case 'quickQuery':
    case 'locationQuery':
    case 'hydrantQuery':
      executeClassifiedQuery()
      break;
  }
}

const handleReset = () => {
  queryForm.value = initForm();
  if(isActive('roundRoad').value) {
    tableData.value = []
  } else {
    executeClassifiedQuery()
  }
  radiusValue.value = 200;

  // 清除拾取状态和缓冲区
  pickedLocation.value = null;
  pickedGeometry.value = null;
  isPicking.value = false;
  isProcessingDraw.value = false;

  // 移除事件监听器
  if (drawTool) {
    try {
      drawTool.removeEventListener(DrawEventTypeEnum.DRAW_FINISH, handleDrawFinish);
    } catch (e) {
      // 忽略移除不存在的监听器的错误
    }
  }

  clearBuffer();
  clearHighlights(); // 清除高亮点位
}

const handlePickLocation = async () => {
  if (isPicking.value) {
    ElMessage.warning('正在拾取中，请先完成当前操作');
    return;
  }

  try {
    isPicking.value = true;

    if (mapEngine.value === 'cesium') {
      await handleCesiumPick();
    } else {
      await handleMaplibrePick();
    }
  } catch (error) {
    console.error('❌ 拾取位置失败:', error);
    ElMessage.error('拾取位置失败');
    isPicking.value = false;
  }
};

const cardVisible = ref(false)
const detailLoading = ref(false)
const detailInfo = ref<Record<string,any>>()

const handleCheck = async (row: any) => {
  try {
    detailLoading.value = true
    cardVisible.value = true
    const result = await pipeNodeDetail(row.gid)
    if (result && result?.code === 200) {
      detailInfo.value = result.data
    }
  } catch (error) {
    console.error('获取管点详情错误',error)
  } finally {
    detailLoading.value = false
  }
}

/**
 * 处理Cesium地图拾取
 */
const handleCesiumPick = async () => {
  const plotUtil = AppCesium.getInstance().getPlotUtil();

  ElMessage.info('请在地图上点击选择位置');

  plotUtil.draw('point', (overlay: any) => {

    const position = overlay.position;
    pickedLocation.value = { lng: position.lng, lat: position.lat };

    // 创建点Feature
    const pointFeature = turf.point([position.lng, position.lat]);

    // 使用turf计算缓冲区
    const bufferDistanceInKm = Number(radiusValue.value) / 1000;
    const buffered = turf.buffer(pointFeature, bufferDistanceInKm, {
      units: 'kilometers'
    });

    pickedGeometry.value = buffered;

    // 将GeoJSON数据赋值给queryForm的json字段
    queryForm.value.json = JSON.stringify(buffered?.geometry);

    // 渲染缓冲区图形
    renderBufferOnCesium(buffered);

    // 还原鼠标状态
    isPicking.value = false;

    ElMessage.success(`位置拾取成功: ${position.lng.toFixed(6)}, ${position.lat.toFixed(6)}`);

    // 自动发送查询请求
    executeClassifiedQuery();
  }, {}, true);
};

/**
 * 处理MapLibre地图拾取
 */
const handleMaplibrePick = async () => {
  if (!drawTool) {
    // 初始化绘制工具
    drawTool = AppMaplibre.getDrawTool();
    if (!drawTool.isEnabled()) {
      await drawTool.start(true);
    }
  }

  // 重置处理状态
  isProcessingDraw.value = false;

  // 确保事件监听器已添加（避免重复添加）
  try {
    drawTool.removeEventListener(DrawEventTypeEnum.DRAW_FINISH, handleDrawFinish);
  } catch (e) {
    // 忽略移除不存在的监听器的错误
  }
  drawTool.addEventListener(DrawEventTypeEnum.DRAW_FINISH, handleDrawFinish);

  ElMessage.info('请在地图上点击选择位置');

  // 清除之前的绘制结果
  drawTool.clearAllFeatures();
  // 切换到点绘制模式
  drawTool.setMode('point' as DrawMode);
};

/**
 * 处理MapLibre绘制完成事件
 */
const handleDrawFinish = (event: DrawEventData) => {

  // 防止重复处理
  if (isProcessingDraw.value) {
    return;
  }

  // 检查是否正在拾取状态
  if (!isPicking.value) {
    return;
  }

  try {
    isProcessingDraw.value = true;

    if (event.features) {
      const features = Array.isArray(event.features) ? event.features : [event.features];

      if (features.length > 0) {
        const feature = features[0];
        if (feature && feature.geometry && feature.geometry.type === 'Point') {
          const coordinates = feature.geometry.coordinates;
          pickedLocation.value = { lng: coordinates[0], lat: coordinates[1] };

          // 使用turf计算缓冲区
          const bufferDistanceInKm = Number(radiusValue.value) / 1000;
          const buffered = turf.buffer(feature, bufferDistanceInKm, {
            units: 'kilometers'
          });

          pickedGeometry.value = buffered;
          // 将GeoJSON数据赋值给queryForm的json字段
          queryForm.value.json = JSON.stringify(buffered?.geometry);

          // 渲染缓冲区图形
          renderBufferOnMapLibre(buffered);

          // 切换回选择模式并移除事件监听器
          if (drawTool) {
            drawTool.setMode('select' as DrawMode);
            // 移除事件监听器，防止后续触发
            drawTool.removeEventListener(DrawEventTypeEnum.DRAW_FINISH, handleDrawFinish);
          }

          // 还原状态
          isPicking.value = false;
          isProcessingDraw.value = false;

          ElMessage.success(`位置拾取成功: ${coordinates[0].toFixed(6)}, ${coordinates[1].toFixed(6)}`);

          // 自动发送查询请求
          executeClassifiedQuery();

          return; // 成功处理后直接返回
        }
      }
    }

    // 如果没有成功处理，重置状态
    isProcessingDraw.value = false;

  } catch (error) {
    console.error('❌ 处理拾取结果失败:', error);
    ElMessage.error('处理拾取结果失败');
    isPicking.value = false;
    isProcessingDraw.value = false;
  }
};

/**
 * 在Cesium上渲染缓冲区
 */
const renderBufferOnCesium = async (bufferGeometry: any) => {
  const {Cesium} = BC.Namespace;
  try {
    const viewer = AppCesium.getInstance().getViewer();

    // 清除之前的缓冲区GroundPrimitive
    if (cesiumBufferLayer) {
      viewer.scene.groundPrimitives.remove(cesiumBufferLayer);
      cesiumBufferLayer = null;
    }

    // 转换坐标为Cesium格式
    const coordinates = bufferGeometry.geometry.coordinates[0];
    const positions: number[] = [];

    coordinates.forEach((coord: number[]) => {
      positions.push(coord[0], coord[1]); // 经度, 纬度
    });

    // 创建多边形几何体
    const polygonGeometry = new Cesium.PolygonGeometry({
      polygonHierarchy: new Cesium.PolygonHierarchy(
        Cesium.Cartesian3.fromDegreesArray(positions)
      ),
      vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT
    });

    // 创建几何体实例
    const geometryInstance = new Cesium.GeometryInstance({
      geometry: polygonGeometry,
      attributes: {
        color: Cesium.ColorGeometryInstanceAttribute.fromColor(
          Cesium.Color.BLUE.withAlpha(0.3) // 蓝色半透明
        )
      }
    });

    // 创建GroundPrimitive进行渲染
    cesiumBufferLayer = new Cesium.GroundPrimitive({
      geometryInstances: geometryInstance,
      appearance: new Cesium.PerInstanceColorAppearance({
        translucent: true,
        closed: false
      }),
      asynchronous: false
    });

    // 添加到场景
    viewer.scene.groundPrimitives.add(cesiumBufferLayer);

  } catch (error) {
    console.error('Cesium缓冲区渲染失败:', error);
  }
};

/**
 * 在MapLibre上渲染缓冲区
 */
const renderBufferOnMapLibre = async (bufferGeometry: any) => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      throw new Error('MapLibre地图实例不存在');
    }

    // 移除现有的缓冲区图层
    if (map.getLayer(maplibreBufferFillLayerId)) {
      map.removeLayer(maplibreBufferFillLayerId);
    }
    if (map.getLayer(maplibreBufferLineLayerId)) {
      map.removeLayer(maplibreBufferLineLayerId);
    }
    if (map.getSource(maplibreBufferSourceId)) {
      map.removeSource(maplibreBufferSourceId);
    }

    // 添加缓冲区数据源
    map.addSource(maplibreBufferSourceId, {
      type: 'geojson',
      data: bufferGeometry
    });

    // 添加填充图层（蓝色半透明面）
    map.addLayer({
      id: maplibreBufferFillLayerId,
      type: 'fill',
      source: maplibreBufferSourceId,
      layout: {},
      paint: {
        'fill-color': '#0080ff', // 蓝色
        'fill-opacity': 0.3      // 半透明
      }
    });

    // 添加边框图层（蓝色虚线）
    map.addLayer({
      id: maplibreBufferLineLayerId,
      type: 'line',
      source: maplibreBufferSourceId,
      layout: {},
      paint: {
        'line-color': '#0080ff',    // 蓝色
        'line-width': 2,            // 线宽
        'line-dasharray': [5, 5]    // 虚线样式
      }
    });

  } catch (error) {
    console.error('MapLibre缓冲区渲染失败:', error);
  }
};

// 表格相关状态
const tableData = ref([]);
const tableLoading = ref(false);

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const pageSizes = [10, 20, 50, 100];

/**
 * 分页页码改变
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  queryForm.value.pageNum = page;
  executeClassifiedQuery();
};

/**
 * 分页大小改变
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  queryForm.value.pageSize = size;
  queryForm.value.pageNum = 1;
  executeClassifiedQuery();
};


/**
 * 执行分类查询请求
 */
const executeClassifiedQuery = async () => {
  try {
    tableLoading.value = true;
    isLoading.value = true;

    // 二次查询时先清空之前的高亮点位
    clearHighlights();

    if (activeValue.value === 'hydrantQuery') {
      queryForm.value.flag = '1'
    }

    const result = await classifiedQuery(queryForm.value);

    if (result && result.code === 200) {
      // ElMessage.success('查询成功');

      // 更新表格数据和分页信息
      tableData.value = result.data.list || [];
      total.value = result.data.totalCount || 0;
      currentPage.value = result.data.currPage || 1;
      pageSize.value = result.data.pageSize || 10;

      // 高亮查询结果点位
      if (tableData.value.length > 0) {
        highlightQueryResults(tableData.value);
      }
    } else {
      ElMessage.error('查询失败: ' + (result?.msg || '未知错误'));
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('查询请求失败:', error);
    ElMessage.error('查询请求失败，请稍后重试');
    tableData.value = [];
    total.value = 0;
  } finally {
    tableLoading.value = false;
    isLoading.value = false;
  }
};

/**
 * 处理表格行点击事件
 * @param row 点击的行数据
 */
const handleLocation = async (row: any) => {
  try {
    console.log('🎯 表格行点击，定位到点位:', row);

    if (mapEngine.value === 'cesium') {
      // Cesium模式：执行飞行和高亮
      await flyToPointOnCesium(row);
    } else {
      // MapLibre模式：飞行到点位并临时高亮
      await flyToPointOnMapLibre(row);
    }

  } catch (error) {
    console.error('❌ 定位失败:', error);
    ElMessage.error('定位失败，请稍后重试');
  }
};

/**
 * @description 在MapLibre上飞行到指定点位
 * @param row 行数据
 */
const flyToPointOnMapLibre = async (row: any) => {
  try {
    if (!row.geojson) {
      ElMessage.warning('该记录缺少坐标信息');
      return;
    }

    const geojson = JSON.parse(row.geojson);
    if (geojson.type !== 'Point' || !geojson.coordinates || geojson.coordinates.length < 2) {
      ElMessage.warning('坐标数据格式错误');
      return;
    }

    const [lng, lat] = geojson.coordinates;
    const map = AppMaplibre.getMap();

    if (!map) {
      console.error('❌ MapLibre地图实例不存在');
      return;
    }

    // 飞行到点位
    map.flyTo({
      center: [lng, lat],
      zoom: Math.max(map.getZoom(), 16), // 确保缩放级别足够大
      duration: 1500
    });

    // 临时高亮该点位（闪烁效果）
    highlightSinglePoint(row, 3000); // 高亮3秒

    console.log('✅ 飞行到点位成功:', lng, lat);

  } catch (error) {
    console.error('❌ MapLibre飞行失败:', error);
    throw error;
  }
};

/**
 * @description 在Cesium上飞行到指定点位
 * @param row 行数据
 */
const flyToPointOnCesium = async (row: any) => {
  try {
    console.log('🎯 Cesium飞行到点位:', row);

    // 先清除之前的高亮
    clearPipeLocationHighlight('cesium');

    // 使用原有的locPipePoint逻辑进行飞行
    locPipePoint(row);

    // 使用pipeLocationByBms进行高亮
    const pipeCode = row.gxddh || row.bm || row.gid;
    if (pipeCode) {
      await pipeLocationByBms([pipeCode], 'cesium', 'point', {
        autoFit: false, // 不自动缩放，使用locPipePoint的飞行逻辑
        highlightColor: '#ffff00', // 黄色临时高亮
        pointRadius: 10
      });

      // 3秒后清除临时高亮
      setTimeout(() => {
        clearPipeLocationHighlight('cesium');
      }, 3000);
    }

    console.log('✅ Cesium飞行和高亮完成');

  } catch (error) {
    console.error('❌ Cesium飞行失败:', error);
    throw error;
  }
};

/**
 * @description 临时高亮单个点位
 * @param row 行数据
 * @param duration 高亮持续时间（毫秒）
 */
const highlightSinglePoint = (row: any, duration: number = 3000) => {
  try {
    const map = AppMaplibre.getMap();
    if (!map) return;

    const tempSourceId = 'temp-highlight-source';
    const tempLayerId = 'temp-highlight-layer';

    // 清除之前的临时高亮
    if (map.getLayer(tempLayerId)) {
      map.removeLayer(tempLayerId);
    }
    if (map.getSource(tempSourceId)) {
      map.removeSource(tempSourceId);
    }

    const geojson = JSON.parse(row.geojson);
    const feature = {
      type: 'Feature' as const,
      geometry: geojson,
      properties: row
    };

    // 添加临时高亮数据源
    map.addSource(tempSourceId, {
      type: 'geojson',
      data: feature
    });

    // 添加临时高亮图层（更大的黄色圆点）
    map.addLayer({
      id: tempLayerId,
      type: 'circle',
      source: tempSourceId,
      paint: {
        'circle-radius': 10,
        'circle-color': '#ffff00',  // 黄色
        'circle-opacity': 0.8,
        'circle-stroke-width': 3,
        'circle-stroke-color': '#ff0000',  // 红色边框
        'circle-stroke-opacity': 1
      }
    });

    // 添加闪烁动画
    let opacity = 0.8;
    let increasing = false;
    const animate = () => {
      if (!map.getLayer(tempLayerId)) return;

      if (increasing) {
        opacity += 0.1;
        if (opacity >= 1.0) {
          opacity = 1.0;
          increasing = false;
        }
      } else {
        opacity -= 0.1;
        if (opacity <= 0.3) {
          opacity = 0.3;
          increasing = true;
        }
      }

      try {
        map.setPaintProperty(tempLayerId, 'circle-opacity', opacity);
        requestAnimationFrame(animate);
      } catch (error) {
        // 图层已被移除，停止动画
      }
    };

    animate();

    // 定时清除临时高亮
    setTimeout(() => {
      try {
        if (map.getLayer(tempLayerId)) {
          map.removeLayer(tempLayerId);
        }
        if (map.getSource(tempSourceId)) {
          map.removeSource(tempSourceId);
        }
      } catch (error) {
        console.warn('清除临时高亮失败:', error);
      }
    }, duration);

  } catch (error) {
    console.error('❌ 临时高亮失败:', error);
  }
};
const setHeight = (point: any, height: number) => {
  const { Cesium } = BC.Namespace
  let cartographic = Cesium.Cartographic.fromCartesian(point)
  cartographic.height = height
  return Cesium.Cartographic.toCartesian(cartographic)
}

const locPipePoint = (row: any) => {
  
  const { Cesium } = BC.Namespace
  let geojson = JSON.parse(row.geojson)
    let geojsonLayer = new BC.GeoJsonLayer('locGeoJsonLayer', geojson)
    geojsonLayer.addTo(AppCesium.getInstance().getViewer())
    geojsonLayer.eachOverlay((overlay: any) => {
      const cartesian3 = overlay.position.getValue(new BC.JulianDate())
      const newCartesian3 = setHeight(
        cartesian3,
        parseFloat(row.dmgc || 400)
      )
      AppCesium.getInstance()
        .getViewer()
        .camera.lookAt(
          newCartesian3,
          new Cesium.HeadingPitchRange(
            BC.Math.toRadians(14.192604699345909),
            BC.Math.toRadians(-45.46914000324496 || 0),
            30
          )
        )

      AppCesium.getInstance().getViewer().camera.lookAtTransform(BC.Matrix4.IDENTITY)
    }, this)
    AppCesium.getInstance().getViewer().removeLayer(geojsonLayer)
}

// 移除未使用的flyToCesium函数，使用locPipePoint和pipeLocationByBms替代



/**
 * @description 高亮查询结果点位
 * @param results 查询结果数据
 */
const highlightQueryResults = (results: any[]) => {
  try {
    console.log('🎯 开始高亮查询结果点位，数量:', results.length);

    // 先清除之前的高亮
    clearHighlights();

    if (!results || results.length === 0) {
      console.log('📍 没有查询结果需要高亮');
      return;
    }

    // 过滤有效的坐标数据
    const validResults = results.filter(item => {
      if (!item.geojson) {
        console.warn('⚠️ 记录缺少geojson字段:', item);
        return false;
      }

      try {
        const geojson = JSON.parse(item.geojson);
        return geojson.type === 'Point' &&
               geojson.coordinates &&
               geojson.coordinates.length >= 2;
      } catch (error) {
        console.warn('⚠️ 解析geojson失败:', item.geojson, error);
        return false;
      }
    });

    if (validResults.length === 0) {
      console.warn('⚠️ 没有有效的坐标数据可以高亮');
      return;
    }

    console.log('✅ 有效的高亮点位数量:', validResults.length);

    // 根据地图引擎类型进行高亮
    if (mapEngine.value === 'cesium') {
      highlightResultsOnCesium(validResults);
    } else {
      highlightResultsOnMapLibre(validResults);
    }

    // 保存高亮的要素数据
    highlightedFeatures.value = validResults;

  } catch (error) {
    console.error('❌ 高亮查询结果失败:', error);
  }
};

/**
 * @description 在MapLibre上高亮查询结果
 * @param results 查询结果数据
 */
const highlightResultsOnMapLibre = (results: any[]) => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      console.error('❌ MapLibre地图实例不存在');
      return;
    }

    // 构建GeoJSON FeatureCollection
    const features = results.map((item, index) => {
      const geojson = JSON.parse(item.geojson);
      return {
        type: 'Feature' as const,
        id: item.gid || item.gxddh || index,
        geometry: geojson,
        properties: {
          ...item,
          highlightIndex: index
        }
      };
    });

    const featureCollection = {
      type: 'FeatureCollection' as const,
      features
    };

    // 添加高亮数据源
    map.addSource(maplibreHighlightSourceId, {
      type: 'geojson',
      data: featureCollection
    });

    // 添加高亮图层（闪烁的红色圆点）
    map.addLayer({
      id: maplibreHighlightLayerId,
      type: 'circle',
      source: maplibreHighlightSourceId,
      paint: {
        'circle-radius': [
          'interpolate',
          ['linear'],
          ['zoom'],
          8, 5,    // 缩放级别8时半径6px
          15, 8   // 缩放级别15时半径12px
        ],
        'circle-color': '#ff4444',  // 红色
        'circle-opacity': [
          'case',
          ['boolean', ['feature-state', 'highlight'], false],
          0.9,  // 高亮时不透明
          0.7   // 默认半透明
        ],
        'circle-stroke-width': 2,
        'circle-stroke-color': '#ffffff',
        'circle-stroke-opacity': 0.8
      }
    });

    // 添加闪烁动画效果
    startMapLibreBlinkAnimation();

    console.log('✅ MapLibre高亮图层创建成功，点位数量:', features.length);

  } catch (error) {
    console.error('❌ MapLibre高亮失败:', error);
  }
};

/**
 * @description 在Cesium上高亮查询结果
 * @param results 查询结果数据
 */
const highlightResultsOnCesium = async (results: any[]) => {
  try {
    console.log('🎯 开始Cesium高亮查询结果，点位数量:', results.length);

    // 提取管点编码用于高亮
    const pipeCodes = results
      .map(item => item.gxddh || item.bm || item.gid)
      .filter(code => code); // 过滤掉空值

    if (pipeCodes.length === 0) {
      console.warn('⚠️ 没有有效的管点编码可以高亮');
      return;
    }

    console.log('📍 提取的管点编码:', pipeCodes);

    // 使用pipeLocationByBms方法进行高亮
    await pipeLocationByBms(pipeCodes, 'cesium', 'point', {
      autoFit: false, // 不自动缩放，保持当前视角
      highlightColor: '#ff4444', // 红色高亮
      pointRadius: 8
    });

    console.log('✅ Cesium高亮完成');

  } catch (error) {
    console.error('❌ Cesium高亮失败:', error);
  }
};

/**
 * @description 启动MapLibre闪烁动画
 */
const startMapLibreBlinkAnimation = () => {
  const map = AppMaplibre.getMap();
  if (!map || !map.getLayer(maplibreHighlightLayerId)) return;

  let opacity = 0.7;
  let increasing = true;

  const animate = () => {
    if (!map.getLayer(maplibreHighlightLayerId)) return;

    if (increasing) {
      opacity += 0.05;
      if (opacity >= 1.0) {
        opacity = 1.0;
        increasing = false;
      }
    } else {
      opacity -= 0.05;
      if (opacity <= 0.3) {
        opacity = 0.3;
        increasing = true;
      }
    }

    try {
      map.setPaintProperty(maplibreHighlightLayerId, 'circle-opacity', opacity);
      requestAnimationFrame(animate);
    } catch (error) {
      // 图层可能已被移除，停止动画
      console.log('🔄 高亮动画停止');
    }
  };

  animate();
};

/**
 * @description 清除所有高亮点位
 */
const clearHighlights = () => {
  try {
    console.log('🧹 清除高亮点位');

    if (mapEngine.value === 'cesium') {
      // 清除Cesium高亮
      clearPipeLocationHighlight('cesium');
      cesiumHighlightLayer = null;
    } else {
      // 清除MapLibre高亮图层
      const map = AppMaplibre.getMap();
      if (map) {
        if (map.getLayer(maplibreHighlightLayerId)) {
          map.removeLayer(maplibreHighlightLayerId);
        }
        if (map.getSource(maplibreHighlightSourceId)) {
          map.removeSource(maplibreHighlightSourceId);
        }
      }
    }

    // 清空高亮要素数组
    highlightedFeatures.value = [];

  } catch (error) {
    console.error('❌ 清除高亮失败:', error);
  }
};

/**
 * 清除缓冲区
 */
const clearBuffer = () => {
  try {
    if (mapEngine.value === 'cesium') {
      // 清除Cesium缓冲区GroundPrimitive
      if (cesiumBufferLayer) {
        const viewer = AppCesium.getInstance().getViewer();
        viewer.scene.groundPrimitives.remove(cesiumBufferLayer);
        cesiumBufferLayer = null;
      }
    } else {
      // 清除MapLibre缓冲区图层
      const map = AppMaplibre.getMap();
      if (map) {
        if (map.getLayer(maplibreBufferFillLayerId)) {
          map.removeLayer(maplibreBufferFillLayerId);
        }
        if (map.getLayer(maplibreBufferLineLayerId)) {
          map.removeLayer(maplibreBufferLineLayerId);
        }
        if (map.getSource(maplibreBufferSourceId)) {
          map.removeSource(maplibreBufferSourceId);
        }
      }

      // 清除绘制工具中的所有要素（包括蓝色圆点）
      if (drawTool) {
        try {
          drawTool.clearAllFeatures();
        } catch (error) {
          console.warn('清除绘制工具要素失败:', error);
        }
      }
    }

  } catch (error) {
    console.error('清除缓冲区失败:', error);
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
// 组件挂载时初始化
onMounted(async () => {
  if (mapEngine.value === 'maplibre') {
    try {
      await nextTick();
      drawTool = AppMaplibre.getDrawTool();

      if (drawTool && !drawTool.isEnabled()) {
        await drawTool.start(true);
      }
    } catch (error) {
      console.error('❌ ClassifiedQuery绘制工具初始化失败:', error);
    }
  }
});

// 组件卸载时清理
onUnmounted(() => {
  // 清除缓冲区
  clearBuffer();

  // 清除高亮点位
  clearHighlights();

  if (drawTool) {
    try {
      drawTool.removeEventListener(DrawEventTypeEnum.DRAW_FINISH, handleDrawFinish);
    } catch (error) {
      console.warn('清理ClassifiedQuery绘制工具事件监听器失败:', error);
    }
  }
});
</script>
<style lang="scss" scoped>
.classified-query {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 694px;
}

.reset-button {
  width: unset;
}

:deep(.radius-form-item) {
  .el-form-item__content {
    flex-wrap: unset;
  }
}
:deep(.query-buttons) {
  .el-form-item__content {
    flex-wrap: nowrap;
  }
}

.buttons-row {
  width: calc(100% - 70px);
  display: flex;
  justify-content: space-between;
}
</style>
