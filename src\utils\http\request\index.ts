/**
 * @fileoverview HTTP请求封装类
 * @description 基于Axios封装的HTTP请求类，提供拦截器支持和统一的错误处理
 * <AUTHOR>
 * @date 2024-07-03 14:38:34
 * @lastEditors xiao
 * @lastEditTime 2025-02-13 14:53:57
 * @version 1.0.0
 */

import axios from 'axios';

import type { AxiosInstance } from 'axios';
import type { HYRequestInterceptors, HYRequestConfig } from './type';
import router from '@/router';
import { ElMessage } from 'element-plus';
import localCache from '@/utils/auth'

/**
 * @class Http
 * @description HTTP请求封装类
 * @details 基于Axios实现的HTTP客户端，支持请求/响应拦截器、错误处理和多种HTTP方法
 */
class Http {
  /** @description Axios实例 */
  instance: AxiosInstance;
  
  /** @description 自定义拦截器配置 */
  interceptors?: HYRequestInterceptors;

  /**
   * @description 构造函数
   * @param {HYRequestConfig} config - HTTP请求配置对象
   * @details 创建Axios实例并配置拦截器
   */
  constructor(config: HYRequestConfig) {
    // 创建Axios实例
    this.instance = axios.create(config);
    this.interceptors = config.interceptors;

    // 1.使用实例拦截器
    this.instance.interceptors.request.use(
      this.interceptors?.requestInterceptor as any,
      this.interceptors?.requestInterceptorCatch
    );
    this.instance.interceptors.response.use(
      this.interceptors?.responseInterceptor,
      this.interceptors?.responseInterceptorCatch
    );

    // 2.添加所有的实例都有的拦截器
    this.instance.interceptors.request.use(
      (config) => {
        return config;
      },
      (err) => {
        return err;
      }
    );

    /**
     * @description 全局响应拦截器
     * @details 处理通用的响应逻辑，包括登录过期检查和错误处理
     */
    this.instance.interceptors.response.use(
      (res: any) => {
        const data = res.data;
        if (data instanceof Function) {
          return data();
        }
        // 文件流
        if (data instanceof Blob) {
          return res
        }
        // 检查登录状态
        if (data.code === 401) {
          ElMessage.error({
            message: '登录过期，请重新登录',
            grouping: true,
            repeatNum: 1
          });
          localCache.clearCache()
          return router.replace('/login');
        }
        return data;
      },
      async (err) => {
        return await Promise.reject(err);
      }
    );
  }

  /**
   * @description 通用请求方法
   * @template T - 响应数据类型
   * @param {HYRequestConfig<T>} config - 请求配置
   * @returns {Promise<T>} 返回Promise包装的响应数据
   * @details 支持单独的请求/响应拦截器配置
   */
  async request<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await new Promise((resolve, reject) => {
      // 如果有单独的请求拦截器，先执行
      if (config.interceptors?.requestInterceptor != null) {
        config = config.interceptors.requestInterceptor(config);
      }
      this.instance
        .request<any, T>(config)
        .then((res) => {
          // 如果有单独的响应拦截器，先执行
          if (config.interceptors?.responseInterceptor != null) {
            res = config.interceptors.responseInterceptor(res);
          }
          resolve(res);
        })
        .catch((err) => {
          reject(err);
          return err;
        });
    });
  }

  /**
   * @description GET请求方法
   * @template T - 响应数据类型
   * @param {HYRequestConfig<T>} config - 请求配置
   * @returns {Promise<T>} 返回Promise包装的响应数据
   */
  async get<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await this.request<T>({ ...config, method: 'GET' });
  }

  /**
   * @description POST请求方法
   * @template T - 响应数据类型
   * @param {HYRequestConfig<T>} config - 请求配置
   * @returns {Promise<T>} 返回Promise包装的响应数据
   */
  async post<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await this.request<T>({ ...config, method: 'POST' });
  }

  /**
   * @description PUT请求方法
   * @template T - 响应数据类型
   * @param {HYRequestConfig<T>} config - 请求配置
   * @returns {Promise<T>} 返回Promise包装的响应数据
   */
  async put<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await this.request<T>({ ...config, method: 'PUT' });
  }

  /**
   * @description DELETE请求方法
   * @template T - 响应数据类型
   * @param {HYRequestConfig<T>} config - 请求配置
   * @returns {Promise<T>} 返回Promise包装的响应数据
   */
  async delete<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await this.request<T>({ ...config, method: 'DELETE' });
  }

  /**
   * @description PATCH请求方法
   * @template T - 响应数据类型
   * @param {HYRequestConfig<T>} config - 请求配置
   * @returns {Promise<T>} 返回Promise包装的响应数据
   */
  async patch<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await this.request<T>({ ...config, method: 'PATCH' });
  }
}

export default Http;
