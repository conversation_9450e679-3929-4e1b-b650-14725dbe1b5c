/**
 * @fileoverview 基础图层管理器
 * @description 管理底图图层的添加、删除和切换
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import type { Map as MapLibreMap } from 'maplibre-gl'
import { 
  createLayerManager, 
  LayerType, 
  CoordinateSystem,
  type LayerConfig,
  type LayerManagerConfig
} from '@/lib/maplibre/layer'

/**
 * @interface BasemapConfig
 * @description 底图配置接口
 */
export interface BasemapConfig {
  id: string
  name: string
  type: LayerType
  thumbnail: string
  config: LayerConfig
}

/**
 * @interface BaseLayerManagerOptions
 * @description 基础图层管理器选项
 */
export interface BaseLayerManagerOptions {
  map: MapLibreMap
  tiandituToken?: string
  debug?: boolean
}

/**
 * @class BaseLayerManager
 * @description 基础图层管理器，负责底图的统一管理
 */
export class BaseLayerManager {
  private map: MapLibreMap
  private layerManager: any
  private currentBasemap: BasemapConfig | null = null
  private currentAnnotationLayer: string | null = null
  private tiandituToken: string
  private debug: boolean
  private initialized = false

  // 预定义的底图配置
  public readonly tiandituMaps: BasemapConfig[]
  public readonly baiduMaps: BasemapConfig[]
  public readonly amapMaps: BasemapConfig[]

  /**
   * @constructor
   * @param options - 初始化选项
   */
  constructor(options: BaseLayerManagerOptions) {
    this.map = options.map
    this.tiandituToken = options.tiandituToken || 'b254904598a72fd14661de55fa70511d'
    this.debug = options.debug || false

    // 初始化底图配置
    this.tiandituMaps = this.initTiandituMaps()
    this.baiduMaps = this.initBaiduMaps()
    this.amapMaps = this.initAmapMaps()
  }

  /**
   * @description 初始化天地图配置
   */
  private initTiandituMaps(): BasemapConfig[] {
    return [
      {
        id: 'tdt-vector',
        name: '矢量地图',
        type: LayerType.TDT,
        thumbnail: 'basemap/gaode-digital.png',
        config: {
          id: 'tdt-vector',
          title: '天地图矢量',
          type: LayerType.TDT,
          layer: 'vec_w',
          crs: '3857',
          token: this.tiandituToken,
          show: true
        }
      },
      {
        id: 'tdt-satellite',
        name: '卫星地图',
        type: LayerType.TDT,
        thumbnail: 'basemap/gaode-image.png',
        config: {
          id: 'tdt-satellite',
          title: '天地图卫星',
          type: LayerType.TDT,
          layer: 'img_w',
          crs: '3857',
          token: this.tiandituToken,
          show: true
        }
      },
      {
        id: 'tdt-terrain',
        name: '地形地图',
        type: LayerType.TDT,
        thumbnail: 'basemap/gaode-image-annotation.png',
        config: {
          id: 'tdt-terrain',
          title: '天地图地形',
          type: LayerType.TDT,
          layer: 'ter_w',
          crs: '3857',
          token: this.tiandituToken,
          show: true
        }
      }
    ]
  }

  /**
   * @description 初始化百度地图配置
   */
  private initBaiduMaps(): BasemapConfig[] {
    return [
      {
        id: 'baidu-normal',
        name: '标准地图',
        type: LayerType.BAIDU,
        thumbnail: 'basemap/gaode-digital.png',
        config: {
          id: 'baidu-normal',
          title: '百度地图',
          type: LayerType.BAIDU,
          style: 'normal',
          showLabel: true,
          crs: CoordinateSystem.BD09,
          show: true
        }
      },
      {
        id: 'baidu-satellite',
        name: '卫星地图',
        type: LayerType.BAIDU,
        thumbnail: 'basemap/gaode-image.png',
        config: {
          id: 'baidu-satellite',
          title: '百度卫星图',
          type: LayerType.BAIDU,
          style: 'satellite',
          showLabel: true,
          crs: CoordinateSystem.BD09,
          show: true
        }
      }
    ]
  }

  /**
   * @description 初始化高德地图配置
   */
  private initAmapMaps(): BasemapConfig[] {
    return [
      {
        id: 'amap-normal',
        name: '标准地图',
        type: LayerType.AMAP,
        thumbnail: 'basemap/gaode-digital.png',
        config: {
          id: 'amap-normal',
          title: '高德地图',
          type: LayerType.AMAP,
          style: 'normal',
          showLabel: true,
          showRoad: true,
          lang: 'zh_cn',
          scale: 1,
          crs: CoordinateSystem.GCJ02,
          show: true
        }
      },
      {
        id: 'amap-satellite',
        name: '卫星地图',
        type: LayerType.AMAP,
        thumbnail: 'basemap/gaode-image.png',
        config: {
          id: 'amap-satellite',
          title: '高德卫星图',
          type: LayerType.AMAP,
          style: 'satellite',
          showLabel: true,
          showRoad: false,
          lang: 'zh_cn',
          scale: 1,
          crs: CoordinateSystem.GCJ02,
          show: true
        }
      }
    ]
  }

  /**
   * @description 初始化图层管理器
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      const config: LayerManagerConfig = {
        map: this.map,
        enablePerformanceMonitoring: true,
        debug: this.debug,
        maxConcurrentLoads: 1 // 底图切换时限制并发数
      }

      this.layerManager = createLayerManager(config)

      // 监听图层事件
      this.setupEventListeners()

      this.initialized = true
      console.log('基础图层管理器初始化成功')
    } catch (error) {
      console.error('基础图层管理器初始化失败:', error)
      throw error
    }
  }

  /**
   * @description 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.layerManager) return

    this.layerManager.layerEvents.subscribe((event: any) => {
      if (this.debug) {
        console.log('基础图层事件:', event.type, event.layerId)
      }
      
      if (event.type === 'loaded' || event.type === 'sourceAdded') {
        const isCurrentBasemap = this.currentBasemap && event.layerId === this.currentBasemap.id
        const isCurrentAnnotation = this.currentAnnotationLayer && event.layerId === this.currentAnnotationLayer
        
        if (isCurrentBasemap || isCurrentAnnotation) {
          console.log('底图加载成功:', event.layerId)
          // 底图加载完成后，确保其他图层在正确位置
          this.ensureLayerOrder()
        }
      } else if (event.type === 'error' || event.type === 'layerAddError') {
        const isCurrentBasemap = this.currentBasemap && event.layerId === this.currentBasemap.id
        const isCurrentAnnotation = this.currentAnnotationLayer && event.layerId === this.currentAnnotationLayer
        
        if (isCurrentBasemap || isCurrentAnnotation) {
          console.error('底图加载失败:', event)
        }
      }
    })
  }

  /**
   * @description 切换底图
   * @param basemap - 底图配置
   */
  async switchBasemap(basemap: BasemapConfig): Promise<void> {
    if (!this.layerManager) {
      throw new Error('图层管理器未初始化')
    }

    if (this.currentBasemap?.id === basemap.id) {
      return // 已经是当前底图
    }

    try {
      console.log('=== 开始切换底图 ===')
      console.log(`从 ${this.currentBasemap?.name || '无'} 切换到 ${basemap.name}`)

      const isFirstLoad = !this.currentBasemap
      
      if (isFirstLoad) {
        // 第一次加载：使用 addLayers 初始化根图层组
        console.log('首次加载底图，使用 addLayers 初始化图层结构')
        this.currentBasemap = basemap
        
        await this.layerManager.addLayers([basemap.config])
        console.log('首次底图加载成功:', basemap.id)
        
        // 如果是天地图，添加对应的注记图层
        if (basemap.type === LayerType.TDT) {
          await this.addTiandituAnnotation(basemap)
        }
        
      } else {
        // 后续切换：移除当前底图，添加新底图
        console.log('切换底图，移除旧底图并添加新底图')
        
        // 移除当前底图
        if (this.currentBasemap) {
          this.safeRemoveLayer(this.currentBasemap.id, '当前底图')
        }

        // 移除当前注记图层
        if (this.currentAnnotationLayer) {
          this.safeRemoveLayer(this.currentAnnotationLayer, '当前注记图层')
          this.currentAnnotationLayer = null
        }

        // 更新当前底图状态
        this.currentBasemap = basemap
        
        // 使用 addLayer 精确添加底图
        await this.layerManager.addLayer('', basemap.config)
        console.log('底图添加成功:', basemap.id)
        
        // 如果是天地图，添加对应的注记图层
        if (basemap.type === LayerType.TDT) {
          await this.addTiandituAnnotation(basemap)
        }
      }
      
      console.log('底图切换成功:', basemap.name)
      
      // 底图切换后确保图层顺序正确
      await this.ensureLayerOrder()
      
    } catch (error) {
      console.error('切换底图失败:', error)
      throw error
    }
  }

  /**
   * @description 为天地图添加注记图层
   * @param basemap - 天地图底图配置
   */
  private async addTiandituAnnotation(basemap: BasemapConfig): Promise<void> {
    if (!this.layerManager) return

    // 根据底图类型确定注记图层
    let annotationLayer = ''
    switch (basemap.config.layer) {
      case 'vec_w': // 矢量地图
        annotationLayer = 'cva_w' // 矢量注记
        break
      case 'img_w': // 卫星地图
        annotationLayer = 'cia_w' // 卫星注记
        break
      case 'ter_w': // 地形地图
        annotationLayer = 'cta_w' // 地形注记
        break
      default:
        return
    }

    const annotationConfig: LayerConfig = {
      id: `${basemap.id}-annotation`,
      title: `${basemap.config.title}注记`,
      type: LayerType.TDT,
      layer: annotationLayer,
      crs: basemap.config.crs || '3857',
      token: this.tiandituToken,
      show: true
    }

    try {
      console.log('添加天地图注记图层:', annotationConfig.id)
      
      this.currentAnnotationLayer = annotationConfig.id || null
      
      await this.layerManager.addLayer('', annotationConfig)
      console.log('天地图注记图层添加成功:', annotationConfig.id)
      
    } catch (err) {
      console.error('添加天地图注记图层失败:', err)
      this.currentAnnotationLayer = null
    }
  }

  /**
   * @description 确保图层顺序正确
   * @description 底图在最底层，其他功能图层在上层
   */
  private async ensureLayerOrder(): Promise<void> {
    try {
      console.log('=== 开始调整图层顺序 ===')
      
      // 等待底图完全加载
      await new Promise(resolve => setTimeout(resolve, 200))
      
      // 获取需要移到顶层的图层（标绘图层、管网图层等）
      const layersToMoveTop = this.getTopLayerIds()
      
      if (layersToMoveTop.length === 0) {
        console.log('没有需要调整顺序的图层')
        return
      }
      
      console.log(`发现 ${layersToMoveTop.length} 个图层需要移动到顶层:`, layersToMoveTop)
      
      // 将这些图层移动到最顶层
      layersToMoveTop.forEach(layerId => {
        try {
          this.map.moveLayer(layerId) // 移动到最顶层
          console.log(`✅ 图层 ${layerId} 已移动到顶层`)
        } catch (error) {
          console.warn(`⚠️ 移动图层 ${layerId} 失败:`, error)
        }
      })
      
      console.log('=== 图层顺序调整完成 ===')
      
    } catch (error) {
      console.error('调整图层顺序失败:', error)
    }
  }

  /**
   * @description 获取需要移动到顶层的图层ID列表
   * @description 包括MVT注记图层、设备聚合图层、标绘图层等业务图层
   */
  private getTopLayerIds(): string[] {
    const layerIds: string[] = []

    // 获取地图上所有图层
    const allMapLayers = this.map.getStyle().layers || []

    // 1. 标绘图层
    const plotLayerIds = [
      'plot-features-layer-point',
      'plot-features-layer-line',
      'plot-features-layer-polygon-fill',
      'plot-features-layer-polygon-stroke'
    ]

    // 2. 管网图层
    const pipeLayerIds = [
      'pipe-nodes-layer',
      'pipe-lines-layer',
      'pipe-nodes-labels',
      'pipe-lines-labels'
    ]

    // 3. 基础设备图层
    const deviceLayerIds = [
      'device-layer'
    ]

    // 4. 管线、管点图层
    const pipeNodeLayerIds = [
      'mvt_pipeNode'
    ]

    const pipeLineLayerIds = [
      'mvt_pipeLine'
    ]

    // 5. 动态检测MVT注记图层（格式：{layerId}_label）
    const mvtLabelLayerIds: string[] = []
    allMapLayers.forEach(layer => {
      if (layer.id.endsWith('_label') && layer.type === 'symbol') {
        mvtLabelLayerIds.push(layer.id)
      }
    })

    // 6. 动态检测设备聚合相关图层
    const deviceClusterLayerIds: string[] = []
    allMapLayers.forEach(layer => {
      const layerId = layer.id
      // 设备聚合图层：device_{icon}_clusters
      if (layerId.includes('_clusters') && layerId.startsWith('device_')) {
        deviceClusterLayerIds.push(layerId)
      }
      // 设备聚合数量图层：device_{icon}_cluster_count
      if (layerId.includes('_cluster_count') && layerId.startsWith('device_')) {
        deviceClusterLayerIds.push(layerId)
      }
      // 设备展开图层：device_{icon}_expanded_{clusterId}
      if (layerId.includes('_expanded_') && layerId.startsWith('device_')) {
        deviceClusterLayerIds.push(layerId)
      }
      // 设备主图层：device_{icon}
      if (layerId.startsWith('device_') &&
          !layerId.includes('_clusters') &&
          !layerId.includes('_cluster_count') &&
          !layerId.includes('_expanded_') &&
          !layerId.includes('_source')) {
        deviceClusterLayerIds.push(layerId)
      }
    })

    // 7. 其他业务图层（可根据需要扩展）
    const businessLayerIds: string[] = []
    allMapLayers.forEach(layer => {
      const layerId = layer.id
      // 跳过底图相关图层
      if (this.isBasemapLayer(layerId)) {
        return
      }
      // 跳过已经包含的图层
      if ([...plotLayerIds, ...pipeLayerIds, ...deviceLayerIds,
           ...pipeNodeLayerIds, ...pipeLineLayerIds,
           ...mvtLabelLayerIds, ...deviceClusterLayerIds].includes(layerId)) {
        return
      }
      // 其他业务图层
      businessLayerIds.push(layerId)
    })

    // 合并所有需要置顶的图层（注意顺序：从底层到顶层）
    const allBusinessLayerIds = [
      ...plotLayerIds,           // 标绘图层（最底层的业务图层）
      ...pipeLayerIds,           // 管网图层
      ...deviceLayerIds,         // 基础设备图层
      ...pipeLineLayerIds,       // 管线图层
      ...pipeNodeLayerIds,       // 管点图层（在管线图层之上）
      ...deviceClusterLayerIds,  // 设备聚合图层
      ...mvtLabelLayerIds,       // MVT注记图层
      ...businessLayerIds        // 其他业务图层（最顶层）
    ]

    // 检查这些图层是否存在于地图中
    allBusinessLayerIds.forEach(layerId => {
      if (this.map.getLayer(layerId)) {
        layerIds.push(layerId)
      }
    })

    if (this.debug && layerIds.length > 0) {
      console.log('🔝 检测到需要置顶的业务图层（从底层到顶层）:', {
        '1. 标绘图层': plotLayerIds.filter(id => this.map.getLayer(id)),
        '2. 管网图层': pipeLayerIds.filter(id => this.map.getLayer(id)),
        '3. 设备图层': deviceLayerIds.filter(id => this.map.getLayer(id)),
        '4. 管线图层': pipeLineLayerIds.filter(id => this.map.getLayer(id)),
        '5. 管点图层': pipeNodeLayerIds.filter(id => this.map.getLayer(id)),
        '6. 设备聚合图层': deviceClusterLayerIds.filter(id => this.map.getLayer(id)),
        '7. MVT注记图层': mvtLabelLayerIds.filter(id => this.map.getLayer(id)),
        '8. 其他业务图层': businessLayerIds.filter(id => this.map.getLayer(id))
      })
    }

    return layerIds
  }

  /**
   * @description 判断是否为底图相关图层
   * @param layerId - 图层ID
   * @returns 是否为底图图层
   */
  private isBasemapLayer(layerId: string): boolean {
    // 当前底图图层
    if (this.currentBasemap && layerId === this.currentBasemap.id) {
      return true
    }

    // 当前注记图层
    if (this.currentAnnotationLayer && layerId === this.currentAnnotationLayer) {
      return true
    }

    // 天地图相关图层
    if (layerId.startsWith('tdt-') || layerId.includes('-annotation')) {
      return true
    }

    // 百度地图相关图层
    if (layerId.startsWith('baidu-')) {
      return true
    }

    // 高德地图相关图层
    if (layerId.startsWith('amap-')) {
      return true
    }

    // 其他底图相关图层模式
    const basemapPatterns = [
      /^(satellite|vector|terrain|hybrid)[-_]/,  // 卫星、矢量、地形、混合底图
      /^(osm|mapbox|google)[-_]/,                // OSM、Mapbox、Google底图
      /^basemap[-_]/,                            // 通用底图前缀
      /[-_](base|background)$/                   // 底图后缀
    ]

    return basemapPatterns.some(pattern => pattern.test(layerId))
  }

  /**
   * @description 安全移除图层
   * @param layerId - 图层ID
   * @param layerName - 图层名称（用于日志）
   */
  private safeRemoveLayer(layerId: string, layerName: string): boolean {
    if (!this.layerManager || !layerId) {
      return false
    }

    // 先检查图层是否存在
    if (!this.checkLayerExists(layerId)) {
      console.info(`${layerName} (${layerId}) 不存在，跳过移除`)
      return true
    }

    try {
      console.log(`正在移除${layerName}:`, layerId)
      const success = this.layerManager.removeLayer(layerId)
      
      if (success) {
        console.log(`${layerName}移除成功`)
      } else {
        console.warn(`${layerName}移除失败`)
      }
      
      return success
    } catch (error) {
      console.error(`移除${layerName}时发生异常:`, error)
      return false
    }
  }

  /**
   * @description 检查图层是否存在
   * @param layerId - 图层ID
   */
  private checkLayerExists(layerId: string): boolean {
    if (!this.layerManager || !layerId) return false
    
    try {
      const layer = this.layerManager.findLayerById(layerId)
      return layer !== null
    } catch (error) {
      console.error('检查图层存在性时发生错误:', error)
      return false
    }
  }

  /**
   * @description 设置默认底图
   */
  async setDefaultBasemap(): Promise<void> {
    if (this.tiandituMaps.length > 0) {
      console.log('设置默认底图为天地图矢量地图')
      await this.switchBasemap(this.tiandituMaps[0])
    }
  }

  /**
   * @description 获取当前底图
   */
  getCurrentBasemap(): BasemapConfig | null {
    return this.currentBasemap
  }

  /**
   * @description 获取所有可用底图
   */
  getAllBasemaps(): BasemapConfig[] {
    return [...this.tiandituMaps, ...this.baiduMaps, ...this.amapMaps]
  }

  /**
   * @description 清理所有底图图层
   */
  clearAllBasemaps(): void {
    if (!this.layerManager) return

    // 移除当前底图
    if (this.currentBasemap) {
      this.safeRemoveLayer(this.currentBasemap.id, '底图')
      this.currentBasemap = null
    }

    // 移除当前注记图层
    if (this.currentAnnotationLayer) {
      this.safeRemoveLayer(this.currentAnnotationLayer, '注记图层')
      this.currentAnnotationLayer = null
    }
  }

  /**
   * @description 销毁管理器
   */
  destroy(): void {
    this.clearAllBasemaps()
    this.layerManager = null
    this.initialized = false
    console.log('基础图层管理器已销毁')
  }
} 