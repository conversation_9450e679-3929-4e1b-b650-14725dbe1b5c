/**
 * @fileoverview 百度地图图层实现
 * @description 支持百度地图瓦片服务，包含坐标转换、多种地图样式和标注控制
 * <AUTHOR>
 * @version 2.0.0
 */

import { BaseLayer } from './BaseLayer'
import { Map as glMap } from 'maplibre-gl'
import { 
  LayerType, 
  CoordinateSystem,
  type BaiduLayerConfig, 
  type LayerLoadOptions 
} from '../types/LayerTypes'


/**
 * @class BaiduMapLayer
 * @description 百度地图图层类，支持百度地图的各种样式和坐标转换
 * @extends BaseLayer
 */
export class BaiduMapLayer extends BaseLayer {
  private _style: string = 'normal'

  /**
   * @constructor
   * @param options - 百度地图图层配置
   */
  constructor(options: BaiduLayerConfig) {
    super(options)
    this._style = options.style || 'normal'
    this._type = LayerType.BAIDU
  }

  /**
   * @description 图层内部添加实现
   * @param map - MapLibre地图实例
   * @param isRefresh - 是否为刷新操作
   * @param options - 加载选项
   * @returns 图层代理对象
   */
  async addToInternal(map: glMap, isRefresh?: boolean, options?: LayerLoadOptions): Promise<any> {
    try {
      if(this._style === 'normal') {
        map.addLayer((window as any).rasterTileLayer(this.id, 'Baidu.Normal.Map'))
      } else if(this._style === 'satellite') {
        map.addLayer((window as any).rasterTileLayer(this.id, 'Baidu.Satellite.Map'))
        map.addLayer((window as any).rasterTileLayer(this.id + '_anno', 'Baidu.Satellite.Annotion'))
      }


      const delegate = map.getLayer(this.id)
      
      
      
      return delegate
    } catch (error) {
      this._performanceMetrics.errorCount = (this._performanceMetrics.errorCount || 0) + 1
      console.error(`百度地图图层 ${this._name} 添加失败:`, error)
      
      // 发射错误事件
      this.emit('error', {
        layerId: this._id,
        type: 'error',
        data: error,
        timestamp: Date.now()
      })
      
      throw error
    }
  }

  

  /**
   * @description 移除图层
   */
  removeInternal(): void {
    if (this._map) {
      try {
        // 移除标注图层
        if (this._map.getLayer(this.id + '_anno')) {
          this._map.removeLayer(this.id + '_anno')
        }

        // 移除主图层
        if (this._map.getLayer(this.id)) {
          this._map.removeLayer(this.id)
        }

      } catch (error) {
        console.error(`移除百度地图图层 ${this._name} 失败:`, error)
      }
    }
  }

  
} 