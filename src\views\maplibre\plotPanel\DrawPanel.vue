<!--
 * @Description: 标绘绘制面板 - 统一绘制入口
 * @Date: 2022-04-22 08:54:28
 * @Author: GISerZ
 * @LastEditor: 项目开发团�?
 * @LastEditTime: 2024-01-20 10:30:00
-->
<template>
  <custom-card
    @closeHandler="closeHandler"
    :width="'450px'"
    :top="'90px'"
    :left="'1000px'"
    :title="drawBar.title"
  >
    <!-- 绘制状态提�?-->
    <div v-if="currentStatus !== 'idle'" class="status-indicator">
      <el-alert
        :title="getStatusMessage()"
        :type="getStatusType()"
        :closable="false"
        show-icon
        class="mb-3"
      />
    </div>

    <!-- 根据类型加载对应的组�?-->
    <component 
      :is="typeComponentMap[drawBar.type]"
      :drawData="currentDrawData"
      :plotService="plotService"
      @draw-start="handleDrawStart"
      @draw-cancel="handleDrawCancel"
      @feature-save="handleFeatureSave"
    />
  </custom-card>
</template>

<script lang="ts" setup>
import { onMounted, ref, onUnmounted, watch } from "vue";
import { DrawBar, DrawModule, DrawData, DrawStatus } from "./DrawModule";
import { PlotService } from "./PlotService";
import CustomCard from "@/components/dialog/CustomCard.vue";
import DrawPointPanel from "./DrawPointPanel.vue";
import DrawPolylinePanel from "./DrawPolylinePanel.vue";
import DrawPolygonPanel from "./DrawPolygonPanel.vue";
import DrawRectanglePanel from "./DrawRectanglePanel.vue";
import { useDialogStore } from "@/stores/Dialogs";
import { ElMessage } from "element-plus";
import { Subscription } from "rxjs";
import type { PlotFeature } from "@/lib/maplibre/layer/types/LayerTypes";


// 组件映射
const typeComponentMap: Record<string, any> = {
  DrawPointPanel: DrawPointPanel,
  DrawPolylinePanel: DrawPolylinePanel,
  DrawPolygonPanel: DrawPolygonPanel,
  DrawRectanglePanel: DrawRectanglePanel,
};

// 响应式状�?
const drawBar = ref(new DrawBar());
const currentDrawData = ref<DrawData | null>(null);
const currentStatus = ref<DrawStatus>(DrawStatus.IDLE);
const plotService = ref<PlotService | null>(null);

// 订阅管理
let showChangeSubscription: Subscription | null = null;
let dataChangeSubscription: Subscription | null = null;
let statusChangeSubscription: Subscription | null = null;

// 初始化标�?
const isInitialized = ref(false);

/**
 * @description 组件挂载时初始化
 */
onMounted(async () => {

  
  // 先重置绘制状态，确保面板打开时是干净的状�?
  const drawModule = DrawModule.getInstance();
  drawModule.resetDrawState();
  
  await initializeServices();
  setupSubscriptions();
});

/**
 * @description 组件卸载时清理资�?
 */
onUnmounted(() => {

  
  // 清理订阅
  cleanupSubscriptions();
  
  // 重置绘制状态，确保关闭面板时停止所有绘制操�?
  const drawModule = DrawModule.getInstance();
  drawModule.resetDrawState();
  
  // 如果绘制面板关闭，重置DrawModule状�?
  if (drawBar.value.title === "标绘新增") {
    DrawModule.showChange.next(new DrawBar());
  }
});

/**
 * @description 初始化服�?
 */
async function initializeServices(): Promise<void> {
  try {


    // 获取已经初始化的PlotService实例
    plotService.value = PlotService.getInstance({
      autoSave: false,  // 手动保存，由各Panel组件控制
      showMessages: true,
      validateGeometry: true
    });

    // 检查DrawModule是否已经初始化（应该在地图load时已经初始化�?
    const drawModule = DrawModule.getInstance();
    if (!drawModule.getStatus || drawModule.getStatus() === DrawStatus.ERROR) {
      console.warn('DrawModule可能未正确初始化，尝试重新初始化...');
      const initialized = await plotService.value.initialize();
      if (!initialized) {
        throw new Error('PlotService初始化失败');
      }
    } else {

    }

    // 设置事件回调
    plotService.value.setCallbacks({
      onDrawStart: () => {

      },
      onDrawFinish: (feature: PlotFeature) => {

      },
      onDrawCancel: () => {

      },
      onSaveSuccess: (feature: PlotFeature) => {

        // ElMessage.success(`要素 "${feature.name}" 保存成功`);
      },
      onSaveError: (error: Error) => {
        console.error('保存失败回调:', error);
        ElMessage.error(`保存失败: ${error.message}`);
      },
      onStatusChange: (status: DrawStatus) => {
        currentStatus.value = status;
      }
    });

    isInitialized.value = true;

    
  } catch (error) {
    console.error('DrawPanel 服务初始化失�?', error);
    ElMessage.error('绘制面板初始化失败，请刷新页面重试');
  }
}

/**
 * @description 设置订阅
 */
function setupSubscriptions(): void {
  // 监听面板显示状态变�?
  showChangeSubscription = DrawModule.showChange.subscribe((val: DrawBar) => {
    drawBar.value = val;

  });

  // 监听绘制数据变化
  dataChangeSubscription = DrawModule.dataChange.subscribe((data: DrawData) => {
    currentDrawData.value = data;

  });

  // 监听绘制状态变�?
  statusChangeSubscription = DrawModule.statusChange.subscribe((status: DrawStatus) => {
    currentStatus.value = status;

  });


}

/**
 * @description 清理订阅
 */
function cleanupSubscriptions(): void {
  showChangeSubscription?.unsubscribe();
  dataChangeSubscription?.unsubscribe();
  statusChangeSubscription?.unsubscribe();
  
  showChangeSubscription = null;
  dataChangeSubscription = null;
  statusChangeSubscription = null;
  

}

/**
 * @description 处理绘制开始事�?
 */
function handleDrawStart(): void {

}

/**
 * @description 处理绘制取消事件
 */
function handleDrawCancel(): void {

  // 可以添加额外的取消逻辑
}

/**
 * @description 处理要素保存事件
 * @param feature - 要保存的要素
 */
async function handleFeatureSave(feature: PlotFeature): Promise<void> {

  
  if (plotService.value) {
    try {
      const success = await plotService.value.saveFeature(feature);
      if (success) {
        // 保存成功后可以关闭面�?
        setTimeout(() => {
          closeHandler();
        }, 1000);
      }
    } catch (error) {
      console.error('保存要素失败:', error);
    }
  }
}

/**
 * @description 获取状态消�?
 */
function getStatusMessage(): string {
  switch (currentStatus.value) {
    case DrawStatus.DRAWING:
      return '正在绘制，请在地图上操作';
    case DrawStatus.EDITING:
      return '正在编辑，请修改要素';
    case DrawStatus.SAVING:
      return '正在保存要素...';
    case DrawStatus.ERROR:
      return '操作出现错误，请重试';
    default:
      return '准备就绪';
  }
}

/**
 * @description 获取状态类�?
 */
function getStatusType(): 'success' | 'warning' | 'info' | 'error' {
  switch (currentStatus.value) {
    case DrawStatus.DRAWING:
      return 'info';
    case DrawStatus.EDITING:
      return 'warning';
    case DrawStatus.SAVING:
      return 'info';
    case DrawStatus.ERROR:
      return 'error';
    default:
      return 'success';
  }
}

/**
 * @description 关闭面板
 */
function closeHandler(): void {

  
  // === 修复：关闭时清除临时绘制图层 ===
  if (plotService.value) {
    // 清除当前绘制的临时图�?
    plotService.value.clearCurrentDrawing();

    
    // 停止当前绘制
    plotService.value.stopDraw();

  }
  
  // 同时从DrawModule层面确保完全清理
  const drawModule = DrawModule.getInstance();
  drawModule.resetDrawState();

  
  // 关闭对话�?
  useDialogStore().closeDialog("DrawPanel");
}

// 监听服务初始化状�?
watch(isInitialized, (initialized) => {
  if (initialized) {

  }
});
</script>

<style lang="scss" scoped>
.status-indicator {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 12px;
}

.custom-admin-layout {
  width: 22% !important;
}

.draw-container {
  z-index: 1001;
}

:deep(.el-alert) {
  padding: 8px 12px;
  font-size: 13px;
}

:deep(.el-alert__title) {
  font-size: 13px;
  font-weight: 500;
}
</style>
