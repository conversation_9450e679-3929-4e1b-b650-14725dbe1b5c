<!--
 * @Description: 坐标拾取组件
 * @Date: 2022-04-22 08:54:28
 * @Author: GISerZ
 * @LastEditor: 项目开发团队
 * @LastEditTime: 2023-08-10 15:00:00
-->
<template>
  <custom-card
    @closeHandler="close"
    v-show="mainItem.show"
    :main-item="mainItem"
    :title="mainItem.title ?? (mainItem.title = '空间量算')"
  >
    <div class="operate-box">
      <el-button
        type="primary"
        class="primary-btn"
        @click="measure('distance')"
        >{{ mainItem.type === 'cesium' ? '水平距离' : '距离' }}</el-button
      >
      <el-button
        type="primary"
        class="primary-btn"
        @click="measure('area_surface')"
        >面积</el-button
      >
      <el-button
        v-if="mainItem.type === 'cesium'"
        type="primary"
        class="primary-btn"
        @click="measure('diatance_surface')"
        >贴地距离</el-button
      >
      <el-button class="clear-btn" @click="deactivate"
        >清除</el-button
      >
    </div>
  </custom-card>
</template>

<script lang="ts" setup>
import { Delete } from "@element-plus/icons-vue";
import { onUnmounted, ref } from "vue";
import { AppCesium } from "@/lib/cesium/AppCesium";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
/**
 * 组件属性定义
 */
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});

/**
 * @description 组件卸载时的清理工作
 */
onUnmounted(() => {
  console.log("测量组件即将卸载，进行清理工作");

  // 根据地图类型进行相应的清理
  if (props.mainItem.type === "cesium") {
    // Cesium测量工具清理
    try {
      let measureUtil = AppCesium.getInstance().getMeasureUtil();
      if (measureUtil) {
        measureUtil.deactivate();
        console.log("Cesium测量工具已停用");
      }
    } catch (error) {
      console.warn("Cesium测量工具清理失败:", error);
    }
  } else {
    // MapLibre测量工具清理
    try {
      const measureTool = AppMaplibre.getMeasureTool();

      // 检查是否有活动的测量
      const status = measureTool.getCurrentMeasureStatus();
      if (status.isActive) {
        console.log(`停止活动的${status.type}测量`);
        measureTool.stopCurrentMeasurement();
      }

      // 清除所有测量要素
      measureTool.clearMeasureAll();
      console.log("MapLibre测量工具已清理");
    } catch (error) {
      console.warn("MapLibre测量工具清理失败:", error);
    }
  }

  // 最后调用关闭方法
  close();
});
const measure = (type: string) => {
  if (props.mainItem.type === "cesium") {
    measureCesium(type);
  } else {
    measureMaplibre(type);
  }
};

/**
 * @description MapLibre地图测量功能
 * @param type - 测量类型 ('distance' | 'area_surface')
 */
const measureMaplibre = async (type: string) => {
  try {
    // 获取测量工具实例
    const measureTool = AppMaplibre.getMeasureTool();

    // 生成唯一的图层ID
    const layerId = `measure-${type}-${Date.now()}`;

    // 根据测量类型调用相应的测量方法
    if (type === "distance") {
      console.log("开始距离测量");
      await measureTool.measureDistance(layerId);
    } else if (type === "area_surface") {
      console.log("开始面积测量");
      await measureTool.measureArea(layerId);
    } else {
      console.warn("不支持的测量类型:", type);
    }
  } catch (error) {
    console.error("启动测量工具失败:", error);
  }
};

const measureCesium = (type: string) => {
  console.log(type);
  let lerpNum = 5;
  //贴地距离增加精度
  if (type === "diatance_surface") {
    lerpNum = 200;
  }
  let measureUtil = AppCesium.getInstance().getMeasureUtil();
  measureUtil.activate(type, {
    clampToModel: true,
    lerpNum: lerpNum,
    onDrawFinish: () => {
      // store.commit("setIsEditing", false);
    },
  });
  // store.commit("setIsEditing", true);
};
/**
 * @description 停用测量功能
 */
const deactivate = () => {
  if (props.mainItem.type === "cesium") {
    // Cesium测量工具清理
    let measureUtil = AppCesium.getInstance().getMeasureUtil();
    measureUtil && measureUtil.deactivate();
  } else {
    // MapLibre测量工具清理
    try {
      const measureTool = AppMaplibre.getMeasureTool();
      measureTool.clearMeasureAll();
      console.log("已清除所有MapLibre测量要素");
    } catch (error) {
      console.error("清除MapLibre测量要素失败:", error);
    }
  }
};
/**
 * @description 关闭测量组件
 */
const close = () => {
  console.log("关闭测量组件");

  // 执行测量工具清理
  try {
    deactivate();
  } catch (error) {
    console.warn("测量工具停用失败:", error);
  }

  // 关闭对话框
  try {
    useDialogStore().closeDialog("BaseMeasure");
  } catch (error) {
    console.warn("关闭对话框失败:", error);
  }
};
</script>

<style lang="scss" scoped>
.operate-box {
  // height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.operate-btn {
  color: white;
}

.distance {
  width: 16px;
  height: 16px;
  // background: url("../../../../public/img/panorama/distance.png");
  background-size: contain;
  margin-right: 2px;
}
.area {
  width: 16px;
  height: 16px;
  // background: url("../../../../public/img/panorama/area.png");
  background-size: contain;
  margin-right: 2px;
}
.altitude {
  width: 16px;
  height: 16px;
  // background: url("../../../../public/img/panorama/altitude.png");
  background-size: contain;
  margin-right: 2px;
}
</style>
