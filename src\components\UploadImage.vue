<!--
 * @Author: xiao
 * @Date: 2023-11-28 20:53:39
 * @LastEditors: xiao
 * @LastEditTime: 2024-09-18 09:36:31
 * @Description:
-->
<template>
  <el-scrollbar max-height="250px">
    <el-upload
      ref="upload"
      v-model:file-list="fileList"
      :action="reqUrl"
      list-type="picture-card"
      :headers="headers"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload"
      :data="data"
      :accept="accept"
      :disabled="disabled"
    >
      <el-icon><icon-ep-plus></icon-ep-plus></el-icon>
      <template #tip>
        <div class="el-upload__tip">支持.jpg,.jpeg,.png</div>
      </template>
    </el-upload>

    <el-dialog v-model="dialogVisible" width="40%" :append-to-body="true">
      <img :src="dialogImageUrl" class="preview-img" alt="Preview Image" />
    </el-dialog>
  </el-scrollbar>
  <!-- <div class="upload-img-box">
  </div> -->
</template>

<script setup lang="ts">
import localCache from "@/utils/auth";
import type {
  UploadInstance,
  UploadProps,
  UploadRawFile,
  UploadUserFile,
} from "element-plus";

const props = withDefaults(
  defineProps<{
    list: UploadUserFile[];
    disabled?: boolean;
  }>(),
  {
    disabled: false,
  }
);

const emits = defineEmits(["validFile", "update:list"]);

const fileList = ref<UploadUserFile[]>([]);

// const list = ref<IFileList[]>([]);
const list = computed({
  get() {
    return props.list;
  },
  set(val) {
    emits("update:list", val);
  },
});

const upload = ref<UploadInstance>();
const headers = {
  Latias: localCache.getCache("Latias") ?? "",
};

const dialogImageUrl = ref("");
const dialogVisible = ref(false);

const reqUrl = ref(import.meta.env.VITE_XHR_URL + "/v1/resource");

const data = ref();

const accept = [".jpg", ".png", ".jpeg"].join(",");
const typeList = ["image/png", "image/jpeg", "image/webp"];
const beforeUpload = (rawFile: UploadRawFile) => {
  if (!typeList.includes(rawFile.type)) {
    ElMessage.error("请上传png,jpg,jpeg,webp格式");
    return false;
  }
  const formData = new FormData();
  data.value = formData.append("file", rawFile, rawFile.name);
};

const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
  const idx = list.value.findIndex((item) => item.name === uploadFile.name);
  list.value.splice(idx, 1);
};

const handlePictureCardPreview: UploadProps["onPreview"] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!;
  dialogVisible.value = true;
};

const handleError = (error: Error) => {
  console.log(error);
};

const router = useRouter();
const handleSuccess = (response: any) => {
  switch (response.code) {
    case 200:
    
      list.value.push(response.data);
        console.log(list.value);
      emits("validFile");
      break;
    case 401:
      ElMessage.error(response.msg);
      localCache.clearCache();
      router.replace("/login");
      break;
    default:
      ElMessage.error(response.msg);
      break;
  }
};

const setFileList = (item: UploadUserFile) => {
  fileList.value.push(item);
};

defineExpose({
  // list,
  setFileList,
});
</script>

<style scoped lang="scss">
.preview-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.upload-img-box {
  max-height: 250px;
}
</style>
