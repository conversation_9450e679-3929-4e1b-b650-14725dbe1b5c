/** * @description: maplibre首页 - 图层分类管理 */
<template>
  <div class="layer-control-panel">
    <!-- 管网图层分类 -->
    <div class="layer-category">
      <div
        class="category-header"
        :class="{ expanded: pipeLayersExpanded }"
        @click="togglePipeLayersExpanded"
      >
        <el-checkbox
          v-model="allPipeLayersVisible"
          @change="handleAllPipeLayersToggle"
          @click.stop
        />
        <span class="category-title">管网全览</span>
        <el-icon class="expand-icon">
          <ArrowUp v-if="pipeLayersExpanded" />
          <ArrowDown v-else />
        </el-icon>
      </div>

      <div class="layer-list" v-show="pipeLayersExpanded">
        <div
          v-for="layer in pipeLayersList"
          :key="layer.id"
          class="layer-item"
        >
          <el-checkbox
            v-model="layer.show"
            @change="handleLayerToggle(layer)"
          />
          <span class="layer-name">{{ layer.name }}</span>
        </div>
      </div>
    </div>

    <!-- 设备图层分类 -->
    <div class="layer-category">
      <div
        class="category-header"
        :class="{ expanded: deviceLayersExpanded }"
        @click="toggleDeviceLayersExpanded"
      >
        <el-checkbox
          v-model="allDeviceLayersVisible"
          @change="handleAllDeviceLayersToggle"
          @click.stop
        />
        <span class="category-title">全部设备</span>
        <el-icon class="expand-icon">
          <ArrowUp v-if="deviceLayersExpanded" />
          <ArrowDown v-else />
        </el-icon>
      </div>

      <div class="layer-list" v-show="deviceLayersExpanded">
        <div
          v-for="layer in deviceLayersList"
          :key="layer.id"
          class="layer-item"
        >
          <el-checkbox
            v-model="layer.show"
            @change="handleLayerToggle(layer)"
          />
          <span class="layer-name">{{ layer.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import type { Subscription } from 'rxjs';
import type { LayerItem } from '@/lib/maplibre/layer/type/LayerItem';
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';

// ============ 响应式状态 ============
const layerdata = ref<LayerItem[]>([]);
const pipeLayersExpanded = ref(true); // 默认展开
const deviceLayersExpanded = ref(true); // 默认展开

let subscription: Subscription | null = null;

// ============ 计算属性 ============
/**
 * 管网图层列表
 */
const pipeLayersList = computed(() => {
  return layerdata.value.filter(layer =>
    (layer.id && (layer.id.includes('mvt_pipeLine')))
  );
});

/**
 * 设备图层列表
 */
const deviceLayersList = computed(() => {
  return layerdata.value.filter(layer =>
    (layer.id && layer.id.includes('device_'))
  );
});

/**
 * 所有管网图层是否可见
 */
const allPipeLayersVisible = computed({
  get: () => pipeLayersList.value.length > 0 && pipeLayersList.value.every(layer => layer.show),
  set: (value: boolean) => {
    pipeLayersList.value.forEach(layer => {
      layer.show = value;
      handleLayerToggle(layer);
    });
  }
});

/**
 * 所有设备图层是否可见
 */
const allDeviceLayersVisible = computed({
  get: () => deviceLayersList.value.length > 0 && deviceLayersList.value.every(layer => layer.show),
  set: (value: boolean) => {
    deviceLayersList.value.forEach(layer => {
      layer.show = value;
      handleLayerToggle(layer);
    });
  }
});

// ============ 方法函数 ============
/**
 * 切换管网图层展开状态
 */
const togglePipeLayersExpanded = () => {
  pipeLayersExpanded.value = !pipeLayersExpanded.value;
};

/**
 * 切换设备图层展开状态
 */
const toggleDeviceLayersExpanded = () => {
  deviceLayersExpanded.value = !deviceLayersExpanded.value;
};

/**
 * 处理单个图层显示隐藏
 */
const handleLayerToggle = (layer: LayerItem) => {
  const layerManager = AppMaplibre.getLayerManager();
  if (layerManager) {
    // 如果图层有子图层，需要同时控制所有子图层
    if (layer.children && layer.children.length > 0) {
      const newVisibility = layer.show;

      // 控制父级图层
      layerManager.setLayerVisible(layer.id, newVisibility);

      // 递归控制所有子图层
      const toggleChildrenLayers = (children: LayerItem[]) => {
        for (const child of children) {
          layerManager.setLayerVisible(child.id, newVisibility);
          if (child.children && child.children.length > 0) {
            toggleChildrenLayers(child.children);
          }
        }
      };

      toggleChildrenLayers(layer.children);
    } else {
      // 普通图层，直接切换
      layerManager.setLayerVisible(layer.id, layer.show);
    }
  }
};

/**
 * 处理所有管网图层切换
 */
const handleAllPipeLayersToggle = (value: any) => {
  console.log(`${value ? '显示' : '隐藏'}所有管网图层`);
};

/**
 * 处理所有设备图层切换
 */
const handleAllDeviceLayersToggle = (value: any) => {
  console.log(`${value ? '显示' : '隐藏'}所有设备图层`);
};

/**
 * 递归收集所有带有 showLayerControl 标记的图层
 */
const collectLayersWithControl = (layers: LayerItem[]): LayerItem[] => {
  const result: LayerItem[] = [];

  const traverse = (layerList: LayerItem[]) => {
    for (const layer of layerList) {
      // 检查图层的 showLayerControl 标记
      const hasLayerControl = layer.showLayerControl ||
                             (layer.options && layer.options.showLayerControl);

      if (hasLayerControl) {
        result.push({ ...layer });
      } else {
        // 如果当前层级没有 showLayerControl，继续遍历子图层
        if (layer.children && layer.children.length > 0) {
          traverse(layer.children);
        }
      }
    }
  };

  traverse(layers);
  return result;
};

// ============ 生命周期 ============
onMounted(async () => {
  await nextTick();
  const layerManager = AppMaplibre.getLayerManager();
  if (layerManager) {
    subscription = layerManager.layerChanged.subscribe((layers: any) => {
      if (layers.length > 0) {
        // 收集所有带有 showLayerControl 标记的图层
        const controlLayers = collectLayersWithControl(layers);
        layerdata.value = controlLayers;
        console.log('图层控制面板加载的图层:', controlLayers.map(l => ({ id: l.id, name: l.name, type: l.type })));
      } else {
        layerdata.value = [];
      }
    });
  }
});

onUnmounted(() => {
  if (subscription) {
    subscription.unsubscribe();
  }
});
</script>

<style scoped lang="scss">
.layer-control-panel {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 260px;
  // background: rgba(255, 255, 255, 0.95);
  // border-radius: 8px;
  // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.layer-category {
  margin-bottom: 10px;
  &:last-child {
    border-bottom: none;
  }
}

.category-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  // box-shadow: 2px 0px 12px 0px rgba(196, 216, 255, 0.4);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 1);
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.category-title {
  flex: 1;
  margin-left: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  transition: color 0.3s ease;
}

.expand-icon {
  color: #6c757d;
  transition: all 0.3s ease;

  &:hover {
    color: #495057;
  }
}

.layer-list {
  background: white;
  max-height: 500px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.layer-item {
  display: flex;
  align-items: center;
  padding: 4px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
}

.layer-name {
  margin-left: 8px;
  font-size: 13px;
  color: #495057;
  flex: 1;
  line-height: 1.4;
}

// 复选框样式优化
:deep(.el-checkbox) {
  .el-checkbox__input {
    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      border-radius: 3px;
      border: 2px solid #ddd;

      &:hover {
        border-color: #4080ff;
      }
    }

    &.is-checked .el-checkbox__inner {
      background-color: #4080ff;
      border-color: #4080ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .layer-control-panel {
    width: 260px;
    left: 8px;
    top: 8px;
  }

  .category-header {
    padding: 10px 14px;
  }

  .layer-item {
    padding: 8px 14px;
  }

  .category-title {
    font-size: 13px;
  }

  .layer-name {
    font-size: 12px;
  }
}
</style>
