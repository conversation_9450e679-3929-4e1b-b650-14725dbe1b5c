<!--
 * @Description: 管网材质专题
 * @Date: 2024-01-16
 * @Author: AI Assistant
 * @LastEditTime: 2024-01-16
 -->
<template>
  <page-card
    class="pipe-material-theme"
    title="管网材质专题"
    @closeCard="closeCard"
  >
    <!-- 加载状态显示 -->
    <el-row v-if="loading" class="loading-section">
      <el-col :span="24">
        <div class="loading-content">
          <el-icon class="is-loading"><Loading /></el-icon>
          <el-text class="loading-text">正在加载材质专题图...</el-text>
        </div>
      </el-col>
    </el-row>
    
    <!-- 专题图说明 -->
    <el-row v-else class="description-section">
      <el-col :span="24">
        <el-text class="description-text">
          根据管线材质对管线进行颜色分类显示，不同颜色代表不同的材质类型。图例显示在地图右下角。
        </el-text>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, onBeforeUnmount } from 'vue';
import { ElMessage, ElIcon } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import { onBeforeRouteLeave } from 'vue-router';
import PageCard from '@/components/PageCard.vue';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import { queryPipeMaterialList } from '@/api/pipeLine';
import {
  useLegend,
  type LegendItem,
  type LegendObj,
} from '@/components/legend';
import type { ApiResult } from '@/api/pipeLine';
import type { Map as MapLibreMap } from 'maplibre-gl';

/**
 * @interface Emits
 * @description 组件事件接口
 */
interface Emits {
  (e: 'close'): void;
}

const emit = defineEmits<Emits>();

// ============ 响应式状态 ============

/** 加载状态 */
const loading = ref(false);

/** 图例store */
const legendStore = useLegend();

/** 管线图层ID常量 */
const PIPELINE_LAYER_ID = 'mvt_pipeLine';

/** 管点图层ID常量 */
const PIPE_NODE_LAYER_ID = 'mvt_pipeNode';

/** 原始管线颜色备份 */
const originalLineColor = ref<string>('#2b78fe');

/** 原始管点图层最小缩放级别备份 */
const originalNodeMinZoom = ref<number | null>(null);

/** 悬浮提示框元素 */
const hoverTooltip = ref<any>(null);

/** 鼠标移动事件处理器 */
let mouseMoveHandler: ((e: any) => void) | null = null;

// ============ 事件处理方法 ============

/**
 * @function closeCard
 * @description 关闭专题面板
 */
const closeCard = (): void => {
  try {
    console.log('关闭管网材质专题面板');
    
    // 在关闭前先还原样式
    restorePipelineStyle();
    
    emit('close');
    ElMessage.info('已关闭管网材质专题');
  } catch (error) {
    console.error('关闭面板失败:', error);
    ElMessage.error('关闭面板失败');
  }
};

/**
 * @function createHoverTooltip
 * @description 创建悬浮提示框
 */
const createHoverTooltip = (): void => {
  // 创建提示框元素
  const tooltip = document.createElement('div');
  tooltip.className = 'pipe-material-tooltip';
  tooltip.style.cssText = `
    position: absolute;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(52, 73, 94, 0.9));
    color: white;
    padding: 10px 14px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    pointer-events: none;
    z-index: 10000;
    display: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateY(0);
    transition: opacity 0.2s ease, transform 0.2s ease;
    opacity: 0;
    white-space: nowrap;
  `;

  document.body.appendChild(tooltip);
  hoverTooltip.value = tooltip;
};

/**
 * @function showTooltip
 * @description 显示提示框
 * @param x 鼠标X坐标
 * @param y 鼠标Y坐标
 * @param material 材质值
 */
const showTooltip = (x: number, y: number, material: string): void => {
  if (!hoverTooltip.value) return;

  // 设置内容和位置
  hoverTooltip.value.innerHTML = `<i style="color: #e67e22;">🔧</i> 材质: <strong>${material}</strong>`;
  hoverTooltip.value.style.left = `${x + 15}px`;
  hoverTooltip.value.style.top = `${y - 40}px`;
  hoverTooltip.value.style.display = 'block';

  // 添加显示动画
  setTimeout(() => {
    if (hoverTooltip.value) {
      hoverTooltip.value.style.opacity = '1';
      hoverTooltip.value.style.transform = 'translateY(-5px)';
    }
  }, 10);
};

/**
 * @function hideTooltip
 * @description 隐藏提示框
 */
const hideTooltip = (): void => {
  if (!hoverTooltip.value) return;

  // 添加隐藏动画
  hoverTooltip.value.style.opacity = '0';
  hoverTooltip.value.style.transform = 'translateY(0)';

  setTimeout(() => {
    if (hoverTooltip.value) {
      hoverTooltip.value.style.display = 'none';
    }
  }, 200);
};

/**
 * @function setupMouseHover
 * @description 设置鼠标悬停事件
 */
const setupMouseHover = (): void => {
  try {
    const map = AppMaplibre.getMap();

    // 创建提示框
    createHoverTooltip();

    // 鼠标移动事件处理器
    mouseMoveHandler = (e: any) => {
      try {
        // 查询鼠标位置的管线要素
        const degree = 10;
        const features = map.queryRenderedFeatures(
          [
            [e.point.x - degree / 2, e.point.y - degree / 2],
            [e.point.x + degree / 2, e.point.y + degree / 2],
          ],
          {
            layers: [PIPELINE_LAYER_ID],
          }
        );

        if (features.length > 0) {
          const feature = features[0];
          const material =
            feature.properties?.cz || feature.properties?.CZ || '未知';

          // 显示材质信息
          showTooltip(
            e.originalEvent.clientX,
            e.originalEvent.clientY,
            material
          );

          // 设置鼠标样式
          map.getCanvas().style.cursor = 'pointer';
        } else {
          // 隐藏提示框
          hideTooltip();

          // 恢复鼠标样式
          map.getCanvas().style.cursor = '';
        }
      } catch (error) {
        console.error('处理鼠标移动事件失败:', error);
      }
    };

    // 绑定事件
    map.on('mousemove', mouseMoveHandler);

    // 鼠标离开地图时隐藏提示框
    map.on('mouseleave', hideTooltip);

    console.log('鼠标悬停事件已设置');
  } catch (error) {
    console.error('设置鼠标悬停事件失败:', error);
  }
};

/**
 * @function cleanupMouseHover
 * @description 清理鼠标悬停事件
 */
const cleanupMouseHover = (): void => {
  try {
    const map = AppMaplibre.getMap();

    // 移除事件监听器
    if (mouseMoveHandler) {
      map.off('mousemove', mouseMoveHandler);
      map.off('mouseleave', hideTooltip);
      mouseMoveHandler = null;
    }

    // 移除提示框元素
    if (hoverTooltip.value) {
      document.body.removeChild(hoverTooltip.value);
      hoverTooltip.value = null;
    }

    // 恢复鼠标样式
    map.getCanvas().style.cursor = '';

    console.log('鼠标悬停事件已清理');
  } catch (error) {
    console.error('清理鼠标悬停事件失败:', error);
  }
};

/**
 * @function generateColorMapping
 * @description 根据材质数据生成颜色映射
 * @param materials 材质数组
 * @returns 颜色映射数组
 */
const generateColorMapping = (materials: string[]): any[] => {
  // 去重并排序
  const uniqueMaterials = [...new Set(materials)].sort();

  // 生成材质专用色谱
  const colors = [
    '#e74c3c', // 红色 - PVC
    '#3498db', // 蓝色 - PE
    '#95a5a6', // 灰色 - 铸铁
    '#f39c12', // 橙色 - 玻璃钢
    '#9b59b6', // 紫色 - 钢管
    '#2ecc71', // 绿色 - 其他材质
    '#e67e22', // 深橙
    '#34495e', // 深灰
    '#f1c40f', // 黄色
    '#1abc9c', // 青绿
    '#e91e63', // 粉红
    '#8e44ad', // 深紫
    '#27ae60', // 深绿
    '#d35400', // 深橙红
    '#2c3e50', // 深蓝灰
  ];

  const mapping: any[] = ['case'];
  uniqueMaterials.forEach((material, index) => {
    mapping.push(['==', ['get', 'cz'], material]);
    mapping.push(colors[index % colors.length]);
  });

  // 默认颜色（原始颜色）
  mapping.push('#2b78fe');

  return mapping;
};

/**
 * @function generateLegendData
 * @description 生成图例数据
 * @param materials 材质数组
 * @param colorMapping 颜色映射
 * @returns 图例数据
 */
const generateLegendData = (
  materials: string[],
  colorMapping: any[]
): LegendItem[] => {
  const uniqueMaterials = [...new Set(materials)].sort();
  const colors = [
    '#e74c3c',
    '#3498db',
    '#95a5a6',
    '#f39c12',
    '#9b59b6',
    '#2ecc71',
    '#e67e22',
    '#34495e',
    '#f1c40f',
    '#1abc9c',
    '#e91e63',
    '#8e44ad',
    '#27ae60',
    '#d35400',
    '#2c3e50',
  ];

  return uniqueMaterials.map((material, index) => ({
    name: `${material}`,
    value: colors[index % colors.length],
  }));
};

/**
 * @function initPipeMaterialTheme
 * @description 初始化材质专题图
 */
const initPipeMaterialTheme = async (): Promise<void> => {
  try {
    loading.value = true;
    console.log('开始初始化材质专题图...');

    // 获取地图实例
    const map = AppMaplibre.getMap();

    // 备份原始样式
    const layer = map.getLayer(PIPELINE_LAYER_ID);
    if (layer && 'paint' in layer && layer.paint) {
      // 安全地获取原始line-color值
      const paintStyle = layer.paint as any;
      originalLineColor.value = paintStyle['line-color'] || '#2b78fe';
      console.log('已备份原始管线颜色:', originalLineColor.value);
    }

    // 备份管点图层的原始minzoom并调整
    const nodeLayer = map.getLayer(PIPE_NODE_LAYER_ID);
    if (nodeLayer && 'minzoom' in nodeLayer) {
      originalNodeMinZoom.value = nodeLayer.minzoom || 12;
      // 调整管点可见层级到更早显示（从12调整到8）
      map.setLayerZoomRange(PIPE_NODE_LAYER_ID, 16, (nodeLayer as any).maxzoom || 24);
      console.log(
        `管点图层可见层级已调整: minzoom从${originalNodeMinZoom.value}调整为8`
      );
    }

    // 获取材质数据
    const response: ApiResult<any> = await queryPipeMaterialList();
    if (!response || !response.data) {
      throw new Error('获取材质数据失败');
    }

    // 提取材质数组
    const materials = response.data;
    if (materials.length === 0) {
      throw new Error('未找到有效的材质数据');
    }

    console.log('获取到的材质数据:', materials);

    // 生成颜色映射
    const colorMapping = generateColorMapping(materials);

    // 应用样式到管线图层
    (map as any).setPaintProperty(PIPELINE_LAYER_ID, 'line-color', colorMapping);

    console.log('材质专题图样式已应用');

    // 生成并显示图例
    const legendData = generateLegendData(materials, colorMapping);
    const legendObj: LegendObj = {
      title: '材质分类',
      data: legendData,
    };

    legendStore.setLegend(legendObj);
    console.log('材质图例已显示');

    // 设置鼠标悬停事件
    setupMouseHover();

    // ElMessage.success('材质专题图加载完成');
  } catch (error) {
    console.error('初始化材质专题图失败:', error);
    ElMessage.error(
      `加载失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  } finally {
    loading.value = false;
  }
};

/**
 * @function restorePipelineStyle
 * @description 还原管线样式
 */
const restorePipelineStyle = (): void => {
  try {
    const map = AppMaplibre.getMap();
    console.log('开始还原材质专题图样式...');
    // 先重置为简单的颜色值，清除复杂表达式
    try {
      // 使用原始备份的颜色或默认颜色
      const colorToRestore = originalLineColor.value || '#2b78fe';
      console.log('正在还原管线颜色为:', colorToRestore);
      
      // 直接设置简单的颜色值
      (map as any).setPaintProperty(PIPELINE_LAYER_ID, 'line-color', colorToRestore);
      console.log('管线颜色已还原');
    } catch (colorError) {
      console.warn('还原管线颜色失败，使用默认颜色:', colorError);
      // 如果还原失败，强制使用默认颜色
      try {
        (map as any).setPaintProperty(PIPELINE_LAYER_ID, 'line-color', '#2b78fe');
        console.log('已应用默认管线颜色');
      } catch (defaultError) {
        console.error('应用默认颜色也失败:', defaultError);
      }
    }

    // 还原管点图层的原始minzoom
    if (originalNodeMinZoom.value !== null) {
      try {
        const nodeLayer = map.getLayer(PIPE_NODE_LAYER_ID);
        if (nodeLayer) {
          map.setLayerZoomRange(
            PIPE_NODE_LAYER_ID,
            originalNodeMinZoom.value,
            (nodeLayer as any).maxzoom || 24
          );
          console.log(
            `管点图层可见层级已还原: minzoom还原为${originalNodeMinZoom.value}`
          );
        }
        originalNodeMinZoom.value = null;
      } catch (zoomError) {
        console.warn('还原管点图层缩放级别失败:', zoomError);
        originalNodeMinZoom.value = null;
      }
    }

    // 清除图例
    try {
      legendStore.clearLegend();
      console.log('图例已清除');
    } catch (legendError) {
      console.warn('清除图例失败:', legendError);
    }

    // 清理鼠标悬停事件
    try {
      cleanupMouseHover();
      console.log('鼠标悬停事件已清理');
    } catch (hoverError) {
      console.warn('清理鼠标悬停事件失败:', hoverError);
    }

    console.log('材质专题图样式还原完成');
  } catch (error) {
    console.error('还原材质专题图样式失败:', error);
    // 即使出错，也要尝试基本的清理工作
    try {
      legendStore.clearLegend();
      cleanupMouseHover();
    } catch (cleanupError) {
      console.error('基本清理工作也失败:', cleanupError);
    }
  }
};

// ============ 生命周期钩子 ============

onMounted(() => {
  initPipeMaterialTheme();
});

onUnmounted(() => {
  // 在组件卸载前清理鼠标悬停事件
  cleanupMouseHover();
  restorePipelineStyle();
});
</script>

<style scoped lang="scss">
.pipe-material-theme {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 400px;
  min-height: 120px;
  z-index: 1000;
}

.loading-section {
  .loading-content {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;

    .loading-text {
      color: #409eff;
      font-size: 14px;
    }
  }
}

.description-section {
  .description-text {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
    display: block;
  }
}
</style> 