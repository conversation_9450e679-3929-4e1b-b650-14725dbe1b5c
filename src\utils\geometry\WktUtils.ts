/**
 * @fileoverview GeoJSON与WKT格式转换工具类
 * @description 提供GeoJSON几何体与WKT（Well-Known Text）格式之间的双向转换功能
 * <AUTHOR>
 * @version 1.0.0
 */

import type { GeoJSON } from 'geojson';

/**
 * @interface WktOptions
 * @description WKT转换选项配置
 */
export interface WktOptions {
  /** 坐标精度（小数位数），默认为6 */
  precision?: number;
  /** 是否包含Z坐标，默认为false */
  includeZ?: boolean;
  /** 是否包含M坐标，默认为false */
  includeM?: boolean;
  /** 坐标分隔符，默认为空格 */
  coordinateSeparator?: string;
  /** 是否格式化输出（换行和缩进），默认为false */
  formatted?: boolean;
}

/**
 * @interface WktResult
 * @description WKT转换结果
 */
export interface WktResult {
  /** 转换是否成功 */
  success: boolean;
  /** WKT字符串 */
  wkt?: string;
  /** 错误信息 */
  error?: string;
  /** 几何类型 */
  geometryType?: string;
}

/**
 * @class WktUtils
 * @description WKT格式转换工具类
 */
export class WktUtils {
  /** 默认转换选项 */
  private static readonly DEFAULT_OPTIONS: Required<WktOptions> = {
    precision: 6,
    includeZ: false,
    includeM: false,
    coordinateSeparator: ' ',
    formatted: false
  };

  /**
   * @description 将GeoJSON几何体转换为WKT字符串
   * @param geometry - GeoJSON几何体
   * @param options - 转换选项
   * @returns WKT转换结果
   * @example
   * ```typescript
   * const geometry = {
   *   type: 'Point',
   *   coordinates: [120.1234, 30.5678]
   * };
   * const result = WktUtils.geometryToWkt(geometry);
   * console.log(result.wkt); // "POINT (120.1234 30.5678)"
   * ```
   */
  static geometryToWkt(
    geometry: GeoJSON.Geometry | null | undefined,
    options: WktOptions = {}
  ): WktResult {
    try {
      // 验证输入
      if (!geometry) {
        return {
          success: false,
          error: '几何体不能为空'
        };
      }

      if (!geometry.type) {
        return {
          success: false,
          error: '几何体缺少type属性'
        };
      }

      // 合并选项
      const opts = { ...this.DEFAULT_OPTIONS, ...options };

      // 根据几何类型进行转换
      let wkt: string;
      switch (geometry.type) {
        case 'Point':
          wkt = this.pointToWkt(geometry as GeoJSON.Point, opts);
          break;
        case 'LineString':
          wkt = this.lineStringToWkt(geometry as GeoJSON.LineString, opts);
          break;
        case 'Polygon':
          wkt = this.polygonToWkt(geometry as GeoJSON.Polygon, opts);
          break;
        case 'MultiPoint':
          wkt = this.multiPointToWkt(geometry as GeoJSON.MultiPoint, opts);
          break;
        case 'MultiLineString':
          wkt = this.multiLineStringToWkt(geometry as GeoJSON.MultiLineString, opts);
          break;
        case 'MultiPolygon':
          wkt = this.multiPolygonToWkt(geometry as GeoJSON.MultiPolygon, opts);
          break;
        case 'GeometryCollection':
          wkt = this.geometryCollectionToWkt(geometry as GeoJSON.GeometryCollection, opts);
          break;
        default:
          return {
            success: false,
            error: `不支持的几何类型: ${(geometry as any).type}`
          };
      }

      return {
        success: true,
        wkt,
        geometryType: geometry.type
      };

    } catch (error) {
      return {
        success: false,
        error: `转换失败: ${(error as Error).message}`
      };
    }
  }

  /**
   * @description 将GeoJSON Feature转换为WKT字符串
   * @param feature - GeoJSON Feature
   * @param options - 转换选项
   * @returns WKT转换结果
   */
  static featureToWkt(
    feature: GeoJSON.Feature | null | undefined,
    options: WktOptions = {}
  ): WktResult {
    if (!feature || !feature.geometry) {
      return {
        success: false,
        error: 'Feature或其几何体不能为空'
      };
    }

    return this.geometryToWkt(feature.geometry, options);
  }

  /**
   * @description Point几何体转WKT
   * @private
   */
  private static pointToWkt(geometry: GeoJSON.Point, options: Required<WktOptions>): string {
    const coords = geometry.coordinates;
    if (!Array.isArray(coords) || coords.length < 2) {
      throw new Error('Point坐标无效');
    }

    if (coords.length === 0 || coords.every(c => c === null || c === undefined)) {
      return 'POINT EMPTY';
    }

    const coordStr = this.formatCoordinate(coords, options);
    return `POINT (${coordStr})`;
  }

  /**
   * @description LineString几何体转WKT
   * @private
   */
  private static lineStringToWkt(geometry: GeoJSON.LineString, options: Required<WktOptions>): string {
    const coords = geometry.coordinates;
    if (!Array.isArray(coords) || coords.length < 2) {
      throw new Error('LineString至少需要2个坐标点');
    }

    if (coords.length === 0) {
      return 'LINESTRING EMPTY';
    }

    const coordStr = coords.map(coord => this.formatCoordinate(coord, options)).join(', ');
    return `LINESTRING (${coordStr})`;
  }

  /**
   * @description Polygon几何体转WKT
   * @private
   */
  private static polygonToWkt(geometry: GeoJSON.Polygon, options: Required<WktOptions>): string {
    const coords = geometry.coordinates;
    if (!Array.isArray(coords) || coords.length === 0) {
      return 'POLYGON EMPTY';
    }

    const rings = coords.map(ring => {
      if (ring.length < 4) {
        throw new Error('多边形环至少需要4个坐标点');
      }
      const coordStr = ring.map(coord => this.formatCoordinate(coord, options)).join(', ');
      return `(${coordStr})`;
    });

    return `POLYGON (${rings.join(', ')})`;
  }

  /**
   * @description MultiPoint几何体转WKT
   * @private
   */
  private static multiPointToWkt(geometry: GeoJSON.MultiPoint, options: Required<WktOptions>): string {
    const coords = geometry.coordinates;
    if (!Array.isArray(coords) || coords.length === 0) {
      return 'MULTIPOINT EMPTY';
    }

    const points = coords.map(coord => {
      const coordStr = this.formatCoordinate(coord, options);
      return `(${coordStr})`;
    });

    return `MULTIPOINT (${points.join(', ')})`;
  }

  /**
   * @description MultiLineString几何体转WKT
   * @private
   */
  private static multiLineStringToWkt(geometry: GeoJSON.MultiLineString, options: Required<WktOptions>): string {
    const coords = geometry.coordinates;
    if (!Array.isArray(coords) || coords.length === 0) {
      return 'MULTILINESTRING EMPTY';
    }

    const lines = coords.map(line => {
      if (line.length < 2) {
        throw new Error('LineString至少需要2个坐标点');
      }
      const coordStr = line.map(coord => this.formatCoordinate(coord, options)).join(', ');
      return `(${coordStr})`;
    });

    return `MULTILINESTRING (${lines.join(', ')})`;
  }

  /**
   * @description MultiPolygon几何体转WKT
   * @private
   */
  private static multiPolygonToWkt(geometry: GeoJSON.MultiPolygon, options: Required<WktOptions>): string {
    const coords = geometry.coordinates;
    if (!Array.isArray(coords) || coords.length === 0) {
      return 'MULTIPOLYGON EMPTY';
    }

    const polygons = coords.map(polygon => {
      const rings = polygon.map(ring => {
        if (ring.length < 4) {
          throw new Error('多边形环至少需要4个坐标点');
        }
        const coordStr = ring.map(coord => this.formatCoordinate(coord, options)).join(', ');
        return `(${coordStr})`;
      });
      return `(${rings.join(', ')})`;
    });

    return `MULTIPOLYGON (${polygons.join(', ')})`;
  }

  /**
   * @description GeometryCollection转WKT
   * @private
   */
  private static geometryCollectionToWkt(geometry: GeoJSON.GeometryCollection, options: Required<WktOptions>): string {
    const geometries = geometry.geometries;
    if (!Array.isArray(geometries) || geometries.length === 0) {
      return 'GEOMETRYCOLLECTION EMPTY';
    }

    const wkts: string[] = [];
    for (const geom of geometries) {
      const result = this.geometryToWkt(geom, options);
      if (result.success && result.wkt) {
        wkts.push(result.wkt);
      } else {
        throw new Error(`GeometryCollection中包含无效几何体: ${result.error}`);
      }
    }

    return `GEOMETRYCOLLECTION (${wkts.join(', ')})`;
  }

  /**
   * @description 格式化坐标
   * @private
   */
  private static formatCoordinate(coordinate: number[], options: Required<WktOptions>): string {
    if (!Array.isArray(coordinate) || coordinate.length < 2) {
      throw new Error('坐标格式无效');
    }

    const [x, y, z, m] = coordinate;
    
    // 格式化数字精度
    let coords = [
      Number(x).toFixed(options.precision),
      Number(y).toFixed(options.precision)
    ];

    // 添加Z坐标
    if (options.includeZ && z !== undefined && z !== null) {
      coords.push(Number(z).toFixed(options.precision));
    }

    // 添加M坐标
    if (options.includeM && m !== undefined && m !== null) {
      coords.push(Number(m).toFixed(options.precision));
    }

    return coords.join(options.coordinateSeparator);
  }

  /**
   * @description 批量转换GeoJSON要素集合为WKT数组
   * @param featureCollection - GeoJSON要素集合
   * @param options - 转换选项
   * @returns WKT字符串数组
   */
  static featureCollectionToWktArray(
    featureCollection: GeoJSON.FeatureCollection,
    options: WktOptions = {}
  ): Array<{ success: boolean; wkt?: string; error?: string; featureId?: string | number }> {
    if (!featureCollection || !featureCollection.features) {
      return [{
        success: false,
        error: 'FeatureCollection或features数组不能为空'
      }];
    }

    return featureCollection.features.map((feature, index) => {
      const result = this.featureToWkt(feature, options);
      return {
        ...result,
        featureId: feature.id ?? index
      };
    });
  }

  /**
   * @description 验证GeoJSON几何体格式
   * @param geometry - 待验证的几何体
   * @returns 验证结果
   */
  static validateGeometry(geometry: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!geometry) {
      return { isValid: false, errors: ['几何体不能为空'] };
    }

    if (!geometry.type) {
      errors.push('几何体缺少type属性');
    }

    if (!geometry.coordinates) {
      errors.push('几何体缺少coordinates属性');
    }

    const supportedTypes = [
      'Point', 'LineString', 'Polygon', 
      'MultiPoint', 'MultiLineString', 'MultiPolygon', 
      'GeometryCollection'
    ];

    if (geometry.type && !supportedTypes.includes(geometry.type)) {
      errors.push(`不支持的几何类型: ${geometry.type}`);
    }

    // 基础坐标验证
    if (geometry.coordinates && Array.isArray(geometry.coordinates)) {
      try {
        switch (geometry.type) {
          case 'Point':
            if (!Array.isArray(geometry.coordinates) || geometry.coordinates.length < 2) {
              errors.push('Point坐标格式无效');
            }
            break;
          case 'LineString':
            if (geometry.coordinates.length < 2) {
              errors.push('LineString至少需要2个坐标点');
            }
            break;
          case 'Polygon':
            if (geometry.coordinates.length === 0 || geometry.coordinates[0].length < 4) {
              errors.push('Polygon外环至少需要4个坐标点');
            }
            break;
        }
      } catch (error) {
        errors.push(`坐标验证失败: ${(error as Error).message}`);
      }
    }

    return { isValid: errors.length === 0, errors };
  }
}

/**
 * @description 便捷函数：将GeoJSON几何体快速转换为WKT字符串
 * @param geometry - GeoJSON几何体
 * @param precision - 坐标精度，默认为6
 * @returns WKT字符串，转换失败时返回null
 */
export function geoJsonToWkt(geometry: GeoJSON.Geometry, precision: number = 6): string | null {
  const result = WktUtils.geometryToWkt(geometry, { precision });
  return result.success ? (result.wkt || null) : null;
}

/**
 * @description 便捷函数：将GeoJSON Feature快速转换为WKT字符串
 * @param feature - GeoJSON Feature
 * @param precision - 坐标精度，默认为6
 * @returns WKT字符串，转换失败时返回null
 */
export function featureToWkt(feature: GeoJSON.Feature, precision: number = 6): string | null {
  const result = WktUtils.featureToWkt(feature, { precision });
  return result.success ? (result.wkt || null) : null;
}

// 默认导出
export default WktUtils; 