export function loadBMap(ak: string) {
  return new Promise(function(resolve, reject) {
    if (typeof (window as any).BMap !== 'undefined') {
      resolve((window as any).BMap)
      return true
    }
    (window as any).onBMapCallback = function() {
      resolve((window as any).BMap)
    }
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src =
      'https://api.map.baidu.com/api?type=webgl&v=1.0&ak=' + ak + '&callback=onBMapCallback'
    script.onerror = reject
    document.head.appendChild(script)
  })
}