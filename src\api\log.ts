import hRequest from '@/utils/http'
import type { DataType } from '@/utils/http/types'
// 分页
export const logPage = (params: any) => {
  return hRequest.get<DataType>({
    url: '/sys/oper-log/page',
    params
  })
}
// 日志新增
export const addLog = (data: any) => {
  return hRequest.post<DataType>({
    url: "/sys/oper-log",
    data: data
  });
};
// 详情
export const detailsLog = (id: any) => {
  return hRequest.get<DataType>({
    url: `/sys/oper-log/${id}`,
  });
};
// 删除
export const deleteLog = (id: any) => {
  return hRequest.delete<DataType>({
    url: `/sys/oper-log/${id}`,
  });
};
// 访客统计
export const visitorLog = () => {
  return hRequest.get<DataType>({
    url: '/sys/oper-log/visitor'
  })
}
// 近七日访客统计
export const visitorDay = () => {
  return hRequest.get<DataType>({
    url: '/sys/oper-log/visitor/last7'
  })
}
// 月度访客统计
export const visitorMonth = (params: any) => {
  return hRequest.get<DataType>({
    url: '/sys/oper-log/visitor/month',
    params
  })
}
// 年度访客统计
export const visitorYear = (params: any) => {
  return hRequest.get<DataType>({
    url: '/sys/oper-log/visitor/year',
    params
  })
}
// 用户看板
export const userDashboard = (params: any) => {
  return hRequest.get<DataType>({
    url: '/sys/oper-log/dashboard',
    params
  })
}
// 数据日志
export const logDataPage = (params: any) => {
  return hRequest.get<DataType>({
    url: '/sys/oper-log/data/page',
    params
  })
}
// 访问统计
export const visitorStat = () => {
  return hRequest.get<DataType>({
    url: '/sys/oper-log/visitor/stat'
  })
}