<!--
 * @Description: 消防栓专题
 * @Date: 2024-01-16
 * @Author: AI Assistant
 * @LastEditTime: 2024-01-16
 -->
<template>
  <page-card class="fire-hydrant-theme" title="消防栓专题" @closeCard="closeCard">
    <!-- 加载状态显示 -->
    <el-row v-if="loading" class="loading-section">
      <el-col :span="24">
        <div class="loading-content">
          <el-icon class="is-loading"><Loading /></el-icon>
          <el-text class="loading-text">正在加载消防栓专题图...</el-text>
        </div>
      </el-col>
    </el-row>

    <!-- 专题图说明 -->
    <el-row v-else class="description-section">
      <el-col :span="24">
        <el-text class="description-text">
          突出显示管网中的消防栓设备，使用专用图标标识消防栓位置，其余管点保持默认显示。
        </el-text>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElIcon } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import PageCard from '@/components/PageCard.vue';
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre';
import type { Map as MapLibreMap } from 'maplibre-gl';

/**
 * @interface Emits
 * @description 组件事件接口
 */
interface Emits {
  (e: 'close'): void;
}

const emit = defineEmits<Emits>();

// ============ 响应式状态 ============

/** 加载状态 */
const loading = ref(false);

/** 管点图层ID常量 */
const PIPE_NODE_LAYER_ID = 'mvt_pipeNode';

/** 消防栓专题图层ID */
const FIRE_HYDRANT_LAYER_ID = 'fire-hydrant-theme-layer';

/** 消防栓图标数据源ID */
const FIRE_HYDRANT_SOURCE_ID = 'fire-hydrant-source';

/** 原始管点图层样式备份 */
const originalNodePaint = ref<Record<string, any> | null>(null);

/** 原始管点图层最小缩放级别备份 */
const originalNodeMinZoom = ref<number | null>(null);

/** 消防栓图标是否已加载 */
const fireHydrantIconLoaded = ref(false);

// ============ 事件处理方法 ============

/**
 * @function closeCard
 * @description 关闭专题面板
 */
const closeCard = (): void => {
  try {
    console.log('关闭消防栓专题面板');
    
    // 在关闭前先还原样式
    restoreNodeStyle();
    
    emit('close');
    ElMessage.info('已关闭消防栓专题');
  } catch (error) {
    console.error('关闭面板失败:', error);
    ElMessage.error('关闭面板失败');
  }
};

/**
 * @function loadFireHydrantIcon
 * @description 加载消防栓图标到地图
 */
const loadFireHydrantIcon = async (): Promise<void> => {
  try {
    const map = AppMaplibre.getMap();
    
    // 检查图标是否已存在
    if (map.hasImage('fire-hydrant-icon')) {
      fireHydrantIconLoaded.value = true;
      console.log('消防栓图标已存在，跳过加载');
      return;
    }

    // 创建图片元素加载SVG
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    await new Promise<void>((resolve, reject) => {
      img.onload = () => {
        try {
          // 添加图标到地图
          map.addImage('fire-hydrant-icon', img, { sdf: false });
          fireHydrantIconLoaded.value = true;
          console.log('消防栓图标加载成功');
          resolve();
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => {
        reject(new Error('消防栓图标加载失败'));
      };
      
      // 加载SVG图标 - 使用动态import获取正确的资源路径
      const fireHydrantIconUrl = new URL('/src/assets/icon-svg/fire-hydrant.svg', import.meta.url).href;
      img.src = fireHydrantIconUrl;
    });

  } catch (error) {
    console.error('加载消防栓图标失败:', error);
    throw error;
  }
};

/**
 * @function createFireHydrantLayer
 * @description 创建消防栓专题图层
 */
const createFireHydrantLayer = (): void => {
  try {
    const map = AppMaplibre.getMap();

    // 获取原始管点数据源
    const originalSource = map.getSource('mvt_pipeNode') as any;
    if (!originalSource) {
      throw new Error('未找到管点数据源');
    }

    // 添加消防栓数据源（使用相同的MVT源）
    if (!map.getSource(FIRE_HYDRANT_SOURCE_ID)) {
      // 使用与原始管点图层相同的配置
      const sourceConfig = {
        type: 'vector' as const,
        minzoom: originalSource.minzoom || 0,
        maxzoom: originalSource.maxzoom || 22
      };

      // 根据原始数据源类型添加相应配置
      if (originalSource.tiles) {
        (sourceConfig as any).tiles = originalSource.tiles;
      } else if (originalSource.url) {
        (sourceConfig as any).url = originalSource.url;
      } else {
        // 使用管点图层的默认MVT URL
        (sourceConfig as any).tiles = ['https://gis.lshywater.cn/api/analyse/gs/pt/mvt/{z}/{x}/{y}.mvt'];
      }

      map.addSource(FIRE_HYDRANT_SOURCE_ID, sourceConfig);
    }

    // 添加消防栓图层（symbol类型用于显示图标）
    if (!map.getLayer(FIRE_HYDRANT_LAYER_ID)) {
      map.addLayer({
        id: FIRE_HYDRANT_LAYER_ID,
        type: 'symbol',
        source: FIRE_HYDRANT_SOURCE_ID,
        'source-layer': 'point', // 与原管点图层相同的source-layer
        minzoom: 8, // 较早显示消防栓
        filter: ['==', ['get', 'fsw'], '消火栓'], // 只显示fsw='消火栓'的要素
        layout: {
          'icon-image': 'fire-hydrant-icon',
          'icon-size': 0.8,
          'icon-allow-overlap': true,
          'icon-ignore-placement': true
        },
        paint: {
          'icon-opacity': 1
        }
      });
    }

    console.log('消防栓专题图层创建成功');
  } catch (error) {
    console.error('创建消防栓专题图层失败:', error);
    throw error;
  }
};

/**
 * @function modifyNodeLayerStyle
 * @description 修改管点图层样式，降低非消防栓管点的显示
 */
const modifyNodeLayerStyle = (): void => {
  try {
    const map = AppMaplibre.getMap();
    
    // 备份原始样式
    const nodeLayer = map.getLayer(PIPE_NODE_LAYER_ID);
    if (nodeLayer && 'paint' in nodeLayer && nodeLayer.paint) {
      originalNodePaint.value = { ...nodeLayer.paint };
      console.log('已备份原始管点样式:', originalNodePaint.value);
    }

    // 备份并调整管点图层的缩放级别
    if (nodeLayer && 'minzoom' in nodeLayer) {
      originalNodeMinZoom.value = nodeLayer.minzoom || 12;
      map.setLayerZoomRange(PIPE_NODE_LAYER_ID, 12, (nodeLayer as any).maxzoom || 24);
      console.log(`管点图层可见层级保持: minzoom=${originalNodeMinZoom.value}`);
    }

    // 修改管点样式，使非消防栓管点更加低调
    map.setPaintProperty(PIPE_NODE_LAYER_ID, 'circle-opacity', 0);
    map.setPaintProperty(PIPE_NODE_LAYER_ID, 'circle-stroke-opacity', 0);
    
    console.log('管点图层样式已调整');
  } catch (error) {
    console.error('修改管点图层样式失败:', error);
    throw error;
  }
};

/**
 * @function initFireHydrantTheme
 * @description 初始化消防栓专题图
 */
const initFireHydrantTheme = async (): Promise<void> => {
  try {
    loading.value = true;
    console.log('开始初始化消防栓专题图...');

    // 1. 加载消防栓图标
    await loadFireHydrantIcon();

    // 2. 创建消防栓专题图层
    createFireHydrantLayer();

    // 3. 调整原管点图层样式
    modifyNodeLayerStyle();

    // ElMessage.success('消防栓专题图加载完成');
    console.log('消防栓专题图初始化完成');
  } catch (error) {
    console.error('初始化消防栓专题图失败:', error);
    ElMessage.error(`加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

/**
 * @function restoreNodeStyle
 * @description 还原管点图层样式
 */
const restoreNodeStyle = (): void => {
  try {
    const map = AppMaplibre.getMap();
    console.log('开始还原管点样式...');
    
    // 移除消防栓专题图层
    if (map.getLayer(FIRE_HYDRANT_LAYER_ID)) {
      map.removeLayer(FIRE_HYDRANT_LAYER_ID);
      console.log('消防栓专题图层已移除');
    }

    // 移除消防栓数据源
    if (map.getSource(FIRE_HYDRANT_SOURCE_ID)) {
      map.removeSource(FIRE_HYDRANT_SOURCE_ID);
      console.log('消防栓数据源已移除');
    }

    // 还原管点图层样式
    if (originalNodePaint.value) {
      try {
        const nodeLayer = map.getLayer(PIPE_NODE_LAYER_ID);
        if (nodeLayer) {
          // 逐个还原paint属性
          Object.entries(originalNodePaint.value).forEach(([key, value]) => {
            map.setPaintProperty(PIPE_NODE_LAYER_ID, key, value);
          });
          console.log('管点图层样式已还原');
        }
        originalNodePaint.value = null;
      } catch (paintError) {
        console.warn('还原管点样式失败:', paintError);
        // 如果还原失败，使用默认样式
        map.setPaintProperty(PIPE_NODE_LAYER_ID, 'circle-opacity', 1);
        map.setPaintProperty(PIPE_NODE_LAYER_ID, 'circle-stroke-opacity', 1);
        originalNodePaint.value = null;
      }
    }

    // 还原管点图层的缩放级别
    if (originalNodeMinZoom.value !== null) {
      try {
        const nodeLayer = map.getLayer(PIPE_NODE_LAYER_ID);
        if (nodeLayer) {
          map.setLayerZoomRange(
            PIPE_NODE_LAYER_ID,
            originalNodeMinZoom.value,
            (nodeLayer as any).maxzoom || 24
          );
          console.log(`管点图层可见层级已还原: minzoom还原为${originalNodeMinZoom.value}`);
        }
        originalNodeMinZoom.value = null;
      } catch (zoomError) {
        console.warn('还原管点图层缩放级别失败:', zoomError);
        originalNodeMinZoom.value = null;
      }
    }

    console.log('管点样式还原完成');
  } catch (error) {
    console.error('还原管点样式失败:', error);
  }
};

// ============ 生命周期钩子 ============

/**
 * @description 组件挂载时初始化消防栓专题
 */
onMounted(() => {
  try {
    // 延迟初始化，确保地图完全加载
    setTimeout(() => {
      initFireHydrantTheme();
    }, 500);
  } catch (error) {
    console.error('组件挂载时初始化失败:', error);
    ElMessage.error('初始化消防栓专题失败');
  }
});

/**
 * @description 组件卸载时清理资源
 */
onUnmounted(() => {
  try {
    restoreNodeStyle();
    console.log('消防栓专题资源已清理');
  } catch (error) {
    console.error('清理消防栓专题资源失败:', error);
  }
});
</script>

<style scoped lang="scss">
.fire-hydrant-theme {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 400px;
  min-height: 120px;
  z-index: 1000;
}

.loading-section {
  padding: 20px;
  text-align: center;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .loading-text {
      color: #606266;
      font-size: 14px;
    }
  }
}

.description-section {
  .description-text {
    color: #2c3037;
    font-size: 14px;
    line-height: 1.6;
    display: block;
  }
}
</style> 