/**
 * @fileoverview 坐标计算数据存储
 * @description 提供坐标拾取、转换和存储的全局状态管理
 * <AUTHOR>
 * @version 2.0.0
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { CoordinateTransform } from '@/utils/coordinate/CoordinateTransform';
import type { CoordinatePoint } from '@/utils/coordinate/CoordinateTransform';

/**
 * @description 坐标计算全局状态管理
 * 负责存储和管理坐标拾取相关的状态，包括当前坐标、历史记录和拾取状态
 */
export const useCalcCoordStore = defineStore("CalcCoordStore", () => {
  /**
   * 当前选中的坐标点
   * 存储最近一次拾取的坐标，默认为null
   */
  const currentCoord = ref<CoordinatePoint | null>(null);
  
  /**
   * 坐标拾取历史记录
   * 按时间顺序存储所有拾取的坐标点
   */
  const coordHistory = ref<CoordinatePoint[]>([]);
  
  /**
   * 是否正在拾取坐标
   * 标记当前是否处于坐标拾取状态
   */
  const isPicking = ref(false);
  
  /**
   * 最大历史记录数量
   * 限制历史记录的最大长度，防止内存占用过大
   */
  const MAX_HISTORY_LENGTH = 50;
  
  /**
   * 计算属性：当前坐标的 WGS84 格式
   * 如果当前坐标不存在，返回null
   */
  const wgs84Coord = computed(() => {
    if (!currentCoord.value) return null;
    // 注意：此处假设 currentCoord 已经是 WGS84 坐标
    return currentCoord.value;
  });
  
  /**
   * 计算属性：当前坐标的 GCJ02 格式（国测局坐标系/火星坐标系）
   * 如果当前坐标不存在，返回null
   */
  const gcj02Coord = computed(() => {
    if (!currentCoord.value) return null;
    return CoordinateTransform.wgs84ToGcj02(currentCoord.value);
  });
  
  /**
   * 计算属性：当前坐标的 BD09 格式（百度坐标系）
   * 如果当前坐标不存在，返回null
   */
  const bd09Coord = computed(() => {
    if (!currentCoord.value) return null;
    return CoordinateTransform.wgs84ToBd09(currentCoord.value);
  });
  
  /**
   * 计算属性：当前坐标的 EPSG:3857 格式（Web墨卡托投影）
   * 如果当前坐标不存在，返回null
   */
  const epsg3857Coord = computed(() => {
    if (!currentCoord.value) return null;
    
    // 地球半径
    const EARTH_RADIUS = 6378137.0;
    
    // 计算墨卡托投影坐标
    const lng = currentCoord.value.lng * Math.PI * EARTH_RADIUS / 180.0;
    let lat = Math.log(Math.tan((90.0 + currentCoord.value.lat) * Math.PI / 360.0)) * EARTH_RADIUS;
    
    // 处理极端情况
    if (!Number.isFinite(lat)) {
      lat = 0;
    }
    
    return {
      lng: Number(lng.toFixed(2)),
      lat: Number(lat.toFixed(2)),
      alt: currentCoord.value.alt
    };
  });
  
  /**
   * @description 设置当前坐标
   * 更新当前坐标并将其添加到历史记录中
   * @param coord - 坐标点
   */
  const setCurrentCoord = (coord: CoordinatePoint) => {
    if (!coord || typeof coord.lng !== 'number' || typeof coord.lat !== 'number') {
      console.error('无效的坐标数据', coord);
      return;
    }
    
    // 格式化坐标精度
    const formattedCoord = {
      lng: Number(coord.lng.toFixed(6)),
      lat: Number(coord.lat.toFixed(6)),
      alt: coord.alt ? Number(coord.alt.toFixed(3)) : coord.alt
    };
    
    // 更新当前坐标
    currentCoord.value = formattedCoord;
    
    // 添加到历史记录
    coordHistory.value.push({...formattedCoord});
    
    // 限制历史记录长度，防止内存占用过大
    if (coordHistory.value.length > MAX_HISTORY_LENGTH) {
      coordHistory.value = coordHistory.value.slice(-MAX_HISTORY_LENGTH);
    }
  };
  
  /**
   * @description 清除当前坐标
   * 将当前坐标重置为null
   */
  const clearCurrentCoord = () => {
    currentCoord.value = null;
  };
  
  /**
   * @description 清除历史记录
   * 清空所有坐标历史记录
   */
  const clearHistory = () => {
    coordHistory.value = [];
  };
  
  /**
   * @description 开始坐标拾取
   * 将拾取状态标记为true
   */
  const startPicking = () => {
    isPicking.value = true;
  };
  
  /**
   * @description 停止坐标拾取
   * 将拾取状态标记为false
   */
  const stopPicking = () => {
    isPicking.value = false;
  };
  
  /**
   * @description 坐标转换
   * 在不同坐标系之间转换坐标
   * @param coord - 原始坐标
   * @param fromType - 源坐标系类型
   * @param toType - 目标坐标系类型
   * @returns 转换后的坐标
   * @throws 如果坐标转换失败
   */
  const transformCoord = (
    coord: CoordinatePoint,
    fromType: 'WGS84' | 'GCJ02' | 'BD09',
    toType: 'WGS84' | 'GCJ02' | 'BD09'
  ): CoordinatePoint => {
    try {
      // 验证输入坐标
      if (!coord || typeof coord.lng !== 'number' || typeof coord.lat !== 'number') {
        throw new Error('无效的坐标数据');
      }
      
      // 如果源坐标系和目标坐标系相同，直接返回副本
      if (fromType === toType) {
        return {...coord};
      }
      
      return CoordinateTransform.transform(coord, fromType, toType);
    } catch (error) {
      console.error('坐标转换错误:', error);
      throw error;
    }
  };
  
  /**
   * @description 获取最近的历史坐标
   * @param count - 要获取的坐标数量，默认为5
   * @returns 最近的历史坐标数组
   */
  const getRecentHistory = (count: number = 5): CoordinatePoint[] => {
    return coordHistory.value.slice(-Math.min(count, coordHistory.value.length));
  };
  
  /**
   * @description 从历史记录中删除特定索引的坐标
   * @param index - 要删除的坐标索引
   */
  const removeHistoryAt = (index: number): void => {
    if (index >= 0 && index < coordHistory.value.length) {
      coordHistory.value.splice(index, 1);
    }
  };
  
  return {
    // 状态
    currentCoord,
    coordHistory,
    isPicking,
    
    // 计算属性
    wgs84Coord,
    gcj02Coord,
    bd09Coord,
    epsg3857Coord,
    
    // 方法
    setCurrentCoord,
    clearCurrentCoord,
    clearHistory,
    startPicking,
    stopPicking,
    transformCoord,
    getRecentHistory,
    removeHistoryAt
  };
});