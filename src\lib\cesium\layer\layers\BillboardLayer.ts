
/*
 * @Description: Billboard图层实现
 * @Date: 2023-03-03 13:35:59
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2024-03-19 14:58:30
 */
// import { getDevicesByType } from "@/api/device";
import { getImages } from '@/utils/getImages';
import { BaseLayer } from './BaseLayer';
import { fetchDeviceSummary } from '@/utils/deviceUtils';
import { mockLngLatInPolygon } from '@/utils/mockUtil';

/**
 * @interface DeviceData
 * @description 设备数据接口
 */
interface DeviceData {
  longitude: number;
  latitude: number;
  altitude?: number;
  name?: string;
  properties?: Record<string, any>;
}

/**
 * @class BillboardLayer
 * @description 广告牌图层类，用于在三维场景中显示设备点位
 */
export class BillboardLayer extends BaseLayer {
  protected declare _vector: BC.VectorLayer;

  /**
   * @description 添加图层到场景
   * @param viewer - Cesium 查看器实例
   * @returns 返回矢量图层对象
   */
  async addToInternal(viewer: BC.Viewer) {
    const vector: any = await this.loadVector(this.options);
    vector.addTo(viewer);
    this._vector = vector;
    return vector;
  }

  /**
   * @description 验证并解析经纬度坐标
   * @param longitude - 经度值（字符串或数字）
   * @param latitude - 纬度值（字符串或数字）
   * @returns 有效的坐标数组 [lng, lat] 或 null
   */
  private validateAndParseCoordinates(longitude: any, latitude: any): [number, number] | null {
    // 经度正则表达式：-180 到 180，支持小数
    const longitudeRegex = /^-?(?:180(?:\.0+)?|(?:1[0-7]\d|[1-9]?\d)(?:\.\d+)?)$/;

    // 纬度正则表达式：-90 到 90，支持小数
    const latitudeRegex = /^-?(?:90(?:\.0+)?|(?:[1-8]?\d)(?:\.\d+)?)$/;

    // 转换为字符串进行验证
    const lngStr = String(longitude).trim();
    const latStr = String(latitude).trim();

    // 检查是否为空或无效值
    if (!lngStr || !latStr || lngStr === 'null' || latStr === 'null' ||
        lngStr === 'undefined' || latStr === 'undefined' ||
        lngStr === '' || latStr === '') {
      return null;
    }

    // 正则表达式验证
    if (!longitudeRegex.test(lngStr) || !latitudeRegex.test(latStr)) {
      return null;
    }

    // 转换为数字
    const lng = parseFloat(lngStr);
    const lat = parseFloat(latStr);

    // 数值范围验证（双重保险）
    if (isNaN(lng) || isNaN(lat) || lng < -180 || lng > 180 || lat < -90 || lat > 90) {
      return null;
    }

    return [lng, lat];
  }

  /**
   * @description 从 URL 加载 GeoJSON 数据
   */
  private async loadGeoJsonData(url: string): Promise<GeoJSON.FeatureCollection> {
    try {
      const response = await fetchDeviceSummary({
        size: -1,
        current: 1,
        secondDeviceTypeCode: this.options.code,
      });

      const validFeatures: GeoJSON.Feature[] = [];

      for (const item of response) {
        // 验证经纬度
        const coordinates = this.validateAndParseCoordinates(item.longitude, item.latitude);

        // 如果经纬度验证失败，跳过该项
        if (!coordinates) {
          console.warn('跳过无效坐标的设备:', {
            id: item.id,
            name: item.name,
            longitude: item.longitude,
            latitude: item.latitude
          });
          continue;
        }

        validFeatures.push({
          type: 'Feature',
          properties: item,
          geometry: {
            type: 'Point',
            coordinates: coordinates,
          },
        });
      }
      console.log(validFeatures);
      return {
        type: 'FeatureCollection' as const,
        features: validFeatures,
      };
    } catch (error) {
      throw error;
    }
  }


  /**
   * @description 加载矢量设备数据并创建 Billboard 图层
   * @param deviceOption - 设备配置选项
   * @returns Promise<BC.VectorLayer | undefined>
   */
  async loadVector(deviceOption: any): Promise<BC.VectorLayer | undefined> {


    try {
      // 从 URL 加载 GeoJSON 数据
      const geoJsonData = await this.loadGeoJsonData(deviceOption.url);
      
      // if (!geoJsonData.features || geoJsonData.features.length === 0) {
      //   console.warn(`设备数据为空: ${deviceOption.url}`);
      //   return undefined;
      // }

      // 创建矢量图层
      const vectorLayer = new BC.VectorLayer(deviceOption.id);


      // 遍历所有 GeoJSON Features
      for (const feature of geoJsonData.features) {
        if (feature.geometry.type !== 'Point') {
          console.warn('跳过非点类型的要素', feature);
          continue;
        }

        try {
          // 创建位置对象
          const position = new BC.Position(feature.geometry.coordinates[0], feature.geometry.coordinates[1], feature.properties?.高度 || 200);

          // 创建 Billboard
          const billboard = new BC.Billboard(position, getImages(`device/${deviceOption.icon}2x.png`));
          billboard.size = [44, 51];
          billboard.setStyle({
            scale: 1.0,
            scaleByDistance: {
              near: 200, // 最近距离
              nearValue: 1.8, // 最近距离值
              far: 2000, // 最远距离值
              farValue: 0.5, // 最远距离值
            }, // 根据距离设置比例
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
          });

          billboard.attr = feature?.properties || {};

          // 添加到矢量图层
          vectorLayer.addOverlay(billboard);
        } catch (error) {
          console.error('处理设备要素失败', feature, error);
          continue;
        }
      }

      console.log(`成功加载设备图层 ${deviceOption.name}，共 ${geoJsonData.features.length} 个设备`);
      return vectorLayer;

    } catch (error) {
      console.error(`加载设备图层失败: ${deviceOption.name}`, error);
      throw error;
    }
  }

  /**
   * @description 移除图层
   */
  removeInternal() {
    if (this._vector && this._viewer) {
      this._viewer.removeLayer(this._vector);
    }
  }

  /**
   * @description 设置Billboard图层透明度
   * @param transparent 是否启用透明度
   * @param alphaValue 透明度值 (0.0 - 1.0)
   */
  setTransparency(transparent: boolean, alphaValue: number): void {
    // try {
    //   if (!this._vector) {
    //     console.warn('Billboard图层未初始化，无法设置透明度');
    //     return;
    //   }

    //   // 获取图层中的所有Billboard覆盖物
    //   const overlays = this._vector.getOverlays();
      
    //   if (!overlays || overlays.length === 0) {
    //     console.warn('Billboard图层中没有覆盖物');
    //     return;
    //   }

    //   console.log(`🎨 设置Billboard图层透明度: ${transparent ? `alpha=${alphaValue}` : '恢复不透明'}`);

    //   // 遍历所有Billboard覆盖物并设置透明度
    //   overlays.forEach((overlay: any) => {
    //     if (overlay && overlay.delegate) {
    //       try {
    //         if (transparent) {
    //           // 设置透明度
    //           overlay.delegate.color = BC.Namespace.Cesium.Color.WHITE.withAlpha(alphaValue);
    //         } else {
    //           // 恢复不透明状态
    //           overlay.delegate.color = BC.Namespace.Cesium.Color.WHITE;
    //         }
    //       } catch (error) {
    //         console.error('设置单个Billboard透明度失败:', error);
    //       }
    //     }
    //   });

    //   console.log(`✅ 成功设置${overlays.length}个Billboard的透明度`);
    // } catch (error) {
    //   console.error('设置Billboard图层透明度失败:', error);
    // }
  }
}
