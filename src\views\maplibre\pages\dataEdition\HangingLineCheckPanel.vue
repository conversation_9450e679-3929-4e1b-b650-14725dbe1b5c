<!--
 * @Description: 悬挂线检查面板
 * @Date: 2024-01-16
 * @Author: AI Assistant
 * @LastEditTime: 2024-01-16
 -->
<template>
  <page-card
    class="hanging-line-check-panel"
    title="悬挂线检查"
    @closeCard="closeCard"
  >
    <!-- 统计信息 -->
    <div class="stats-section" v-if="!loading">
      <el-alert
        :title="`发现 ${total} 条悬挂线需要处理`"
        :type="total > 0 ? 'warning' : 'success'"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 操作按钮区域 -->
    <el-row class="button-section" flex="~ row justify-start">
      <el-button
        type="primary"
        class="select-btn h-9"
        :icon="Refresh"
        @click="handleRefresh"
        :loading="loading"
      >
        刷新检查
      </el-button>
    </el-row>

    <!-- 悬挂线列表表格 -->
    <el-row v-if="hangingLines.length > 0" class="table-section">
      <el-col :span="24">
        <el-text class="table-label">悬挂线详细信息</el-text>
        <el-table
          :data="hangingLines"
          v-loading="loading"
          class="routeCt"
          :row-class-name="tableRowClassName"
          stripe
          size="small"
          height="300"
          empty-text="暂无悬挂线数据"
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            :index="getTableIndex"
          />
          <el-table-column
            prop="gxbm"
            label="管线编码"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="gl"
            label="管类"
            width="90"
            show-overflow-tooltip
          />
          <el-table-column
            prop="gj"
            label="管径"
            width="90"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.gj ? `${row.gj}mm` : "-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="szdl"
            label="所在道路"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="操作"
            width="120"
            fixed="right"
            align="center"
          >
            <template #default="{ row, $index }">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleLocate(row)"
                title="定位到地图"
              >
                定位
              </el-button>
              <el-popconfirm
                title="确定要删除这条悬挂线吗？"
                @confirm="handleDelete(row, $index)"
                confirm-button-text="确定"
                cancel-button-text="取消"
              >
                <template #reference>
                  <el-button
                    type="text"
                    style="color: #ff7373"
                    title="删除悬挂线"
                  >
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center mt-5">
          <div>
            <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
          </div>
          <!-- 分页组件 -->
          <el-pagination
            v-if="total > 0"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :total="total"
            :pager-count="5"
            layout="prev, pager, next, jumper"
            background
            class="pagination"
            small
          />
        </div>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Refresh, Delete, Position } from "@element-plus/icons-vue";
import PageCard from "@/components/PageCard.vue";

// API接口
import { queryHangingLines, pipeLineDelete } from "@/api/pipeLine";
import type { GsLnVo, HangingLinePageQuery } from "@/api/pipeLine";

// 地图相关
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import type { Map as MapLibreMap } from "maplibre-gl";

/**
 * 悬挂线高亮图层ID常量
 */
const HANGING_LINE_LAYER_IDS = {
  SOURCE: "hanging-line-highlight-source",
  LAYER: "hanging-line-highlight-layer",
} as const;

/**
 * @interface Emits
 * @description 组件事件接口
 */
interface Emits {
  (e: "close"): void;
  (e: "locate", data: GsLnVo): void;
}

const emit = defineEmits<Emits>();

// ============ 响应式状态 ============

/** 加载状态 */
const loading = ref(false);

/** 悬挂线数据列表 */
const hangingLines = ref<GsLnVo[]>([]);

/** 分页相关状态 */
const currentPage = ref<number>(1);
const pageSize = ref<number>(10);
const total = ref<number>(0);
const pageSizes = ref<number[]>([5, 10, 20, 50]);

/** 地图实例 */
let map: MapLibreMap | null = null;

// ============ 生命周期 ============

/**
 * @function onMounted
 * @description 组件挂载时自动查询悬挂线
 */
onMounted(async () => {
  // 获取地图实例
  try {
    map = AppMaplibre.getMap();
  } catch (error) {
    console.warn("获取地图实例失败:", error);
  }

  // 自动查询悬挂线
  await handleRefresh();
});

/**
 * @function onUnmounted
 * @description 组件卸载时清理悬挂线高亮图层
 */
onUnmounted(() => {
  clearHangingLineHighlight();
});

// ============ 图层管理方法 ============

/**
 * @function clearHangingLineHighlight
 * @description 清除悬挂线高亮图层
 */
const clearHangingLineHighlight = (): void => {
  try {
    if (!map) return;

    // 先删除图层，再删除数据源
    if (map.getLayer(HANGING_LINE_LAYER_IDS.LAYER)) {
      map.removeLayer(HANGING_LINE_LAYER_IDS.LAYER);
      console.log("已删除悬挂线高亮图层");
    }

    if (map.getSource(HANGING_LINE_LAYER_IDS.SOURCE)) {
      map.removeSource(HANGING_LINE_LAYER_IDS.SOURCE);
      console.log("已删除悬挂线高亮数据源");
    }
  } catch (error) {
    console.error("清除悬挂线高亮图层失败:", error);
  }
};

/**
 * @function addHangingLineHighlight
 * @description 添加悬挂线高亮图层
 * @param geojsonData 悬挂线几何数据
 */
const addHangingLineHighlight = (geojsonData: any): void => {
  try {
    if (!map) {
      console.warn("地图实例不可用，无法添加高亮图层");
      return;
    }

    // 先清除已有的高亮图层
    clearHangingLineHighlight();

    // 准备GeoJSON特征
    const feature = {
      type: "Feature" as const,
      geometry: geojsonData,
      properties: {},
    };

    // 添加数据源
    map.addSource(HANGING_LINE_LAYER_IDS.SOURCE, {
      type: "geojson",
      data: {
        type: "FeatureCollection",
        features: [feature],
      },
    });

    // 添加高亮图层
    map.addLayer({
      id: HANGING_LINE_LAYER_IDS.LAYER,
      type: "line",
      source: HANGING_LINE_LAYER_IDS.SOURCE,
      layout: {
        "line-join": "round",
        "line-cap": "round",
      },
      paint: {
        "line-color": "#ff4d4f",
        "line-width": 6,
        "line-opacity": 0.8,
      },
    });

    console.log("已添加悬挂线高亮图层");
  } catch (error) {
    console.error("添加悬挂线高亮图层失败:", error);
  }
};

// ============ 分页相关方法 ============

/**
 * @function getTableIndex
 * @description 获取表格序号（考虑分页）
 * @param index 当前页内索引
 * @returns 全局索引
 */
const getTableIndex = (index: number): number => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};

/**
 * @function handleCurrentChange
 * @description 处理页码变化
 * @param page 新页码
 */
const handleCurrentChange = (page: number): void => {
  currentPage.value = page;
  handleRefresh();
};

/**
 * @function handleSizeChange
 * @description 处理每页条数变化
 * @param size 新的每页条数
 */
const handleSizeChange = (size: number): void => {
  pageSize.value = size;
  currentPage.value = 1; // 重置到第一页
  handleRefresh();
};

// ============ 事件处理方法 ============

/**
 * @function closeCard
 * @description 处理关闭面板
 */
const closeCard = (): void => {
  try {
    console.log("关闭悬挂线检查面板");
    clearHangingLineHighlight();
    emit("close");
    ElMessage.info("已关闭悬挂线检查功能");
  } catch (error) {
    console.error("关闭面板失败:", error);
    ElMessage.error("关闭面板失败");
  }
};

/**
 * @function handleRefresh
 * @description 刷新查询悬挂线数据（分页查询）
 */
const handleRefresh = async (): Promise<void> => {
  try {
    loading.value = true;
    console.log("开始查询悬挂线数据...");

    // 清除之前的高亮图层
    clearHangingLineHighlight();

    // 构建分页查询参数
    const params: HangingLinePageQuery = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      // orderByColumn: "gxbm",
      // isAsc: "asc",
    };

    const response = await queryHangingLines(params);

    if (response.code === 200 && response.data) {
      const pageInfo = response.data;
      hangingLines.value = pageInfo.list || [];
      total.value = pageInfo.totalCount || 0;

      console.log("查询悬挂线成功:", pageInfo);

      if (total.value === 0) {
        ElMessage.success("太好了！没有发现悬挂线");
      } else {
        ElMessage.warning(`发现 ${total.value} 条悬挂线需要处理`);
      }
    } else {
      console.error("查询悬挂线失败:", response.msg);
      ElMessage.error(`查询悬挂线失败: ${response.msg}`);
      hangingLines.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error("查询悬挂线异常:", error);
    ElMessage.error(
      `查询悬挂线异常: ${error instanceof Error ? error.message : "未知错误"}`
    );
    hangingLines.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

/**
 * @function handleLocate
 * @description 定位到悬挂线并添加高亮显示
 * @param hangingLine 悬挂线数据
 */
const handleLocate = (hangingLine: GsLnVo): void => {
  try {
    console.log("定位悬挂线:", hangingLine);

    if (!map) {
      ElMessage.error("地图实例不可用，无法定位");
      return;
    }

    // 解析geojson数据
    let geometry;
    if (hangingLine.geojson) {
      try {
        const geojsonData = JSON.parse(hangingLine.geojson);
        geometry = geojsonData.geometry || geojsonData;
      } catch (parseError) {
        console.error("解析geojson失败:", parseError);
        ElMessage.error("管线位置数据格式错误");
        return;
      }
    }

    if (!geometry || !geometry.coordinates) {
      ElMessage.error("管线位置数据不完整，无法定位");
      return;
    }

    // 添加高亮图层
    addHangingLineHighlight(geometry);

    // 计算几何中心并缩放到适当级别
    let bounds;
    if (geometry.type === "LineString") {
      bounds = calculateLineStringBounds(geometry.coordinates);
    } else if (geometry.type === "MultiLineString") {
      bounds = calculateMultiLineStringBounds(geometry.coordinates);
    } else {
      ElMessage.error("不支持的几何类型");
      return;
    }

    if (bounds) {
      // 添加一些边距
      const padding = 0.001; // 约100米的边距
      const extendedBounds = [
        [bounds[0] - padding, bounds[1] - padding],
        [bounds[2] + padding, bounds[3] + padding],
      ];

      map.fitBounds(extendedBounds as [[number, number], [number, number]], {
        padding: 50,
        duration: 1000,
      });

      ElMessage.success(`已定位到管线：${hangingLine.gxbm || "未知编码"}`);

      // 触发定位事件
      emit("locate", hangingLine);
    }
  } catch (error) {
    console.error("定位悬挂线失败:", error);
    ElMessage.error(
      `定位失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};

/**
 * @function calculateLineStringBounds
 * @description 计算LineString的边界框
 * @param coordinates 坐标数组
 * @returns 边界框 [minLng, minLat, maxLng, maxLat]
 */
const calculateLineStringBounds = (coordinates: number[][]): number[] => {
  let minLng = Infinity,
    minLat = Infinity;
  let maxLng = -Infinity,
    maxLat = -Infinity;

  coordinates.forEach(([lng, lat]) => {
    minLng = Math.min(minLng, lng);
    minLat = Math.min(minLat, lat);
    maxLng = Math.max(maxLng, lng);
    maxLat = Math.max(maxLat, lat);
  });

  return [minLng, minLat, maxLng, maxLat];
};

/**
 * @function calculateMultiLineStringBounds
 * @description 计算MultiLineString的边界框
 * @param coordinatesArray 多个坐标数组
 * @returns 边界框 [minLng, minLat, maxLng, maxLat]
 */
const calculateMultiLineStringBounds = (
  coordinatesArray: number[][][]
): number[] => {
  let minLng = Infinity,
    minLat = Infinity;
  let maxLng = -Infinity,
    maxLat = -Infinity;

  coordinatesArray.forEach((coordinates) => {
    const bounds = calculateLineStringBounds(coordinates);
    minLng = Math.min(minLng, bounds[0]);
    minLat = Math.min(minLat, bounds[1]);
    maxLng = Math.max(maxLng, bounds[2]);
    maxLat = Math.max(maxLat, bounds[3]);
  });

  return [minLng, minLat, maxLng, maxLat];
};

/**
 * @function handleDelete
 * @description 删除单条悬挂线
 * @param hangingLine 悬挂线数据
 * @param index 索引
 */
const handleDelete = async (
  hangingLine: GsLnVo,
  index: number
): Promise<void> => {
  try {
    if (!hangingLine.gid) {
      ElMessage.error("管线ID不存在，无法删除");
      return;
    }

    console.log("删除悬挂线:", hangingLine);

    const response = await pipeLineDelete(hangingLine.gid);

    if (response.code === 200) {
      // 清除高亮图层（如果当前删除的正是被高亮的管线）
      clearHangingLineHighlight();
      ElMessage.success(`悬挂线 ${hangingLine.gxbm || "未知编码"} 删除成功`);

      // 刷新当前页数据
      await handleRefresh();
      // 刷新图层
      handleUpdatePipeLayer();
    } else {
      console.error("删除悬挂线失败:", response.msg);
      ElMessage.error(`删除失败: ${response.msg}`);
    }
  } catch (error) {
    console.error("删除悬挂线异常:", error);
    ElMessage.error(
      `删除异常: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};
/**
 * @function handleUpdatePipeLayer
 * @description 处理更新管网图层
 */
const handleUpdatePipeLayer = (): void => {
  console.log("通知更新管网图层（矢量瓦片将自动同步）");
  // 在新架构中，矢量瓦片会自动同步数据库变更，无需手动刷新
  try {
    const layerManager = AppMaplibre.getLayerManager();
    if (layerManager && layerManager.refreshMvt) {
      layerManager.refreshMvt();
    } else {
      console.warn("LayerManager或refreshMvt方法不可用");
    }
  } catch (error) {
    console.error("刷新MVT图层失败:", error);
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
</script>

<style scoped lang="scss">
.hanging-line-check-panel {
  position: absolute;
  left: 10px;
  top: 150px; // 避免与主控制面板重叠
  width: 724px; // 调整宽度适应移除长度列后的表格
  max-height: 85vh; // 增加最大高度以容纳分页组件
  z-index: 999;
}

// 统计信息区域
.stats-section {
  margin-bottom: 16px;

  .el-alert {
    border-radius: 6px;
  }
}

// 按钮区域样式
.button-section {
  margin-bottom: 16px;
  gap: 12px;

  .select-btn,
  .clear-btn {
    min-width: 120px;
    border-radius: 6px;
  }
}

// 表格区域样式
.table-section {
  margin-bottom: 16px;

  .table-label {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
    display: block;
  }

  :deep(.el-table) {
    border-radius: 6px;
    overflow: hidden;

    .el-table__header {
      background: #f8f9fa;
    }

    .el-button {
      margin-right: 4px;
      border-radius: 4px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  // 分页组件样式
  // .pagination {
  //   margin-top: 15px;
  //   justify-content: flex-end;
  //   display: flex;

  //   :deep(.el-pagination) {
  //     .el-pagination__total {
  //       color: #606266;
  //       font-size: 13px;
  //     }

  //     .el-pager li {
  //       min-width: 28px;
  //       height: 28px;
  //       line-height: 28px;
  //       font-size: 13px;
  //     }

  //     .btn-prev,
  //     .btn-next {
  //       width: 28px;
  //       height: 28px;
  //       font-size: 13px;
  //     }
  //   }
  // }
}

// 响应式设计
@media (max-width: 1200px) {
  .hanging-line-check-panel {
    left: 10px;
    width: 90vw;
    max-width: 750px;
  }
}
</style>
