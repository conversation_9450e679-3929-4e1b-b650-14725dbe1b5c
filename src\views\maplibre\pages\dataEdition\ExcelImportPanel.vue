<!--
 * @Description: Excel导入面板
 * @Date: 2024-01-16
 * @Author: AI Assistant
 * @LastEditTime: 2024-01-16
 -->
<template>
  <page-card
    class="excel-import-panel"
    title="Excel数据导入"
    @closeCard="closeCard"
  >
    <!-- 操作工具栏 -->
    <div class="group" grid="~ cols-2 gap-x-4">
      <div
        v-for="item in importList"
        :key="item.value"
        class="btn flex-c cursor-pointer"
        @click="handleImport(item.value)"
        :class="item.value === importType ? 'active' : ''"
      >
        <div>
          <img
            :src="
              item.value === importType
                ? getImages(item.icon + '-ac.png')
                : getImages(item.icon + '.png')
            "
            alt=""
            srcset=""
          />
        </div>
        <div class="ml-2.5">{{ item.name }}</div>
      </div>
      <!-- <el-radio-group v-model="importType" size="default" class="import-type-radio">
        <el-radio-button label="node">管点导入</el-radio-button>
        <el-radio-button label="line">管线导入</el-radio-button>0
      </el-radio-group> -->
    </div>
    <div class="toolbar">
      <el-button
        type="primary"
        :icon="Download"
        @click="handleDownloadTemplate"
        size="default"
      >
        下载模板
      </el-button>
      <el-button
        type="success"
        :icon="Upload"
        @click="handleImportData"
        :loading="isImporting"
        :disabled="!hasValidFile || isImporting"
        size="default"
      >
        导入
      </el-button>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-section">
      <el-upload
        class="excel-upload"
        drag
        :auto-upload="false"
        :show-file-list="false"
        accept=".xls,.xlsx"
        :on-change="handleFileChange"
        :before-upload="() => false"
      >
        <el-icon class="el-icon--upload">
          <FolderOpened />
        </el-icon>
        <div class="el-upload__text">
          拖拽或点击选择Excel文件<br />
          <span class="upload-hint">(仅支持.xls, .xlsx格式)</span>
        </div>
      </el-upload>

      <!-- 简化的文件状态 -->
      <div v-if="selectedFile" class="file-status">
        <el-text :type="hasValidFile ? 'success' : 'warning'" size="small">
          {{ statusMessage }}
        </el-text>
      </div>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElMessage, ElButton, ElUpload, ElIcon, ElText } from "element-plus";
import { Download, Upload, FolderOpened } from "@element-plus/icons-vue";
import PageCard from "@/components/PageCard.vue";
import ImportFileApi, { ImportFileUtils } from "@/api/importFile";
import { downloadFile } from "@/utils/file";
import type { UploadFile } from "element-plus";
import { getImages } from "@/utils/getImages";
/**
 * @interface Emits
 * @description 组件事件接口
 */
interface Emits {
  (e: "close"): void;
}

const emit = defineEmits<Emits>();

// ============ 响应式状态 ============

/** 导入状态 */
const isImporting = ref<boolean>(false);

/** 选中的文件列表 */
const selectedFile = ref<File | null>(null);

/** 导入类型 */
const importType = ref<"node" | "line">("node");
const importList = ref([
  {
    name: "管点导入",
    value: "node",
    icon: "basemap/point",
  },
  {
    name: "管线导入",
    value: "line",
    icon: "basemap/pipeline",
  },
]);
// ============ 计算属性 ============

/** 是否有有效文件 */
const hasValidFile = computed<boolean>(() => {
  if (!selectedFile.value) return false;
  return ImportFileUtils.validateExcelFile(selectedFile.value).valid;
});

/** 状态消息 */
const statusMessage = computed<string>(() => {
  if (!selectedFile.value) return "尚未选择文件";
  const { valid, message } = ImportFileUtils.validateExcelFile(
    selectedFile.value
  );
  return valid ? `✓ 文件: ${selectedFile.value.name}` : `⚠ ${message}`;
});

// ============ 方法定义 ============

/**
 * @function handleFileChange
 * @description 处理文件选择变化
 */
const handleFileChange = (uploadFile: UploadFile): void => {
  selectedFile.value = uploadFile.raw || null;
};

/**
 * @function downloadTemplate
 * @description 处理模板文件下载
 */
const downloadTemplate = async (
  apiCall: () => Promise<any>,
  filename: string
): Promise<void> => {
  try {
    const res = await apiCall();
    downloadFile(res.data, filename);
    ElMessage.success(`${filename} 下载成功`);
  } catch (error) {
    console.error(`${filename} 下载失败:`, error);
    ElMessage.error(`${filename} 下载失败，请稍后重试`);
  }
};

/**
 * @function importData
 * @description 处理数据导入
 */
const importData = async (
  apiCall: (file: File) => Promise<any>,
  type: "管点" | "管线"
): Promise<void> => {
  if (!hasValidFile.value || !selectedFile.value) {
    ElMessage.warning(`请先选择一个有效的Excel文件再导入${type}数据`);
    return;
  }
  isImporting.value = true;
  try {
    const response = await apiCall(selectedFile.value);
    if (response.code === 200) {
      ElMessage.success(`${type}数据导入成功`);
      selectedFile.value = null; // 导入成功后清空
    } else {
      throw new Error(response.msg || "导入失败");
    }
  } catch (error: any) {
    console.error(`${type}数据导入失败:`, error);
    ElMessage.error(`导入失败: ${error.message || "未知错误"}`);
  } finally {
    isImporting.value = false;
  }
};

/**
 * @function closeCard
 * @description 处理关闭面板
 */
const closeCard = (): void => {
  try {
    console.log("关闭Excel导入面板");
    emit("close");
    ElMessage.info("已关闭Excel导入功能");
  } catch (error) {
    console.error("关闭面板失败:", error);
    ElMessage.error("关闭面板失败");
  }
};

// 根据当前模式动态下载模板
const handleDownloadTemplate = () => {
  if (importType.value === "node") {
    downloadTemplate(ImportFileApi.exportPipeNodeTemplate, "管点导入模板.xlsx");
  } else {
    downloadTemplate(ImportFileApi.exportPipeLineTemplate, "管线导入模板.xlsx");
  }
};
const handleImport = (value: string) => {
  importType.value = value;
};
// 根据当前模式动态导入数据
const handleImportData = () => {
  if (importType.value === "node") {
    importData(ImportFileApi.importPipeNodeExcel, "管点");
  } else {
    importData(ImportFileApi.importPipeLineExcel, "管线");
  }
};
</script>

<style scoped lang="scss">
.excel-import-panel {
  position: absolute;
  left: 10px;
  top: 150px;
  width: 500px;
  max-height: 85vh;
  z-index: 999;
}
.group {
  background: #f4f7ff;
  padding: 10px;
  box-sizing: border-box;
  margin-bottom: 20px;
  // display: flex;
}
.btn {
  background: #fff;
  border-radius: 2px;
  color: #2c3037;
  padding: 5px 0;
}
.active {
  border: 1px solid #1966ff;
  color: #1966ff;
}
// ============ 工具栏样式 ============
.toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;

  .el-button {
    flex-grow: 1;
    min-width: 45%;
  }
}

// ============ 上传区域样式 ============
.upload-section {
  .excel-upload {
    width: 100%;

    :deep(.el-upload-dragger) {
      width: 99%;
      height: 150px;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      transition: border-color 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-bottom: 5px;
      &:hover {
        border-color: #409eff;
      }
    }

    .el-icon--upload {
      font-size: 48px;
      color: #c0c4cc;
      margin: 20px 0 12px;
    }

    .el-upload__text {
      color: #606266;
      font-size: 14px;
      text-align: center;
      line-height: 1.5;

      .upload-hint {
        display: block;
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }
  }

  // 文件状态样式
  .file-status {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    text-align: center;
  }
}

// ============ 响应式样式 ============
@media (max-width: 768px) {
  .excel-import-panel {
    width: 90vw;
    left: 5vw;
  }

  .toolbar {
    flex-direction: column;
  }
}
</style>
