/*
 * @Author: xiao
 * @Date: 2024-09-04 14:32:39
 * @LastEditors: xiao
 * @LastEditTime: 2024-12-11 18:09:13
 * @Description:
 */
// import { StateColors } from './constant'
/**
 * 判断日期是否禁用
 *
 * @param time 日期对象
 * @returns 如果传入日期大于当前日期，则返回 true，否则返回 false
 */
export const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

/**
 * @description: 字典数据需要查一遍的
 * @param {BaseOption} constant 数据
 * @param {any} val 后端返回值
 * @param {*} k 键名
 * @return {*}  值
 */
export const useConstValue = (constant: BaseOption[], val: any, k: 'label' | 'name' = 'label') => {
  const item = constant.find((item) => item.value == val)
  console.log(item ? item[k] : '无');
  return item ? item[k] : '无'
}

/**
 * 设置状态颜色
 *
 * @param val 状态值对应的label
 * @description 以下数组可自行添加对应label，只要颜色符合即可
 * @returns 返回状态对应的颜色值
 */
// export const setStateColor = (val: string) => {
//   if (!val || val === '') return val
//   if (['在线'].includes(val)) return StateColors.success
//   if (['停用'].includes(val)) return StateColors.error
//   if (['故障'].includes(val)) return StateColors.warning
//   if (['离线'].includes(val)) return StateColors.info
//   if (['已拆除,预警'].includes(val)) return StateColors.done
// }

// 文件大小单位转换
export const formatFileSize = (bytes: number | string | undefined) => {
  if (!bytes) return bytes
  let myBytes = typeof bytes === 'number' ? bytes : Number(bytes)
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let unitIndex = 0

  const baseNum = 1024
  while (myBytes >= baseNum && unitIndex < units.length - 1) {
    myBytes /= baseNum
    unitIndex++
  }

  const formatted = myBytes.toFixed(2).replace(/\.?0+$/, '') // 去掉末尾的0和小数点

  return `${formatted} ${units[unitIndex]}`
}
