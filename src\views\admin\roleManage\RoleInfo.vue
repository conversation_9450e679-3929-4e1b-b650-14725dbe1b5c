<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    class="admin-sub-form"
    label-width="auto"
    :disabled="editable"
  >
    <el-form-item label="角色名称" prop="name">
      <el-input
        class=""
        v-model.trim="formData.name"
        placeholder="请输入角色名称"
      />
    </el-form-item>
    <el-form-item label="角色编码" prop="roleKey">
      <el-input
        class=""
        v-model.trim="formData.roleKey"
        placeholder="请输入角色编码"
      />
    </el-form-item>
    <el-form-item label="角色排序" prop="orderNum">
      <el-input-number
        v-model="formData.orderNum"
        class="-number"
        :min="1"
        :max="100"
        controls-position="right"
      />
    </el-form-item>
    <el-form-item label="角色状态" prop="status">
      <el-switch
        v-model="formData.status"
        class="custom-form-switch"
        size="large"
        inline-prompt
        active-text="启用"
        :active-value="'1'"
        inactive-text="禁用"
        :inactive-value="'0'"
      />
    </el-form-item>
    <el-form-item label="角色权限" prop="menuIds">
      <div class="custom-div">
        <el-tree
          ref="treeRef"
          :data="roleTree"
          @check="check"
          :default-checked-keys="formData.menuIds"
          show-checkbox
          node-key="id"
          highlight-current
          :props="defaultProps"
          :check-strictly="true"
          :expand-on-click-node="false"
        />
      </div>
    </el-form-item>
    <el-form-item label="角色描述" prop="remark">
      <el-input
        class="custom-textarea"
        placeholder="请输入角色描述"
        v-model.trim="formData.remark"
        type="textarea"
        resize="none"
        :rows="3"
        maxlength="500"
      ></el-input>
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import { sourceState } from "@/utils/constant";
import { menuTree } from "@/api/menu";
const props = defineProps({
  formData: Object,
  editable: Boolean,
});
let formData: any = ref(props.formData);
let editable = ref(props.editable);
watch(
  () => props.formData,
  (val) => {
    treeRef.value.setCheckedKeys([]);
    resetForm();
    formData.value = val;
  }
);
watch(
  () => props.editable,
  (val) => {
    editable.value = val;
  }
);
const defaultProps = {
  children: "children",
  label: "label",
  value: "id",
};
const checkCif = (rule: any, value: any, callback: any) => {
  const regMobile = /^[a-zA-Z][a-zA-Z_-]{1,19}$/;
  if (regMobile.test(value)) {
    return callback();
  }
  // 返回一个错误提示
  callback(new Error("包含大小写字母、下划线“_”、连接符“-”，且必须以字母开头"));
};
const rules = {
  name: [
    { required: true, message: "请输入角色名称", trigger: "blur" },
    { min: 2, message: "长度不低于2个字符", trigger: "blur" },
    { max: 20, message: "长度不超过20个字符", trigger: "blur" },
  ],
  status: [{ required: true, message: "请选择角色状态", trigger: "change" }],
  roleKey: [
    { required: true, message: "请输入角色编码", trigger: "blur" },
    { validator: checkCif, trigger: "blur" },
  ],
  orderNum: [{ required: true, message: "请输入排序", trigger: "blur" }],
};
const treeRef = ref();
const roleTree = ref([]);
let form: any = ref(null);
const check = (checkedNodes: any, { checkedKeys }: any) => {
  formData.value.menuIds = checkedKeys;
};
const getMenuTree = async () => {
  const result = await menuTree({ status: "1" });
  roleTree.value = result.data;
};
const submitForm = async (): Promise<any> => {
  return (await form.value?.validate()) ? formData.value : null;
};
const handleCascadeChange = async () => {
  treeRef.value.setCheckedKeys(formData.value.menuIds);
};
const resetForm = () => {
  form.value?.resetFields();
};
onMounted(async () => {
  resetForm();
  await getMenuTree();
});
defineExpose({
  submitForm,
  resetForm,
});
</script>
<style scoped lang="scss">
.custom-div {
  border: 1px solid #ebebeb;
  border-radius: 5px;
  min-height: 80px;
  max-height: 230px;
  overflow-y: auto;
  width: 100%;
}
.custom-div::-webkit-scrollbar {
  width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.custom-div::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background: #ebebeb;
}
</style>
