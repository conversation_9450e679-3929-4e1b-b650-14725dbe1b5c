import { queryMenus } from '@/api/menu';
import localCatch from '@/utils/auth'
export const useMenuStore = defineStore('menuStore', () => {
  const menu = ref(localCatch.getCache('menu') || [])

  const getMenu = async () => {
    try {
      const res = await queryMenus();
      menu.value = res.data;
      localCatch.setCache('menu', res.data || [])
    } catch (error) {
      console.error(error);
    }
  };

  return {
    menu,
    getMenu
  };
});
