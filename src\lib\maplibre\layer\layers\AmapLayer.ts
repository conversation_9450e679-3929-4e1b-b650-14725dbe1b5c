/**
 * @fileoverview 高德地图图层实现
 * @description 支持高德地图瓦片服务，包含多种地图样式、标注控制和坐标转换
 * <AUTHOR>
 * @version 2.0.0
 */

import { BaseLayer } from './BaseLayer'
import { Map as glMap } from 'maplibre-gl'
import { 
  LayerType, 
  CoordinateSystem,
  type AmapLayerConfig, 
  type LayerLoadOptions 
} from '../types/LayerTypes'

/**
 * @class AmapLayer
 * @description 高德地图图层类，支持高德地图的各种样式和坐标转换
 * @extends BaseLayer
 */
export class AmapLayer extends BaseLayer {
  private _style: string = 'normal'
  private _showLabel: boolean = true
  private _showRoad: boolean = true
  private _lang: string = 'zh_cn'
  private _scale: number = 1
  private _subdomains: string[] = ['01', '02', '03', '04']

  /**
   * @constructor
   * @param options - 高德地图图层配置
   */
  constructor(options: AmapLayerConfig) {
    super(options)
    this._style = options.style || 'normal'
    this._showLabel = options.showLabel !== false
    this._showRoad = options.showRoad !== false
    this._lang = options.lang || 'zh_cn'
    this._scale = options.scale || 1
    this._subdomains = options.subdomains || ['01', '02', '03', '04']
  }

  /**
   * @description 图层内部添加实现
   * @param map - MapLibre地图实例
   * @param isRefresh - 是否为刷新操作
   * @param options - 加载选项
   * @returns 图层代理对象
   */
  async addToInternal(map: glMap, isRefresh?: boolean, options?: LayerLoadOptions): Promise<any> {
    try {
      // const startTime = performance.now()
      if(this._style === 'normal') {
        map.addLayer((window as any).rasterTileLayer(this.id, 'GaoDe.Normal.Map'))
      } else if(this._style === 'satellite') {
        map.addLayer((window as any).rasterTileLayer(this.id, 'GaoDe.Satellite.Map'))
        map.addLayer((window as any).rasterTileLayer(this.id + '_anno', 'GaoDe.Satellite.Annotion'))
      }
      const delegate = map.getLayer(this.id)
      
      console.log(map.getLayersOrder())      
      return delegate
    } catch (error) {
      this._performanceMetrics.errorCount = (this._performanceMetrics.errorCount || 0) + 1
      console.error(`高德地图图层 ${this._name} 添加失败:`, error)
      throw error
    }
  }

  
  

  

  

  /**
   * @description 移除图层
   */
  removeInternal(): void {
    if (this._map) {
      try {

        if (this._map.getLayer(this.id + '_anno')) {
          this._map.removeLayer(this.id + '_anno')
        }

        if (this._map.getLayer(this.id)) {
          this._map.removeLayer(this.id)
        }
      } catch (error) {
        console.error(`移除高德地图图层 ${this._name} 失败:`, error)
      }
    }
  }

  

  

  
  



  

  
} 