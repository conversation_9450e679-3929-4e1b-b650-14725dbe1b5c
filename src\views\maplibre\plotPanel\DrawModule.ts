/**
 * @fileoverview 标绘模块核心�?- 优化�?
 * @description 负责管理标绘功能的所有核心逻辑，集成资源管理器提供更好的内存管�?
 * <AUTHOR>
 * @version 2.0.0
 */

import { BehaviorSubject } from "rxjs";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { PlotDataManager } from "@/lib/maplibre/layer/PlotDataManager";
import { FeatureLayer } from "@/lib/maplibre/layer/layers/FeatureLayer";
import { ResourceManager, ResourceType, ResourceStatus } from "@/lib/maplibre/layer/ResourceManager";
import type { DrawManager } from "@/lib/maplibre/draw/DrawManager";
import { DrawEventType } from "@/lib/maplibre/draw/types";
import {
  LayerType,
  LayerGroupType,
  LayerPriority,
  DEFAULT_FEATURE_STYLE,
  type PlotFeature,
  type FeatureLayerConfig,
} from "@/lib/maplibre/layer/types/LayerTypes";
import { Subject, type Subscription } from 'rxjs';
import { EngineType } from "@/types/plotting";

/**
 * @description 绘制面板状态数�?
 */
export class DrawBar {
  constructor(
    public show: boolean = false,
    public type: string = "Point",
    public title: string = "标绘新增",
    public isContainPushRange: boolean = false,
    public editFeature?: PlotFeature // 新增：编辑要素数�?
  ) {}
}

/**
 * @description 绘制要素数据
 */
export class DrawData {
  constructor(
    public feature: PlotFeature | null = null,
    public isEditing: boolean = false,
    public geometry: any = null
  ) {}
}

/**
 * @description 绘制状态枚�?
 */
export enum DrawStatus {
  IDLE = 'idle',           // 空闲状�?
  DRAWING = 'drawing',     // 正在绘制
  EDITING = 'editing',     // 正在编辑
  SAVING = 'saving',       // 正在保存
  ERROR = 'error'          // 错误状�?
}

/**
 * @description 绘制事件类型
 */
export enum PlotEventType {
  DRAW_START = 'draw_start',
  DRAW_FINISH = 'draw_finish',
  DRAW_CANCEL = 'draw_cancel',
  EDIT_START = 'edit_start',
  EDIT_FINISH = 'edit_finish',
  FEATURE_SAVE = 'feature_save',
  FEATURE_DELETE = 'feature_delete',
  STATUS_CHANGE = 'status_change',
  FEATURE_UPDATE = 'feature_update'
}

/**
 * @description 标绘绘制模块 - 核心管理类（优化版）
 */
export class DrawModule {
  // 静态实�?
  private static instance: DrawModule | null = null;

  // 状态管�?
  static showChange: BehaviorSubject<DrawBar> = new BehaviorSubject(new DrawBar());
  static dataChange: BehaviorSubject<DrawData> = new BehaviorSubject(new DrawData());
  static statusChange: BehaviorSubject<DrawStatus> = new BehaviorSubject<DrawStatus>(DrawStatus.IDLE);

  // === 新增：资源管理器 ===
  private resourceManager: ResourceManager;

  // 核心管理�?
  private drawManager: DrawManager | null = null;
  private plotDataManager: PlotDataManager | null = null;
  private featureLayer: FeatureLayer | null = null;

  // 当前状�?
  private currentStatus: DrawStatus = DrawStatus.IDLE;
  private currentFeature: PlotFeature | null = null;
  private originalFeature: PlotFeature | null = null; // 编辑时备份的原要�?
  private isInitialized: boolean = false;

  // === 新增：错误恢复计数器 ===
  private retryCount: number = 0;
  private maxRetries: number = 3;

  // === 新增：定时器管理 ===
  private layerCheckInterval: number | null = null;
  private layerCheckTimeout: number | null = null;

  /**
   * @description 获取单例实例
   */
  static getInstance(): DrawModule {
    if (!DrawModule.instance) {
      DrawModule.instance = new DrawModule();
    }
    return DrawModule.instance;
  }

  /**
   * @description 私有构造函�?
   */
  private constructor() {
    // 初始化资源管理器
    this.resourceManager = ResourceManager.getInstance();
    
    // 注册自身到资源管理器
    this.resourceManager.registerResource(
      ResourceType.DRAW_MODULE,
      this,
      ['destroy']
    );
  }

  /**
   * @description 初始化绘制模�?
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      this.resourceManager.updateResourceStatus(ResourceType.DRAW_MODULE, ResourceStatus.INITIALIZING);

      // 初始化核心管理器（地图已经完全加载）
      await this.initializeManagers();

      // 设置事件监听
      this.setupEventListeners();

      // 创建要素图层
      const isAnalysisOnly = useAnalysisModeStore().isAnalysisOnly;
      if(!isAnalysisOnly) {
        await this.createFeatureLayer();
      }
      

      this.isInitialized = true;
      this.retryCount = 0; // 重置重试计数
      this.resourceManager.updateResourceStatus(ResourceType.DRAW_MODULE, ResourceStatus.READY);
      return true;
    } catch (error) {
      console.error('DrawModule 初始化失�?', error);
      this.resourceManager.updateResourceStatus(ResourceType.DRAW_MODULE, ResourceStatus.ERROR);
      this.setStatus(DrawStatus.ERROR);
      
      // 尝试错误恢复
      return this.handleInitializationError(error);
    }
  }

  /**
   * @description 处理初始化错�?
   * @param error - 错误信息
   * @returns 是否恢复成功
   * @private
   */
  private async handleInitializationError(error: any): Promise<boolean> {
    if (this.retryCount >= this.maxRetries) {
      console.error('DrawModule 初始化重试次数已达上限，停止重试');
      return false;
    }

    this.retryCount++;
    // 等待一段时间后重试
    await new Promise(resolve => setTimeout(resolve, 1000 * this.retryCount));

    try {
      // 清理可能的残留状�?
      await this.cleanup();
      
      // 重新初始�?
      this.isInitialized = false;
      return await this.initialize();
    } catch (retryError) {
      console.error(`�?${this.retryCount} 次恢复失�?`, retryError);
      return this.handleInitializationError(retryError);
    }
  }

  /**
   * @description 清理方法
   * @private
   */
  private async cleanup(): Promise<void> {
    try {
      // 清理事件监听�?
      this.cleanupEventListeners();
      
      // 重置状�?
      this.currentFeature = null;
      this.currentStatus = DrawStatus.IDLE;
      
      // 清理管理器引�?
      this.drawManager = null;
      this.plotDataManager = null;
      this.featureLayer = null;
    } catch (error) {
      console.error('清理 DrawModule 状态失�?', error);
    }
  }

  /**
   * @description 清理事件监听�?
   * @private
   */
  private cleanupEventListeners(): void {
    if (this.drawManager) {
      try {
        this.drawManager.removeEventListener(DrawEventType.DRAW_FINISH, this.handleDrawFinish);
        this.drawManager.removeEventListener(DrawEventType.DRAW_START, this.handleDrawStart);
        this.drawManager.removeEventListener(DrawEventType.DRAW_CANCEL, this.handleDrawCancel);
        this.drawManager.removeEventListener(DrawEventType.FEATURE_UPDATE, this.handleFeatureUpdate);
      } catch (error) {
        console.error('清理事件监听器失�?', error);
      }
    }
  }

  /**
   * @description 初始化核心管理器（优化版�?
   */
  private async initializeManagers(): Promise<void> {
    // 获取地图实例
    const map = AppMaplibre.getMap();
    if (!map) {
      throw new Error('地图未初始化，无法初始化 DrawModule');
    }

    // 初始化绘制管理器
    this.drawManager = AppMaplibre.getDrawTool();
    if (!this.drawManager) {
      throw new Error('DrawManager 未初始化');
    }

    // 注册绘制管理器到资源管理�?
    this.resourceManager.registerResource(
      ResourceType.DRAW_MANAGER,
      this.drawManager,
      ['destroy']
    );

    // 初始化数据管理器
    this.plotDataManager = PlotDataManager.getInstance();
    if (!this.plotDataManager) {
      throw new Error('PlotDataManager 未初始化');
    }

    // 注册数据管理器到资源管理�?
    this.resourceManager.registerResource(
      ResourceType.DATA_MANAGER,
      this.plotDataManager,
      ['destroy']
    );


  }

  /**
   * @description 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.drawManager) return;

    // 监听绘制完成事件
    this.drawManager.addEventListener(DrawEventType.DRAW_FINISH, (event: any) => {
      this.handleDrawFinish(event);
    });

    // 监听绘制开始事�?
    this.drawManager.addEventListener(DrawEventType.DRAW_START, () => {
      this.handleDrawStart();
    });

    // 监听绘制取消事件
    this.drawManager.addEventListener(DrawEventType.DRAW_CANCEL, () => {
      this.handleDrawCancel();
    });

    // 监听要素更新事件（编辑完成）
    this.drawManager.addEventListener(DrawEventType.FEATURE_UPDATE, (event: any) => {
      this.handleFeatureUpdate(event);
    });

    // === 新增：监听地图图层变化，检测底图切�?===
    this.setupMapLayerChangeListener();


  }

  /**
   * @description 设置地图图层变化监听�?
   * @description 用于检测底图切换等情况，自动重新调整标绘图层顺�?
   * @private
   */
  private setupMapLayerChangeListener(): void {
    const map = AppMaplibre.getMap();
    if (!map) return;

    let lastLayerCount = 0;
    let checkInterval: number | null = null;

    // 定期检查图层数量变化（检测底图切换）
    const checkLayerChanges = () => {
      try {
        // 检查地图实例是否仍然有效
        const currentMap = AppMaplibre.getMap();
        if (!currentMap || AppMaplibre.isDestroyed()) {
          console.log('地图实例已被清理，停止图层检查');
          this.clearLayerCheckTimers();
          return;
        }

        // 检查地图样式是否可用
        const mapStyle = currentMap.getStyle();
        if (!mapStyle || !mapStyle.layers) {
          console.warn('地图样式不可用，跳过本次图层检查');
          return;
        }

        const currentLayers = mapStyle.layers;
        const currentLayerCount = currentLayers.length;

        // 检查是否有基础图层和标绘图�?
        const hasBaseLayers = currentLayers.some(layer => 
          layer.id.includes('tdt-') || 
          layer.id.includes('amap-') || 
          layer.id.includes('baidu-')
        );
        
        const plotLayers = currentLayers.filter(layer => 
          layer.id.startsWith('plot-features-layer')
        );

        // 如果图层数量发生显著变化，且有基础图层存在，检查标绘图层顺�?
        if (Math.abs(currentLayerCount - lastLayerCount) > 0 && hasBaseLayers && plotLayers.length > 0) {
  
          
          // 延迟检查，确保图层完全加载
          setTimeout(() => {
            // 再次检查地图实例有效性
            if (!AppMaplibre.isDestroyed() && AppMaplibre.getMap()) {
              this.ensurePlotLayersOnTopInternal();
            }
          }, 300);
        }

        lastLayerCount = currentLayerCount;
      } catch (error) {
        console.error('检查图层变化失�?', error);
      }
    };

    // 启动定期检查（仅在初始化后的前30秒内，避免长期占用资源）
    this.layerCheckInterval = window.setInterval(checkLayerChanges, 1000);
    
    // 30秒后停止定期检�?
    this.layerCheckTimeout = window.setTimeout(() => {
      this.clearLayerCheckTimers();
      console.log('图层检查定时器已清理（30秒超时）');
    }, 30000);


  }

  /**
   * @description 内部的标绘图层顺序调整方�?
   * @private
   */
  private ensurePlotLayersOnTopInternal(): void {
    const map = AppMaplibre.getMap();
    if (!map) return;

    try {
  
      
      const plotLayerIds = [
        'plot-features-layer-point',
        'plot-features-layer-line',
        'plot-features-layer-polygon-fill',
        'plot-features-layer-polygon-stroke'
      ];
      
      const existingPlotLayers = plotLayerIds.filter(layerId => map.getLayer(layerId));
      
      if (existingPlotLayers.length === 0) {
        return;
      }
      
      // 获取当前图层顺序
      const allLayers = map.getStyle().layers || [];
      const plotLayerPositions = existingPlotLayers.map(layerId => {
        const index = allLayers.findIndex(layer => layer.id === layerId);
        return { layerId, position: index };
      });
      
      // 检查是否有标绘图层不在顶层
      const needsReordering = plotLayerPositions.some(({ position }) => 
        position < allLayers.length - existingPlotLayers.length
      );
      
      if (needsReordering) {
        
        existingPlotLayers.forEach(layerId => {
          try {
            map.moveLayer(layerId);

          } catch (error) {
            console.error(`�?移动标绘图层 ${layerId} 失败:`, error);
          }
        });
      } else {
        
      }
      
    } catch (error) {
      console.error('内部标绘图层顺序调整失败:', error);
    }
  }

  /**
   * @description 创建要素图层
   */
  private async createFeatureLayer(): Promise<void> {
    const map = AppMaplibre.getMap();
    if (!map || !this.plotDataManager) return;

    try {
  
      
      // 加载已有要素数据
      const existingFeatures = await this.plotDataManager.getAllFeatures();


      // 验证加载的要素数�?
      const validFeatures = existingFeatures.filter((feature: PlotFeature) => {
        if (!feature.geojson?.geometry) {
          console.warn(`要素 ${feature.id} 缺少几何数据，跳过加载`);
          return false;
        }
        return true;
      });



      const layerConfig: FeatureLayerConfig = {
        id: 'plot-features-layer',
        title: '标绘要素图层',
        type: LayerType.FEATURE,
        group: LayerGroupType.PLOT,
        priority: LayerPriority.PLOT,
        features: validFeatures,
        showLabels: true,
        editable: true,
      };
      
      // 创建要素图层
      this.featureLayer = new FeatureLayer(layerConfig);
      await this.featureLayer.addTo(map);


      
      // === 新增：强制调整图层顺�?===

      
      // 等待图层完全加载
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 获取所有标绘子图层ID
      const plotLayerIds = [
        'plot-features-layer-point',
        'plot-features-layer-line',
        'plot-features-layer-polygon-fill',
        'plot-features-layer-polygon-stroke'
      ];
      
      // 检查哪些图层实际存在于地图�?
      const existingPlotLayers = plotLayerIds.filter(layerId => map.getLayer(layerId));

      
      // 强制将每个标绘图层移动到最顶层
      existingPlotLayers.forEach(layerId => {
        try {
          map.moveLayer(layerId); // 移动到最顶层

        } catch (error) {
          console.error(`移动标绘图层 ${layerId} 失败:`, error);
        }
      });
      
      // 验证最终的图层顺序
      const finalLayers = map.getStyle().layers || [];

      finalLayers.forEach((layer, index) => {
        const isPlotLayer = layer.id.startsWith('plot-features-layer');

      });

      
      // 验证图层是否成功添加到地�?
      const layerExists = map.getLayer('plot-features-layer-point') || 
                         map.getLayer('plot-features-layer-line') || 
                         map.getLayer('plot-features-layer-polygon-fill');
      
      if (layerExists) {

      } else {
        console.warn('要素图层可能未成功添加到地图');
      }
      
    } catch (error) {
      console.error('创建要素图层失败:', error);
      throw error;
    }
  }

  /**
   * @description 开始绘制要�?
   * @param type - 要素类型
   * @param featureData - 预设要素数据
   * @param preserveFeature - 是否保持现有要素信息（用于编辑模式下的重绘）
   */
  async startDraw(type: string, featureData?: Partial<PlotFeature>, preserveFeature?: PlotFeature): Promise<void> {
    if (!this.drawManager) {
      throw new Error('DrawManager 未初始化');
    }

    try {
      let newFeature: PlotFeature;
      
      if (preserveFeature) {
        // 编辑模式下的重绘：保持原要素的ID和基本信息，只更新几何图�?

        const updatedStyle = { ...preserveFeature.geojson.properties.style, ...featureData?.geojson?.properties?.style };
        
        newFeature = {
          ...preserveFeature,
          geojson: {
            ...preserveFeature.geojson,
            geometry: { type: 'Point', coordinates: [0, 0] }, // 临时几何图形，绘制完成后更新
            properties: {
              ...preserveFeature.geojson.properties,
              style: updatedStyle
            }
          }
        };
      } else {
        // 新建模式：创建全新要素（使用新的GeoJSON格式�?
        const style = { ...DEFAULT_FEATURE_STYLE, ...featureData?.geojson?.properties?.style };
        
        const geojson: any = {
          type: 'Feature',
          geometry: null, // 几何图形将在绘制完成后更�?
          properties: {
            type: 'plot',
            geometryType: type,
            style: style
          }
        };

        newFeature = {
          id: -1, // 新建要素使用-1作为临时ID，保存后会更新为数据库ID
          name: featureData?.name || `${this.getFeatureTypeName(type)}_${new Date().toLocaleString()}`,
          remark: featureData?.remark || '',
          geojson: geojson,
          engineType: EngineType.MAPLIBRE
        };

      }

      this.currentFeature = newFeature;
      this.setStatus(DrawStatus.DRAWING);

      // 启动对应的绘制模�?
      switch (type) {
        case 'point':
          this.drawManager.setMode('point' as any);
          break;
        case 'linestring':
          this.drawManager.setMode('linestring' as any);
          break;
        case 'polygon':
          this.drawManager.setMode('polygon' as any);
          break;
        case 'rectangle':
          this.drawManager.setMode('rectangle' as any);
          break;
        default:
          throw new Error(`不支持的绘制类型: ${type}`);
      }

      // === 新增：确保绘制图层在顶层，解决绘制过程中线条显示问题 ===
      // 延迟一点时间，确保Terra Draw图层完全创建
      setTimeout(() => {
        if (this.drawManager && (this.drawManager as any).ensureDrawLayersOnTop) {

          (this.drawManager as any).ensureDrawLayersOnTop();
        }
      }, 150);


    } catch (error) {
      console.error('启动绘制失败:', error);
      this.setStatus(DrawStatus.ERROR);
      throw error;
    }
  }

  /**
   * @description 处理绘制开始事�?
   */
  private handleDrawStart(): void {

    this.setStatus(DrawStatus.DRAWING);
  }

  /**
   * @description 处理绘制完成事件
   */
  private handleDrawFinish(event: any): void {



    if (!event.features || !event.features[0] || !this.currentFeature) {
      console.error('绘制完成但缺少要素数据');


      return;
    }

    try {


      // 更新当前要素的几何信�?
      this.currentFeature.geojson.geometry = event.features[0].geometry;


      // 立即停止绘制模式，防止继续绘�?

      if (this.drawManager) {
        this.drawManager.setMode('select' as any);

      }

      // === 修复：使用originalFeature判断编辑模式和新增模�?===
      if (this.originalFeature) {
        // 编辑模式：需要添加到图层（因为原要素在重新绘制时已被移除�?
        if (this.featureLayer) {
          this.featureLayer.addFeature(this.currentFeature);

        }
      } else {
        // 新增模式：不添加到图层，避免与saveFeature时的添加重复

      }

      // 发布要素数据变更

      DrawModule.dataChange.next(new DrawData(this.currentFeature, false, this.currentFeature.geojson.geometry));

      // 重置状态为空闲，确保绘制模式完全停�?

      this.setStatus(DrawStatus.IDLE);



    } catch (error) {
      console.error('处理绘制完成失败:', error);
      this.setStatus(DrawStatus.ERROR);
      // 即使出错也要停止绘制模式
      if (this.drawManager) {
        this.drawManager.setMode('select' as any);

      }
    }
  }

  /**
   * @description 处理绘制取消事件
   */
  private handleDrawCancel(): void {

    this.currentFeature = null;
    this.setStatus(DrawStatus.IDLE);
    DrawModule.dataChange.next(new DrawData());
  }

  /**
   * @description 保存要素
   * @param feature - 要素数据
   */
  async saveFeature(feature: PlotFeature): Promise<boolean> {
    if (!this.plotDataManager || !this.featureLayer) {
      console.error('数据管理器或图层未初始化');
      return false;
    }

    try {
      this.setStatus(DrawStatus.SAVING);

      // 保存到数据库
      const saved = await this.plotDataManager.addFeature(feature);
      if (!saved) {
        throw new Error('保存要素到数据库失败');
      }

      // 检查要素是否已在图层中，避免重复添�?
      const existingFeature = this.featureLayer.getFeature(feature.id);
      if (existingFeature) {
        // 如果已存在，更新图层中的要素
        this.featureLayer.updateFeature(feature);
      } else {
        // 如果不存在，添加到图�?
        this.featureLayer.addFeature(feature);
      }

      // 保存成功后清除Terra Draw的临时绘制数�?
      if (this.drawManager) {
        try {
          // 清除Terra Draw中的所有临时要�?
          this.drawManager.clearAllFeatures();
          
          // 切换回选择模式，停止绘制状�?
          this.drawManager.setMode('select' as any);
        } catch (error) {
          console.warn('清除Terra Draw临时数据失败，但要素保存成功:', error);
        }
      }

      // 重置状�?
      this.currentFeature = null;
      this.setStatus(DrawStatus.IDLE);

      return true;
    } catch (error) {
      console.error('保存要素失败:', error);
      this.setStatus(DrawStatus.ERROR);
      return false;
    }
  }

  /**
   * @description 更新要素
   * @param feature - 更新后的要素数据
   */
  async updateFeature(feature: PlotFeature): Promise<boolean> {
    if (!this.plotDataManager || !this.featureLayer) {
      console.error('数据管理器或图层未初始化');
      return false;
    }

    try {
      this.setStatus(DrawStatus.SAVING);
      
      // 检查数据管理器中是否存在该要素
      const existingFeature = await this.plotDataManager.getFeature(feature.id);
      if (!existingFeature) {
        throw new Error(`要素 ${feature.id} 不存在，无法更新`);
      }

      // 更新数据库中的要�?
      const updated = await this.plotDataManager.updateFeature(feature);
      if (!updated) {
        throw new Error('更新要素到数据管理器失败');
      }

      // 更新图层中的要素
      const layerUpdated = this.featureLayer.updateFeature(feature);
      if (!layerUpdated) {
        console.warn('图层更新失败，但数据管理器已更新');
      }

      this.setStatus(DrawStatus.IDLE);
      return true;
    } catch (error) {
      console.error('更新要素失败:', error);
      this.setStatus(DrawStatus.ERROR);
      return false;
    }
  }

  /**
   * @description 删除要素
   * @param featureId - 要素ID
   */
  async deleteFeature(featureId: number): Promise<boolean> {
    if (!this.plotDataManager || !this.featureLayer) {
      console.error('数据管理器或图层未初始化');
      return false;
    }

    try {
      // 从数据库删除
      const deleted = await this.plotDataManager.deleteFeature(featureId);
      if (!deleted) {
        throw new Error('从数据库删除要素失败');
      }

      // 从图层删�?
      this.featureLayer.removeFeature(featureId);

      return true;
    } catch (error) {
      console.error('删除要素失败:', error);
      return false;
    }
  }

  /**
   * @description 停止绘制
   */
  stopDraw(): void {
    if (this.drawManager) {
      this.drawManager.setMode('select' as any);
    }
    this.currentFeature = null;
    this.setStatus(DrawStatus.IDLE);
    DrawModule.dataChange.next(new DrawData());
  }

  /**
   * @description 重置绘制状�?
   * @description 用于面板重新打开时清理之前的状�?
   */
  resetDrawState(): void {
    try {
      // 清除Terra Draw的临时绘制数�?
      if (this.drawManager) {
        try {
          // 清除Terra Draw中的所有临时要�?
          this.drawManager.clearAllFeatures();
        } catch (error) {
          console.warn('重置时清除Terra Draw临时数据失败:', error);
        }
      }

      // 停止当前绘制模式
      if (this.drawManager) {
        this.drawManager.setMode('select' as any);
      }

      // 清理当前要素
      this.currentFeature = null;

      // 重置状态为空闲
      this.setStatus(DrawStatus.IDLE);

      // 清理数据变更通知
      DrawModule.dataChange.next(new DrawData());
    } catch (error) {
      console.error('重置绘制状态失�?', error);
      // 确保至少状态被重置
      this.setStatus(DrawStatus.IDLE);
    }
  }

  /**
   * @description 清除当前绘制的图形（未保存的�?
   */
  clearCurrentDrawing(): void {
    if (!this.drawManager) {
      console.warn('DrawManager 未初始化，无法清除绘制');
      return;
    }

    try {
      // 清除地图上当前绘制的要素（DrawManager中的临时绘制数据�?
      this.drawManager.clearAllFeatures();
      
      // 如果有当前要素且未保存，从图层中移除
      if (this.currentFeature && this.featureLayer) {
        const existingFeature = this.featureLayer.getFeature(this.currentFeature.id);
        if (existingFeature) {
          this.featureLayer.removeFeature(this.currentFeature.id);

        }
      }
      
      // 重置当前要素和状�?
      this.currentFeature = null;
      this.setStatus(DrawStatus.IDLE);
      
      // 发布数据清除事件
      DrawModule.dataChange.next(new DrawData());
      

    } catch (error) {
      console.error('清除当前绘制失败:', error);
      // 即使清除失败，也要重置状�?
      this.currentFeature = null;
      this.setStatus(DrawStatus.IDLE);
      DrawModule.dataChange.next(new DrawData());
    }
  }

  /**
   * @description 获取所有要�?
   */
  async getAllFeatures(): Promise<PlotFeature[]> {
    if (!this.plotDataManager) {
      return [];
    }
    return await this.plotDataManager.getAllFeatures();
  }

  /**
   * @description 设置状�?
   */
  private setStatus(status: DrawStatus): void {
    this.currentStatus = status;
    DrawModule.statusChange.next(status);
  }

  /**
   * @description 获取当前状�?
   */
  getStatus(): DrawStatus {
    return this.currentStatus;
  }

  /**
   * @description 获取当前要素
   * @description 返回当前正在编辑或绘制的要素
   */
  getCurrentFeature(): PlotFeature | null {
    return this.currentFeature;
  }

  /**
   * @description 获取要素类型名称
   */
  private getFeatureTypeName(type: string): string {
    const names: Record<string, string> = {
      point: '点',
      linestring: '线',
      polygon: '多边形',
      rectangle: '矩形',
    };
    return names[type] || type;
  }

  /**
   * @description 生成默认要素名称
   * @param type - 要素类型
   * @returns 格式化的默认名称
   */
  generateDefaultFeatureName(type: string): string {
    const typeName = this.getFeatureTypeName(type);
    const timestamp = new Date().toISOString().replace(/[-:T]/g, '').split('.')[0]; // 格式�?0250530092431
    return `${typeName}${timestamp}`;
  }

  /**
   * @description 清理资源
   */
  destroy(): void {
    // 清理定时器
    this.clearLayerCheckTimers();
    
    if (this.drawManager) {
      this.drawManager.removeEventListener(DrawEventType.DRAW_FINISH, this.handleDrawFinish);
      this.drawManager.removeEventListener(DrawEventType.DRAW_START, this.handleDrawStart);
      this.drawManager.removeEventListener(DrawEventType.DRAW_CANCEL, this.handleDrawCancel);
      this.drawManager.removeEventListener(DrawEventType.FEATURE_UPDATE, this.handleFeatureUpdate);
    }

    this.drawManager = null;
    this.plotDataManager = null;
    this.featureLayer = null;
    this.currentFeature = null;
    this.isInitialized = false;
    DrawModule.instance = null;
  }

  /**
   * @description 清理图层检查定时器
   * @private
   */
  private clearLayerCheckTimers(): void {
    if (this.layerCheckInterval) {
      window.clearInterval(this.layerCheckInterval);
      this.layerCheckInterval = null;
    }
    
    if (this.layerCheckTimeout) {
      window.clearTimeout(this.layerCheckTimeout);
      this.layerCheckTimeout = null;
    }
  }

  /**
   * @description 重新加载localStorage中的要素数据到地�?
   * @description 用于调试或手动刷新已保存的要�?
   */
  async reloadStoredFeatures(): Promise<void> {
    if (!this.plotDataManager || !this.featureLayer) {
      console.error('数据管理器或图层未初始化，无法重新加载要素');
      return;
    }

    try {

      
      // 获取数据库中的所有要�?
      const storedFeatures = await this.plotDataManager.getAllFeatures();

      
      // 清空当前图层中的要素
      this.featureLayer.clearAllFeatures();
      
      // 重新添加有效要素
      const validFeatures = storedFeatures.filter((feature: PlotFeature) => feature.geojson.geometry);

      
      for (const feature of validFeatures) {
        this.featureLayer.addFeature(feature);

      }
      

      
    } catch (error) {
      console.error('重新加载存储要素失败:', error);
    }
  }

  /**
   * @description 获取当前图层中的要素数量（用于调试）
   */
  getLayerFeatureCount(): number {
    return this.featureLayer?.getAllFeatures().length || 0;
  }

  /**
   * @description 获取数据库中的要素数量（用于调试�?
   */
  async getStoredFeatureCount(): Promise<number> {
    if (!this.plotDataManager) {
      return 0;
    }
    const features = await this.plotDataManager.getAllFeatures();
    return features.length;
  }

  /**
   * @description 开始编辑已有要�?
   * @param feature - 要编辑的要素
   */
  async startEdit(feature: PlotFeature): Promise<void> {
    if (!this.drawManager || !this.featureLayer) {
      throw new Error('DrawManager �?FeatureLayer 未初始化');
    }

    try {


      // 备份原要素数据，用于取消编辑时恢�?
      this.originalFeature = { ...feature };


      // 设置当前要素为编辑要�?
      this.currentFeature = { ...feature };
      this.setStatus(DrawStatus.EDITING);

      // 确保要素在图层中显示
      const existingFeature = this.featureLayer.getFeature(feature.id);
      if (!existingFeature) {
        this.featureLayer.addFeature(feature);

      }

      // === 优化：先确保DrawManager处于正常状�?===
      if (!this.drawManager.isEnabled()) {

        this.drawManager.start();
        // 等待DrawManager完全启动
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 清除绘制管理器中的现有要�?
      this.drawManager.clearAllFeatures();
      
      // 将要素添加到绘制管理器进行编�?
      const geoJsonFeature = {
        type: 'Feature',
        id: String(feature.id), // GeoJSON要求string类型的id
        geometry: feature.geojson.geometry,
        properties: {
          name: feature.name,
          remark: feature.remark || '',
          // 添加其他需要的属�?
          ...feature.geojson.properties.style
        }
      };
      
      // 添加要素到绘制管理器
      this.drawManager.addFeatures([geoJsonFeature]);

      
      // 切换到选择模式
      this.drawManager.setMode('select' as any);


      // === 新增：确保要素被选中，显示编辑锚�?===
      // 等待模式切换完成
      await new Promise(resolve => setTimeout(resolve, 150));
      
      try {
        // 尝试选中要素（如果DrawManager有selectFeature方法�?
        if (typeof (this.drawManager as any).selectFeature === 'function') {
          (this.drawManager as any).selectFeature(feature.id);

        } else {
          // 备选方案：通过Terra Draw实例直接操作
          const terraDraw = (this.drawManager as any).terraDraw;
          if (terraDraw && typeof terraDraw.setSelected === 'function') {
            terraDraw.setSelected([feature.id]);

          } else if (terraDraw && typeof terraDraw.selectFeature === 'function') {
            terraDraw.selectFeature(feature.id);

          } else {
            console.warn('无法找到选中要素的方法，要素可能无法显示编辑锚点');
            // 尝试通过模拟点击来选中要素
            this.trySelectFeatureByCoordinates(feature);
          }
        }
      } catch (selectError) {
        console.warn('选中要素失败:', selectError);
        // 备选方案：尝试通过坐标选中
        this.trySelectFeatureByCoordinates(feature);
      }

      // 发布编辑开始事�?
      DrawModule.dataChange.next(new DrawData(this.currentFeature, true, this.currentFeature.geojson.geometry));


    } catch (error) {
      console.error('启动要素编辑失败:', error);
      this.setStatus(DrawStatus.ERROR);
      throw error;
    }
  }

  /**
   * @description 尝试通过坐标位置选中要素
   * @param feature - 要选中的要�?
   * @private
   */
  private trySelectFeatureByCoordinates(feature: PlotFeature): void {
    try {
      const map = AppMaplibre.getMap();
      if (!map) return;

      // 计算要素的中心点
      let centerCoordinates: [number, number];
      
      switch (feature.geojson.geometry.type) {
        case 'Point':
          centerCoordinates = feature.geojson.geometry.coordinates as [number, number];
          break;
        case 'LineString':
          const lineCoords = feature.geojson.geometry.coordinates as number[][];
          const midIndex = Math.floor(lineCoords.length / 2);
          centerCoordinates = lineCoords[midIndex] as [number, number];
          break;
        case 'Polygon':
          const polyCoords = feature.geojson.geometry.coordinates[0] as number[][];
          // 计算多边形重�?
          let x = 0, y = 0;
          for (const coord of polyCoords) {
            x += coord[0];
            y += coord[1];
          }
          centerCoordinates = [x / polyCoords.length, y / polyCoords.length];
          break;
        default:
          console.warn('不支持的几何类型:', feature.geojson.geometry.type);
          return;
      }

      // 将经纬度转换为屏幕坐�?
      const screenPoint = map.project(centerCoordinates);
      
      // 模拟点击事件来选中要素
      const clickEvent = new MouseEvent('click', {
        clientX: screenPoint.x,
        clientY: screenPoint.y,
        bubbles: true
      });
      
      // 在地图容器上触发点击事件
      const mapContainer = map.getContainer();
      if (mapContainer) {
        mapContainer.dispatchEvent(clickEvent);

      }
    } catch (error) {
      console.warn('通过坐标选中要素失败:', error);
    }
  }

  /**
   * @description 停止编辑模式
   */
  stopEdit(): void {
    if (this.drawManager) {
      this.drawManager.setMode('select' as any);
    }
    
    // 清理编辑状态和备份数据
    this.originalFeature = null;
    this.setStatus(DrawStatus.IDLE);

  }

  /**
   * @description 取消编辑并恢复原要素
   */
  cancelEdit(): void {
    if (!this.originalFeature) {
      console.warn('没有原要素备份，无法恢复');
      // 如果没有备份，直接清除当前绘�?
      this.clearCurrentDrawing();
      return;
    }

    try {


      // 1. 清除地图上的当前绘制和编辑状�?
      if (this.drawManager) {
        this.drawManager.clearAllFeatures();
        this.drawManager.setMode('select' as any);
      }

      // 2. 恢复原要素到图层�?
      if (this.featureLayer) {
        // 确保原要素在图层中显�?
        const existingFeature = this.featureLayer.getFeature(this.originalFeature.id);
        if (existingFeature) {
          // 如果已存在，更新为原数据
          this.featureLayer.updateFeature(this.originalFeature);

        } else {
          // 如果不存在，重新添加
          this.featureLayer.addFeature(this.originalFeature);

        }
      }

      // 3. 清理编辑状�?
      this.currentFeature = null;
      this.originalFeature = null;
      this.setStatus(DrawStatus.IDLE);

      // 4. 发布数据清除事件
      DrawModule.dataChange.next(new DrawData());


    } catch (error) {
      console.error('取消编辑失败:', error);
      // 发生错误时，至少要清理状�?
      this.originalFeature = null;
      this.currentFeature = null;
      this.setStatus(DrawStatus.ERROR);
    }
  }

  /**
   * @description 处理要素更新事件（编辑完成）
   */
  private handleFeatureUpdate(event: any): void {



    // 如果当前处于编辑状态，更新几何信息
    if (this.currentStatus === DrawStatus.EDITING && this.currentFeature) {
      try {


        // Terra Draw的change事件可能传递features数组或单个feature
        let updatedFeature = null;
        
        if (event.features && event.features.length > 0) {
          // 从features数组中找到对应的要素
          updatedFeature = event.features.find((f: any) => f.id === this.currentFeature!.id);
          if (!updatedFeature) {
            updatedFeature = event.features[0]; // 使用第一个要�?
          }
        } else if (event.feature) {
          updatedFeature = event.feature;
        } else {
          // 尝试从Terra Draw获取最新数�?
          if (this.drawManager) {
            const allFeatures = this.drawManager['terraDraw']?.getSnapshot();
            if (allFeatures) {
              updatedFeature = allFeatures.find((f: any) => f.id === this.currentFeature!.id);
            }
          }
        }

        if (updatedFeature && updatedFeature.geometry) {
          this.currentFeature.geojson.geometry = updatedFeature.geometry;


          // 发布要素数据变更（保持编辑状态）

          DrawModule.dataChange.next(new DrawData(this.currentFeature, true, this.currentFeature.geojson.geometry));


        } else {
          console.warn('未找到更新的要素几何数据');
        }
      } catch (error) {
        console.error('处理要素更新失败:', error);
        this.setStatus(DrawStatus.ERROR);
      }
    } else {

    }
  }
}
