<template>
  <div class="base-card">
    <header>
      <div>
        <img src="@/assets/images/card-icon.png" alt="" class="mr-2" />
        <el-text class="card-title">{{ title }}</el-text>
      </div>
      <div><img src="@/assets/images/omit.png" alt="" srcset="" /></div>
    </header>
    <main>
      <el-scrollbar height="100%">
        <slot></slot>
      </el-scrollbar>
    </main>
  </div>
</template>
<script lang="ts" setup>
withDefaults(defineProps<{ title: string }>(), {});
</script>
<style lang="scss" scoped>
.base-card {
  border-radius: 6px;
  background-color: #fff;
  // box-shadow: 0 0 12px 3px rgba($color: #a9bee8, $alpha: 0.4);
  z-index: 10;
  overflow: hidden;
  // margin-bottom: 14px;
}
header {
  height: 42px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 14px;
  border-bottom: 1px solid #eeeeee;
}
.card-title {
  color: #2c3037;
  font-size: 16px;
}
</style>
