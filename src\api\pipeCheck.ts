import hRequest from '@/utils/http';

/**
 * 查询重复点
 * @param params 查询参数
 * @returns Promise<RPageInfoRepeatPtVo>
 */
export function queryRepeatPoint(params: {
  pageSize?: number;
  pageNum?: number;
  orderByColumn?: string;
  isAsc?: string;
  firstNum?: number;
}) {
  return hRequest.get({ url: '/analyse/gs/pt/repeat', params });
}

/**
 * 查询重复线
 * @param params 查询参数
 * @returns Promise<RPageInfoRepeatLnVo>
 */
export function queryRepeatLine(params: {
  pageSize?: number;
  pageNum?: number;
  orderByColumn?: string;
  isAsc?: string;
  firstNum?: number;
}) {
  return hRequest.get({ url: '/analyse/gs/ln/repeat', params });
}

/**
 * 查询重复属性
 * @param params 查询参数
 * @returns Promise<RPageInfoGsPtVo>
 */
export function queryRepeatAttr(params: {
  pageSize?: number;
  pageNum?: number;
  orderByColumn?: string;
  isAsc?: string;
  fieldName?: string;
  firstNum?: number;
}) {
  return hRequest.get({ url: '/analyse/gs/pt/repeat/attr', params });
}

// ==================== 类型定义 ====================

/**
 * 重复点分页查询参数
 */
export interface RepeatPointPageQuery {
  pageSize?: number;
  pageNum?: number;
  orderByColumn?: string;
  isAsc?: string;
}

/**
 * 重复点详细信息
 */
export interface RepeatPointDetail {
  gid: number;
  gl: string;
  gxddh: string;
  sx: string;
  fsw: string;
  dmgc: number;
  x: number;
  y: number;
  geojson: string;
  geom: string;
  jgcz: string;
  jggs: string;
  js: number;
  latitude: number | null;
  longitude: number | null;
  szdl: string;
  [key: string]: any;
}

/**
 * 重复点分组信息
 */
export interface RepeatPointGroup {
  count: number;
  gidArray: string;
  gidList: number[];
  list: RepeatPointDetail[];
  [key: string]: any;
}

/**
 * 重复点数据VO (用于表格显示的扁平化数据)
 */
export interface RepeatPtVo {
  groupId: string;
  groupIndex: number;
  rowIndexInGroup: number;
  totalRowsInGroup: number;
  point_code: string;
  count: number;
  szdl: string;
  geojson: string;
  gidArray: string;
  // 当前行对应的具体重复点详细信息
  gid: number;
  gl: string;
  gxddh: string;
  sx: string;
  fsw: string;
  jgcz: string;
  jggs: string;
  x: number;
  y: number;
  [key: string]: any;
}

/**
 * 重复线分页查询参数
 */
export interface RepeatLinePageQuery {
  pageSize?: number;
  pageNum?: number;
  orderByColumn?: string;
  isAsc?: string;
}

/**
 * 重复线详细信息
 */
export interface RepeatLineDetail {
  gid: number;
  gl: string;
  gxbm: string;
  gj: string;
  szdl: string;
  geojson: string;
  geom: string;
  x?: number;
  y?: number;
  length?: number;
  [key: string]: any;
}

/**
 * 重复线分组信息
 */
export interface RepeatLineGroup {
  count: number;
  gidArray: string;
  gidList: number[];
  list: RepeatLineDetail[];
  [key: string]: any;
}

/**
 * 重复线数据VO (用于表格显示的扁平化数据)
 */
export interface RepeatLnVo {
  groupId: string;
  groupIndex: number;
  rowIndexInGroup: number;
  totalRowsInGroup: number;
  gxbm: string;
  count: number;
  gl: string;
  gj: string;
  szdl: string;
  geojson: string;
  gidArray: string;
  // 当前行对应的具体重复线详细信息
  gid: number;
  x?: number;
  y?: number;
  length?: number;
  [key: string]: any;
}

/**
 * 重复属性检查分页查询参数
 */
export interface RepeatAttrPageQuery {
  pageSize?: number;
  pageNum?: number;
  orderByColumn?: string;
  isAsc?: string;
  fieldName?: string;
  firstNum?: number;
}

/**
 * 可选的重复属性检查字段
 */
export interface RepeatAttrField {
  key: string;
  label: string;
  description: string;
}

/**
 * 重复属性检查可选字段定义
 */
export const REPEAT_ATTR_FIELDS: RepeatAttrField[] = [
  { key: 'fsw', label: '附属物', description: '管点附属物类型' },
  { key: 'sx', label: '属性', description: '管点属性信息' },
  { key: 'dmgc', label: '地面高程', description: '管点地面高程值' },
  { key: 'js', label: '井深', description: '检查井深度' },
  { key: 'jggg', label: '井盖规格', description: '井盖规格型号' },
  { key: 'jgcz', label: '井盖材质', description: '井盖材质类型' },
  { key: 'szdl', label: '所在道路', description: '管点所在道路名称' }
];

/**
 * 重复属性检查结果详情 (基于GsPtVo)
 */
export interface RepeatAttrDetail {
  gid: number;
  gl?: string;
  gxddh?: string;
  sx?: string;
  fsw?: string;
  dmgc?: number;
  x?: number;
  y?: number;
  js?: number;
  jggg?: string;
  jgcz?: string;
  szdl?: string;
  longitude?: number | null;
  latitude?: number | null;
  geom?: string;
  geojson?: string;
  [key: string]: any;
}

/**
 * 重复属性检查数据VO (用于表格显示)
 */
export interface RepeatAttrVo {
  gid: number;
  gl: string;
  gxddh: string;
  sx: string;
  fsw: string;
  dmgc: number;
  js: number;
  jggg: string;
  jgcz: string;
  szdl: string;
  x: number;
  y: number;
  longitude?: number | null;
  latitude?: number | null;
  geojson: string;
  geom: string;
  // 用于显示选中的属性值
  selectedAttrValues: { [key: string]: any };
  [key: string]: any;
}
