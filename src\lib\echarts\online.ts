import { echarts } from './echarts'
export const initOnlineEchart = () => {
  const placeHolderStyle = {
    normal: {
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
      color: "rgba(0,0,0,0)",
      borderWidth: 0,
    },
    emphasis: {
      color: "rgba(0,0,0,0)",
      borderWidth: 0,
    },
  };

  const dataStyle = {
    normal: {
      // formatter: "{c}%",
      position: "center",
      show: false,
      textStyle: {
        fontSize: "40",
        fontWeight: "normal",
        color: "#34374E",
      },
    },
  };

  const option: any = {
    backgroundColor: "#fff",
    title:
    {
      text: "85%",
      left: "48.5%",
      top: "47%",
      textAlign: "center",
      textStyle: {
        color: "#1968ff",
        fontWeight: "normal",
        fontSize: "28",
        textAlign: "center",

      },
    },

    // grid: {
    //   bottom: 0,
    //   containLabel: true,
    // },
    //第一个图表
    series: [
      {
        type: "pie",
        hoverAnimation: false,
        radius: ["75%", "95%"],
        center: ["50%", "54%"],
        startAngle: 225,
        labelLine: {
          normal: {
            show: false,
          },
        },
        label: {
          normal: {
            position: "center",
          },
        },
        data: [
          {
            value: 100,
            itemStyle: {
              normal: {
                color: "#E1E8EE",
              },
            },
          },
          {
            value: 35,
            itemStyle: placeHolderStyle,
          },
        ],
      },

      //上层环形配置
      {
        type: "pie",
        hoverAnimation: false,
        radius: ["75%", "95%"],
        center: ["50%", "54%"],
        startAngle: 225,
        labelLine: {
          normal: {
            show: false,
          },
        },
        label: {
          normal: {
            show: false,
            position: "center",
          },
        },
        data: [
          {
            value: 85,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: '#1966FF'
                  },
                  {
                    offset: 1,
                    color: '#53C4FF'
                  }
                ]),
              },
            },
            label: dataStyle,
          },
          {
            value: 55,
            itemStyle: placeHolderStyle,
          },
        ],
      },
    ],
  };
  return option
}
