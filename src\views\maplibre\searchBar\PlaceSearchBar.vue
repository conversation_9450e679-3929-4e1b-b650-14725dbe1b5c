<!--
 * @Description: 简化版百度地图地名地址搜索组件
 * @Date: 2024-01-16 16:00:00
 * @Author: 项目开发团队
 * @LastEditTime: 2024-01-16 16:00:00
 * @Version: 2.0.0 - 简化版本
-->
<template>
  <div class="place-search-container">
    <div class="search-box">
      <el-input
        v-model="searchText"
        placeholder="搜索地名、地址、兴趣点..."
        clearable
        @input="handleInput"
        @keyup.enter="handleSearch"
        @clear="handleClear"
        @focus="handleFocus"
        @blur="handleBlur"
        class="search-input"
        :class="{ focused: isFocused }"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #append>
          <el-button
            class="search-btn"
            @click="handleSearch"
            :loading="isSearching"
            type="primary"
          >
            搜索
          </el-button>
        </template>
      </el-input>

      <!-- 搜索建议下拉列表 -->
      <div
        v-if="showSuggestions && (suggestions.length > 0 || isSearching)"
        class="suggestions-dropdown"
      >
        <!-- 加载提示 -->
        <div v-if="isSearching" class="loading-item">
          <el-icon class="loading-icon"><Loading /></el-icon>
          正在搜索...
        </div>

        <!-- 搜索建议列表 -->
        <div
          v-for="(item, index) in suggestions"
          :key="index"
          class="suggestion-item"
          @click="selectSuggestion(item)"
          @mouseenter="highlightIndex = index"
          :class="{ highlighted: highlightIndex === index }"
        >
          <el-icon class="item-icon">
            <LocationInformation v-if="item.type === 'poi'" />
            <Location v-else />
          </el-icon>
          <div class="item-content">
            <div
              class="item-name"
              v-html="highlightSearchText(item.name)"
            ></div>
            <div class="item-address">{{ item.address }}</div>
          </div>
        </div>

        <!-- 无结果提示 -->
        <div v-if="!isSearching && suggestions.length === 0" class="no-results">
          <el-icon><WarningFilled /></el-icon>
          未找到相关地点
        </div>
      </div>
      

    </div>
    <div id="bd-map" style="width: 0;height: 0;"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { ElMessage, ElNotification } from "element-plus";
import {
  Search,
  Location,
  LocationInformation,
  Loading,
  WarningFilled,
} from "@element-plus/icons-vue";
import type { Map } from "maplibre-gl";
import {loadBMap} from './loadMap'
/**
 * @description 百度地图搜索API配置
 */
const BAIDU_API_CONFIG = {
  baseUrl: 'http://api.map.baidu.com/',
  ak: 'nSxiPohfziUaCuONe4ViUP2N',
  seckey: 'qLcLIFDmzqw3lwDH54wR13A4E4OxbHCBJxBUr5qEggw%3D%2CkC4AqEdkK3_xDphwQn5KkdwNvk2ujNXx6ITJynaiiaalBKBG1tgmBbfrlbtzOt_jjmGp4UDWhfc7hiu6y0KRhyN-qmYX08bvf_jX6Z_sMmSyJj_h6LoImvzVMfH33TiQ8e_9BcX8g092vOmoBBSK9kJUD9_rJ_DewKXjuEIR2eZosCcIGUvQCgSPtPciVVZd',
  region: '79', // 乐山市区域代码
  cityLimit: false
};

const BmapGL = ref();
const BMapInstance = ref();
/**
 * @description 搜索结果项接口
 */
interface SearchResultItem {
  name: string; // 地点名称
  address: string; // 详细地址
  location: {
    // 经纬度坐标
    lng: number;
    lat: number;
  };
  type: "poi" | "geo"; // 类型：兴趣点或地理位置
  uid?: string; // 百度POI唯一标识
}

/**
 * @description 组件状态管理
 */
const searchText = ref("");
const suggestions = ref<SearchResultItem[]>([]);
const isSearching = ref(false);
const isFocused = ref(false);
const showSuggestions = ref(false);
const highlightIndex = ref(-1);
const mapInstance = ref<Map | null>(null);

/**
 * @description 防抖定时器
 */
let debounceTimer: number | null = null;

/**
 * @description 处理输入事件
 */
const handleInput = () => {
  // 清除之前的防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  if (!searchText.value.trim()) {
    showSuggestions.value = false;
    return;
  }

  // 500ms防抖
  debounceTimer = setTimeout(() => {
    performSuggestionSearch();
  }, 500);
};

/**
 * @description 处理焦点事件
 */
const handleFocus = () => {
  isFocused.value = true;

  if (searchText.value.trim()) {
    showSuggestions.value = suggestions.value.length > 0;
  }
};

/**
 * @description 处理失去焦点事件
 */
const handleBlur = () => {
  // 延迟处理，避免点击建议项时立即隐藏
  setTimeout(() => {
    isFocused.value = false;
    showSuggestions.value = false;
  }, 200);
};

/**
 * @description 执行搜索建议
 */
const performSuggestionSearch = async () => {
  if (!searchText.value.trim()) return;

  try {
    isSearching.value = true;
    showSuggestions.value = true;

    const results = await searchPlaces(searchText.value, "suggestion");
    suggestions.value = results.slice(0, 6); // 限制建议数量为6条
    highlightIndex.value = -1;
  } catch (error) {
    console.error("搜索建议失败:", error);
    suggestions.value = [];
  } finally {
    isSearching.value = false;
  }
};

/**
 * @description 处理搜索操作
 */
const handleSearch = async () => {
  if (!searchText.value.trim()) {
    ElMessage.warning("请输入搜索关键词");
    return;
  }

  try {
    isSearching.value = true;
    showSuggestions.value = true;

    console.log("🔍 [PlaceSearch] 执行搜索:", searchText.value);

    const results = await searchPlaces(searchText.value, "search");
    suggestions.value = results;

    if (results.length === 0) {
      ElMessage.info("未找到相关地点，请尝试其他关键词");
    } else if (results.length === 1) {
      // 只有一个结果时自动选择
      selectSuggestion(results[0]);
    }
  } catch (error) {
    console.error("搜索失败:", error);
    ElMessage.error("搜索失败，请稍后重试");
    suggestions.value = [];
  } finally {
    isSearching.value = false;
  }
};

/**
 * @description 调用搜索API - 使用百度地图API
 */
const searchPlaces = async (
  keyword: string,
  type: "suggestion" | "search"
): Promise<SearchResultItem[]> => {
  console.log(`🌍 [PlaceSearch] 搜索: ${keyword}, type: ${type}`);

  try {
    // 构建百度地图搜索URL
    const timestamp = Date.now();
    const encodedKeyword = encodeURIComponent(keyword);
    const callbackName = `BMapGL._rd._cbk16246`;

    const searchUrl = `${BAIDU_API_CONFIG.baseUrl}?qt=placesug&query=${encodedKeyword}&callback=${callbackName}&region=${BAIDU_API_CONFIG.region}&city_limit=${BAIDU_API_CONFIG.cityLimit}&t=${timestamp}&from=jsapi&ak=${BAIDU_API_CONFIG.ak}&v=gl&seckey=${BAIDU_API_CONFIG.seckey}&timeStamp=${timestamp}`;

    console.log('🔍 [PlaceSearch] 请求URL:', searchUrl);

    // 使用JSONP方式请求百度API
    const results = await fetchBaiduPlaces(searchUrl);

    return results.map((item: any) => ({
      name: item.name || item.n || '',
      address: item.address || item.addr || item.district || '',
      location: {
        lng: parseFloat(item.lng) || parseFloat(item.x) || 0,
        lat: parseFloat(item.lat) || parseFloat(item.y) || 0,
      },
      type: (item.type === 'poi' || item.detail_info) ? 'poi' as const : 'geo' as const,
      uid: item.uid || item.id || '',
    }));
  } catch (error) {
    console.error("搜索地点失败:", error);
    throw error;
  }
};

/**
 * @description 使用JSONP方式请求百度地图API
 */
const fetchBaiduPlaces = (url: string): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    // 从URL中提取回调函数名
    const callbackMatch = url.match(/callback=([^&]*)/);
    const callbackName = callbackMatch ? callbackMatch[1] : `baiduCallback_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // 创建script标签
    const script = document.createElement('script');
    script.src = url;

    // 设置全局回调函数
    (window as any)[callbackName] = (data: any) => {
      try {
        // 清理
        document.head.removeChild(script);
        delete (window as any)[callbackName];

        // 处理返回数据
        console.log('🔍 [PlaceSearch] 百度API返回数据:', data);
        if (data && data.s && Array.isArray(data.s)) {
          resolve(data.s);
        } else {
          resolve([]);
        }
      } catch (error) {
        reject(error);
      }
    };

    // 错误处理
    script.onerror = () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
      delete (window as any)[callbackName];
      reject(new Error('网络请求失败'));
    };

    // 超时处理
    setTimeout(() => {
      if ((window as any)[callbackName]) {
        if (document.head.contains(script)) {
          document.head.removeChild(script);
        }
        delete (window as any)[callbackName];
        reject(new Error('请求超时'));
      }
    }, 10000);

    // 添加到页面
    document.head.appendChild(script);
  });
};





/**
 * @description 选择搜索建议
 */
const selectSuggestion = async (item: SearchResultItem) => {
  try {
    console.log("📍 [PlaceSearch] 选择地点:", item);

    // 更新搜索框文本
    searchText.value = item.name;

    // 隐藏下拉列表
    showSuggestions.value = false;

    // 执行地图定位
    await performMapLocation(item.location.lng, item.location.lat);

    // 显示成功提示
    ElNotification({
      title: "定位成功",
      message: `已定位到：${item.name}`,
      type: "success",
      duration: 2000,
      position: "top-right",
    });
  } catch (error) {
    console.error("选择地点失败:", error);
    ElMessage.error("定位失败，请重试");
  }
};



/**
 * @description 执行地图定位跳转
 */
const performMapLocation = async (longitude: number, latitude: number) => {
  if (!mapInstance.value) {
    throw new Error("地图实例未初始化");
  }

  console.log("🗺️ [PlaceSearch] 地图定位:", { longitude, latitude });

  // 使用flyTo方法平滑跳转到目标位置
  mapInstance.value.flyTo({
    center: [longitude, latitude],
    zoom: 16,
    duration: 2000,
    essential: true,
  });
};

/**
 * @description 处理清除操作
 */
const handleClear = () => {
  searchText.value = "";
  suggestions.value = [];
  showSuggestions.value = false;
};

/**
 * @description 高亮搜索关键词
 */
const highlightSearchText = (text: string): string => {
  if (!searchText.value.trim()) return text;

  const keyword = searchText.value.trim();
  const regex = new RegExp(`(${keyword})`, "gi");
  return text.replace(regex, '<span class="highlight">$1</span>');
};

/**
 * @description 处理键盘事件
 */
const handleKeydown = (event: KeyboardEvent) => {
  if (!showSuggestions.value || suggestions.value.length === 0) return;

  switch (event.key) {
    case "ArrowDown":
      event.preventDefault();
      highlightIndex.value = Math.min(
        highlightIndex.value + 1,
        suggestions.value.length - 1
      );
      break;
    case "ArrowUp":
      event.preventDefault();
      highlightIndex.value = Math.max(highlightIndex.value - 1, -1);
      break;
    case "Enter":
      event.preventDefault();
      if (
        highlightIndex.value >= 0 &&
        highlightIndex.value < suggestions.value.length
      ) {
        selectSuggestion(suggestions.value[highlightIndex.value]);
      }
      break;
    case "Escape":
      showSuggestions.value = false;
      break;
  }
};

/**
 * @description 处理点击外部事件
 */
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  const container = target.closest(".place-search-container");

  if (!container) {
    showSuggestions.value = false;
    isFocused.value = false;
  }
};

/**
 * @description 组件挂载后初始化
 */
onMounted(() => {
  // 延迟获取地图实例
  nextTick(() => {
    setTimeout(() => {
      try {
        mapInstance.value = AppMaplibre.getMap();
        console.log("✅ [PlaceSearch] 已连接到MapLibre地图实例");
        loadBMap(BAIDU_API_CONFIG.ak).then(res => {
          BmapGL.value = res;
          BMapInstance.value = BmapGL.value.Map('bd-map')
        })

      } catch (error) {
        console.error("❌ [PlaceSearch] 无法获取MapLibre地图实例:", error);
      }
    }, 500);
  });

  // 添加键盘事件监听
  document.addEventListener("keydown", handleKeydown);

  // 添加点击外部关闭下拉列表的监听
  document.addEventListener("click", handleClickOutside);

  // 开发环境下暴露调试方法到全局
  if (import.meta.env.DEV) {
    (window as any).testPlaceSearch = async (keyword: string) => {
      try {
        console.log('🧪 [PlaceSearch] 测试搜索:', keyword);
        const results = await searchPlaces(keyword, 'search');
        console.log('🧪 [PlaceSearch] 搜索结果:', results);
        return results;
      } catch (error) {
        console.error('🧪 [PlaceSearch] 测试失败:', error);
        throw error;
      }
    };
    console.log('🧪 [PlaceSearch] 调试方法已暴露: window.testPlaceSearch(keyword)');
  }
});

/**
 * @description 组件卸载前清理
 */
onUnmounted(() => {
  // 清理定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  // 移除事件监听器
  document.removeEventListener("keydown", handleKeydown);
  document.removeEventListener("click", handleClickOutside);
});
</script>

<style scoped lang="scss">
.place-search-wrapper {
  position: relative;
  width: 380px;
  z-index: 1;
}

.search-box {
  position: relative;
}

.search-input {
  width: 100%;

  &.focused {
    :deep(.el-input__wrapper) {
      border-color: #409eff;
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }

  :deep(.el-input__wrapper) {
    border-radius: 8px 0 0 8px;
    transition: all 0.3s ease;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
    }
  }

  :deep(.el-input__prefix) {
    color: #909399;
  }

  :deep(.el-input-group__append) {
    border-radius: 0 6px 6px 0;
    border-left: none;

    .search-btn {
      border: none;
      border-radius: 0 6px 6px 0;
    }
  }
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  max-height: 320px;
  overflow-y: auto;
  z-index: 1;
  margin-top: 4px;
}

.loading-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #909399;
  font-size: 14px;

  .loading-icon {
    margin-right: 8px;
    animation: rotate 1s linear infinite;
  }
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f5f7fa;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover,
  &.highlighted {
    background-color: #f5f7fa;
  }

  .item-icon {
    margin-right: 12px;
    color: #409eff;
    flex-shrink: 0;
  }

  .item-content {
    flex: 1;
    min-width: 0;

    .item-name {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
      word-break: break-all;

      :deep(.highlight) {
        color: #409eff;
        font-weight: 600;
        background-color: rgba(64, 158, 255, 0.1);
        padding: 0 2px;
        border-radius: 2px;
      }
    }

    .item-address {
      font-size: 12px;
      color: #909399;
      word-break: break-all;
    }
  }
}

.no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  color: #909399;
  font-size: 14px;

  .el-icon {
    margin-right: 8px;
  }
}



/* 滚动条样式 */
.suggestions-dropdown::-webkit-scrollbar {
  width: 6px;
}

.suggestions-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.suggestions-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

/* 动画效果 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .place-search-container {
    width: 100%;
    max-width: 300px;
  }
}
</style>
