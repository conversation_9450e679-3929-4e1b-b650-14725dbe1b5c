/**
 * @fileoverview 动态图片资源导入工具
 * @description 使用Vite的import.meta.glob预加载机制，解决生产环境下动态图片路径404问题
 * <AUTHOR> 
 * @version 3.0.0 - 基于import.meta.glob的全新实现
 * @date 2024-12-19
 */

/**
 * @description 使用import.meta.glob预加载所有图片资源
 * @details eager: true 表示立即加载，而不是返回函数
 */
const imageModules = import.meta.glob('/src/assets/images/**/*', { 
  eager: true, 
  as: 'url' 
});

/**
 * @description 构建图片路径映射表
 * @details 将绝对路径 '/src/assets/images/xxx' 转换为相对路径 'xxx'
 */
const imageMap: Record<string, string> = {};

// 初始化图片映射表
for (const path in imageModules) {
  // 从 '/src/assets/images/tools/bookmark.png' 提取 'tools/bookmark.png'
  const relativePath = path.replace('/src/assets/images/', '');
  const imageUrl = imageModules[path] as string;
  imageMap[relativePath] = imageUrl;
}

/**
 * @description 动态引入本地图片（全新实现）
 * @param {string} name - 图片名称，相对于 src/assets/images/ 的路径
 * @returns {string} 图片URL，在开发和生产环境下都能正确解析
 * 
 * @example
 * // 基础用法
 * getImages('user.png') // -> '/src/assets/images/user.png' (dev) 或 '/assets/user-abc123.png' (prod)
 * 
 * // 子目录
 * getImages('tools/bookmark.png') // -> 对应的实际URL
 * 
 * // 动态拼接（常见场景）
 * getImages(`device-svg/${device.icon}.svg`)
 * getImages(`tools/${tool.name}-active.png`)
 */
export const getImages = (name: string): string => {
  // 标准化路径：移除开头的斜杠
  const normalizedName = name.startsWith('/') ? name.slice(1) : name;
  
  // 从预加载的映射表中获取图片URL
  const imageUrl = imageMap[normalizedName];
  
  if (imageUrl) {
    // 成功找到图片
    if (import.meta.env.DEV) {
      // console.debug(`🖼️ [getImages] 找到图片: ${normalizedName} -> ${imageUrl}`);
    }
    return imageUrl;
  } else {
    // 未找到图片时的处理
    // console.warn(`⚠️ [getImages] 未找到图片: "${normalizedName}"`);
    
    if (import.meta.env.DEV) {
      // 开发环境：显示详细的错误信息和建议
      // console.warn(`🔍 [getImages] 可用的图片列表:`, Object.keys(imageMap));
      
      // 尝试给出相似的建议
      const suggestions = Object.keys(imageMap).filter(path => 
        path.includes(normalizedName.split('/')[0]) || 
        path.includes(normalizedName.split('.')[0])
      );
      
      if (suggestions.length > 0) {
        // console.warn(`💡 [getImages] 相似的图片路径:`, suggestions);
      }
    }
    
    // 返回一个明显的占位符，避免破坏页面布局
    return `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+acquaJvjwvdGV4dD48L3N2Zz4=`;
  }
};

/**
 * @description 检查图片是否存在
 * @param {string} name - 图片名称
 * @returns {boolean} 是否存在该图片
 */
export const hasImage = (name: string): boolean => {
  const normalizedName = name.startsWith('/') ? name.slice(1) : name;
  return normalizedName in imageMap;
};

/**
 * @description 获取所有可用的图片路径
 * @returns {string[]} 所有预加载的图片路径
 */
export const getAllImagePaths = (): string[] => {
  return Object.keys(imageMap);
};

/**
 * @description 根据目录前缀获取图片列表
 * @param {string} prefix - 目录前缀，如 'tools', 'aside' 等
 * @returns {string[]} 该目录下的所有图片路径
 */
export const getImagesByPrefix = (prefix: string): string[] => {
  return Object.keys(imageMap).filter(path => path.startsWith(prefix + '/'));
};

/**
 * @description 获取图片映射表的统计信息
 * @returns {object} 包含总数和各目录统计的对象
 */
export const getImageStats = () => {
  const stats = {
    total: Object.keys(imageMap).length,
    byDirectory: {} as Record<string, number>
  };
  
  Object.keys(imageMap).forEach(path => {
    const dir = path.split('/')[0];
    stats.byDirectory[dir] = (stats.byDirectory[dir] || 0) + 1;
  });
  
  return stats;
};

// 向后兼容：保持原有的默认导出
export default getImages;

/**
 * @description 初始化日志和统计信息
 */
if (import.meta.env.DEV) {
  const stats = getImageStats();
  // console.log(`🖼️ getImages 已初始化，预加载了 ${stats.total} 个图片资源`);
  // console.log(`📁 目录分布:`, stats.byDirectory);
  // console.log(`📋 完整图片列表:`, getAllImagePaths());
} else {
  // console.log(`🖼️ getImages 生产模式已就绪，包含 ${Object.keys(imageMap).length} 个图片资源`);
}
