<!--
 * @Description: 重复点检查面板
 * @Date: 2024-07-04
 * @Author: AI Assistant
 -->
<template>
  <page-card
    class="repeat-point-check-panel"
    title="重复点检查"
    @closeCard="closeCard"
  >
    <!-- 统计信息 -->
    <div class="stats-section" v-if="!loading">
      <el-alert
        :title="`发现 ${getRepeatGroupCount()} 组重复点数据${
          getRepeatGroupCount() > 0
            ? '，总计' + getTotalRepeatCount() + '个重复点'
            : ''
        }`"
        :type="total > 0 ? 'warning' : 'success'"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 操作按钮区域 -->
    <el-row class="button-section" flex="~ row justify-start">
      <el-button
        type="primary"
        class="select-btn h-9"
        :icon="Refresh"
        @click="handleRefresh"
        :loading="loading"
      >
        刷新检查
      </el-button>
    </el-row>

    <!-- 重复点列表表格 -->
    <el-row v-if="repeatPoints.length > 0" class="table-section">
      <el-col :span="24">
        <el-text class="table-label">重复点详细信息</el-text>
        <el-table
          :data="repeatPoints"
          v-loading="loading"
          class="routeCt"
          stripe
          height="300"
          empty-text="暂无重复点数据"
          :span-method="arraySpanMethod"
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            :index="getTableIndex"
          />

          <el-table-column
            prop="count"
            label="重复数量"
            width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="gid"
            label="GID"
            width="80"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="gxddh"
            label="管点编码"
            width="130"
            show-overflow-tooltip
          />
          <el-table-column
            prop="gl"
            label="管类"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="sx"
            label="属性"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="jgcz"
            label="井盖材质"
            width="90"
            show-overflow-tooltip
          />
          <el-table-column
            prop="szdl"
            label="所在道路"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="操作"
            width="120"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleLocate(row)"
                title="定位到地图"
              >
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center">
          <div>
            <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
          </div>
          <!-- 分页组件 -->
          <el-pagination
            v-if="total > 0"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :total="total"
            :pager-count="5"
            layout="prev, pager, next, jumper"
            class="pagination"
            background
            small
          />
        </div>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import { Refresh, Position } from "@element-plus/icons-vue";
import PageCard from "@/components/PageCard.vue";
import { queryRepeatPoint } from "@/api/pipeCheck";
import type {
  RepeatPtVo,
  RepeatPointPageQuery,
  RepeatPointGroup,
  RepeatPointDetail,
} from "@/api/pipeCheck";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import type { Map as MapLibreMap } from "maplibre-gl";

const REPEAT_POINT_LAYER_IDS = {
  SOURCE: "repeat-point-highlight-source",
  LAYER: "repeat-point-highlight-layer",
} as const;

interface Emits {
  (e: "close"): void;
}
const emit = defineEmits<Emits>();

const loading = ref(false);
const repeatPoints = ref<RepeatPtVo[]>([]);
const originalGroupData = ref<RepeatPointGroup[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const pageSizes = ref([5, 10, 20, 50]);
let map: MapLibreMap | null = null;

onMounted(async () => {
  try {
    map = AppMaplibre.getMap();
  } catch (error) {
    console.warn("获取地图实例失败:", error);
  }
  await handleRefresh();
});

onUnmounted(() => {
  clearRepeatPointHighlight();
  resetData();
});

const clearRepeatPointHighlight = (): void => {
  if (!map) return;
  if (map.getLayer(REPEAT_POINT_LAYER_IDS.LAYER)) {
    map.removeLayer(REPEAT_POINT_LAYER_IDS.LAYER);
  }
  if (map.getSource(REPEAT_POINT_LAYER_IDS.SOURCE)) {
    map.removeSource(REPEAT_POINT_LAYER_IDS.SOURCE);
  }
};

const addRepeatPointHighlight = (geometry: any): void => {
  if (!map) return;
  clearRepeatPointHighlight();
  map.addSource(REPEAT_POINT_LAYER_IDS.SOURCE, {
    type: "geojson",
    data: {
      type: "FeatureCollection",
      features: [{ type: "Feature", geometry, properties: {} }],
    },
  });
  map.addLayer({
    id: REPEAT_POINT_LAYER_IDS.LAYER,
    type: "circle",
    source: REPEAT_POINT_LAYER_IDS.SOURCE,
    paint: {
      "circle-radius": 8,
      "circle-color": "#ff4d4f",
      "circle-stroke-width": 2,
      "circle-stroke-color": "#ffffff",
    },
  });
};

/**
 * 计算表格序号，基于重复组计算
 * @param index 行索引
 * @returns 序号
 */
const getTableIndex = (index: number): number => {
  const row = repeatPoints.value[index];
  if (!row) return index + 1;

  // 只有组内第一行才显示序号，其他行通过行合并不显示
  if (row.rowIndexInGroup === 0) {
    // 计算当前组在当前页面中的序号
    let groupSequence = 0;
    const seenGroups = new Set<number>();

    for (let i = 0; i <= index; i++) {
      const currentRow = repeatPoints.value[i];
      if (
        currentRow?.rowIndexInGroup === 0 &&
        !seenGroups.has(currentRow.groupIndex)
      ) {
        groupSequence++;
        seenGroups.add(currentRow.groupIndex);
        if (i === index) {
          return groupSequence;
        }
      }
    }
    return groupSequence;
  }

  // 非第一行返回占位符（实际不会显示，因为行合并）
  return index + 1;
};

const handleCurrentChange = (page: number): void => {
  currentPage.value = page;
  handleRefresh();
};

const handleSizeChange = (size: number): void => {
  pageSize.value = size;
  currentPage.value = 1;
  handleRefresh();
};

const closeCard = (): void => {
  clearRepeatPointHighlight();
  resetData();
  emit("close");
};

/**
 * 计算总的重复点数量
 * 基于原始分组数据进行统计，避免重复计算
 * @returns 总重复点数量
 */
const getTotalRepeatCount = (): number => {
  return originalGroupData.value.reduce((total, group) => {
    // 每个分组的重复点数量就是该组内list的长度
    return total + (group.list?.length || 0);
  }, 0);
};

/**
 * 计算重复点分组数量
 * @returns 重复点分组数量
 */
const getRepeatGroupCount = (): number => {
  return originalGroupData.value.length;
};

/**
 * 表格行合并方法
 * @param row 当前行数据
 * @param column 当前列
 * @param rowIndex 行索引
 * @param columnIndex 列索引
 * @returns 合并信息 [rowspan, colspan]
 */
const arraySpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}: any): [number, number] => {
  const currentRow = row as RepeatPtVo;

  // 需要合并的列索引：序号(0)、重复数量(1)
  // 列索引对应：0=序号, 1=重复数量, 2=GID, 3=管点编码, 4=管类, 5=属性, 6=井盖材质, 7=所在道路, 8=操作
  const mergeColumnIndexes = [0, 1];

  if (mergeColumnIndexes.includes(columnIndex)) {
    // 如果是组内第一行，显示合并单元格
    if (currentRow.rowIndexInGroup === 0) {
      return [currentRow.totalRowsInGroup, 1];
    } else {
      // 其他行不显示
      return [0, 0];
    }
  }

  // 其他列正常显示
  return [1, 1];
};

// ==================== 数据处理工具函数 ====================

/**
 * 创建单个重复点的扁平化数据项
 * @param point 重复点详细信息
 * @param group 所属分组信息
 * @param groupIndex 分组索引
 * @param pointIndex 点在分组中的索引
 * @returns 扁平化数据项
 */
const createFlatDataItem = (
  point: RepeatPointDetail,
  group: RepeatPointGroup,
  groupIndex: number,
  pointIndex: number
): RepeatPtVo => {
  return {
    groupId: `group_${groupIndex}`,
    groupIndex: groupIndex,
    rowIndexInGroup: pointIndex,
    totalRowsInGroup: group.list.length,
    point_code:
      point.gxddh ||
      point.gid?.toString() ||
      `未知编码_${groupIndex}_${pointIndex}`,
    count: group.count,
    szdl: point.szdl || "未知道路",
    geojson: point.geojson || "",
    gidArray: group.gidArray,
    // 当前行对应的具体重复点详细信息
    gid: point.gid,
    gl: point.gl || "",
    gxddh: point.gxddh || "",
    sx: point.sx || "",
    fsw: point.fsw || "",
    jgcz: point.jgcz || "",
    jggs: point.jggs || "",
    x: point.x,
    y: point.y,
  };
};

/**
 * 将重复点分组数据转换为扁平化格式，每个重复点作为单独一行
 * @param groupData 重复点分组数据
 * @returns 扁平化的重复点数据
 */
const convertGroupDataToFlatData = (
  groupData: RepeatPointGroup[]
): RepeatPtVo[] => {
  const flatData: RepeatPtVo[] = [];

  groupData.forEach((group, groupIndex) => {
    if (group.list && group.list.length > 0) {
      // 将每个重复点都作为单独的行
      group.list.forEach((point, pointIndex) => {
        const flatItem = createFlatDataItem(
          point,
          group,
          groupIndex,
          pointIndex
        );
        flatData.push(flatItem);
      });
    }
  });

  return flatData;
};

/**
 * 解析点位坐标信息
 * @param point 重复点数据
 * @returns 坐标数组或null
 */
const parsePointCoordinates = (point: RepeatPtVo): [number, number] | null => {
  // 优先尝试解析geojson
  if (point.geojson) {
    try {
      const geometry = JSON.parse(point.geojson);
      if (geometry.type === "Point" && geometry.coordinates) {
        return geometry.coordinates;
      }
    } catch (geoError) {
      console.warn("解析geojson失败:", geoError);
    }
  }

  // 如果geojson解析失败，使用x,y坐标
  if (point.x && point.y) {
    return [point.x, point.y];
  }

  return null;
};

const handleRefresh = async (): Promise<void> => {
  loading.value = true;
  clearRepeatPointHighlight();

  try {
    const params: RepeatPointPageQuery = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    };

    const response = await queryRepeatPoint(params);

    if (response.code === 200 && response.data) {
      // 保存原始分组数据
      const rawGroupData: RepeatPointGroup[] = response.data.list || [];
      originalGroupData.value = rawGroupData;

      // 转换为表格显示数据
      const convertedData = convertGroupDataToFlatData(rawGroupData);
      repeatPoints.value = convertedData;

      // 更新分页信息
      total.value = response.data.totalCount || 0;

      // 用户反馈
      if (total.value === 0) {
        ElMessage.success("数据健康！未发现重复点");
      } else {
        const totalRepeatCount = getTotalRepeatCount();
        const groupCount = getRepeatGroupCount();
        ElMessage.info(
          `发现 ${groupCount} 组重复点，共计 ${totalRepeatCount} 个重复点`
        );
      }
    } else {
      // 错误处理
      const errorMsg = response.msg || "未知错误";
      ElMessage.error(`查询重复点失败: ${errorMsg}`);
      resetData();
    }
  } catch (error) {
    console.error("查询重复点异常:", error);
    const errorMsg = error instanceof Error ? error.message : "网络或系统异常";
    ElMessage.error(`查询重复点异常: ${errorMsg}`);
    resetData();
  } finally {
    loading.value = false;
  }
};

/**
 * 重置数据状态
 */
const resetData = (): void => {
  repeatPoints.value = [];
  originalGroupData.value = [];
  total.value = 0;
};

const handleLocate = (point: RepeatPtVo): void => {
  if (!map) {
    ElMessage.error("地图实例不可用");
    return;
  }

  try {
    const coordinates = parsePointCoordinates(point);

    if (!coordinates) {
      ElMessage.error("无法获取有效的位置坐标");
      return;
    }

    // 创建用于高亮的几何对象
    const geometry = {
      type: "Point",
      coordinates: coordinates,
    };

    addRepeatPointHighlight(geometry);
    map.flyTo({
      center: coordinates,
      zoom: 18,
      duration: 1500,
    });

    // const pointName = point.gxddh || point.gid || "未知";
    // ElMessage.success(
    //   `已定位到管点: ${pointName} (GID: ${point.gid})，该组共发现 ${point.count} 个重复点`
    // );
  } catch (error) {
    console.error("定位失败:", error);
    ElMessage.error("定位失败，请检查数据格式");
  }
};
</script>

<style scoped lang="scss">
.repeat-point-check-panel {
  position: absolute;
  left: 10px;
  top: 150px;
  width: 724px;
  max-height: 85vh;
  z-index: 999;
}
.stats-section,
.button-section,
.table-section {
  margin-bottom: 16px;
}
.table-label {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}
</style>
