import hRequest from '@/utils/http';
import type { DataType } from '@/utils/http/types';
// 导航
export const queryMenus = () => {
  return hRequest.get<DataType>({
    url: '/sys/menu/nav'
  });
};
// 树形结构
export const menuTree = (data: any) => {
  return hRequest.get<DataType>({
    url: '/sys/menu/tree',
    params: data
  });
};
// 新增
export const addMenu = (data: any) => {
  return hRequest.post<DataType>({
    url: '/sys/menu',
    data: data
  });
};
// 修改
export const editMenu = (data: any) => {
  return hRequest.put<DataType>({
    url: '/sys/menu',
    data: data
  });
};
// 修改状态
export const editMenuState = (data: any) => {
  return hRequest.put<DataType>({
    url: '/sys/menu/status',
    data: data
  });
};
// 详情
export const detailMenu = (id: any) => {
  return hRequest.get<DataType>({
    url: `/sys/menu/${id}`
  });
};
// 删除
export const deleteMenu = (id: any) => {
  return hRequest.delete<DataType>({
    url: `/sys/menu/${id}`
  });
};
export const permission = () => {
  return hRequest.get<DataType>({
    url: "/sys/menu/user/perms",
  });
};