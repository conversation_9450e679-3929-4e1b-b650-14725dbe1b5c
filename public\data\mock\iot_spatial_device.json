{"code": 200, "message": "success", "data": [[{"fieldType": 1, "fieldKey": "site_code", "fieldName": "所属站点", "displayForm": "select", "source": "site", "dictMap": {"1943192319004758016": "乐山站点", "111111": "龚电家园", "1943278388035399680": "乐山站点2", "1943191054883475456": "乐轧泵房", "1940608774704869376": "邦德加压站"}, "isMultiple": 0, "fieldValues": "1943192319004758016"}, {"fieldType": 1, "fieldKey": "device_name", "fieldName": "设备名称", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "奇怪剂"}, {"fieldType": 1, "fieldKey": "second_device_type_code", "fieldName": "设备类型", "displayForm": "select", "source": "iot_device_type", "dictMap": {"start_meter": "电子围栏", "pressure_sensor": "压力传感器", "flowmeter": "流量计", "detector": "噪声听漏仪", "iot_device_type_wqtm": "水质检测仪"}, "isMultiple": 0, "fieldValues": "start_meter"}, {"fieldType": 1, "fieldKey": "equipment_time", "fieldName": "设备生产日期", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "2023.11.03"}, {"fieldType": 1, "fieldKey": "picture", "fieldName": "设备图片", "displayForm": "file", "source": null, "dictMap": {"1943565950683693056": "1.jpeg::https://minio.zhiyou-tec.com/dev-iot/120250711150006_7870edae-09fd-47df-9487-ebe35b75dc7b.jpeg"}, "isMultiple": 0, "fieldValues": "1943565950683693056"}, {"fieldType": 1, "fieldKey": "describe", "fieldName": "设备描述", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "奇怪的奇"}, {"fieldType": 1, "fieldKey": "serial_number", "fieldName": "设备序列号", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": ""}, {"fieldType": 1, "fieldKey": "factory_code", "fieldName": "所属厂商", "displayForm": "select", "source": "manufacturer", "dictMap": {"M.C_8148600153450807296": "重庆怡源水表公司", "M.C_8148290270440521728": "四川水利仪器设备有限公司\t", "M.C_8163119358158110720": "天津蓝瑄仪表"}, "isMultiple": 0, "fieldValues": "M.C_8148600153450807296"}, {"fieldType": 1, "fieldKey": "model_number", "fieldName": "型号", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": ""}, {"fieldType": 1, "fieldKey": "address", "fieldName": "所属位置", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "11是"}, {"fieldType": 1, "fieldKey": "longitude", "fieldName": "经度", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "103.594883"}, {"fieldType": 1, "fieldKey": "latitude", "fieldName": "纬度", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "29.472302"}, {"fieldType": 1, "fieldKey": "online_status", "fieldName": "设备在线状态", "displayForm": "switch", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": ""}, {"fieldType": 1, "fieldKey": "status", "fieldName": "设备使用状态", "displayForm": "switch", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "0"}, {"fieldType": 4, "fieldKey": "protocol_code", "fieldName": "绑定协议", "displayForm": "select", "source": "protocol", "dictMap": {"PROTOCOL0001": "天津蓝瑄水表数据解析协议", "PROTOCOL0003": "西川府星水表数据解析协议", "PROTOCOL0002": "成都声立德克超声波大表数据解析协议"}, "isMultiple": 0, "fieldValues": "PROTOCOL0001"}, {"fieldType": 1, "fieldKey": "device_code", "fieldName": "设备编码", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "1945411610270515200"}, {"fieldType": 1, "fieldKey": "first_device_type_code", "fieldName": "设备大类", "displayForm": "select", "source": "device_first_type", "dictMap": {"device_first_type_collector": "采集器", "device_first_type_trade": "行业设备", "device_first_type_concentrator": "集中器", "device_first_type_remote_spreadsheet": "远传表", "device_first_type_iot": "iot设备"}, "isMultiple": 0, "fieldValues": "device_first_type_iot"}], [{"fieldType": 1, "fieldKey": "site_code", "fieldName": "所属站点", "displayForm": "select", "source": "site", "dictMap": {"1943192319004758016": "乐山站点", "111111": "龚电家园", "1943278388035399680": "乐山站点2", "1943191054883475456": "乐轧泵房", "1940608774704869376": "邦德加压站"}, "isMultiple": 0, "fieldValues": "1943192319004758016"}, {"fieldType": 1, "fieldKey": "device_name", "fieldName": "设备名称", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "什么剂"}, {"fieldType": 1, "fieldKey": "second_device_type_code", "fieldName": "设备类型", "displayForm": "select", "source": "iot_device_type", "dictMap": {"start_meter": "电子围栏", "pressure_sensor": "压力传感器", "flowmeter": "流量计", "detector": "噪声听漏仪", "iot_device_type_wqtm": "水质检测仪"}, "isMultiple": 0, "fieldValues": "start_meter"}, {"fieldType": 1, "fieldKey": "equipment_time", "fieldName": "设备生产日期", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "2023.11.03"}, {"fieldType": 1, "fieldKey": "picture", "fieldName": "设备图片", "displayForm": "file", "source": null, "dictMap": {"1943565950683693056": "1.jpeg::https://minio.zhiyou-tec.com/dev-iot/120250711150006_7870edae-09fd-47df-9487-ebe35b75dc7b.jpeg"}, "isMultiple": 0, "fieldValues": "1943565950683693056"}, {"fieldType": 1, "fieldKey": "describe", "fieldName": "设备描述", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "啥的"}, {"fieldType": 1, "fieldKey": "serial_number", "fieldName": "设备序列号", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "v1111111"}, {"fieldType": 1, "fieldKey": "factory_code", "fieldName": "所属厂商", "displayForm": "select", "source": "manufacturer", "dictMap": {"M.C_8148600153450807296": "重庆怡源水表公司", "M.C_8148290270440521728": "四川水利仪器设备有限公司\t", "M.C_8163119358158110720": "天津蓝瑄仪表"}, "isMultiple": 0, "fieldValues": "M.C_8148290270440521728"}, {"fieldType": 1, "fieldKey": "model_number", "fieldName": "型号", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": ""}, {"fieldType": 1, "fieldKey": "address", "fieldName": "所属位置", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "风格"}, {"fieldType": 1, "fieldKey": "longitude", "fieldName": "经度", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "103.637786"}, {"fieldType": 1, "fieldKey": "latitude", "fieldName": "纬度", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "29.494184"}, {"fieldType": 1, "fieldKey": "online_status", "fieldName": "设备在线状态", "displayForm": "switch", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": ""}, {"fieldType": 1, "fieldKey": "status", "fieldName": "设备使用状态", "displayForm": "switch", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "0"}, {"fieldType": 4, "fieldKey": "protocol_code", "fieldName": "绑定协议", "displayForm": "select", "source": "protocol", "dictMap": {"PROTOCOL0001": "天津蓝瑄水表数据解析协议", "PROTOCOL0003": "西川府星水表数据解析协议", "PROTOCOL0002": "成都声立德克超声波大表数据解析协议"}, "isMultiple": 0, "fieldValues": "PROTOCOL0003"}, {"fieldType": 1, "fieldKey": "device_code", "fieldName": "设备编码", "displayForm": "text", "source": null, "dictMap": null, "isMultiple": 0, "fieldValues": "1945411788595544064"}, {"fieldType": 1, "fieldKey": "first_device_type_code", "fieldName": "设备大类", "displayForm": "select", "source": "device_first_type", "dictMap": {"device_first_type_collector": "采集器", "device_first_type_trade": "行业设备", "device_first_type_concentrator": "集中器", "device_first_type_remote_spreadsheet": "远传表", "device_first_type_iot": "iot设备"}, "isMultiple": 0, "fieldValues": "device_first_type_iot"}]]}