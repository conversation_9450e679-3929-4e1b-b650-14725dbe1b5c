/**
 * @fileoverview 应用程序主入口文件
 * @description 负责初始化Vue应用、配置BCGIS SDK、设置Cesium环境并挂载应用
 * <AUTHOR>
 * @version 1.0.0
 */

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import 'bcgis-sdk/dist/bcgis.core.min.css';
import '@/assets/css/index.scss';
import App from './App.vue';
import router from './router';
import { default as BCGIS } from 'bcgis-sdk/dist/bcgis.base.min';
import BcCore from 'bcgis-sdk/dist/bcgis.core.min';
import 'maplibre-gl/dist/maplibre-gl.css';
import 'virtual:svg-icons-register'; // 导入 SVG 图标
import { createLatias, useTokenRouterGuard, useToken } from '@latias/vue';
import { authToken } from './api/login';
import localCache from '@/utils/auth';
import { useAnalysisModeStore } from '@/stores/AnalysisModeStore';
const initApp = () => {
  // 将BCGIS SDK挂载到全局对象上，供整个应用使用
  globalThis.BC = BCGIS;

  // @ts-ignore
  // 注册BCGIS核心模块
  BC.use(BcCore);

  /**
   * @description 处理路由导致的Cesium资源文件访问路径问题
   * @details 根据当前路由层级动态调整Cesium资源文件的基础URL
   */
  // 计算当前路由的层级深度
  const count = window.location.pathname.split('/').length;
  // @ts-ignore
  let baseUrl = BC.baseUrl;

  // // 根据路由层级调整资源文件路径
  for (let i = 1; i < count; i++) {
    // @ts-ignore
    baseUrl = '../' + baseUrl;
  }
  // const latias = createLatias({
  //   // 平台code
  //   appId: 'efb6f5af9ec8c7bb8db87d1592887636',
  //   // latias鉴权服务网关地址
  //   gateway: 'http://************:7834/api/v1',
  // });

  /**
   * @description BCGIS SDK就绪后的回调函数
   * @details 在SDK完全加载后初始化Cesium配置并启动Vue应用
   */
  // @ts-ignore
  BC.ready(() => {
    // 获取Cesium命名空间
    const { Cesium } = BC.Namespace;

    //如果是生产环境，则设置Cesium模块的基础URL
    // if (import.meta.env.PROD) {
    //   baseUrl = '../../pipeweb/libs/bcgis-sdk/resources/';
    // }
    // @ts-ignore
    // 设置Cesium模块的基础URL，解决资源文件路径问题
    Cesium.buildModuleUrl.setBaseUrl(baseUrl);

    /**
     * @description 设置Cesium Ion访问令牌
     * @details 用于访问Cesium Ion云服务的地形、影像等数据
     */
    Cesium.Ion.defaultAccessToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjZmJiYmIyOC05YWVkLTQ5NTgtYjc4NS03M2U5YmU5ZTAwMDYiLCJpZCI6MjA1MjYsImlhdCI6MTcxOTk5MzQzN30.n04rPInlX9foPzQI-vbQflpxs6a-SCIKRDJ5pJp-CEo';

    /**
     * @description 创建并配置Vue应用实例
     */
    const app = createApp(App);

    // 注册Pinia状态管理
    app.use(createPinia());

    // 注册Vue Router路由
    app.use(router);
    // app.use(latias as any)
    // 将应用挂载到DOM元素上
    app.mount('#app');
    // 开启 token 识别
    // useTokenRouterGuard()
  });
};

/**
 * @description 统一的URL参数检测函数
 * @details 兼容Hash路由模式，支持参数在hash之前或之后
 * @param {string} paramName - 参数名称
 * @returns {string | null} 参数值
 */
const getUrlParam = (paramName: string): string | null => {
  // 先检查 search 参数（hash 之前）
  // 格式：http://domain.com/?onlyAnalysis=1#/path
  const searchParams = new URLSearchParams(window.location.search);
  if (searchParams.has(paramName)) {
    return searchParams.get(paramName);
  }

  // 再检查 hash 中的参数（hash 之后）
  // 格式：http://domain.com/#/path?onlyAnalysis=1
  const hash = window.location.hash;
  const queryIndex = hash.indexOf('?');
  if (queryIndex !== -1) {
    const hashParams = new URLSearchParams(hash.substring(queryIndex + 1));
    return hashParams.get(paramName);
  }

  return null;
};

/**
 * @description 检测是否为仅分析模式
 * @returns {boolean} 是否为仅分析模式
 */
const isOnlyAnalysis = (): boolean => {
  return getUrlParam('onlyAnalysis') === '1';
};

const isOnlyPipeHistory = (): boolean => {
  return getUrlParam('onlyPipeHistory') === '1';
};

const getToken = () => {
  //从当前地址获取token
  const token = window.location.search.split('token=')[1];
  return token;
};

const authLogin = (token: string) => {
  authToken(token)
    .then((res) => {
      if (res.code === 200 && res.data) {
        localCache.setCache('Latias', token);
        initApp();
      } else {
        debugger;
        window.location.href = 'https://engine.lshywater.cn/#/login';
      }
    })
    .catch((err) => {
      console.log(err);
      debugger;
      window.location.href = 'https://engine.lshywater.cn/#/login';
    });
};
//开发模式直接初始化
if (import.meta.env.DEV) {
  // const response = await fetch('data/token.json')
  // const json = await response.json()
  // const token = json.token;

  localCache.setCache(
    'Latias',
    'Bearer lWOzojQ33vy971JWDmivQ4lWlFlnGcNSLfIig4jNI6pyRDiMSissuSGZeFek8642063214036582400'
  );
  initApp();

  // 在应用初始化后检测分析模式
  setTimeout(() => {
    const onlyPipeHistory = isOnlyPipeHistory();
    if (onlyPipeHistory) {
      console.log('✅ 启用仅管网历史模式');
      useAnalysisModeStore().setAnalysisMode(true);
      useAnalysisModeStore().setPipeHistoryMode(true);
    }

    const onlyAnalysis = isOnlyAnalysis();
    console.log('🔍 检测到URL参数 onlyAnalysis:', onlyAnalysis);
    console.log('🔍 当前URL:', window.location.href);
    console.log('🔍 search参数:', window.location.search);
    console.log('🔍 hash参数:', window.location.hash);

    if (onlyAnalysis) {
      console.log('✅ 启用仅分析模式');
      useAnalysisModeStore().setAnalysisMode(true);
    }
  }, 100);
} else {
  // 生产模式也需要检测分析模式
  setTimeout(() => {
    const onlyAnalysis = isOnlyAnalysis();
    if (onlyAnalysis) {
      console.log('✅ 生产模式启用仅分析模式');
      useAnalysisModeStore().setAnalysisMode(true);
    }
  }, 100);
  const urlParamToken = getUrlParam('token') || getToken();
  //先判断url参数中是否带有token，如果是直接鉴权，如果没有再查看localStorage中是否有token
  if (!urlParamToken) {
    const localStorageToken = localCache.getCache('Latias');
    if (!localStorageToken) {
      window.location.href = 'https://engine.lshywater.cn/#/login';
      localCache.clearCache();
    } else {
      authLogin(localStorageToken);
    }
  } else {
    authLogin(urlParamToken);
  }
}
