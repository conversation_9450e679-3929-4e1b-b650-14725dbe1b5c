<template>
  <page-card
    class="two-line-hang-panel"
    title="两边栓点"
    @closeCard="closeCard"
  >
    <!-- 参照边选择区域 -->
    <!-- <el-row class="button-section" flex="~ row justify-start">
      <el-button
        type="primary"
        class="select-btn h-9"
        :loading="isSelectingLine1"
        @click="handleSelectLine1"
      >
        选择参照边1
      </el-button>
      <el-button
        type="primary"
        class="select-btn h-9"
        :loading="isSelectingLine2"
        @click="handleSelectLine2"
      >
        选择参照边2
      </el-button>
    </el-row> -->

    <div class="brd">
      <div class="title">参照边1</div>
      <div>
        <el-button
          type="primary"
          class="bg-#E3EDFF color-#1966FF border-color-#1966FF w-25 h-9"
          :loading="isSelectingLine1"
          @click="handleSelectLine1"
        >
          选择
        </el-button>
      </div>
      <div class="distance-input-group">
        <el-text class="distance-label">参照边1偏移距离(米)：</el-text>
        <el-input
          v-model.number="offset1"
          type="number"
          placeholder="请输入偏移距离"
          :min="0.1"
          :step="0.1"
          :disabled="!referenceLine1"
          class="distance-input"
        ></el-input>
      </div>
    </div>

    <div class="brd">
      <div class="title">参照边2</div>
      <div>
        <el-button
          type="primary"
          class="bg-#E3EDFF color-#1966FF border-color-#1966FF w-25 h-9"
          :loading="isSelectingLine2"
          @click="handleSelectLine2"
        >
          选择
        </el-button>
      </div>
      <div class="distance-input-group">
        <el-text class="distance-label">参照边2偏移距离(米)：</el-text>
        <el-input
          v-model.number="offset2"
          type="number"
          placeholder="请输入偏移距离"
          :min="0.1"
          :step="0.1"
          :disabled="!referenceLine2"
          class="distance-input"
        ></el-input>
      </div>
    </div>
    <!-- 偏移距离设置区域 -->
    <!-- <el-row class="distance-section" flex="~ row justify-start gap-4">
      <el-col :span="12">
        <div class="distance-input-group">
          <el-text class="distance-label">参照边1偏移距离(米)：</el-text>
          <el-input
            v-model.number="offset1"
            type="number"
            placeholder="请输入偏移距离"
            :min="0.1"
            :step="0.1"
            :disabled="!referenceLine1"
            class="distance-input"
          ></el-input>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="distance-input-group">
          <el-text class="distance-label">参照边2偏移距离(米)：</el-text>
          <el-input
            v-model.number="offset2"
            type="number"
            placeholder="请输入偏移距离"
            :min="0.1"
            :step="0.1"
            :disabled="!referenceLine2"
            class="distance-input"
          ></el-input>
        </div>
      </el-col>
    </el-row> -->

    <!-- 计算按钮区域 -->
    <el-row class="calculation-section" flex="~ row justify-start">
      <el-button
        type="primary"
        class="calculate-btn h-9"
        :loading="isCalculating"
        @click="handleCalculate"
        :disabled="!canCalculate"
      >
        计算栓点
      </el-button>
      <el-button
        class="clear-btn h-9"
        @click="handleClear"
        :disabled="isCalculating || isSelectingLine1 || isSelectingLine2"
      >
        清除
      </el-button>
    </el-row>

    <!-- 选择的参照边显示区域 -->
    <div
      v-if="referenceLine1 || referenceLine2"
      grid="~ cols-2 gap-4"
      class="lines-section"
    >
      <el-row v-if="referenceLine1" class="line-row">
        <el-col :span="24">
          <el-text class="line-label">参照边1：</el-text>
          <div class="line-info">
            <span class="line-text">
              管线编号: {{ referenceLine1.gxbm || "无" }}
            </span>
            <span class="coordinate-text">
              长度: {{ referenceLine1.length?.toFixed(2) || "未知" }}米
            </span>
            <span v-if="offset1" class="distance-text">
              偏移距离: {{ offset1 }}米
            </span>
          </div>
        </el-col>
      </el-row>
      <el-row v-if="referenceLine2" class="line-row">
        <el-col :span="24">
          <el-text class="line-label">参照边2：</el-text>
          <div class="line-info">
            <span class="line-text">
              管线编号: {{ referenceLine2.gxbm || "无" }}
            </span>
            <span class="coordinate-text">
              长度: {{ referenceLine2.length?.toFixed(2) || "未知" }}米
            </span>
            <span v-if="offset2" class="distance-text">
              偏移距离: {{ offset2 }}米
            </span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 计算结果显示区域 -->
    <div
      v-if="showResult"
      class="result-section"
      :class="calculationResult.success ? 'success' : 'error'"
    >
      <el-icon v-if="calculationResult.success" class="color-#00B42A"
        ><CircleCheckFilled
      /></el-icon>
      <el-icon v-else class="color-#FF7373"><CircleCloseFilled /></el-icon>

      <el-text class="result-label" v-if="calculationResult.success"
        >计算结果：</el-text
      >
      <span :class="'result-text'">{{ calculationResult.message }}</span>
    </div>
    <!-- 栓点结果表格 -->
    <el-row
      v-if="showResultTable && hangPoints.length > 0"
      class="table-section"
    >
      <el-col :span="24">
        <div class="title mb-2.5">计算出的栓点位置</div>
        <el-table
          :data="hangPoints"
          style="width: 100%; height: 130px"
          class="routeCt"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
          ></el-table-column>
          <el-table-column label="栓点编号" width="90">
            <template #default="scope"> 栓点{{ scope.$index + 1 }} </template>
          </el-table-column>
          <el-table-column prop="lng" label="经度" width="120">
            <template #default="scope">
              {{ scope.row.lng.toFixed(8) }}
            </template>
          </el-table-column>
          <el-table-column prop="lat" label="纬度" width="120">
            <template #default="scope">
              {{ scope.row.lat.toFixed(8) }}
            </template>
          </el-table-column>
          <el-table-column prop="offset1" label="到参照边1偏移(米)" width="140">
            <template #default="scope">
              {{ scope.row.offset1.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="offset2" label="到参照边2偏移(米)" width="140">
            <template #default="scope">
              {{ scope.row.offset2.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="type"
            label="交点类型"
            width="110"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.type }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="90" fixed="right">
            <template #default="scope">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleAddNode(scope.row)"
              >
                新增管点
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import * as turf from "@turf/turf";
import { CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue";
/**
 * 处理关闭面板
 */
const closeCard = () => {
  try {
    console.log("关闭两边栓点面板");

    // 先清理所有数据和图层
    handleClear();

    // 通知父组件关闭面板
    emit("close");

    ElMessage.info("已关闭两边栓点功能");
  } catch (error) {
    console.error("关闭面板失败:", error);
    ElMessage.error("关闭面板失败");
  }
};

/**
 * 临时图层ID常量
 */
const TEMP_LAYER_IDS = {
  REFERENCE_LINE1_SOURCE: "two-line-hang-line1-source",
  REFERENCE_LINE1_LAYER: "two-line-hang-line1-layer",
  REFERENCE_LINE2_SOURCE: "two-line-hang-line2-source",
  REFERENCE_LINE2_LAYER: "two-line-hang-line2-layer",
  // 参照线1的正负偏移平行线
  PARALLEL_LINE1_POSITIVE_SOURCE: "two-line-hang-parallel1-pos-source",
  PARALLEL_LINE1_POSITIVE_LAYER: "two-line-hang-parallel1-pos-layer",
  PARALLEL_LINE1_NEGATIVE_SOURCE: "two-line-hang-parallel1-neg-source",
  PARALLEL_LINE1_NEGATIVE_LAYER: "two-line-hang-parallel1-neg-layer",
  // 参照线2的正负偏移平行线
  PARALLEL_LINE2_POSITIVE_SOURCE: "two-line-hang-parallel2-pos-source",
  PARALLEL_LINE2_POSITIVE_LAYER: "two-line-hang-parallel2-pos-layer",
  PARALLEL_LINE2_NEGATIVE_SOURCE: "two-line-hang-parallel2-neg-source",
  PARALLEL_LINE2_NEGATIVE_LAYER: "two-line-hang-parallel2-neg-layer",
  HANG_POINTS_SOURCE: "two-line-hang-points-source",
  HANG_POINTS_LAYER: "two-line-hang-points-layer",
} as const;

/**
 * 定义参照线接口
 */
interface ReferenceLine {
  gxbm: string; // 管线编号
  geometry: any; // 几何信息
  coordinates: number[][]; // 坐标数组
  length?: number; // 线长度
  properties: any; // 属性信息
}

/**
 * 定义栓点接口
 */
interface HangPoint {
  lng: number;
  lat: number;
  offset1: number; // 到参照边1的偏移距离
  offset2: number; // 到参照边2的偏移距离
  direction1: "positive" | "negative"; // 参照边1的偏移方向
  direction2: "positive" | "negative"; // 参照边2的偏移方向
  type: string; // 交点类型描述，如 "正偏移-正偏移"
}

/**
 * 定义计算结果接口
 */
interface CalculationResult {
  success: boolean;
  message: string;
}

/**
 * 响应式数据状态
 */
// 参照线相关状态
const referenceLine1 = ref<ReferenceLine | null>(null);
const referenceLine2 = ref<ReferenceLine | null>(null);
const offset1 = ref<number>(0);
const offset2 = ref<number>(0);
const isSelectingLine1 = ref<boolean>(false);
const isSelectingLine2 = ref<boolean>(false);
const isCalculating = ref<boolean>(false);

// 计算结果状态
const showResult = ref<boolean>(false);
const calculationResult = ref<CalculationResult>({
  success: false,
  message: "",
});
const hangPoints = ref<HangPoint[]>([]);
const showResultTable = ref<boolean>(false);

/**
 * 计算属性：是否可以进行计算
 */
const canCalculate = computed(() => {
  return (
    referenceLine1.value &&
    referenceLine2.value &&
    offset1.value > 0 &&
    offset2.value > 0 &&
    !isSelectingLine1.value &&
    !isSelectingLine2.value
  );
});

/**
 * 从地图选择管线
 * @returns Promise<ReferenceLine> 返回选择的管线信息
 */
const selectLineFromMap = (): Promise<ReferenceLine> => {
  return new Promise((resolve, reject) => {
    try {
      const map = AppMaplibre.getMap();

      if (!map) {
        throw new Error("地图实例未初始化");
      }

      // 修改鼠标样式提示用户可以点击选择
      map.getCanvas().style.cursor = "pointer";

      // 绑定单次点击事件
      map.once("click", (e: any) => {
        try {
          // 恢复默认鼠标样式
          map.getCanvas().style.cursor = "";
          const degree = 10;
          // 查询点击位置的管线要素
          const features = map.queryRenderedFeatures(
            [
              [e.point.x - degree / 2, e.point.y - degree / 2],
              [e.point.x + degree / 2, e.point.y + degree / 2],
            ],
            {
              layers: ["mvt_pipeLine"],
            }
          );

          if (features.length === 0) {
            throw new Error("请点击管线位置");
          }

          const feature: any = features[0];
          const gxbm = feature.properties?.gxbm || feature.properties?.GXBM;

          if (!gxbm) {
            throw new Error("该管线缺少编号信息");
          }

          // 计算管线长度
          const lineString = turf.lineString(feature.geometry.coordinates);
          const length = turf.length(lineString, { units: "meters" });

          const line: ReferenceLine = {
            gxbm: gxbm,
            geometry: feature.geometry,
            coordinates: feature.geometry.coordinates,
            length: length,
            properties: feature.properties,
          };

          resolve(line);
        } catch (error) {
          // 恢复默认鼠标样式
          map.getCanvas().style.cursor = "";
          reject(error);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * 延伸线段长度
 * @param lineString 原始线段
 * @param extensionFactor 延伸倍数，默认10倍
 * @returns 延伸后的线段
 */
const extendLineString = (
  lineString: any,
  extensionFactor: number = 5
): any => {
  try {
    const coordinates = lineString.geometry.coordinates;

    if (coordinates.length < 2) {
      console.warn("线段坐标点不足，无法延伸");
      return lineString;
    }

    // 计算原始线段长度
    const originalLength = turf.length(lineString, { units: "kilometers" });
    const extensionLength = originalLength * extensionFactor;

    console.log(
      `线段延伸：原长度 ${originalLength.toFixed(
        3
      )}km，延伸长度 ${extensionLength.toFixed(3)}km`
    );

    // 获取起点和终点
    const startPoint = turf.point(coordinates[0]);
    const endPoint = turf.point(coordinates[coordinates.length - 1]);

    // 计算起点到终点的方位角
    const forwardBearing = turf.bearing(startPoint, endPoint);
    // 计算终点到起点的方位角（反向）
    const backwardBearing = turf.bearing(endPoint, startPoint);

    // 从起点向反方向延伸
    const extendedStart = turf.destination(
      startPoint,
      extensionLength,
      backwardBearing,
      { units: "kilometers" }
    );

    // 从终点向正方向延伸
    const extendedEnd = turf.destination(
      endPoint,
      extensionLength,
      forwardBearing,
      { units: "kilometers" }
    );

    // 创建延伸后的线段坐标数组
    const extendedCoordinates = [
      extendedStart.geometry.coordinates,
      ...coordinates,
      extendedEnd.geometry.coordinates,
    ];

    // 创建新的延伸线段
    const extendedLineString = turf.lineString(extendedCoordinates);

    console.log(
      `线段延伸完成：新长度 ${turf
        .length(extendedLineString, { units: "kilometers" })
        .toFixed(3)}km`
    );

    return extendedLineString;
  } catch (error) {
    console.error("延伸线段失败:", error);
    return lineString; // 出错时返回原始线段
  }
};

/**
 * 添加参照线的临时图层显示
 * @param line 参照线
 * @param lineNumber 线编号：1 | 2
 */
const addReferenceLineLayer = (line: ReferenceLine, lineNumber: 1 | 2) => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      console.error("地图实例不存在");
      return;
    }

    const sourceId =
      lineNumber === 1
        ? TEMP_LAYER_IDS.REFERENCE_LINE1_SOURCE
        : TEMP_LAYER_IDS.REFERENCE_LINE2_SOURCE;
    const layerId =
      lineNumber === 1
        ? TEMP_LAYER_IDS.REFERENCE_LINE1_LAYER
        : TEMP_LAYER_IDS.REFERENCE_LINE2_LAYER;
    const labelLayerId = `${layerId}-label`;
    // 使用更明显的颜色：参照边1绿色，参照边2黄色
    const color = lineNumber === 1 ? "#00ff00" : "#ff7d00";

    const featureData = {
      type: "FeatureCollection" as const,
      features: [
        {
          type: "Feature" as const,
          geometry: line.geometry,
          properties: {
            type: `reference-line-${lineNumber}`,
            gxbm: line.gxbm || "",
            lineNumber: lineNumber,
            length: line.length || 0,
          },
        },
      ],
    };

    // 检查并更新数据源
    const existingSource = map.getSource(sourceId);
    if (existingSource) {
      // 如果数据源已存在，直接更新数据
      (existingSource as any).setData(featureData);
      console.log(`已更新参照边${lineNumber}数据源:`, line.gxbm);
    } else {
      // 数据源不存在，创建新的数据源和图层
      map.addSource(sourceId, {
        type: "geojson",
        data: featureData,
      });

      // 添加线图层（更明显）
      map.addLayer({
        id: layerId,
        type: "line",
        source: sourceId,
        paint: {
          "line-color": color,
          "line-width": 5,
          "line-opacity": 0.8,
        },
      });

      // 添加标签图层
      map.addLayer({
        id: labelLayerId,
        type: "symbol",
        source: sourceId,
        layout: {
          "text-field": ["concat", "参照边", ["get", "lineNumber"]],
          "text-font": ["Open Sans Regular", "Arial Unicode MS Regular"],
          "text-offset": [0, -1],
          "text-anchor": "bottom",
          "text-size": ["interpolate", ["linear"], ["zoom"], 10, 10, 18, 16],
          "text-allow-overlap": true,
          "text-ignore-placement": true,
          "symbol-placement": "line-center",
        },
        paint: {
          "text-color": color,
          "text-halo-color": "#ffffff",
          "text-halo-width": 2,
          "text-halo-blur": 0.5,
        },
      });

      console.log(`已创建参照边${lineNumber}图层:`, line.gxbm);
    }
  } catch (error) {
    console.error(`添加参照边${lineNumber}临时图层失败:`, error);
  }
};

/**
 * 创建双侧平行线并显示
 * @param line 参照线
 * @param offsetMeters 偏移距离（米）
 * @param lineNumber 线编号：1 | 2
 * @returns 返回正负两条平行线
 */
const addParallelLineLayer = (
  line: ReferenceLine,
  offsetMeters: number,
  lineNumber: 1 | 2
) => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      console.error("地图实例不存在");
      return { positive: null, negative: null };
    }

    // 获取正负偏移的图层ID
    const positiveSourceId =
      lineNumber === 1
        ? TEMP_LAYER_IDS.PARALLEL_LINE1_POSITIVE_SOURCE
        : TEMP_LAYER_IDS.PARALLEL_LINE2_POSITIVE_SOURCE;
    const positiveLayerId =
      lineNumber === 1
        ? TEMP_LAYER_IDS.PARALLEL_LINE1_POSITIVE_LAYER
        : TEMP_LAYER_IDS.PARALLEL_LINE2_POSITIVE_LAYER;
    const negativeSourceId =
      lineNumber === 1
        ? TEMP_LAYER_IDS.PARALLEL_LINE1_NEGATIVE_SOURCE
        : TEMP_LAYER_IDS.PARALLEL_LINE2_NEGATIVE_SOURCE;
    const negativeLayerId =
      lineNumber === 1
        ? TEMP_LAYER_IDS.PARALLEL_LINE1_NEGATIVE_LAYER
        : TEMP_LAYER_IDS.PARALLEL_LINE2_NEGATIVE_LAYER;

    // 颜色设置：参照线1绿色系，参照线2蓝色系
    const positiveColor = lineNumber === 1 ? "#00cc0080" : "#0066cc80"; // 正偏移：较深色
    const negativeColor = lineNumber === 1 ? "#66ff6680" : "#66ccff80"; // 负偏移：较浅色

    // 创建原始线段并延伸
    const originalLineString = turf.lineString(line.coordinates);
    const extendedLineString = extendLineString(originalLineString, 5);

    // 使用延伸后的线段生成双侧平行线
    const positiveOffsetLine = turf.lineOffset(
      extendedLineString,
      offsetMeters / 1000,
      { units: "kilometers" }
    );
    const negativeOffsetLine = turf.lineOffset(
      extendedLineString,
      -offsetMeters / 1000,
      { units: "kilometers" }
    );

    // 创建或更新正偏移平行线
    const existingPositiveSource = map.getSource(positiveSourceId);
    if (existingPositiveSource) {
      (existingPositiveSource as any).setData(positiveOffsetLine);
    } else {
      map.addSource(positiveSourceId, {
        type: "geojson",
        data: positiveOffsetLine,
      });

      map.addLayer({
        id: positiveLayerId,
        type: "line",
        source: positiveSourceId,
        paint: {
          "line-color": positiveColor,
          "line-width": 3,
          "line-opacity": 0.8,
          "line-dasharray": [4, 2], // 实线样式，区分正偏移
        },
      });
    }

    // 创建或更新负偏移平行线
    const existingNegativeSource = map.getSource(negativeSourceId);
    if (existingNegativeSource) {
      (existingNegativeSource as any).setData(negativeOffsetLine);
    } else {
      map.addSource(negativeSourceId, {
        type: "geojson",
        data: negativeOffsetLine,
      });

      map.addLayer({
        id: negativeLayerId,
        type: "line",
        source: negativeSourceId,
        paint: {
          "line-color": negativeColor,
          "line-width": 3,
          "line-opacity": 0.8,
          "line-dasharray": [2, 4], // 不同虚线样式，区分负偏移
        },
      });
    }

    console.log(
      `已创建参照线${lineNumber}的双侧延伸平行线（延伸10倍长度），偏移: ±${offsetMeters}米`
    );

    return {
      positive: positiveOffsetLine,
      negative: negativeOffsetLine,
    };
  } catch (error) {
    console.error(`添加参照线${lineNumber}双侧平行线失败:`, error);
    return { positive: null, negative: null };
  }
};

/**
 * 计算两条平行线的交点
 * @param line1 第一条参照线
 * @param offset1 第一条线的偏移距离（米）
 * @param line2 第二条参照线
 * @param offset2 第二条线的偏移距离（米）
 * @returns 交点数组
 */
const calculateLineIntersection = (
  line1: ReferenceLine,
  offset1: number,
  line2: ReferenceLine,
  offset2: number
): HangPoint[] => {
  try {
    console.log("开始计算双侧平行线交点:", {
      line1: line1.gxbm,
      offset1: offset1,
      line2: line2.gxbm,
      offset2: offset2,
    });

    // 创建原始线段并延伸以提高交点计算成功率
    const originalLineString1 = turf.lineString(line1.coordinates);
    const originalLineString2 = turf.lineString(line2.coordinates);
    const extendedLineString1 = extendLineString(originalLineString1, 5);
    const extendedLineString2 = extendLineString(originalLineString2, 5);

    // 使用延伸后的线段生成双侧平行线
    const line1Positive = turf.lineOffset(extendedLineString1, offset1 / 1000, {
      units: "kilometers",
    });
    const line1Negative = turf.lineOffset(
      extendedLineString1,
      -offset1 / 1000,
      { units: "kilometers" }
    );

    // 参照线2的正负偏移平行线
    const line2Positive = turf.lineOffset(extendedLineString2, offset2 / 1000, {
      units: "kilometers",
    });
    const line2Negative = turf.lineOffset(
      extendedLineString2,
      -offset2 / 1000,
      { units: "kilometers" }
    );

    const hangPoints: HangPoint[] = [];
    let intersectionCount = 0;

    // 计算4种交点组合
    const combinations = [
      {
        line1: line1Positive,
        line2: line2Positive,
        dir1: "positive",
        dir2: "positive",
        type: "正偏移-正偏移",
      },
      {
        line1: line1Positive,
        line2: line2Negative,
        dir1: "positive",
        dir2: "negative",
        type: "正偏移-负偏移",
      },
      {
        line1: line1Negative,
        line2: line2Positive,
        dir1: "negative",
        dir2: "positive",
        type: "负偏移-正偏移",
      },
      {
        line1: line1Negative,
        line2: line2Negative,
        dir1: "negative",
        dir2: "negative",
        type: "负偏移-负偏移",
      },
    ];

    combinations.forEach((combo) => {
      try {
        const intersections = turf.lineIntersect(combo.line1, combo.line2);

        if (intersections && intersections.features.length > 0) {
          intersections.features.forEach((intersection) => {
            if (intersection.geometry && intersection.geometry.coordinates) {
              const coords = intersection.geometry.coordinates;
              intersectionCount++;

              hangPoints.push({
                lng: coords[0],
                lat: coords[1],
                offset1: offset1,
                offset2: offset2,
                direction1: combo.dir1 as "positive" | "negative",
                direction2: combo.dir2 as "positive" | "negative",
                type: combo.type,
              });

              console.log(`交点${intersectionCount} (${combo.type}):`, {
                坐标: { lng: coords[0], lat: coords[1] },
                偏移距离1: `${
                  combo.dir1 === "positive" ? "+" : "-"
                }${offset1}米`,
                偏移距离2: `${
                  combo.dir2 === "positive" ? "+" : "-"
                }${offset2}米`,
              });
            }
          });
        }
      } catch (comboError) {
        console.warn(`计算${combo.type}交点时出错:`, comboError);
      }
    });

    if (hangPoints.length === 0) {
      throw new Error(
        "所有双侧平行线组合都不相交，请调整偏移距离或选择其他参照边"
      );
    }

    console.log(
      `成功计算出 ${hangPoints.length} 个交点（来自${intersectionCount}个交点计算）`
    );
    return hangPoints;
  } catch (error) {
    console.error("计算双侧平行线交点失败:", error);
    throw error;
  }
};

/**
 * 添加栓点结果图层
 * @param points 栓点数组
 */
const addHangPointsLayer = (points: HangPoint[]) => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      console.error("地图实例不存在");
      return;
    }

    console.log("开始创建栓点图层，数据:", points);

    const features = points.map((point, index) => ({
      type: "Feature" as const,
      geometry: {
        type: "Point" as const,
        coordinates: [point.lng, point.lat],
      },
      properties: {
        type: "hang-point",
        index: index + 1,
        offset1: point.offset1,
        offset2: point.offset2,
        direction1: point.direction1,
        direction2: point.direction2,
        intersectionType: point.type,
      },
    }));

    const featureData = {
      type: "FeatureCollection" as const,
      features: features,
    };

    console.log("栓点要素数据:", featureData);

    // 检查并更新数据源
    const existingSource = map.getSource(TEMP_LAYER_IDS.HANG_POINTS_SOURCE);
    if (existingSource) {
      // 如果数据源已存在，直接更新数据
      (existingSource as any).setData(featureData);
      console.log("已更新栓点数据源");
    } else {
      // 数据源不存在，创建新的数据源和图层
      map.addSource(TEMP_LAYER_IDS.HANG_POINTS_SOURCE, {
        type: "geojson",
        data: featureData,
      });

      console.log("已创建栓点数据源:", TEMP_LAYER_IDS.HANG_POINTS_SOURCE);

      // 添加圆形标记图层
      map.addLayer({
        id: TEMP_LAYER_IDS.HANG_POINTS_LAYER,
        type: "circle",
        source: TEMP_LAYER_IDS.HANG_POINTS_SOURCE,
        paint: {
          "circle-radius": ["interpolate", ["linear"], ["zoom"], 4, 6, 6, 10],
          "circle-color": "#ff4400",
          "circle-stroke-width": 2,
          "circle-stroke-color": "#b30000",
          "circle-opacity": 0.9,
          "circle-stroke-opacity": 1,
        },
      });

      console.log("已创建栓点圆形图层:", TEMP_LAYER_IDS.HANG_POINTS_LAYER);

      // 添加标签图层
      const labelLayerId = `${TEMP_LAYER_IDS.HANG_POINTS_LAYER}-label`;
      map.addLayer({
        id: labelLayerId,
        type: "symbol",
        source: TEMP_LAYER_IDS.HANG_POINTS_SOURCE,
        layout: {
          "text-field": ["concat", "栓点", ["get", "index"]],
          "text-font": ["Open Sans Regular", "Arial Unicode MS Regular"],
          "text-offset": [0, -1.5],
          "text-anchor": "bottom",
          "text-size": ["interpolate", ["linear"], ["zoom"], 10, 10, 18, 16],
          "text-allow-overlap": true,
          "text-ignore-placement": true,
        },
        paint: {
          "text-color": "#b30000",
          "text-halo-color": "#ffffff",
          "text-halo-width": 2,
          "text-halo-blur": 0.5,
        },
      });

      console.log("已创建栓点标签图层");
    }
  } catch (error) {
    console.error("添加栓点临时图层失败:", error);
  }
};

/**
 * 清除栓点相关的临时图层
 */
const clearHangPointsLayers = () => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      return;
    }

    // 清理栓点图层和标签图层
    const hangPointLayerId = TEMP_LAYER_IDS.HANG_POINTS_LAYER;
    const hangPointLabelLayerId = `${hangPointLayerId}-label`;
    const hangPointSourceId = TEMP_LAYER_IDS.HANG_POINTS_SOURCE;

    // 移除栓点图层
    if (map.getLayer(hangPointLayerId)) {
      try {
        map.removeLayer(hangPointLayerId);
        console.log(`已移除栓点图层: ${hangPointLayerId}`);
      } catch (error) {
        console.warn(`移除栓点图层失败:`, error);
      }
    }

    // 移除栓点标签图层
    if (map.getLayer(hangPointLabelLayerId)) {
      try {
        map.removeLayer(hangPointLabelLayerId);
        console.log(`已移除栓点标签图层: ${hangPointLabelLayerId}`);
      } catch (error) {
        console.warn(`移除栓点标签图层失败:`, error);
      }
    }

    // 移除栓点数据源
    if (map.getSource(hangPointSourceId)) {
      try {
        map.removeSource(hangPointSourceId);
        console.log(`已移除栓点数据源: ${hangPointSourceId}`);
      } catch (error) {
        console.warn(`移除栓点数据源失败:`, error);
      }
    }

    console.log("已清除栓点相关图层");
  } catch (error) {
    console.error("清除栓点图层失败:", error);
  }
};

/**
 * 清除所有临时图层
 */
const clearAllTempLayers = () => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      return;
    }

    // 首先收集所有需要移除的图层ID
    const layersToRemove: string[] = [];
    const sourcesToRemove: string[] = [];

    Object.values(TEMP_LAYER_IDS).forEach((id) => {
      // 收集主图层
      if (map.getLayer(id)) {
        layersToRemove.push(id);
      }

      // 收集标签图层
      const labelLayerId = `${id}-label`;
      if (map.getLayer(labelLayerId)) {
        layersToRemove.push(labelLayerId);
      }

      // 收集数据源
      if (map.getSource(id)) {
        sourcesToRemove.push(id);
      }
    });

    // 先移除所有图层
    layersToRemove.forEach((layerId) => {
      try {
        map.removeLayer(layerId);
        console.log(`已移除图层: ${layerId}`);
      } catch (layerError) {
        console.warn(`移除图层 ${layerId} 时出错:`, layerError);
      }
    });

    // 再移除所有数据源
    sourcesToRemove.forEach((sourceId) => {
      try {
        map.removeSource(sourceId);
        console.log(`已移除数据源: ${sourceId}`);
      } catch (sourceError) {
        console.warn(`移除数据源 ${sourceId} 时出错:`, sourceError);
      }
    });

    console.log("已清除所有两边栓点临时图层");
  } catch (error) {
    console.error("清除临时图层失败:", error);
  }
};

/**
 * 处理选择参照边1
 */
const handleSelectLine1 = async (): Promise<void> => {
  try {
    if (isSelectingLine1.value) {
      return;
    }

    isSelectingLine1.value = true;
    ElMessage.info("请在地图上点击选择参照边1");

    const line = await selectLineFromMap();
    referenceLine1.value = line;

    // 添加参照线图层
    addReferenceLineLayer(line, 1);

    ElMessage.success(`已选择参照边1: ${line.gxbm}`);
  } catch (error) {
    console.error("选择参照边1失败:", error);
    ElMessage.error(
      `选择参照边1失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isSelectingLine1.value = false;
  }
};

/**
 * 处理选择参照边2
 */
const handleSelectLine2 = async (): Promise<void> => {
  try {
    if (isSelectingLine2.value) {
      return;
    }

    isSelectingLine2.value = true;
    ElMessage.info("请在地图上点击选择参照边2");

    const line = await selectLineFromMap();
    referenceLine2.value = line;

    // 添加参照线图层
    addReferenceLineLayer(line, 2);

    ElMessage.success(`已选择参照边2: ${line.gxbm}`);
  } catch (error) {
    console.error("选择参照边2失败:", error);
    ElMessage.error(
      `选择参照边2失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isSelectingLine2.value = false;
  }
};

/**
 * 处理计算栓点
 */
const handleCalculate = async (): Promise<void> => {
  try {
    if (!canCalculate.value) {
      ElMessage.warning("请完成参照边选择和偏移距离设置");
      return;
    }

    isCalculating.value = true;
    showResult.value = false;
    showResultTable.value = false;
    hangPoints.value = [];

    // 清除上次计算的栓点图层
    clearHangPointsLayers();

    ElMessage.info("正在计算栓点位置...");

    // 首先绘制平行线（无论计算是否成功都绘制）
    const parallelLines = addParallelLineLayer(
      referenceLine1.value!,
      offset1.value,
      1
    );
    const parallelLines2 = addParallelLineLayer(
      referenceLine2.value!,
      offset2.value,
      2
    );

    try {
      // 计算两条平行线的交点
      const intersections = calculateLineIntersection(
        referenceLine1.value!,
        offset1.value,
        referenceLine2.value!,
        offset2.value
      );

      if (intersections.length === 0) {
        throw new Error("无法计算出有效的栓点位置");
      }

      // 显示栓点
      addHangPointsLayer(intersections);

      // 更新结果
      hangPoints.value = intersections;
      calculationResult.value = {
        success: true,
        message: `成功计算出 ${intersections.length} 个栓点位置`,
      };
      showResult.value = true;
      showResultTable.value = true;

      ElMessage.success(`计算完成，共找到 ${intersections.length} 个栓点位置`);
    } catch (calculationError) {
      console.error("计算栓点失败:", calculationError);

      // 即使计算失败，也显示结果（包含错误信息），平行线已经绘制
      calculationResult.value = {
        success: false,
        message: `计算失败: ${
          calculationError instanceof Error
            ? calculationError.message
            : "未知错误"
        }`,
      };
      showResult.value = true;
      showResultTable.value = false; // 计算失败时不显示结果表格

      ElMessage.warning(
        `计算失败: ${
          calculationError instanceof Error
            ? calculationError.message
            : "未知错误"
        }，平行线已显示供参考`
      );
    }
  } catch (error) {
    console.error("启动计算失败:", error);
    ElMessage.error(
      `启动计算失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isCalculating.value = false;
  }
};

/**
 * 处理清除操作
 */
const handleClear = (): void => {
  try {
    // 清除数据
    referenceLine1.value = null;
    referenceLine2.value = null;
    offset1.value = 0;
    offset2.value = 0;
    hangPoints.value = [];
    showResult.value = false;
    showResultTable.value = false;
    calculationResult.value = { success: false, message: "" };

    // 清除地图图层
    clearAllTempLayers();

    ElMessage.success("已清除所有数据");
  } catch (error) {
    console.error("清除操作失败:", error);
    ElMessage.error("清除操作失败");
  }
};

/**
 * 处理新增管点
 * @param hangPoint 栓点数据
 */
const handleAddNode = (hangPoint: HangPoint): void => {
  try {
    // 触发父组件的新增管点事件
    emit("addNode", {
      lng: hangPoint.lng,
      lat: hangPoint.lat,
      source: "two-line-hang",
      referenceLine1: referenceLine1.value,
      referenceLine2: referenceLine2.value,
      offset1: offset1.value,
      offset2: offset2.value,
    });

    // ElMessage.success("开始新增管点，请填写管点信息");
  } catch (error) {
    console.error("新增管点失败:", error);
    ElMessage.error("新增管点失败");
  }
};

// 定义组件事件
const emit = defineEmits<{
  addNode: [
    data: {
      lng: number;
      lat: number;
      source: string;
      referenceLine1: ReferenceLine | null;
      referenceLine2: ReferenceLine | null;
      offset1: number;
      offset2: number;
    }
  ];
  close: [];
}>();

const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
// 组件卸载时清理资源
onUnmounted(() => {
  clearAllTempLayers();
});
</script>

<style scoped>
.two-line-hang-panel {
  width: 600px;
  min-height: 130px;
  position: absolute;
  top: 190px;
  left: 10px;
  z-index: 1000;
}

.button-section {
  margin-bottom: 16px;
  gap: 12px;
}

.distance-section {
  margin-bottom: 16px;
}

.distance-input-group {
  display: flex;
  align-items: center;
}

.distance-label {
  width: 160px;
  font-size: 14px;
  color: #5c5f66;
  font-weight: 500;
}

.distance-input {
  width: 200px;
  height: 32px;
}

.calculation-section {
  margin-bottom: 16px;
  gap: 12px;
}

/* .select-btn,
.calculate-btn,
.clear-btn {
  width: auto;
  padding: 0 16px;
  font-size: 14px;
} */

.lines-section {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.line-row {
  margin-bottom: 8px;
}

.line-label {
  font-weight: 500;
  font-size: 14px;
  color: #1966ff;
  margin-bottom: 4px;
  display: block;
}

.line-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.line-text {
  font-size: 13px;
  color: #333;
}

.coordinate-text {
  font-size: 14px;
  color: #909399;
  font-family: SansCN-Regular;
}

.distance-text {
  font-size: 14px;
  color: #ff7d00;
  font-weight: 500;
}

.result-section {
  margin: 20px 0;
  color: #333333;
  padding: 5px 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.result-label {
  font-size: 14px;
  color: #333333;
  margin-right: 5px;
  margin-left: 10px;
}

.result-text {
  font-size: 14px;
  margin-left: 10px;
}

.table-section {
  margin-bottom: 16px;
}
.success {
  background: #eff9f1;
  border: 1px solid #00b42a;
}

.error {
  background: #fef1f1;
  border: 1px solid #ff9292;
}
.table-label {
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
  display: block;
}
.title {
  font-size: 14px;
  color: #2c3037;
}
.brd {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  /* border: 1px solid rgba(220, 220, 220, 1); */
  border-radius: 4px;
  padding: 0px 10px 20px 10px;
  /* margin: 10px 0; */
}
</style>
