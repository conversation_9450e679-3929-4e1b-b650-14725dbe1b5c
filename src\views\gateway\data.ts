import { getImages } from "@/utils/getImages";
export const topList = [
  {
    name: "服务数量",
    value: "261",
    unit: "个",
    icon: getImages("gateway/2.png"),
  },
  {
    name: "总访问次数",
    value: "261",
    unit: "次",
    icon: getImages("gateway/3.png"),
  },
]
export const menuList = [{
  name: "二维管网GIS模块",
  path: "/maplibre",
  value: 'two_pipe_network',
  icon: getImages("gateway/7.png"),
},
{
  name: "三维管网GIS模块",
  path: "/cesium",
  value: 'three_pipe_network',
  icon: getImages("gateway/8.png"),
},
{
  name: "GIS信息服务平台",
  path: "",
  value: 'gis_information_service',
  icon: getImages("gateway/9.png"),
},
{
  name: "后台管理系统",
  path: "/admin",
  value: 'background_manage',
  icon: getImages("gateway/10.png"),
},]
export const btmList = [{
  name: "服务能力",
  icon: getImages("gateway/11.png"),
  content:
    "提供管网数据管理、二三维可视化、空间分析等功能，实现供水系统数字化运维与科学调度。",
},
{
  name: "平台特性",
  icon: getImages("gateway/12.png"),
  content:
    "以“智能、高效、开放、安全”为核心特性，具备多源数据融合、动态可视化及跨平台协同能力。",
},
{
  name: "服务扩展能力",
  icon: getImages("gateway/13.png"),
  content:
    "提供标准化API接口和开发套件(SDK)，支持与第三方系统无缝对接，支持基于业务需求定制开发。",
},
{
  name: "水务专题",
  icon: getImages("gateway/14.png"),
  content:
    "为智慧水务“产-供-销”提供决策支持，覆盖水厂生产、管网输水、预警防控、供水调度等场景。",
},]