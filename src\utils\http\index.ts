/**
 * @fileoverview HTTP请求工具出口文件
 * @description 配置并导出HTTP请求实例，包含认证拦截器和错误处理
 * <AUTHOR>
 * @version 1.0.0
 */

// 出口文件
import Http from './request/index';
import localCache from '@/utils/auth'

/**
 * @description 创建HTTP请求实例
 * @details 配置基础URL、超时时间和请求/响应拦截器
 */
const hRequest = new Http({
  // 从环境变量获取API基础URL
  baseURL: import.meta.env.VITE_XHR_URL,
  // 请求超时时间（毫秒）
  timeout: 10000,
  interceptors: {
    /**
     * @description 请求拦截器
     * @param {any} config - Axios请求配置对象
     * @returns {any} 修改后的请求配置
     * @details 自动添加Authorization头部，从localStorage获取token
     */
    requestInterceptor: (config) => {
      const token = localCache.getCache('Latias') ?? '';
      // const token = localStorage.getItem('X-TOKEN');
      if (token !== null) {
        config.headers!['Latias'] = token;
      }
      return config;
    },
    /**
     * @description 请求错误拦截器
     * @param {any} err - 请求错误对象
     * @returns {any} 错误对象
     */
    requestInterceptorCatch: (err) => {
      return err;
    },
    /**
     * @description 响应拦截器
     * @param {any} res - 响应对象
     * @returns {any} 响应对象
     */
    responseInterceptor: (res) => {
      return res;
    },
    /**
     * @description 响应错误拦截器
     * @param {any} err - 响应错误对象
     * @returns {any} 错误对象
     */
    responseInterceptorCatch: (err) => {
      return err;
    }
  }
});

export default hRequest;
