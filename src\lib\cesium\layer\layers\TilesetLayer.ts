import { BaseLayer } from "./BaseLayer";

/*
 * @Description: 模型图层
 * @Autor: silei
 * @Date: 2023-08-03 09:17:11
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-09-14 16:25:33
 */
export class TilesetLayer extends BaseLayer {
  protected declare _delegate: BC.Tileset;
  protected declare _tileset: BC.TilesetLayer;
  addToInternal(viewer: BC.Viewer) {
    const tileset = new BC.Tileset(this.options.url);
    tileset.attr = this.options;
    if(this.type === 'reality') {
      tileset.setHeight(6)
    }
    const tilesetLayer = new BC.TilesetLayer(this._id);
    tilesetLayer.attr = this.options
    tilesetLayer.addTo(viewer);
    tileset.addTo(tilesetLayer);

    this._tileset = tilesetLayer;
    return tileset;
  }

  getOverlays() {
    return this._tileset.getOverlays();
  }

  flyToModel(model: any) {
    const boundingSphere = BC.Namespace.Cesium.BoundingSphere.transform(
      model.boundingSphere,
      model.modelMatrix,
      new BC.Namespace.Cesium.BoundingSphere()
    );
    this._viewer.camera.flyToBoundingSphere(boundingSphere);
  }
  flyTo(): void {
    if (this._delegate?.delegate) {
      this._delegate.readyPromise.then(()=>{
        this.flyToModel(this._delegate.delegate);
      })
    }
  }
  removeInternal() {
      this._viewer.removeLayer(this._tileset);
  }

  /**
   * @description 设置Tileset图层透明度
   * @param transparent 是否启用透明度
   * @param alphaValue 透明度值 (0.0 - 1.0)
   * @returns Promise<void>
   */
  async setTransparency(transparent: boolean, alphaValue: number = 0.5): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const tileset = this._delegate?.delegate;

        if (!tileset) {
          console.warn("无效的Tileset对象");
          resolve();
          return;
        }

        const applyTransparency = () => {
          try {
            if (transparent) {
              // 启用透明度
              console.log(`🎨 设置Tileset透明度: alpha=${alphaValue}`);

              // 方法1：使用Cesium3DTileStyle（推荐）
              tileset.style = new BC.Namespace.Cesium.Cesium3DTileStyle({
                color: {
                  conditions: [["true", `rgba(255, 255, 255, ${alphaValue})`]],
                },
              });

              // 性能优化设置（仅在透明时应用）
              tileset.skipLevelOfDetail = true;
              tileset.baseScreenSpaceError = 1024;
              tileset.skipScreenSpaceErrorFactor = 16;
              tileset.skipLevels = 1;
              tileset.immediatelyLoadDesiredLevelOfDetail = false;
              tileset.loadSiblings = false;
              tileset.cullWithChildrenBounds = true;
            } else {
              // 恢复不透明状态
              console.log("🔄 恢复Tileset不透明状态");

              // 完全重置样式
              tileset.style = undefined;

              // 恢复默认性能设置
              tileset.skipLevelOfDetail = false;
              tileset.baseScreenSpaceError = 16;
              tileset.skipScreenSpaceErrorFactor = 2;
              tileset.skipLevels = 0;
              tileset.immediatelyLoadDesiredLevelOfDetail = true;
              tileset.loadSiblings = true;
              tileset.cullWithChildrenBounds = false;
            }

            resolve();
          } catch (styleError) {
            console.error("Tileset样式设置失败，尝试其他方法:", styleError);

            // 备用方案：尝试使用CustomShader
            try {
              if (transparent) {
                tileset.customShader = new BC.Namespace.Cesium.CustomShader({
                  uniforms: {
                    u_transparency: {
                      value: alphaValue,
                      type: BC.Namespace.Cesium.UniformType.FLOAT,
                    },
                  },
                  fragmentShaderText: `
                    void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
                      material.diffuse.a *= u_transparency;
                      material.alpha *= u_transparency;
                    }
                  `,
                });
              } else {
                tileset.customShader = undefined;
              }
              resolve();
            } catch (shaderError) {
              console.error("Tileset CustomShader设置也失败:", shaderError);
              reject(shaderError);
            }
          }
        };

        // 检查Tileset是否已准备就绪
        if (tileset.ready) {
          applyTransparency();
        } else {
          // 等待Tileset加载完成
          tileset.readyPromise
            .then(() => {
              applyTransparency();
            })
            .catch((loadError: any) => {
              console.error("Tileset加载失败:", loadError);
              reject(loadError);
            });
        }
      } catch (error) {
        console.error("处理Tileset透明度失败:", error);
        reject(error);
      }
    });
  }

  /**
   * @description 获取当前透明度值
   * @returns number | null 当前透明度值，如果未设置透明度则返回null
   */
  getCurrentTransparency(): number | null {
    try {
      const tileset = this._delegate?.delegate;
      if (!tileset || !tileset.style) {
        return null;
      }

      // 尝试从样式中解析透明度值
      const style = tileset.style;
      if (style.color && style.color.conditions) {
        const condition = style.color.conditions[0];
        if (condition && condition[1]) {
          const rgba = condition[1].match(/rgba\([\d\s,]+,\s*([\d.]+)\)/);
          if (rgba && rgba[1]) {
            return parseFloat(rgba[1]);
          }
        }
      }

      // 尝试从CustomShader中获取透明度值
      if (tileset.customShader && tileset.customShader.uniforms.u_transparency) {
        return tileset.customShader.uniforms.u_transparency.value;
      }

      return null;
    } catch (error) {
      console.error("获取Tileset透明度失败:", error);
      return null;
    }
  }

  /**
   * @description 重置Tileset样式和性能设置
   */
  resetStyle(): void {
    try {
      const tileset = this._delegate?.delegate;
      if (!tileset) {
        console.warn("无效的Tileset对象");
        return;
      }

      // 重置样式
      tileset.style = undefined;
      tileset.customShader = undefined;

      // 恢复默认性能设置
      tileset.skipLevelOfDetail = false;
      tileset.baseScreenSpaceError = 16;
      tileset.skipScreenSpaceErrorFactor = 2;
      tileset.skipLevels = 0;
      tileset.immediatelyLoadDesiredLevelOfDetail = true;
      tileset.loadSiblings = true;
      tileset.cullWithChildrenBounds = false;

      console.log("🔄 已重置Tileset样式和性能设置");
    } catch (error) {
      console.error("重置Tileset样式失败:", error);
    }
  }
}
