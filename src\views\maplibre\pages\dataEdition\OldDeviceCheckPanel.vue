<!--
 * @Description: 老化设备检查面板
 * @Date: 2024-07-04
 * @Author: AI Assistant
 -->
<template>
  <page-card
    class="old-device-check-panel"
    title="老化设备检查"
    @closeCard="closeCard"
  >
    <!-- 检查结果统计 -->
    <el-row v-if="!loading" class="stats-section">
      <el-col :span="24">
        <el-text class="table-label">检查结果统计</el-text>
        <div class="stats-content">
          <el-tag type="info" size="large">
            总设备数量: {{ totalDeviceCount }}
          </el-tag>
          <el-tag
            :type="oldDevices.length > 0 ? 'danger' : 'success'"
            size="large"
            style="margin-left: 10px"
          >
            老化设备数量: {{ oldDevices.length }}
          </el-tag>
          <el-tag
            v-if="oldDevices.length > 0"
            type="warning"
            size="large"
            style="margin-left: 10px"
          >
            老化率:
            {{ ((oldDevices.length / totalDeviceCount) * 100).toFixed(1) }}%
          </el-tag>
        </div>
      </el-col>
    </el-row>

    <!-- 操作按钮区域 -->
    <el-row class="button-section">
      <el-col :span="24">
        <el-button
          type="primary"
          :icon="Refresh"
          @click="handleRefresh"
          :loading="loading"
        >
          重新检查
        </el-button>
      </el-col>
    </el-row>

    <!-- 老化设备列表表格 -->
    <el-row v-if="oldDevices.length > 0" class="table-section">
      <el-col :span="24">
        <el-text class="table-label">老化设备详细信息</el-text>
        <el-table
          :data="paginatedDevices"
          v-loading="loading"
          class="routeCt"
          stripe
          height="350"
          empty-text="暂无老化设备数据"
          style="width: 100%"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            :index="getTableIndex"
          />

          <el-table-column
            prop="code"
            label="设备编码"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="name"
            label="设备名称"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="firstDeviceTypeName"
            label="设备类型"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="factoryName"
            label="厂家"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="equipmentTime"
            label="设备时间"
            width="120"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ formatDate(row.equipmentTime) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="agingYears"
            label="老化年限"
            width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-tag :type="getAgingTagType(row.agingYears)">
                {{ row.agingYears }}年
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="120"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleLocate(row)"
                title="定位到地图"
              >
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center mt-5">
          <div>
            <div class="font-size-3.5 color-#323233">
              共{{ oldDevices.length }}条数据
            </div>
          </div>
          <!-- 分页组件 -->
          <el-pagination
            v-if="oldDevices.length > 0"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :pager-count="5"
            :page-size="pageSize"
            :total="oldDevices.length"
            layout="prev, pager, next, jumper"
            class="pagination"
            background
            small
          />
        </div>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { ElMessage } from "element-plus";
import { Refresh, Position } from "@element-plus/icons-vue";
import PageCard from "@/components/PageCard.vue";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { mockLngLatInPolygon } from "@/utils/mockUtil";
import type { Map as MapLibreMap } from "maplibre-gl";
import { fetchDeviceSummary } from "@/utils/deviceUtils";

const OLD_DEVICE_LAYER_IDS = {
  SOURCE: "old-device-highlight-source",
  LAYER: "old-device-highlight-layer",
} as const;

// 设备数据接口
interface DeviceData {
  code: string;
  siteCode: string | null;
  siteName: string | null;
  name: string;
  firstDeviceTypeCode: string;
  firstDeviceTypeName: string;
  secondDeviceTypeCode: string;
  secondDeviceTypeName: string;
  equipmentTime: string | null;
  picture: string | null;
  pictureList: any[];
  describe: string | null;
  serialNumber: string;
  factoryCode: string;
  factoryName: string;
  modelNumber: string | null;
  address: string | null;
  longitude: any;
  latitude: any;
  // 计算字段
  agingYears?: number;
  mockLongitude?: number;
  mockLatitude?: number;
}

interface Emits {
  (e: "close"): void;
}
const emit = defineEmits<Emits>();

const loading = ref(false);
const oldDevices = ref<DeviceData[]>([]);
const totalDeviceCount = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const pageSizes = ref([5, 10, 20, 50]);
let map: MapLibreMap | null = null;

// 分页计算
const paginatedDevices = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return oldDevices.value.slice(start, end);
});

onMounted(async () => {
  try {
    map = AppMaplibre.getMap();
  } catch (error) {
    console.warn("获取地图实例失败:", error);
  }
  await handleRefresh();
});

onUnmounted(() => {
  clearOldDeviceHighlight();
  resetData();
});

const clearOldDeviceHighlight = (): void => {
  if (!map) return;
  if (map.getLayer(OLD_DEVICE_LAYER_IDS.LAYER)) {
    map.removeLayer(OLD_DEVICE_LAYER_IDS.LAYER);
  }
  if (map.getSource(OLD_DEVICE_LAYER_IDS.SOURCE)) {
    map.removeSource(OLD_DEVICE_LAYER_IDS.SOURCE);
  }
};

const addOldDeviceHighlight = (longitude: number, latitude: number): void => {
  if (!map) return;
  clearOldDeviceHighlight();

  const coordinates = [longitude, latitude];

  map.addSource(OLD_DEVICE_LAYER_IDS.SOURCE, {
    type: "geojson",
    data: {
      type: "FeatureCollection",
      features: [
        {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: coordinates,
          },
          properties: {},
        },
      ],
    },
  });
  map.addLayer({
    id: OLD_DEVICE_LAYER_IDS.LAYER,
    type: "circle",
    source: OLD_DEVICE_LAYER_IDS.SOURCE,
    paint: {
      "circle-radius": 8,
      "circle-color": "#ff8c00",
      "circle-stroke-width": 2,
      "circle-stroke-color": "#ffffff",
    },
  });
};

/**
 * 生成2010-2025年之间的随机日期
 */
const generateRandomEquipmentTime = (): string => {
  const startYear = 2014;
  const endYear = 2025;
  const year =
    Math.floor(Math.random() * (endYear - startYear + 1)) + startYear;
  const month = Math.floor(Math.random() * 12) + 1;
  const day = Math.floor(Math.random() * 28) + 1; // 使用28天避免月份天数问题

  return `${year}-${month.toString().padStart(2, "0")}-${day
    .toString()
    .padStart(2, "0")}`;
};

/**
 * 计算设备老化年限
 */
const calculateAgingYears = (equipmentTime: string): number => {
  const equipmentDate = new Date(equipmentTime);
  const currentDate = new Date();
  const diffTime = currentDate.getTime() - equipmentDate.getTime();
  const diffYears = diffTime / (1000 * 60 * 60 * 24 * 365.25);
  return Math.floor(diffYears);
};

/**
 * 判断设备是否老化（超过10年）
 */
const isDeviceOld = (agingYears: number): boolean => {
  return agingYears >= 10;
};

/**
 * 格式化日期显示
 */
const formatDate = (dateString: string): string => {
  if (!dateString) return "";
  return dateString.split(" ")[0]; // 只显示日期部分
};

/**
 * 获取老化年限标签类型
 */
const getAgingTagType = (
  agingYears: number
): "primary" | "success" | "warning" | "info" | "danger" => {
  if (agingYears >= 15) return "danger";
  if (agingYears >= 12) return "warning";
  return "primary";
};

/**
 * 计算表格序号
 */
const getTableIndex = (index: number): number => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};

const handleCurrentChange = (page: number): void => {
  currentPage.value = page;
};

const handleSizeChange = (size: number): void => {
  pageSize.value = size;
  currentPage.value = 1;
};

const closeCard = (): void => {
  clearOldDeviceHighlight();
  resetData();
  emit("close");
};

/**
 * 加载设备数据
 */
const loadDeviceData = async (): Promise<DeviceData[]> => {
  try {
    // const response = await fetch("/data/device/device.json");
    const response = await fetchDeviceSummary({
        size: -1,
        current: 1,
        // secondDeviceTypeCode: this.options.code,
      });
    // if (!response.ok) {
    //   throw new Error(`HTTP error! status: ${response.status}`);
    // }
    // const deviceData: DeviceData[] = await response.json();
    return response;
  } catch (error) {
    console.error("加载设备数据失败:", error);
    throw error;
  }
};

/**
 * 处理设备数据，添加mock时间和老化年限计算
 */
const processDeviceData = (devices: DeviceData[]): DeviceData[] => {
  return devices.map((device) => {
    // 如果equipmentTime为null，生成mock时间
    if (!device.equipmentTime) {
      device.equipmentTime = generateRandomEquipmentTime();
    }

    // 计算老化年限
    device.agingYears = calculateAgingYears(device.equipmentTime);
    // 为没有经纬度的设备生成mock坐标
    if (!device.longitude || !device.latitude) {
      const [mockLng, mockLat] = mockLngLatInPolygon();
      device.mockLongitude = mockLng;
      device.mockLatitude = mockLat;
    } else {
      device.mockLongitude = parseFloat(device.longitude as any);
      device.mockLatitude = parseFloat(device.latitude as any);
    }

    return device;
  });
};

const handleRefresh = async (): Promise<void> => {
  loading.value = true;
  clearOldDeviceHighlight();

  try {
    // 加载设备数据
    const rawDeviceData = await loadDeviceData();
    totalDeviceCount.value = rawDeviceData.length;

    // 处理设备数据（添加mock时间和老化年限）
    const processedDevices = processDeviceData(rawDeviceData);

    // 筛选老化设备（超过10年的设备）
    const filteredOldDevices = processedDevices.filter((device) =>
      isDeviceOld(device.agingYears!)
    );

    oldDevices.value = filteredOldDevices;

    // 重置分页
    currentPage.value = 1;

    // 用户反馈
    if (oldDevices.value.length === 0) {
      ElMessage.success("设备状态良好！未发现老化设备");
    } else {
      // const oldDeviceCount = oldDevices.value.length;
      // const agingRate = (
      //   (oldDeviceCount / totalDeviceCount.value) *
      //   100
      // ).toFixed(1);
      // ElMessage.warning(
      //   `发现 ${oldDeviceCount} 台老化设备，老化率 ${agingRate}%，建议及时维护或更换`
      // );
    }
  } catch (error) {
    console.error("检查老化设备异常:", error);
    const errorMsg = error instanceof Error ? error.message : "网络或系统异常";
    ElMessage.error(`检查老化设备失败: ${errorMsg}`);
    resetData();
  } finally {
    loading.value = false;
  }
};

/**
 * 重置数据状态
 */
const resetData = (): void => {
  oldDevices.value = [];
  totalDeviceCount.value = 0;
};

const handleLocate = (device: DeviceData): void => {
  if (!map) {
    ElMessage.error("地图实例不可用");
    return;
  }

  try {
    // 使用真实坐标或mock坐标
    const longitude = device.mockLongitude;
    const latitude = device.mockLatitude;

    if (!longitude || !latitude) {
      ElMessage.error("无法获取有效的位置坐标");
      return;
    }

    // 添加高亮标记
    addOldDeviceHighlight(longitude, latitude);

    // 飞行到目标位置
    map.flyTo({
      center: [longitude, latitude],
      zoom: 18,
      duration: 1500,
    });

    // const locationTip = device.longitude ? "真实位置" : "模拟位置";
    // ElMessage.success(
    //   `已定位到老化设备: ${device.name}，老化年限: ${device.agingYears}年 (${locationTip})`
    // );
  } catch (error) {
    console.error("定位失败:", error);
    ElMessage.error("定位失败，请检查数据格式");
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
</script>

<style scoped lang="scss">
.old-device-check-panel {
  position: absolute;
  left: 10px;
  top: 150px;
  width: 724px;
  max-height: 85vh;
  z-index: 999;
}

.stats-section,
.button-section,
.table-section {
  margin-bottom: 16px;
}

.stats-content {
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.table-label {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}
</style>
