<template>
  <div
    class="base-main [&_>div]:overflow-hidden"
    grid="~ rows-[72px_1fr] gap-y-2.5"
  >
    <div class="query-form">
      <el-form :model="queryForm">
        <el-row :gutter="40">
          <el-col :span="6">
            <el-form-item label="菜单名称">
              <el-input
                v-model="queryForm.name"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="菜单状态">
              <el-select
                v-model="queryForm.status"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in userState"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button
                class="admin-query-btn"
                type="primary"
                @click="queryData"
                :icon="Search"
              >
                查询
              </el-button>
              <el-button
                class="admin-reset-btn"
                :icon="Refresh"
                @click="resetSearch"
              >
                重置</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="[&_>div]:overflow-hidden bg-#fff rounded-2 mt-2.5 p-5">
      <div class="table-height">
        <el-table
          row-key="id"
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          height="100%"
          header-row-class-name="table-header"
          :tree-props="{ children: 'children' }"
        >
          <el-table-column
            prop="label"
            label="菜单名称"
            align="center"
            min-width="50"
          />
          <el-table-column prop="path" label="路由地址" align="center" />
          <el-table-column
            prop="orderNum"
            label="排序"
            width="100"
            align="center"
          />
          <el-table-column prop="type" label="菜单类型" align="center">
            <template #default="scope">
              <div v-if="scope.row.type == 'S'">系统</div>
              <div v-if="scope.row.type == 'C'">目录</div>
              <div v-if="scope.row.type == 'M'">菜单</div>
              <div v-if="scope.row.type == 'B'">按钮</div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="菜单状态" align="center">
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                inline-prompt
                :active-value="'1'"
                :inactive-value="'0'"
                :loading="switchLoading"
                @change="loadStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="描述" align="center" />
          <el-table-column label="操作" min-width="100" align="center">
            <template v-slot="scope">
              <el-button
                link
                class="primary-link color-#1966FF"
                @click="checkHandler(row)"
              >
                详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :key="dialogKey"
      :model-value="visible"
      width="30%"
      class="custom-dialog"
      :before-close="handleClose"
      :close-on-click-modal="false"
      ref="test"
    >
      <menu-info
        ref="menuinfo"
        v-model:formData="formData"
        v-model:editable="editable"
        v-model:menuData="tableData"
      />
      <template v-slot:footer v-if="dialogTitle != '菜单查看'">
        <div>
          <el-button @click="handleClose" :loading="subBtnLoading"
            >取 消</el-button
          >
          <el-button
            type="primary"
            :loading="subBtnLoading"
            @click="eventSubmit"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { Search, Refresh, Plus } from "@element-plus/icons-vue";
import MenuInfo from "./MenuInfo.vue";
import { userState } from "@/utils/constant";
import type { SysMenu, QueryForm } from "./type";
import {
  menuTree,
  addMenu,
  editMenu,
  detailMenu,
  deleteMenu,
  editMenuState,
} from "@/api/menu";
// import { useMenuStore } from "@/stores/menu";
import localCatch from "@/utils/auth";
const initQueryForm = () => {
  return {
    name: "",
    status: "",
    pageNum: 1,
    pageSize: 10,
  };
};
const queryForm = ref<QueryForm>(initQueryForm());
const initFormData = () => {
  return {
    name: "",
    status: "1",
    type: "M",
    path: "",
    remark: "",
    parentId: "",
    perms: "",
    orderNum: "",
    visible: "1",
    title: "",
    icon: "",
  };
};
const formData = ref<SysMenu>(initFormData());
const editable = ref(true);
const switchLoading = ref(false);
const loading = ref(false);
const tableData = ref<SysMenu[]>([]);
const dialogTitle = ref("");
const dialogKey = ref(0);
const visible = ref(false);
const subBtnLoading = ref(false);
const menuinfo = ref();
const handleClose = () => {
  visible.value = false;
};

const queryData = () => {
  getList();
};
const resetSearch = () => {
  queryForm.value = initQueryForm();
  getList();
};
const newData = () => {
  formData.value = initFormData();
  editable.value = false;
  dialogTitle.value = "菜单新增";
  dialogKey.value++;
  visible.value = true;
};
const eventSubmit = async () => {
  try {
    var info = await menuinfo.value!.submitForm();
    if (info) {
      subBtnLoading.value = true;
      if (dialogTitle.value === "菜单新增") {
        const result = await addMenu(info);
        if (result.code === 200) {
          visible.value = false;
          ElMessage.success(result.msg);
          // await navChange();
          // await permsn();
          await getList();
        }
      } else {
        const result = await editMenu(info);
        if (result.code === 200) {
          visible.value = false;
          ElMessage.success("修改成功");
          // await navChange();
          // await permsn();
          await getList();
        }
      }
    }
  } finally {
    subBtnLoading.value = false;
  }
};
const { getMenu } = useMenuStore();
// 导航修改
// const navChange = async () => {
//   getMenu();
// };
// 状态修改
const loadStatusChange = async (row: SysMenu) => {
  // const { id, status } = row;
  // const result = await editMenuState({ id, status });
  // if (result.code == 200) {
  //   ElMessage({
  //     showClose: true,
  //     message: "修改成功",
  //     type: "success",
  //   });
  // }
  // getList();
};
const checkHandler = async (row: SysMenu) => {
  editable.value = true;
  dialogTitle.value = "菜单查看";
  const result = await detailMenu(row.id);
  formData.value = result.data;
  visible.value = true;
};
const editHandler = async (row: SysMenu) => {
  editable.value = false;
  dialogTitle.value = "菜单修改";
  const result = await detailMenu(row.id);
  formData.value = result.data;
  visible.value = true;
};
const delteHandler = (row: SysMenu) => {
  ElMessageBox.confirm("确定要删除当前菜单吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox",
  }).then(() => {
    deleteMenu(row.id).then((res) => {
      if (res.code == 200) {
        ElMessage({
          showClose: true,
          message: "删除成功",
          type: "success",
        });
        navChange();
        getList();
      }
    });
  });
};
const getList = async () => {
  try {
    loading.value = true;
    const result = await menuTree(queryForm.value);
    tableData.value = result.data;
  } finally {
    loading.value = false;
  }
};
// // 当前用户权限
// const permsn = () => {
//   permission().then((res) => {
//     if (res.code == 200) {
//       localCatch.deleteCache('permsSet');
//       localCatch.setCache('permsSet', res.data.toString());
//     }
//   });
// };
onMounted(() => {
  getList();
});
</script>
<style lang="scss" setup>
.table-height {
  height: calc(100vh - 300px);
}
</style>
