/**
 * @fileoverview 管线纵断面分析组件测试
 * @description 测试HorizontalProfileAnalysis组件的固定间距功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock数据
const mockTestData = [
  {
    gid: '1',
    gxddh: 'GX001',
    qdms: 2.5,
    zdms: 3.0,
    gj: 300,
    cz: 'PE',
    geojson: JSON.stringify({
      type: 'LineString',
      coordinates: [[116.1, 39.1], [116.2, 39.2]]
    })
  },
  {
    gid: '2', 
    gxddh: 'GX002',
    qdms: 3.0,
    zdms: 2.8,
    gj: 400,
    cz: 'PE',
    geojson: JSON.stringify({
      type: 'LineString',
      coordinates: [[116.2, 39.2], [116.3, 39.3]]
    })
  },
  {
    gid: '3',
    gxddh: 'GX003', 
    qdms: 2.8,
    zdms: 3.2,
    gj: 500,
    cz: 'PE',
    geojson: JSON.stringify({
      type: 'LineString',
      coordinates: [[116.3, 39.3], [116.4, 39.4]]
    })
  }
];

// Mock turf.js
vi.mock('@turf/turf', () => ({
  length: vi.fn((geojson: any) => {
    // 模拟不同的管线长度
    const coords = geojson.coordinates;
    if (coords.length >= 2) {
      // 返回不同的长度值来测试固定间距
      const startLng = coords[0][0];
      if (startLng === 116.1) return 150.5; // 第一段管线
      if (startLng === 116.2) return 89.3;  // 第二段管线
      if (startLng === 116.3) return 234.7; // 第三段管线
    }
    return 100;
  })
}));

describe('HorizontalProfileAnalysis 固定间距功能测试', () => {
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('x坐标固定间距计算', () => {
    it('应该使用固定间距而不是管线长度计算x坐标', () => {
      const testdata = [...mockTestData];
      const fixedSpacing = 80;
      
      // 模拟组件中的x坐标计算逻辑
      testdata.forEach((item: any, index: number) => {
        item.xdata = index * fixedSpacing;
      });
      
      // 验证x坐标是固定间距
      expect(testdata[0].xdata).toBe(0);      // 第一个点在原点
      expect(testdata[1].xdata).toBe(80);     // 第二个点在80px
      expect(testdata[2].xdata).toBe(160);    // 第三个点在160px
      
      // 验证间距是固定的
      const spacing1 = testdata[1].xdata - testdata[0].xdata;
      const spacing2 = testdata[2].xdata - testdata[1].xdata;
      expect(spacing1).toBe(fixedSpacing);
      expect(spacing2).toBe(fixedSpacing);
      expect(spacing1).toBe(spacing2); // 间距相等
    });

    it('应该保留管线长度信息用于显示', () => {
      const testdata = [...mockTestData];
      const turf = require('@turf/turf');
      
      // 模拟管线长度计算
      testdata.forEach((item: any) => {
        if (item.geojson) {
          item.gxcd = turf.length(JSON.parse(item.geojson), { units: "meters" });
        } else {
          item.gxcd = item.gdcd || 0;
        }
      });
      
      // 验证管线长度被正确计算和保存
      expect(testdata[0].gxcd).toBe(150.5);
      expect(testdata[1].gxcd).toBe(89.3);
      expect(testdata[2].gxcd).toBe(234.7);
      
      // 验证长度不同但x坐标仍然是固定间距
      const fixedSpacing = 80;
      testdata.forEach((item: any, index: number) => {
        item.xdata = index * fixedSpacing;
      });
      
      expect(testdata[0].xdata).toBe(0);
      expect(testdata[1].xdata).toBe(80);
      expect(testdata[2].xdata).toBe(160);
    });
  });

  describe('图表宽度计算', () => {
    it('应该基于固定间距和管点数量计算宽度', () => {
      const testdata = [...mockTestData];
      const fixedSpacing = 80;
      const minWidth = 650;
      
      // 模拟宽度计算逻辑
      let width = Math.max(testdata.length * fixedSpacing, minWidth);
      
      // 3个管点 * 80px = 240px，小于最小宽度650px
      expect(width).toBe(minWidth);
      
      // 测试更多管点的情况
      const moreData = new Array(10).fill(null).map((_, i) => ({ ...mockTestData[0], gid: i.toString() }));
      width = Math.max(moreData.length * fixedSpacing, minWidth);
      
      // 10个管点 * 80px = 800px，大于最小宽度
      expect(width).toBe(800);
    });
  });

  describe('x轴刻度和标签', () => {
    it('应该生成正确的管点序号刻度值', () => {
      const testdata = [...mockTestData];
      const fixedSpacing = 80;
      
      // 模拟刻度值生成
      const tickValues = testdata.map((_: any, i: number) => i * fixedSpacing);
      
      expect(tickValues).toEqual([0, 80, 160]);
    });

    it('应该生成正确的管点序号标签', () => {
      const testdata = [...mockTestData];
      
      // 模拟标签生成
      const tickLabels = testdata.map((_: any, i: number) => `P${i + 1}`);
      
      expect(tickLabels).toEqual(['P1', 'P2', 'P3']);
    });
  });

  describe('数据完整性', () => {
    it('应该保持原有数据结构不变', () => {
      const testdata = [...mockTestData];
      const originalKeys = Object.keys(testdata[0]);
      
      // 模拟数据处理
      const turf = require('@turf/turf');
      testdata.forEach((item: any, index: number) => {
        // 计算管线长度
        if (item.geojson) {
          item.gxcd = turf.length(JSON.parse(item.geojson), { units: "meters" });
        }
        // 设置固定间距x坐标
        item.xdata = index * 80;
      });
      
      // 验证原有字段都还存在
      originalKeys.forEach(key => {
        expect(testdata[0]).toHaveProperty(key);
      });
      
      // 验证新增字段
      expect(testdata[0]).toHaveProperty('gxcd');
      expect(testdata[0]).toHaveProperty('xdata');
    });

    it('应该处理缺少geojson的情况', () => {
      const testdataWithoutGeojson = [
        { gid: '1', gxddh: 'GX001', gdcd: 120 },
        { gid: '2', gxddh: 'GX002' }, // 没有geojson和gdcd
        { gid: '3', gxddh: 'GX003', gdcd: 200 }
      ];
      
      // 模拟处理逻辑
      testdataWithoutGeojson.forEach((item: any, index: number) => {
        if (item.geojson) {
          // 有geojson时计算长度
          const turf = require('@turf/turf');
          item.gxcd = turf.length(JSON.parse(item.geojson), { units: "meters" });
        } else {
          // 没有geojson时使用gdcd或默认值
          item.gxcd = item.gdcd || 0;
        }
        item.xdata = index * 80;
      });
      
      expect(testdataWithoutGeojson[0].gxcd).toBe(120);
      expect(testdataWithoutGeojson[1].gxcd).toBe(0);   // 默认值
      expect(testdataWithoutGeojson[2].gxcd).toBe(200);
      
      // x坐标仍然是固定间距
      expect(testdataWithoutGeojson[0].xdata).toBe(0);
      expect(testdataWithoutGeojson[1].xdata).toBe(80);
      expect(testdataWithoutGeojson[2].xdata).toBe(160);
    });
  });

  describe('边界情况测试', () => {
    it('应该处理单个管点的情况', () => {
      const singleData = [mockTestData[0]];
      const fixedSpacing = 80;
      
      singleData.forEach((item: any, index: number) => {
        item.xdata = index * fixedSpacing;
      });
      
      expect(singleData[0].xdata).toBe(0);
      
      // 宽度应该使用最小值
      const width = Math.max(singleData.length * fixedSpacing, 650);
      expect(width).toBe(650);
    });

    it('应该处理大量管点的情况', () => {
      const manyData = new Array(20).fill(null).map((_, i) => ({
        ...mockTestData[0],
        gid: i.toString()
      }));
      
      const fixedSpacing = 80;
      manyData.forEach((item: any, index: number) => {
        item.xdata = index * fixedSpacing;
      });
      
      // 验证最后一个点的位置
      expect(manyData[19].xdata).toBe(19 * fixedSpacing);
      
      // 验证宽度计算
      const width = Math.max(manyData.length * fixedSpacing, 650);
      expect(width).toBe(1600); // 20 * 80
    });
  });

  describe('配置参数测试', () => {
    it('应该支持不同的固定间距值', () => {
      const testdata = [...mockTestData];

      // 测试不同的间距值
      [60, 80, 100].forEach(spacing => {
        testdata.forEach((item: any, index: number) => {
          item.xdata = index * spacing;
        });

        expect(testdata[1].xdata - testdata[0].xdata).toBe(spacing);
        expect(testdata[2].xdata - testdata[1].xdata).toBe(spacing);
      });
    });
  });

  describe('文本对齐功能测试', () => {
    it('应该计算正确的文本位置（左对齐到虚线）', () => {
      const testdata = [...mockTestData];
      const fixedSpacing = 80;

      // 模拟x坐标计算
      testdata.forEach((item: any, index: number) => {
        item.xdata = index * fixedSpacing;
      });

      // 模拟xRange函数
      const mockXRange = (x: number) => x;

      // 模拟文本位置计算
      const textPositions = testdata.map((item: any) => ({
        x: mockXRange(item.xdata) + 75, // 左对齐位置
        anchor: 'start' // 左对齐锚点
      }));

      // 验证文本位置
      expect(textPositions[0].x).toBe(75);  // 第一个文本位置
      expect(textPositions[1].x).toBe(155); // 第二个文本位置
      expect(textPositions[2].x).toBe(235); // 第三个文本位置

      // 验证对齐方式
      textPositions.forEach(pos => {
        expect(pos.anchor).toBe('start');
      });
    });

    it('应该生成正确的标签位置', () => {
      const labels = [
        { text: "管点编码", y: 249 },
        { text: "地面高程/m", y: 269 },
        { text: "管点高程/m", y: 289 },
        { text: "埋深/m", y: 309 },
        { text: "管线长度/m", y: 329 },
        { text: "断面尺寸/mm", y: 349 },
      ];

      // 验证标签数量和内容
      expect(labels).toHaveLength(6);
      expect(labels[0].text).toBe("管点编码");
      expect(labels[5].text).toBe("断面尺寸/mm");

      // 验证y坐标递增
      for (let i = 1; i < labels.length; i++) {
        expect(labels[i].y).toBeGreaterThan(labels[i-1].y);
      }
    });

    it('应该处理文本内容的默认值', () => {
      const testdataWithDefaults = [
        { gid: '1' }, // 缺少大部分字段
        { gid: '2', qdbh: 'GD001', qdgc: 45.5, qdms: 2.5, gj: 300 },
        { gid: '3', zdbh: 'GD002', zdgc: 46.0, zdms: 3.0, dmcc: 400 }
      ];

      // 模拟文本内容生成
      const textContents = testdataWithDefaults.map(item => ({
        code: item.qdbh || item.zdbh || item.bm || '',
        elevation: item.qdgc || item.zdgc || "0.00",
        depth: item.qdms || item.zdms || "0.00",
        diameter: item.gj || item.dmcc || ""
      }));

      // 验证默认值处理
      expect(textContents[0].code).toBe('');
      expect(textContents[0].elevation).toBe("0.00");
      expect(textContents[0].depth).toBe("0.00");
      expect(textContents[0].diameter).toBe("");

      expect(textContents[1].code).toBe('GD001');
      expect(textContents[1].elevation).toBe(45.5);
      expect(textContents[1].depth).toBe(2.5);
      expect(textContents[1].diameter).toBe(300);
    });
  });
});
