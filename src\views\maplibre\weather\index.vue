<template>
  <div class="w-420px box-border p-4 bg-#fff z-2 show" v-if="params.forecast">
    <div class="flex justify-between bg rounded-2 box-border p-5">
      <div class="flex">
        <div>
          <img
            :src="
              params?.forecast[0].type.includes('雨')
                ? imgMapper['雨']
                : imgMapper[params.forecast[0].type]
            "
            alt=""
          />
        </div>
        <div class="ml-5">
          <span class="text-white font-size-54px">{{
            Math.floor(params.wendu) + "°"
          }}</span>
          <span class="text-white">{{ params.forecast[0].type }}</span>
          <div class="font-size-4.5 color-#fff font-500 mt--2.5">乐山市</div>
        </div>
      </div>
      <div class="font-size-3.5 color-#fff">
        <div>{{ params.forecast[0].week }}</div>
        <div class="mt-2.5 mb-6">{{ params.forecast[0].ymd }}</div>
        <div>{{ params.forecast[0].fx }}</div>
      </div>
    </div>
    <div class="font-size-3.5 color-#2C3037 mt-10">
      <div class="mb-2.5">近5日天气预报</div>
      <div
        v-for="(item, index) in params.forecast.slice(0, 5)"
        class="flex items-center justify-between line mb-2.5"
      >
        <div class="w-42px text-end">
          {{ index === 0 ? "今天" : item.week }}
        </div>
        <img
          :src="
            params.forecast[0].type.includes('雨')
              ? imgMapperAc['雨']
              : imgMapperAc[params.forecast[0].type]
          "
          class="w-9 h-9"
          alt=""
        />
        <div class="flex items-center">
          <div>{{ item.low.match(/\d+/g)[0] + "°" }}</div>
          <div class="w-120px h-4px bg-#b4d5ff rounded-1 m-x-2"></div>
          <div>{{ item.high.match(/\d+/g)[0] + "°" }}</div>
        </div>

        <div class="w-50px">{{ item.fx }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getImages } from "@/utils/getImages";

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
});
let params = ref(props.params);
watch(
  () => props.params,
  (val) => {
    params.value = val;
  }
);
// console.log(props.mainItem);
// const allWeather = ref<any>([]);
const imgMapper: any = {
  阴: getImages("weather/yin.png"),
  雨: getImages("weather/rain.png"),
  多云: getImages("weather/csun.png"),
  晴: getImages("weather/sunny.png"),
};
const imgMapperAc: any = {
  阴: getImages("weather/yin-ac.png"),
  雨: getImages("weather/rain-ac.png"),
  多云: getImages("weather/csun-ac.png"),
  晴: getImages("weather/sunny-ac.png"),
};
</script>

<style lang="scss" scoped>
.bg {
  background: linear-gradient(
    90deg,
    rgba(103, 186, 255, 1),
    rgba(37, 92, 255, 1) 100%
  );
}
.line {
  border-top: 1px solid #d8e8fe;
  padding-top: 10px;
}
// :deep(.custom-earth-card .custom_card_body) {
//   padding: 0;
// }
.weather {
  :deep(.custom_card_body) {
    padding: 0;
  }
}
.show {
  box-shadow: 0px 0px 12px 3px rgba(169, 190, 232, 0.4);
}
</style>
