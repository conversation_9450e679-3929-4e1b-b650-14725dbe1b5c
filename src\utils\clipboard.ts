/**
 * @fileoverview 剪贴板工具函数
 * @description 提供兼容性更好的剪贴板操作功能，支持HTTPS和HTTP环境
 * <AUTHOR>
 * @version 1.0.0
 */

import { ElMessage } from 'element-plus';

/**
 * @interface ClipboardOptions
 * @description 剪贴板操作选项
 */
interface ClipboardOptions {
  /** 成功提示消息 */
  successMessage?: string;
  /** 失败提示消息 */
  errorMessage?: string;
  /** 是否显示提示消息，默认为true */
  showMessage?: boolean;
  /** 失败时是否显示手动复制提示，默认为true */
  showFallbackMessage?: boolean;
}

/**
 * @description 检查剪贴板API是否可用
 * @returns 是否支持现代剪贴板API
 */
export function isClipboardAPIAvailable(): boolean {
  return !!(navigator.clipboard && window.isSecureContext);
}

/**
 * @description 检查是否为安全上下文（HTTPS或localhost）
 * @returns 是否为安全上下文
 */
export function isSecureContext(): boolean {
  return window.isSecureContext || 
         location.protocol === 'https:' || 
         location.hostname === 'localhost' || 
         location.hostname === '127.0.0.1';
}

/**
 * @description 使用传统方法复制文本（兼容性更好）
 * @param text - 要复制的文本
 * @returns 是否成功复制
 */
function fallbackCopyText(text: string): boolean {
  try {
    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 设置样式，使其不可见但可选中
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    textArea.style.pointerEvents = 'none';
    textArea.style.width = '1px';
    textArea.style.height = '1px';
    textArea.setAttribute('readonly', '');
    
    // 添加到DOM
    document.body.appendChild(textArea);
    
    // 选中文本
    textArea.focus();
    textArea.select();
    textArea.setSelectionRange(0, text.length);
    
    // 尝试复制
    const successful = document.execCommand('copy');
    
    // 清理DOM
    document.body.removeChild(textArea);
    
    return successful;
  } catch (error) {
    console.error('传统复制方法失败:', error);
    return false;
  }
}

/**
 * @description 复制文本到剪贴板（兼容性版本）
 * @param text - 要复制的文本
 * @param options - 复制选项
 * @returns Promise<boolean> 是否成功复制
 */
export async function copyToClipboard(
  text: string, 
  options: ClipboardOptions = {}
): Promise<boolean> {
  const {
    successMessage = '复制成功',
    errorMessage = '复制失败',
    showMessage = true,
    showFallbackMessage = true
  } = options;

  if (!text || text.trim() === '') {
    if (showMessage) {
      ElMessage.warning('没有内容可复制');
    }
    return false;
  }

  try {
    // 方法1：尝试使用现代 Clipboard API
    if (isClipboardAPIAvailable()) {
      await navigator.clipboard.writeText(text);
      if (showMessage) {
        ElMessage.success(successMessage);
      }
      return true;
    }
    
    // 方法2：使用传统的 document.execCommand 方法
    const success = fallbackCopyText(text);
    
    if (success) {
      if (showMessage) {
        ElMessage.success(successMessage);
      }
      return true;
    } else {
      throw new Error('传统复制方法失败');
    }
    
  } catch (error) {
    console.error('复制失败:', error);
    
    if (showMessage) {
      if (showFallbackMessage) {
        // 显示手动复制提示
        ElMessage({
          message: `${errorMessage}，请手动复制: ${text}`,
          type: 'warning',
          duration: 8000,
          showClose: true,
          dangerouslyUseHTMLString: false
        });
      } else {
        ElMessage.error(errorMessage);
      }
    }
    
    return false;
  }
}

/**
 * @description 复制坐标到剪贴板
 * @param lng - 经度
 * @param lat - 纬度
 * @param precision - 精度（小数位数），默认为6
 * @param coordSystem - 坐标系名称，用于提示消息
 * @returns Promise<boolean> 是否成功复制
 */
export async function copyCoordinateToClipboard(
  lng: number,
  lat: number,
  precision: number = 6,
  coordSystem: string = '坐标'
): Promise<boolean> {
  const coordText = `${lng.toFixed(precision)},${lat.toFixed(precision)}`;

  return copyToClipboard(coordText, {
    successMessage: `已复制${coordSystem}: ${coordText}`,
    errorMessage: `复制${coordSystem}失败`
  });
}

/**
 * @description 复制三维坐标到剪贴板（包含高程）
 * @param lng - 经度
 * @param lat - 纬度
 * @param alt - 高程（可选）
 * @param precision - 精度（小数位数），默认为6
 * @param altPrecision - 高程精度（小数位数），默认为2
 * @param coordSystem - 坐标系名称，用于提示消息
 * @returns Promise<boolean> 是否成功复制
 */
export async function copy3DCoordinateToClipboard(
  lng: number,
  lat: number,
  alt?: number,
  precision: number = 6,
  altPrecision: number = 2,
  coordSystem: string = '坐标'
): Promise<boolean> {
  let coordText: string;

  if (typeof alt === 'number' && !isNaN(alt)) {
    // 包含高程的三维坐标
    coordText = `${lng.toFixed(precision)},${lat.toFixed(precision)},${alt.toFixed(altPrecision)}`;
  } else {
    // 只有经纬度的二维坐标
    coordText = `${lng.toFixed(precision)},${lat.toFixed(precision)}`;
  }

  return copyToClipboard(coordText, {
    successMessage: `已复制${coordSystem}: ${coordText}`,
    errorMessage: `复制${coordSystem}失败`
  });
}

/**
 * @description 复制JSON数据到剪贴板
 * @param data - 要复制的数据对象
 * @param pretty - 是否格式化JSON，默认为true
 * @param successMessage - 成功提示消息
 * @returns Promise<boolean> 是否成功复制
 */
export async function copyJSONToClipboard(
  data: any,
  pretty: boolean = true,
  successMessage: string = '已复制JSON数据'
): Promise<boolean> {
  try {
    const jsonText = pretty ? JSON.stringify(data, null, 2) : JSON.stringify(data);
    
    return copyToClipboard(jsonText, {
      successMessage,
      errorMessage: '复制JSON数据失败'
    });
  } catch (error) {
    console.error('序列化JSON失败:', error);
    ElMessage.error('数据格式错误，无法复制');
    return false;
  }
}

/**
 * @description 从剪贴板读取文本（仅在支持的环境中可用）
 * @returns Promise<string | null> 剪贴板中的文本，失败时返回null
 */
export async function readFromClipboard(): Promise<string | null> {
  try {
    if (isClipboardAPIAvailable()) {
      const text = await navigator.clipboard.readText();
      return text;
    } else {
      console.warn('当前环境不支持从剪贴板读取');
      ElMessage.warning('当前环境不支持从剪贴板读取');
      return null;
    }
  } catch (error) {
    console.error('读取剪贴板失败:', error);
    ElMessage.error('读取剪贴板失败');
    return null;
  }
}

/**
 * @description 获取剪贴板支持信息
 * @returns 剪贴板支持信息对象
 */
export function getClipboardSupport() {
  return {
    /** 是否支持现代剪贴板API */
    modernAPI: isClipboardAPIAvailable(),
    /** 是否为安全上下文 */
    secureContext: isSecureContext(),
    /** 是否支持传统复制方法 */
    fallbackMethod: typeof document !== 'undefined' && 'execCommand' in document,
    /** 浏览器信息 */
    userAgent: navigator.userAgent,
    /** 协议信息 */
    protocol: location.protocol,
    /** 主机信息 */
    hostname: location.hostname
  };
}
