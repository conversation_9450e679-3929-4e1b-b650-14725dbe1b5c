/**
 * @fileoverview HTTP请求拦截器和配置类型定义
 * @description 定义HTTP请求拦截器接口和扩展的请求配置类型
 * <AUTHOR>
 * @version 1.0.0
 */

import type { AxiosRequestConfig, AxiosResponse } from 'axios'

/**
 * @interface HYRequestInterceptors
 * @description HTTP请求拦截器接口
 * @template T - 响应类型，默认为AxiosResponse
 */
export interface HYRequestInterceptors<T = AxiosResponse> {
  /** @description 请求拦截器函数 */
  requestInterceptor?: (config: AxiosRequestConfig) => AxiosRequestConfig
  /** @description 请求错误拦截器函数 */
  requestInterceptorCatch?: (error: any) => any
  /** @description 响应拦截器函数 */
  responseInterceptor?: (res: T) => T
  /** @description 响应错误拦截器函数 */
  responseInterceptorCatch?: (error: any) => any
}

/**
 * @interface HYRequestConfig
 * @description 扩展的HTTP请求配置接口
 * @template T - 响应类型，默认为AxiosResponse
 * @extends AxiosRequestConfig
 */
export interface HYRequestConfig<T = AxiosResponse> extends AxiosRequestConfig {
  /** @description 自定义拦截器配置 */
  interceptors?: HYRequestInterceptors<T>
  /** @description 是否显示加载状态 */
  showLoading?: boolean
}
