// measureTool.js
    
import 'maplibre-gl/dist/maplibre-gl.css'
import * as turf from '@turf/turf'
import './measureIcon.css'
import { Marker } from "maplibre-gl";

/**
 * @interface MeasureConfig 测量配置接口
 */
interface MeasureConfig {
  /** 是否启用测量功能 */
  enabled?: boolean
  /** 线条颜色 */
  lineColor?: string
  /** 填充颜色 */
  fillColor?: string
  /** 线条宽度 */
  lineWidth?: number
  /** 点的半径 */
  pointRadius?: number
}

/**
 * @class MeasureTool 测量工具类
 * @description 提供距离测量和面积测量功能，支持交互式绘制和编辑
 */
export default class MeasureTool {
  /** @description 地图实例 */
  private map: any = void 0
  
  /** @description 地图实例副本 */
  private sdMap: any = void 0
  
  /** @description 距离测量图层ID列表 */
  private layerDistanceList: string[] = []
  
  /** @description 面积测量图层ID列表 */
  private layerAreaList: string[] = []
  
  /** @description 测量状态标记 */
  private isMeasure: boolean = false
  
  /** @description 编辑状态标记 */
  private isEdit: boolean = false
  
  /** @description 拖拽点顺序 */
  private dragPointOrder: number = 0
  
  /** @description 线上点坐标 */
  private pointOnLine: number[] = [0, 0]
  
  /** @description 几何点列表 */
  private Geolist: number[][] = []
  
  /** @description 停止距离测量函数 */
  private setDistance = () => { }
  
  /** @description 停止面积测量函数 */
  private setArea = () => { }
  
  /** @description 测量配置 */
  private config: MeasureConfig
  
  /** @description 当前活动的测量类型 */
  private currentMeasureType: 'distance' | 'area' | null = null
  
  /** @description 当前活动的测量图层ID */
  private currentLayerId: string | null = null
  
  /** @description 事件监听器集合 */
  private activeEventListeners: Map<string, (...args: any[]) => void> = new Map()
  
  /** @description Marker对象集合 */
  private activeMarkers: Set<any> = new Set()
  
  /**
   * @constructor
   * @param map - MapLibre地图实例
   * @param config - 测量配置选项
   */
  constructor(map: any, config: MeasureConfig = {}) {
    this.sdMap = map
    this.map = map
    this.config = {
      enabled: true,
      lineColor: '#ff0000',
      fillColor: '#ff0000',
      lineWidth: 2,
      pointRadius: 3.5,
      ...config
    }
  }
 
  /**
   * @description 启动距离测量
   * @param layerId - 图层ID
   * @returns Promise<void>
   * @public
   */
  public async measureDistance(layerId: string): Promise<void> {
    if (!this.config.enabled || !this.map) {
      console.warn('测量工具未启用或地图实例无效')
      return
    }
    
    await this.startMeasurement(layerId, 'distance')
  }

  /**
   * @description 启动面积测量
   * @param layerId - 图层ID
   * @returns Promise<void>
   * @public
   */
  public async measureArea(layerId: string): Promise<void> {
    if (!this.config.enabled || !this.map) {
      console.warn('测量工具未启用或地图实例无效')
      return
    }
    
    await this.startMeasurement(layerId, 'area')
  }

  /**
   * @description 统一的测量启动方法
   * @param layerId - 图层ID
   * @param type - 测量类型 ('distance' | 'area')
   * @private
   */
  private async startMeasurement(layerId: string, type: 'distance' | 'area'): Promise<void> {
    // 如果已有活动测量，先停止
    if (this.isMeasure || this.currentMeasureType) {
      console.log('停止当前测量，启动新的测量')
      this.stopCurrentMeasurement()
    }
    
    // 设置当前测量状态
    this.currentMeasureType = type
    this.currentLayerId = layerId
    this.isMeasure = true
    
    console.log(`开始${type === 'distance' ? '距离' : '面积'}测量，图层ID: ${layerId}`)
    
    if (type === 'distance') {
      await this.initDistanceMeasurement(layerId)
    } else {
      await this.initAreaMeasurementInternal(layerId)
    }
  }

  /**
   * @description 注册Marker对象
   * @param marker - Marker实例
   * @private
   */
  private registerMarker(marker: any): void {
    if (marker) {
      this.activeMarkers.add(marker)
    }
  }

  /**
   * @description 注册事件监听器
   * @param eventName - 事件名称
   * @param handler - 事件处理函数
   * @param target - 目标（可选）
   * @private
   */
  private registerEventListener(eventName: string, handler: (...args: any[]) => void, target?: string): void {
    if (!this.map) return
    
    const key = target ? `${eventName}-${target}` : eventName
    this.activeEventListeners.set(key, handler)
    
    if (target) {
      this.map.on(eventName, target, handler)
    } else {
      this.map.on(eventName, handler)
    }
  }
 
  /**
   * @description 初始化距离测量
   * @param layerId - 图层ID
   * @private
   */
  private async initDistanceMeasurement(layerId: string): Promise<void> {
    this.layerDistanceList.push(layerId)
    let isMeasure = true
    const map = this.map
    map.doubleClickZoom.disable()
    let isEdit = false
    let Geolist: any = []
    let dragPointOrder = 0
    let pointOnLine = [0, 0]
 
    const jsonPoint: any = {
      type: 'FeatureCollection',
      features: [],
    }
    const jsonLine: any = {
      type: 'FeatureCollection',
      features: [],
    }
    
    // 添加测量结果弹窗
    const ele = document.createElement('div')
    ele.setAttribute('class', 'measure-move')
    ele.setAttribute('style', 'border:1px solid #f00;padding:2px 5px 17px 5px;')
    const option: any = {
      element: ele,
      anchor: 'left',
      offset: [8, 0],
    }
    const tooltip: any = new Marker(option).setLngLat([0, 0]).addTo(map)
    // 临时 Marker 不注册到 activeMarkers 中，手动管理
 
    const eleM = document.createElement('div')
    eleM.setAttribute('style', 'border:1px solid #f00;')
    eleM.setAttribute('class', 'measure-move')
    const optionM: any = {
      element: eleM,
      anchor: 'left',
      offset: [8, 30],
    }
    eleM.innerHTML = '单击确定起点';
    const moveTip: any = new Marker(optionM).setLngLat([0, 0]).addTo(map)
    // 临时 Marker 不注册到 activeMarkers 中，手动管理
    let hoverTip: any = null;
 
    // 添加测量图层
    map.addSource('points' + layerId, {
      type: 'geojson',
      data: jsonPoint,
    })
    map.addSource('point-move' + layerId, {
      type: 'geojson',
      data: jsonPoint,
    })
    map.addSource('line' + layerId, {
      type: 'geojson',
      data: jsonLine,
    })
    map.addSource('line-move' + layerId, {
      type: 'geojson',
      data: jsonLine,
    })
    map.addSource('point-follow' + layerId, {
      type: 'geojson',
      data: jsonPoint,
    })
    map.addLayer({
      id: 'line' + layerId,
      type: 'line',
      source: 'line' + layerId,
      paint: {
        'line-color': '#ff0000',
        'line-width': 2,
        'line-opacity': 0.65,
      },
    })
    map.addLayer({
      id: 'line-move' + layerId,
      type: 'line',
      source: 'line-move' + layerId,
      paint: {
        'line-color': '#ff0000',
        'line-width': 2,
        'line-opacity': 0.65,
        'line-dasharray': [5, 2],
      },
    })
    map.addLayer({
      id: 'points' + layerId,
      type: 'circle',
      source: 'points' + layerId,
      paint: {
        'circle-color': '#ffffff',
        'circle-radius': 3.5,
        'circle-stroke-width': 1.5,
        'circle-stroke-color': '#ff0000',
      },
    })
    map.addLayer({
      id: 'point-move' + layerId,
      type: 'circle',
      source: 'point-move' + layerId,
      paint: {
        'circle-color': '#ffffff',
        'circle-radius': 3.5,
        'circle-stroke-width': 1.5,
        'circle-stroke-color': '#ff0000',
      },
    })
    map.addLayer({
      id: 'point-follow' + layerId,
      type: 'circle',
      source: 'point-follow' + layerId,
      paint: {
        'circle-color': '#199afc',
        'circle-radius': 5.5,
        'circle-stroke-width': 1.5,
        'circle-stroke-color': '#ffffff',
      },
    })
 
    // 清除面积测量
    this.setArea = () => {
      isMeasure = false
      map.removeLayer('point-move' + layerId)
      map.removeLayer('line-move' + layerId)
      return isMeasure
    }
 
    // 事件处理函数
    const addPointforJSON = (_e: any) => {
      if (isMeasure) {
        const point: any = {
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [_e.lngLat.lng, _e.lngLat.lat],
          },
          properties: {
            id: String(new Date().getTime()),
          },
        }
        jsonPoint.features.push(point)
        map.getSource('points' + layerId).setData(jsonPoint)
        drawLine(jsonPoint)
        addMeasureRes(jsonPoint)
      }
    }
 
    const drawLine = (jsonPoint: any) => {
      if (jsonPoint.features.length > 1) {
        jsonLine.features = []
        for (let i = 0; i < jsonPoint.features.length - 1; i++) {
          const coords = jsonPoint.features[i].geometry.coordinates
          const next_coords = jsonPoint.features[i + 1].geometry.coordinates
          jsonLine.features.push({
            type: 'Feature',
            geometry: {
              type: 'LineString',
              coordinates: [coords, next_coords],
            },
          })
        }
        map.getSource('line' + layerId).setData(jsonLine)
      }
    }
 
    const addMeasureRes = (jsonPoint: any) => {
      if (jsonPoint.features.length > 0) {
        removedom()
        const pointList = []
        for (let i = 0; i < jsonPoint.features.length; i++) {
          const coords = jsonPoint.features[i].geometry.coordinates
          pointList.push(coords)
 
          const element: any = document.createElement('div')
          element.setAttribute('class', 'measure-result ' + layerId)
          const option: any = {
            element: element,
            anchor: 'left',
            offset: [8, 0],
          }
          element.innerHTML = i === 0 ? '起点' : getLength(pointList);
          if ((jsonPoint.features.length === i + 1) && !isMeasure) {
            element.setAttribute('style', 'border:1px solid #f00;margin: 20px 0 0 -10px;')
            element.innerHTML = `总长：<span style="color:red">${getLength(pointList)}</span>`
          }
          const marker = new Marker(option).setLngLat(coords).addTo(map)
          this.registerMarker(marker)
            }
          }
    }
 
    const getLength = (pointList: any) => {
      const line = turf.lineString(pointList)
      let len: any = turf.length(line)
      if (len < 1) {
        len = Math.round(len * 1000) + '米'
      } else {
        len = len.toFixed(2) + '公里'
      }
      return len
    }
 
    const removedom = () => {
      const dom = document.getElementsByClassName('measure-result ' + layerId)
      const len = dom.length
      if (len) {
        for (let i = len - 1; i >= 0; i--) {
          if (dom[i]) dom[i].remove()
        }
      }
    }
 
    const mouseMove = (_e: any) => {
      if (isMeasure) {
        map.getCanvas().style.cursor = 'default'
        const coords = [_e.lngLat.lng, _e.lngLat.lat]
        const jsonp = {
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: coords,
          },
        }
        map.getSource('point-move' + layerId).setData(jsonp)
 
        if (jsonPoint.features.length > 0) {
          moveTip.remove();
          const pointList = []
          for (let i = 0; i < jsonPoint.features.length; i++) {
            const coord = jsonPoint.features[i].geometry.coordinates
            pointList.push(coord)
          }
          pointList.push(coords)
          const prev = jsonPoint.features[jsonPoint.features.length - 1]
          const jsonl = {
            type: 'Feature',
            geometry: {
              type: 'LineString',
              coordinates: [prev.geometry.coordinates, coords],
            },
          }
          map.getSource('line-move' + layerId).setData(jsonl)
          ele.innerHTML = `总长：<span style="color:red">${getLength(pointList)}</span></br> <span style="color:#999;">单击确定地点，双击结束</span>`
          tooltip.setLngLat(coords)
        } else {
          moveTip.setLngLat(coords)
        }
      }
    }
 
    const finish = (_e: any) => {
      if (isMeasure) {
        // 阻止事件传播，防止触发地图双击缩放
        if (_e && _e.preventDefault) _e.preventDefault()
        if (_e && _e.stopPropagation) _e.stopPropagation()
        
        setTimeout(() => {
          isMeasure = false
          this.isMeasure = false
          
          // 显示最终测量结果
          if (jsonPoint.features.length >= 2) {
      addMeasureRes(jsonPoint)
    }
 
          // 清理临时元素
          map.getCanvas().style.cursor = 'default'
          tooltip.remove()
          moveTip.remove()
          
          // 清理临时图层
          this.safeRemoveLayer('point-move' + layerId)
          this.safeRemoveLayer('line-move' + layerId)
          this.safeRemoveLayer('point-follow' + layerId)
          this.safeRemoveSource('point-move' + layerId)
          this.safeRemoveSource('line-move' + layerId)
          this.safeRemoveSource('point-follow' + layerId)
          
          // 清理事件监听器但保留测量结果
          this.cleanupMeasurementEvents()
        }, 100);
      }
    }
 
    const mouseLeave = () => {
      map.getCanvas().style.cursor = "";
      if (hoverTip) {
        hoverTip.remove()
        hoverTip = null
        }
      }
 
    // 注册事件监听器
    this.registerEventListener('click', addPointforJSON)
    this.registerEventListener('mousemove', mouseMove)
    this.registerEventListener('dblclick', finish)
    this.registerEventListener('mouseleave', mouseLeave, 'points' + layerId)
    }
 
  /**
   * @description 初始化面积测量（内部方法）
   * @param layerId - 图层ID
   * @private
   */
  private async initAreaMeasurementInternal(layerId: string): Promise<void> {
    this.layerAreaList.push(layerId)
    let isMeasure = true
    const map = this.map
    map.doubleClickZoom.disable()
    let isEdit = false
    let Geolist: any = []
    let dragPointOrder = 0
    let pointOnLine = [0, 0]
 
    const jsonPoint: any = {
      type: 'FeatureCollection',
      features: [],
    }
    const jsonLine: any = {
      type: 'FeatureCollection',
      features: [],
    }
 
    const ele = document.createElement('div')
    ele.setAttribute('class', 'measure-move')
    const option: any = {
      element: ele,
      anchor: 'left',
      offset: [8, 0],
    }
    const tooltip = new Marker(option).setLngLat([0, 0]).addTo(map)
    // 临时 Marker 不注册到 activeMarkers 中，手动管理
 
    map.addSource('points-area' + layerId, {
      type: 'geojson',
      data: jsonPoint,
    })
    map.addSource('point-move' + layerId, {
      type: 'geojson',
      data: jsonPoint,
    })
    map.addSource('line-area' + layerId, {
      type: 'geojson',
      data: jsonLine,
    })
    map.addSource('line-move' + layerId, {
      type: 'geojson',
      data: jsonLine,
    })
    map.addLayer({
      id: 'line-move' + layerId,
      type: 'line',
      source: 'line-move' + layerId,
      paint: {
        'line-color': '#ff0000',
        'line-width': 2,
        'line-opacity': 0.65,
        'line-dasharray': [5, 2],
      },
    })
    map.addLayer({
      id: 'line-area' + layerId,
      type: 'fill',
      source: 'line-area' + layerId,
      paint: {
        'fill-color': '#ff0000',
        'fill-opacity': 0.1,
      },
    })
    map.addLayer({
      id: 'line-area-stroke' + layerId,
      type: 'line',
      source: 'line-area' + layerId,
      paint: {
        'line-color': '#ff0000',
        'line-width': 2,
        'line-opacity': 0.65,
      },
    })
    map.addLayer({
      id: 'points-area' + layerId,
      type: 'circle',
      source: 'points-area' + layerId,
      paint: {
        'circle-color': '#ffffff',
        'circle-radius': 3.5,
        'circle-stroke-width': 1.5,
        'circle-stroke-color': '#ff0000',
      },
    })
    map.addLayer({
      id: 'point-move' + layerId,
      type: 'circle',
      source: 'point-move' + layerId,
      paint: {
        'circle-color': '#ffffff',
        'circle-radius': 3.5,
        'circle-stroke-width': 1.5,
        'circle-stroke-color': '#ff0000',
      },
    })
 
    this.setDistance = () => {
      isMeasure = false
      map.removeLayer('point-move' + layerId)
      map.removeLayer('line-move' + layerId)
      return isMeasure
    }
 
    // 事件处理函数
    const addPointforJSON = (_e: any) => {
      if (isMeasure) {
        const point = {
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [_e.lngLat.lng, _e.lngLat.lat],
          },
          properties: {
            id: String(new Date().getTime()),
          },
        }
        jsonPoint.features.push(point)
        map.getSource('points-area' + layerId).setData(jsonPoint)
        addMeasureRes(jsonPoint)
      }
    }
 
    const addMeasureRes = (jsonPoint: any) => {
      if (jsonPoint.features.length >= 3) {
        removedom()
        const pointList = []
        for (let i = 0; i < jsonPoint.features.length; i++) {
          const coords = jsonPoint.features[i].geometry.coordinates
          pointList.push(coords)
        }
        
        // 在面积测量完成时显示最终结果
        if (!isMeasure) {
          const lastCoords = jsonPoint.features[jsonPoint.features.length - 1].geometry.coordinates
            const element = document.createElement('div')
            element.setAttribute('class', 'measure-result ' + layerId)
          element.setAttribute('style', 'border:1px solid #f00;padding:2px 5px;background:white;')
            const option: any = {
              element: element,
              anchor: 'left',
            offset: [8, 0],
            }
          element.innerHTML = `总面积：<span style="color:red">${getArea(pointList)}</span>`
          const marker = new Marker(option).setLngLat(lastCoords).addTo(map)
          this.registerMarker(marker)
          }
        }
      }
 
    const getArea = (pointList: any) => {
      pointList.push(pointList[0])
      const polygon = turf.polygon([pointList])
      let area: any = turf.area(polygon)
      if (area < 10000) {
        area = Math.round(area) + '平方米'
      } else {
        area = (area / 1000000).toFixed(2) + '平方公里'
      }
      return area
    }
 
    const removedom = () => {
      const dom = document.getElementsByClassName('measure-result ' + layerId)
      const len = dom.length
      if (len) {
        for (let i = len - 1; i >= 0; i--) {
          if (dom[i]) dom[i].remove()
        }
      }
    }
 
    const mouseMove = (_e: any) => {
      if (isMeasure) {
        map.getCanvas().style.cursor = 'default'
        const coords: any = [_e.lngLat.lng, _e.lngLat.lat]
        const jsonp = {
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: coords,
          },
        }
        map.getSource('point-move' + layerId).setData(jsonp)
 
        if (jsonPoint.features.length > 0) {
          if (jsonPoint.features.length === 1) {
            const prev = jsonPoint.features[jsonPoint.features.length - 1]
            const jsonl = {
              type: 'Feature',
              geometry: {
                type: 'LineString',
                coordinates: [prev.geometry.coordinates, coords],
              },
            }
            map.getSource('line-move' + layerId).setData(jsonl)
          } else {
            const json = {
              type: 'FeatureCollection',
              features: [],
            }
            map.getSource('line-move' + layerId).setData(json)
 
            const pointList = []
            for (let i = 0; i < jsonPoint.features.length; i++) {
              const coord = jsonPoint.features[i].geometry.coordinates
              pointList.push(coord)
            }
            pointList.push(coords)
            const pts = pointList.concat([pointList[0]])
            const jsona = {
              type: 'Feature',
              geometry: {
                type: 'Polygon',
                coordinates: [pts],
              },
            }
            map.getSource('line-area' + layerId).setData(jsona)
            ele.innerHTML = getArea(pointList)
            tooltip.setLngLat(coords)
          }
        }
      }
    }
 
    const finish = (_e: any) => {
      if (isMeasure) {
        // 阻止事件传播，防止触发地图双击缩放
        if (_e && _e.preventDefault) _e.preventDefault()
        if (_e && _e.stopPropagation) _e.stopPropagation()
        
        isMeasure = false
        this.isMeasure = false
        
        // 显示最终测量结果
        if (jsonPoint.features.length >= 3) {
          addMeasureRes(jsonPoint)
        }
        
        // 清理临时元素
        map.getCanvas().style.cursor = 'default'
        tooltip.remove()
        
        // 清理临时图层
        this.safeRemoveLayer('point-move' + layerId)
        this.safeRemoveLayer('line-move' + layerId)
        this.safeRemoveSource('point-move' + layerId)
        this.safeRemoveSource('line-move' + layerId)
        
        // 清理事件监听器但保留测量结果
        this.cleanupMeasurementEvents()
      }
    }
 
    // 注册事件监听器
    this.registerEventListener('click', addPointforJSON)
    this.registerEventListener('dblclick', finish)
    this.registerEventListener('mousemove', mouseMove)
  }
 
    /**
   * @description 获取所有活动的测量图层ID
   * @returns 所有测量图层ID数组
   * @public
   */
  public getAllMeasureLayerIds(): string[] {
    return [...this.layerDistanceList, ...this.layerAreaList]
  }

  /**
   * @description 检查是否有活动的测量
   * @returns 是否有活动测量
   * @public
   */
  public hasActiveMeasurements(): boolean {
    return this.layerDistanceList.length > 0 || this.layerAreaList.length > 0
  }

  /**
   * @description 清理测量事件监听器但保留结果
   * @private
   */
  private cleanupMeasurementEvents(): void {
    // 清理事件监听器
    this.activeEventListeners.forEach((handler, key) => {
      try {
        const [eventName, target] = key.split('-')
        if (target) {
          this.map.off(eventName, target, handler)
        } else {
          this.map.off(eventName, handler)
        }
      } catch (error) {
        console.warn(`移除事件监听器失败 (${key}):`, error)
        }
    })
    this.activeEventListeners.clear()
    
    // 重置测量状态
    this.isMeasure = false
    this.isEdit = false
    this.currentMeasureType = null
    this.currentLayerId = null
    
    // 延迟恢复地图的双击缩放功能，避免立即触发缩放
    setTimeout(() => {
      if (this.map?.doubleClickZoom) {
        this.map.doubleClickZoom.enable()
      }
    }, 200)
        }
 
  /**
   * @description 停止当前测量操作
   * @public
   */
  public stopCurrentMeasurement(): void {
    console.log('停止当前测量操作')
    
    // 清理所有Marker对象
    this.activeMarkers.forEach(marker => {
      if (marker && typeof marker.remove === 'function') {
        try {
          marker.remove()
        } catch (error) {
          console.warn('移除Marker失败:', error)
        }
      }
    })
    this.activeMarkers.clear()
    
    // 清理事件监听器
    this.activeEventListeners.forEach((handler, key) => {
      try {
        const [eventName, target] = key.split('-')
        if (target) {
          this.map.off(eventName, target, handler)
        } else {
          this.map.off(eventName, handler)
    }
      } catch (error) {
        console.warn(`移除事件监听器失败 (${key}):`, error)
      }
    })
    this.activeEventListeners.clear()
    
    // 重置测量状态
    this.isMeasure = false
    this.isEdit = false
    this.currentMeasureType = null
    this.currentLayerId = null
    
    // 延迟恢复地图的双击缩放功能，避免立即触发缩放
    setTimeout(() => {
      if (this.map?.doubleClickZoom) {
        this.map.doubleClickZoom.enable()
      }
    }, 200)
    
    console.log('当前测量操作已完全停止')
    }
 
  /**
   * @description 获取当前测量状态
   * @returns 当前测量状态信息
   * @public
   */
  public getCurrentMeasureStatus(): {
    isActive: boolean;
    type: 'distance' | 'area' | null;
    layerId: string | null;
    isEditing: boolean;
  } {
    return {
      isActive: this.isMeasure,
      type: this.currentMeasureType,
      layerId: this.currentLayerId,
      isEditing: this.isEdit
    }
  }

  /**
   * @description 安全检查图层是否存在
   * @param layerId - 图层ID
   * @returns 图层是否存在
   * @private
   */
  private layerExists(layerId: string): boolean {
    if (!this.map || !layerId) return false
    try {
      return this.map.getLayer(layerId) !== undefined
    } catch (error) {
      return false
      }
    }
 
  /**
   * @description 安全检查数据源是否存在
   * @param sourceId - 数据源ID
   * @returns 数据源是否存在
   * @private
   */
  private sourceExists(sourceId: string): boolean {
    if (!this.map || !sourceId) return false
    try {
      return this.map.getSource(sourceId) !== undefined
    } catch (error) {
      return false
        }
      }
 
  /**
   * @description 安全移除图层
   * @param layerId - 图层ID
   * @private
   */
  private safeRemoveLayer(layerId: string): void {
    if (this.layerExists(layerId)) {
      try {
        this.map.removeLayer(layerId)
      } catch (error) {
        console.warn(`移除图层 ${layerId} 失败:`, error)
      }
    }
    }
 
  /**
   * @description 安全移除数据源
   * @param sourceId - 数据源ID
   * @private
   */
  private safeRemoveSource(sourceId: string): void {
    if (this.sourceExists(sourceId)) {
      try {
        this.map.removeSource(sourceId)
      } catch (error) {
        console.warn(`移除数据源 ${sourceId} 失败:`, error)
      }
    }
  }
 
  /**
   * @description 清除所有测量要素
   * @public
   */
  public clearMeasureAll(): void {
    console.log('开始清除所有测量要素')
    
    // 停止当前测量操作
    this.stopCurrentMeasurement()
    
    // 移除所有测量相关的 DOM 元素
    const dom = document.getElementsByClassName('measure-result')
    const len = dom.length
    if (len) {
      for (let i = len - 1; i >= 0; i--) {
        if (dom[i]) dom[i].remove()
      }
    }
    const dom1 = document.getElementsByClassName('measure-move')
    const len2 = dom1.length
    if (len2) {
      for (let i = len2 - 1; i >= 0; i--) {
        if (dom1[i]) dom1[i].remove()
      }
    }

    // 安全移除所有距离测量相关的图层和数据源
    if (this.layerDistanceList && this.layerDistanceList.length > 0) {
      for (let i = 0; i < this.layerDistanceList.length; i++) {
        const layerid = this.layerDistanceList[i]
        console.log(`清理距离测量图层: ${layerid}`)
        
        // 安全移除图层
        this.safeRemoveLayer('points' + layerid)
        this.safeRemoveLayer('line' + layerid)
        this.safeRemoveLayer('point-move' + layerid)
        this.safeRemoveLayer('line-move' + layerid)
        this.safeRemoveLayer('point-follow' + layerid)
          
        // 安全移除数据源
        this.safeRemoveSource('points' + layerid)
        this.safeRemoveSource('point-move' + layerid)
        this.safeRemoveSource('line' + layerid)
        this.safeRemoveSource('line-move' + layerid)
        this.safeRemoveSource('point-follow' + layerid)
      }
    }
    this.layerDistanceList = []

    // 安全移除所有面积测量相关的图层和数据源
    if (this.layerAreaList && this.layerAreaList.length > 0) {
      for (let i = 0; i < this.layerAreaList.length; i++) {
        const layerid = this.layerAreaList[i]
        console.log(`清理面积测量图层: ${layerid}`)
        
        // 安全移除图层
        this.safeRemoveLayer('points-area' + layerid)
        this.safeRemoveLayer('line-area' + layerid)
        this.safeRemoveLayer('line-area-stroke' + layerid)
        this.safeRemoveLayer('point-move' + layerid)
        this.safeRemoveLayer('line-move' + layerid)
          
        // 安全移除数据源
        this.safeRemoveSource('points-area' + layerid)
        this.safeRemoveSource('point-move' + layerid)
        this.safeRemoveSource('line-area' + layerid)
        this.safeRemoveSource('line-move' + layerid)
      }
    }
    this.layerAreaList = []

    // 重置所有状态
    this.isMeasure = false
    this.isEdit = false
    this.currentMeasureType = null
    this.currentLayerId = null
    this.dragPointOrder = 0
    this.pointOnLine = [0, 0]
    this.Geolist = []

    // 恢复地图的双击缩放功能
    if (this.map?.doubleClickZoom) {
    this.map.doubleClickZoom.enable()
    }
    
    console.log('所有测量要素已清除')
  }
}
 
 