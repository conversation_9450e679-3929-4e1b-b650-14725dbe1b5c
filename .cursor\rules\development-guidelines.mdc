---
description: 
globs: 
alwaysApply: false
---
# 开发规范和最佳实践

## 代码规范

### TypeScript 开发规范
- **类型安全**：所有公开接口必须有明确的类型定义
- **JSDoc注释**：使用详细的JSDoc为类、方法、参数提供文档
- **错误处理**：使用 try-catch 块处理可能的异常，提供有意义的错误信息
- **异步操作**：优先使用 async/await 而不是回调函数

### Vue 组件开发规范
- **Composition API**：优先使用 Vue 3 Composition API
- **Props 验证**：为所有 props 提供类型定义和默认值
- **事件命名**：使用 kebab-case 命名自定义事件
- **生命周期**：正确使用 onMounted、onUnmounted 等生命周期钩子

## 地图相关开发规范

### 绘制工具集成
使用绘制功能时，参考 [SpatialQueryButtons.vue](mdc:src/components/SpatialQueryButtons.vue) 的实现模式：

```typescript
// 1. 获取绘制工具实例
const drawTool = AppMaplibre.getDrawTool()

// 2. 检查状态并添加事件监听
if (!drawTool.isEnabled()) {
  drawTool.start()
}
drawTool.addEventListener(DrawEventTypeEnum.DRAW_FINISH, handleDrawFinish)

// 3. 组件销毁时清理
onUnmounted(() => {
  if (drawTool) {
    drawTool.removeEventListener(DrawEventTypeEnum.DRAW_FINISH, handleDrawFinish)
    drawTool.stop()
  }
})
```

### 错误处理模式
实现分级错误处理机制：

```typescript
try {
  // 尝试执行操作
  drawTool.clearAllFeatures()
} catch (error) {
  console.error('操作失败，尝试恢复:', error)
  try {
    // 第二级恢复
    AppMaplibre.recreateDrawTool()
  } catch (recoveryError) {
    // 最终错误处理
    console.error('恢复失败:', recoveryError)
    // 通知用户或降级功能
  }
}
```

## 性能优化原则

### 内存管理
- **事件监听器**：确保在组件销毁时移除所有事件监听器
- **定时器清理**：使用 setTimeout 或 setInterval 时，在组件销毁时清理
- **单例实例**：合理使用单例模式，避免重复创建昂贵的对象

### 状态管理
- **响应式数据**：只将必要的数据设为响应式
- **计算属性**：使用 computed 缓存复杂计算结果
- **避免不必要的重渲染**：合理使用 v-memo 和 key

## 调试和日志

### 日志规范
- **中文日志**：面向用户的日志使用中文
- **详细上下文**：包含足够的上下文信息帮助定位问题
- **分级输出**：区分 console.log、console.warn、console.error

### 调试技巧
- **状态检查**：在关键操作前检查对象状态
- **边界条件**：测试组件在边界条件下的行为
- **清理验证**：验证资源是否正确清理，避免内存泄漏

## 测试策略

### 组件测试
- **生命周期测试**：测试组件的挂载和销毁
- **事件触发测试**：验证事件是否正确触发和处理
- **状态恢复测试**：测试异常情况下的状态恢复

### 集成测试  
- **多次开关测试**：验证组件多次开关后的功能正常性
- **引擎切换测试**：测试在不同地图引擎间的兼容性
- **错误恢复测试**：模拟异常情况，验证自动恢复机制

