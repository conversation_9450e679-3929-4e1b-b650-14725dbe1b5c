/**
 * @fileoverview 管点API接口
 * @description 管点数据的增删改查操作接口
 * <AUTHOR>
 * @version 1.0.0
 */

import hRequest from '@/utils/http'
import type { DataType } from '@/utils/http/types'

/**
 * @description 管点业务对象
 */
export interface GsPtDto {
  /** 主键ID */
  gid?: any;
  /** 管类 */
  gl?: string;
  /** 管线点编号 */
  gxddh?: string;
  /** 属性 */
  sx?: string;
  /** 附属物 */
  fsw?: string;
  /** 地面高程 */
  dmgc?: number;
  /** X坐标 */
  x?: string;
  /** Y坐标 */
  y?: string;
  /** 接收 */
  js?: number;
  /** 井盖规格 */
  jggg?: string;
  /** 井盖材质 */
  jgcz?: string;
  /** 所在道路 */
  szdl?: string;
  /** 经度 */
  longitude: number;
  /** 纬度 */
  latitude: number;
}

/**
 * @description 管点视图对象
 */
export interface GsPtVo extends GsPtDto {
  /** GeoJSON格式的几何数据 */
  geojson?: string;
}

/**
 * @description 管点分页视图对象
 */
export interface GsPtPageVo extends Omit<GsPtDto, 'geojson'> {
  // 分页视图对象继承业务对象，但不包含geojson字段
}

/**
 * @description 管点分页查询参数接口
 */
export interface PipeNodePageQuery {
  /** 分页大小 */
  pageSize?: number;
  /** 当前页数 */
  pageNum?: number;
  /** 排序列 */
  orderByColumn?: string;
  /** 排序方向（desc或asc） */
  isAsc?: string;
  /** 首页记录数 */
  firstNum?: number;
}

/**
 * @description 管点分页返回结果
 */
export interface PageInfoGsPtPageVo {
  /** 总记录数 */
  total: number;
  /** 数据列表 */
  list: GsPtPageVo[];
  /** 分页信息 */
  pageNum: number;
  pageSize: number;
  size: number;
  startRow: number;
  endRow: number;
  pages: number;
  prePage: number;
  nextPage: number;
  isFirstPage: boolean;
  isLastPage: boolean;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  navigatePages: number;
  navigatepageNums: number[];
  navigateFirstPage: number;
  navigateLastPage: number;
}

/**
 * @description 通用返回结果包装
 */
export interface ApiResult<T> {
  /** 状态码 */
  code: number;
  /** 返回消息 */
  msg: string;
  /** 返回数据 */
  data: T;
}

/**
 * @description 管点列表查询
 * @returns Promise<ApiResult<GsPtVo[]>> 返回管点列表数据
 */
export const pipeNodeList = () => {
  return hRequest.get<ApiResult<GsPtVo[]>>({
    url: '/analyse/gs/pt/list'
  })
}

/**
 * @description 管点分页查询
 * @param params 分页查询参数
 * @returns Promise<ApiResult<PageInfoGsPtPageVo>> 返回分页查询结果
 */
export const pipeNodePage = (params: PipeNodePageQuery) => {
  return hRequest.get<ApiResult<PageInfoGsPtPageVo>>({
    url: '/analyse/gs/pt/page',
    params
  })
}
/**
 * @description 判断是否为悬挂点
 * @param ptNodeGid 管点编号
 * @returns Promise<ApiResult<boolean>> 返回判断结果
 */
export const pipeNodeIsHangPt = (ptNode: string) => {
  return hRequest.get<ApiResult<boolean>>({
    url: `/analyse/gs/pt/isHangPt`,
    params: {
      gxddh: ptNode
    }
  })
}

/**
 * @description 新增管点
 * @param data 管点数据对象
 * @returns Promise<ApiResult<number>> 返回新增结果，数据为生成的ID
 */
export const pipeNodeAdd = (data: GsPtDto) => {
  return hRequest.post<ApiResult<number>>({
    url: '/analyse/gs/pt',
    data
  })
}

/**
 * @description 修改管点
 * @param data 管点数据对象
 * @returns Promise<ApiResult<number>> 返回修改结果，数据为影响的记录数
 */
export const pipeNodeEdit = (data: GsPtDto) => {
  return hRequest.put<ApiResult<number>>({
    url: '/analyse/gs/pt',
    data
  })
}

/**
 * @description 管点详情查询
 * @param gid 管点GID
 * @returns Promise<ApiResult<GsPtVo>> 返回管点详情数据
 */
export const pipeNodeDetail = (gid: number | string) => {
  return hRequest.get<ApiResult<GsPtVo>>({
    url: `/analyse/gs/pt/${gid}`,
  })
}

/**
 * @description 删除管点
 * @param gid 管点GID
 * @returns Promise<ApiResult<void>> 返回删除结果
 */
export const pipeNodeDelete = (gid: number | string) => {
  return hRequest.delete<ApiResult<void>>({
    url: `/analyse/gs/pt/${gid}`,
  })
} 
/**
 * @description 导出管点shp文件
 * @param fsw 附属物
 * @returns Promise<ApiResult<void>> 返回导出结果
 */
export const pipeNodeExportShp = (fsw: string) => {
  return hRequest.get<ApiResult<void>>({
    url: `/analyse/gs/pt/export`,
    params: {
      fsw: fsw
    },
    responseType: 'blob',
    timeout: 25000
  })
} 

/**
 * @description 按fsw查询列表
 * @returns Promise<ApiResult<StatisticsData>> 返回管材列表数据
 */
export const queryPipeFswList = () => {
  return hRequest.get<ApiResult<any>>({
    url: '/analyse/gs/pt/fsw/list'
  })
}