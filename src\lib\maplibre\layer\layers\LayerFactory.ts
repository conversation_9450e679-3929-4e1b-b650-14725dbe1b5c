/**
 * @fileoverview 图层工厂类
 * @description 负责根据配置创建不同类型的图层实例，支持所有图层类型
 * <AUTHOR>
 * @version 2.0.0
 */

import { BaseLayer } from './BaseLayer'
import { LayerGroup } from './LayerGroup'
import { ImageryLayer } from './ImageryLayer'
import { TdtImageryLayer } from './TdtImageryLayer'
import { CustomXYZLayer } from './CustomXYZLayer'
import { MvtLayer } from './MvtLayer'
import { BaiduMapLayer } from './BaiduMapLayer'
import { AmapLayer } from './AmapLayer'
import { FeatureLayer } from './FeatureLayer'
import { 
  LayerType, 
  type LayerConfig,
  type BaiduLayerConfig,
  type AmapLayerConfig,
  type TdtLayerConfig,
  type CustomXYZLayerConfig,
  type MvtLayerConfig,
  type ImageryLayerConfig,
  type LayerGroupConfig,
  type FeatureLayerConfig,
  type DeviceLayerConfig
} from '../types/LayerTypes'
import DeviceLayer from './DeviceLayer'
import AlarmLayer from './AlarmLayer'

/**
 * @class LayerFactory
 * @description 图层工厂类，提供统一的图层创建接口
 */
export class LayerFactory {
  /**
   * @description 创建图层实例
   * @param options - 图层配置
   * @returns 图层实例
   * @throws 当图层类型不支持时抛出错误
   */
  static createLayer(options: LayerConfig): BaseLayer {
    // 验证基础配置
    if (!options.type) {
      throw new Error('图层配置缺少必需的 type 字段')
    }

    try {
      switch (options.type) {
        case LayerType.GROUP:
          return new LayerGroup(options as LayerGroupConfig)
          
        case LayerType.IMAGE:
          return new ImageryLayer(options as ImageryLayerConfig)
          
        case LayerType.TDT:
          return new TdtImageryLayer(options as TdtLayerConfig)
          
        case LayerType.BAIDU:
          return new BaiduMapLayer(options as BaiduLayerConfig)
          
        case LayerType.AMAP:
          return new AmapLayer(options as AmapLayerConfig)
          
        case LayerType.CUSTOM_XYZ:
          return new CustomXYZLayer(options as CustomXYZLayerConfig)
          
        case LayerType.MVT:
          return new MvtLayer(options as MvtLayerConfig)
          
        case LayerType.FEATURE:
          return new FeatureLayer(options as FeatureLayerConfig)
        case LayerType.DEVICE:
          return new DeviceLayer(options as DeviceLayerConfig)
        case LayerType.ALARM:
          return new AlarmLayer(options as DeviceLayerConfig)
        default:
          throw new Error(`不支持的图层类型: ${options.type}`)
      }
    } catch (error) {
      console.error(`创建图层失败 [${options.type}]:`, error)
      throw error
    }
  }

  /**
   * @description 兼容旧版本的创建方法
   * @param type - 图层类型
   * @param options - 图层配置
   * @returns 图层实例
   * @deprecated 请使用 createLayer 方法
   */
  static crateLayer(type: string, options: any): BaseLayer {
    console.warn('crateLayer 方法已废弃，请使用 createLayer 方法')
    return this.createLayer({ ...options, type })
  }

  /**
   * @description 获取支持的图层类型列表
   * @returns 支持的图层类型数组
   */
  static getSupportedLayerTypes(): string[] {
    return Object.values(LayerType)
  }

  /**
   * @description 验证图层配置
   * @param options - 图层配置
   * @returns 验证结果
   */
  static validateLayerConfig(options: LayerConfig): { valid: boolean; errors: string[]; warnings: string[] } {
    const result = {
      valid: true,
      errors: [] as string[],
      warnings: [] as string[]
    }

    // 检查必需字段
    if (!options.type) {
      result.errors.push('缺少必需的 type 字段')
      result.valid = false
    }

    if (!options.id && !options.title) {
      result.warnings.push('建议提供 id 或 title 字段以便识别图层')
    }

    // 检查图层类型是否支持
    const supportedTypes = this.getSupportedLayerTypes()
    const legacyTypes = ['group', 'imagery', 'image', 'tdt', 'customxyz', 'mvt', 'baidu', 'amap']
    
    if (!supportedTypes.includes(options.type) && !legacyTypes.includes(options.type)) {
      result.errors.push(`不支持的图层类型: ${options.type}`)
      result.valid = false
    }

    // 检查是否使用了废弃的类型名称
    if (legacyTypes.includes(options.type)) {
      result.warnings.push(`图层类型 "${options.type}" 已废弃，建议使用 LayerType 枚举`)
    }

    return result
  }

  /**
   * @description 创建图层并验证配置
   * @param options - 图层配置
   * @returns 图层实例
   * @throws 当配置无效时抛出错误
   */
  static createLayerWithValidation(options: LayerConfig): BaseLayer {
    const validation = this.validateLayerConfig(options)
    
    if (!validation.valid) {
      throw new Error(`图层配置验证失败: ${validation.errors.join(', ')}`)
    }

    if (validation.warnings.length > 0) {
      console.warn('图层配置警告:', validation.warnings.join(', '))
    }

    return this.createLayer(options)
  }

  /**
   * @description 批量创建图层
   * @param configArray - 图层配置数组
   * @returns 图层实例数组
   */
  static createLayers(configArray: LayerConfig[]): BaseLayer[] {
    return configArray.map(config => this.createLayer(config))
  }

  /**
   * @description 获取图层类型的默认配置
   * @param layerType - 图层类型
   * @returns 默认配置对象
   */
  static getDefaultConfig(layerType: LayerType | string): Partial<LayerConfig> {
    switch (layerType) {
      case LayerType.BAIDU:
      case 'baidu':
        return {
          type: LayerType.BAIDU,
          style: 'normal',
          showLabel: true,
          opacity: 1,
          show: true
        }

      case LayerType.AMAP:
      case 'amap':
        return {
          type: LayerType.AMAP,
          style: 'normal',
          showLabel: true,
          showRoad: true,
          lang: 'zh_cn',
          scale: 1,
          opacity: 1,
          show: true
        }

      case LayerType.TDT:
      case 'tdt':
        return {
          type: LayerType.TDT,
          style: 'vec',
          opacity: 1,
          show: true
        }

      case LayerType.CUSTOM_XYZ:
      case 'customxyz':
        return {
          type: LayerType.CUSTOM_XYZ,
          opacity: 1,
          show: true,
          minZoom: 0,
          maxZoom: 22
        }

      case LayerType.MVT:
      case 'mvt':
        return {
          type: LayerType.MVT,
          opacity: 1,
          show: true
        }

      case LayerType.GROUP:
      case 'group':
        return {
          type: LayerType.GROUP,
          show: true,
          children: []
        }

      case LayerType.IMAGE:
      case 'imagery':
      case 'image':
        return {
          type: LayerType.IMAGE,
          opacity: 1,
          show: true
        }

      case LayerType.FEATURE:
      case 'feature':
        return {
          type: LayerType.FEATURE,
          opacity: 1,
          show: true,
          features: [],
          editable: true,
          showLabels: true
        }

      default:
        return {
          opacity: 1,
          show: true
        }
    }
  }
}
