/**
 * 判断点是否在多边形内（射线法）
 */
function isPointInPolygon(
  point: [number, number],
  polygon: [number, number][]
): boolean {
  let [x, y] = point;
  let inside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const [xi, yi] = polygon[i];
    const [xj, yj] = polygon[j];
    const intersect =
      yi > y !== yj > y &&
      x < ((xj - xi) * (y - yi)) / (yj - yi + Number.EPSILON) + xi;
    if (intersect) inside = !inside;
  }
  return inside;
}

/**
 * 在多边形内随机生成一个经纬度
 */
function mockLngLatInPolygon(polygon?: [number, number][]): [number, number] {
  // 默认多边形区域（沙湾区范围）
  const defaultPolygon: [number, number][] = [
    [103.521827561, 29.415304612],
    [103.544111102, 29.397707684],
    [103.565272451, 29.422286679],
    [103.602304808, 29.461656545],
    [103.637253144, 29.500173948],
    [103.633245279, 29.508266335],
    [103.603266691, 29.515521018],
    [103.569761232, 29.485801415],
    [103.521827561, 29.415304612]
  ];

  const targetPolygon = polygon || defaultPolygon;
  
  // 计算外接矩形
  const lons = targetPolygon.map(p => p[0]);
  const lats = targetPolygon.map(p => p[1]);
  const minLon = Math.min(...lons);
  const maxLon = Math.max(...lons);
  const minLat = Math.min(...lats);
  const maxLat = Math.max(...lats);

  let point: [number, number];
  let tries = 0;
  do {
    const lng = minLon + Math.random() * (maxLon - minLon);
    const lat = minLat + Math.random() * (maxLat - minLat);
    point = [lng, lat];
    tries++;
    // 防止死循环
    if (tries > 10000) {
      console.warn('生成多边形内的点位超时，使用多边形中心点');
      return [
        (minLon + maxLon) / 2,
        (minLat + maxLat) / 2
      ];
    }
  } while (!isPointInPolygon(point, targetPolygon));
  
  return point;
}

export { mockLngLatInPolygon, isPointInPolygon };