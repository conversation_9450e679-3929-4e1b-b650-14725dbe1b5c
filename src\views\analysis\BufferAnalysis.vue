/** * @Description: 缓冲区分析 * @Date: 2025-01-10 * @Author: 项目开发团队 *
@LastEditTime: 2025-01-10 */
<template>
  <page-card class="tabulate-sta" :close-icon="false" title="缓冲区分析">
    <!-- 输入区域 -->
    <div class="input-section">
      <di class="label-text">缓冲距离：</di>
      <div class="input-container">
        <el-input-number
          v-model="bufferDistance"
          placeholder="请输入缓冲距离"
          :min="1"
          :max="10000"
          :step="10"
          controls-position="right"
          class="requirement-input"
        />
        <span class="unit-text">(m)</span>
      </div>
    </div>
    <div class="buffer-analysis-container">
      <!-- 使用新的缓冲区分析绘制按钮组件 -->
      <BufferAnalysisDrawButtons
        :map-engine="mapEngine"
        :config="bufferDrawConfig"
        @draw-start="handleDrawStart"
        @draw-complete="handleDrawComplete"
        @draw-error="handleDrawError"
        @clear-all="handleClearAll"
      />
    </div>
  </page-card>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onUnmounted } from "vue";
import {
  ElDivider,
  ElTag,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage,
} from "element-plus";
import BufferAnalysisDrawButtons from "@/components/BufferAnalysisDrawButtons.vue";
import type {
  DrawType,
  MapEngineType,
  DrawResult,
  BufferDrawConfig,
} from "@/components/BufferAnalysisDrawButtons.vue";
import * as turf from "@turf/turf";
import { AppCesium } from "@/lib/cesium/AppCesium";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";

const bufferDistance = ref(500);
const route = useRoute();

/**
 * @description 计算地图引擎类型
 */
const mapEngine = computed((): MapEngineType => {
  return route.path.includes("cesium") ? "cesium" : "maplibre";
});

/**
 * @description 缓冲区分析绘制配置
 */
const bufferDrawConfig: Partial<BufferDrawConfig> = {
  // UI配置
  buttonSize: "default",
  buttonLayout: "horizontal",
  buttonSpacing: 8,
  // 功能配置
  enabledDrawTypes: ["point", "linestring", "polygon", "rectangle"],
  showTips: true,
  showResult: true,
  showResultDetails: true,
  // 绘制配置
  clearPreviousDrawing: true,
  autoSwitchToSelect: true,
};

// 组件状态
const currentResult = ref<DrawResult | null>(null);
const showExternalResult = ref(false); // 是否显示外部结果区域
const bufferGeometry = ref<any>(null); // 缓冲区几何体

// 分析参数
const analysisParams = ref({
  bufferRadius: 100, // 缓冲半径(米)
});

// 缓冲区图层管理
let cesiumBufferLayer: any = null;
let maplibreBufferSourceId = "buffer-source";
let maplibreBufferFillLayerId = "buffer-fill-layer";
let maplibreBufferLineLayerId = "buffer-line-layer";

/**
 * @description 处理绘制开始事件
 * @param {DrawType} type - 绘制类型
 */
const handleDrawStart = (type: DrawType) => {
  console.log(`缓冲区分析绘制开始: ${type}`);
  currentResult.value = null;
  showExternalResult.value = false;
};

/**
 * @description 处理绘制完成事件
 * @param {DrawResult} result - 绘制结果
 */
const handleDrawComplete = async (result: DrawResult) => {
  console.log("缓冲区分析绘制完成:", result);
  currentResult.value = result;
  showExternalResult.value = true;

  // 自动计算并渲染缓冲区
  try {
    await calculateAndRenderBuffer(result.geometry);
    // ElMessage.success(`${getDrawTypeLabel(result.type)}绘制完成，缓冲区已生成`);
  } catch (error) {
    console.error("缓冲区计算失败:", error);
    ElMessage.error("缓冲区计算失败");
  }
};

/**
 * @description 处理绘制错误事件
 * @param {object} error - 错误信息
 */
const handleDrawError = (error: { type: DrawType; message: string }) => {
  console.error("缓冲区分析绘制错误:", error);

  // 显示错误提示
  ElMessage.error(`${getDrawTypeLabel(error.type)}失败: ${error.message}`);
};

/**
 * @description 计算并渲染缓冲区
 * @param {any} geometry - 输入几何体
 */
const calculateAndRenderBuffer = async (geometry: any) => {
  if (!geometry) {
    throw new Error("几何体不能为空");
  }

  // 验证缓冲距离
  const distance = Number(bufferDistance.value);
  if (!distance || distance <= 0 || distance > 10000) {
    throw new Error("缓冲距离必须在1-10000米之间");
  }

  // 获取缓冲距离（转换为公里，turf使用公里单位）
  const bufferDistanceInKm = distance / 1000;

  console.log(`计算缓冲区 - 距离: ${distance}米 (${bufferDistanceInKm}公里)`);

  try {
    // 使用turf计算缓冲区
    const buffered = turf.buffer(geometry, bufferDistanceInKm, {
      units: "kilometers",
    });

    if (!buffered || !buffered.geometry) {
      throw new Error("缓冲区计算失败：无法生成有效的缓冲区几何体");
    }

    bufferGeometry.value = buffered;

    // 根据地图引擎渲染缓冲区
    if (mapEngine.value === "cesium") {
      await renderBufferOnCesium(buffered);
    } else {
      await renderBufferOnMapLibre(buffered);
    }

    console.log("缓冲区计算和渲染完成");
  } catch (error) {
    console.error("缓冲区计算失败:", error);
    throw error;
  }
};

/**
 * @description 在Cesium上渲染缓冲区
 * @param {any} bufferGeometry - 缓冲区几何体
 */
const renderBufferOnCesium = async (bufferGeometry: any) => {
  const { Cesium } = BC.Namespace;
  try {
    const viewer = AppCesium.getInstance().getViewer();

    // 清除之前的缓冲区GroundPrimitive
    if (cesiumBufferLayer) {
      viewer.scene.groundPrimitives.remove(cesiumBufferLayer);
      cesiumBufferLayer = null;
    }

    // 转换坐标为Cesium格式
    const coordinates = bufferGeometry.geometry.coordinates[0];
    const positions: number[] = [];

    coordinates.forEach((coord: number[]) => {
      positions.push(coord[0], coord[1]); // 经度, 纬度
    });

    // 创建多边形几何体
    const polygonGeometry = new Cesium.PolygonGeometry({
      polygonHierarchy: new Cesium.PolygonHierarchy(
        Cesium.Cartesian3.fromDegreesArray(positions)
      ),
      vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
    });

    // 创建几何体实例
    const geometryInstance = new Cesium.GeometryInstance({
      geometry: polygonGeometry,
      attributes: {
        color: Cesium.ColorGeometryInstanceAttribute.fromColor(
          Cesium.Color.BLUE.withAlpha(0.3) // 蓝色半透明
        ),
      },
    });

    // 创建GroundPrimitive进行渲染
    cesiumBufferLayer = new Cesium.GroundPrimitive({
      geometryInstances: geometryInstance,
      appearance: new Cesium.PerInstanceColorAppearance({
        translucent: true,
        closed: false,
      }),
      asynchronous: false,
    });

    // 添加到场景
    viewer.scene.groundPrimitives.add(cesiumBufferLayer);

    console.log("Cesium缓冲区GroundPrimitive渲染完成");
  } catch (error) {
    console.error("Cesium缓冲区GroundPrimitive渲染失败:", error);
    throw error;
  }
};

/**
 * @description 在MapLibre上渲染缓冲区
 * @param {any} bufferGeometry - 缓冲区几何体
 */
const renderBufferOnMapLibre = async (bufferGeometry: any) => {
  try {
    const map = AppMaplibre.getMap();

    if (!map) {
      throw new Error("MapLibre地图实例不存在");
    }

    // 移除现有的缓冲区图层
    if (map.getLayer(maplibreBufferFillLayerId)) {
      map.removeLayer(maplibreBufferFillLayerId);
    }
    if (map.getLayer(maplibreBufferLineLayerId)) {
      map.removeLayer(maplibreBufferLineLayerId);
    }
    if (map.getSource(maplibreBufferSourceId)) {
      map.removeSource(maplibreBufferSourceId);
    }

    // 添加缓冲区数据源
    map.addSource(maplibreBufferSourceId, {
      type: "geojson",
      data: bufferGeometry,
    });

    // 添加填充图层（蓝色半透明面）
    map.addLayer({
      id: maplibreBufferFillLayerId,
      type: "fill",
      source: maplibreBufferSourceId,
      layout: {},
      paint: {
        "fill-color": "#0080ff", // 蓝色
        "fill-opacity": 0.3, // 半透明
      },
    });

    // 添加边框图层（蓝色虚线）
    map.addLayer({
      id: maplibreBufferLineLayerId,
      type: "line",
      source: maplibreBufferSourceId,
      layout: {},
      paint: {
        "line-color": "#0080ff", // 蓝色
        "line-width": 2, // 线宽
        "line-dasharray": [5, 5], // 虚线样式
      },
    });

    console.log("MapLibre缓冲区渲染完成");
  } catch (error) {
    console.error("MapLibre缓冲区渲染失败:", error);
    throw error;
  }
};

/**
 * @description 清除缓冲区
 */
const clearBuffer = () => {
  try {
    if (mapEngine.value === "cesium") {
      // 清除Cesium缓冲区GroundPrimitive
      if (cesiumBufferLayer) {
        const viewer = AppCesium.getInstance().getViewer();
        viewer.scene.groundPrimitives.remove(cesiumBufferLayer);
        cesiumBufferLayer = null;
      }
    } else {
      // 清除MapLibre缓冲区图层
      const map = AppMaplibre.getMap();
      if (map) {
        if (map.getLayer(maplibreBufferFillLayerId)) {
          map.removeLayer(maplibreBufferFillLayerId);
        }
        if (map.getLayer(maplibreBufferLineLayerId)) {
          map.removeLayer(maplibreBufferLineLayerId);
        }
        if (map.getSource(maplibreBufferSourceId)) {
          map.removeSource(maplibreBufferSourceId);
        }
      }
    }

    bufferGeometry.value = null;
    console.log("缓冲区已清除");
  } catch (error) {
    console.error("清除缓冲区失败:", error);
  }
};

/**
 * @description 处理清空事件
 */
const handleClearAll = () => {
  console.log("清空所有绘制");

  // 清除缓冲区
  clearBuffer();

  currentResult.value = null;
  showExternalResult.value = false;

  // 重置分析参数
  analysisParams.value = {
    bufferRadius: 100,
  };

  ElMessage.info("已清空所有绘制结果和缓冲区");
};

/**
 * @description 开始缓冲区分析
 */
const startBufferAnalysis = () => {
  if (!currentResult.value || !currentResult.value.geometry) {
    ElMessage.warning("请先绘制几何体");
    return;
  }

  console.log("开始缓冲区分析:", {
    geometry: currentResult.value.geometry,
    params: analysisParams.value,
  });

  // TODO: 这里可以实现实际的缓冲区分析逻辑
  // 例如：调用后端API或使用前端几何计算库

  // 示例：模拟分析过程
  setTimeout(() => {
    ElMessage.success("缓冲区分析完成！（此为演示功能）");
  }, 2000);
};

/**
 * @description 重置分析
 */
const resetAnalysis = () => {
  analysisParams.value = {
    bufferRadius: 100,
  };

  ElMessage.info("已重置分析参数");
};

/**
 * @description 获取绘制类型标签
 * @param {DrawType} type - 绘制类型
 * @returns {string} 类型标签
 */
const getDrawTypeLabel = (type: DrawType): string => {
  const labels = {
    point: "点绘制",
    linestring: "线段绘制",
    polygon: "多边形绘制",
    rectangle: "矩形绘制",
  };
  return labels[type] || type;
};

/**
 * @description 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化的时间
 */
const formatTimestamp = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString();
};

/**
 * @description 监听缓冲距离变化，实时更新缓冲区
 */
watch(
  bufferDistance,
  async (newDistance, oldDistance) => {
    // 只有在距离发生变化且存在绘制结果时才重新计算
    if (
      newDistance !== oldDistance &&
      currentResult.value &&
      currentResult.value.geometry
    ) {
      try {
        console.log(`缓冲距离变化: ${oldDistance}米 -> ${newDistance}米`);
        await calculateAndRenderBuffer(currentResult.value.geometry);
      } catch (error) {
        console.error("缓冲距离变化时重新计算缓冲区失败:", error);
      }
    }
  },
  {
    // 增加防抖延迟，避免频繁计算
    flush: "post",
  }
);

/**
 * @description 组件卸载时清理资源
 */
onUnmounted(() => {
  console.log("缓冲区分析组件卸载，开始清理资源");

  // 清除缓冲区
  clearBuffer();

  // 清理Cesium GroundPrimitive
  if (cesiumBufferLayer) {
    try {
      const viewer = AppCesium.getInstance().getViewer();
      viewer.scene.groundPrimitives.remove(cesiumBufferLayer);
      cesiumBufferLayer = null;
      console.log("✓ Cesium缓冲区GroundPrimitive已清理");
    } catch (error) {
      console.warn("清理Cesium缓冲区GroundPrimitive失败:", error);
    }
  }

  console.log("缓冲区分析组件资源清理完成");
});
</script>

<style lang="scss" scoped>
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  // width: 550px;
  min-height: 120px;
  z-index: 1000;
}
.input-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .label-text {
    font-size: 14px;
    color: #2c3037;
  }

  .input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 350px;

    .requirement-input {
      flex: 1;
    }

    .unit-text {
      color: #909399;
      font-size: 14px;
    }
  }
}

// .buffer-analysis-container {
//   padding: 8px;
// }

.external-result {
  margin-top: 16px;

  .ml-2 {
    margin-left: 8px;
  }
}

.analysis-params {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;

  :deep(.el-form-item) {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  :deep(.el-form-item__label) {
    font-size: 12px;
    color: #606266;
  }

  :deep(.el-input-number) {
    .el-input__inner {
      font-size: 12px;
    }
  }

  :deep(.el-select) {
    .el-input__inner {
      font-size: 12px;
    }
  }

  :deep(.el-button) {
    font-size: 12px;
    padding: 6px 12px;
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
