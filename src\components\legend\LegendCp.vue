<template>
  <div class="legend-card" v-if="legends && legends.data.length > 0">
    <div class="legend-title">
      <span>图例</span>
      ({{ legends.title }})
    </div>
    <div class="min-hgt">
      <el-scrollbar>
        <div
          class="legend-item"
          :key="index"
          v-for="(legend, index) in legends.data"
        >
          <div class="legend-value" :style="'background:' + legend.value"></div>
          <span class="legend-name">{{ legend.name }}</span>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useLegend } from "./index";
import { storeToRefs } from "pinia";
const legend = useLegend();
const { legends } = storeToRefs(legend);
</script>
<style lang="scss" scoped>
.min-hgt {
  height: 180px;
}
.legend-card {
  position: absolute;
  bottom: 16px;
  right: 128px;
  border-radius: 8px;
  min-width: 180px;
  max-width: 280px;
  max-height: 200px;
  // overflow-y: hidden;
  padding: 16px 20px;
  z-index: 9999;
  pointer-events: auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  color: #374151;
  font-size: 13px;

  // 优化滚动条样式
  // &::-webkit-scrollbar {
  //   width: 4px;
  // }

  // &::-webkit-scrollbar-track {
  //   background: transparent;
  // }

  // &::-webkit-scrollbar-thumb {
  //   background: #d1d5db;
  //   border-radius: 2px;

  //   &:hover {
  //     background: #9ca3af;
  //   }
  // }

  .legend-item {
    display: flex;
    align-items: center;
    margin: 8px 0;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .legend-value {
      width: 20px;
      height: 12px;
      border-radius: 2px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      flex-shrink: 0;
    }

    .legend-name {
      margin-left: 12px;
      line-height: 1.4;
      color: #4b5563;
      font-weight: 400;
    }
  }
}

.legend-title {
  margin-bottom: 12px;
  color: #1f2937;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;

  span {
    color: #374151;
    font-weight: 700;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .legend-card {
    right: 16px;
    bottom: 80px;
    min-width: 160px;
    max-width: 240px;
    padding: 14px 16px;
    font-size: 12px;

    .legend-title {
      font-size: 13px;
      margin-bottom: 10px;
    }

    .legend-item {
      margin: 6px 0;

      .legend-value {
        width: 18px;
        height: 10px;
      }

      .legend-name {
        margin-left: 10px;
      }
    }
  }
}
</style>
