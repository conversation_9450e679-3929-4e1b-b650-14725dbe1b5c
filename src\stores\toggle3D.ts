/**
 * @fileoverview Cesium二三维切换功能管理
 * @description 提供Cesium引擎的二维/三维视角切换功能，仅支持Cesium引擎
 * <AUTHOR>
 * @version 2.0.0
 * @date 2024-01-16
 */

import { AppCesium } from '@/lib/cesium/AppCesium';
import { defineStore } from 'pinia';
import { type Ref, ref } from 'vue';
import Coord from '@/lib/cesium/utils/Coord';

/**
 * @description 相机状态接口定义
 */
interface CameraState {
  /** 相机位置 */
  destination: any;
  /** 相机方向 */
  orientation: {
    heading: number;
    pitch: number;
    roll: number;
  };
}

/**
 * @description 二三维切换状态管理
 * @details 只支持Cesium引擎，提供简洁的二维/三维视角切换功能
 */
export const useToggle3D = defineStore('toggle3D', () => {
  /** @description 保存的三维相机状态 */
  const savedCameraState: Ref<CameraState | null> = ref(null);
  
  /** @description 当前是否为三维视角模式 */
  const is3D = ref(true);

  /**
   * @description 执行二三维视角切换
   * @throws {Error} 当Cesium引擎未初始化时抛出错误
   */
  const toggle = (): void => {
    try {
      const viewer = AppCesium.getInstance().getViewer();
      
      if (!viewer?.camera) {
        throw new Error('Cesium引擎未初始化或相机对象无效');
      }

      // 切换状态
      is3D.value = !is3D.value;
      
      if (is3D.value) {
        // 切换到三维模式
        switchTo3D(viewer);
      } else {
        // 切换到二维模式
        switchTo2D(viewer);
      }
      
      console.log(`视角切换完成: ${is3D.value ? '三维' : '二维'}模式`);
    } catch (error) {
      console.error('二三维切换失败:', error);
      // 恢复原状态
      is3D.value = !is3D.value;
      throw error;
    }
  };

  /**
   * @description 切换到二维模式（重构版本）
   * @param {any} viewer - Cesium视图器实例
   */
  const switchTo2D = (viewer: any): void => {
    const camera = viewer.camera;
    
    console.log('=== 开始三维到二维切换 ===');
    
    // 1. 记录切换前的屏幕中心坐标
    const originalCenterCoord = Coord.getScreenCenterCoordinate(camera);
    console.log(`切换前屏幕中心: 经度=${originalCenterCoord.longitude.toFixed(6)}°, 纬度=${originalCenterCoord.latitude.toFixed(6)}°`);
    
    // 2. 保存当前三维相机状态
    savedCameraState.value = {
      destination: camera.position.clone(),
      orientation: {
        heading: camera.heading,
        pitch: camera.pitch,
        roll: camera.roll,
      },
    };

    // 3. 禁用倾斜控制（二维模式特征）
    viewer.scene.screenSpaceCameraController.enableTilt = false;
    
    try {
      // 4. 计算最佳2D位置，确保屏幕中心坐标不变
      const optimal2DParams = Coord.calculateOptimal2DPosition(camera);
      
      // 5. 设置二维视角 - 直接定位到屏幕中心正上方
      camera.setView({
        destination: optimal2DParams.position,
        orientation: {
          heading: 0, // 二维模式使用标准北向
          pitch: BC.Math.toRadians(-90), // 垂直俯视
          roll: 0
        },
        duration: 1.5, // 添加1.5秒平滑过渡动画
        complete: () => {
          // 6. 切换完成后验证屏幕中心坐标一致性
          setTimeout(() => {
            const validation = Coord.validateScreenCenterConsistency(camera, originalCenterCoord, 0.001);
            if (!validation.isConsistent) {
              console.warn('屏幕中心坐标不一致，尝试微调...');
              // 如果不一致，尝试微调位置
              adjustCameraPosition(camera, originalCenterCoord);
            } else {
              console.log('✓ 屏幕中心坐标保持一致');
            }
          }, 100); // 等待动画完成后再验证
        }
      });

      
      console.log(`二维模式激活: 高度=${optimal2DParams.height.toFixed(0)}m, 方法=${optimal2DParams.method}`);
      console.log('=== 三维到二维切换完成 ===');
      
    } catch (error) {
      console.error('2D切换失败，使用降级方案:', error);
      
      // 降级方案：使用简单的高度调整
      const currentHeight = Coord.getHeight(camera.position);
      const optimal2DHeight = Math.max(currentHeight * 1.5, 8000);
      
      const cartesian3 = Coord.setHeight(camera.position.clone(), optimal2DHeight);
      camera.setView({
        destination: cartesian3,
        orientation: {
          heading: 0,
          pitch: BC.Math.toRadians(-90),
          roll: 0
        },
        duration: 1.5
      });

      
      console.log(`二维模式激活(降级): 高度=${optimal2DHeight.toFixed(0)}m`);
    }
  };

  /**
   * @description 微调相机位置以确保屏幕中心坐标一致
   * @param {any} camera - 相机对象
   * @param {any} targetCoord - 目标中心坐标
   */
  const adjustCameraPosition = (camera: any, targetCoord: any): void => {
    try {
      const currentHeight = Coord.getHeight(camera.position);
      
      // 创建目标位置（在目标坐标正上方）
      const targetCartographic = new BC.Namespace.Cesium.Cartographic(
        BC.Namespace.Cesium.Math.toRadians(targetCoord.longitude),
        BC.Namespace.Cesium.Math.toRadians(targetCoord.latitude),
        currentHeight
      );
      const targetPosition = BC.Namespace.Cesium.Cartographic.toCartesian(targetCartographic);
      
      // 平滑调整到目标位置
      camera.setView({
        destination: targetPosition,
        orientation: {
          heading: 0,
          pitch: BC.Namespace.Cesium.Math.toRadians(-90),
          roll: 0
        },
        duration: 0.5 // 快速微调
      });
      
      console.log(`相机位置已微调到目标坐标: 经度=${targetCoord.longitude.toFixed(6)}°, 纬度=${targetCoord.latitude.toFixed(6)}°`);
      
    } catch (error) {
      console.error('微调相机位置失败:', error);
    }
  };

  /**
   * @description 切换到三维模式（重构版本）
   * @param {any} viewer - Cesium视图器实例
   */
  const switchTo3D = (viewer: any): void => {
    console.log('=== 开始二维到三维切换 ===');
    
    // 1. 启用倾斜控制（三维模式特征）
    viewer.scene.screenSpaceCameraController.enableTilt = true;
    
    if (savedCameraState.value) {
      // 2. 恢复保存的三维相机状态，添加平滑过渡
      try {
        const restoreState = {
          ...savedCameraState.value,
          duration: 1.5 // 添加1.5秒平滑过渡动画
        };
        viewer.camera.setView(restoreState);
        console.log('三维视角恢复成功，带平滑过渡');
        console.log('=== 二维到三维切换完成 ===');
      } catch (error) {
        console.warn('三维视角恢复失败，使用默认设置:', error);
        setDefault3DView(viewer);
      }
    } else {
      // 3. 设置默认三维视角
      console.log('无保存的三维状态，设置默认三维视角');
      setDefault3DView(viewer);
    }

  };

  /**
   * @description 设置默认的三维视角（重构版本）
   * @param {any} viewer - Cesium视图器实例
   */
  const setDefault3DView = (viewer: any): void => {
    const camera = viewer.camera;
    
    try {
      // 获取当前屏幕中心坐标，确保三维视角也以此为中心
      const centerCoord = Coord.getScreenCenterCoordinate(camera);
      const currentHeight = Coord.getHeight(camera.position);
      
      // 设置合适的三维视角高度
      const defaultHeight = Math.max(currentHeight * 0.6, 5000);
      
      // 创建以屏幕中心为基准的三维位置
      const defaultCartographic = new BC.Namespace.Cesium.Cartographic(
        BC.Namespace.Cesium.Math.toRadians(centerCoord.longitude),
        BC.Namespace.Cesium.Math.toRadians(centerCoord.latitude),
        defaultHeight
      );
      const defaultPosition = BC.Namespace.Cesium.Cartographic.toCartesian(defaultCartographic);
      
      camera.setView({
        destination: defaultPosition,
        orientation: {
          heading: 0, // 标准北向
          pitch: BC.Math.toRadians(-45), // 45度俯视角
          roll: 0
        },
        duration: 1.5 // 添加1.5秒平滑过渡动画
      });
      
      console.log(`设置默认三维视角: 中心(${centerCoord.longitude.toFixed(6)}°, ${centerCoord.latitude.toFixed(6)}°), 高度=${defaultHeight.toFixed(0)}m`);
      console.log('=== 二维到三维切换完成 ===');
      
    } catch (error) {
      console.error('设置默认三维视角失败:', error);
      
      // 降级方案：使用当前位置
      const currentHeight = Coord.getHeight(camera.position);
      const defaultHeight = Math.max(currentHeight * 0.6, 5000);
      const defaultPosition = Coord.setHeight(camera.position.clone(), defaultHeight);
      
      camera.setView({
        destination: defaultPosition,
        orientation: {
          heading: 0,
          pitch: BC.Math.toRadians(-45),
          roll: 0
        },
        duration: 1.5
      });
      
      console.log(`设置默认三维视角(降级): 高度=${defaultHeight.toFixed(0)}m`);
    }
  };

  /**
   * @description 重置切换状态
   * @details 清除保存的相机状态，重置为三维模式
   */
  const reset = (): void => {
    savedCameraState.value = null;
    is3D.value = true;
    console.log('二三维切换状态已重置');
  };

  /**
   * @description 获取当前模式描述
   * @returns {string} 当前模式的中文描述
   */
  const getModeDescription = (): string => {
    return is3D.value ? '三维模式' : '二维模式';
  };

  return {
    /** 当前是否为三维模式 */
    is3D: readonly(is3D),
    /** 执行二三维切换 */
    toggle,
    /** 重置状态 */
    reset,
    /** 获取模式描述 */
    getModeDescription
  };
});