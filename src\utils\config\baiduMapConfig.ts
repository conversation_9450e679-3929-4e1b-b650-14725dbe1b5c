/**
 * @fileoverview 百度地图API配置文件
 * @description 管理百度地图Place API的配置信息
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * @description 百度地图API配置接口
 */
export interface BaiduMapConfig {
  /** 百度地图API密钥 */
  ak: string;
  /** 搜索区域限制 */
  region: string;
  /** 搜索范围半径（米） */
  radius: number;
  /** 搜索中心点坐标 */
  center: {
    lng: number;
    lat: number;
  };
  /** API基础URL */
  baseUrl: string;
  /** 是否启用HTTPS */
  secure: boolean;
}

/**
 * @description 沙湾区百度地图API配置
 * @details 配置了沙湾区的地理位置和搜索范围
 */
export const BAIDU_MAP_CONFIG: BaiduMapConfig = {
  // TODO: 请替换为实际的百度地图API密钥
  // 申请地址：https://lbsyun.baidu.com/apiconsole/key
  ak: import.meta.env.VITE_BAIDU_MAP_AK || 'your_baidu_api_key_here',
  
  // 搜索区域限制为沙湾区
  region: '四川省乐山市沙湾区',
  
  // 搜索范围限制（以沙湾区中心为圆心，50公里半径）
  radius: 50000,
  
  // 沙湾区中心坐标（大致位置）
  center: {
    lng: 103.551,  // 经度
    lat: 29.413    // 纬度
  },
  
  // 百度地图Place API基础URL
  baseUrl: 'https://api.map.baidu.com',
  
  // 启用HTTPS
  secure: true
};

/**
 * @description 搜索API端点配置
 */
export const SEARCH_ENDPOINTS = {
  /** 地点搜索API */
  PLACE_SEARCH: '/place/v2/search',
  /** 地点建议API */
  PLACE_SUGGESTION: '/place/v2/suggestion',
  /** 地点详情API */
  PLACE_DETAIL: '/place/v2/detail',
  /** 地理编码API */
  GEOCODING: '/geocoding/v3',
  /** 逆地理编码API */
  REVERSE_GEOCODING: '/reverse_geocoding/v3'
};

/**
 * @description 搜索配置选项
 */
export const SEARCH_OPTIONS = {
  /** 每页返回结果数量 */
  PAGE_SIZE: {
    SUGGESTION: 8,   // 搜索建议
    SEARCH: 20       // 正常搜索
  },
  
  /** 搜索范围 */
  SCOPE: {
    BASIC: '1',      // 基本信息
    DETAIL: '2'      // 详细信息
  },
  
  /** 输出格式 */
  OUTPUT: 'json',
  
  /** 坐标系类型 */
  COORD_TYPE: 'bd09ll',  // 百度经纬度坐标系
  
  /** 搜索半径（米） */
  RADIUS: 5000
};

/**
 * @description POI分类配置
 * @details 定义不同类型地点的图标和标签
 */
export const POI_CATEGORIES = {
  // 政府机构
  government: {
    name: '政府机构',
    icon: '🏛️',
    tags: ['政府', '行政', '服务中心', '办事处']
  },
  
  // 医疗机构
  medical: {
    name: '医疗机构',
    icon: '🏥',
    tags: ['医院', '诊所', '卫生院', '药店']
  },
  
  // 教育机构
  education: {
    name: '教育机构',
    icon: '🏫',
    tags: ['学校', '幼儿园', '培训', '图书馆']
  },
  
  // 交通设施
  transportation: {
    name: '交通设施',
    icon: '🚌',
    tags: ['车站', '桥梁', '停车场', '加油站']
  },
  
  // 商业场所
  commercial: {
    name: '商业场所',
    icon: '🏪',
    tags: ['商场', '超市', '市场', '银行', '餐厅']
  },
  
  // 旅游景点
  tourism: {
    name: '旅游景点',
    icon: '🎯',
    tags: ['景点', '公园', '古镇', '广场']
  },
  
  // 工业园区
  industrial: {
    name: '工业园区',
    icon: '🏭',
    tags: ['工业园', '厂区', '开发区']
  },
  
  // 乡镇街道
  administrative: {
    name: '乡镇街道',
    icon: '🏘️',
    tags: ['乡镇', '街道', '村委会']
  }
};

/**
 * @description 沙湾区热门搜索关键词
 */
export const HOT_KEYWORDS = [
  '沙湾区政府',
  '沙湾区人民医院',
  '沙湾古镇',
  '沙湾大桥',
  '沙湾客运站',
  '沙湾中学',
  '太平镇',
  '福禄镇',
  '嘉农镇',
  '轸溪乡',
  '沙湾广场',
  '沙湾公园',
  '大渡河',
  '沙湾工业园区'
];

/**
 * @description 验证API配置
 * @returns 配置验证结果
 */
export const validateConfig = (): { isValid: boolean; message: string } => {
  if (!BAIDU_MAP_CONFIG.ak || BAIDU_MAP_CONFIG.ak === 'your_baidu_api_key_here') {
    return {
      isValid: false,
      message: '请配置有效的百度地图API密钥。请在.env文件中设置VITE_BAIDU_MAP_AK，或在baiduMapConfig.ts中直接配置。'
    };
  }
  
  if (!BAIDU_MAP_CONFIG.region) {
    return {
      isValid: false,
      message: '请配置搜索区域'
    };
  }
  
  return {
    isValid: true,
    message: '配置验证通过'
  };
};

/**
 * @description 获取完整的API URL
 * @param endpoint API端点
 * @returns 完整的API URL
 */
export const getApiUrl = (endpoint: string): string => {
  const protocol = BAIDU_MAP_CONFIG.secure ? 'https:' : 'http:';
  return `${protocol}//${BAIDU_MAP_CONFIG.baseUrl.replace(/^https?:\/\//, '')}${endpoint}`;
};

/**
 * @description 构建搜索请求参数
 * @param keyword 搜索关键词
 * @param type 搜索类型
 * @param options 额外选项
 * @returns URLSearchParams对象
 */
export const buildSearchParams = (
  keyword: string,
  type: 'suggestion' | 'search',
  options: {
    pageNum?: number;
    location?: { lng: number; lat: number };
  } = {}
): URLSearchParams => {
  const params = new URLSearchParams({
    query: keyword,
    region: BAIDU_MAP_CONFIG.region,
    output: SEARCH_OPTIONS.OUTPUT,
    ak: BAIDU_MAP_CONFIG.ak,
    scope: SEARCH_OPTIONS.SCOPE.DETAIL,
    page_size: String(SEARCH_OPTIONS.PAGE_SIZE[type.toUpperCase() as keyof typeof SEARCH_OPTIONS.PAGE_SIZE]),
    page_num: String(options.pageNum || 0)
  });
  
  // 如果提供了位置信息，添加location参数
  if (options.location) {
    params.append('location', `${options.location.lat},${options.location.lng}`);
    params.append('radius', String(SEARCH_OPTIONS.RADIUS));
  }
  
  return params;
}; 