<!--
 * @Description: 鼠标坐标显示组件
 * @Date: 2023-08-15 10:00:00
 * @Author: 项目开发团队
 * @LastEditTime: 2023-08-15 10:00:00
 * @Version: 1.1.0
-->
<template>
  <div
    class="mouse-coordinate-container"
    v-show="visible"

  >
    <div
      class="coordinate-display"
      :class="{ 'no-coordinate': !hasCoordinate }"
    >
      <template v-if="hasCoordinate">
        <div class="coordinate-item">
          <span class="coordinate-label">WGS84：</span>
          <span class="coordinate-value">{{
            formatCoord(wgs84, precision)
          }}</span>
        </div>
      </template>

      <template v-else>
        <div class="no-coordinate-text">鼠标移出地图区域</div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { CoordinatePoint } from "@/utils/coordinate/CoordinateTransform";
import { useMouseCoordinate } from "./useMouseCoordinate";
/**
 * 组件属性定义
 */
const props = defineProps({
  /** 地图类型 */
  mapType: {
    type: String as () => "maplibre" | "cesium",
    required: true,
  },
  /** 地图实例 */
  mapInstance: {
    type: Object,
    required: true,
  },
  /** 是否显示组件 */
  visible: {
    type: Boolean,
    default: true,
  },
  /** 坐标精度（小数位数） */
  precision: {
    type: Number,
    default: 6,
  },
});

/**
 * 初始化鼠标坐标追踪逻辑
 */
const { wgs84, hasCoordinate, startTracking, stopTracking } =
  useMouseCoordinate({
    mapType: props.mapType,
    mapInstance: props.mapInstance,
    precision: props.precision,
  });

/**
 * 格式化坐标显示
 * @param coord - 坐标对象
 * @param precision - 精度（小数位数）
 * @returns 格式化后的坐标字符串
 */
const formatCoord = (coord: CoordinatePoint, precision: number): string => {
  if (
    !coord ||
    typeof coord.lng !== "number" ||
    typeof coord.lat !== "number"
  ) {
    return "暂无数据";
  }

  return `${coord.lng.toFixed(precision)}, ${coord.lat.toFixed(precision)}`;
};
const route = useRoute();
console.log(route.name);
/**
 * 暴露组件方法
 */
defineExpose({
  startTracking,
  stopTracking,
});
</script>

<style lang="scss" scoped>
.mouse-coordinate-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 278px;
  z-index: 10;
  pointer-events: none; // 允许点击穿透
}
.lft {
  left: 42%;
  // bottom: 16px;
}
.coordinate-display {
  height: 30px;
  background-color: rgba(255, 255, 255, 0.7);
  color: #616161;
  // padding: 5px 10px;
  border-radius: 0;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;

  &.no-coordinate {
    justify-content: center;
  }
}

.coordinate-item {
  display: flex;
  align-items: center;
}

.coordinate-label {
  // font-weight: bold;
  margin-right: 5px;
  color: #616161;
}

.coordinate-value {
  font-family: 微软雅黑;
  // font-family: "Courier New", monospace;
}

.no-coordinate-text {
  color: #ccc;
  font-style: italic;
}
</style>
