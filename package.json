{"name": "swq-pipe-master", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "build-only": "vite build --mode production"}, "dependencies": {"@latias/vue": "^1.0.0-alpha.24", "@turf/turf": "^7.2.0", "@types/three": "^0.178.1", "@vueuse/core": "^13.2.0", "axios": "^1.9.0", "bcgis-sdk": "1.1.45", "canvas-record": "^5.2.1", "crypto-js": "^4.1.1", "d3": "^7.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.11", "maplibre-gl": "^5.5.0", "marked": "^16.1.2", "ol": "^10.6.1", "pinia": "^3.0.2", "proj4": "^2.19.7", "qs": "^6.11.1", "rxjs": "^7.8.2", "sass": "^1.89.0", "terra-draw": "^1.6.2", "terra-draw-maplibre-gl-adapter": "^1.0.3", "three": "^0.178.0", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.5", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/ep": "^1.2.2", "@unocss/preset-rem-to-px": "^66.1.2", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "fast-glob": "^3.3.3", "typescript": "~5.8.3", "unocss": "^66.1.3", "unplugin-auto-import": "^19.2.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.5", "vite-plugin-static-copy": "^3.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vconsole": "^2.1.1", "vue-tsc": "^2.2.8"}}