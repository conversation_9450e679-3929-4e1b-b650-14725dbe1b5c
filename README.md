# MapLibre 图层管理系统

基于 MapLibre GL JS 的强大图层管理系统，支持多种地图服务、坐标系转换和高级图层操作。

## 🚀 特性

- **多地图服务支持**: 天地图、百度地图、高德地图、自定义XYZ、MVT等
- **坐标系转换**: 支持WGS84、GCJ02、BD09、Web墨卡托等坐标系
- **图层组管理**: 支持嵌套图层结构和批量操作
- **事件系统**: 完整的图层事件监听和处理
- **性能监控**: 内置性能指标监控和优化
- **TypeScript支持**: 完整的类型定义和智能提示
- **异步加载**: 支持并发图层加载和错误处理

## 📦 安装

```bash
npm install maplibre-gl
```

## 🎯 快速开始

### 基础用法

```typescript
import { Map } from 'maplibre-gl'
import { 
  createLayerManager, 
  LayerType, 
  CoordinateSystem 
} from '@/lib/maplibre/layer'

// 创建地图实例
const map = new Map({
  container: 'map',
  style: 'https://demotiles.maplibre.org/style.json',
  center: [116.404, 39.915],
  zoom: 10
})

// 创建图层管理器
const layerManager = createLayerManager({
  map,
  enablePerformanceMonitoring: true,
  debug: true,
  maxConcurrentLoads: 5
})

// 添加百度地图图层
await layerManager.addLayers([
  {
    id: 'baidu-normal',
    title: '百度地图',
    type: LayerType.BAIDU,
    style: 'normal',
    showLabel: true,
    crs: CoordinateSystem.BD09,
    show: true
  }
])
```

### 图层组管理

```typescript
// 创建图层组
const layerGroupConfig = {
  id: 'base-maps',
  title: '底图组',
  type: LayerType.GROUP,
  expanded: true,
  children: [
    {
      id: 'amap-satellite',
      title: '高德卫星图',
      type: LayerType.AMAP,
      style: 'satellite',
      lang: 'zh_cn',
      scale: 1,
      crs: CoordinateSystem.GCJ02
    },
    {
      id: 'tdt-vector',
      title: '天地图矢量',
      type: LayerType.TDT,
      layer: 'vec_w',
      crs: '3857',
      token: 'your-tianditu-token'
    }
  ]
}

await layerManager.addLayers([layerGroupConfig])
```

## 📚 API 文档

### LayerManager

图层管理器是整个系统的核心，负责图层的创建、管理和事件处理。

#### 构造函数

```typescript
interface LayerManagerConfig {
  map: Map                              // MapLibre地图实例
  enablePerformanceMonitoring?: boolean // 是否启用性能监控
  debug?: boolean                       // 是否启用调试模式
  defaultLoadOptions?: LayerLoadOptions // 默认加载选项
  maxConcurrentLoads?: number          // 最大并发加载数
}

const layerManager = new LayerManager(config)
```

#### 主要方法

```typescript
// 批量添加图层
await layerManager.addLayers(configs: LayerConfig[]): Promise<void>

// 添加单个图层
await layerManager.addLayer(
  target: string, 
  config: LayerConfig, 
  index?: number
): Promise<BaseLayer | null>

// 查找图层
layerManager.findLayerById(layerId: string): BaseLayer | null
layerManager.findLayers(predicate: (layer: BaseLayer) => boolean): BaseLayer[]

// 设置图层可见性
layerManager.setLayerVisible(layerId: string, visible: boolean): void
layerManager.setLayersVisible(layerIds: string[], visible: boolean): void

// 移除图层
layerManager.removeLayer(layerId: string): void

// 获取图层树
layerManager.getLayerTree(): LayerItem[]

// 性能监控
layerManager.getPerformanceMetrics(): Map<string, LayerPerformanceMetrics>
layerManager.getLayerPerformanceMetrics(layerId: string): LayerPerformanceMetrics | undefined

// 清理资源
layerManager.clear(): void
layerManager.destroy(): void
```

#### 事件监听

```typescript
// 监听图层变化
layerManager.layerChanged.subscribe(layerTree => {
  console.log('图层树更新:', layerTree)
})

// 监听图层事件
layerManager.layerEvents.subscribe(event => {
  console.log('图层事件:', event.type, event.data)
})

// 监听性能更新
layerManager.performanceUpdated.subscribe(({ layerId, metrics }) => {
  console.log(`图层 ${layerId} 性能指标:`, metrics)
})
```

### 图层类型

#### 百度地图图层 (BaiduMapLayer)

```typescript
const baiduConfig: BaiduLayerConfig = {
  id: 'baidu-map',
  title: '百度地图',
  type: LayerType.BAIDU,
  style: 'normal' | 'satellite' | 'hybrid' | 'dark' | 'light',
  showLabel: true,
  crs: CoordinateSystem.BD09,
  subdomains: ['0', '1', '2', '3']
}

// 动态切换样式
const baiduLayer = layerManager.findLayerById('baidu-map') as BaiduMapLayer
baiduLayer.setStyle('satellite')
baiduLayer.setShowLabel(false)
```

#### 高德地图图层 (AmapLayer)

```typescript
const amapConfig: AmapLayerConfig = {
  id: 'amap-layer',
  title: '高德地图',
  type: LayerType.AMAP,
  style: 'normal' | 'satellite' | 'hybrid' | 'dark' | 'light' | 'whitesmoke',
  showLabel: true,
  showRoad: true,
  lang: 'zh_cn' | 'zh_en' | 'en',
  scale: 1 | 2,
  crs: CoordinateSystem.GCJ02
}

// 动态配置
const amapLayer = layerManager.findLayerById('amap-layer') as AmapLayer
amapLayer.setStyle('hybrid')
amapLayer.setLanguage('en')
amapLayer.setScale(2)
```

#### 天地图图层 (TdtImageryLayer)

```typescript
const tdtConfig: TdtLayerConfig = {
  id: 'tdt-layer',
  title: '天地图',
  type: LayerType.TDT,
  layer: 'vec_w' | 'vec_z' | 'img_w' | 'cva_w' | 'ter_d' | 'ter_z',
  crs: '3857' | '4326',
  token: 'your-tianditu-token'
}
```

#### 自定义XYZ图层 (CustomXYZLayer)

```typescript
const xyzConfig: CustomXYZLayerConfig = {
  id: 'custom-xyz',
  title: '自定义瓦片',
  type: LayerType.CUSTOM_XYZ,
  url: 'https://example.com/{z}/{x}/{y}.png',
  subdomains: ['a', 'b', 'c'],
  tileSize: 256,
  minZoom: 0,
  maxZoom: 18
}
```

#### MVT矢量瓦片图层 (MvtLayer)

```typescript
const mvtConfig: MvtLayerConfig = {
  id: 'mvt-layer',
  title: 'MVT图层',
  type: LayerType.MVT,
  url: 'https://example.com/tiles/{z}/{x}/{y}.mvt',
  sourceLayer: 'layer-name',
  paint: {
    'fill-color': '#ff0000',
    'fill-opacity': 0.8
  },
  layout: {
    'visibility': 'visible'
  }
}
```

### 坐标转换

```typescript
import { CoordinateTransform } from '@/utils/coordinate/CoordinateTransform'

// WGS84 转 GCJ02
const gcj02Point = CoordinateTransform.wgs84ToGcj02({
  lng: 116.404,
  lat: 39.915
})

// GCJ02 转 BD09
const bd09Point = CoordinateTransform.gcj02ToBd09({
  lng: 116.404,
  lat: 39.915
})

// 批量转换
const points = [
  { lng: 116.404, lat: 39.915 },
  { lng: 121.473, lat: 31.230 }
]
const convertedPoints = points.map(point => 
  CoordinateTransform.wgs84ToGcj02(point)
)
```

## 🔧 高级用法

### 自定义图层

```typescript
import { BaseLayer } from '@/lib/maplibre/layer'

class CustomLayer extends BaseLayer {
  async addToInternal(map: glMap, isRefresh?: boolean): Promise<any> {
    // 自定义图层实现
    map.addSource(this.id, {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: []
      }
    })

    map.addLayer({
      id: this.id,
      type: 'fill',
      source: this.id,
      paint: {
        'fill-color': '#ff0000',
        'fill-opacity': 0.5
      }
    })

    return map.getLayer(this.id)
  }

  removeInternal(): void {
    if (this._map) {
      if (this._map.getLayer(this.id)) {
        this._map.removeLayer(this.id)
      }
      if (this._map.getSource(this.id)) {
        this._map.removeSource(this.id)
      }
    }
  }
}

// 注册自定义图层到工厂
LayerFactory.registerLayerType('custom', CustomLayer)
```

### 性能优化

```typescript
// 启用性能监控
const layerManager = createLayerManager({
  map,
  enablePerformanceMonitoring: true,
  maxConcurrentLoads: 3 // 限制并发加载数
})

// 监控性能指标
layerManager.performanceUpdated.subscribe(({ layerId, metrics }) => {
  if (metrics.loadTime > 5000) {
    console.warn(`图层 ${layerId} 加载时间过长: ${metrics.loadTime}ms`)
  }
  
  if (metrics.tileSuccessRate < 0.9) {
    console.warn(`图层 ${layerId} 瓦片加载成功率低: ${metrics.tileSuccessRate}`)
  }
})

// 批量操作优化
const configs = [/* 大量图层配置 */]
await layerManager.addLayers(configs, {
  async: true,        // 异步并行加载
  timeout: 10000,     // 10秒超时
  retryCount: 3,      // 重试3次
  priority: 'high'    // 高优先级
})
```

### 错误处理

```typescript
// 全局错误监听
layerManager.on('error', (event) => {
  console.error('图层错误:', event.data)
  
  // 根据错误类型进行处理
  if (event.data.code === 'NETWORK_ERROR') {
    // 网络错误，尝试重新加载
    layerManager.refreshLayer(event.layerId)
  }
})

// 图层验证
try {
  const validation = validateLayerConfig(config)
  if (!validation.valid) {
    console.error('配置验证失败:', validation.errors)
    return
  }
  
  if (validation.warnings.length > 0) {
    console.warn('配置警告:', validation.warnings)
  }
  
  const layer = createLayer(config)
} catch (error) {
  console.error('创建图层失败:', error)
}
```

## 🎨 样式定制

### 图层样式配置

```typescript
// MVT图层样式
const mvtLayer = {
  type: LayerType.MVT,
  url: 'https://example.com/tiles/{z}/{x}/{y}.mvt',
  paint: {
    'fill-color': [
      'case',
      ['==', ['get', 'type'], 'water'],
      '#0066cc',
      ['==', ['get', 'type'], 'park'],
      '#00cc66',
      '#cccccc'
    ],
    'fill-opacity': 0.8
  },
  layout: {
    'visibility': 'visible'
  },
  filter: ['>', ['get', 'area'], 1000]
}
```

### 主题切换

```typescript
class ThemeManager {
  private layerManager: LayerManager
  
  constructor(layerManager: LayerManager) {
    this.layerManager = layerManager
  }
  
  switchToDarkTheme(): void {
    // 切换百度地图到暗色主题
    const baiduLayers = this.layerManager.findLayers(
      layer => layer instanceof BaiduMapLayer
    ) as BaiduMapLayer[]
    
    baiduLayers.forEach(layer => {
      layer.setStyle('dark')
    })
    
    // 切换高德地图到暗色主题
    const amapLayers = this.layerManager.findLayers(
      layer => layer instanceof AmapLayer
    ) as AmapLayer[]
    
    amapLayers.forEach(layer => {
      layer.setStyle('dark')
    })
  }
}
```

## 🔍 调试和开发

### 调试模式

```typescript
const layerManager = createLayerManager({
  map,
  debug: true // 启用调试模式
})

// 调试信息将输出到控制台
// [LayerManager] 图层事件: loaded { layerId: 'baidu-map', ... }
// [LayerManager] 性能指标: { loadTime: 1234, tileSuccessRate: 0.95 }
```

### 开发工具

```typescript
// 获取图层信息
const layerInfo = layer.getLayerInfo()
console.log('图层信息:', layerInfo)

// 获取图层树结构
const layerTree = layerManager.getLayerTree()
console.log('图层树:', JSON.stringify(layerTree, null, 2))

// 性能分析
const metrics = layerManager.getPerformanceMetrics()
metrics.forEach((metric, layerId) => {
  console.log(`${layerId}:`, metric)
})
```

## 📝 更新日志

### v2.0.0
- 🎉 重构整个图层系统架构
- ✨ 新增百度地图和高德地图支持
- ✨ 完整的TypeScript类型定义
- ✨ 事件系统和性能监控
- ✨ 异步加载和错误处理
- 🐛 修复图层组管理问题
- 📚 完善文档和示例

### v1.x.x
- 基础图层管理功能
- 天地图和自定义XYZ图层支持

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📝 许可证

MIT License

# SWQ地图管线系统

这是一个基于Vue 3的地理信息系统，集成了Cesium三维地图和MapLibre二维地图功能。

## 🚀 主要功能

### 二三维联动（双Cesium版）
访问路径：`/dual-map`

**核心特性：**
- **1:1分屏显示**：左侧Cesium三维视图，右侧Cesium二维视图，固定50%:50%分屏比例
- **精确双向同步**：
  - 使用相同引擎（Cesium）实现完美同步
  - 三维视图操作实时同步到二维视图
  - 二维视图操作实时同步到三维视图
  - 200ms防抖延迟，避免过于频繁的同步
- **原生坐标系统**：
  - 无需复杂的坐标转换
  - 相同的相机参数体系
  - 完美的位置和方向同步
- **响应式设计**：移动端自动切换为上下分屏

**技术优势：**
- 相同引擎确保100%同步精度
- 右侧使用Cesium 2D模式（禁用倾斜和3D效果）
- 直接共享相机参数，无需映射转换
- 专注于最核心的联动同步功能
- 性能优化，减少不必要的UI组件

### 其他功能模块
- MapLibre二维地图模块：`/maplibre`
- Cesium三维地图模块：`/cesium`

## 🛠️ 技术栈

- **前端框架**：Vue 3 + TypeScript
- **地图引擎**：
  - 统一使用：Cesium/BC (BoxesCesium)
  - 三维模式：完整的3D功能
  - 二维模式：Cesium 2D模式（SCENE2D）
- **UI框架**：Element Plus
- **构建工具**：Vite
- **状态管理**：Pinia

## 📦 开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 🎯 使用说明

### 二三维联动使用步骤：

1. 访问 `http://localhost:5173/dual-map`
2. 等待地图初始化完成
3. 在任一视图中进行操作（平移、缩放、旋转）
4. 观察另一视图的自动同步效果

### 技术特点：

- **无缝同步**：视角变化实时反映在两个视图中
- **性能优化**：防抖机制避免频繁同步，提升性能
- **容错处理**：同步过程中的错误会自动恢复，不影响用户体验
- **简洁界面**：专注核心功能，减少干扰元素

## 📝 版本历史

### v3.0.0 (当前版本)
- 🔄 重构为双Cesium架构，实现完美同步
- ✅ 使用相同引擎避免坐标转换误差
- 🎯 右侧采用Cesium 2D模式替代MapLibre
- ⚡ 原生相机参数共享，性能更优
- 🔧 简化同步算法，稳定性大幅提升

### v2.0.0
- 🎯 精简二三维联动功能，专注核心体验
- ✂️ 移除复杂的UI控制面板
- ⚡ 性能优化，减少组件开销
- 📱 改进响应式支持

### v1.0.0
- 🚀 完整的二三维联动功能
- ��️ 包含分屏控制、同步设置等高级功能
