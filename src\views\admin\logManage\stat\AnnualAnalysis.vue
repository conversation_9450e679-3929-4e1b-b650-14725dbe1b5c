<template>
  <div>
    <div class="flex justify-between">
      <div>年度总访客统计</div>
      <div>
        <el-select
          v-model="sevenDays"
          class="w-25 drawer-select"
          popper-class="drawer-popper-select"
          placeholder="请选择"
          size="small"
          @change="sedayChange"
        >
          <el-option
            v-for="item in yearData"
            :key="ite"
            :label="item"
            :value="item"
          />
        </el-select>
      </div>
    </div>
    <div class="chart" ref="categoryRef"></div>
  </div>
</template>
<script lang="ts" setup>
import { initAnnualAlsEchart } from "@/lib/echarts";
import { visitorYear } from "@/api/log";
const { initChart } = useEchart();
const yearData = ref(["2025"]);
const categoryRef = ref();
const now = new Date();
const sevenDays = ref(now.getFullYear());
const date = ref<any>([]);
const twoGis = ref<any>([]);
const threeGis = ref<any>([]);
const gisService = ref<any>([]);
const backgroundManage = ref<any>([]);
const getChart = async () => {
  date.value = [];
  twoGis.value = [];
  threeGis.value = [];
  gisService.value = [];
  backgroundManage.value = [];
  const result = await visitorYear({ year: sevenDays.value });
  if (result.code === 200) {
    result.data.forEach((item: any) => {
      date.value.push(item.date + "月");
      twoGis.value.push(item.twoGis);
      threeGis.value.push(item.threeGis);
      gisService.value.push(item.gisService);
      backgroundManage.value.push(item.backgroundManage);
    });
  }
  categoryRef.value &&
    initChart(
      categoryRef.value,
      initAnnualAlsEchart(
        date.value,
        twoGis.value,
        threeGis.value,
        gisService.value,
        backgroundManage.value
      )
    );
};
const sedayChange = () => {
  getChart();
};
onMounted(() => {
  getChart();
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  // height: 240px;
  height: calc(100vh - 784px);
}
</style>
