<!--
 * @Description: 缓冲区分析绘制按钮组件
 * @Date: 2025-01-10
 * @Author: 项目开发团队
 * @LastEditTime: 2025-01-10
-->
<template>
  <div class="buffer-analysis-draw-buttons">
    <!-- 所有按钮 - flex 布局 -->
    <div class="button-container">
      <!-- 绘制按钮 -->
      <el-button
        v-for="button in enabledButtons"
        :key="button.type"
        :type="button.elType"
        :size="config.buttonSize"
        :disabled="button.disabled || isLoading"
        :loading="currentOperation === button.type"
        @click="handleDraw(button.type)"
        class="primary-btn"
        :class="button.customClass"
      >
        <i v-if="button.icon" :class="button.icon" class="mr-1"></i>
        {{ button.label }}
      </el-button>
      
      <!-- 清空按钮 -->
      <el-button
        class="clear-btn"
        :size="config.buttonSize"
        @click="clearAll"
        plain
      >
        <!-- <i class="el-icon-delete mr-1"></i> -->
        清空绘制
      </el-button>
    </div>

    <!-- 操作提示 -->
    <div v-if="showTips && currentTip" class="operation-tip">
      <el-alert
        :title="currentTip"
        type="info"
        :closable="false"
        :show-icon="true"
        effect="light"
      />
    </div>

    
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import {
  ElButton,
  ElAlert,
} from "element-plus";
import { AppCesium } from "@/lib/cesium/AppCesium";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import {
  DrawEventTypeEnum,
  type DrawMode,
  type DrawManager,
  type DrawEventData,
} from "@/lib/maplibre/draw";

/**
 * @description 绘制类型枚举
 */
export type DrawType = "point" | "linestring" | "polygon" | "rectangle";

/**
 * @description 地图引擎类型
 */
export type MapEngineType = "cesium" | "maplibre";

/**
 * @description 按钮配置接口
 */
export interface ButtonConfig {
  type: DrawType;
  label: string;
  icon?: string;
  elType?: "primary" | "success" | "warning" | "danger" | "info" | "";
  disabled?: boolean;
  customClass?: string;
}

/**
 * @description 组件配置接口
 */
export interface BufferDrawConfig {
  // 按钮样式配置
  buttonSize?: "large" | "default" | "small";
  buttonLayout?: "horizontal" | "vertical";
  buttonSpacing?: number;
  // 功能配置
  enabledDrawTypes?: DrawType[];
  showTips?: boolean;
  showResult?: boolean;
  showResultDetails?: boolean;
  // 绘制配置
  clearPreviousDrawing?: boolean;
  autoSwitchToSelect?: boolean;
}

/**
 * @description 绘制结果接口
 */
export interface DrawResult {
  type: DrawType;
  geometry: any;
  properties?: Record<string, any>;
  timestamp: number;
  success: boolean;
  error?: string;
}

/**
 * @description 组件属性
 */
interface Props {
  /** 地图引擎类型 */
  mapEngine: MapEngineType;
  /** 组件配置 */
  config?: Partial<BufferDrawConfig>;
  /** 自定义按钮配置 */
  customButtons?: Partial<ButtonConfig>[];
}

/**
 * @description 组件事件
 */
interface Emits {
  /** 绘制开始事件 */
  (e: "draw-start", type: DrawType): void;
  /** 绘制完成事件 */
  (e: "draw-complete", result: DrawResult): void;
  /** 绘制错误事件 */
  (e: "draw-error", error: { type: DrawType; message: string }): void;
  /** 清空事件 */
  (e: "clear-all"): void;
}

const props = withDefaults(defineProps<Props>(), {
  mapEngine: "maplibre",
  config: () => ({}),
  customButtons: () => [],
});

const emit = defineEmits<Emits>();

// 组件状态
const isLoading = ref(false);
const currentOperation = ref<DrawType | null>(null);
const currentTip = ref("");
const resultGeometry = ref<any>(null);
const showDetails = ref(false);
const hasDrawnGeometry = ref(false);

// 绘制工具相关
let drawTool: DrawManager | null = null;
let cesiumLayer: any = null;

/**
 * @description 默认配置
 */
const defaultConfig: Required<BufferDrawConfig> = {
  buttonSize: "default",
  buttonLayout: "horizontal",
  buttonSpacing: 8,
  enabledDrawTypes: ["point", "linestring", "polygon", "rectangle"],
  showTips: true,
  showResult: true,
  showResultDetails: true,
  clearPreviousDrawing: true,
  autoSwitchToSelect: true,
};

/**
 * @description 合并后的配置
 */
const config = computed(() => ({
  ...defaultConfig,
  ...props.config,
}));

/**
 * @description 计算属性 - 显示提示
 */
const showTips = computed(() => config.value.showTips);

/**
 * @description 计算属性 - 显示结果
 */
const showResult = computed(() => config.value.showResult);

/**
 * @description 默认按钮配置
 */
const defaultButtons: ButtonConfig[] = [
  {
    type: "point",
    label: "绘制点",
    icon: "el-icon-location",
    elType: "primary",
  },
  {
    type: "linestring",
    label: "绘制线",
    icon: "el-icon-connection",
    elType: "primary",
  },
  {
    type: "polygon",
    label: "绘制多边形",
    icon: "el-icon-edit",
    elType: "primary",
  },
  {
    type: "rectangle",
    label: "绘制矩形",
    icon: "el-icon-crop",
    elType: "primary",
  },
];

/**
 * @description 计算属性 - 启用的按钮
 */
const enabledButtons = computed(() => {
  return defaultButtons
    .filter((btn) => config.value.enabledDrawTypes.includes(btn.type))
    .map((btn) => {
      // 应用自定义按钮配置
      const custom = props.customButtons?.find((c) => c.type === btn.type);
      return custom ? { ...btn, ...custom } : btn;
    });
});

/**
 * @description 处理绘制完成事件
 * @param {DrawEventData} event - 绘制事件数据
 */
const handleDrawFinish = (event: DrawEventData) => {
  console.log("缓冲区分析绘制完成事件:", event);

  try {
    if (event.features) {
      // 处理 GeoJSONStoreFeatures 类型
      const features = Array.isArray(event.features)
        ? event.features
        : [event.features];

      if (features.length > 0) {
        // 获取第一个要素的几何体
        const feature = features[0];
        if (feature && feature.geometry) {
          const geometry = feature.geometry;

          // 创建绘制结果
          const result: DrawResult = {
            type: currentOperation.value!,
            geometry: geometry,
            properties: feature.properties || {},
            timestamp: Date.now(),
            success: true,
          };

          resultGeometry.value = geometry;
          hasDrawnGeometry.value = true;

          // 停止绘制，切换回选择模式
          if (config.value.autoSwitchToSelect && drawTool) {
            drawTool.setMode("select" as DrawMode);
          }

          // 清除操作状态
          currentOperation.value = null;
          currentTip.value = "";
          isLoading.value = false;

          console.log("缓冲区分析绘制几何体获取成功:", geometry);

          // 触发事件
          emit("draw-complete", result);
        } else {
          throw new Error("绘制要素无几何体数据");
        }
      } else {
        throw new Error("绘制完成但无要素数据");
      }
    } else {
      throw new Error("绘制完成事件无features数据");
    }
  } catch (error) {
    console.error("处理缓冲区分析绘制完成事件失败:", error);

    const errorMsg = (error as Error).message;
    const drawType = currentOperation.value!;

    // 清除操作状态
    currentOperation.value = null;
    currentTip.value = "";
    isLoading.value = false;

    // 触发错误事件
    emit("draw-error", { type: drawType, message: errorMsg });
  }
};

/**
 * @description 主要绘制处理函数
 * @param {DrawType} type - 绘制类型
 */
const handleDraw = async (type: DrawType) => {
  console.log(`开始执行缓冲区分析绘制 - 类型: ${type}`);

  try {
    // 设置操作状态
    isLoading.value = true;
    currentOperation.value = type;
    currentTip.value = "";

    // 触发绘制开始事件
    emit("draw-start", type);

    // 清除之前的绘制结果
    if (config.value.clearPreviousDrawing) {
      clearPreviousResults();
    }

    switch (type) {
      case "point":
        await handlePointDraw();
        break;
      case "linestring":
        await handleLinestringDraw();
        break;
      case "polygon":
        await handlePolygonDraw();
        break;
      case "rectangle":
        await handleRectangleDraw();
        break;
      default:
        throw new Error(`未知的绘制类型: ${type}`);
    }
  } catch (error) {
    console.error(`缓冲区分析绘制执行失败 (${type}):`, error);
    emit("draw-error", {
      type,
      message: (error as Error).message,
    });

    // 清除加载状态
    isLoading.value = false;
    currentOperation.value = null;
  }
};

/**
 * @description 处理点绘制
 */
const handlePointDraw = async (): Promise<void> => {
  console.log("开始绘制点");

  if (props.mapEngine === "cesium") {
    console.log("Cesium点绘制");

    const plotUtil = AppCesium.getInstance().getPlotUtil();

    currentTip.value = "请在地图上点击绘制点位";

    plotUtil.draw(
      "point",
      (overlay: any) => {
        console.log("Cesium点绘制完成:", overlay);

        if (cesiumLayer) {
          cesiumLayer.addOverlay(overlay);
        }

        const position = overlay.position;
        const geometry = {
          type: "Point",
          coordinates: [position.lng, position.lat],
        };

        const result: DrawResult = {
          type: "point",
          geometry,
          timestamp: Date.now(),
          success: true,
        };

        resultGeometry.value = geometry;
        hasDrawnGeometry.value = true;

        // 清除操作状态
        currentOperation.value = null;
        currentTip.value = "";
        isLoading.value = false;

        emit("draw-complete", result);
      },
      {},
      true
    );
  } else {
    console.log("MapLibre点绘制");

    if (!drawTool) {
      throw new Error("绘制工具未初始化，无法执行点绘制");
    }

    currentTip.value = "请在地图上点击绘制点位";

    // 清除之前的绘制结果
    drawTool.clearAllFeatures();
    // 切换到点绘制模式
    drawTool.setMode("point" as DrawMode);

    console.log("已切换到点绘制模式");
  }
};

/**
 * @description 处理线段绘制
 */
const handleLinestringDraw = async (): Promise<void> => {
  console.log("开始绘制线段");
  if (props.mapEngine === "cesium") {
    console.log("Cesium线段绘制");

    const plotUtil = AppCesium.getInstance().getPlotUtil();

    currentTip.value = "请在地图上绘制线段，双击结束";

    plotUtil.draw(
      "polyline",
      (overlay: any) => {
        console.log("Cesium线段绘制完成:", overlay);

        if (cesiumLayer) {
          cesiumLayer.addOverlay(overlay);
        }

        const positions = overlay.positions;
        const coordinates: number[][] = [];

        positions.forEach((position: any) => {
          coordinates.push([position.lng, position.lat]);
        });

        const geometry = {
          type: "LineString",
          coordinates,
        };

        const result: DrawResult = {
          type: "linestring",
          geometry,
          timestamp: Date.now(),
          success: true,
        };

        resultGeometry.value = geometry;
        hasDrawnGeometry.value = true;

        // 清除操作状态
        currentOperation.value = null;
        currentTip.value = "";
        isLoading.value = false;

        emit("draw-complete", result);
      },
      {},
      true
    );
  } else {
    console.log("MapLibre线段绘制");

    if (!drawTool) {
      throw new Error("绘制工具未初始化，无法执行线段绘制");
    }

    currentTip.value = "请在地图上绘制线段，双击结束绘制";

    // 清除之前的绘制结果
    drawTool.clearAllFeatures();
    // 切换到线段绘制模式
    drawTool.setMode("linestring" as DrawMode);

    console.log("已切换到线段绘制模式");
  }
};

/**
 * @description 处理多边形绘制
 */
const handlePolygonDraw = async (): Promise<void> => {
  console.log("开始绘制多边形");

  if (props.mapEngine === "cesium") {
    console.log("Cesium多边形绘制");

    const plotUtil = AppCesium.getInstance().getPlotUtil();

    currentTip.value = "请在地图上绘制多边形，双击结束";

    plotUtil.draw(
      "polygon",
      (overlay: any) => {
        console.log("Cesium多边形绘制完成:", overlay);

        if (cesiumLayer) {
          cesiumLayer.addOverlay(overlay);
        }

        const positions = overlay.positions;
        const coordinates: number[][] = [];

        positions.forEach((position: any) => {
          coordinates.push([position.lng, position.lat]);
        });

        // 确保多边形闭合
        if (coordinates.length > 0) {
          coordinates.push(coordinates[0]);
        }

        const geometry = {
          type: "Polygon",
          coordinates: [coordinates],
        };

        const result: DrawResult = {
          type: "polygon",
          geometry,
          timestamp: Date.now(),
          success: true,
        };

        resultGeometry.value = geometry;
        hasDrawnGeometry.value = true;

        // 清除操作状态
        currentOperation.value = null;
        currentTip.value = "";
        isLoading.value = false;
        coordinates.length > 2 && emit("draw-complete", result);
      },
      {},
      true
    );
  } else {
    console.log("MapLibre多边形绘制");

    if (!drawTool) {
      throw new Error("绘制工具未初始化，无法执行多边形绘制");
    }

    currentTip.value = "请在地图上绘制多边形，双击结束绘制";

    // 清除之前的绘制结果
    drawTool.clearAllFeatures();
    // 切换到多边形绘制模式
    drawTool.setMode("polygon" as DrawMode);

    console.log("已切换到多边形绘制模式");
  }
};

/**
 * @description 处理矩形绘制
 */
const handleRectangleDraw = async (): Promise<void> => {
  console.log("开始绘制矩形");

  if (props.mapEngine === "cesium") {
    console.log("Cesium矩形绘制");

    const plotUtil = AppCesium.getInstance().getPlotUtil();

    currentTip.value = "请在地图上绘制矩形";

    plotUtil.draw(
      "rectangle",
      (overlay: any) => {
        console.log("Cesium矩形绘制完成:", overlay);

        if (cesiumLayer) {
          cesiumLayer.addOverlay(overlay);
        }

        const positions = overlay.positions;
        // 西北角
        const northwest = positions[0];
        // 东南角
        const southeast = positions[1];

        const coordinates = [
          [
            [northwest.lng, northwest.lat],
            [southeast.lng, northwest.lat],
            [southeast.lng, southeast.lat],
            [northwest.lng, southeast.lat],
            [northwest.lng, northwest.lat],
          ],
        ];

        const geometry = {
          type: "Polygon",
          coordinates,
        };

        const result: DrawResult = {
          type: "rectangle",
          geometry,
          timestamp: Date.now(),
          success: true,
        };

        resultGeometry.value = geometry;
        hasDrawnGeometry.value = true;

        // 清除操作状态
        currentOperation.value = null;
        currentTip.value = "";
        isLoading.value = false;

        emit("draw-complete", result);
      },
      {},
      true
    );
  } else {
    console.log("MapLibre矩形绘制");

    if (!drawTool) {
      throw new Error("绘制工具未初始化，无法执行矩形绘制");
    }

    currentTip.value = "请在地图上绘制矩形，点击两个对角点";

    // 清除之前的绘制结果
    drawTool.clearAllFeatures();
    // 切换到矩形绘制模式
    drawTool.setMode("rectangle" as DrawMode);

    console.log("已切换到矩形绘制模式");
  }
};

/**
 * @description 清空所有绘制
 */
const clearAll = (): void => {
  console.log("清空所有绘制结果");

  // 如果正在绘制过程中，先中断当前绘制操作
  if (currentOperation.value) {
    console.log(`正在绘制${currentOperation.value}，中断当前绘制操作`);

    if (props.mapEngine === "cesium") {
      // 中断Cesium绘制操作
      try {
        const plotUtil = AppCesium.getInstance().getPlotUtil();
        if (plotUtil && (plotUtil as any).deactivate) {
          (plotUtil as any).deactivate();
          console.log("✓ 停用Cesium绘制操作");
        } else if (plotUtil && (plotUtil as any).stop) {
          (plotUtil as any).stop();
          console.log("✓ 停止Cesium绘制操作");
        }

        // 恢复Cesium地图鼠标样式
        const viewer = AppCesium.getInstance().getViewer();
        if (viewer && viewer.canvas) {
          viewer.canvas.style.cursor = "";
          console.log("✓ 恢复Cesium鼠标样式");
        }
      } catch (error) {
        console.warn("停止Cesium绘制工具失败:", error);
      }
    } else {
      // 中断MapLibre绘制操作
      if (drawTool) {
        try {
          // 切换到选择模式以中断绘制
          drawTool.setMode("select" as DrawMode);
          console.log("✓ 切换MapLibre到选择模式");

          // 恢复MapLibre地图鼠标样式
          const map = AppMaplibre.getMap();
          if (map && map.getCanvas) {
            map.getCanvas().style.cursor = "";
            console.log("✓ 恢复MapLibre鼠标样式");
          }
        } catch (error) {
          console.warn("停止MapLibre绘制工具失败:", error);
        }
      }
    }
  }

  // 清除之前的绘制结果
  clearPreviousResults();

  // 重置状态
  resultGeometry.value = null;
  hasDrawnGeometry.value = false;
  currentOperation.value = null;
  currentTip.value = "";
  isLoading.value = false;
  showDetails.value = false;

  emit("clear-all");
};

/**
 * @description 清除之前的结果
 */
const clearPreviousResults = (): void => {
  // 清除Cesium图层
  if (cesiumLayer) {
    cesiumLayer.clear();
  }

  // 清除MapLibre绘制
  if (drawTool) {
    drawTool.clearAllFeatures();
  }
};


defineExpose({
  clearAll,
});



/**
 * @description 组件挂载
 */
onMounted(async () => {
  if (props.mapEngine === "cesium") {
    // 初始化Cesium图层
    await nextTick();
    cesiumLayer = new BC.VectorLayer("bufferAnalysisDrawLayer");
    AppCesium.getInstance().getViewer().addLayer(cesiumLayer);
  } else {
    // 初始化MapLibre绘制工具
    try {
      await nextTick();
      drawTool = AppMaplibre.getDrawTool();

      // 安全启动绘制工具，等待地图加载完成
      if (drawTool && !drawTool.isEnabled()) {
        console.log("绘制工具未启动，等待地图加载完成后启动...");
        await drawTool.start(true); // 等待地图加载完成
        console.log("绘制工具启动成功");
      }

      // 添加绘制事件监听器
      drawTool.addEventListener(
        DrawEventTypeEnum.DRAW_FINISH,
        handleDrawFinish
      );
      console.log("MapLibre缓冲区分析绘制工具初始化成功");
    } catch (error) {
      console.error("MapLibre缓冲区分析绘制工具初始化失败:", error);

      // 如果初始化失败，尝试重新创建绘制工具
      try {
        console.log("尝试重新创建绘制工具...");
        AppMaplibre.recreateDrawTool();
        drawTool = AppMaplibre.getDrawTool();
        await drawTool.start(true); // 等待地图加载完成
        drawTool.addEventListener(
          DrawEventTypeEnum.DRAW_FINISH,
          handleDrawFinish
        );
        console.log("缓冲区分析绘制工具重新创建成功");
      } catch (recreateError) {
        console.error("重新创建缓冲区分析绘制工具失败:", recreateError);
      }
    }
  }
});

/**
 * @description 组件卸载
 */
onUnmounted(() => {
  if (props.mapEngine === "cesium") {
    // 清理Cesium图层和绘制工具
    console.log("缓冲区分析组件卸载 - 开始清理Cesium绘制工具");

    try {
      // 1. 如果正在绘制过程中，先取消当前绘制
      if (currentOperation.value) {
        console.log(`正在绘制${currentOperation.value}，取消当前绘制操作`);

        // 停止Cesium绘制工具
        try {
          const plotUtil = AppCesium.getInstance().getPlotUtil();
          if (plotUtil && (plotUtil as any).deactivate) {
            (plotUtil as any).deactivate();
            console.log("✓ 停用Cesium绘制操作");
          } else if (plotUtil && (plotUtil as any).stop) {
            (plotUtil as any).stop();
            console.log("✓ 停止Cesium绘制操作");
          }
        } catch (error) {
          console.warn("停止Cesium绘制工具失败:", error);
        }

        // 恢复Cesium地图鼠标样式
        const viewer = AppCesium.getInstance().getViewer();
        if (viewer && viewer.canvas) {
          viewer.canvas.style.cursor = "";
          console.log("✓ 恢复Cesium鼠标样式");
        }

        // 触发绘制取消事件
        emit("draw-error", {
          type: currentOperation.value,
          message: "组件关闭，绘制操作已取消",
        });
      }

      // 2. 清理Cesium图层
      if (cesiumLayer) {
        cesiumLayer.clear();
        AppCesium.getInstance().getViewer().removeLayer(cesiumLayer);
        console.log("✓ 清理Cesium图层");
        cesiumLayer = null;
      }

      // 3. 重置组件状态
      currentOperation.value = null;
      currentTip.value = "";
      isLoading.value = false;
      resultGeometry.value = null;
      hasDrawnGeometry.value = false;

      console.log("✓ 缓冲区分析组件状态已重置");
      console.log("Cesium缓冲区分析绘制工具已彻底清理和停止");
    } catch (error) {
      console.error("清理Cesium缓冲区分析绘制工具失败:", error);
      // 即使清理失败，也要重置状态
      currentOperation.value = null;
      currentTip.value = "";
      isLoading.value = false;
      resultGeometry.value = null;
      hasDrawnGeometry.value = false;

      if (cesiumLayer) {
        cesiumLayer = null;
      }
    }
  } else {
    // 清理MapLibre绘制工具
    console.log("缓冲区分析组件卸载 - 开始清理MapLibre绘制工具");

    if (drawTool) {
      try {
        // 1. 如果正在绘制过程中，先取消当前绘制
        if (currentOperation.value) {
          console.log(`正在绘制${currentOperation.value}，取消当前绘制操作`);

          // 触发绘制取消事件
          emit("draw-error", {
            type: currentOperation.value,
            message: "组件关闭，绘制操作已取消",
          });
        }

        // 2. 移除事件监听器
        drawTool.removeEventListener(
          DrawEventTypeEnum.DRAW_FINISH,
          handleDrawFinish
        );
        console.log("✓ 移除缓冲区分析绘制事件监听器");

        // 3. 清除所有绘制要素
        drawTool.clearAllFeatures();
        console.log("✓ 清除所有缓冲区分析绘制要素");

        // 4. 停止Terra Draw实例以彻底禁用绘制交互
        drawTool.stop();
        console.log("✓ 停止Terra Draw实例");

        // 5. 手动重置MapLibre地图画布的鼠标样式
        const map = AppMaplibre.getMap();
        if (map && map.getCanvas) {
          map.getCanvas().style.cursor = "";
          console.log("✓ 恢复MapLibre鼠标样式");
        }

        console.log("MapLibre缓冲区分析绘制工具已彻底清理和停止");
      } catch (error) {
        console.error("清理MapLibre缓冲区分析绘制工具失败:", error);
      } finally {
        // 6. 清理本地引用
        drawTool = null;

        // 7. 重置组件状态
        currentOperation.value = null;
        currentTip.value = "";
        isLoading.value = false;
        resultGeometry.value = null;
        hasDrawnGeometry.value = false;

        console.log("✓ 缓冲区分析组件状态已重置");
      }
    } else {
      console.log("缓冲区分析绘制工具未初始化，无需清理");
    }
  }

  console.log("BufferAnalysisDrawButtons组件卸载完成");
});
</script>

<style lang="scss" scoped>
.buffer-analysis-draw-buttons {
  .button-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
  }
  
  .draw-button {
    width: 100%;
    margin-bottom: 4px;
  }

  .operation-tip {
    margin-top: 12px;
  }

  .result-display {
    margin-top: 16px;

    .result-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .geometry-info {
      margin-bottom: 12px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 4px;
      font-size: 12px;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: #606266;
        }

        .value {
          color: #303133;
        }
      }
    }

    .result-details {
      background: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 12px;
      font-family: "Courier New", monospace;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;

      pre {
        margin: 0;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }

  .mb-2 {
    margin-bottom: 8px;
  }

  .mr-1 {
    margin-right: 4px;
  }

  .mt-2 {
    margin-top: 8px;
  }
}
</style>
