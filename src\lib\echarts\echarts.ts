/*
 * @Author: xiao
 * @Date: 2023-05-19 14:04:40
 * @LastEditors: xiao
 * @LastEditTime: 2024-05-22 09:59:36
 * @Description:
 */
import * as echarts from 'echarts/core'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, <PERSON><PERSON><PERSON>, <PERSON>ctor<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>auge<PERSON>hart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  // 数据集组件
  DatasetComponent,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent,
  LegendComponent,
  DataZoomComponent,
  PolarComponent,
  ToolboxComponent,
  GraphicComponent 
} from 'echarts/components'
// 3D
// import "echarts-gl";
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import type {
  // 系列类型的定义后缀都为 SeriesOption
  BarSeriesOption,
  LineSeriesOption,
  PieSeriesOption,
  PictorialBarSeriesOption,
  GaugeSeriesOption
} from 'echarts/charts'
import type {
  // 组件类型的定义后缀都为 ComponentOption
  TitleComponentOption,
  TooltipComponentOption,
  GridComponentOption,
  DatasetComponentOption,
  LegendComponentOption,
  DataZoomComponentOption,
  PolarComponentOption
} from 'echarts/components'
import type { ComposeOption } from 'echarts/core'

// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型
type ECOption = ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | PieSeriesOption
  | PictorialBarSeriesOption
  | GaugeSeriesOption
  | PolarComponentOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | DatasetComponentOption
  | LegendComponentOption
  | DataZoomComponentOption
>

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  PolarComponent,
  BarChart,
  LineChart,
  PieChart,
  GaugeChart,
  PictorialBarChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
  GraphicComponent 
])

export { echarts, type ECOption }
