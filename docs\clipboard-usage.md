# 剪贴板工具使用指南

## 概述

本项目提供了兼容性更好的剪贴板工具函数，解决了 `navigator.clipboard` 在非HTTPS环境下不可用的问题。

## 问题背景

### navigator.clipboard 的限制

`navigator.clipboard` API 只在以下情况下可用：

1. **HTTPS 环境**：必须是安全上下文（localhost 或 HTTPS）
2. **用户交互**：必须在用户交互事件中调用
3. **权限策略**：浏览器的权限策略允许
4. **现代浏览器**：不是所有浏览器都支持

### 常见问题场景

- ✅ 本地开发（localhost）：通常可用
- ❌ HTTP 部署：不可用
- ✅ HTTPS 部署：可用
- ❌ iframe 中：可能受限
- ❌ 某些浏览器：不支持

## 解决方案

### 兼容性策略

我们的剪贴板工具采用多层兼容性策略：

1. **优先使用现代API**：在支持的环境中使用 `navigator.clipboard`
2. **降级到传统方法**：使用 `document.execCommand('copy')`
3. **友好的错误处理**：提供手动复制提示

## 使用方法

### 1. 基础文本复制

```typescript
import { copyToClipboard } from '@/utils/clipboard';

// 基础使用
const success = await copyToClipboard('要复制的文本');

// 自定义选项
const success = await copyToClipboard('要复制的文本', {
  successMessage: '复制成功！',
  errorMessage: '复制失败',
  showMessage: true,
  showFallbackMessage: true
});
```

### 2. 坐标复制

```typescript
import { copyCoordinateToClipboard } from '@/utils/clipboard';

// 复制坐标
const success = await copyCoordinateToClipboard(
  116.123456,  // 经度
  39.654321,   // 纬度
  6,           // 精度（小数位数）
  'WGS84坐标'  // 坐标系名称
);
```

### 3. JSON数据复制

```typescript
import { copyJSONToClipboard } from '@/utils/clipboard';

const data = { name: '测试', value: 123 };

// 格式化复制
const success = await copyJSONToClipboard(data, true, '已复制配置数据');

// 压缩复制
const success = await copyJSONToClipboard(data, false);
```

### 4. 检查剪贴板支持

```typescript
import { getClipboardSupport, isClipboardAPIAvailable } from '@/utils/clipboard';

// 检查是否支持现代API
if (isClipboardAPIAvailable()) {
  console.log('支持现代剪贴板API');
}

// 获取详细支持信息
const support = getClipboardSupport();
console.log('剪贴板支持信息:', support);
```

## 在组件中使用

### Vue组件示例

```vue
<template>
  <div>
    <el-button @click="handleCopy">复制坐标</el-button>
    <el-button @click="handleCopyJSON">复制数据</el-button>
  </div>
</template>

<script setup lang="ts">
import { copyCoordinateToClipboard, copyJSONToClipboard } from '@/utils/clipboard';

const handleCopy = async () => {
  const success = await copyCoordinateToClipboard(116.123, 39.456, 6, 'WGS84');
  if (success) {
    console.log('坐标复制成功');
  }
};

const handleCopyJSON = async () => {
  const data = { lat: 39.456, lng: 116.123 };
  await copyJSONToClipboard(data, true, '已复制位置数据');
};
</script>
```

## 环境兼容性

### 支持的环境

| 环境 | 现代API | 传统方法 | 状态 |
|------|---------|----------|------|
| HTTPS | ✅ | ✅ | 完全支持 |
| localhost | ✅ | ✅ | 完全支持 |
| HTTP | ❌ | ✅ | 降级支持 |
| 旧浏览器 | ❌ | ✅ | 降级支持 |

### 部署建议

1. **生产环境**：建议使用HTTPS部署
2. **开发环境**：localhost自动支持
3. **测试环境**：如果是HTTP，会自动降级到传统方法

## 错误处理

### 自动降级

```typescript
// 工具会自动尝试多种方法
const success = await copyToClipboard('文本');

if (!success) {
  // 所有方法都失败时的处理
  console.log('复制失败，请手动复制');
}
```

### 手动复制提示

当自动复制失败时，工具会显示包含文本内容的提示消息，用户可以手动复制。

## 最佳实践

### 1. 用户交互中调用

```typescript
// ✅ 正确：在用户点击事件中调用
const handleClick = async () => {
  await copyToClipboard('文本');
};

// ❌ 错误：在页面加载时自动调用
onMounted(() => {
  copyToClipboard('文本'); // 可能被浏览器阻止
});
```

### 2. 错误处理

```typescript
const handleCopy = async () => {
  try {
    const success = await copyToClipboard('文本');
    if (!success) {
      // 提供备选方案
      showManualCopyDialog('文本');
    }
  } catch (error) {
    console.error('复制操作异常:', error);
  }
};
```

### 3. 功能检测

```typescript
import { isClipboardAPIAvailable } from '@/utils/clipboard';

// 根据支持情况调整UI
const showCopyButton = computed(() => {
  return isClipboardAPIAvailable() || 
         (typeof document !== 'undefined' && 'execCommand' in document);
});
```

## 迁移指南

### 从直接使用 navigator.clipboard 迁移

```typescript
// 旧代码
try {
  await navigator.clipboard.writeText(text);
  ElMessage.success('复制成功');
} catch (error) {
  ElMessage.error('复制失败');
}

// 新代码
import { copyToClipboard } from '@/utils/clipboard';

await copyToClipboard(text, {
  successMessage: '复制成功',
  errorMessage: '复制失败'
});
```

### 坐标复制迁移

```typescript
// 旧代码
const coordText = `${lng.toFixed(6)},${lat.toFixed(6)}`;
await navigator.clipboard.writeText(coordText);

// 新代码
import { copyCoordinateToClipboard } from '@/utils/clipboard';

await copyCoordinateToClipboard(lng, lat, 6, '坐标');
```

## 注意事项

1. **安全限制**：某些浏览器可能仍然限制剪贴板访问
2. **用户体验**：失败时提供手动复制选项
3. **性能考虑**：避免频繁调用剪贴板操作
4. **隐私保护**：不要复制敏感信息到剪贴板

## 调试技巧

### 检查支持状态

```typescript
import { getClipboardSupport } from '@/utils/clipboard';

console.log('剪贴板支持信息:', getClipboardSupport());
```

### 测试不同环境

1. **HTTPS环境**：应该支持所有功能
2. **HTTP环境**：只支持传统方法
3. **localhost**：通常支持所有功能
4. **iframe**：可能受到限制
