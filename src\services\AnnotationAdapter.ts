/**
 * @fileoverview 标注数据适配器
 * @description 处理地图标注数据与数据库API之间的格式转换
 * <AUTHOR>
 * @version 1.0.0
 */

import type { 
  IMapAnnotation, 
  CreateAnnotationParams 
} from '@/types/annotation';
import type { 
  PlottingCreateRequest, 
  PlottingUpdateRequest,
  PlottingInfoVo,
  PlottingPageVo
} from '@/types/plotting';
import { 
  EngineType,
  PlottingType 
} from '@/types/plotting';

/**
 * @class AnnotationAdapter
 * @description 标注数据适配器，处理不同数据格式之间的转换
 */
export class AnnotationAdapter {
  
  /**
   * @description 将创建参数转换为数据库创建请求
   * @param params 标注创建参数
   * @param engineType 引擎类型
   * @returns 数据库创建请求对象
   */
  static createParamsToDbRequest(
    params: CreateAnnotationParams, 
    engineType: EngineType
  ): PlottingCreateRequest {
    // 构建GeoJSON数据
    const geojsonData = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [params.lng, params.lat]
      },
      properties: {
        type: 'annotation',
        style: params.style,
        ...(params.alt !== undefined && { alt: params.alt })
      }
    };

    return {
      name: params.name.trim(),
      type: PlottingType.POINT,
      engineType: engineType,
      graphicsJson: JSON.stringify(geojsonData),
      remark: params.description || ''
    };
  }

  /**
   * @description 将标注对象转换为数据库更新请求
   * @param annotation 标注对象
   * @returns 数据库更新请求对象
   */
  static annotationToDbUpdateRequest(annotation: IMapAnnotation): PlottingUpdateRequest {
    return {
      id: parseInt(annotation.id),
      name: annotation.name,
      type: PlottingType.POINT,
      engineType: EngineType.CESIUM, // 固定为Cesium
      graphicsJson: JSON.stringify(annotation.geojson),
      remark: annotation.description || ''
    };
  }

  /**
   * @description 将数据库记录转换为标注对象
   * @param dbRecord 数据库记录
   * @returns 标注对象
   */
  static dbRecordToAnnotation(dbRecord: PlottingPageVo): IMapAnnotation {
    const geojsonData = JSON.parse(dbRecord.graphicsJson);
    
    return {
      id: dbRecord.id.toString(), // 直接使用数据库ID
      name: dbRecord.name,
      description: '', // PlottingPageVo没有remark字段，使用空字符串
      geojson: geojsonData,
      engineType: EngineType.CESIUM, // 固定为cesium
      createTime: dbRecord.createTime ? new Date(dbRecord.createTime).getTime() : Date.now(),
      modifyTime: dbRecord.updateTime ? new Date(dbRecord.updateTime).getTime() : Date.now(),
      visible: true,
      locked: false
    };
  }
} 