import { AppCesium } from '@/lib/cesium/AppCesium'
import { RoamingPath } from './type/RoamingPath'
import type { RouteData } from '@/utils/routeService'


export default class RoamTool {
  pathLayer: BC.VectorLayer
  datasource: any
  constructor(viewer: BC.Viewer) {
    this.pathLayer = new BC.VectorLayer('roamLayer')
    this.pathLayer.addTo(viewer)
    const { Cesium } = BC.Namespace;
    viewer.dataSources.add(new Cesium.CustomDataSource('roaming')).then((db) => {
      this.datasource = db
    })
  }

  /**
   * @description 改变漫游速度
   * @param routeId 路线ID
   * @param speed 速度值
   */
  changeSpeed(routeId: string, speed: number) {
    AppCesium.getInstance().RoamingManager.changeSpeed(routeId, speed)
  }

  /**
   * @description 改变漫游进度
   * @param routeId 路线ID  
   * @param progress 进度值
   */
  changeProgress(routeId: string, progress: number) {
    AppCesium.getInstance().RoamingManager.changeProgress(routeId, progress)
  }

  /**
   * @description 暂停漫游
   */
  pauseRoaming() {
    AppCesium.getInstance().RoamingManager.pause()
  }

  /**
   * @description 继续漫游
   * @param routeId 路线ID
   */
  continueRoaming(routeId: string) {
    AppCesium.getInstance().RoamingManager.continue(routeId)
  }

  /**
   * @description 停止漫游
   */
  stopRoaming() {
    // this.datasource && App.getInstance().getViewer().delegate.dataSources.remove(this.datasource)
    this.datasource && this.datasource.entities.removeAll()
    AppCesium.getInstance().RoamingManager.deactivate()
    AppCesium.getInstance().RoamingManager.clear()
    this.pathLayer.clear()
  }


  /**
   * @description 开始漫游
   * @param routeData 路线数据
   * @param callback 进度回调函数
   */
  startRoaming(routeData: RouteData, callback?: (progress: number) => void) {
    try {
      console.log('开始漫游路线:', routeData.name, routeData);
      const data = JSON.parse(routeData.value as any);
      // 验证路线数据
      if (!routeData || !data || data.length < 2) {
        throw new Error('路线数据无效或锚点不足');
      }

      // 将RouteStop数据转换为BC.Position格式
      const positions = this.convertRouteStopsToPositions(data);

      if (positions.length < 2) {
        throw new Error('有效锚点数量不足，至少需要2个锚点');
      }

      // 创建路径线
      const polyline: any = new BC.Polyline(positions);

      // 清理之前的路径
      this.pathLayer.clear();

      // 可选：在地图上显示路径线
      // this.pathLayer.addOverlay(polyline);

      // 创建漫游路径对象
      const path = new RoamingPath(routeData.id);
      path.range = 3;     // 视距范围
      path.speed = 2;     // 漫游速度
      path.pitch = 0;     // 俯仰角

      // 配置漫游管理器
      const roamingManager = AppCesium.getInstance().RoamingManager;
      roamingManager.addPath(path);
      roamingManager.changePath(routeData.id, polyline.positions);

      // 激活漫游
      roamingManager.active(routeData.id, (progress: number) => {
        callback && callback(progress);
      });

      console.log(`✈️ 漫游启动成功，路线: ${routeData.name}，锚点数量: ${positions.length}`);

    } catch (error) {
      console.error('启动漫游失败:', error);
      throw error;
    }
  }

  /**
   * @description 将路线锚点转换为BC.Position格式
   * @param stops 路线锚点数组
   * @returns BC.Position数组
   */
  private convertRouteStopsToPositions(stops: RouteData['stops']): BC.Position[] {
    try {
      // 按index排序确保顺序正确
      const sortedStops = [...stops].sort((a, b) => a.index - b.index);

      const positions = sortedStops.map((stop, index) => {
        // 验证坐标数据
        if (typeof stop.lon !== 'number' || typeof stop.lat !== 'number') {
          console.warn(`锚点 ${stop.name} 坐标数据无效:`, stop);
          return null;
        }

        // 经度范围检查
        if (stop.lon < -180 || stop.lon > 180) {
          console.warn(`锚点 ${stop.name} 经度超出范围: ${stop.lon}`);
          return null;
        }

        // 纬度范围检查
        if (stop.lat < -90 || stop.lat > 90) {
          console.warn(`锚点 ${stop.name} 纬度超出范围: ${stop.lat}`);
          return null;
        }

        // 高度处理，如果没有设置高度则使用默认值
        const height = typeof stop.height === 'number' && stop.height > 0 ? stop.height : 100;

        console.log(`转换锚点 ${index + 1}: ${stop.name} [${stop.lon}, ${stop.lat}, ${height}]`);

        return new BC.Position(stop.lon, stop.lat, height);
      }).filter(pos => pos !== null); // 过滤掉无效的坐标

      console.log(`坐标转换完成，有效锚点: ${positions.length}/${stops.length}`);
      return positions as BC.Position[];

    } catch (error) {
      console.error('转换锚点坐标失败:', error);
      throw new Error('坐标数据转换失败');
    }
  }





}
