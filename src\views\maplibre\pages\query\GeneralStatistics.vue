<template>
  <page-card class="general-statistics" title="通用统计" :close-icon="false">
    <!-- 查询表单 -->
    <el-form class="query-form">
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item class="page-form-item" label="分组字段：">
            <el-select v-model="groupField" placeholder="请选择分组字段" style="width: 100%" @change="handleGroupFieldChange">
              <el-option v-for="item in groupOptions" :key="item.fieldKey" :label="item.fieldName" :value="item.fieldKey" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="page-form-item" label="统计字段：">
            <el-select
              v-model="statisticsField"
              placeholder="请选择统计字段"
              style="width: 100%"
            >
              <el-option v-for="item in statisticsFieldOptions" :key="item.fieldKey" :label="item.fieldName" :value="item.fieldKey" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item class="page-form-item" label="统计方式：">
            <el-select v-model="statisticsMethod" placeholder="请选择统计方式" style="width: 100%">
              <el-option v-for="item in statisticsOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="page-form-item" label="展示方式：">
            <el-select v-model="displayMethod" placeholder="请选择展示方式" style="width: 100%">
              <el-option v-for="item in displayOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 空间查询按钮 -->
    <SpatialQueryButtons
      :map-engine="mapEngine"
      :config="spatialQueryConfig"
      @query-start="handleQueryStart"
      @query-complete="handleQueryComplete"
      @query-error="handleQueryError"
      @draw-start="handleDrawStart"
      @draw-finish="handleDrawFinish"
      class="px-6px"
    />

    <!-- 统计图表 -->
    <div v-if="showChart" class="chart-container" v-loading="loading">
      <div class="chart-wrapper">
        <!-- 无求和项提示 -->
        <div v-if="showNoSumMessage" class="no-sum-message">
          <el-empty description="无求和项" :image-size="80">
            <template #description>
              <p>请选择统计字段进行求和统计</p>
            </template>
          </el-empty>
        </div>
        <!-- 图表容器 -->
        <div v-else class="chart-placeholder" ref="chartRef"></div>
      </div>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import SpatialQueryButtons from '@/components/SpatialQueryButtons.vue';
import type {
  QueryType,
  QueryResult,
  SpatialQueryConfig
} from '@/components/SpatialQueryButtons.vue';
import type { IotDeviceField } from './SpatialQuery.vue';
// import { getMockIotDevices } from '@/api/query';
import * as echarts from 'echarts';
import type { MapEngineType } from '@/components/SpatialQueryButtons.vue';
import {
  groupDevicesByField,
  calculateStatistics,
  processQueryCoordinates,
  spatialQuery,
  fetchDeviceFields,
  calculateDeviceTypeStatistics,
  type ResFields
} from '@/utils/deviceUtils';

const route = useRoute()

const mapEngine = computed((): MapEngineType => {
  return route.path.includes('cesium') ? 'cesium' : 'maplibre';
});


const statisticsOptions = ['求和', '计数']
const displayOptions = ['统计图', '折线图']
const groupOptions = ref<ResFields[]>([])
const statisticsFieldOptions = ref<ResFields[]>([])

// 表单数据
const groupField = ref('');
const statisticsField = ref('');
const statisticsMethod = ref('计数');
const displayMethod = ref('统计图');

// 图表相关
const showChart = ref(false);
const chartRef = ref();
const chartInstance = ref<echarts.ECharts | null>(null);

// 设备数据相关 - 支持两种数据格式
const deviceData = ref<IotDeviceField[][] | Record<string, IotDeviceField[][]>>([]);
const loading = ref(false);

// 防抖定时器
let debounceTimer: number | null = null;

// 计算属性：是否显示无求和项提示
const showNoSumMessage = computed(() => {
  return statisticsMethod.value === '求和' && !statisticsField.value;
});

/**
 * 获取设备数据
 */
const fetchIotDevices = async (result: any) => {
  // 如果选择求和但没有选择统计字段，显示提示信息，不发送请求
  if (statisticsMethod.value === '求和' && !statisticsField.value) {
    showChart.value = true;
    return;
  }

  loading.value = true;

  // 清理之前的数据和图表
  deviceData.value = [];
  if (chartInstance.value) {
    try {
      chartInstance.value.dispose();
    } catch (error) {
      console.warn('清理图表实例时出错:', error);
    }
    chartInstance.value = null;
  }

  try {
    const coordinates = processQueryCoordinates(result);

    // 根据是否选择分组字段决定请求参数
    const queryParams = { coordinate: coordinates, fieldGroup: groupField.value }

    const response = await spatialQuery(queryParams);
    // 处理两种不同的数据格式
    if (response && (Array.isArray(response) ? response.length > 0 : Object.keys(response).length > 0)) {
      deviceData.value = response;

      // 等待loading状态更新完成后再生成统计
      await nextTick();
      generateStatistics();
    } else {
      deviceData.value = [];
      statisticsFieldOptions.value = [];
      showChart.value = false;
    }
  } catch (err) {
    console.error('获取物联网设备数据出错:', err);
    deviceData.value = [];
    showChart.value = false;
  } finally {
    loading.value = false;
  }
};

/**
 * @description 空间查询配置
 */
const spatialQueryConfig: Partial<SpatialQueryConfig> = {
  // 沙湾区边界配置
  regionBounds: {
    west: 103.42,
    south: 29.19,
    east: 103.74,
    north: 29.53,
    name: '沙湾区'
  },
  // UI配置
  buttonSize: 'default',
  buttonLayout: 'horizontal',
  buttonSpacing: 8,
  // 功能配置
  enabledQueries: ['all', 'current', 'polygon', 'rectangle'],
  showTips: true,
  // 绘制配置
  clearPreviousDrawing: true,
  autoSwitchToSelect: true
};

// 组件状态
const currentResult = ref<QueryResult | null>(null);

/**
 * @description 处理查询开始事件
 * @param {QueryType} _type - 查询类型
 */
const handleQueryStart = (_type: QueryType) => {

  // 清理之前的状态
  currentResult.value = null;

  // 清理图表实例，避免DOM冲突
  if (chartInstance.value) {
    try {
      chartInstance.value.dispose();
    } catch (error) {
      console.warn('清理图表实例时出错:', error);
    }
    chartInstance.value = null;
  }

  // 清理防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }

  showChart.value = true; // 显示图表容器
};

/**
 * 根据选择的字段生成统计数据（带防抖）
 */
const generateStatistics = () => {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  // 设置防抖延迟
  debounceTimer = setTimeout(() => {
    generateStatisticsInternal();
  }, 300);
};

/**
 * 内部统计生成函数
 */
const generateStatisticsInternal = () => {
  // 检查数据是否存在
  const hasData = deviceData.value && (
    Array.isArray(deviceData.value)
      ? deviceData.value.length > 0
      : Object.keys(deviceData.value).length > 0
  );
  if (!hasData) {
    showChart.value = false;
    return;
  }

  const groupFieldValue = groupField.value;
  const statisticsMethodValue = statisticsMethod.value;
  const displayMethodValue = displayMethod.value;

  // 如果选择求和但没有选择统计字段，显示提示信息
  if (statisticsMethodValue === '求和' && !statisticsField.value) {
    showChart.value = true; // 显示图表容器以显示提示信息
    return;
  }

  try {
    let groupedData: Record<string, IotDeviceField[][]>;

    // 判断数据格式并处理
    if (Array.isArray(deviceData.value)) {
      // iot_spatial_device.json 格式：IotDeviceField[][]
      if (groupFieldValue) {
        // 有分组字段，按字段分组
        groupedData = groupDevicesByField(deviceData.value, groupFieldValue);
      } else {
        // 没有分组字段，将所有设备放在一个默认分组中
        groupedData = { '全部': deviceData.value };
      }
    } else {
      // iot_fields.json 格式：Record<string, IotDeviceField[][]>
      // 数据已经按分组字段分组
      groupedData = deviceData.value as Record<string, IotDeviceField[][]>;
    }

    let statisticsData: { name: string; value: number }[];
    if (groupFieldValue == 'second_device_type_code' || groupFieldValue == 'first_device_type_code') {
      const deviceStats = calculateDeviceTypeStatistics(groupedData, groupFieldValue);
      statisticsData = deviceStats;
    } else {
      // 根据统计方式计算数据
      statisticsData = calculateStatistics(groupedData, statisticsMethodValue,groupFieldValue, statisticsField.value);
    }

    // 根据展示方式生成图表
    generateChart(statisticsData, displayMethodValue);
  } catch (error) {
    console.error('统计生成失败:', error);
    showChart.value = false;
  }
};


/**
 * 生成图表
 */
const generateChart = (data: { name: string; value: number }[], displayType: string) => {

  if (data.length === 0) {
    showChart.value = false;
    return;
  }

  // 确保图表容器可见
  showChart.value = true;

  // 清除之前的图表实例
  if (chartInstance.value) {
    try {
      chartInstance.value.dispose();
    } catch (error) {
      console.warn('清除图表实例时出错:', error);
    }
    chartInstance.value = null;
  }

  // 等待DOM更新后创建图表实例
  setTimeout(() => {
    try {
      // 多重检查确保DOM元素存在且可用
      if (!chartRef.value) {
        return;
      }

      if (!chartRef.value.offsetParent && chartRef.value.offsetWidth === 0) {
        setTimeout(() => generateChart(data, displayType), 100);
        return;
      }

      // 创建新的图表实例
      chartInstance.value = echarts.init(chartRef.value);

  let option: any;

  if (displayType === '统计图') {
    // 饼图配置
    const total = data.reduce((sum, item) => sum + item.value, 0);
    option = {
      title: {
        text: `总数：${total}台`,
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 500,
          color: '#2C3037'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          // const percent = ((params.value / total) * 100).toFixed(1);
          return `${params.marker} ${params.name}：${params.value}台`;
        }
      },
      series: [
        {
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['60%', '80%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'outside',
            formatter: '{c}台',
            fontSize: 12
          },
          labelLine: {
            show: true,
            length: 10,
            length2: 0
          },
          data: data
        }
      ]
    };
  } else if (displayType === '折线图') {
    // 折线图配置
    option = {
      title: {
        text: '设备统计',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          return `${params[0].name}：${params[0].value}台`;
        }
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.name),
        axisLabel: {
          rotate: 45,
          interval: 0
        }
      },
      yAxis: {
        type: 'value',
        name: '数量(台)'
      },
      series: [
        {
          type: 'line',
          data: data.map(item => item.value),
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 2
          }
        }
      ]
    };
  }

      if (option) {
        chartInstance.value.setOption(option);
        showChart.value = true;
      }
    } catch (error) {
      console.error('图表创建失败:', error);
      showChart.value = false;
      if (chartInstance.value) {
        chartInstance.value.dispose();
        chartInstance.value = null;
      }
    }
  }, 100); // 给DOM更新留出时间
};

/**
 * @description 处理查询完成事件
 * @param {QueryResult} result - 查询结果
 */
const handleQueryComplete = (result: QueryResult) => {
  currentResult.value = result;
  // 获取设备数据并生成统计
  fetchIotDevices(result);
};

/**
 * @description 处理查询错误事件
 * @param {object} error - 错误信息
 */
const handleQueryError = (error: { type: QueryType; message: string }) => {
  console.error('通用统计查询错误:', error);
};

/**
 * @description 处理绘制开始事件
 * @param {QueryType} _type - 绘制类型
 */
const handleDrawStart = (_type: QueryType) => {
};

/**
 * @description 处理绘制完成事件
 * @param {QueryResult} result - 绘制结果
 */
const handleDrawFinish = (result: QueryResult) => {
  currentResult.value = result;
};

/**
 * @description 处理分组字段变化事件
 * @param {string} value - 选中的分组字段值
 */
const handleGroupFieldChange = (value: string) => {
  if(currentResult.value) {
    fetchIotDevices(currentResult.value);
  }
};

// 监听统计方式变化，当选择"计数"时清空统计字段
watch(statisticsMethod, (newMethod) => {
  if (newMethod === '计数') {
    statisticsField.value = '';
  }
});

// 监听表单字段变化，重新生成统计
watch([groupField, statisticsMethod, displayMethod, statisticsField], () => {
  const hasData = deviceData.value && (
    Array.isArray(deviceData.value)
      ? deviceData.value.length > 0
      : Object.keys(deviceData.value).length > 0
  );

  if (hasData) {
    generateStatistics(); // 使用防抖的统计生成函数
  }
}, { flush: 'post' }); // 确保在DOM更新后执行

onMounted(async () => {
  const fields = await fetchDeviceFields({ size: -1 });
  
  const allKeys = ['describe', 'device_code','latitude','longitude','password','picture','username','equipmentTime']
  
  groupOptions.value = fields.filter(f => !allKeys.includes(f.fieldKey))
})

// 组件卸载时清理图表和定时器
onUnmounted(() => {
  // 清理防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }

  // 清理图表实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
    chartInstance.value = null;
  }
});
</script>

<style lang="scss" scoped>
.general-statistics {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 694px;
  max-width: calc(100vw - 20px);
  box-sizing: border-box;
}

.query-form {
  overflow: hidden;

  :deep(.el-row) {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  :deep(.el-col) {
    padding-left: 6px !important;
    padding-right: 6px !important;
  }

  :deep(.page-form-item) {
    margin-bottom: 16px;

    .el-form-item__label {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.chart-container {
  margin-top: 16px;
  background-color: #fff;
  border-radius: 4px;
  padding: 12px;
  overflow: hidden;
}

.chart-wrapper {
  width: 100%;
  height: 400px;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
}

.no-sum-message {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  :deep(.el-empty__description p) {
    color: #909399;
    font-size: 14px;
    margin: 0;
  }
}
</style>