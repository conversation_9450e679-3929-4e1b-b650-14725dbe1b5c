<template>
  <page-card class="pipe-sta" title="管网统计" :close-icon="false">
    <el-form>
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item class="page-form-item" label="管网图层：">
            <el-select
              v-model="pipeType"
              placeholder="请选择管网图层"
              @change="handlePipeTypeChange"
            >
              <el-option
                v-for="item in pipeLayerTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="page-form-item" label="分类字段：">
            <el-select
              v-model="pipeField"
              placeholder="请选择分类字段"
              :disabled="!pipeType || loading"
              @change="handlePipeFieldChange"
            >
              <el-option
                v-for="item in pipeFieldOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <SpatialQueryButtons
      ref="spatialQueryButtonsRef"
      :map-engine="mapEngine"
      :config="spatialQueryConfig"
      @query-start="handleQueryStart"
      @query-complete="handleQueryComplete"
      @query-error="handleQueryError"
      @draw-start="handleDrawStart"
      @draw-finish="handleDrawFinish"
      :fetchLoading="loading"
      class="px-6px"
    >
      <template #other-buttons>
        <el-button class="clear-btn" @click="handleReset">重置</el-button>
      </template>
    </SpatialQueryButtons>
    <div class="query-result mt-16px" v-show="showResult">
      <el-text class="pl-6px">统计结果</el-text>
      <StatisticsChart :data="statisticsData" :option-generator="currentOptionGenerator" />
    </div>
  </page-card>
</template>

<script setup lang="ts">
import SpatialQueryButtons from '@/components/SpatialQueryButtons.vue';
import type {
  QueryType,
  QueryResult,
  SpatialQueryConfig
} from '@/components/SpatialQueryButtons.vue';
import type { MapEngineType } from '@/components/SpatialQueryButtons.vue';
import { queryPtStatisticsField, queryPtStatistics, queryLnStatisticsField, queryLnStatistics } from '@/api/query';
import { initTabulateEchart } from '@/lib/echarts';
import StatisticsChart from '@/components/StatisticsChart.vue';

const route = useRoute();

const mapEngine = computed((): MapEngineType => {
  return route.path.includes('cesium') ? 'cesium' : 'maplibre';
});

// 管网图层数据
const pipeLayerTypes = [
  { label: '管线', value: 'pipeline' },
  { label: '管点', value: 'pipepoint' }
] as const;

const pipeType = ref('');
const pipeField = ref('');
const loading = ref(false);

// 分类字段选项
const pipeFieldOptions = ref<Array<{ label: string; value: string }>>([]);

// 统计数据
const statisticsData = ref<Array<{ name: string; value: number }>>([]);

// 当前单位
const currentUnit = ref<string>('米');

// 显示结果
const showResult = computed(() => {
  return Array.isArray(statisticsData.value) && statisticsData.value.length > 0;
});

// 带单位的图表配置生成器
const currentOptionGenerator = computed(() => {
  return (data: Array<{ name: string; value: number }>) => initTabulateEchart(data, currentUnit.value);
});

// 空间查询配置
const spatialQueryConfig: Partial<SpatialQueryConfig> = {
  // 沙湾区边界配置
  regionBounds: {
    west: 103.42,
    south: 29.19,
    east: 103.74,
    north: 29.53,
    name: '沙湾区'
  },
  // UI配置
  buttonSize: 'default',
  buttonLayout: 'horizontal',
  buttonSpacing: 8,
  // 功能配置
  enabledQueries: ['all', 'current', 'polygon', 'rectangle'],
  showTips: true,
  // 绘制配置
  clearPreviousDrawing: true,
  autoSwitchToSelect: true
};

// 组件状态
const currentResult = ref<QueryResult | null>(null);

// SpatialQueryButtons 组件引用
const spatialQueryButtonsRef = ref();

/**
 * 处理管网图层类型变化
 */
const handlePipeTypeChange = async () => {
  if (!pipeType.value) {
    pipeFieldOptions.value = [];
    pipeField.value = '';
    return;
  }

  try {
    loading.value = true;
    let response;

    // 根据管网图层类型请求对应的分类字段
    if (pipeType.value === 'pipeline') {
      // 管线
      response = await queryLnStatisticsField();
    } else if (pipeType.value === 'pipepoint') {
      // 管点
      response = await queryPtStatisticsField();
    }

    if (response && response.code === 200 && response.data) {
      pipeFieldOptions.value = response.data.map((item: any) => ({
        label: item.name,
        value: item.field
      }));
    } else {
      pipeFieldOptions.value = [];
    }

    // 清空已选择的分类字段
    pipeField.value = '';
  } catch (error) {
    console.error('获取分类字段失败:', error);
    pipeFieldOptions.value = [];
    ElMessage.error('获取分类字段失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 执行管网统计查询
 */
const executeStatisticsQuery = async () => {
  if (!pipeType.value || !pipeField.value || !currentResult.value) {
    return;
  }

  try {
    loading.value = true;

    const coords = currentResult.value?.geometry ?? []
    const coordsStr = JSON.stringify(coords)

    let statisticsResult;

    // 根据管网图层类型设置单位和调用对应的统计接口
    if (pipeType.value === 'pipeline') {
      // 管线统计，单位是米
      currentUnit.value = '米';
      statisticsResult = await queryLnStatistics({
        range: coordsStr,
        field: pipeField.value
      });
    } else if (pipeType.value === 'pipepoint') {
      // 管点统计，单位是个
      currentUnit.value = '个';
      statisticsResult = await queryPtStatistics({
        range: coordsStr,
        field: pipeField.value
      });
    }

    // 处理数据格式，将Object转换为统计图需要的数据格式
    if (statisticsResult && statisticsResult.code === 200 && statisticsResult.data) {
      const data = statisticsResult.data;
      const chartData: Array<{ name: string; value: number }> = [];

      // 将Object格式转换为数组格式
      Object.entries(data).forEach(([key, value]) => {
        chartData.push({
          name: key,
          value: Number(value) || 0
        });
      });

      // 按值降序排序
      chartData.sort((a, b) => b.value - a.value);
      statisticsData.value = chartData;
    } else {
      statisticsData.value = [];
    }

  } catch (error) {
    console.error('管网统计查询失败:', error);
    ElMessage.error('管网统计查询失败');
    statisticsData.value = [];
  } finally {
    loading.value = false;
  }
};

/**
 * 处理分类字段变化
 */
const handlePipeFieldChange = async () => {
  // 只有在有查询结果且选择了分类字段时才重新发送请求
  if (!currentResult.value || !pipeField.value || !pipeType.value) {
    return;
  }

  await executeStatisticsQuery();
};

/**
 * @description 处理查询开始事件
 * @param {QueryType} type - 查询类型
 */
const handleQueryStart = (type: QueryType) => {
  // 清空当前查询结果
  currentResult.value = null;
};

/**
 * @description 处理查询完成事件
 * @param {QueryResult} result - 查询结果
 */
const handleQueryComplete = async (result: QueryResult) => {
  currentResult.value = result;

  // 检查是否选择了管网图层类型和分类字段
  if (!pipeType.value || !pipeField.value) {
    ElMessage.warning('请先选择管网图层类型和分类字段');
    return;
  }

  await executeStatisticsQuery();
};

/**
 * @description 处理查询错误事件
 * @param {object} error - 错误信息
 */
const handleQueryError = (error: { type: QueryType; message: string }) => {
  console.error('管网统计查询错误:', error);
};

/**
 * @description 处理绘制开始事件
 * @param {QueryType} type - 绘制类型
 */
const handleDrawStart = (type: QueryType) => {
  console.log(`开始绘制统计区域: ${type}`);
};

/**
 * @description 处理绘制完成事件
 * @param {QueryResult} result - 绘制结果
 */
const handleDrawFinish = (result: QueryResult) => {
  currentResult.value = result;
};

const handleReset = () => {
  // 清除所有数据
  pipeType.value = '';
  pipeField.value = '';
  pipeFieldOptions.value = [];
  statisticsData.value = [];
  currentUnit.value = '米';

  if (spatialQueryButtonsRef.value && spatialQueryButtonsRef.value.clearPreviousResults) {
    spatialQueryButtonsRef.value.clearPreviousResults();
  } else {
    console.error("无法访问 SpatialQueryButtons 的 clearPreviousResults 方法");
  }
  if (spatialQueryButtonsRef.value && spatialQueryButtonsRef.value.clearActive) {
    spatialQueryButtonsRef.value.clearActive();
  } else {
    console.error("无法访问 SpatialQueryButtons 的 clearActive 方法");
  }

  currentResult.value = null;

  currentResult.value = null;
};
</script>

<style scoped lang="scss">
.pipe-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 694px;
  max-width: calc(100vw - 20px);
  box-sizing: border-box;
}

:deep(.el-row) {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

:deep(.el-col) {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

:deep(.page-form-item) {
  margin-bottom: 16px;

  .el-form-item__label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.query-result {
  margin-top: 16px;
}
</style>