/**
 * @fileoverview MapLibre GL JS地图应用核心管理类
 * @description 提供MapLibre地图的初始化、配置和管理功能
 * <AUTHOR>
 * @version 2.0.0 - 增强清理和重置功能
 */

import { Map as GlMap, type LngLatLike } from 'maplibre-gl'
import MeasureTool from './measure/MeasureTool'
import { createDrawManager, type DrawManager } from './draw'
import { BaseLayerManager, type BaseLayerManagerOptions, type BasemapConfig } from './basemap/BaseLayerManager'
// import MapboxDraw from '@mapbox/mapbox-gl-draw'
import { LayerManager } from './layer/LayerManager'
// import { LayerGroup } from './layer/layers/LayerGroup'
// import baseLayerConfig from '@/lib/maplibre/layer/baseLayer.json'

// 添加管线管点详情查看相关API导入
import { pipeNodeDetail } from '@/api/pipeNode'
import { pipeLineDetail } from '@/api/pipeLine'
import { ElMessage } from 'element-plus'

/**
 * @class AppMaplibre
 * @description MapLibre GL JS地图应用的核心管理类（优化版本）
 * @details 负责地图实例的创建、配置、销毁以及相关工具的管理，支持完整的资源清理和状态重置
 * @version 2.0.0 - 增强清理和重置功能
 */
export class AppMaplibre {
  /** @description 地图实例对象 */
  static _map: GlMap
  
  /** @description 是否开启点击事件 */
  static openClickEvent: boolean = true
  
  static _drawTool: DrawManager
  
  /** @description 地图是否已创建的标志 */
  public static mapCreated: boolean = false
  
  /** @description 测量工具实例 */
  private static _measureTool: MeasureTool | undefined = undefined
  
  /** @description 基础图层管理器实例 */
  private static _baseLayerManager: BaseLayerManager | undefined = undefined
  
  static _layerManager: LayerManager | undefined = undefined
  
  /** @description 系统默认地图中心点坐标（沙湾区） */
  static _center: LngLatLike = [103.58482512650977, 29.414037255380464]
  
  /** @description 默认地图缩放级别 */
  static _zoom: number = 12
  
  /** @description 沙湾区地理边界范围 */
  static _bounds: [[number, number], [number, number]] = [
    [103.367098213, 29.324590107], // 西南角 [west, south]
    [103.833177651, 29.538829755]  // 东北角 [east, north]
  ]

  /** @description 实例初始化状态标志 */
  private static _initialized: boolean = false

  /** @description 清理状态标志 */
  private static _isDestroyed: boolean = false

  /** @description 事件监听器管理 */
  private static _eventListeners: Map<string, Function[]> = new Map()
  
  /** @description 要素详情查看功能是否启用 */
  private static _featureDetailEnabled: boolean = false
  
  /** @description 查看详情点击处理器 */
  private static _detailClickHandler: Function | null = null

  /**
   * @description 检查实例是否已初始化
   * @returns 是否已初始化
   */
  static isInitialized(): boolean {
    const result = this._initialized && !this._isDestroyed && !!this._map;
    console.log(`🔍 [AppMaplibre] 状态检查 - isInitialized: ${result} (initialized: ${this._initialized}, isDestroyed: ${this._isDestroyed}, hasMap: ${!!this._map})`);
    return result;
  }

  /**
   * @description 检查实例是否已销毁
   * @returns 是否已销毁
   */
  static isDestroyed(): boolean {
    console.log(`🔍 [AppMaplibre] 状态检查 - isDestroyed: ${this._isDestroyed}`);
    return this._isDestroyed;
  }

  /**
   * @description 重置所有静态状态
   * @details 强制重置所有静态变量，用于引擎切换
   */
  static resetInstance(): void {
    console.log('🔄 [AppMaplibre] 重置实例状态...');
    
    try {
      // 执行完整清理（可能会设置_isDestroyed为true）
      this._performFullCleanup();
      
    } catch (error) {
      console.error('❌ [AppMaplibre] 清理过程中出现异常:', error);
      
    } finally {
      // 无论清理是否成功，都要重置所有静态状态
      // 这确保了即使清理失败，实例也能重新初始化
      try {
        this._map = undefined as any;
        this._drawTool = undefined as any;
        this._measureTool = undefined;
        this._baseLayerManager = undefined;
        this._layerManager = undefined;
        this.mapCreated = false;
        this.openClickEvent = true;
        this._initialized = false;
        this._isDestroyed = false; // 强制重置为可用状态
        this._eventListeners.clear();
        
        console.log('✅ [AppMaplibre] 实例状态重置完成 (强制重置)');
        
      } catch (resetError) {
        console.error('❌ [AppMaplibre] 强制重置状态失败:', resetError);
        // 最后的保险措施，确保关键状态被重置
        this._isDestroyed = false;
        this._initialized = false;
        this.mapCreated = false;
      }
    }
  }

  /**
   * @description 获取实例状态信息
   * @returns 实例状态对象
   */
  static getStatus() {
    return {
      initialized: this._initialized,
      destroyed: this._isDestroyed,
      mapCreated: this.mapCreated,
      hasMap: !!this._map,
      hasDrawTool: !!this._drawTool,
      hasMeasureTool: !!this._measureTool,
      hasBaseLayerManager: !!this._baseLayerManager,
      hasLayerManager: !!this._layerManager,
      openClickEvent: this.openClickEvent,
      eventListenersCount: this._eventListeners.size
    };
  }
  
  /**
   * @description 初始化MapLibre地图实例
   * @param {string} id - 地图容器的DOM元素ID
   * @returns {GlMap} 返回创建的地图实例
   * @example
   * ```typescript
   * const map = AppMaplibre.initMap('map-container');
   * ```
   */
  static initMap (id: string) {
    // 如果实例处于销毁状态，先重置状态以允许重新初始化
    if (this._isDestroyed) {
      console.log('🔄 [AppMaplibre] 检测到实例已销毁，正在重置状态以允许重新初始化...');
      this._isDestroyed = false;
      this._initialized = false;
      this.mapCreated = false;
    }

    console.log('📋 [AppMaplibre] 初始化地图实例...');
    
    try {
      // 销毁现有地图实例
      this.destroy()
      
      // 创建新的地图实例
      const map = new GlMap({
        container: id, // container id
        // style: null,
        style: {
          version: 8,
          sources: {},
          layers: [],
          glyphs: 'https://demotiles.maplibre.org/font/{fontstack}/{range}.pbf',
        },
        zoom: this._zoom,
        center: this._center,
        // minZoom: 11,
        // maxZoom: 18,
        // 添加WebGL上下文配置以支持Canvas截图
        canvasContextAttributes: {
          preserveDrawingBuffer: true,
          antialias: true,
          alpha: true
        },
        // hash: true,
        dragRotate: false
      })
      this._map = map
      
      // const draw = new MapboxDraw({
      //   displayControlsDefault: false,
      //   controls: {
      //     polygon: true,
      //     trash: true,
      //   },
      //   defaultMode: 'simple_select',
      // })
      
      // 地图加载完成后的回调
      map.once('load', async () => {
        try {
          // 异步初始化基础图层管理器
          await this.initBaseLayerManager()
          // 添加管线图层
          await this.getLayerManager().loadAllLayers()
          // 添加预警图层
            await this.getLayerManager().loadAlarmLayer();
          const onlyAnalysis = useAnalysisModeStore().isAnalysisOnly;
          if (!onlyAnalysis) {
            
            await this.getLayerManager().loadLeakLayer();
          }
          // 标记初始化完成
          this._initialized = true;
          this._isDestroyed = false;
          this.mapCreated = true;
          
          console.log('✅ [AppMaplibre] 地图加载和初始化完成');
          
          // 启用要素详情查看功能
          this.enableFeatureDetailView();
          
        } catch (error) {
          console.error('❌ [AppMaplibre] 地图加载后初始化失败:', error);
        }
      })

      // 设置地图点击事件监听器（移除原来的简单调试代码）
      // const clickHandler = (e: any) => {
      //   console.log(e);
      //   console.log(e.lngLat);
      //   console.log(map.getZoom())
      // };
      
      // map.on('click', clickHandler);
      
      // 记录事件监听器
      // this._addEventListener('click', clickHandler);
      
      // draw.defaultMode = 'draw-polygon'
      // this._draw = draw
      
      console.log('✅ [AppMaplibre] 地图实例创建完成');
      return map;
      
    } catch (error) {
      console.error('❌ [AppMaplibre] 初始化地图失败:', error);
      this._isDestroyed = true;
      throw error;
    }
  }

  /**
   * @description 添加事件监听器记录
   * @param eventType 事件类型
   * @param handler 事件处理函数
   */
  private static _addEventListener(eventType: string, handler: Function): void {
    if (!this._eventListeners.has(eventType)) {
      this._eventListeners.set(eventType, []);
    }
    this._eventListeners.get(eventType)!.push(handler);
  }

  /**
   * @description 清理所有事件监听器
   */
  private static _cleanupEventListeners(): void {
    try {
      console.log('🧹 [AppMaplibre] 清理事件监听器...');
      
      // 禁用要素详情查看功能
      this.disableFeatureDetailView();
      
      if (this._map) {
        // 清理所有记录的事件监听器
        for (const [eventType, handlers] of this._eventListeners) {
          for (const handler of handlers) {
            try {
              // MapLibre的off方法需要具体的处理函数引用
              // 这里我们使用removeAllListeners的方式
              this._map.off(eventType as any, handler as any);
            } catch (e) {
              console.warn(`移除${eventType}事件监听器时出错:`, e);
            }
          }
        }
      }
      
      // 清空事件监听器记录
      this._eventListeners.clear();
      
      console.log('✅ [AppMaplibre] 事件监听器清理完成');
      
    } catch (error) {
      console.error('❌ [AppMaplibre] 清理事件监听器失败:', error);
      // 强制清空记录
      this._eventListeners.clear();
    }
  }

  /**
   * @description 获取当前地图实例
   * @returns {GlMap} 返回地图实例对象
   * @throws {Error} 当地图未初始化时抛出错误
   * @example
   * ```typescript
   * const map = AppMaplibre.getMap();
   * map.flyTo({ center: [lng, lat], zoom: 15 });
   * ```
   */
  static getMap () {
    if (this._isDestroyed) {
      throw new Error('AppMaplibre实例已销毁');
    }
    
    if (!this._map) {
      // throw new DeveloperError("当前Viewer还没有初始化！");
      console.log('当前map还没有初始化！')
      throw new Error('当前map还没有初始化！')
    }
    return this._map
  }

  /**
   * @description 回到默认中心点位置
   * @details 将地图视角移动到默认的中心点和缩放级别
   * @example
   * ```typescript
   * AppMaplibre.goToHome();
   * ```
   */
  static goToHome() {
    if (this._isDestroyed) {
      console.warn('⚠️ [AppMaplibre] 实例已销毁，无法执行goToHome');
      return;
    }

    console.log(this.getMap().getCenter());
    console.log(this.getMap().getZoom());
    this.getMap().flyTo({
      center: this._center,
      zoom: this._zoom,
    })
  }

  /**
   * @description 复位到沙湾区范围
   * @details 将地图视角调整到四川省乐山市沙湾区的完整地理范围
   * @example
   * ```typescript
   * AppMaplibre.resetRegion();
   * ```
   */
  static resetRegion() {
    if (this._isDestroyed) {
      console.warn('⚠️ [AppMaplibre] 实例已销毁，无法执行resetRegion');
      return;
    }

    this.getMap().fitBounds(this._bounds)
  }

  /**
   * @description 等待地图加载完成
   * @returns {Promise<GlMap>} 返回Promise，resolve时传入地图实例
   * @details 如果地图已加载则立即resolve，否则等待load事件
   * @example
   * ```typescript
   * const map = await AppMaplibre.mapReady();
   * // 地图已完全加载，可以安全地进行操作
   * ```
   */
  static async mapReady () {
    if (this._isDestroyed) {
      throw new Error('AppMaplibre实例已销毁');
    }

    return new Promise<GlMap>((resolve: any) => {
      if (this.getMap().loaded()) {
        resolve(this.getMap())
        return
      }
      this.getMap().on('load', () => {
        resolve(this.getMap())
      })
    })
  }

  /**
   * @description 初始化基础图层管理器
   * @description 在地图加载完成后自动调用
   */
  static async initBaseLayerManager(): Promise<void> {
    if (this._isDestroyed) {
      console.warn('⚠️ [AppMaplibre] 实例已销毁，无法初始化基础图层管理器');
      return;
    }

    try {
      console.log('开始初始化基础图层管理器...')
      
      if (!this._map) {
        console.warn('地图实例不存在，跳过基础图层管理器初始化')
        return
      }

      // 创建基础图层管理器实例
      const options: BaseLayerManagerOptions = {
        map: this._map,
        tiandituToken: 'b254904598a72fd14661de55fa70511d', // 可以从环境变量获取
        debug: false
      }

      this._baseLayerManager = new BaseLayerManager(options)
      
      // 初始化管理器
      await this._baseLayerManager.initialize()
      
      // 设置默认底图
      await this._baseLayerManager.setDefaultBasemap()
      
      console.log('基础图层管理器初始化完成')
      
    } catch (error) {
      console.error('基础图层管理器初始化失败:', error)
      // 不抛出错误，让应用继续运行
    }
  }

  /**
   * @description 获取基础图层管理器实例
   * @returns 基础图层管理器实例
   * @throws {Error} 当地图未初始化时抛出错误
   */
  static getBaseLayerManager(): BaseLayerManager {
    if (this._isDestroyed) {
      throw new Error('AppMaplibre实例已销毁');
    }
    
    if (!this._map) {
      throw new Error('地图实例未初始化，无法获取基础图层管理器')
    }
    
    if (!this._baseLayerManager) {
      throw new Error('基础图层管理器未初始化')
    }
    
    return this._baseLayerManager
  }

  /**
   * @description 切换底图
   * @param basemapId - 底图ID
   */
  static async switchBasemap(basemapId: string): Promise<void> {
    if (this._isDestroyed) {
      throw new Error('AppMaplibre实例已销毁');
    }

    if (!this._baseLayerManager) {
      throw new Error('基础图层管理器未初始化')
    }

    const allBasemaps = this._baseLayerManager.getAllBasemaps()
    const targetBasemap = allBasemaps.find(basemap => basemap.id === basemapId)
    
    if (!targetBasemap) {
      throw new Error(`找不到底图: ${basemapId}`)
    }

    await this._baseLayerManager.switchBasemap(targetBasemap)
  }

  /**
   * @description 获取当前底图
   */
  static getCurrentBasemap(): BasemapConfig | null {
    if (this._isDestroyed) {
      return null;
    }
    
    return this._baseLayerManager?.getCurrentBasemap() || null
  }

  /**
   * @description 获取所有可用底图
   */
  static getAllBasemaps(): BasemapConfig[] {
    if (this._isDestroyed) {
      return [];
    }
    
    return this._baseLayerManager?.getAllBasemaps() || []
  }

  /**
   * @description 获取测量工具实例
   * @returns 测量工具实例
   * @throws {Error} 当地图未初始化时抛出错误
   * @example
   * ```typescript
   * const measureTool = AppMaplibre.getMeasureTool();
   * await measureTool.measureDistance('measure-layer-1');
   * ```
   */
  static getMeasureTool (): MeasureTool {
    if (this._isDestroyed) {
      throw new Error('AppMaplibre实例已销毁');
    }
    
    if (!this._map) {
      throw new Error('地图实例未初始化，无法创建测量工具')
    }
    
    if (!this._measureTool) {
      this._measureTool = new MeasureTool(this._map, {
        enabled: true,
        lineColor: '#ff0000',
        fillColor: '#ff0000',
        lineWidth: 2,
        pointRadius: 3.5
      })
    }
    
    return this._measureTool
  }

  /** 
   * @description 获取绘制工具实例
   * @returns 绘制工具实例
   * @throws {Error} 当地图未初始化时抛出错误
   * @example
   * ```typescript
   * const drawTool = AppMaplibre.getDrawTool();
   * await drawTool.start(); // 确保地图加载完成后启动
   * ```
   */
  static getDrawTool (): DrawManager {
    if (this._isDestroyed) {
      throw new Error('AppMaplibre实例已销毁');
    }
    
    if (!this._map) {
      throw new Error('地图实例未初始化，无法创建绘制工具') 
    }
    
    if (!this._drawTool) {
      this._drawTool = createDrawManager(this._map, {
        config: {
          enabled: false // 创建时不自动启动，需要手动调用 start()
        }
      })
    }
    
    return this._drawTool
  }

  /**
   * @description 重新创建绘制工具实例
   * @description 用于解决Terra Draw状态异常时的恢复
   * @throws {Error} 当地图未初始化时抛出错误
   */
  static recreateDrawTool(): void {
    if (this._isDestroyed) {
      throw new Error('AppMaplibre实例已销毁');
    }
    
    if (!this._map) {
      throw new Error('地图实例未初始化，无法重新创建绘制工具');
    }
    
    try {
      // 销毁现有的绘制工具实例
      if (this._drawTool) {
        this._drawTool.destroy();
        console.log('已销毁现有绘制工具实例');
      }
      
      // 创建新的绘制工具实例
      this._drawTool = createDrawManager(this._map);
      console.log('绘制工具实例已重新创建');
    } catch (error) {
      console.error('重新创建绘制工具失败:', error);
      throw error;
    }
  }

  /**
   * @description 完整的实例清理
   * @details 清理所有管理的实例和状态
   */
  private static _performFullCleanup(): void {
    if (this._isDestroyed) {
      console.log('⚠️ [AppMaplibre] 实例已清理，跳过重复清理');
      return;
    }

    console.log('🧹 [AppMaplibre] 开始执行完整清理...');
    
    // 标记开始清理（但不立即设置为destroyed，让resetInstance来决定最终状态）
    this._initialized = false;
    this.mapCreated = false;
    
    try {
      // 清理所有管理的工具实例
      this._cleanupManagedInstances();
      
      // 清理事件监听器
      this._cleanupEventListeners();
      
      // 清理地图实例
      this._cleanupMap();
      
      console.log('✅ [AppMaplibre] 完整清理完成');
      
    } catch (error) {
      console.error('❌ [AppMaplibre] 完整清理失败:', error);
      
      // 不在此处设置_isDestroyed状态，让调用方决定
      // 这样可以避免在resetInstance场景下状态被错误锁定
      console.warn('⚠️ [AppMaplibre] 清理过程出现异常，但将继续状态重置流程');
    }
  }

  /**
   * @description 清理管理的工具实例
   */
  private static _cleanupManagedInstances(): void {
    try {
      console.log('🧹 [AppMaplibre] 清理管理的工具实例...');
      
      // 清理测量工具 - 使用缓存引用防止竞争条件
      const measureTool = this._measureTool;
      this._measureTool = undefined; // 立即重置避免重复访问
      
      if (measureTool) {
        try {
          // 先尝试清理测量数据
          if (measureTool && typeof measureTool.clearMeasureAll === 'function') {
            measureTool.clearMeasureAll();
          }
          // 然后尝试销毁实例
          if (measureTool && typeof (measureTool as any).destroy === 'function') {
            (measureTool as any).destroy();
          }
          console.log('✅ [AppMaplibre] 测量工具清理完成');
        } catch (e) {
          console.warn('⚠️ [AppMaplibre] 清理测量工具时出错:', e);
        }
      }
      
      // 清理绘制工具 - 使用缓存引用防止竞争条件
      const drawTool = this._drawTool;
      this._drawTool = undefined as any; // 立即重置避免重复访问
      
      if (drawTool) {
        try {
          if (drawTool && typeof drawTool.destroy === 'function') {
            drawTool.destroy();
            console.log('✅ [AppMaplibre] 绘制工具清理完成');
          } else {
            console.warn('⚠️ [AppMaplibre] 绘制工具没有destroy方法');
          }
        } catch (e) {
          console.warn('⚠️ [AppMaplibre] 清理绘制工具时出错:', e);
        }
      }
      
      // 清理基础图层管理器 - 使用缓存引用防止竞争条件
      const baseLayerManager = this._baseLayerManager;
      this._baseLayerManager = undefined; // 立即重置避免重复访问
      
      if (baseLayerManager) {
        try {
          if (baseLayerManager && typeof baseLayerManager.destroy === 'function') {
            baseLayerManager.destroy();
            console.log('✅ [AppMaplibre] 基础图层管理器清理完成');
          } else {
            console.warn('⚠️ [AppMaplibre] 基础图层管理器没有destroy方法');
          }
        } catch (e) {
          console.warn('⚠️ [AppMaplibre] 清理基础图层管理器时出错:', e);
        }
      }
      
      // 清理图层管理器 - 使用缓存引用防止竞争条件
      const layerManager = this._layerManager;
      this._layerManager = undefined; // 立即重置避免重复访问
      
      if (layerManager) {
        try {
          if (layerManager && typeof (layerManager as any).destroy === 'function') {
            (layerManager as any).destroy();
            console.log('✅ [AppMaplibre] 图层管理器清理完成');
          } else {
            console.warn('⚠️ [AppMaplibre] 图层管理器没有destroy方法');
          }
        } catch (e) {
          console.warn('⚠️ [AppMaplibre] 清理图层管理器时出错:', e);
        }
      }
      
      console.log('✅ [AppMaplibre] 管理的工具实例清理完成');
      
    } catch (error) {
      console.error('❌ [AppMaplibre] 清理管理的工具实例失败:', error);
      
      // 最终保险：强制重置所有引用
      this._measureTool = undefined;
      this._drawTool = undefined as any;
      this._baseLayerManager = undefined;
      this._layerManager = undefined;
    }
  }

  /**
   * @description 清理地图实例
   */
  private static _cleanupMap(): void {
    try {
      if (this._map && typeof this._map.remove === 'function') {
        console.log('🗑️ [AppMaplibre] 清理地图实例...');
        
        // 清理地图图层和数据源
        try {
          // 清理所有图层
          const layers = this._map.getStyle()?.layers || [];
          layers.forEach(layer => {
            try {
              this._map.removeLayer(layer.id);
            } catch (e) {
              // 图层可能已被移除
            }
          });
          
          // 清理所有数据源
          const sources = this._map.getStyle()?.sources || {};
          Object.keys(sources).forEach(sourceId => {
            try {
              this._map.removeSource(sourceId);
            } catch (e) {
              // 数据源可能已被移除
            }
          });
        } catch (error) {
          console.warn('清理图层和数据源时出错:', error);
        }
        
        // 销毁地图实例
        try {
          this._map.remove();
        } catch (e) {
          console.warn('销毁地图实例时出错:', e);
        }
        
        console.log('✅ [AppMaplibre] 地图实例清理完成');
      }
      
      this._map = undefined as any;
      
    } catch (error) {
      console.error('❌ [AppMaplibre] 清理地图实例失败:', error);
      // 强制重置引用
      this._map = undefined as any;
    }
  }

  /**
   * @description 销毁当前地图实例
   * @details 清理地图资源，重置相关状态标志
   * @example
   * ```typescript
   * AppMaplibre.destroy();
   * ```
   */
  static destroy () {
    console.log('🗑️ [AppMaplibre] 公共销毁方法被调用');
    this._performFullCleanup();
  }

  /**
   * @description 重置实例状态
   * @details 清理当前状态但保持实例可用
   */
  static reset(): void {
    console.log('🔄 [AppMaplibre] 重置实例状态...');
    
    try {
      // 清理管理的实例，但不销毁整个AppMaplibre
      this._cleanupManagedInstances();
      
      // 清理事件监听器
      this._cleanupEventListeners();
      
      // 重置状态标志
      this._initialized = false;
      this._isDestroyed = false;
      this.mapCreated = false;
      
      // 保留地图引用，但清理其内容
      if (this._map) {
        try {
          // 清理图层和数据源
          const layers = this._map.getStyle()?.layers || [];
          layers.forEach(layer => {
            try {
              this._map.removeLayer(layer.id);
            } catch (e) {
              // 图层可能已被移除
            }
          });
          
          const sources = this._map.getStyle()?.sources || {};
          Object.keys(sources).forEach(sourceId => {
            try {
              this._map.removeSource(sourceId);
            } catch (e) {
              // 数据源可能已被移除
            }
          });
        } catch (e) {
          console.warn('重置地图内容时出错:', e);
        }
      }
      
      console.log('✅ [AppMaplibre] 实例状态重置完成');
      
    } catch (error) {
      console.error('❌ [AppMaplibre] 重置实例状态失败:', error);
    }
  }

  static getLayerManager () {
    if (this._isDestroyed) {
      throw new Error('AppMaplibre实例已销毁');
    }

    if (!this._layerManager) {
      this._layerManager = new LayerManager(this.getMap())
    }
    return this._layerManager
  }

  /**
   * @description 将瓦片坐标转换为QuadKey格式
   * @param {number} tileX - 瓦片X坐标
   * @param {number} tileY - 瓦片Y坐标
   * @param {any} levelOfDetail - 缩放级别
   * @returns {string} 返回QuadKey字符串
   * @details 用于某些瓦片服务的URL构建，将XYZ格式转换为QuadKey格式
   */
  static TileXYToQuadKey (tileX: number, tileY: number, levelOfDetail: any) {
    let quadKey = ''
    for (let i = levelOfDetail; i > 0; i--) {
      let digit = 0
      const mask = 1 << (i - 1)
      if ((tileX & mask) !== 0) {
        digit++
      }
      if ((tileY & mask) !== 0) {
        digit++
        digit++
      }
      quadKey += digit
    }
    return quadKey
  }

  /**
   * @description 启用要素详情查看功能
   */
  static enableFeatureDetailView(): void {
    try {
      if (!this._map) {
        console.warn('地图实例不可用，无法启用要素详情查看功能');
        return;
      }

      if (this._featureDetailEnabled) {
        console.log('要素详情查看功能已启用');
        return;
      }

      // 创建点击事件处理器
      const clickHandler = (e: any) => {
        this.handleFeatureDetailClick(e);
      };

      // 保存处理器引用
      this._detailClickHandler = clickHandler;

      // 绑定点击事件
      this._map.on('click', clickHandler);
      this._addEventListener('click', clickHandler);

      // 设置启用状态
      this._featureDetailEnabled = true;

      console.log('✅ [AppMaplibre] 要素详情查看功能已启用');
    } catch (error) {
      console.error('❌ [AppMaplibre] 启用要素详情查看功能失败:', error);
    }
  }

  /**
   * @description 禁用要素详情查看功能
   */
  static disableFeatureDetailView(): void {
    try {
      if (this._map && this._detailClickHandler) {
        this._map.off('click', this._detailClickHandler as any);
        this._detailClickHandler = null;
      }

      this._featureDetailEnabled = false;
      console.log('✅ [AppMaplibre] 要素详情查看功能已禁用');
    } catch (error) {
      console.error('❌ [AppMaplibre] 禁用要素详情查看功能失败:', error);
    }
  }

  /**
   * @description 处理要素详情查看点击
   */
  static async handleFeatureDetailClick(e: any): Promise<void> {
    //在使用某些功能时，需要禁用掉详情点击
    const detailDialogEnable = useDialogStore().detailDialogEnable;
    if (!detailDialogEnable) return;
    
    if (!this._map) {
      console.warn('地图实例不可用，无法处理要素详情查看点击');
      return;
    }

    try {
      console.log('🔍 要素详情查看点击:', { point: e.point, lngLat: e.lngLat });

      const offset = 10; // 点击容差
      const allFeatures = this._map.queryRenderedFeatures([
        [e.point.x - offset / 2, e.point.y - offset / 2],
        [e.point.x + offset / 2, e.point.y + offset / 2],
      ]);

      console.log('🔍 要素详情查看点击:', allFeatures);
      // 按优先级查询要素：先管点，后管线
      // 1. 查询管点要素
      const nodeFeatures = this._map.queryRenderedFeatures([
        [e.point.x - offset / 2, e.point.y - offset / 2],
        [e.point.x + offset / 2, e.point.y + offset / 2],
      ], {
        layers: ['mvt_pipeNode'], // 管点图层ID
      });

      if (nodeFeatures.length > 0) {
        const nodeFeature = nodeFeatures[0];
        await this.handleViewNodeDetail(nodeFeature.properties);
        return;
      }

      // 2. 查询管线要素
      const lineFeatures = this._map.queryRenderedFeatures([
        [e.point.x - offset / 2, e.point.y - offset / 2],
        [e.point.x + offset / 2, e.point.y + offset / 2],
      ], {
        layers: ['mvt_pipeLine'], // 管线图层ID
      });

      if (lineFeatures.length > 0) {
        const lineFeature = lineFeatures[0];
        await this.handleViewLineDetail(lineFeature.properties);
        return;
      }

      // 没有点击到任何要素
      console.log('未点击到管网要素');
    } catch (error) {
      console.error('❌ 处理要素详情查看失败:', error);
      ElMessage.error('查看要素详情失败');
    }
  }

  /**
   * @description 处理查看管点详情
   */
  static async handleViewNodeDetail(nodeData: any): Promise<void> {
    try {
      if (!nodeData || !nodeData.gid) {
        ElMessage.warning('管点数据不完整，无法查看详情');
        return;
      }

      console.log('👁️ 查看管点详情:', nodeData);

      // 查询管点详情数据
      const nodeDetail = await this.queryNodeDetail(nodeData.gid);
      if (!nodeDetail) {
        ElMessage.error('获取管点详情失败');
        return;
      }

      // 使用Dialogs Store打开管点详情面板
      const { useDialogStore } = await import('@/stores/Dialogs');
      const dialogStore = useDialogStore();
      dialogStore.closeDialog('PipeNodePanel');
      dialogStore.closeDialog('PipeLinePanel');
      dialogStore.addDialog({
        name: '管点详情',
        path: 'PipeNodePanel',
        type: 'maplibre',
        params: {
          pipeNode: nodeDetail,
          nodeGid: nodeDetail.gid,
          isNew: false,
          readonly: true,
          isViewingDetail: true,
          coordinates: nodeDetail.longitude && nodeDetail.latitude ? 
            [nodeDetail.longitude, nodeDetail.latitude] : undefined,
          position: {
            right: '120px',
            top: '75px'
          }
        }
      });

      console.log('✅ 管点详情面板已打开（查看模式）');
    } catch (error) {
      console.error('❌ 查看管点详情失败:', error);
      ElMessage.error(`查看管点详情失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * @description 处理查看管线详情
   */
  static async handleViewLineDetail(lineData: any): Promise<void> {
    try {
      if (!lineData || !lineData.gid) {
        ElMessage.warning('管线数据不完整，无法查看详情');
        return;
      }

      console.log('👁️ 查看管线详情:', lineData);

      // 查询管线详情数据
      const lineDetailResponse = await pipeLineDetail(lineData.gid);
      if (lineDetailResponse.code !== 200 || !lineDetailResponse.data) {
        throw new Error(lineDetailResponse.msg || '获取管线详情失败');
      }

      // 使用Dialogs Store打开管线详情面板
      const { useDialogStore } = await import('@/stores/Dialogs');
      const dialogStore = useDialogStore();
      dialogStore.closeDialog('PipeNodePanel');
      dialogStore.closeDialog('PipeLinePanel');
      dialogStore.addDialog({
        name: '管线详情',
        path: 'PipeLinePanel',
        type: 'maplibre',
        params: {
          lineGid: lineDetailResponse.data.gid,
          lineData: lineDetailResponse.data,
          isNew: false,
          readonly: true,
          isViewingDetail: true,
          editMode: 'viewing',
          position: {
            right: '120px',
            top: '75px'
          }
        }
      });

      console.log('✅ 管线详情面板已打开（查看模式）');
    } catch (error) {
      console.error('❌ 查看管线详情失败:', error);
      ElMessage.error(`查看管线详情失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * @description 查询管点详情
   */
  static async queryNodeDetail(nodeGid: string): Promise<any | null> {
    try {
      const response = await pipeNodeDetail(nodeGid);
      if (response.code === 200 && response.data) {
        return response.data;
      } else {
        console.error('查询管点详情失败:', response.msg);
        return null;
      }
    } catch (error) {
      console.error('查询管点详情异常:', error);
      return null;
    }
  }
}
