 <template>
  <div class="bg-#F5F5F5 h-full overflow-auto">
    <Header />
    <div class="bg">
      <div class="title">
        二三维空间数据管理、可视化展示、智能分析、服务共享
      </div>
      <div class="top">
        <div v-for="(item, index) in topList" :key="index" class="item">
          <div>
            <img :src="item.icon" alt="" />
          </div>
          <div class="ml-4">
            <div class="name">{{ item.name }}</div>
            <div class="value">
              <span>{{ item.value }}</span>
              <span>{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="box-border px-25">
      <div grid="~ cols-4 gap-x-9" class="w-full">
        <div
          v-for="item in menuList"
          :key="item.name"
          class="menu-item cursor-pointer"
          @click="menuClick(item)"
        >
          <img
            :src="getImages('gateway/' + item.icon + '.png')"
            class="w-20 h-20"
            alt=""
          />
          <div class="name">{{ item.label }}</div>
          <div>
            <img :src="getImages('gateway/15.png')" alt="" />
          </div>
        </div>
      </div>
      <div
        class="bg-#fff mt-5 flex items-center justify-between box-border px-10 py-2.5 mb-5 ht rounded-3"
      >
        <div v-for="(item, index) in btmList" :key="item.name" class="btm-item">
          <div>
            <img :src="item.icon" alt="" srcset="" />
          </div>
          <div class="color-#232A3A mb-2 font-size-4.5">{{ item.name }}</div>
          <div class="color-#5C637C font-size-4">{{ item.content }}</div>
          <div class="line" :class="index === 3 ? 'border-none' : ''"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Header from "./Header.vue";
import { getImages } from "@/utils/getImages";
import { btmList } from "./data";
import { addLog } from "@/api/log";
import { userCurrent } from "@/api/user";
import router from "@/router";
import localCache from "@/utils/auth";
import { queryServiceStat } from "@/api/analysis";
const topList = ref([
  {
    name: "服务数量",
    value: "261",
    unit: "个",
    icon: getImages("gateway/2.png"),
  },
  {
    name: "总访问次数",
    value: "261",
    unit: "次",
    icon: getImages("gateway/3.png"),
  },
]);
const menuStore = useMenuStore();
const { menu } = storeToRefs(menuStore);
const menuList = ref<any>([]);
// const menu
const menuClick = async (item: any) => {
  const result = await addLog({
    // operName: "ls_8345041408308248576",
    operName: localCache.getCache("code") ?? "",
    title: item.component,
    businessType: "6",
  });
  if (result.code === 200) {
    if (item.component === "gis_information_service") {
      // window.open("http://36.138.75.137:8077/home/<USER>");
      const token = localCache.getCache("Latias") ?? "";
      if (token.indexOf("Bearer") !== -1) {
        window.open(`http://36.138.75.137:8077/home?token=${token}`);
      } else {
        window.location.href = "https://engine.lshywater.cn/#/login";
      }
    } else {
      router.push(item.path);
    }
  } else {
    ElMessage.error(result.msg);
  }
};
const dataInfo = ref();
const getUser = async () => {
  const result = await userCurrent({
    token: localCache.getCache("Latias") ?? "",
  });
  if (result.code === 200) {
    menuList.value = result.data.menus;
    await menuStore.getMenu();
    localCache.setCache("Name", result.data.name);
    localCache.setCache("Photo", result.data.photo);
    localCache.setCache("code", result.data.code);
  }
  // localCache.getCache('Latias')
};
const getStat = async () => {
  const result = await queryServiceStat();
  // console.log(result);
  // dataInfo.value = result
  topList.value[0].value = result.total;
  topList.value[1].value = result.visited;
};
onMounted(() => {
  getUser();
  getStat();
});
</script>
<style lang="scss" scoped>
.bg {
  width: 100%;
  // height: calc(100vh - 630px);
  height: 320px;
  // height: 460px;
  background: url(@/assets/images/gateway/1.png) no-repeat;
  background-size: 100% 100%;
  // padding: 0 40px;
  padding-left: 120px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 20px;
  .title {
    font-size: 34px;
    font-weight: 600;
    width: 869px;
    color: #6684b3;
  }
  .top {
    display: flex;
    margin-top: 60px;
  }

  .name {
    font-size: 18px;
    font-weight: 500;
    color: #5c637c;
  }
  .value {
    color: #1966ff;
    font-size: 30px;
    font-weight: 600;
  }
  .item {
    display: flex;
    align-items: center;
    margin-right: 100px;
  }
}
.menu-item {
  width: 400px;
  height: 210px;
  background: url(@/assets/images/gateway/6.png) no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 16px;
  color: #232a3a;
  .name {
    font: 600 22px SansCN-Regular;
    margin: 16px 0 6px 0;
  }
  &:active {
    border: 1px solid #1966ff;
    border-radius: 6px;
    color: #1966ff;
  }
  &:hover {
    border: 1px solid #1966ff;
    border-radius: 6px;
    color: #1966ff;
  }
}
.ht {
  // height: 300px;
  height: calc(100vh - 650px);
}
.btm-item {
  width: 360px;
  text-align: center;
  box-sizing: border-box;
  position: relative;
}
.line {
  height: 100%;
  border-right: 2px solid #e8ecf2;
  position: absolute;
  top: 0;
  right: -30px;
}
</style>
