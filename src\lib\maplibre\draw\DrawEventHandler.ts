/**
 * @fileoverview MapLibre 绘制工具事件处理器
 * @description 处理 Terra Draw 的各种事件，提供统一的事件管理接口
 * <AUTHOR>
 * @version 1.0.0
 */

import type { TerraDraw } from 'terra-draw';
import { 
  DrawEventType, 
  type DrawEventData, 
  type DrawEventCallback, 
  type DrawMode 
} from './types';

/**
 * @description 绘制事件处理器类
 */
export class DrawEventHandler {
  private terraDraw: TerraDraw;
  private eventCallbacks: Map<DrawEventType, Set<DrawEventCallback>> = new Map();
  private isListening: boolean = false;

  /**
   * @constructor
   * @param terraDraw - Terra Draw 实例
   */
  constructor(terraDraw: TerraDraw) {
    this.terraDraw = terraDraw;
    this.initializeEventTypes();
  }

  /**
   * @description 初始化事件类型映射
   * @private
   */
  private initializeEventTypes(): void {
    // 初始化所有事件类型的回调集合
    Object.values(DrawEventType).forEach(eventType => {
      this.eventCallbacks.set(eventType, new Set());
    });
  }

  /**
   * @description 开始监听 Terra Draw 事件
   */
  startListening(): void {
    if (this.isListening) {
      console.warn('绘制事件处理器已在监听中');
      return;
    }

    try {
      // 监听绘制完成事件
      this.terraDraw.on('finish', this.handleDrawFinish.bind(this));
      
      // 监听要素选择事件
      this.terraDraw.on('select', this.handleFeatureSelect.bind(this));
      
      // 监听要素取消选择事件
      this.terraDraw.on('deselect', this.handleFeatureDeselect.bind(this));
      
      // 监听要素更新事件
      this.terraDraw.on('change', this.handleFeatureUpdate.bind(this));

      this.isListening = true;
      console.log('绘制事件处理器开始监听');
    } catch (error) {
      console.error('启动绘制事件监听失败:', error);
      throw error;
    }
  }

  /**
   * @description 停止监听 Terra Draw 事件
   */
  stopListening(): void {
    if (!this.isListening) {
      console.warn('绘制事件处理器未在监听中');
      return;
    }

    try {
      // 移除所有事件监听器
      this.terraDraw.off('finish', this.handleDrawFinish.bind(this));
      this.terraDraw.off('select', this.handleFeatureSelect.bind(this));
      this.terraDraw.off('deselect', this.handleFeatureDeselect.bind(this));
      this.terraDraw.off('change', this.handleFeatureUpdate.bind(this));

      this.isListening = false;
      console.log('绘制事件处理器停止监听');
    } catch (error) {
      console.error('停止绘制事件监听失败:', error);
    }
  }

  /**
   * @description 添加事件回调
   * @param eventType - 事件类型
   * @param callback - 回调函数
   */
  addEventListener(eventType: DrawEventType, callback: DrawEventCallback): void {
    const callbacks = this.eventCallbacks.get(eventType);
    if (callbacks) {
      callbacks.add(callback);
      console.log(`添加事件监听器: ${eventType}`);
    } else {
      console.warn(`未知的事件类型: ${eventType}`);
    }
  }

  /**
   * @description 移除事件回调
   * @param eventType - 事件类型
   * @param callback - 回调函数
   */
  removeEventListener(eventType: DrawEventType, callback: DrawEventCallback): void {
    const callbacks = this.eventCallbacks.get(eventType);
    if (callbacks) {
      callbacks.delete(callback);
      console.log(`移除事件监听器: ${eventType}`);
    } else {
      console.warn(`未知的事件类型: ${eventType}`);
    }
  }

  /**
   * @description 移除所有事件回调
   * @param eventType - 事件类型（可选，不传则移除所有类型的回调）
   */
  removeAllEventListeners(eventType?: DrawEventType): void {
    if (eventType) {
      const callbacks = this.eventCallbacks.get(eventType);
      if (callbacks) {
        callbacks.clear();
        console.log(`清除所有 ${eventType} 事件监听器`);
      }
    } else {
      this.eventCallbacks.forEach((callbacks) => callbacks.clear());
      console.log('清除所有事件监听器');
    }
  }

  /**
   * @description 触发事件回调
   * @param eventData - 事件数据
   * @private
   */
  private triggerEvent(eventData: DrawEventData): void {
    const callbacks = this.eventCallbacks.get(eventData.type);
    if (callbacks && callbacks.size > 0) {
      callbacks.forEach(callback => {
        try {
          callback(eventData);
        } catch (error) {
          console.error(`事件回调执行失败 (${eventData.type}):`, error);
        }
      });
    }
  }

  /**
   * @description 处理绘制开始事件
   * @param mode - 当前绘制模式
   */
  handleDrawStart(mode: string): void {
    console.log('绘制开始:', mode);
    this.triggerEvent({
      type: DrawEventType.DRAW_START,
      mode: this.mapTerraDrawModeToDrawMode(mode),
      data: { mode }
    });
  }

  /**
   * @description 处理绘制完成事件
   * @param featureId - Terra Draw 传递的要素ID（string或number）
   * @private
   */
  private handleDrawFinish(featureId: string | number): void {
    console.log('绘制完成，要素ID:', featureId);
    
    try {
      // Terra Draw的finish事件只传递ID字符串，需要通过getSnapshot获取实际要素
      const allFeatures = this.terraDraw.getSnapshot();
      
      // 根据ID找到对应的要素
      const completedFeature = allFeatures.find(feature => feature.id === featureId);
      
      if (completedFeature) {
        console.log('找到完成的要素:', completedFeature);
        this.triggerEvent({
          type: DrawEventType.DRAW_FINISH,
          features: [completedFeature],
          data: { featureId, feature: completedFeature }
        });
      } else {
        console.warn('未找到对应的要素，ID:', featureId);
        // 触发事件但不包含要素数据
        this.triggerEvent({
          type: DrawEventType.DRAW_FINISH,
          data: { featureId, error: '未找到对应的要素' }
        });
      }
    } catch (error) {
      console.error('处理绘制完成事件失败:', error);
      this.triggerEvent({
        type: DrawEventType.DRAW_FINISH,
        data: { featureId, error: (error as Error).message }
      });
    }
  }

  /**
   * @description 处理绘制取消事件
   * @param mode - 当前绘制模式
   */
  handleDrawCancel(mode: string): void {
    console.log('绘制取消:', mode);
    this.triggerEvent({
      type: DrawEventType.DRAW_CANCEL,
      mode: this.mapTerraDrawModeToDrawMode(mode),
      data: { mode }
    });
  }

  /**
   * @description 处理要素选择事件
   * @param event - Terra Draw 事件数据
   * @private
   */
  private handleFeatureSelect(event: any): void {
    console.log('要素选中:', event);
    this.triggerEvent({
      type: DrawEventType.FEATURE_SELECT,
      features: event.features || [event.feature],
      data: event
    });
  }

  /**
   * @description 处理要素取消选择事件
   * @private
   */
  private handleFeatureDeselect(): void {
    console.log('要素取消选中');
    this.triggerEvent({
      type: DrawEventType.FEATURE_DESELECT,
      data: {}
    });
  }

  /**
   * @description 处理要素更新事件
   * @param event - Terra Draw 事件数据
   * @private
   */
  private handleFeatureUpdate(event: any): void {
    console.log('要素更新:', event);
    this.triggerEvent({
      type: DrawEventType.FEATURE_UPDATE,
      features: event.features || [event.feature],
      data: event
    });
  }

  /**
   * @description 处理模式改变事件
   * @param mode - 新的绘制模式
   */
  handleModeChange(mode: string): void {
    console.log('模式改变:', mode);
    this.triggerEvent({
      type: DrawEventType.MODE_CHANGE,
      mode: this.mapTerraDrawModeToDrawMode(mode),
      data: { mode }
    });
  }

  /**
   * @description 将 Terra Draw 模式映射到自定义绘制模式
   * @param terraDrawMode - Terra Draw 模式字符串
   * @returns 自定义绘制模式
   * @private
   */
  private mapTerraDrawModeToDrawMode(terraDrawMode: string): DrawMode {
    const modeMap: Record<string, DrawMode> = {
      'select': 'select' as DrawMode,
      'point': 'point' as DrawMode,
      'linestring': 'linestring' as DrawMode,
      'polygon': 'polygon' as DrawMode,
      'rectangle': 'rectangle' as DrawMode,
      'circle': 'circle' as DrawMode,
      'freehand': 'freehand' as DrawMode
    };

    return modeMap[terraDrawMode] || ('select' as DrawMode);
  }

  /**
   * @description 获取当前监听状态
   * @returns 是否正在监听
   */
  isCurrentlyListening(): boolean {
    return this.isListening;
  }

  /**
   * @description 销毁事件处理器
   */
  destroy(): void {
    this.stopListening();
    this.removeAllEventListeners();
    console.log('绘制事件处理器已销毁');
  }
} 