<template>
  <custom-card
    :width="'596px'"
    @closeHandler="close"
    v-show="mainItem.show"
    :main-item="mainItem"
    :title="mainItem.title ?? (mainItem.title = '路线管理')"
  >
    <div class="flex items-center">
      <div class="font-size-3.5 color-#5C5F66 w-80px">路线名称:</div>
      <el-input
        v-model="routeName"
        placeholder="请输入路线名称"
        :class="{ 'error-input': nameError }"
        @blur="validateRouteName"
      ></el-input>
    </div>

    <div v-if="nameError" class="error-text">{{ nameError }}</div>
    <div class="my-5">
      <el-button class="btn" @click="handleDrawRoute()">绘 制</el-button>
      <el-button class="btn" @click="handleAddStop()">打 点</el-button>
      <el-button class="btn" @click="handleClearStop()">清 空</el-button>
      <el-button class="btn" @click="handleSaveRoute()">保 存</el-button>
      <el-button
        v-if="currentMode"
        class="btn cancel-btn"
        type="warning"
        @click="handleCancelOperation()"
      >
        取 消
      </el-button>
    </div>
    <div class="route-status">
      <span class="color-#5C5F66 font-size-3.5"
        >锚点数量: {{ routeStops.length }}</span
      >
      <span v-if="currentMode" class="mode-indicator"
        >当前模式: {{ currentMode }}</span
      >
      <div v-if="routeStops.length > 0" class="batch-height-control">
        <el-input
          v-model="batchHeight"
          size="small"
          placeholder="批量高程"
          style="width: 100px; margin-right: 8px"
          @input="handleBatchHeightInput"
        />
        <el-button
          size="small"
          type="primary"
          @click="handleBatchSetHeight"
        >
          批量设置
        </el-button>
      </div>
    </div>
    <el-row class="routeInfo">
      <el-table
        class="routeCt"
         :row-class-name="tableRowClassName"
        :data="routeStops"
        height="310px"
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="60">
        </el-table-column>
        <el-table-column prop="name" label="锚点">
          <template v-slot="scope">
            <div>
              {{ scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="lon"
          label="经度"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="lat"
          label="纬度"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="height" label="高程" width="120">
          <template v-slot="scope">
            <el-input
              v-model="scope.row.height"
              size="small"
              @input="handleHeightInput(scope.row, $event)"
              @change="handleHeightChange(scope.$index, scope.row)"
              @blur="validateHeight(scope.row)"
              placeholder="高程"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="170">
          <template v-slot="scope">
            <el-button
              type="text"
              class="route_oth_btn"
              @click="handleRowClick(scope.row)"
              >定位</el-button
            >
            <el-button
              type="text"
              class="route_oth_btn"
              @click="handleAddStop(scope.$index)"
              >新增</el-button
            >
            <el-button
              class="route_oth_btn"
              type="text"
              @click="handleModifyStop(scope.$index, scope.row)"
              >修改
            </el-button>
            <el-button
              class="route_dlt"
              type="text"
              @click="handleDeleteStop(scope.$index, scope.row)"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
  </custom-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { AppCesium } from "@/lib/cesium/AppCesium";
import { useDialogStore } from "@/stores/Dialogs";
import {
  RouteService,
  RouteEventManager,
  ROUTE_EVENTS,
  type RouteData,
  type RouteStop,
} from "@/utils/routeService";
import { routeAdd, routeEdit } from "@/api/roam";
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});

// === 响应式数据定义 ===
const routeName = ref<string>("");
const routeStops = ref<RouteStop[]>([]);
const nameError = ref<string>("");
const currentMode = ref<string>("");
const isEditMode = ref<boolean>(false);
const currentRouteId = ref<string>("");
const currentEditingIndex = ref<number>(-1);
const lineLayer = ref<any>(null);
const stopLayer = ref<any>(null);
const routeId = ref("");

// 事件监听器管理
const activeEventHandlers = ref<Array<() => void>>([]);

// 批量高程设置
const batchHeight = ref<string>('');
// === 生命周期管理 ===
onMounted(() => {
  console.log("RouteManagerPanel初始化参数:", props.mainItem.params);
  initializeComponent();
});

onUnmounted(() => {
  cleanup();
});

// === 数据监听 ===
watch(
  routeStops,
  (newVal) => {
    updateRouteVisualization(newVal);
  },
  { deep: true }
);

// === 初始化方法 ===
/**
 * @description 初始化组件
 */
const initializeComponent = (): void => {
  initializeLayers();
  initializeRouteData();
};

/**
 * @description 初始化图层
 */
const initializeLayers = (): void => {
  try {
    lineLayer.value = new BC.VectorLayer("route-layer");
    stopLayer.value = new BC.VectorLayer("stop-layer");
    lineLayer.value.addTo(AppCesium.getInstance().getViewer());
    stopLayer.value.addTo(AppCesium.getInstance().getViewer());
  } catch (error) {
    console.error("初始化图层失败:", error);
    ElMessage.error("初始化3D图层失败");
  }
};

/**
 * @description 初始化路线数据
 */
const initializeRouteData = (): void => {
  const params = props.mainItem.params;
  if (params?.type === "edit") {
    isEditMode.value = true;
    loadRouteForEdit(params);
  } else {
    isEditMode.value = false;
    resetRouteData();
  }
};

/**
 * @description 加载编辑模式的路线数据
 */
const loadRouteForEdit = (params: any): void => {
  try {
    const { routeId, routeName: editRouteName, value } = params;

    if (!routeId && !editRouteName) {
      ElMessage.error("编辑模式缺少路线标识");
      return;
    }
    populateRouteData(params);
    displayRouteOnMap(params);

    // 优先使用ID查找，其次使用名称
    // const routeData = routeId
    //   ? RouteService.getRouteById(routeId)
    //   : RouteService.getRouteByName(editRouteName);

    // if (routeData) {
    // populateRouteData(routeData);
    // displayRouteOnMap(routeData);
    // ElMessage.success(`已加载路线: ${routeData.name}`);
    // } else {
    //   ElMessage.error("未找到指定的路线数据");
    // }
  } catch (error) {
    console.error("加载编辑路线失败:", error);
    ElMessage.error("加载路线数据失败");
  }
};

/**
 * @description 填充路线数据到界面
 */
const populateRouteData = (routeData: any): void => {
  currentRouteId.value = routeData.routeId;
  console.log(routeData);
  routeName.value = routeData.name;
  routeStops.value = JSON.parse(routeData.value);
  updateStopIndexes();
};

/**
 * @description 在地图上显示路线
 */
const displayRouteOnMap = (routeData: any): void => {
  try {
    clearMapDisplay();
    const data = JSON.parse(routeData.value);
    // 显示锚点
    data.forEach((stop: any) => {
      const position = new BC.Position(stop.lon, stop.lat, stop.height);
      const point = new BC.Point(position).setStyle({
        color: BC.Color.GREEN,
      });
      point.id = stop.id;
      stopLayer.value.addOverlay(point);
    });

    // 显示线条
    if (data.length > 1) {
      const positions = data.map(
        (stop: any) => new BC.Position(stop.lon, stop.lat, stop.height)
      );
      const line = new BC.Polyline(positions);
      lineLayer.value.addOverlay(line);
    }
  } catch (error) {
    console.error("在地图上显示路线失败:", error);
    ElMessage.error("在地图上显示路线失败");
  }
};

/**
 * @description 重置路线数据
 */
const resetRouteData = (): void => {
  routeName.value = "";
  routeStops.value = [];
  currentRouteId.value = "";
  nameError.value = "";
  currentMode.value = "";
};

/**
 * @description 清理资源
 */
const cleanup = (): void => {
  try {
    // 清理当前模式和状态
    const mode = currentMode.value;
    currentMode.value = "";
    currentEditingIndex.value = -1;

    // 清理地图图层
    lineLayer.value?.remove();
    stopLayer.value?.remove();

    // 清理可能存在的事件监听器和绘制状态
    try {
      // 清理所有活动的事件处理器
      activeEventHandlers.value.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          console.warn("清理单个事件处理器失败:", error);
        }
      });
      activeEventHandlers.value = [];

      // 如果正在绘制，停止绘制
      if (mode === "绘制路线") {
        const plotUtil = AppCesium.getInstance().getPlotUtil();
        if (plotUtil && plotUtil.stop) {
          plotUtil.stop();
        }
      }

      console.log(`清理模式: ${mode}，清理了 ${activeEventHandlers.value.length} 个事件处理器`);
    } catch (error) {
      console.warn("清理事件监听器失败:", error);
    }

    console.log("RouteManagerPanel资源清理完成");
  } catch (error) {
    console.error("清理资源失败:", error);
  }
};

// === 可视化更新方法 ===
/**
 * @description 更新路线可视化
 */
const updateRouteVisualization = (stops: RouteStop[]): void => {
  try {
    const positions = stops.map(
      (stop) => new BC.Position(stop.lon, stop.lat, stop.height)
    );

    if (positions.length > 1) {
      if (lineLayer.value.getOverlays().length === 0) {
        const line = new BC.Polyline(positions);
        lineLayer.value.addOverlay(line);
      } else {
        const overlay = lineLayer.value.getOverlays()[0];
        overlay.positions = positions;
      }
    } else {
      lineLayer.value.clear();
    }
  } catch (error) {
    console.error("更新路线可视化失败:", error);
  }
};

/**
 * @description 清空地图显示
 */
const clearMapDisplay = (): void => {
  try {
    lineLayer.value?.clear();
    stopLayer.value?.clear();
  } catch (error) {
    console.error("清空地图显示失败:", error);
  }
};

// === 工具方法 ===
/**
 * @description 关闭面板
 */
const close = (): void => {
  useDialogStore().closeDialog("RouteManagerPanel");
};

/**
 * @description 验证路线名称
 */
const validateRouteName = (): boolean => {
  const errors = RouteService.validateRouteData({
    name: routeName.value,
    value: routeStops.value,
  });

  const nameErrors = errors.filter((error) => error.includes("路线名称"));
  if (nameErrors.length > 0) {
    nameError.value = nameErrors[0];
    return false;
  }

  // 检查名称重复
  if (
    RouteService.isNameDuplicate(routeName.value.trim(), currentRouteId.value)
  ) {
    nameError.value = "路线名称已存在";
    return false;
  }

  nameError.value = "";
  return true;
};

/**
 * @description 更新锚点序号和名称
 */
const updateStopIndexes = (): void => {
  routeStops.value = RouteService.updateStopIndexes(routeStops.value);
};

// === 核心功能方法 ===
/**
 * @description 处理绘制路线
 */
const handleDrawRoute = (): void => {
  const plotUtil = AppCesium.getInstance().getPlotUtil();
  if (!plotUtil) {
    ElMessage.error("3D场景未准备就绪，请稍后重试");
    return;
  }

  // 检查是否已经在绘制模式中
  if (currentMode.value === "绘制路线") {
    ElMessage.warning("正在绘制路线中，请先完成当前绘制或清空重新开始");
    return;
  }

  // 检查是否在其他模式中
  if (currentMode.value && currentMode.value !== "") {
    ElMessage.warning(`当前正在${currentMode.value}模式中，请先完成当前操作`);
    return;
  }

  try {
    currentMode.value = "绘制路线";
    ElMessage.info("开始绘制路线，点击地图添加路径点，双击结束");
    const { Cesium } = BC.Namespace;

    handleClearStop();

    plotUtil.draw(
      "polyline",
      (overlay: any) => {
        handleDrawComplete(overlay);
      },
      {
        material: Cesium.Color.CYAN.withAlpha(0.8),
        width: 3,
        clampToGround: true,
      },
      true
    );
  } catch (error) {
    console.error("启动路线绘制失败:", error);
    ElMessage.error("启动绘制失败，请检查3D场景状态");
    currentMode.value = "";
  }
};

/**
 * @description 处理绘制完成
 */
const handleDrawComplete = (overlay: any): void => {
  try {
    console.log("Cesium路线绘制完成:", overlay);

    if (!overlay?.positions || overlay.positions.length === 0) {
      ElMessage.warning("绘制路线无效，请重新绘制");
      currentMode.value = "";
      return;
    }

    const stops: RouteStop[] = overlay.positions.map(
      (position: any, index: number) => {
        const point = new BC.Point(
          new BC.Position(position.lng, position.lat, position.alt + 0.2)
        );
        point.setStyle({
          color: BC.Color.GREEN,
        });
        const id = RouteService.generateStopId();
        point.id = id;
        stopLayer.value.addOverlay(point);

        return {
          id: id,
          name: RouteService.generateStopName(index),
          lon: Number(position.lng.toFixed(6)),
          lat: Number(position.lat.toFixed(6)),
          height: Number(position.alt.toFixed(2)),
          index: index,
        };
      }
    );

    routeStops.value = stops;
    currentMode.value = "";

    ElMessage.success(`路线绘制完成，共添加 ${stops.length} 个锚点`);
    console.log("生成的路线锚点:", stops);
  } catch (error) {
    console.error("处理绘制结果失败:", error);
    ElMessage.error("绘制路线失败，请重试");
    currentMode.value = "";
  }
};

/**
 * @description 处理添加锚点
 */
const handleAddStop = (insertIndex?: number): void => {
  try {
    // 检查是否已经在添加锚点模式中
    if (currentMode.value === "添加锚点") {
      ElMessage.warning("正在添加锚点中，请先在地图上点击选择位置");
      return;
    }

    // 检查是否在其他模式中
    if (currentMode.value && currentMode.value !== "") {
      ElMessage.warning(`当前正在${currentMode.value}模式中，请先完成当前操作`);
      return;
    }

    currentMode.value = "添加锚点";
    ElMessage.info("请在3D场景中点击选择锚点位置");

    const handleAddStopByClick = (e: any) => {
      try {
        handleStopAdd(e.wgs84Position, insertIndex);
      } finally {
        // 清理事件处理器引用
        const index = activeEventHandlers.value.indexOf(cleanupHandler);
        if (index > -1) {
          activeEventHandlers.value.splice(index, 1);
        }
      }
    };

    const viewer = AppCesium.getInstance().getViewer();
    viewer.once(BC.MouseEventType.LEFT_CLICK, handleAddStopByClick, this);

    // 保存清理函数的引用
    const cleanupHandler = () => {
      try {
        viewer.off(BC.MouseEventType.LEFT_CLICK, handleAddStopByClick, this);
      } catch (error) {
        console.warn("清理添加锚点事件失败:", error);
      }
    };
    activeEventHandlers.value.push(cleanupHandler);
  } catch (error) {
    console.error("启动点拾取失败:", error);
    ElMessage.error("启动点拾取失败，请检查3D场景状态");
    currentMode.value = "";
  }
};



/**
 * @description 处理锚点添加
 */
const handleStopAdd = (position: any, insertIndex?: number): void => {
  try {
    if (!position) {
      ElMessage.warning("未能获取有效位置，请重新选择");
      currentMode.value = "";
      return;
    }

    const id = RouteService.generateStopId();
    const point = new BC.Point(position).setStyle({
      color: BC.Color.GREEN,
    });
    point.id = id;
    stopLayer.value.addOverlay(point);

    const newStop: RouteStop = {
      id: id,
      name: "",
      lon: Number(position.lng.toFixed(6)),
      lat: Number(position.lat.toFixed(6)),
      height: Number(position.alt.toFixed(2)),
      index: 0,
    };

    if (typeof insertIndex === "number" && insertIndex >= 0) {
      routeStops.value.splice(insertIndex + 1, 0, newStop);
      ElMessage.success(`在锚点${insertIndex + 1}后添加新锚点`);
    } else {
      routeStops.value.push(newStop);
      ElMessage.success("锚点添加成功");
    }

    updateStopIndexes();
    currentMode.value = "";
    console.log("新增锚点:", newStop);
  } catch (error) {
    console.error("处理拾取位置失败:", error);
    ElMessage.error("添加锚点失败，请重试");
    currentMode.value = "";
  }
};

/**
 * @description 处理清空路线
 */
const handleClearStop = (): void => {
  if (routeStops.value.length === 0) {
    return;
  }

  try {
    routeStops.value = [];
    currentMode.value = "";
    currentEditingIndex.value = -1;
    clearMapDisplay();

    ElMessage.success("路线已清空");
    console.log("路线数据已清空");
  } catch (error) {
    console.error("清空路线失败:", error);
    ElMessage.error("清空路线失败");
  }
};

/**
 * @description 处理保存路线
 */
const handleSaveRoute = async () => {
  // 验证路线数据
  const errors = RouteService.validateRouteData({
    name: routeName.value,
    value: routeStops.value,
  });

  if (errors.length > 0) {
    ElMessage.error(errors[0]);
    if (errors[0].includes("路线名称")) {
      nameError.value = errors[0];
    }
    return;
  }

  // 检查名称重复
  // if (
  //   RouteService.isNameDuplicate(routeName.value.trim(), currentRouteId.value)
  // ) {
  //   nameError.value = "路线名称已存在";
  //   ElMessage.error("路线名称已存在");
  //   return;
  // }

  try {
    // const routeData: RouteData = {
    //   id:
    //     isEditMode.value && currentRouteId.value
    //       ? currentRouteId.value
    //       : RouteService.generateRouteId(),
    //   name: routeName.value.trim(),
    //   stops: [...routeStops.value],
    //   geoJson: generateGeoJSON(),
    //   createTime: isEditMode.value ? getOriginalCreateTime() : Date.now(),
    //   modifyTime: Date.now(),
    // };
    const routeData: RouteData = {
      id: currentRouteId.value,
      name: routeName.value.trim(),
      value: JSON.stringify([...routeStops.value] as any) as any,
    };
    if (!isEditMode.value) {
      const result = await routeAdd(routeData);
      if (result.code === 200) {
        const action = isEditMode.value ? "更新" : "保存";
        ElMessage.success(`路线 "${routeData.name}" ${action}成功`);
        useRoamingStore().update();
        useDialogStore().closeDialog('RouteManagerPanel');
      } else {
        ElMessage.error(result.msg);
      }
    } else {
      const result = await routeEdit(routeData);
      if (result.code === 200) {
        const action = isEditMode.value ? "更新" : "保存";
        ElMessage.success(`路线 "${routeData.name}" ${action}成功`);
        useRoamingStore().update();
        useDialogStore().closeDialog('RouteManagerPanel');
      } else {
        ElMessage.error(result.msg);
      }
    }

    // const success = RouteService.saveRoute(routeData);
    // if (success) {
    //   const action = isEditMode.value ? "更新" : "保存";
    //   ElMessage.success(`路线 "${routeData.name}" ${action}成功`);
    //   console.log(`${action}的路线数据:`, routeData);

    //   // 触发保存事件
    //   RouteEventManager.emit(
    //     isEditMode.value
    //       ? ROUTE_EVENTS.ROUTE_UPDATED
    //       : ROUTE_EVENTS.ROUTE_SAVED,
    //     routeData
    //   );
    // } else {
    //   ElMessage.error("保存路线失败");
    // }
  } catch (error) {
    console.error("保存路线失败:", error);
    ElMessage.error("保存路线失败，请重试");
  }
};

/**
 * @description 获取原始创建时间
 */
// const getOriginalCreateTime = (): number => {
//   if (!isEditMode.value || !currentRouteId.value) {
//     return Date.now();
//   }

//   const originalRoute = RouteService.getRouteById(currentRouteId.value);
//   return originalRoute?.createTime || Date.now();
// };

/**
 * @description 处理表格行点击：定位到锚点
 */
const handleRowClick = (row: RouteStop): void => {
  console.log("点击锚点:", row);
  const { Cesium } = BC.Namespace;

  try {
    const viewer = AppCesium.getInstance().getViewer();
    if (viewer) {
      const position = Cesium.Cartesian3.fromDegrees(
        row.lon,
        row.lat,
        row.height + 500
      );

      viewer.camera.flyTo({
        destination: position,
        duration: 1.0,
      });

      ElMessage.info(`已飞行到锚点: ${row.name}`);
    }
  } catch (error) {
    console.error("飞行到锚点失败:", error);
  }
};

/**
 * @description 处理删除锚点
 */
const handleDeleteStop = (index: number, row: RouteStop): void => {
  if (routeStops.value.length <= 2) {
    ElMessage.warning("路线至少需要保留2个锚点");
    return;
  }

  try {
    // 从地图移除锚点
    stopLayer.value.eachOverlay((overlay: any) => {
      if (overlay.id === row.id) {
        stopLayer.value.removeOverlay(overlay);
      }
    });

    // 从数据中移除
    routeStops.value.splice(index, 1);
    updateStopIndexes();

    ElMessage.success(`已删除锚点: ${row.name}`);
    console.log("删除锚点:", row);
  } catch (error) {
    console.error("删除锚点失败:", error);
    ElMessage.error("删除锚点失败");
  }
};

/**
 * @description 处理修改锚点
 */
const handleModifyStop = (index: number, row: RouteStop): void => {
  try {
    // 检查是否已经在修改模式中
    if (currentMode.value.startsWith("修改锚点:")) {
      ElMessage.warning("正在修改锚点中，请先在地图上点击选择新位置");
      return;
    }

    // 检查是否在其他模式中
    if (currentMode.value && currentMode.value !== "") {
      ElMessage.warning(`当前正在${currentMode.value}模式中，请先完成当前操作`);
      return;
    }

    currentMode.value = `修改锚点: ${row.name}`;
    currentEditingIndex.value = index;
    ElMessage.info(`请在3D场景中点击选择 ${row.name} 的新位置`);

    const handleModifyStopByClick = (e: any) => {
      try {
        handleStopModify(e.wgs84Position, index, row);
      } finally {
        // 清理事件处理器引用
        const handlerIndex = activeEventHandlers.value.indexOf(cleanupHandler);
        if (handlerIndex > -1) {
          activeEventHandlers.value.splice(handlerIndex, 1);
        }
      }
    };

    const viewer = AppCesium.getInstance().getViewer();
    viewer.once(BC.MouseEventType.LEFT_CLICK, handleModifyStopByClick, this);

    // 保存清理函数的引用
    const cleanupHandler = () => {
      try {
        viewer.off(BC.MouseEventType.LEFT_CLICK, handleModifyStopByClick, this);
      } catch (error) {
        console.warn("清理修改锚点事件失败:", error);
      }
    };
    activeEventHandlers.value.push(cleanupHandler);
  } catch (error) {
    console.error("启动位置修改失败:", error);
    ElMessage.error("启动位置修改失败，请检查3D场景状态");
    resetEditingState();
  }
};

/**
 * @description 处理锚点修改
 */
const handleStopModify = (
  position: any,
  index: number,
  row: RouteStop
): void => {
  try {
    if (!position) {
      ElMessage.warning("未能获取有效位置，请重新选择");
      resetEditingState();
      return;
    }

    const stop = routeStops.value[index];
    if (stop) {
      stop.lon = Number(position.lng.toFixed(6));
      stop.lat = Number(position.lat.toFixed(6));
      stop.height = Number(position.alt.toFixed(2));

      // 更新地图上的锚点位置
      stopLayer.value.eachOverlay((overlay: any) => {
        if (overlay.id === stop.id) {
          overlay.position = position;
        }
      });

      ElMessage.success(`${stop.name} 位置已更新`);
      console.log("修改锚点:", stop);
    }

    resetEditingState();
  } catch (error) {
    console.error("处理拾取位置失败:", error);
    ElMessage.error("修改锚点失败，请重试");
    resetEditingState();
  }
};

/**
 * @description 取消当前操作
 */
const handleCancelOperation = (): void => {
  try {
    const mode = currentMode.value;

    // 清理所有活动的事件处理器
    activeEventHandlers.value.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.warn("清理事件处理器失败:", error);
      }
    });
    activeEventHandlers.value = [];

    // 重置状态
    currentMode.value = "";
    currentEditingIndex.value = -1;

    // 根据不同模式进行相应的清理
    if (mode === "绘制路线") {
      // 如果正在绘制，尝试停止绘制
      try {
        const plotUtil = AppCesium.getInstance().getPlotUtil();
        if (plotUtil && plotUtil.stop) {
          plotUtil.stop();
        }
      } catch (error) {
        console.warn("停止绘制失败:", error);
      }
    }

    ElMessage.info(`已取消${mode}操作`);
    console.log(`用户取消了${mode}操作`);
  } catch (error) {
    console.error("取消操作失败:", error);
    ElMessage.error("取消操作失败");
  }
};

/**
 * @description 处理高程输入
 */
const handleHeightInput = (row: RouteStop, value: string): void => {
  try {
    // 只允许数字、小数点和负号
    const cleanValue = value.replace(/[^\d.-]/g, '');

    // 防止多个小数点
    const parts = cleanValue.split('.');
    let processedValue = '';
    if (parts.length > 2) {
      processedValue = parts[0] + '.' + parts.slice(1).join('');
    } else {
      processedValue = cleanValue;
    }

    // 防止多个负号，只允许在开头
    if (processedValue.indexOf('-') > 0) {
      processedValue = processedValue.replace(/-/g, '');
    }

    // 临时存储为字符串形式，允许用户输入过程中的中间状态
    (row as any).height = processedValue;

    console.log(`锚点 ${row.name} 高程输入: ${processedValue}`);
  } catch (error) {
    console.error("处理高程输入失败:", error);
  }
};

/**
 * @description 处理高程变化
 */
const handleHeightChange = (index: number, row: RouteStop): void => {
  try {
    const heightValue = (row as any).height;

    // 转换为数字进行验证和计算
    const numericHeight = parseFloat(String(heightValue));
    if (!isNaN(numericHeight)) {
      row.height = numericHeight;
    }

    console.log(`锚点 ${row.name} 高程更新为: ${row.height}`);

    // 更新地图上的点位置
    updateStopPosition(index, row);

    // 可以在这里添加实时保存逻辑
    // RouteService.updateStopHeight(row.id, row.height);
  } catch (error) {
    console.error("更新高程失败:", error);
    ElMessage.error("更新高程失败");
  }
};

/**
 * @description 验证高程值
 */
const validateHeight = (row: RouteStop): void => {
  try {
    const heightValue = (row as any).height;

    // 处理空值情况
    if (heightValue === null || heightValue === undefined || heightValue === '') {
      row.height = 0;
      ElMessage.warning(`${row.name} 高程不能为空，已设置为0`);
      return;
    }

    // 转换为数字
    const numericHeight = parseFloat(String(heightValue));

    // 检查是否为有效数字
    if (isNaN(numericHeight)) {
      row.height = 0;
      ElMessage.warning(`${row.name} 高程必须为数字，已设置为0`);
      return;
    }

    // 限制高程范围（可根据实际需求调整）
    let validHeight = numericHeight;
    if (validHeight < -1000) {
      validHeight = -1000;
      ElMessage.warning(`${row.name} 高程不能低于-1000米`);
    } else if (validHeight > 10000) {
      validHeight = 10000;
      ElMessage.warning(`${row.name} 高程不能高于10000米`);
    }

    // 保留两位小数
    validHeight = Math.round(validHeight * 100) / 100;
    row.height = validHeight;

    console.log(`${row.name} 高程验证完成: ${row.height}`);
  } catch (error) {
    console.error("验证高程失败:", error);
    row.height = 0;
  }
};

/**
 * @description 处理批量高程输入
 */
const handleBatchHeightInput = (value: string): void => {
  try {
    // 只允许数字、小数点和负号
    const cleanValue = value.replace(/[^\d.-]/g, '');

    // 防止多个小数点
    const parts = cleanValue.split('.');
    let processedValue = '';
    if (parts.length > 2) {
      processedValue = parts[0] + '.' + parts.slice(1).join('');
    } else {
      processedValue = cleanValue;
    }

    // 防止多个负号，只允许在开头
    if (processedValue.indexOf('-') > 0) {
      processedValue = processedValue.replace(/-/g, '');
    }

    batchHeight.value = processedValue;
  } catch (error) {
    console.error("处理批量高程输入失败:", error);
  }
};

/**
 * @description 批量设置高程
 */
const handleBatchSetHeight = (): void => {
  try {
    if (routeStops.value.length === 0) {
      ElMessage.warning("没有锚点可以设置高程");
      return;
    }

    if (!batchHeight.value || batchHeight.value.trim() === '') {
      ElMessage.warning("请输入有效的高程值");
      return;
    }

    const numericHeight = parseFloat(batchHeight.value);
    if (isNaN(numericHeight)) {
      ElMessage.warning("高程值必须为数字");
      return;
    }

    // 验证高程范围
    let validHeight = numericHeight;
    if (validHeight < -1000) {
      validHeight = -1000;
      ElMessage.warning("高程不能低于-1000米，已调整为-1000");
    } else if (validHeight > 10000) {
      validHeight = 10000;
      ElMessage.warning("高程不能高于10000米，已调整为10000");
    }

    // 保留两位小数
    validHeight = Math.round(validHeight * 100) / 100;
    batchHeight.value = validHeight.toString();

    // 批量更新所有锚点的高程
    routeStops.value.forEach((stop) => {
      stop.height = validHeight;
    });

    // 批量更新地图上的锚点位置（性能优化）
    updateAllStopPositions();

    ElMessage.success(`已将所有 ${routeStops.value.length} 个锚点的高程设置为 ${validHeight} 米`);
    console.log(`批量设置高程: ${validHeight}，影响锚点数量: ${routeStops.value.length}`);
  } catch (error) {
    console.error("批量设置高程失败:", error);
    ElMessage.error("批量设置高程失败");
  }
};

/**
 * @description 更新锚点在地图上的位置
 */
const updateStopPosition = (_index: number, row: RouteStop): void => {
  try {
    if (!stopLayer.value) {
      console.warn("锚点图层未初始化，无法更新位置");
      return;
    }

    console.log(`开始更新锚点 ${row.name} 的位置，新高程: ${row.height}`);

    // 创建新的位置对象
    const newPosition = new BC.Position(row.lon, row.lat, row.height);

    // 查找并更新对应的地图锚点
    let updated = false;
    stopLayer.value.eachOverlay((overlay: any) => {
      if (overlay.id === row.id) {
        overlay.position = newPosition;
        updated = true;
        console.log(`成功更新锚点 ${row.name} 的地图位置: [${row.lon}, ${row.lat}, ${row.height}]`);
      }
    });

    if (!updated) {
      console.warn(`未找到锚点 ${row.name} (ID: ${row.id}) 对应的地图对象`);
    }

    // 同时更新路线显示
    updateRouteVisualization(routeStops.value);

    // 调试：检查位置同步状态
    setTimeout(() => debugCheckStopPositions(), 100);
  } catch (error) {
    console.error(`更新锚点 ${row.name} 地图位置失败:`, error);
  }
};

/**
 * @description 批量更新所有锚点在地图上的位置
 */
const updateAllStopPositions = (): void => {
  try {
    if (!stopLayer.value) {
      console.warn("锚点图层未初始化，无法批量更新位置");
      return;
    }

    console.log(`开始批量更新 ${routeStops.value.length} 个锚点的位置`);

    let updatedCount = 0;

    // 遍历所有锚点数据
    routeStops.value.forEach((stop) => {
      const newPosition = new BC.Position(stop.lon, stop.lat, stop.height);

      // 查找并更新对应的地图锚点
      stopLayer.value.eachOverlay((overlay: any) => {
        if (overlay.id === stop.id) {
          overlay.position = newPosition;
          updatedCount++;
          console.log(`批量更新锚点 ${stop.name} 位置: [${stop.lon}, ${stop.lat}, ${stop.height}]`);
        }
      });
    });

    console.log(`批量更新完成，成功更新 ${updatedCount} 个锚点位置`);

    // 更新路线显示
    updateRouteVisualization(routeStops.value);

    // 调试：检查批量更新后的位置同步状态
    setTimeout(() => debugCheckStopPositions(), 100);
  } catch (error) {
    console.error("批量更新锚点位置失败:", error);
  }
};

/**
 * @description 调试：检查锚点位置同步状态
 */
const debugCheckStopPositions = (): void => {
  try {
    if (!stopLayer.value) {
      console.log("调试：锚点图层未初始化");
      return;
    }

    console.log("=== 锚点位置同步检查 ===");
    console.log(`数据中的锚点数量: ${routeStops.value.length}`);

    let mapStopCount = 0;
    stopLayer.value.eachOverlay((_overlay: any) => {
      mapStopCount++;
    });
    console.log(`地图中的锚点数量: ${mapStopCount}`);

    routeStops.value.forEach((stop, index) => {
      console.log(`锚点 ${index + 1}: ${stop.name}`);
      console.log(`  数据位置: [${stop.lon}, ${stop.lat}, ${stop.height}]`);

      stopLayer.value.eachOverlay((overlay: any) => {
        if (overlay.id === stop.id) {
          const pos = overlay.position;
          console.log(`  地图位置: [${pos.lng}, ${pos.lat}, ${pos.alt}]`);

          const heightDiff = Math.abs(pos.alt - stop.height);
          if (heightDiff > 0.01) {
            console.warn(`  ⚠️ 高程不同步！差值: ${heightDiff}`);
          } else {
            console.log(`  ✅ 位置同步正常`);
          }
        }
      });
    });
    console.log("========================");
  } catch (error) {
    console.error("调试检查失败:", error);
  }
};

/**
 * @description 重置编辑状态
 */
const resetEditingState = (): void => {
  currentMode.value = "";
  currentEditingIndex.value = -1;
};

// === 数据处理方法 ===
/**
 * @description 生成GeoJSON数据
 */
const generateGeoJSON = () => {
  if (routeStops.value.length === 0) {
    return null;
  }

  return {
    type: "Feature",
    geometry: {
      type: "LineString",
      coordinates: routeStops.value.map((stop) => [
        stop.lon,
        stop.lat,
        stop.height,
      ]),
    },
    properties: {
      name: routeName.value,
      stopCount: routeStops.value.length,
      stops: routeStops.value,
    },
  };
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
</script>

<style scoped>
.error-input {
  border-color: #f56c6c !important;
}

.error-text {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  margin-bottom: 8px;
}

.route-status {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
  font-size: 12px;
  color: #606266;
}

.mode-indicator {
  color: #409eff;
  font-weight: bold;
}

.routeInfo {
  margin-top: 10px;
}

.route_dlt {
  color: #ff7373;
}

.route_oth_btn {
  font-size: 14px;
  color: #1966ff;
}

.el-button + .el-button {
  margin-left: 8px;
}

.el-row {
  margin-bottom: 10px;
}
.btn {
  width: 100px;
  height: 36px;
  background: #f2f6ff;
  border-color: #f2f6ff;
  color: #2c3037;
  box-sizing: border-box;

  &:hover {
    border-color: #409eff;
    background: rgba(#f2f6ff, 0.75);
  }
}

.cancel-btn {
  background: #f56c6c !important;
  border-color: #f56c6c !important;
  color: #fff !important;

  &:hover {
    background: #f45454 !important;
    border-color: #f45454 !important;
  }
}

.mode-indicator {
  color: #409eff;
  font-weight: bold;
  background: #ecf5ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 10px;
}

.batch-height-control {
  display: flex;
  align-items: center;
  margin-left: 15px;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.route-status {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

/* 高程输入框样式 */
:deep(.el-table .el-input) {
  .el-input__inner {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 12px;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }

    &:hover {
      border-color: #c0c4cc;
    }
  }
}

/* 表格行样式优化 */
:deep(.el-table) {
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
