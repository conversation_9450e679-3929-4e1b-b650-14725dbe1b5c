<template>
  <page-card
    :close-icon="false"
    class="connectivity-analysis"
    title="连通性分析"
  >
    <!-- 按钮区域 -->
    <el-row class="button-section" flex="~ row justify-start">
      <el-button
        type="primary"
        class="primary-btn h-9 w-100px"
        :loading="isSelectingStartPoint"
        @click="handleSelectStartPoint"
      >
        选择起点
      </el-button>
      <el-button
        type="primary"
        class="primary-btn h-9 w-100px"
        :loading="isSelectingEndPoint"
        @click="handleSelectEndPoint"
      >
        选择终点
      </el-button>
      <el-button
        type="primary"
        class="primary-btn h-9 w-100px"
        :loading="isAnalyzing"
        @click="handleAnalysis"
        :disabled="
          !startPoint ||
          !endPoint ||
          isSelectingStartPoint ||
          isSelectingEndPoint
        "
      >
        分析
      </el-button>
      <el-button
        class="clear-btn h-9 w-100px"
        @click="handleClear"
        :disabled="isAnalyzing || isSelectingStartPoint || isSelectingEndPoint"
      >
        清除
      </el-button>
    </el-row>

    <!-- 选择的点显示区域 -->
    <div
      v-if="startPoint || endPoint"
      grid="~ cols-2 gap-4"
      class="points-section"
    >
      <el-row v-if="startPoint" class="point-row">
        <el-col :span="24">
          <el-text class="point-label">已选择起点：</el-text>
          <div class="point-info">
            <span class="point-text">
              管点编号: {{ startPoint.gxddh || "无" }}
            </span>
            <span class="coordinate-text">
              坐标: {{ startPoint.lng.toFixed(6) }},
              {{ startPoint.lat.toFixed(6) }}
            </span>
          </div>
        </el-col>
      </el-row>
      <el-row v-if="endPoint" class="point-row">
        <el-col :span="24">
          <el-text class="point-label">已选择终点：</el-text>
          <div class="point-info">
            <span class="point-text">
              管点编号: {{ endPoint.gxddh || "无" }}
            </span>
            <span class="coordinate-text">
              坐标: {{ endPoint.lng.toFixed(6) }}, {{ endPoint.lat.toFixed(6) }}
            </span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 分析结果显示区域 -->
    <!-- <el-row v-if="showResult" class="result-section">
      <el-col :span="24">
        <el-text class="result-label">分析结果：</el-text>
        <span
          :class="[
            'result-text',
            analysisResult === '两点连通' ? 'success' : 'error',
          ]"
        >
          {{ analysisResult }}
        </span>
      </el-col>
    </el-row> -->
    <div
      v-if="showResult"
      class="result-section"
      :class="analysisResult === '两点连通' ? 'success' : 'error'"
    >
      <el-icon v-if="analysisResult" class="color-#FF7373"
        ><CircleCloseFilled
      /></el-icon>
      <el-icon v-else class="color-#1966FF"><CircleCheckFilled /></el-icon>
      <el-text class="result-label">分析结果：</el-text>
      <span :class="'result-text'">{{ analysisResult }}</span>
    </div>
    <!-- 相关设备表格 -->
    <el-row v-if="showDeviceTable" class="table-section">
      <el-col :span="24">
        <el-text class="table-label">相关设备</el-text>
        <el-table :data="getCurrentPageData()" border style="width: 100%;">
          <el-table-column
            prop="index"
            label="序号"
          ></el-table-column>
          <el-table-column
            prop="gxddh"
            label="管点编号"
            width="100"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column prop="gl" label="管类" width="70">
            <template #default="scope">
              <span>给水</span>
            </template>
          </el-table-column>
          <el-table-column prop="sx" label="属性" width="80"></el-table-column>
          <el-table-column
            prop="fsw"
            label="附属物"
            width="90"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="szdl"
            label="所在道路"
            width="80"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <el-button
                size="small"
                type="text"
                @click="handleLocation(scope.row)"
              >
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center">
          <div>
            <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
          </div>
          <el-pagination
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :total="total"
            layout="prev, pager, next, jumper"
            :pager-count="5"
            class="pagination"
            background
            small
          ></el-pagination>
        </div>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
import { CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue";
import type { MapEngineType } from "@/components/BufferAnalysisDrawButtons.vue";
import { queryConnectivity, pointListByBms } from "@/api/analysis";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import {
  pipeLocationByBms,
  clearPipeLocationHighlight,
} from "@/utils/PipeLocation";
import { AppCesium } from "@/lib/cesium/AppCesium";

/**
 * 临时图层ID常量
 */
const TEMP_LAYER_IDS = {
  START_POINT_SOURCE: "connectivity-start-point-source",
  START_POINT_LAYER: "connectivity-start-point-layer",
  END_POINT_SOURCE: "connectivity-end-point-source",
  END_POINT_LAYER: "connectivity-end-point-layer",
  RESULT_LINE_SOURCE: "connectivity-result-line-source",
  RESULT_LINE_LAYER: "connectivity-result-line-layer",
} as const;

/**
 * 定义相关设备数据项接口
 */
interface ConnectivityDeviceItem {
  index: number;
  gxddh: string; // 管点编号
  gl: string; // 管类
  sx: string; // 属性
  fsw: string; // 附属物
  szdl: string; // 所在道路
  dmgc?: number; // 地面高程
  longitude: number; // 经度
  latitude: number; // 纬度
}

/**
 * 定义点坐标接口
 */
interface AnalysisPoint {
  lng: number;
  lat: number;
  alt?: number; // 高度，用于三维显示
  gxddh?: string; // 管点编号
}

const route = useRoute();
const mapEngine = computed((): MapEngineType => {
  return route.path.includes("cesium") ? "cesium" : "maplibre";
});

/**
 * 响应式数据状态
 */
// 点选择相关状态
const startPoint = ref<AnalysisPoint | null>(null);
const endPoint = ref<AnalysisPoint | null>(null);
const isSelectingStartPoint = ref<boolean>(false);
const isSelectingEndPoint = ref<boolean>(false);
const isAnalyzing = ref<boolean>(false);

// 分析结果状态
const showResult = ref<boolean>(false);
const analysisResult = ref<string>("");

// 设备表格状态
const deviceData = ref<ConnectivityDeviceItem[]>([]);
const currentPage = ref<number>(1);
const pageSize = ref<number>(5);
const pageSizes = ref<number[]>([5, 10, 20, 50]);
const total = ref<number>(0);
const showDeviceTable = ref<boolean>(false);

// Cesium相关状态
const cesiumHighlightedNodes = ref<string[]>([]); // 当前高亮的节点编号

// Cesium VectorLayer管理
const cesiumPointsLayer = ref<any>(null); // 起点终点图层
const cesiumResultLayer = ref<any>(null); // 分析结果图层

/**
 * @description 通用的Cesium节点选择函数
 * @param {string} type - 选择类型：'start' | 'end'
 * @param {Function} onSuccess - 选择成功回调函数
 * @param {Function} onError - 选择失败回调函数
 */
const selectCesiumPipeNode = (
  type: "start" | "end",
  onSuccess: (point: AnalysisPoint) => void,
  onError: (error: any) => void
) => {
  try {
    const tipMessage =
      type === "start"
        ? "请在3D地图上点击管点选择起点"
        : "请在3D地图上点击管点选择终点";
    ElMessage.info(tipMessage);

    AppCesium.getInstance().selectPipeComponent((res: any) => {
      try {
        if (res && res.type === "node" && res.id && res.position) {
          const selectedPoint: AnalysisPoint = {
            lng: res.position.lng,
            lat: res.position.lat,
            alt: res.position.alt, // 使用实际的高度信息
            gxddh: res.id,
          };

          // 检查是否与已选择的点重复
          if (
            type === "end" &&
            startPoint.value &&
            selectedPoint.gxddh === startPoint.value.gxddh
          ) {
            onError(new Error("终点不能与起点相同"));
            return;
          }
          if (
            type === "start" &&
            endPoint.value &&
            selectedPoint.gxddh === endPoint.value.gxddh
          ) {
            onError(new Error("起点不能与终点相同"));
            return;
          }

          onSuccess(selectedPoint);

          const pointName = type === "start" ? "起点" : "终点";
          ElMessage.success(`已选择${pointName}：${selectedPoint.gxddh}`);
        } else {
          onError(new Error("请点击管点位置"));
        }
      } catch (error) {
        onError(error);
      }
    });
  } catch (error) {
    onError(error);
  }
};

/**
 * @description 高亮Cesium中的管点节点
 * @param {string[]} nodeCodes - 需要高亮的节点编号数组
 * @param {string} highlightColor - 高亮颜色，默认为黄色
 */
const highlightCesiumNodes = (
  nodeCodes: string[],
  highlightColor: string = "#ffff00"
) => {
  try {
    const viewer = AppCesium.getInstance().getViewer();

    // 遍历所有管点图层
    viewer.eachLayer((layer: any) => {
      if (layer.attr && layer.attr.type === "pipeline") {
        const overlays = layer.getOverlaysByAttr("id", layer.attr.id);
        if (overlays && overlays.length > 0) {
          // 构建条件样式
          const conditions: any[] = [];

          // 为每个需要高亮的节点添加条件
          nodeCodes.forEach((nodeCode) => {
            conditions.push([
              "${id} === '" + nodeCode + "'",
              `vec4(1.0, 1.0, 0.0, 1.0)`, // 黄色高亮
            ]);
          });

          // 添加默认样式
          conditions.push(["true", "vec4(1.0, 1.0, 1.0, 1.0)"]); // 白色默认

          // 应用样式
          const { Cesium } = BC.Namespace;
          overlays[0].setStyle(
            new Cesium.Cesium3DTileStyle({
              color: {
                conditions: conditions,
              },
            })
          );

          console.log(
            `已对图层 ${layer.attr.name} 应用节点高亮，节点数量: ${nodeCodes.length}`
          );
        }
      }
    }, this);

    // 更新高亮节点状态
    cesiumHighlightedNodes.value = [...nodeCodes];
  } catch (error) {
    console.error("高亮Cesium节点失败:", error);
  }
};

/**
 * @description 清除Cesium中的节点高亮
 */
const clearCesiumNodeHighlight = () => {
  try {
    const viewer = AppCesium.getInstance().getViewer();

    viewer.eachLayer((layer: any) => {
      if (layer.attr && layer.attr.type === "pipeline") {
        const overlays = layer.getOverlaysByAttr("id", layer.attr.id);
        if (overlays && overlays.length > 0) {
          // 恢复默认样式
          const { Cesium } = BC.Namespace;
          overlays[0].setStyle(
            new Cesium.Cesium3DTileStyle({
              color: "vec4(1.0, 1.0, 1.0, 1.0)", // 恢复白色默认
            })
          );
        }
      }
    }, this);

    cesiumHighlightedNodes.value = [];
    console.log("已清除Cesium节点高亮");
  } catch (error) {
    console.error("清除Cesium节点高亮失败:", error);
  }
};

/**
 * 添加选择点的临时图层显示
 * @param point 选择的点
 * @param type 点类型：'start' | 'end'
 */
const addSelectedPointLayer = (
  point: AnalysisPoint,
  type: "start" | "end",
  mapEngine: MapEngineType
) => {
  try {
    if (mapEngine === "cesium") {
      // Cesium模式下使用VectorLayer显示选择的点
      const viewer = AppCesium.getInstance().getViewer();
      const { Cesium } = BC.Namespace;

      // 创建或获取起点终点图层
      if (!cesiumPointsLayer.value) {
        cesiumPointsLayer.value = new BC.VectorLayer(
          "connectivity-points-layer"
        );
        viewer.addLayer(cesiumPointsLayer.value);
      }

      const color = type === "start" ? Cesium.Color.GREEN : Cesium.Color.BLUE;
      const label = type === "start" ? "起点" : "终点";
      const pointId = `connectivity-${type}-point-${point.gxddh}`;

      // 移除已存在的同类型点
      const existingOverlays = cesiumPointsLayer.value.getOverlaysByAttr(
        "id",
        pointId
      );
      if (existingOverlays && existingOverlays.length > 0) {
        existingOverlays.forEach((overlay: any) => {
          cesiumPointsLayer.value.removeOverlay(overlay);
        });
      }

      // 创建BC点标记并添加到VectorLayer
      const position = new BC.Position(point.lng, point.lat, point.alt || 50);

      // 创建点标记
      const pointOverlay = new BC.Point(position);
      pointOverlay.id = pointId;
      pointOverlay.setStyle({
        pixelSize: 12,
        color: color,
        outlineColor: BC.Color.WHITE,
        outlineWidth: 2,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      });

      // 创建标签
      const labelPosition = new BC.Position(
        point.lng,
        point.lat,
        (point.alt || 50) + 2
      );
      const labelOverlay = new BC.Label(
        labelPosition,
        `${label}\n${point.gxddh}`
      );
      labelOverlay.id = `${pointId}-label`;
      labelOverlay.setStyle({
        font: "14px Microsoft YaHei",
        fillColor: color,
        outlineColor: BC.Color.WHITE,
        outlineWidth: 2,
        offsetX: 0,
        offsetY: -20,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      });

      // 添加到图层
      cesiumPointsLayer.value.addOverlay(pointOverlay);
      cesiumPointsLayer.value.addOverlay(labelOverlay);

      console.log(`已在Cesium VectorLayer中创建${label}标记:`, point.gxddh);
    } else {
      // MapLibre模式的原有逻辑
      const map = AppMaplibre.getMap();

      if (!map) {
        console.error("地图实例不存在");
        return;
      }

      const sourceId =
        type === "start"
          ? TEMP_LAYER_IDS.START_POINT_SOURCE
          : TEMP_LAYER_IDS.END_POINT_SOURCE;
      const layerId =
        type === "start"
          ? TEMP_LAYER_IDS.START_POINT_LAYER
          : TEMP_LAYER_IDS.END_POINT_LAYER;
      const labelLayerId = `${layerId}-label`;
      const color = type === "start" ? "#00ff00" : "#0080ff"; // 起点绿色，终点蓝色

      const featureData = {
        type: "FeatureCollection" as const,
        features: [
          {
            type: "Feature" as const,
            geometry: {
              type: "Point" as const,
              coordinates: [point.lng, point.lat],
            },
            properties: {
              type: type,
              gxddh: point.gxddh || "",
            },
          },
        ],
      };

      // 检查并更新数据源
      const existingSource = map.getSource(sourceId);
      if (existingSource) {
        // 如果数据源已存在，直接更新数据
        (existingSource as any).setData(featureData);
        console.log(
          `已更新${type === "start" ? "起点" : "终点"}数据源:`,
          point.gxddh
        );
      } else {
        // 数据源不存在，创建新的数据源和图层
        map.addSource(sourceId, {
          type: "geojson",
          data: featureData,
        });

        // 添加圆形标记图层
        map.addLayer({
          id: layerId,
          type: "circle",
          source: sourceId,
          paint: {
            "circle-radius": [
              "interpolate",
              ["linear"],
              ["zoom"],
              10,
              4,
              18,
              8,
            ],
            "circle-color": color,
            "circle-stroke-width": 2,
            "circle-stroke-color": "#ffffff",
          },
        });

        // 添加标签图层
        map.addLayer({
          id: labelLayerId,
          type: "symbol",
          source: sourceId,
          layout: {
            "text-field": type === "start" ? "起点" : "终点",
            "text-font": ["Open Sans Regular"],
            "text-offset": [0, -2],
            "text-anchor": "bottom",
            "text-size": ["interpolate", ["linear"], ["zoom"], 10, 12, 18, 16],
          },
          paint: {
            "text-color": color,
            "text-halo-color": "#ffffff",
            "text-halo-width": 1,
          },
        });

        console.log(
          `已创建${type === "start" ? "起点" : "终点"}临时图层:`,
          point.gxddh
        );
      }
    }
  } catch (error) {
    console.error(
      `添加${type === "start" ? "起点" : "终点"}临时图层失败:`,
      error
    );
  }
};

/**
 * @description 在Cesium中添加连通性分析结果展示
 * @param {any} geojsonData - 分析结果GeoJSON数据
 */
const addCesiumConnectivityResult = (geojsonData: any) => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = BC.Namespace;

    // 清除之前的分析结果（只清除result类型，保留起点终点标记）
    clearCesiumResultEntities();

    if (
      !geojsonData ||
      !geojsonData.features ||
      geojsonData.features.length === 0
    ) {
      console.log("没有分析结果数据需要展示");
      return;
    }

    // 创建或获取分析结果图层
    if (!cesiumResultLayer.value) {
      cesiumResultLayer.value = new BC.VectorLayer("connectivity-result-layer");
      viewer.addLayer(cesiumResultLayer.value);
    }

    // 遍历所有特征，创建连通路径的可视化
    geojsonData.features.forEach((feature: any, index: number) => {
      if (feature.geometry && feature.geometry.coordinates) {
        let positions: any[] = [];

        if (feature.geometry.type === "MultiLineString") {
          // 处理多线段
          feature.geometry.coordinates.forEach(
            (lineString: number[][], lineIndex: number) => {
              const linePositions = lineString.map(
                (coord: number[], index: number) => {
                  return new BC.Position(
                    coord[0],
                    coord[1],
                    Number(
                      index === 0
                        ? feature?.properties?.QDGC
                        : feature?.properties?.ZDGC
                    ) || 20
                  ); // 高度设为20米
                }
              );

              // 创建BC线段并添加到图层
              const polylineOverlay = new BC.Polyline(linePositions);
              polylineOverlay.id = `connectivity-result-line-${index}-${lineIndex}`;
              polylineOverlay.setStyle({
                width: 6,
                material: BC.Color.ORANGE.withAlpha(0.9),
                clampToGround: false,
              });

              cesiumResultLayer.value.addOverlay(polylineOverlay);
            }
          );
        } else if (feature.geometry.type === "LineString") {
          // 处理单线段
          positions = feature.geometry.coordinates.map((coord: number[]) => {
            return new BC.Position(
              coord[0],
              coord[1],
              Number(
                index === 0
                  ? feature?.properties?.QDGC
                  : feature?.properties?.ZDGC
              ) || 20
            );
          });

          // 创建BC线段并添加到图层
          const polylineOverlay = new BC.Polyline(positions);
          polylineOverlay.id = `connectivity-result-line-${index}`;
          polylineOverlay.setStyle({
            width: 6,
            material: BC.Color.ORANGE.withAlpha(0.9),
            clampToGround: false,
          });

          cesiumResultLayer.value.addOverlay(polylineOverlay);
        }

        // 添加起点和终点标记（如果有属性信息）
        if (feature.properties) {
          const startNodeCode =
            feature.properties.qdbh || feature.properties.QDBH;
          const endNodeCode =
            feature.properties.zdbh || feature.properties.ZDBH;

          if (startNodeCode && positions.length > 0) {
            // 创建起点节点标记
            const startPosition = positions[0];
            const startPoint = new BC.Point(startPosition);
            startPoint.id = `connectivity-start-node-${startNodeCode}`;
            startPoint.setStyle({
              pixelSize: 8,
              color: BC.Color.GREEN,
              outlineColor: BC.Color.WHITE,
              outlineWidth: 2,
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            });

            // 创建起点标签
            const startLabelPosition = new BC.Position(
              startPosition.lng,
              startPosition.lat,
              startPosition.alt + 2
            );
            const startLabel = new BC.Label(startLabelPosition, startNodeCode);
            startLabel.id = `connectivity-start-node-${startNodeCode}-label`;
            startLabel.setStyle({
              font: "12px Microsoft YaHei",
              fillColor: BC.Color.WHITE,
              outlineColor: BC.Color.BLACK,
              outlineWidth: 1,
              offsetX: 0,
              offsetY: -25,
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            });

            cesiumResultLayer.value.addOverlay(startPoint);
            cesiumResultLayer.value.addOverlay(startLabel);
          }

          if (endNodeCode && positions.length > 0) {
            // 创建终点节点标记
            const endPosition = positions[positions.length - 1];
            const endPoint = new BC.Point(endPosition);
            endPoint.id = `connectivity-end-node-${endNodeCode}`;
            endPoint.setStyle({
              pixelSize: 8,
              color: BC.Color.RED,
              outlineColor: BC.Color.WHITE,
              outlineWidth: 2,
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            });

            // 创建终点标签
            const endLabelPosition = new BC.Position(
              endPosition.lng,
              endPosition.lat,
              endPosition.alt + 2
            );
            const endLabel = new BC.Label(endLabelPosition, endNodeCode);
            endLabel.id = `connectivity-end-node-${endNodeCode}-label`;
            endLabel.setStyle({
              font: "12px Microsoft YaHei",
              fillColor: BC.Color.WHITE,
              outlineColor: BC.Color.BLACK,
              outlineWidth: 1,
              offsetX: 0,
              offsetY: -25,
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            });

            cesiumResultLayer.value.addOverlay(endPoint);
            cesiumResultLayer.value.addOverlay(endLabel);
          }
        }
      }
    });

    // 缩放到包含起点终点和分析结果的完整场景
    flyToConnectivityResults();

    console.log("已在Cesium中创建连通性分析结果");
  } catch (error) {
    console.error("在Cesium中添加连通性分析结果失败:", error);
    throw error;
  }
};

/**
 * @description 缩放到连通性分析结果
 */
const flyToConnectivityResults = async () => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = BC.Namespace;

    // 收集所有相关位置点
    const positions: any[] = [];

    // 添加起点终点位置
    if (startPoint.value) {
      positions.push(
        Cesium.Cartesian3.fromDegrees(
          startPoint.value.lng,
          startPoint.value.lat,
          startPoint.value.alt || 50
        )
      );
    }

    if (endPoint.value) {
      positions.push(
        Cesium.Cartesian3.fromDegrees(
          endPoint.value.lng,
          endPoint.value.lat,
          endPoint.value.alt || 50
        )
      );
    }

    // 如果有足够的位置点，计算边界并缩放
    if (positions.length > 0) {
      if (positions.length === 1) {
        // 只有一个点，直接飞到该点
        await viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(
            positions.length === 1 && startPoint.value
              ? startPoint.value.lng
              : endPoint.value!.lng,
            positions.length === 1 && startPoint.value
              ? startPoint.value.lat
              : endPoint.value!.lat,
            1000 // 高度1000米观察
          ),
          duration: 2.0,
        });
      } else {
        // 多个点，计算边界球并缩放
        const boundingSphere = Cesium.BoundingSphere.fromPoints(positions);
        await viewer.camera.flyToBoundingSphere(boundingSphere, {
          duration: 2.0,
          offset: new Cesium.HeadingPitchRange(
            0,
            -0.5,
            boundingSphere.radius * 3
          ),
        });
      }

      console.log("成功缩放到连通性分析结果");
    } else {
      console.warn("没有找到有效的位置点用于缩放");
    }
  } catch (error) {
    console.error("缩放到连通性分析结果失败:", error);
  }
};

/**
 * @description 清除Cesium中的分析结果entities（只清除result类型）
 */
const clearCesiumResultEntities = () => {
  try {
    // 清除结果VectorLayer
    if (cesiumResultLayer.value) {
      cesiumResultLayer.value.clear();
      console.log("已清除分析结果图层中的所有内容");
    }
  } catch (error) {
    console.error("清除Cesium分析结果entities失败:", error);
  }
};

/**
 * @description 清除Cesium中的分析结果entities和VectorLayers
 */
const clearCesiumAnalysisEntities = () => {
  try {
    const viewer = AppCesium.getInstance().getViewer();

    // 清除VectorLayers
    if (cesiumPointsLayer.value) {
      cesiumPointsLayer.value.clear();
      viewer.removeLayer(cesiumPointsLayer.value);
      cesiumPointsLayer.value = null;
    }

    if (cesiumResultLayer.value) {
      cesiumResultLayer.value.clear();
      viewer.removeLayer(cesiumResultLayer.value);
      cesiumResultLayer.value = null;
    }

    // VectorLayer管理，无需额外的数组管理
    console.log("已清除Cesium所有分析相关的VectorLayers");
  } catch (error) {
    console.error("清除Cesium分析结果entities失败:", error);
  }
};

/**
 * 添加连通性分析结果图层
 * @param geojsonData 分析结果GeoJSON数据
 */
const addConnectivityResultLayer = (geojsonData: any) => {
  try {
    const map = AppMaplibre.getMap();

    // 移除已存在的结果图层
    if (map.getLayer(TEMP_LAYER_IDS.RESULT_LINE_LAYER)) {
      map.removeLayer(TEMP_LAYER_IDS.RESULT_LINE_LAYER);
    }
    if (map.getSource(TEMP_LAYER_IDS.RESULT_LINE_SOURCE)) {
      map.removeSource(TEMP_LAYER_IDS.RESULT_LINE_SOURCE);
    }

    // 添加数据源
    map.addSource(TEMP_LAYER_IDS.RESULT_LINE_SOURCE, {
      type: "geojson",
      data: geojsonData,
    });

    // 添加线图层
    map.addLayer({
      id: TEMP_LAYER_IDS.RESULT_LINE_LAYER,
      type: "line",
      source: TEMP_LAYER_IDS.RESULT_LINE_SOURCE,
      layout: {
        "line-join": "round",
        "line-cap": "round",
      },
      paint: {
        "line-color": "#ff6600", // 橙色
        "line-width": 4,
        "line-opacity": 0.8,
      },
    });

    // 自动缩放到结果范围
    if (geojsonData.features && geojsonData.features.length > 0) {
      // 计算边界框
      let minLng = Infinity,
        minLat = Infinity,
        maxLng = -Infinity,
        maxLat = -Infinity;

      geojsonData.features.forEach((feature: any) => {
        if (feature.geometry && feature.geometry.coordinates) {
          const coords = feature.geometry.coordinates;
          if (feature.geometry.type === "MultiLineString") {
            coords.forEach((lineString: number[][]) => {
              lineString.forEach((coord: number[]) => {
                if (coord.length >= 2) {
                  const lng = coord[0];
                  const lat = coord[1];
                  minLng = Math.min(minLng, lng);
                  minLat = Math.min(minLat, lat);
                  maxLng = Math.max(maxLng, lng);
                  maxLat = Math.max(maxLat, lat);
                }
              });
            });
          } else if (feature.geometry.type === "LineString") {
            coords.forEach((coord: number[]) => {
              if (coord.length >= 2) {
                const lng = coord[0];
                const lat = coord[1];
                minLng = Math.min(minLng, lng);
                minLat = Math.min(minLat, lat);
                maxLng = Math.max(maxLng, lng);
                maxLat = Math.max(maxLat, lat);
              }
            });
          }
        }
      });

      // 缩放到边界框
      if (
        isFinite(minLng) &&
        isFinite(minLat) &&
        isFinite(maxLng) &&
        isFinite(maxLat)
      ) {
        map.fitBounds(
          [
            [minLng, minLat],
            [maxLng, maxLat],
          ],
          {
            padding: 100,
            duration: 1000,
          }
        );
      }
    }

    console.log("已添加连通性分析结果图层");
  } catch (error) {
    console.error("添加连通性分析结果图层失败:", error);
  }
};

/**
 * 从连通性分析结果中提取管点编号并去重
 * @param featureCollection 分析结果FeatureCollection
 * @returns 去重后的管点编号数组
 */
const extractNodeCodesFromResult = (featureCollection: any): string[] => {
  try {
    const nodeCodes = new Set<string>();

    if (featureCollection && featureCollection.features) {
      featureCollection.features.forEach((feature: any) => {
        if (feature.properties) {
          // 提取起点编号
          if (feature.properties.qdbh) {
            nodeCodes.add(feature.properties.qdbh);
          }
          // 提取终点编号
          if (feature.properties.zdbh) {
            nodeCodes.add(feature.properties.zdbh);
          }
          // 兼容其他可能的字段名
          if (feature.properties.QDBH) {
            nodeCodes.add(feature.properties.QDBH);
          }
          if (feature.properties.ZDBH) {
            nodeCodes.add(feature.properties.ZDBH);
          }
        }
      });
    }

    const result = Array.from(nodeCodes).filter(
      (code) => code && code.trim() !== ""
    );
    console.log("提取到的管点编号:", result);
    return result;
  } catch (error) {
    console.error("提取管点编号失败:", error);
    return [];
  }
};

/**
 * 从地图选择管点
 * @returns Promise<AnalysisPoint> 返回选择的管点信息
 */
const selectPointFromMap = (): Promise<AnalysisPoint> => {
  return new Promise((resolve, reject) => {
    try {
      const map = AppMaplibre.getMap();

      if (!map) {
        throw new Error("地图实例未初始化");
      }

      // 修改鼠标样式提示用户可以点击
      map.getCanvas().style.cursor = "crosshair";

      // 绑定单次点击事件
      map.once("click", (e: any) => {
        try {
          // 恢复默认鼠标样式
          map.getCanvas().style.cursor = "";

          // 查询点击位置的管点要素
          const features = map.queryRenderedFeatures(e.point, {
            layers: ["mvt_pipeNode"],
          });

          if (features.length === 0) {
            throw new Error("请点击管点位置");
          }

          const feature: any = features[0];
          const gxddh = feature.properties?.gxddh;

          if (!gxddh) {
            throw new Error("该管点缺少编号信息");
          }
          const point: AnalysisPoint = {
            lng: feature.geometry.coordinates[0] as number,
            lat: feature.geometry.coordinates[1] as number,
            gxddh: gxddh,
          };

          resolve(point);
        } catch (error) {
          // 恢复默认鼠标样式
          map.getCanvas().style.cursor = "";
          reject(error);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * 处理选择起点
 */
const handleSelectStartPoint = async () => {
  isSelectingStartPoint.value = true;
  try {
    if (mapEngine.value === "cesium") {
      // 使用通用的Cesium节点选择函数
      selectCesiumPipeNode(
        "start",
        (selectedPoint: AnalysisPoint) => {
          startPoint.value = selectedPoint;

          // 添加起点临时图层显示和高亮
          addSelectedPointLayer(selectedPoint, "start", mapEngine.value);

          // 高亮选中的节点
          if (selectedPoint.gxddh) {
            const nodesToHighlight = [selectedPoint.gxddh];
            if (endPoint.value?.gxddh) {
              nodesToHighlight.push(endPoint.value.gxddh);
            }
            highlightCesiumNodes(nodesToHighlight);
          }

          isSelectingStartPoint.value = false;
        },
        (error: any) => {
          console.error("选择起点失败:", error);
          ElMessage.error(
            `${
              error instanceof Error ? error.message : "未知错误"
            }`
          );
          isSelectingStartPoint.value = false;
        }
      );
    } else {
      // MapLibre地图点选择
      ElMessage.info("请在2D地图上点击管点选择起点");

      const selectedPoint = await selectPointFromMap();
      startPoint.value = selectedPoint;

      // 添加起点临时图层显示
      addSelectedPointLayer(selectedPoint, "start", mapEngine.value);

      ElMessage.success(`已选择起点：${selectedPoint.gxddh}`);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error(
      `${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isSelectingStartPoint.value = false;
  }
};

/**
 * 处理选择终点
 */
const handleSelectEndPoint = async () => {
  isSelectingEndPoint.value = true;
  try {
    if (mapEngine.value === "cesium") {
      // 使用通用的Cesium节点选择函数
      selectCesiumPipeNode(
        "end",
        (selectedPoint: AnalysisPoint) => {
          endPoint.value = selectedPoint;

          // 添加终点临时图层显示和高亮
          addSelectedPointLayer(selectedPoint, "end", mapEngine.value);

          // 高亮选中的节点
          if (selectedPoint.gxddh) {
            const nodesToHighlight = [selectedPoint.gxddh];
            if (startPoint.value?.gxddh) {
              nodesToHighlight.push(startPoint.value.gxddh);
            }
            highlightCesiumNodes(nodesToHighlight);
          }

          isSelectingEndPoint.value = false;
        },
        (error: any) => {
          console.error("选择终点失败:", error);
          ElMessage.error(
            `选择终点失败：${
              error instanceof Error ? error.message : "未知错误"
            }`
          );
          isSelectingEndPoint.value = false;
        }
      );
    } else {
      // MapLibre地图点选择
      ElMessage.info("请在2D地图上点击管点选择终点");

      const selectedPoint = await selectPointFromMap();

      // 检查是否与起点相同
      if (startPoint.value && selectedPoint.gxddh === startPoint.value.gxddh) {
        throw new Error("终点不能与起点相同");
      }

      endPoint.value = selectedPoint;

      // 添加终点临时图层显示
      addSelectedPointLayer(selectedPoint, "end", mapEngine.value);

      ElMessage.success(`已选择终点：${selectedPoint.gxddh}`);
    }
  } catch (error) {
    console.error("选择终点失败:", error);
    ElMessage.error(
      `选择终点失败：${error instanceof Error ? error.message : "未知错误"}`
    );
  } finally {
    isSelectingEndPoint.value = false;
  }
};

/**
 * 处理分析操作
 */
const handleAnalysis = async () => {
  if (!startPoint.value || !endPoint.value) {
    ElMessage.warning("请先选择起点和终点");
    return;
  }

  if (!startPoint.value.gxddh || !endPoint.value.gxddh) {
    ElMessage.warning("起点或终点缺少管点编号信息");
    return;
  }

  isAnalyzing.value = true;
  try {
    // 调用连通性分析API
    const response = await queryConnectivity(
      startPoint.value.gxddh,
      endPoint.value.gxddh
    );
    const response2 = await queryConnectivity(
      endPoint.value.gxddh,
      startPoint.value.gxddh
    );
    let featureCollection;
    if(response.status === 200 && response.data.features) {
      featureCollection = response.data;
    } else if(response2.status === 200 && response2.data.features) {
      featureCollection = response2.data;
    }
    console.log("连通性分析API响应:", response);

    if (featureCollection) {

      if (
        featureCollection &&
        featureCollection.features &&
        featureCollection.features.length > 0
      ) {
        // 1. 在地图上显示分析结果
        if (mapEngine.value === "cesium") {
          addCesiumConnectivityResult(featureCollection);
        } else {
          addConnectivityResultLayer(featureCollection);
        }

        // 2. 提取管点编号并去重
        const nodeCodes = extractNodeCodesFromResult(featureCollection);

        if (nodeCodes.length > 0) {
          // 3. 查询管点详细信息
          const nodeResponse = await pointListByBms(nodeCodes);
          console.log("管点查询响应:", nodeResponse);

          if (nodeResponse.code === 200 && nodeResponse.data) {
            // 4. 筛选有附属物的管点
            const filteredNodes = nodeResponse.data.filter(
              (node: any) =>
                node.fsw && node.fsw !== "无" && node.fsw.trim() !== ""
            );

            // 5. 转换为表格数据格式
            const tableData: ConnectivityDeviceItem[] = filteredNodes.map(
              (node: any, index: number) => ({
                index: index + 1,
                gxddh: node.gxddh || "",
                gl: node.gl || "",
                sx: node.sx || "",
                fsw: node.fsw || "",
                szdl: node.szdl || "",
                dmgc: node.dmgc || 0,
                longitude: node.longitude || 0,
                latitude: node.latitude || 0,
              })
            );

            // 6. 更新表格显示
            deviceData.value = tableData;
            total.value = tableData.length;
            showDeviceTable.value = tableData.length > 0;
            currentPage.value = 1;

            analysisResult.value = `连通性分析完成，${
              featureCollection.features.length > 0
                ? "找到连通路径"
                : "未找到连通路径"
            }，相关附属物设备 ${tableData.length} 个`;

            ElMessage.success(
              `分析完成，找到 ${tableData.length} 个相关附属物设备`
            );
          } else {
            throw new Error("查询管点详细信息失败");
          }
        } else {
          analysisResult.value = "连通性分析完成，但未找到有效的管点信息";
          ElMessage.warning("未找到有效的管点信息");
        }
      } else {
        analysisResult.value = "两点不连通";
        deviceData.value = [];
        showDeviceTable.value = false;
        ElMessage.info("两点之间无连通路径");
      }

      showResult.value = true;
    } else {
      throw new Error(response?.data?.message || "分析失败");
    }
  } catch (error) {
    console.error("连通性分析失败:", error);
    ElMessage.error(
      `分析失败：${error instanceof Error ? error.message : "未知错误"}`
    );
    showResult.value = false;
    showDeviceTable.value = false;
  } finally {
    isAnalyzing.value = false;
  }
};

/**
 * 清除所有临时图层和分析结果
 */
const clearAllLayers = () => {
  try {
    if (mapEngine.value === "cesium") {
      // Cesium模式：清除entities和节点高亮
      clearCesiumAnalysisEntities();
      clearCesiumNodeHighlight();
      console.log("已清除Cesium模式下的分析结果");
    } else {
      // MapLibre模式：清除图层和数据源
      const map = AppMaplibre.getMap();

      if (!map) {
        console.warn("地图实例不存在，无法清除图层");
        return;
      }

      // 必须先删除图层，再删除数据源
      // 1. 先删除所有图层
      const layerIds = Object.values(TEMP_LAYER_IDS);
      layerIds.forEach((layerId) => {
        // 删除主图层
        if (map.getLayer(layerId)) {
          map.removeLayer(layerId);
          console.log(`已删除图层: ${layerId}`);
        }

        // 删除标签图层
        const labelLayerId = `${layerId}-label`;
        if (map.getLayer(labelLayerId)) {
          map.removeLayer(labelLayerId);
          console.log(`已删除标签图层: ${labelLayerId}`);
        }
      });

      // 2. 再删除所有数据源
      const sourceIds = Object.values(TEMP_LAYER_IDS);
      sourceIds.forEach((sourceId) => {
        if (map.getSource(sourceId)) {
          map.removeSource(sourceId);
          console.log(`已删除数据源: ${sourceId}`);
        }
      });

      console.log("已清除所有临时图层");
    }
  } catch (error) {
    console.error("清除临时图层失败:", error);
  }
};

/**
 * 处理清除操作
 */
const handleClear = () => {
  // 清除数据状态
  startPoint.value = null;
  endPoint.value = null;
  showResult.value = false;
  analysisResult.value = "";
  deviceData.value = [];
  showDeviceTable.value = false;
  currentPage.value = 1;

  // 清除地图图层
  clearAllLayers();
  clearPipeLocationHighlight(mapEngine.value);

  ElMessage.info("已清除");
};

/**
 * 处理页码变化
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

/**
 * 处理每页条数变化
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  // 切换每页显示数量时，如果当前页已经没有数据，则跳转到第一页
  if (Math.ceil(total.value / pageSize.value) < currentPage.value) {
    currentPage.value = 1;
  }
};

/**
 * 获取当前页数据
 */
const getCurrentPageData = () => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = Math.min(
    startIndex + pageSize.value,
    deviceData.value.length
  );
  return deviceData.value.slice(startIndex, endIndex);
};

/**
 * 处理设备定位操作（统一使用PipeLocation.ts的定位方式）
 */
const handleLocation = async (row: ConnectivityDeviceItem) => {
  try {
    console.log(`${mapEngine.value === "cesium" ? "3D" : "2D"}设备定位:`, row);
    // ElMessage.info("正在定位管点...");

    // 使用统一的PipeLocation定位方式
    await pipeLocationByBms([row.gxddh], mapEngine.value, "point", {
      autoFit: true,
      padding: 100,
      highlightColor: "#ff4444",
      pointRadius: 8,
    });

    // ElMessage.success("定位成功");
  } catch (error) {
    console.error("定位失败:", error);
    ElMessage.error("定位失败，请检查管点编码");
  }
};

/**
 * 组件卸载时清理资源
 */
onUnmounted(() => {
  clearAllLayers();

  if (mapEngine.value === "cesium") {
    // Cesium模式特有的清理
    clearCesiumAnalysisEntities();
    clearCesiumNodeHighlight();
  } else {
    // MapLibre模式的清理
    clearPipeLocationHighlight(mapEngine.value);
  }

  console.log("连通性分析组件已卸载，清理完成");
});
</script>

<style scoped lang="scss">
.connectivity-analysis {
  position: absolute;
  left: 10px;
  top: 10px;
  // width: 550px;
  // min-height: 130px;
  z-index: 1000;
}

.points-section {
  margin: 20px 0;
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 6px;

  .point-row {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .point-label {
    color: #2c3037;
    font-size: 14px;
    margin-right: 8px;
  }

  .point-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .point-text {
    color: #1890ff;
    font-size: 14px;
    font-weight: 500;
  }

  .coordinate-text {
    color: #666;
    font-size: 12px;
  }
}

.result-section {
  margin: 20px 0;
  color: #333333;
  padding: 5px 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  .result-item {
    margin-bottom: 10px;
  }
  .result-label {
    font-size: 14px;
    color: #333333;
    margin-right: 5px;
    margin-left: 10px;
  }

  .result-text {
    color: #333333;
    font-size: 14px;
    font-weight: 500;
  }
}
.success {
  background: #f1f8fe;
  border: 1px solid #92b7ff;
}

.error {
  background: #fef1f1;
  border: 1px solid #ff9292;
}
.table-section {
  margin-top: 20px;

  .table-label {
    color: #2c3037;
    font-size: 14px;
    margin-bottom: 8px;
    display: block;
  }

  .el-table {
    margin-bottom: 15px;
    font-size: 13px;

    :deep(th) {
      background-color: #f2f6fc;
      color: #606266;
      font-weight: 500;
      font-size: 13px;
    }

    :deep(td) {
      padding: 6px 0;
    }

  }
}
</style>
