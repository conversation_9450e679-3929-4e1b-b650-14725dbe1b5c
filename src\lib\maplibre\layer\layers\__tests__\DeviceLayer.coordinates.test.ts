/**
 * @fileoverview 设备图层经纬度验证功能测试
 * @description 测试DeviceLayer的经纬度正则表达式验证功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock设备数据
const mockValidDevices = [
  {
    id: '1',
    name: '设备1',
    longitude: '116.123456',
    latitude: '39.123456'
  },
  {
    id: '2', 
    name: '设备2',
    longitude: 116.654321,
    latitude: 39.654321
  },
  {
    id: '3',
    name: '设备3',
    longitude: '-73.935242',
    latitude: '40.730610'
  }
];

const mockInvalidDevices = [
  {
    id: '4',
    name: '设备4-无效经度',
    longitude: '181.123456', // 超出范围
    latitude: '39.123456'
  },
  {
    id: '5',
    name: '设备5-无效纬度',
    longitude: '116.123456',
    latitude: '91.123456' // 超出范围
  },
  {
    id: '6',
    name: '设备6-空值',
    longitude: '',
    latitude: ''
  },
  {
    id: '7',
    name: '设备7-null值',
    longitude: null,
    latitude: null
  },
  {
    id: '8',
    name: '设备8-非数字',
    longitude: 'abc',
    latitude: 'def'
  }
];

// Mock DeviceLayer类的validateAndParseCoordinates方法
class MockDeviceLayer {
  /**
   * @description 验证并解析经纬度坐标
   * @param longitude - 经度值（字符串或数字）
   * @param latitude - 纬度值（字符串或数字）
   * @returns 有效的坐标数组 [lng, lat] 或 null
   */
  validateAndParseCoordinates(longitude: any, latitude: any): [number, number] | null {
    // 经度正则表达式：-180 到 180，支持小数
    const longitudeRegex = /^-?(?:180(?:\.0+)?|(?:1[0-7]\d|[1-9]?\d)(?:\.\d+)?)$/;
    
    // 纬度正则表达式：-90 到 90，支持小数
    const latitudeRegex = /^-?(?:90(?:\.0+)?|(?:[1-8]?\d)(?:\.\d+)?)$/;
    
    // 转换为字符串进行验证
    const lngStr = String(longitude).trim();
    const latStr = String(latitude).trim();
    
    // 检查是否为空或无效值
    if (!lngStr || !latStr || lngStr === 'null' || latStr === 'null' || 
        lngStr === 'undefined' || latStr === 'undefined' || 
        lngStr === '' || latStr === '') {
      return null;
    }
    
    // 正则表达式验证
    if (!longitudeRegex.test(lngStr) || !latitudeRegex.test(latStr)) {
      return null;
    }
    
    // 转换为数字
    const lng = parseFloat(lngStr);
    const lat = parseFloat(latStr);
    
    // 数值范围验证（双重保险）
    if (isNaN(lng) || isNaN(lat) || lng < -180 || lng > 180 || lat < -90 || lat > 90) {
      return null;
    }
    
    return [lng, lat];
  }
}

describe('DeviceLayer 经纬度验证功能测试', () => {
  let deviceLayer: MockDeviceLayer;

  beforeEach(() => {
    deviceLayer = new MockDeviceLayer();
  });

  describe('有效经纬度验证', () => {
    it('应该接受有效的字符串格式经纬度', () => {
      const result = deviceLayer.validateAndParseCoordinates('116.123456', '39.123456');
      expect(result).toEqual([116.123456, 39.123456]);
    });

    it('应该接受有效的数字格式经纬度', () => {
      const result = deviceLayer.validateAndParseCoordinates(116.654321, 39.654321);
      expect(result).toEqual([116.654321, 39.654321]);
    });

    it('应该接受负数经纬度', () => {
      const result = deviceLayer.validateAndParseCoordinates('-73.935242', '40.730610');
      expect(result).toEqual([-73.935242, 40.730610]);
    });

    it('应该接受边界值', () => {
      // 经度边界值
      expect(deviceLayer.validateAndParseCoordinates('180', '0')).toEqual([180, 0]);
      expect(deviceLayer.validateAndParseCoordinates('-180', '0')).toEqual([-180, 0]);
      
      // 纬度边界值
      expect(deviceLayer.validateAndParseCoordinates('0', '90')).toEqual([0, 90]);
      expect(deviceLayer.validateAndParseCoordinates('0', '-90')).toEqual([0, -90]);
    });

    it('应该接受整数经纬度', () => {
      const result = deviceLayer.validateAndParseCoordinates('116', '39');
      expect(result).toEqual([116, 39]);
    });

    it('应该接受小数点后多位的经纬度', () => {
      const result = deviceLayer.validateAndParseCoordinates('116.123456789', '39.987654321');
      expect(result).toEqual([116.123456789, 39.987654321]);
    });
  });

  describe('无效经纬度验证', () => {
    it('应该拒绝超出范围的经度', () => {
      expect(deviceLayer.validateAndParseCoordinates('181', '39')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('-181', '39')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('200', '39')).toBeNull();
    });

    it('应该拒绝超出范围的纬度', () => {
      expect(deviceLayer.validateAndParseCoordinates('116', '91')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('116', '-91')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('116', '100')).toBeNull();
    });

    it('应该拒绝空值和null值', () => {
      expect(deviceLayer.validateAndParseCoordinates('', '')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates(null, null)).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates(undefined, undefined)).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('null', 'null')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('undefined', 'undefined')).toBeNull();
    });

    it('应该拒绝非数字字符串', () => {
      expect(deviceLayer.validateAndParseCoordinates('abc', '39')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('116', 'def')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('abc', 'def')).toBeNull();
    });

    it('应该拒绝包含特殊字符的字符串', () => {
      expect(deviceLayer.validateAndParseCoordinates('116.123.456', '39')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('116', '39.123.456')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('116°', '39°')).toBeNull();
    });

    it('应该拒绝科学计数法', () => {
      expect(deviceLayer.validateAndParseCoordinates('1.16e2', '39')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('116', '3.9e1')).toBeNull();
    });
  });

  describe('边界情况测试', () => {
    it('应该处理带空格的字符串', () => {
      const result = deviceLayer.validateAndParseCoordinates(' 116.123456 ', ' 39.123456 ');
      expect(result).toEqual([116.123456, 39.123456]);
    });

    it('应该处理零值', () => {
      const result = deviceLayer.validateAndParseCoordinates('0', '0');
      expect(result).toEqual([0, 0]);
    });

    it('应该处理小数点开头的数字', () => {
      const result = deviceLayer.validateAndParseCoordinates('.5', '.5');
      expect(result).toEqual([0.5, 0.5]);
    });

    it('应该拒绝只有小数点的字符串', () => {
      expect(deviceLayer.validateAndParseCoordinates('.', '.')).toBeNull();
    });

    it('应该拒绝多个负号', () => {
      expect(deviceLayer.validateAndParseCoordinates('--116', '39')).toBeNull();
      expect(deviceLayer.validateAndParseCoordinates('116', '--39')).toBeNull();
    });
  });

  describe('数据处理流程测试', () => {
    it('应该正确处理混合的有效和无效数据', () => {
      const mixedData = [...mockValidDevices, ...mockInvalidDevices];
      const validCoordinates: Array<[number, number]> = [];
      const invalidCount = { count: 0 };

      mixedData.forEach(item => {
        const coordinates = deviceLayer.validateAndParseCoordinates(item.longitude, item.latitude);
        if (coordinates) {
          validCoordinates.push(coordinates);
        } else {
          invalidCount.count++;
        }
      });

      // 应该有3个有效坐标（来自mockValidDevices）
      expect(validCoordinates).toHaveLength(3);
      expect(validCoordinates).toEqual([
        [116.123456, 39.123456],
        [116.654321, 39.654321],
        [-73.935242, 40.730610]
      ]);

      // 应该有5个无效坐标（来自mockInvalidDevices）
      expect(invalidCount.count).toBe(5);
    });

    it('应该正确统计跳过的无效数据', () => {
      const invalidData = mockInvalidDevices;
      let skippedCount = 0;

      invalidData.forEach(item => {
        const coordinates = deviceLayer.validateAndParseCoordinates(item.longitude, item.latitude);
        if (!coordinates) {
          skippedCount++;
        }
      });

      expect(skippedCount).toBe(mockInvalidDevices.length);
    });
  });

  describe('正则表达式精确性测试', () => {
    it('应该精确匹配经度范围', () => {
      // 测试边界值
      expect(deviceLayer.validateAndParseCoordinates('180.0', '0')).toEqual([180, 0]);
      expect(deviceLayer.validateAndParseCoordinates('180.00000', '0')).toEqual([180, 0]);
      expect(deviceLayer.validateAndParseCoordinates('180.1', '0')).toBeNull(); // 超出范围
      
      expect(deviceLayer.validateAndParseCoordinates('-180.0', '0')).toEqual([-180, 0]);
      expect(deviceLayer.validateAndParseCoordinates('-180.1', '0')).toBeNull(); // 超出范围
    });

    it('应该精确匹配纬度范围', () => {
      // 测试边界值
      expect(deviceLayer.validateAndParseCoordinates('0', '90.0')).toEqual([0, 90]);
      expect(deviceLayer.validateAndParseCoordinates('0', '90.00000')).toEqual([0, 90]);
      expect(deviceLayer.validateAndParseCoordinates('0', '90.1')).toBeNull(); // 超出范围
      
      expect(deviceLayer.validateAndParseCoordinates('0', '-90.0')).toEqual([0, -90]);
      expect(deviceLayer.validateAndParseCoordinates('0', '-90.1')).toBeNull(); // 超出范围
    });
  });
});
