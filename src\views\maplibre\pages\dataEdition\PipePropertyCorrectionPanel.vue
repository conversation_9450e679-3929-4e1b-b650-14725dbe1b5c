<!--
 * @Description: 纠错审核面板
 * @Date: 2024-01-16
 * @Author: AI Assistant
 * @LastEditTime: 2024-05-24
 -->
<template>
  <page-card
    class="correction-review-panel"
    title="纠错审核"
    @closeCard="closeCard"
  >
    <!-- 筛选区域 -->
    <el-row class="filter-section">
      <el-radio-group v-model="filterStatus" @change="handleRefresh">
        <el-radio-button label="0">待审核</el-radio-button>
        <el-radio-button label="1">已通过</el-radio-button>
        <el-radio-button label="2">已驳回</el-radio-button>
      </el-radio-group>
    </el-row>
    <!-- 纠错信息列表表格 -->
    <el-row class="table-section">
      <el-col :span="24">
        <el-table
          :data="correctionList"
          v-loading="loading"
          empty-text="暂无相关纠错信息"
          style="width: 100%"
          class="routeCt h-80"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            :index="getTableIndex"
          />
          <el-table-column
            prop="gxddh"
            label="管点编码"
            width="130"
            show-overflow-tooltip
          />
          <el-table-column
            prop="status"
            label="审核状态"
            width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag :type="formatStatus(row.status).tagType" size="small">{{
                formatStatus(row.status).text
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="type"
            label="错误类型"
            width="120"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ PIPE_ERROR_TYPE_LABEL_MAP[row.type] || row.type }}
            </template>
          </el-table-column>
          <el-table-column
            prop="updateValue"
            label="建议更新值"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="remark"
            label="错误描述"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="filterStatus === '2'"
            prop="rejectReason"
            label="驳回原因"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            prop="reporterName"
            label="上报人"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="createTime"
            label="上报时间"
            width="160"
            show-overflow-tooltip
          />
          <el-table-column
            label="操作"
            width="220"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleLocate(row)"
                title="定位到地图"
              >
                定位
              </el-button>
              <template v-if="row.status === '0'">
                <el-button
                  type="text"
                  style="color: #1966ff"
                  @click="handleApprove(row)"
                  title="通过审核"
                >
                  通过
                </el-button>
                <el-button
                  type="text"
                  style="color: #ff7373"
                  @click="handleReject(row)"
                  title="驳回申请"
                >
                  驳回
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center mt-5">
          <div>
            <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
          </div>
          <!-- 分页组件 -->
          <el-pagination
            v-if="total > 0"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :total="total"
            :pager-count="5"
            layout="prev, pager, next, jumper"
            class="pagination"
            background
            small
          />
        </div>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Position, Check, Close } from "@element-plus/icons-vue";
import PageCard from "@/components/PageCard.vue";
import PipeCorrectionApi, {
  reviewPipeCorrection,
  AttributeErrorStatus,
  PIPE_ERROR_TYPE_LABEL_MAP,
  type AttributeErrorPageVo,
} from "@/api/pipeCorrection";
import { pipeNodeDetail } from "@/api/pipeNode";
import type { GsPtVo } from "@/api/pipeNode";

// 地图相关
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import type { Map as MapLibreMap } from "maplibre-gl";

const CORRECTION_NODE_LAYER_IDS = {
  SOURCE: "correction-node-highlight-source",
  LAYER: "correction-node-highlight-layer",
} as const;

interface Emits {
  (e: "close"): void;
}
const emit = defineEmits<Emits>();

const loading = ref(false);
const correctionList = ref<AttributeErrorPageVo[]>([]);
const currentPage = ref<number>(1);
const pageSize = ref<number>(10);
const total = ref<number>(0);
const pageSizes = ref<number[]>([5, 10, 20, 50]);
const filterStatus = ref<string>("0"); // 默认筛选待审核

let map: MapLibreMap | null = null;

onMounted(async () => {
  try {
    map = AppMaplibre.getMap();
  } catch (error) {
    console.warn("获取地图实例失败:", error);
  }
  await handleRefresh();
});

onUnmounted(() => {
  clearCorrectionNodeHighlight();
});

const clearCorrectionNodeHighlight = (): void => {
  try {
    if (!map) return;
    if (map.getLayer(CORRECTION_NODE_LAYER_IDS.LAYER)) {
      map.removeLayer(CORRECTION_NODE_LAYER_IDS.LAYER);
    }
    if (map.getSource(CORRECTION_NODE_LAYER_IDS.SOURCE)) {
      map.removeSource(CORRECTION_NODE_LAYER_IDS.SOURCE);
    }
  } catch (error) {
    console.error("清除高亮图层失败:", error);
  }
};

const addCorrectionNodeHighlight = (geojsonData: any): void => {
  try {
    if (!map) {
      console.warn("地图实例不可用，无法添加高亮图层");
      return;
    }
    clearCorrectionNodeHighlight();

    map.addSource(CORRECTION_NODE_LAYER_IDS.SOURCE, {
      type: "geojson",
      data: {
        type: "FeatureCollection",
        features: [{ type: "Feature", geometry: geojsonData, properties: {} }],
      },
    });

    map.addLayer({
      id: CORRECTION_NODE_LAYER_IDS.LAYER,
      type: "circle",
      source: CORRECTION_NODE_LAYER_IDS.SOURCE,
      paint: {
        "circle-radius": 10,
        "circle-color": "#ff4d4f",
        "circle-opacity": 0.8,
        "circle-stroke-width": 2,
        "circle-stroke-color": "#ffffff",
      },
    });
  } catch (error) {
    console.error("添加高亮图层失败:", error);
  }
};

const getTableIndex = (index: number): number => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};

const handleCurrentChange = (page: number): void => {
  currentPage.value = page;
  handleRefresh();
};

const handleSizeChange = (size: number): void => {
  pageSize.value = size;
  currentPage.value = 1;
  handleRefresh();
};

const closeCard = (): void => {
  clearCorrectionNodeHighlight();
  emit("close");
};

const formatStatus = (
  status: string
): { text: string; tagType: "warning" | "success" | "info" | "danger" } => {
  switch (status) {
    case "0":
      return { text: "待审核", tagType: "warning" };
    case "1":
      return { text: "已通过", tagType: "success" };
    case "2":
      return { text: "已驳回", tagType: "danger" };
    case "3":
      return { text: "已处理", tagType: "info" };
    default:
      return { text: "未知", tagType: "info" };
  }
};

const handleRefresh = async (): Promise<void> => {
  try {
    loading.value = true;
    clearCorrectionNodeHighlight();
    const response = await PipeCorrectionApi.getAttributeErrorPage({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      status: filterStatus.value,
    });

    if (response.code === 200 && response.data) {
      const pageInfo = response.data;
      correctionList.value = pageInfo.list || [];
      total.value = pageInfo.totalCount || 0;
      if (total.value === 0) {
        ElMessage.info("当前筛选条件下无数据");
      }
    } else {
      ElMessage.error(`查询失败: ${response.msg}`);
      correctionList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error("查询纠错信息异常:", error);
    ElMessage.error(
      `查询异常: ${error instanceof Error ? error.message : "未知错误"}`
    );
    correctionList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

const handleLocate = async (
  correction: AttributeErrorPageVo
): Promise<void> => {
  try {
    if (!map) {
      ElMessage.error("地图实例不可用，无法定位");
      return;
    }
    const res = await pipeNodeDetail(correction.ptId);
    if (res.code !== 200 || !res.data || !res.data.geojson) {
      ElMessage.error("获取管点位置信息失败");
      return;
    }
    const geometry = JSON.parse(res.data.geojson);
    if (!geometry || !geometry.coordinates) {
      ElMessage.error("管点位置数据不完整，无法定位");
      return;
    }

    addCorrectionNodeHighlight(geometry);

    map.flyTo({
      center: geometry.coordinates,
      zoom: 18,
      duration: 1500,
    });

    ElMessage.success(`已定位到管点：${correction.gxddh || "未知编码"}`);
  } catch (error) {
    console.error("定位管点失败:", error);
    ElMessage.error(
      `定位失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};

const handleApprove = async (
  correction: AttributeErrorPageVo
): Promise<void> => {
  try {
    await ElMessageBox.confirm(
      `确定要通过对管点 [${correction.gxddh}] 的纠错申请吗？`,
      "确认审核",
      {
        confirmButtonText: "确定通过",
        cancelButtonText: "取消",
        type: "success",
      }
    );

    loading.value = true;
    const response = await reviewPipeCorrection(
      correction.id,
      correction.ptId,
      correction.type,
      correction.updateValue,
      AttributeErrorStatus.APPROVED
    );

    if (response.code === 200) {
      ElMessage.success(`纠错申请 [${correction.gxddh}] 已审核通过`);
      await handleRefresh();
    } else {
      ElMessage.error(`审核失败: ${response.msg}`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("审核操作异常:", error);
      ElMessage.error(
        `审核异常: ${error instanceof Error ? error.message : "未知错误"}`
      );
    }
  } finally {
    loading.value = false;
  }
};

const handleReject = async (
  correction: AttributeErrorPageVo
): Promise<void> => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      "请输入驳回原因",
      "驳回审核",
      {
        confirmButtonText: "确定驳回",
        cancelButtonText: "取消",
        inputPattern: /.+/,
        inputErrorMessage: "必须填写驳回原因",
      }
    );

    loading.value = true;
    const response = await reviewPipeCorrection(
      correction.id,
      correction.ptId,
      correction.type,
      correction.updateValue,
      AttributeErrorStatus.REJECTED,
      reason
    );

    if (response.code === 200) {
      ElMessage.success(`纠错申请 [${correction.gxddh}] 已被驳回`);
      await handleRefresh();
    } else {
      ElMessage.error(`驳回失败: ${response.msg}`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("驳回操作异常:", error);
      ElMessage.error(
        `驳回操作异常: ${error instanceof Error ? error.message : "未知错误"}`
      );
    }
  } finally {
    loading.value = false;
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
</script>

<style scoped lang="scss">
.correction-review-panel {
  position: absolute;
  left: 10px;
  top: 150px;
  width: 724px;
  max-height: 85vh;
  z-index: 999;
}

.filter-section {
  margin-bottom: 16px;
}

.table-section {
  :deep(.el-table) {
    border-radius: 6px;
    overflow: hidden;

    .el-table__header {
      background: #f8f9fa;
    }

    .el-button {
      margin-right: 4px;
      border-radius: 4px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

@media (max-width: 1200px) {
  .correction-review-panel {
    left: 10px;
    width: 90vw;
    max-width: 1200px;
  }
}
</style>
