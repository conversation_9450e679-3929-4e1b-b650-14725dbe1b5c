<!--
 * @Description: 坐标拾取组件
 * @Date: 2022-04-22 08:54:28
 * @Author: GISerZ
 * @LastEditor: 项目开发团队
 * @LastEditTime: 2023-08-10 15:00:00
-->
<template>
  <custom-card
    @closeHandler="close"
    v-show="mainItem.show"
    :main-item="mainItem"
    :title="mainItem.title ?? (mainItem.title = '空间量算')"
  >
    <el-row type="flex" justify="space-between" style="padding-bottom: 10px">
      <el-input
        placeholder="搜索书签名称..."
        v-model="queryForm.name"
        class="search_ipt"
        @blur="blurHandler"
        @keyup.enter="searchEvents"
        clearable
        @clear="searchEvents"
      >
        <template #suffix>
          <el-icon class="search_icon" @click="searchEvents">
            <Search />
          </el-icon>
        </template>
      </el-input>
      <el-button type="primary" class="primary-btn" @click="addNewBookMark"
        >添加</el-button
      >
    </el-row>
    <!-- <div style="padding: 0 10px 5px; font-size: 12px; color: #999">
      共 {{ total }} 个{{
        getCurrentMapType() === "cesium" ? "三维" : "二维"
      }}视角书签
      <span v-if="searchData.trim()" style="margin-left: 10px">
        （搜索结果）
      </span>
    </div> -->
    <el-table
      :data="filteredBookMarkList"
      height="250"
      style="width: 100%"
      class="routeCt"
      :row-class-name="tableRowClassName"
    >
      <el-table-column prop="name" label="视角名称"> </el-table-column>
      <el-table-column label="操作" min-width="60">
        <template v-slot="scope">
          <el-button
            type="text"
            style="color: #1966ff"
            @click.native.prevent="locBookMark(scope.row)"
          >
            定位
          </el-button>
          <el-button
            type="text"
            style="color: #ff7373"
            @click.native.prevent="removeBookMarkById(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex justify-between items-center mt-5">
      <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :total="total"
        :pager-count="5"
        layout="prev, pager, next"
        class="pagination"
        background
        small
      ></el-pagination>
    </div>
  </custom-card>
  <el-dialog
    custom-class
    title="新建视角书签"
    v-model="addImgDigVisible"
    @close="closeDialog"
    :before-close="closeDialog"
    width="400px"
  >
    <i
      class="close-icon close-icon2 iconfont icon-guanbi-o"
      @click="closeDialog"
    ></i>
    <el-form label-position="left" label-width="90px" :model="programInfo">
      <el-form-item label="视角名称">
        <el-input
          v-model="programInfo.name"
          placeholder="请输入书签名称"
          maxlength="20"
          show-word-limit
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <div style="font-size: 12px; color: #999; line-height: 1.5">
          <div style="margin-bottom: 5px">
            <i class="el-icon-info" style="margin-right: 5px"></i>
            将保存当前{{
              getCurrentMapType() === "cesium" ? "三维" : "二维"
            }}地图的视角信息
          </div>
          <div style="color: #666">
            包括位置坐标{{
              getCurrentMapType() === "cesium"
                ? "、相机高度和角度"
                : "和缩放级别"
            }}
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <ul class="btn_group">
        <button class="small_btn ripple" @click="addEvents">新 建</button>
        <button class="small_btn ripple" @click="closeDialog">关 闭</button>
      </ul>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import type { IBookMark, QueryForm } from "@/stores/BookMarkStore";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { AppCesium } from "@/lib/cesium/AppCesium";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { anglePage, angleAdd, angleDelete } from "@/api/bookmark";
const initQueryForm = () => {
  return {
    name: "",
    type: "",
    pageNum: 1,
    pageSize: 10,
  };
};
const queryForm = ref<QueryForm>(initQueryForm());
/**
 * 组件属性定义
 */
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});

/**
 * 获取当前地图类型
 */
const getCurrentMapType = (): "maplibre" | "cesium" => {
  return props.mainItem.type === "cesium" ? "cesium" : "maplibre";
};

const programInfo: IBookMark = reactive({
  id: "",
  name: "",
  type: getCurrentMapType(),
  visualAngle: {
    lng: 0,
    lat: 0,
  },
});

const addImgDigVisible = ref(false);
const searchData = ref("");

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);
const pageSizes = ref([5, 10, 20, 50]);
const total = ref(0);

// 分页事件处理函数
/**
 * 处理页码变更
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  queryForm.value.pageNum = page;
  getList();
};

/**
 * 处理页面大小变更
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  queryForm.value.pageSize = size;
  currentPage.value = 1;
  queryForm.value.pageNum = 1;
  getList();
};

const filteredBookMarkList = ref([]);
/**
 * 获取当前地图视角
 */
const getCurrentMapView = (): IBookMark["visualAngle"] => {
  const mapType = getCurrentMapType();
  if (mapType === "cesium") {
    try {
      const viewer = AppCesium.getInstance().getViewer();
      const camera = viewer.camera;

      // 获取相机位置
      const { Cesium } = BC.Namespace;
      const cartographic = Cesium.Cartographic.fromCartesian(camera.position);

      return {
        lng: Number(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6)),
        lat: Number(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6)),
        alt: Number(cartographic.height.toFixed(2)),
        zoom: undefined, // Cesium使用高度而非缩放级别
        heading: Number(Cesium.Math.toDegrees(camera.heading).toFixed(2)),
        pitch: Number(Cesium.Math.toDegrees(camera.pitch).toFixed(2)),
      };
    } catch (error) {
      console.error("获取Cesium视角失败:", error);
      ElMessage.error("获取当前视角失败");
      throw error;
    }
  } else {
    try {
      const map = AppMaplibre.getMap();
      const center = map.getCenter();
      const zoom = map.getZoom();

      return {
        lng: Number(center.lng.toFixed(6)),
        lat: Number(center.lat.toFixed(6)),
        zoom: Number(zoom.toFixed(2)),
        alt: undefined,
        heading: undefined,
        pitch: undefined,
      };
    } catch (error) {
      console.error("获取MapLibre视角失败:", error);
      ElMessage.error("获取当前视角失败");
      throw error;
    }
  }
};

/**
 * 定位到指定书签
 */
const locBookMark = async (bookmark: IBookMark) => {
  try {
    const visualAngle: any = JSON.parse(bookmark.visualAngle as any);
    if (bookmark.type === "cesium") {
      const viewer = AppCesium.getInstance().getViewer();
      const { Cesium } = BC.Namespace;

      // 创建目标位置
      const destination = Cesium.Cartesian3.fromDegrees(
        visualAngle.lng,
        visualAngle.lat,
        visualAngle.alt || 1000
      );

      // 设置相机参数
      const orientation: any = {};
      if (visualAngle.heading !== undefined) {
        orientation.heading = Cesium.Math.toRadians(visualAngle.heading);
      }
      if (visualAngle.pitch !== undefined) {
        orientation.pitch = Cesium.Math.toRadians(visualAngle.pitch);
      }

      // 飞行到目标位置
      await viewer.camera.flyTo({
        destination: destination,
        orientation: orientation,
        duration: 2.0,
      });

      // ElMessage.success(`已定位到书签"${bookmark.name}"`);
    } else {
      const map = AppMaplibre.getMap();

      // 飞行到目标位置
      map.flyTo({
        center: [visualAngle.lng, visualAngle.lat],
        zoom: visualAngle.zoom || 14,
        duration: 2000,
      });

      // ElMessage.success(`已定位到书签"${bookmark.name}"`);
    }
  } catch (error) {
    console.error("定位到书签失败:", error);
    ElMessage.error(`定位到书签"${bookmark.name}"失败`);
  }
};

/**
 * 删除书签
 */
const removeBookMarkById = (id: string) => {
  ElMessageBox.confirm("确定要删除当前视角吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox",
  }).then(() => {
    angleDelete(id)
      .then((res) => {
        if (res.code == 200) {
          ElMessage({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          getList();
        }
      })
      .catch((error) => {
        console.log(error);
      });
  });
};

/**
 * 关闭组件
 */
const close = () => {
  useDialogStore().closeDialog("BookMark");
};

/**
 * 搜索事件
 */
const searchEvents = () => {
  // 搜索时重置分页
  currentPage.value = 1;
  queryForm.value.pageNum = 1;
  getList();
};

/**
 * 添加新书签
 */
const addNewBookMark = () => {
  try {
    // 获取当前视角
    const currentView = getCurrentMapView();
    // 更新表单数据
    programInfo.visualAngle = currentView;
    programInfo.type = getCurrentMapType();
    // programInfo.id = ""; // 重新生成ID

    // 显示对话框
    addImgDigVisible.value = true;
  } catch (error) {
    console.error("准备添加书签失败:", error);
  }
};

/**
 * 搜索框失焦事件
 */
const blurHandler = (e: any) => {
  // 失焦时触发搜索，重置分页
  currentPage.value = 1;
  queryForm.value.pageNum = 1;
  getList();
};

/**
 * 关闭添加对话框
 */
const closeDialog = () => {
  addImgDigVisible.value = false;
  // 重置表单
  programInfo.name = "";
};

/**
 * 确认添加书签
 */
const addEvents = async () => {
  if (!programInfo.name.trim()) {
    ElMessage.warning("请输入书签名称");
    return;
  }

  try {
    // 创建书签副本以避免响应式问题
    const bookmarkToAdd: IBookMark = {
      id: programInfo.id,
      name: programInfo.name.trim(),
      type: programInfo.type,
      visualAngle: JSON.stringify({ ...programInfo.visualAngle }) as any,
    };
    const result = await angleAdd(bookmarkToAdd);
    if (result.code === 200) {
      addImgDigVisible.value = false;
      ElMessage.success("书签添加成功");
      await getList();
    }
    // 重置表单
    programInfo.name = "";
  } catch (error) {
    console.error("添加书签失败:", error);
    ElMessage.error("添加书签失败");
  }
};
// 列表数据
const getList = async () => {
  try {
    queryForm.value.type = getCurrentMapType();
    const result = await anglePage(queryForm.value);
    filteredBookMarkList.value = result.data.list || [];
    total.value = result.data.totalCount || 0;
    currentPage.value = queryForm.value.pageNum;
  } catch (error) {
    console.error("获取书签列表失败:", error);
    filteredBookMarkList.value = [];
    total.value = 0;
  }
};

// 初始化分页参数
pageSize.value = queryForm.value.pageSize;
currentPage.value = queryForm.value.pageNum;

getList();
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
/**
 * 组件加载时的初始化
 */
onMounted(async () => {
  console.log("地图书签组件已加载，当前地图类型:", getCurrentMapType());
});

/**
 * 监听主组件显示状态变化
 */
watch(
  () => props.mainItem.show,
  (newShow) => {
    if (newShow) {
      // 组件显示时可以执行一些初始化操作
      console.log("书签组件已显示");
    }
  }
);
</script>

<style lang="scss" scoped>
.search_ipt {
  width: 200px;
  height: 36px;
}

.search_icon {
  cursor: pointer;
  color: #909399;
  transition: color 0.3s;

  &:hover {
    color: #409eff;
  }
}

// .upload {
//   color: #409eff;
//   font-weight: 500;
// }

// .upload:hover {
//   color: #66b1ff;
// }

:deep(.el-table) {
  .el-table__body-wrapper {
    max-height: 250px;
    overflow-y: auto;
  }
}

:deep(.el-dialog) {
  .el-form-item__label {
    color: #606266;
    font-weight: 500;
  }

  .el-form-item__content {
    line-height: 1.4;
  }
}

.close-icon2 {
  position: absolute;
  top: 15px;
  right: 15px;
  cursor: pointer;
  font-size: 16px;
  color: #909399;

  &:hover {
    color: #409eff;
  }
}

.dialog-footer {
  text-align: center;

  .btn_group {
    display: inline-flex;
    gap: 10px;
    list-style: none;
    padding: 0;
    margin: 0;

    .small_btn {
      padding: 8px 20px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #fff;
      color: #606266;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;

      &:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }

      &:first-child {
        background: #409eff;
        color: #fff;
        border-color: #409eff;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
}
</style>
