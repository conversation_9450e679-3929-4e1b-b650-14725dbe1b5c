// import { LayerManager } from './layer/layerManager';

import { fetchDeviceSubTypes } from '@/utils/deviceUtils';
import { LayerManager } from './layer/LayerManager';
import { RoamingManager } from './roam/RoamingManager';
import RoamTool from './roam/RoamTool';

/**
 * @class AppCesium
 * @description Cesium应用核心管理类（优化版本）
 * @details 提供Cesium应用的单例管理，支持完整的资源清理和状态重置
 * @version 2.0.0 - 增强清理和重置功能
 */
export class AppCesium {
  private _baseMapUrl = import.meta.env.VITE_XHR_MAP;
  private _viewer: BC.Viewer | undefined = undefined;
  private _measureUtil: BC.Measure | undefined = undefined;
  private _plotUtil: BC.Plot | undefined = undefined;
  private _roamingManager: RoamingManager | undefined = undefined;
  private _clipUtil: BC.ClippingTileset | undefined = undefined;
  private _clipTerrain: BC.ClippingTerrain | undefined = undefined;
  private _roamTool: RoamTool | undefined = undefined;
  // private _center: BC.Position = new BC.Position(106.47774, 29.67838, 649.06, 12.86, -31.16); // 礼嘉
  // private _center: BC.Position = new BC.Position(101.627578, 26.598, 2294.76, 0, -60); // 攀枝花
  //系统默认视角
  private _center: BC.Position = new BC.Position(
    103.579582,
    29.299331,
    28246.27,
    0,
    -71.58
  ); // 沙湾区
  // private _center: BC.Position = new BC.Position(105.492562, 30.308564, 3950.53, 0, -35.5);
  private _layerManager: LayerManager | undefined = undefined;
  private static _instance: AppCesium;

  /** @description 实例初始化状态标志 */
  private _initialized: boolean = false;

  /** @description 清理状态标志 */
  private _isDestroyed: boolean = false;

  get center() {
    return this._center;
  }

  /**
   * @description 获取单例实例
   * @returns AppCesium实例
   */
  static getInstance(): AppCesium {
    if (!this._instance) {
      this._instance = new AppCesium();
    }
    return this._instance;
  }

  /**
   * @description 重置单例实例
   * @details 强制重新创建单例实例，用于引擎切换
   */
  static resetInstance(): void {
    console.log('🔄 [AppCesium] 重置单例实例...');
    
    try {
      // 如果存在实例，先清理
      if (this._instance) {
        this._instance._performFullCleanup();
      }
      
      // 重置单例引用
      this._instance = undefined as any;
      
      console.log('✅ [AppCesium] 单例实例重置完成');
      
    } catch (error) {
      console.error('❌ [AppCesium] 重置单例实例失败:', error);
      // 强制重置
      this._instance = undefined as any;
    }
  }

  /**
   * @description 检查实例是否已初始化
   * @returns 是否已初始化
   */
  isInitialized(): boolean {
    return this._initialized && !this._isDestroyed;
  }

  /**
   * @description 检查实例是否已销毁
   * @returns 是否已销毁
   */
  isDestroyed(): boolean {
    return this._isDestroyed;
  }

  /**
   * @description 初始化Viewer实例
   * @param viewer Cesium Viewer实例
   */
  initViewer(viewer: BC.Viewer) {
    if (this._isDestroyed) {
      console.warn('⚠️ [AppCesium] 实例已销毁，无法初始化Viewer');
      return;
    }

    console.log('📋 [AppCesium] 初始化Viewer实例...');
    
    try {
      // 清理旧的实例
      this._cleanupManagedInstances();
      
      // 设置新的viewer
      this._viewer = viewer;
      // 重置管理器实例
      this._layerManager = undefined;
      this._measureUtil = undefined;
      this._plotUtil = undefined;
      this._roamingManager = undefined;
      this._clipUtil = undefined;
      this._clipTerrain = undefined;
      this._roamTool = undefined;
      
      // 标记为已初始化
      this._initialized = true;
      this._isDestroyed = false;
      
      console.log('✅ [AppCesium] Viewer实例初始化完成');
      
    } catch (error) {
      console.error('❌ [AppCesium] 初始化Viewer失败:', error);
      throw error;
    }
  }

  /**
   * @description 获取Viewer实例
   * @returns Cesium Viewer实例
   * @throws 当Viewer未初始化或已销毁时抛出错误
   */
  getViewer() {
    if (this._isDestroyed) {
      throw new Error('AppCesium实例已销毁');
    }
    
    if (!this._viewer) {
      const { Cesium } = BC.Namespace;
      throw new Cesium.DeveloperError('当前Viewer还没有初始化！');
    }
    return this._viewer;
  }

  /**
   * @description 创建Viewer实例
   * @param parent 父DOM元素
   * @param id 容器ID
   * @returns Cesium Viewer实例
   */
  createViewer(parent: HTMLElement, id: string) {
    if (this._isDestroyed) {
      throw new Error('AppCesium实例已销毁，无法创建Viewer');
    }

    console.log('📋 [AppCesium] 创建Viewer实例...');
    
    try {
      const viewerContainer = document.createElement('div');
      viewerContainer.id = id;
      viewerContainer.style.width = '100%';
      viewerContainer.style.height = '100%';
      parent.appendChild(viewerContainer);
      
      const viewer = new BC.Viewer(id, {
        contextOptions: {
          webgl: {
            //通过canvas.toDataURL()实现截图需要将该项设置为true
            preserveDrawingBuffer: true,
          },
        },
      });
      
      this.initBaseMap(viewer);
      viewer.flyToPosition(this._center, () => {}, 1);
      
      console.log('✅ [AppCesium] Viewer实例创建完成');
      return viewer;
      
    } catch (error) {
      console.error('❌ [AppCesium] 创建Viewer失败:', error);
      throw error;
    }
  }

  async initBaseMap(viewer: BC.Viewer, is2D = false) {
    const { Cesium } = BC.Namespace;
    // const imageryLayer = BC.ImageryLayerFactory.createImageryLayer('wmts', {
    //   url: 'http://localhost:8077/bcserver/uavdom/arcgiscache/service/wmts/rest/uavdom/{style}/{TileMatrixSet}/{TileMatrixSet}:{TileMatrix}/{TileRow}/{TileCol}?format=image/png',
    //   style: 'default',
    //   layer: 'uavdom',
    //   format: 'image/png',
    //   maximumLevel: 19,
    //   tileMatrixSetID: 'EPSG:3857_uavdom'
    // });
    // if (!is2D) {
    //   const terrain = BC.TerrainFactory.createTerrain('xyz', {
    //     // url: `${this._baseMapUrl}/bcserver/services/hbzdem/rest/terrain`
    //     url: `${this._baseMapUrl}/bcserver/services/terrain/rest/terrain` // 河边镇地形
    //     // url: 'http://localhost:8077/bcserver/services/pzhdem/rest/terrain'
    //   });
    //   // const terrain = BC.TerrainFactory.createTerrain('arcgis', {
    //   //   url: `https://elevation3d.arcgis.com/arcgis/rest/services/WorldElevation3D/Terrain3D/ImageServer`
    //   // });
    //   viewer.delegate.terrainProvider = terrain;
    // }

    // viewer.delegate.terrainProvider = await (BC.Namespace.Cesium.CesiumTerrainProvider as any).fromUrl('http://***********:8077/bcserver/services/uavdem/rest/terrain');
    // const imageryLayer = BC.ImageryLayerFactory.createImageryLayer('baidu', {
    //   style: 'img',
    //   crs: 'WGS84'
    // });
    const tdtImageLayer = BC.ImageryLayerFactory.createImageryLayer('tdt', {
      style: 'img',
      key: 'b254904598a72fd14661de55fa70511d',
    });
    const tdtLabelLayer = BC.ImageryLayerFactory.createImageryLayer('tdt', {
      style: 'cva',
      key: 'b254904598a72fd14661de55fa70511d',
    });
    viewer.imageryLayers.addImageryProvider(tdtImageLayer);
    viewer.imageryLayers.addImageryProvider(tdtLabelLayer);

    // const imageryLayer = BC.ImageryLayerFactory.createImageryLayer('amap', {
    //   style: 'img',
    //   crs: 'WGS84',
    //   maximumLevel: 16,
    // });
    // viewer.imageryLayers.addImageryProvider(imageryLayer);
    // // viewer.addBaseLayer(imageryLayer);
    // const imageryLayer1 = BC.ImageryLayerFactory.createImageryLayer('amap', {
    //   style: 'cva',
    //   crs: 'WGS84',
    //   maximumLevel: 17,
    // });
    // viewer.imageryLayers.addImageryProvider(imageryLayer1);
    // viewer.addBaseLayer(imageryLayer1);
    // const imageryLayer = BC.ImageryLayerFactory.createWMTSImageryLayer({
    //   url: `${
    //     this._baseMapUrl
    //   }/bcserver/hbzimg/arcgiscache/service/wmts/rest/hbzimg/{style}/{TileMatrixSet}/{TileMatrixSet}:{TileMatrix}/{TileRow}/{TileCol}?format=image/png`,
    //   style: "default",
    //   // 图层不传也可以
    //   layer: "hbzimg",
    //   // 必填
    //   format: "image/png",
    //   // 选填
    //   maximumLevel: 19,
    //   // 必填
    //   tileMatrixSetID: "EPSG:3857_hbzimg"
    // })
    // viewer.imageryLayers.addImageryProvider(imageryLayer);
    // const terrain = BC.TerrainFactory.createTerrain('xyz', {
    //   url: 'http://localhost:8077/bcserver/services/uavdem/rest/terrain'
    // });
    // viewer.delegate.terrainProvider = terrain;
    // 添加抗锯齿
    if ((Cesium.FeatureDetection as any).supportsImageRenderingPixelated()) {
      //判断是否支持图像渲染像素化处理
      viewer.delegate.resolutionScale = window.devicePixelRatio
    }
    (viewer.scene as any).fxaa = true;
    (viewer as any).allowMouseMove = true;
    viewer.scene.postProcessStages.fxaa.enabled = true;

    // viewer._cesiumWidget._supportsImageRenderingPixelated =
    //   Cesium.FeatureDetection.supportsImageRenderingPixelated();
    // viewer._cesiumWidget._forceResize = true;
    // // 是否支持图像渲染像素化处理
    // if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
    //   let dpr = window.devicePixelRatio;
    //   while (dpr >= 2.0) {
    //     dpr /= 2.0;
    //   }
    //   viewer.resolutionScale = dpr;
    // }
    // // 开启后会造成字体的模糊（不是很明显），不用担心，设置字体的时候，字号设置大一些，然后比例缩小一倍，能够有效解决字体模糊。
    // 加载倾斜摄影
    // if (!is2D) {
    //   const tileset = new BC.TilesetLayer('tileset');
    //   // const model = new BC.Tileset(
    //   //   `${this._baseMapUrl}/bcserver/services/hbztiles/rest/3dtiles/tileset.json`,
    //   //   {
    //   //     maximumScreenSpaceError: 16
    //   //   }
    //   // );
    //   const model = new BC.Tileset(
    //     `${this._baseMapUrl}/tiles/oblique/tileset.json`,
    //     {
    //       maximumScreenSpaceError: 16,
    //     }
    //   );
    //   tileset.addOverlay(model);
    //   tileset.addTo(viewer);
    // }
  }

  async loadLayers() {
    if (this._isDestroyed) {
      console.warn('⚠️ [AppCesium] 实例已销毁，无法加载图层');
      return;
    }

    const layers = await this.getLayers();
    this.getLayerManager().addLayers(layers);
  }

  async getLayers() {
    const res = await fetch('/json/sceneLayers.json');
    const data = await res.json();
    const secondDeviceTypeCodes: any = await fetchDeviceSubTypes({
      parentCode: 'iot_device_type'
    })
    secondDeviceTypeCodes.forEach(async (item: any) => {
      item.id = `device_${item.code}`;
      item.type = 'device';
      item.visible = 1;
      item.showLayerControl = true;
      item.icon = item.code;
    })
    // secondDeviceTypeCodes.push({
    //   id: 'device_zhinengbiao',
    //   code: 'zhinengbiao',
    //   name: '智能表',
    //   type: 'device',
    //   visible: 1,
    //   showLayerControl: true,
    //   icon: 'zhinengbiao',
    // })
    data.push({
      id: 'device_list',
      name: '设备',
      type: 'devices_group',
      visible: 1,
      children: secondDeviceTypeCodes
    })
    return data;
  }

  selectPipeComponent(callback: (value: any) => void) {
    if (this._isDestroyed) {
      console.warn('⚠️ [AppCesium] 实例已销毁，无法选择管线组件');
      return;
    }

    this.getViewer().once(
      BC.MouseEventType.LEFT_CLICK,
      async (value: any) => {
        if (value.feature && value.feature.getPropertyIds) {
          console.log(value.feature.getPropertyIds());
          
          // const type = value.feature.getProperty('type');
          const id = value.feature.getProperty('id');
          const type = id.indexOf('_') === -1 ? 'node' : 'line'
          const callbackData: any = {
            type,
            id
          }
          if (value.wgs84Position) {
            callbackData.position = value.wgs84Position;
          }
          callback(callbackData);
        } else {
          callback(null);
        }
      },
      this
    );
  }

  get RoamingManager() {
    if (this._isDestroyed) {
      throw new Error('AppCesium实例已销毁');
    }

    if (!this._roamingManager) {
      this._roamingManager = new RoamingManager(this._viewer as BC.Viewer);
    }
    return this._roamingManager;
  }

  getMeasureUtil() {
    if (this._isDestroyed) {
      throw new Error('AppCesium实例已销毁');
    }

    if (!this._measureUtil) {
      this._measureUtil = new BC.Measure();
      this.getViewer().use(this._measureUtil);
    }
    return this._measureUtil;
  }

  getClipUtil() {
    if (this._isDestroyed) {
      throw new Error('AppCesium实例已销毁');
    }

    if (!this._clipUtil) {
      this._clipUtil = new (BC as any).ClippingTileset();
      (this._viewer as BC.Viewer).use(this._clipUtil);
    }
    return this._clipUtil;
  }

  getClipTerrainUtil() {
    if (this._isDestroyed) {
      throw new Error('AppCesium实例已销毁');
    }

    if (!this._clipTerrain) {
      this._clipTerrain = new (BC as any).ClippingTerrain();
      (this._viewer as BC.Viewer).use(this._clipTerrain);
    }
    return this._clipTerrain;
  }

  getPlotUtil() {
    if (this._isDestroyed) {
      throw new Error('AppCesium实例已销毁');
    }

    if (!this._plotUtil) {
      this._plotUtil = new BC.Plot(this._viewer as BC.Viewer);
    }
    return this._plotUtil;
  }

  getLayerManager() {
    if (this._isDestroyed) {
      throw new Error('AppCesium实例已销毁');
    }

    if (!this._layerManager) {
      this._layerManager = new LayerManager(this._viewer as BC.Viewer);
    }
    return this._layerManager;
  }

  getRoamTool() {
    if (this._isDestroyed) {
      throw new Error('AppCesium实例已销毁');
    }

    if (!this._roamTool) {
      this._roamTool = new RoamTool(this._viewer as BC.Viewer);
    }
    return this._roamTool;
  }

  goToHome() {
    if (this._isDestroyed) {
      console.warn('⚠️ [AppCesium] 实例已销毁，无法执行goToHome');
      return;
    }

    console.log(this.getViewer().scene.camera);
    this.getViewer().flyToPosition(this._center, () => {}, 1);
  }

  flyToAlarm(data: any) {
    if (this._isDestroyed) {
      console.warn('⚠️ [AppCesium] 实例已销毁，无法执行flyToAlarm');
      return;
    }

    const position = new BC.Position(data.longitude, data.latitude, 0);
    // 创建预警点位
    const billboard = new BC.Billboard(position, '/images/alarm.png');
    billboard.size = [50, 65];
    billboard.setStyle({
      scale: 0.7,
      horizontalOrigin: BC.HorizontalOrigin.CENTER,
      verticalOrigin: BC.VerticalOrigin.BOTTOM,
    });
    billboard.on(
      BC.MouseEventType.LEFT_CLICK as any,
      () => {
        // 打开详情弹框
      },
      this
    );
    this.getViewer().flyTo(billboard);
  }

  rollingCompare() {
    if (this._isDestroyed) {
      console.warn('⚠️ [AppCesium] 实例已销毁，无法执行rollingCompare');
      return;
    }

    //天地图地形图
    let baseLayer_ter = BC.ImageryLayerFactory.createTdtImageryLayer({
      key: 'b254904598a72fd14661de55fa70511d',
      style: 'ter',
    });
    //天地图矢量图
    let baseLayer_digital = BC.ImageryLayerFactory.createTdtImageryLayer({
      key: 'b254904598a72fd14661de55fa70511d',
      style: 'vec',
    });
    this.getViewer().mapSplit.enable = true;
    // this.getViewer().mapSplit.addBaseLayer(baseLayer_ter, -1);
    this.getViewer().mapSplit.addBaseLayer(baseLayer_digital, -1);
  }

  /**
   * @description 完整的实例清理
   * @details 清理所有管理的实例和状态
   */
  private _performFullCleanup(): void {
    if (this._isDestroyed) {
      console.log('⚠️ [AppCesium] 实例已清理，跳过重复清理');
      return;
    }

    console.log('🧹 [AppCesium] 开始执行完整清理...');
    
    try {
      // 标记为销毁状态
      this._isDestroyed = true;
      this._initialized = false;
      
      // 清理所有管理的工具实例
      this._cleanupManagedInstances();
      
      // 清理Viewer实例
      this._cleanupViewer();
      
      console.log('✅ [AppCesium] 完整清理完成');
      
    } catch (error) {
      console.error('❌ [AppCesium] 完整清理失败:', error);
      // 确保标记为销毁状态
      this._isDestroyed = true;
      this._initialized = false;
    }
  }

  /**
   * @description 清理管理的工具实例
   */
  private _cleanupManagedInstances(): void {
    try {
      console.log('🧹 [AppCesium] 清理管理的工具实例...');
      
      // 清理LayerManager
      if (this._layerManager && typeof (this._layerManager as any).destroy === 'function') {
        try {
          (this._layerManager as any).destroy();
        } catch (e) {
          console.warn('清理LayerManager时出错:', e);
        }
      }
      this._layerManager = undefined;
      
      // 清理MeasureUtil
      if (this._measureUtil && typeof (this._measureUtil as any).destroy === 'function') {
        try {
          (this._measureUtil as any).destroy();
        } catch (e) {
          console.warn('清理MeasureUtil时出错:', e);
        }
      }
      this._measureUtil = undefined;
      
      // 清理PlotUtil
      if (this._plotUtil && typeof (this._plotUtil as any).destroy === 'function') {
        try {
          (this._plotUtil as any).destroy();
        } catch (e) {
          console.warn('清理PlotUtil时出错:', e);
        }
      }
      this._plotUtil = undefined;
      
      // 清理RoamingManager
      if (this._roamingManager && typeof (this._roamingManager as any).destroy === 'function') {
        try {
          (this._roamingManager as any).destroy();
        } catch (e) {
          console.warn('清理RoamingManager时出错:', e);
        }
      }
      this._roamingManager = undefined;
      
      // 清理ClipUtil
      if (this._clipUtil && typeof (this._clipUtil as any).destroy === 'function') {
        try {
          (this._clipUtil as any).destroy();
        } catch (e) {
          console.warn('清理ClipUtil时出错:', e);
        }
      }
      this._clipUtil = undefined;
      
      // 清理ClipTerrain
      if (this._clipTerrain && typeof (this._clipTerrain as any).destroy === 'function') {
        try {
          (this._clipTerrain as any).destroy();
        } catch (e) {
          console.warn('清理ClipTerrain时出错:', e);
        }
      }
      this._clipTerrain = undefined;
      
      // 清理RoamTool
      if (this._roamTool && typeof (this._roamTool as any).destroy === 'function') {
        try {
          (this._roamTool as any).destroy();
        } catch (e) {
          console.warn('清理RoamTool时出错:', e);
        }
      }
      this._roamTool = undefined;
      
      console.log('✅ [AppCesium] 管理的工具实例清理完成');
      
    } catch (error) {
      console.error('❌ [AppCesium] 清理管理的工具实例失败:', error);
      
      // 强制重置所有引用
      this._layerManager = undefined;
      this._measureUtil = undefined;
      this._plotUtil = undefined;
      this._roamingManager = undefined;
      this._clipUtil = undefined;
      this._clipTerrain = undefined;
      this._roamTool = undefined;
    }
  }

  /**
   * @description 清理Viewer实例
   */
  private _cleanupViewer(): void {
    try {
      if (this._viewer && typeof this._viewer.destroy === 'function') {
        console.log('🗑️ [AppCesium] 清理Viewer实例...');
        
        // 清理场景内容
        try {
          if (this._viewer.entities) {
            this._viewer.entities.removeAll();
          }
          
          if (this._viewer.scene && this._viewer.scene.primitives) {
            this._viewer.scene.primitives.removeAll();
          }
          
          if (this._viewer.scene && this._viewer.scene.groundPrimitives) {
            this._viewer.scene.groundPrimitives.removeAll();
          }
        } catch (e) {
          console.warn('清理场景内容时出错:', e);
        }
        
        // 销毁viewer实例
        try {
          this._viewer.destroy();
        } catch (e) {
          console.warn('销毁Viewer时出错:', e);
        }
        
        console.log('✅ [AppCesium] Viewer实例清理完成');
      }
      
      this._viewer = undefined;
      
    } catch (error) {
      console.error('❌ [AppCesium] 清理Viewer实例失败:', error);
      // 强制重置引用
      this._viewer = undefined;
    }
  }

  /**
   * @description 公共清理方法
   * @details 提供给外部调用的清理接口
   */
  public destroy(): void {
    console.log('🗑️ [AppCesium] 公共销毁方法被调用');
    this._performFullCleanup();
  }

  /**
   * @description 重置实例状态
   * @details 清理当前状态但保持实例可用
   */
  public reset(): void {
    console.log('🔄 [AppCesium] 重置实例状态...');
    
    try {
      // 清理管理的实例，但不销毁整个AppCesium实例
      this._cleanupManagedInstances();
      
      // 重置状态标志
      this._initialized = false;
      this._isDestroyed = false;
      
      // 保留viewer引用，但清理其内容
      if (this._viewer) {
        try {
          if (this._viewer.entities) {
            this._viewer.entities.removeAll();
          }
          
          if (this._viewer.scene && this._viewer.scene.primitives) {
            this._viewer.scene.primitives.removeAll();
          }
          
          if (this._viewer.scene && this._viewer.scene.groundPrimitives) {
            this._viewer.scene.groundPrimitives.removeAll();
          }
        } catch (e) {
          console.warn('重置Viewer内容时出错:', e);
        }
      }
      
      console.log('✅ [AppCesium] 实例状态重置完成');
      
    } catch (error) {
      console.error('❌ [AppCesium] 重置实例状态失败:', error);
    }
  }

  /**
   * @description 获取实例状态信息
   * @returns 实例状态对象
   */
  public getStatus() {
    return {
      initialized: this._initialized,
      destroyed: this._isDestroyed,
      hasViewer: !!this._viewer,
      hasLayerManager: !!this._layerManager,
      hasMeasureUtil: !!this._measureUtil,
      hasPlotUtil: !!this._plotUtil,
      hasRoamingManager: !!this._roamingManager,
      hasClipUtil: !!this._clipUtil,
      hasClipTerrain: !!this._clipTerrain,
      hasRoamTool: !!this._roamTool
    };
  }
}
