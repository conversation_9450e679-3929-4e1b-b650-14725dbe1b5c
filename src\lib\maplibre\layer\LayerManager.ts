import { BaseLayer } from './layers/BaseLayer';
import { LayerGroup } from './layers/LayerGroup';
import { type LayerItem } from './type/LayerItem';
import { ImageryLayer } from './layers/ImageryLayer';
import { Map as glMap } from 'maplibre-gl';
import { uuid } from '@/utils/uuid';
import { BehaviorSubject, Subject } from 'rxjs';
import {
  LayerType,
  LayerStatus,
  LayerGroupType,
  LayerPriority,
  type LayerManagerConfig,
  type LayerConfig,
  type LayerEventData,
  type LayerEventHandler,
  type LayerLoadOptions,
  type LayerPerformanceMetrics,
  type LayerTreeNode,
} from './types/LayerTypes';
import type { MvtLayer } from './layers/MvtLayer';
import { getDevicePage } from '@/api/query';
import { fetchDeviceSubTypes } from '@/utils/deviceUtils';
import AlarmLayer from './layers/AlarmLayer';
import LeakLayer from './layers/LeakLayer';

/**
 * @class LayerManager
 * @description 图层管理器，负责图层的创建、管理、事件处理和性能监控
 * @version 2.0.0
 */
export class LayerManager {
  private _map: glMap;
  private _layers!: LayerGroup;
  private _config: LayerManagerConfig;
  private _loadingQueue: Map<string, Promise<BaseLayer>> = new Map();
  private _performanceMonitor: Map<string, LayerPerformanceMetrics> = new Map();
  private _eventHandlers: Map<string, LayerEventHandler[]> = new Map();

  // 事件流
  public readonly layerChanged = new BehaviorSubject<LayerItem[]>([]);
  public readonly layerEvents = new Subject<LayerEventData>();
  public readonly performanceUpdated = new Subject<{
    layerId: string;
    metrics: LayerPerformanceMetrics;
  }>();

  /**
   * @constructor
   * @param config - 图层管理器配置
   */
  constructor(config: LayerManagerConfig | glMap) {
    // 兼容旧版本构造函数
    if (config instanceof Map || (config as any).addSource) {
      this._map = config as glMap;
      this._config = {
        map: this._map,
        enablePerformanceMonitoring: false,
        debug: false,
        maxConcurrentLoads: 5,
      };
    } else {
      this._config = config as LayerManagerConfig;
      this._map = this._config.map;
    }

    this.initializeEventSystem();
  }

  /**
   * @description 初始化事件系统
   * @private
   */
  private initializeEventSystem(): void {
    // 监听图层事件并转发
    this.on('*', (event: LayerEventData) => {
      this.layerEvents.next(event);

      if (this._config.debug) {
        console.log(`[LayerManager] 图层事件: ${event.type}`, event);
      }
    });
  }

  /**
   * @description 添加事件监听器
   * @param eventType - 事件类型，使用 '*' 监听所有事件
   * @param handler - 事件处理器
   */
  public on(eventType: string, handler: LayerEventHandler): void {
    if (!this._eventHandlers.has(eventType)) {
      this._eventHandlers.set(eventType, []);
    }
    this._eventHandlers.get(eventType)!.push(handler);
  }

  /**
   * @description 移除事件监听器
   * @param eventType - 事件类型
   * @param handler - 事件处理器
   */
  public off(eventType: string, handler?: LayerEventHandler): void {
    if (!this._eventHandlers.has(eventType)) return;

    const handlers = this._eventHandlers.get(eventType)!;
    if (handler) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    } else {
      handlers.length = 0;
    }
  }

  async loadLeakLayer() {
    const leakLayerOption = {
      id: 'leak_layer',
      name: '漏水事件',
      type: 'leak',
      visible: 1,
      icon: 'leak',
    };
    const leakLayer = new LeakLayer(leakLayerOption);
    await leakLayer.addTo(this._map, undefined, undefined);
  }

  async loadAlarmLayer() {
    const alarmLayerOption = {
      id: 'alarm_layer',
      name: '预警设备',
      type: 'alarm',
      visible: 1,
      icon: 'alarm',
    };
    const alarmLayer = new AlarmLayer(alarmLayerOption);
    await alarmLayer.addTo(this._map, undefined, undefined);
  }

  async loadAllLayers() {
    const secondDeviceTypeCodes: any = await fetchDeviceSubTypes({
      parentCode: 'iot_device_type',
    });
    secondDeviceTypeCodes.forEach(async (item: any) => {
      item.id = `device_${item.code}`;
      item.type = 'device';
      item.visible = 1;
      item.showLayerControl = true;
      item.icon = item.code;
    });
    secondDeviceTypeCodes.push({
      id: 'device_zhinengbiao',
      code: 'zhinengbiao',
      name: '智能表',
      type: 'device',
      visible: 1,
      showLayerControl: true,
      icon: 'zhinengbiao',
    });

    const json = await fetch('json/mapLayers.json');
    const layers = await json.json();
    const onlyAnalysis = useAnalysisModeStore().isAnalysisOnly;
    if (!onlyAnalysis) {
      layers.push({
        id: 'devices',
        name: '设备',
        type: 'devices_group',
        children: secondDeviceTypeCodes,
      });
    }

    this.addLayers(layers);
  }

  async queryAllDevices() {
    const res = await getDevicePage({
      page: 1,
      size: -1,
    });
    debugger;
    return res.data.data;
  }

  /**
   * @description 发射事件
   * @param eventType - 事件类型
   * @param eventData - 事件数据
   * @private
   */
  private emit(eventType: string, eventData: LayerEventData): void {
    // 发射具体事件类型
    const handlers = this._eventHandlers.get(eventType);
    if (handlers) {
      handlers.forEach((handler) => {
        try {
          handler(eventData);
        } catch (error) {
          console.error(`图层管理器事件处理器执行失败 [${eventType}]:`, error);
        }
      });
    }

    // 发射通用事件
    const allHandlers = this._eventHandlers.get('*');
    if (allHandlers) {
      allHandlers.forEach((handler) => {
        try {
          handler(eventData);
        } catch (error) {
          console.error(`图层管理器通用事件处理器执行失败:`, error);
        }
      });
    }
  }

  getDeviceLayers() {
    return [
      {
        type: 'vector',
        data: '/data/device/BZ.json',
      },
    ];
  }

  /**
   * @description 批量添加图层
   * @param config - 图层配置数组或图层组配置
   * @param options - 加载选项
   */
  async addLayers(
    config: LayerConfig[] | any,
    options?: LayerLoadOptions
  ): Promise<void> {
    try {
      // 移除已加载图层
      if (this._layers) {
        this._layers.remove();
      }

      // 创建根图层组
      this._layers = new LayerGroup({
        type: LayerType.GROUP,
        title: '根图层组',
        children: Array.isArray(config) ? config : config.children || config,
      });

      // 监听图层组事件
      this.setupLayerEventListeners(this._layers);

      // 添加到地图
      await this._layers.addTo(this._map, undefined, options);

      // 发送图层更改事件
      this.layerChanged.next(this.getLayerTree());

      this.emit('layersAdded', {
        layerId: this._layers.id,
        type: 'layersAdded',
        data: { count: this.getLayerCount() },
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error('批量添加图层失败:', error);
      this.emit('layersAddError', {
        layerId: 'root',
        type: 'layersAddError',
        data: error,
        timestamp: Date.now(),
      });
      throw error;
    }
  }

  /**
   * @description 设置图层事件监听器
   * @param layer - 图层实例
   * @private
   */
  private setupLayerEventListeners(layer: BaseLayer): void {
    // 监听图层的各种事件
    layer.on('loading', (event) => this.emit('loading', event));
    layer.on('loaded', (event) => {
      this.emit('loaded', event);
      this.updatePerformanceMetrics(event.layerId, event.data);
    });
    layer.on('error', (event) => this.emit('error', event));
    layer.on('visibilityChanged', (event) => {
      this.emit('visibilityChanged', event);
      this.layerChanged.next(this.getLayerTree());
    });
    layer.on('removed', (event) => {
      this.emit('removed', event);
      this.layerChanged.next(this.getLayerTree());
    });
  }

  /**
   * @description 更新性能指标
   * @param layerId - 图层ID
   * @param metrics - 性能指标
   * @private
   */
  private updatePerformanceMetrics(
    layerId: string,
    metrics: Partial<LayerPerformanceMetrics>
  ): void {
    if (!this._config.enablePerformanceMonitoring) return;

    const existing = this._performanceMonitor.get(layerId) || {
      loadTime: 0,
      renderTime: 0,
      memoryUsage: 0,
      tileSuccessRate: 1,
      errorCount: 0,
    };

    const updated = { ...existing, ...metrics };
    this._performanceMonitor.set(layerId, updated);

    this.performanceUpdated.next({ layerId, metrics: updated });
  }
  /**
   * 根据图层查找id
   * @param layerId 图层id
   * @returns
   */
  findLayerById(layerId: string): BaseLayer | null {
    return this._layers.findLayer(layerId);
  }

  /**
   * 根据条件查找图层
   * @param predicate 图层查询条件表达式
   * @returns
   */
  find(predicate: (layer: BaseLayer) => boolean) {
    return this._layers.findLayers(predicate);
  }

  /**
   * 查找符合条件的第一个图层
   * @param filter
   * @returns
   */
  findLayer(filter: (layer: BaseLayer) => boolean): BaseLayer | null {
    return this._layers.findLayerByPredicate(filter);
  }

  /**
   * 查找符合条件的所有图层
   * @param filter
   */
  findLayers(filter: (layer: BaseLayer) => boolean): BaseLayer[] | any[] {
    return this._layers.findLayers(filter);
  }

  /**
   * 定位到图层
   * @param layerId 图层id
   */
  flyToLayer(layerId: string) {
    const layer = this.findLayerById(layerId);
    layer && layer.flyTo();
  }

  /**
   * @description 获取图层树结构
   * @returns 图层树节点数组
   */
  getLayerTree(): LayerItem[] {
    if (!this._layers) return [];
    const layerTree = this._layers.getLayerTree();
    return layerTree.children ?? [];
  }

  /**
   * @description 获取图层总数
   * @returns 图层数量
   */
  getLayerCount(): number {
    if (!this._layers) return 0;
    return this.countLayers(this._layers);
  }

  /**
   * @description 递归计算图层数量
   * @param layer - 图层实例
   * @returns 图层数量
   * @private
   */
  private countLayers(layer: BaseLayer): number {
    if (layer instanceof LayerGroup) {
      let count = 0;
      const children = (layer as any)._delegate?.values() || [];
      for (const child of children) {
        count += this.countLayers(child);
      }
      return count;
    }
    return 1;
  }

  /**
   * @description 获取所有图层的性能指标
   * @returns 性能指标映射
   */
  getPerformanceMetrics(): Map<string, LayerPerformanceMetrics> {
    return new Map(this._performanceMonitor);
  }

  /**
   * @description 获取指定图层的性能指标
   * @param layerId - 图层ID
   * @returns 性能指标或undefined
   */
  getLayerPerformanceMetrics(
    layerId: string
  ): LayerPerformanceMetrics | undefined {
    return this._performanceMonitor.get(layerId);
  }

  /**
   * @description 清理性能指标
   * @param layerId - 图层ID，不提供则清理所有
   */
  clearPerformanceMetrics(layerId?: string): void {
    if (layerId) {
      this._performanceMonitor.delete(layerId);
    } else {
      this._performanceMonitor.clear();
    }
  }

  setLayerVisibleByFunction(
    predicate: (layer: BaseLayer) => boolean,
    show: boolean
  ) {
    const layers = this.find(predicate);
    layers.forEach((layer) => {
      layer.show = show;
    });
  }

  setLayerVisible(layerId: string, show: boolean) {
    const layer = this.findLayerById(layerId);
    if (layer) {
      layer.show = show;
      this.layerChanged.next(this.getLayerTree());
    }
    // let needSetIndex = show && layer instanceof ImageryLayer && !layer.delegate;
    // needSetIndex = needSetIndex || layer instanceof LayerGroup;
    // if (needSetIndex) {
    //   this.changeImageryIndex();
    // }
  }

  addParentLayer(options: any, index?: number) {
    if (this._layers instanceof LayerGroup) {
      const layer = this._layers.addNewLayer(options, index);
      return layer;
    }
    return null;
  }

  /**
   * @description 添加单个图层
   * @param target - 目标图层组ID，为空则添加到根图层组
   * @param options - 图层配置
   * @param index - 插入位置
   * @param loadOptions - 加载选项
   * @returns 创建的图层实例
   */
  async addLayer(
    target: string,
    options: LayerConfig,
    index?: number,
    loadOptions?: LayerLoadOptions
  ): Promise<BaseLayer | null> {
    try {
      const targetLayer = target ? this.findLayerById(target) : this._layers;
      if (targetLayer instanceof LayerGroup) {
        // 检查并发加载限制
        if (this._loadingQueue.size >= (this._config.maxConcurrentLoads || 5)) {
          console.warn(
            `达到最大并发加载数限制 (${this._config.maxConcurrentLoads})，等待其他图层加载完成`
          );
          await Promise.race(this._loadingQueue.values());
        }

        // 创建加载Promise并加入队列
        const loadPromise = this.createLayerAsync(
          targetLayer,
          options,
          index,
          loadOptions
        );
        this._loadingQueue.set(options.id || uuid(), loadPromise);

        const layer = await loadPromise;

        // 从加载队列中移除
        this._loadingQueue.delete(layer.id);

        // 设置事件监听器
        this.setupLayerEventListeners(layer);

        // 调整影像顺序
        if (layer.show && layer instanceof ImageryLayer) {
          this.changeImageryIndex();
        }

        // 确保图层顺序正确（重点：标绘图层应在基础图层之上）
        this.ensureLayerOrder(layer);

        // 更新图层树
        this.layerChanged.next(this.getLayerTree());

        this.emit('layerAdded', {
          layerId: layer.id,
          type: 'layerAdded',
          data: { layer: layer.getLayerTree() },
          timestamp: Date.now(),
        });

        return layer;
      }
      return null;
    } catch (error) {
      console.error('添加图层失败:', error);
      this.emit('layerAddError', {
        layerId: options.id || 'unknown',
        type: 'layerAddError',
        data: error,
        timestamp: Date.now(),
      });
      throw error;
    }
  }

  /**
   * @description 异步创建图层
   * @param targetLayer - 目标图层组
   * @param options - 图层配置
   * @param index - 插入位置
   * @param loadOptions - 加载选项
   * @returns 图层实例
   * @private
   */
  private async createLayerAsync(
    targetLayer: LayerGroup,
    options: LayerConfig,
    index?: number,
    loadOptions?: LayerLoadOptions
  ): Promise<BaseLayer> {
    // 暂时忽略 loadOptions，因为 LayerGroup.addNewLayer 还没有支持这个参数
    // TODO: 在 LayerGroup 重构完成后，传递 loadOptions
    return targetLayer.addNewLayer(options, index);
  }

  /**
   * @description 移除图层
   * @param layerId - 图层ID
   * @returns 是否成功移除
   */
  removeLayer(layerId: string): boolean {
    try {
      if (!layerId) {
        console.warn('LayerManager.removeLayer: 图层ID不能为空');
        return false;
      }

      const layer: any = this.findLayerById(layerId);

      if (!layer) {
        console.warn(
          `LayerManager.removeLayer: 未找到ID为 "${layerId}" 的图层`
        );
        return false;
      }

      // 检查图层是否有remove方法
      if (typeof layer.remove !== 'function') {
        console.error(
          `LayerManager.removeLayer: 图层 "${layerId}" 没有remove方法`
        );
        return false;
      }

      // 移除图层
      layer.remove();

      // 从性能监控中移除
      this._performanceMonitor.delete(layerId);

      // 更新图层树
      this.layerChanged.next(this.getLayerTree());

      // 发射移除事件
      this.emit('layerRemoved', {
        layerId: layerId,
        type: 'layerRemoved',
        data: { layerId },
        timestamp: Date.now(),
      });

      console.log(`图层 "${layerId}" 移除成功`);
      return true;
    } catch (error) {
      console.error(
        `LayerManager.removeLayer: 移除图层 "${layerId}" 失败:`,
        error
      );

      // 发射移除错误事件
      this.emit('layerRemoveError', {
        layerId: layerId,
        type: 'layerRemoveError',
        data: error,
        timestamp: Date.now(),
      });

      return false;
    }
  }

  moveLayer(layerId: string, target: string, index?: number) {
    const layer = this.findLayerById(layerId);
    const targetLayer = target ? this.findLayerById(target) : this._layers;
    if (layer && targetLayer) {
      if (layer.parent) {
        layer.parent.moveLayer(layerId, targetLayer as LayerGroup, index);
      }
    }
  }

  /**
   * 调整影像顺序
   */
  changeImageryIndex() {
    const layers = this._layers.findLayers((layer) => {
      return layer instanceof ImageryLayer && layer.delegate;
    });
    // 按照顺序调整影像位置
    // layers.forEach((layer) => {
    //   this._viewer.imageryLayers.raiseToTop(layer.delegate);
    // });
  }

  /**
   * @description 图层添加后变化（兼容旧版本）
   * @deprecated 使用事件系统替代
   */
  layerAddChange(): void {
    this.layerChanged.next(this.getLayerTree());
  }

  /**
   * @description 批量设置图层可见性
   * @param layerIds - 图层ID数组
   * @param visible - 是否可见
   */
  setLayersVisible(layerIds: string[], visible: boolean): void {
    layerIds.forEach((layerId) => {
      const layer = this.findLayerById(layerId);
      if (layer) {
        layer.show = visible;
      }
    });
  }

  /**
   * @description 获取可见图层列表
   * @returns 可见图层ID数组
   */
  getVisibleLayers(): string[] {
    const visibleLayers: string[] = [];
    this.traverseLayers(this._layers, (layer) => {
      if (layer.show && !(layer instanceof LayerGroup)) {
        visibleLayers.push(layer.id);
      }
    });
    return visibleLayers;
  }

  /**
   * @description 获取隐藏图层列表
   * @returns 隐藏图层ID数组
   */
  getHiddenLayers(): string[] {
    const hiddenLayers: string[] = [];
    this.traverseLayers(this._layers, (layer) => {
      if (!layer.show && !(layer instanceof LayerGroup)) {
        hiddenLayers.push(layer.id);
      }
    });
    return hiddenLayers;
  }

  /**
   * @description 遍历所有图层
   * @param layer - 起始图层
   * @param callback - 回调函数
   * @private
   */
  private traverseLayers(
    layer: BaseLayer,
    callback: (layer: BaseLayer) => void
  ): void {
    callback(layer);

    if (layer instanceof LayerGroup) {
      const children = (layer as any)._delegate?.values() || [];
      for (const child of children) {
        this.traverseLayers(child, callback);
      }
    }
  }

  refreshMvt() {
    const mvtLayers: any = this.findLayers((layer) =>
      layer.id.includes('mvt_')
    );
    if (mvtLayers.length > 0) {
      mvtLayers.forEach((layer: MvtLayer) => {
        layer.refresh();
      });
    }
  }

  /**
   * @description 刷新所有图层
   */
  async refreshAllLayers(): Promise<void> {
    const refreshPromises: Promise<void>[] = [];

    this.traverseLayers(this._layers, (layer) => {
      if (!(layer instanceof LayerGroup)) {
        refreshPromises.push(
          layer.refresh().catch((error) => {
            console.error(`刷新图层 ${layer.name} 失败:`, error);
          })
        );
      }
    });

    await Promise.allSettled(refreshPromises);
    this.layerChanged.next(this.getLayerTree());
  }

  /**
   * @description 清理所有图层
   */
  clear(): void {
    if (this._layers) {
      this._layers.remove();
      this._layers = null as any;
    }

    this._loadingQueue.clear();
    this._performanceMonitor.clear();
    this.layerChanged.next([]);

    this.emit('cleared', {
      layerId: 'all',
      type: 'cleared',
      data: null,
      timestamp: Date.now(),
    });
  }

  /**
   * @description 销毁图层管理器
   */
  destroy(): void {
    this.clear();
    this._eventHandlers.clear();
    this.layerChanged.complete();
    this.layerEvents.complete();
    this.performanceUpdated.complete();
  }

  /**
   * @description 获取顶层基础图层的ID
   * @returns 顶层基础图层的ID，如果没有则返回null
   */
  getTopBaseLayerId(): string | null {
    const allLayers = this._map.getStyle().layers || [];

    console.log('=== 查找顶层基础图层 ===');
    console.log(`地图中共有 ${allLayers.length} 个图层`);

    // 精确匹配基础图层的ID
    const baseLayerIds = [
      'tdt-vector',
      'tdt-satellite',
      'tdt-terrain',
      'amap-normal',
      'amap-satellite',
      'baidu-normal',
      'baidu-satellite',
    ];

    // 从后往前查找（后添加的图层在上层），找到最后一个基础图层
    for (let i = allLayers.length - 1; i >= 0; i--) {
      const layer = allLayers[i];

      // 检查是否为基础图层
      const isBaseLayer =
        baseLayerIds.includes(layer.id) ||
        layer.id.endsWith('-annotation') ||
        layer.id.endsWith('_road') ||
        layer.id.endsWith('_label');

      console.log(
        `检查图层 ${i}: ${layer.id} - ${isBaseLayer ? '基础图层' : '其他图层'}`
      );

      if (isBaseLayer) {
        console.log(`找到顶层基础图层: ${layer.id}`);
        console.log('=== 基础图层查找完成 ===');
        return layer.id;
      }
    }

    console.log('未找到基础图层');
    console.log('=== 基础图层查找完成 ===');
    return null;
  }

  /**
   * @description 确保图层顺序正确
   * @param targetLayer - 要检查顺序的图层
   */
  ensureLayerOrder(targetLayer: BaseLayer): void {
    if (!targetLayer || !this._map) return;

    console.log('=== 开始图层顺序调整 ===');
    console.log(`目标图层: ${targetLayer.name} (${targetLayer.id})`);
    console.log(
      `图层分组: ${targetLayer.group}, 优先级: ${targetLayer.priority}`
    );

    // 如果是标绘图层，确保其在基础图层之上
    if (
      targetLayer.group === LayerGroupType.PLOT ||
      targetLayer.priority >= LayerPriority.PLOT
    ) {
      console.log('这是标绘图层，需要调整顺序');

      // 获取标绘图层在地图中的所有子图层ID
      const plotLayerIds = this.getPlotLayerIds(targetLayer.id);
      console.log(`标绘图层包含子图层: [${plotLayerIds.join(', ')}]`);

      if (plotLayerIds.length === 0) {
        console.warn('未找到标绘图层的子图层，跳过顺序调整');
        return;
      }

      // 获取当前所有图层的顺序
      const allLayers = this._map.getStyle().layers || [];
      console.log(`当前地图图层顺序:`);
      allLayers.forEach((layer, index) => {
        console.log(`  ${index}: ${layer.id} (${layer.type})`);
      });

      // 将每个标绘子图层移动到最顶层
      plotLayerIds.forEach((layerId) => {
        const mapLayer = this._map.getLayer(layerId);
        if (mapLayer) {
          try {
            // 移动到最顶层（不指定beforeId）
            this._map.moveLayer(layerId);
            console.log(`标绘子图层 ${layerId} 已移动到顶层`);
          } catch (error) {
            console.error(`移动标绘子图层 ${layerId} 失败:`, error);
          }
        } else {
          console.warn(`标绘子图层 ${layerId} 在地图中不存在`);
        }
      });

      // 再次检查图层顺序
      const updatedLayers = this._map.getStyle().layers || [];
      console.log(`调整后的图层顺序:`);
      updatedLayers.forEach((layer, index) => {
        console.log(`  ${index}: ${layer.id} (${layer.type})`);
      });

      console.log(`标绘图层 ${targetLayer.name} 顺序调整完成`);
    } else {
      console.log('非标绘图层，无需调整顺序');
    }

    console.log('=== 图层顺序调整完成 ===');
  }

  /**
   * @description 获取指定图层在地图中对应的所有图层ID
   * @param layerId - 图层ID
   * @returns 图层ID数组
   * @private
   */
  private getPlotLayerIds(layerId: string): string[] {
    const layerIds: string[] = [];

    // 标绘图层可能包含多个MapLibre图层（点、线、面等）
    const possibleIds = [
      `${layerId}-point`,
      `${layerId}-line`,
      `${layerId}-polygon-fill`,
      `${layerId}-polygon-stroke`,
      `${layerId}-label`,
    ];

    possibleIds.forEach((id) => {
      if (this._map.getLayer(id)) {
        layerIds.push(id);
      }
    });

    // 如果没有找到子图层，检查是否存在同名图层
    if (layerIds.length === 0 && this._map.getLayer(layerId)) {
      layerIds.push(layerId);
    }

    return layerIds;
  }
}
