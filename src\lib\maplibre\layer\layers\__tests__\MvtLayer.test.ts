/**
 * @fileoverview MVT图层管线注记功能测试
 * @description 测试MvtLayer的管线检测和注记功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { MvtLayer } from '../MvtLayer';

// Mock MapLibre GL
const mockMap = {
  addSource: vi.fn(),
  addLayer: vi.fn(),
  removeLayer: vi.fn(),
  removeSource: vi.fn(),
  getLayer: vi.fn(),
  getSource: vi.fn(),
  setLayoutProperty: vi.fn(),
  setPaintProperty: vi.fn(),
  resize: vi.fn()
};

describe('MvtLayer 管线注记功能测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('管线图层检测', () => {
    it('应该正确检测包含"管线"关键字的图层', () => {
      const layer = new MvtLayer({
        id: 'test-layer',
        name: '智慧水务管线图层',
        url: 'https://example.com/tiles/{z}/{x}/{y}.pbf',
        type: 'line'
      });

      expect(layer.isPipelineLayer).toBe(true);
      expect(layer.labelLayerId).toBe('test-layer_label');
    });

    it('应该正确检测包含"管道"关键字的图层', () => {
      const layer = new MvtLayer({
        id: 'test-layer',
        name: '城市管道系统',
        url: 'https://example.com/tiles/{z}/{x}/{y}.pbf',
        type: 'line'
      });

      expect(layer.isPipelineLayer).toBe(true);
      expect(layer.labelLayerId).toBe('test-layer_label');
    });

    it('应该正确检测包含"pipeline"关键字的图层', () => {
      const layer = new MvtLayer({
        id: 'test-layer',
        name: 'Water Pipeline Network',
        url: 'https://example.com/tiles/{z}/{x}/{y}.pbf',
        type: 'line'
      });

      expect(layer.isPipelineLayer).toBe(true);
      expect(layer.labelLayerId).toBe('test-layer_label');
    });

    it('应该正确检测包含"pipe"关键字的图层', () => {
      const layer = new MvtLayer({
        id: 'test-layer',
        name: 'Water Pipe System',
        url: 'https://example.com/tiles/{z}/{x}/{y}.pbf',
        type: 'line'
      });

      expect(layer.isPipelineLayer).toBe(true);
      expect(layer.labelLayerId).toBe('test-layer_label');
    });

    it('应该正确识别非管线图层', () => {
      const layer = new MvtLayer({
        id: 'test-layer',
        name: '道路网络图层',
        url: 'https://example.com/tiles/{z}/{x}/{y}.pbf',
        type: 'line'
      });

      expect(layer.isPipelineLayer).toBe(false);
      expect(layer.labelLayerId).toBeUndefined();
    });
  });

  describe('注记图层控制', () => {
    let pipelineLayer: MvtLayer;

    beforeEach(() => {
      pipelineLayer = new MvtLayer({
        id: 'pipeline-layer',
        name: '供水管线',
        url: 'https://example.com/tiles/{z}/{x}/{y}.pbf',
        type: 'line',
        options: {
          paint: {
            'line-color': '#0066CC',
            'line-width': 2
          }
        }
      });
    });

    it('应该默认显示注记图层', () => {
      expect(pipelineLayer.labelVisible).toBe(true);
    });

    it('应该能够设置注记图层显示状态', () => {
      pipelineLayer.labelVisible = false;
      expect(pipelineLayer.labelVisible).toBe(false);

      pipelineLayer.labelVisible = true;
      expect(pipelineLayer.labelVisible).toBe(true);
    });

    it('应该能够切换注记图层显示状态', () => {
      const initialState = pipelineLayer.labelVisible;
      const newState = pipelineLayer.toggleLabel();
      
      expect(newState).toBe(!initialState);
      expect(pipelineLayer.labelVisible).toBe(newState);
    });
  });

  describe('图层添加和移除', () => {
    let pipelineLayer: MvtLayer;

    beforeEach(() => {
      pipelineLayer = new MvtLayer({
        id: 'pipeline-layer',
        name: '供水管线',
        url: 'https://example.com/tiles/{z}/{x}/{y}.pbf',
        type: 'line',
        options: {
          paint: {
            'line-color': '#0066CC',
            'line-width': 2
          }
        }
      });
    });

    it('应该在添加管线图层时同时添加注记图层', async () => {
      await pipelineLayer.addToInternal(mockMap as any);

      // 验证添加了数据源
      expect(mockMap.addSource).toHaveBeenCalledWith(
        'pipeline-layer',
        {
          type: 'vector',
          tiles: ['https://example.com/tiles/{z}/{x}/{y}.pbf']
        }
      );

      // 验证添加了主图层
      expect(mockMap.addLayer).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'pipeline-layer',
          source: 'pipeline-layer'
        })
      );

      // 验证添加了注记图层
      expect(mockMap.addLayer).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'pipeline-layer_label',
          type: 'symbol',
          source: 'pipeline-layer',
          minzoom: 18
        })
      );

      // 验证调用了两次addLayer（主图层 + 注记图层）
      expect(mockMap.addLayer).toHaveBeenCalledTimes(2);
    });

    it('应该在移除图层时同时移除注记图层', () => {
      // 模拟图层存在
      mockMap.getLayer.mockImplementation((id: string) => {
        return id === 'pipeline-layer' || id === 'pipeline-layer_label';
      });
      mockMap.getSource.mockReturnValue(true);

      pipelineLayer.removeInternal();

      // 验证移除了注记图层
      expect(mockMap.removeLayer).toHaveBeenCalledWith('pipeline-layer_label');
      
      // 验证移除了主图层
      expect(mockMap.removeLayer).toHaveBeenCalledWith('pipeline-layer');
      
      // 验证移除了数据源
      expect(mockMap.removeSource).toHaveBeenCalledWith('pipeline-layer');
    });

    it('应该在刷新图层时重新创建注记图层', async () => {
      // 模拟图层存在
      mockMap.getLayer.mockImplementation((id: string) => {
        return id === 'pipeline-layer' || id === 'pipeline-layer_label';
      });
      mockMap.getSource.mockReturnValue(true);

      // 使用 vi.useFakeTimers 来控制 setTimeout
      vi.useFakeTimers();

      const refreshPromise = pipelineLayer.refresh();

      // 验证移除了图层
      expect(mockMap.removeLayer).toHaveBeenCalledWith('pipeline-layer_label');
      expect(mockMap.removeLayer).toHaveBeenCalledWith('pipeline-layer');
      expect(mockMap.removeSource).toHaveBeenCalledWith('pipeline-layer');

      // 快进时间，触发 setTimeout 回调
      vi.advanceTimersByTime(500);

      await refreshPromise;

      // 恢复真实的定时器
      vi.useRealTimers();
    });
  });

  describe('非管线图层行为', () => {
    let regularLayer: MvtLayer;

    beforeEach(() => {
      regularLayer = new MvtLayer({
        id: 'road-layer',
        name: '道路网络',
        url: 'https://example.com/tiles/{z}/{x}/{y}.pbf',
        type: 'line',
        options: {
          paint: {
            'line-color': '#666666',
            'line-width': 1
          }
        }
      });
    });

    it('应该不创建注记图层', async () => {
      await regularLayer.addToInternal(mockMap as any);

      // 验证只添加了主图层，没有注记图层
      expect(mockMap.addLayer).toHaveBeenCalledTimes(1);
      expect(mockMap.addLayer).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'road-layer',
          source: 'road-layer'
        })
      );
    });

    it('应该返回正确的管线图层状态', () => {
      expect(regularLayer.isPipelineLayer).toBe(false);
      expect(regularLayer.labelLayerId).toBeUndefined();
    });
  });

  describe('显示状态联动', () => {
    let pipelineLayer: MvtLayer;

    beforeEach(() => {
      pipelineLayer = new MvtLayer({
        id: 'pipeline-layer',
        name: '供水管线',
        url: 'https://example.com/tiles/{z}/{x}/{y}.pbf',
        type: 'line'
      });

      // 模拟地图和图层已添加
      (pipelineLayer as any)._map = mockMap;
      mockMap.getLayer.mockReturnValue(true);
    });

    it('应该在主图层显示时同步显示注记图层', () => {
      pipelineLayer.labelVisible = true;
      (pipelineLayer as any).showInternal(true);

      expect(mockMap.setLayoutProperty).toHaveBeenCalledWith(
        'pipeline-layer_label',
        'visibility',
        'visible'
      );
    });

    it('应该在主图层隐藏时同步隐藏注记图层', () => {
      pipelineLayer.labelVisible = true;
      (pipelineLayer as any).showInternal(false);

      expect(mockMap.setLayoutProperty).toHaveBeenCalledWith(
        'pipeline-layer_label',
        'visibility',
        'none'
      );
    });

    it('应该在注记被禁用时隐藏注记图层', () => {
      pipelineLayer.labelVisible = false;
      (pipelineLayer as any).showInternal(true);

      expect(mockMap.setLayoutProperty).toHaveBeenCalledWith(
        'pipeline-layer_label',
        'visibility',
        'none'
      );
    });
  });
});
