import BaseMeasure from "@/views/tinytools/measure/index.vue";
import CoordPicker from "@/views/tinytools/coordPicker/index.vue";
import BookMark from "@/views/tinytools/bookMark/index.vue";
import RollingCompare from "@/views/tinytools/rollingCompare/index.vue";
// import SpatialQuery from "@/views/maplibre/pages/query/SpatialQuery.vue";
import PlotPanel from "@/views/maplibre/plotPanel/index.vue";
import DrawPanel from "@/views/maplibre/plotPanel/DrawPanel.vue";
import CoordLoc from "@/views/maplibre/searchBar/index.vue";
import DeviceResearch from "@/views/maplibre/searchDevice/index.vue";
import RoamingPanel from "@/views/cesium/roam/index.vue"
import RouteManagerPanel from "@/views/cesium/roam/RouteManagerPanel.vue"
import ModelDetail from "@/views/cesium/popup/ModelDetail.vue"
// import TabulateStatistics from "@/views/query/TabulateStatistics.vue";
import ClassifiedStatistics from "@/views/maplibre/pages/query/ClassifiedStatistics.vue";
import MapAnnotation from "@/views/cesium/MapAnnotation/index.vue";
import LayerTree from "@/components/LayerTree.vue";
import PipePropertyCorrectionDialog from "@/components/dialog/PipePropertyCorrectionDialog.vue";
import DeviceDetail from "@/views/maplibre/popup/DeviceDetail.vue"
import AlarmDetail from "@/views/maplibre/popup/AlarmDetail.vue"
import LeakDetail from "@/views/maplibre/popup/LeakDetail.vue"
import UploadFile from "@/views/maplibre/popup/UploadFile.vue"
import PipeNodePanel from "@/views/maplibre/pages/dataEdition/PipeNodePanel.vue"
import PipeLinePanel from "@/views/maplibre/pages/dataEdition/PipeLinePanel.vue"
import { useOperateToolStore } from '@/stores/OperateToolStore'
interface IMenuItem {
  childrenList?: IMenuItem[];
  icon?: string;
  id?: string;
  name: string;
  path: string; // 组件名
  type?: string;
  modelSection?: any;
  layerId?: string | undefined;
  params?: any;
}

interface IMainComponent {
  component: any;
  name: string;
  title: string | undefined;
  showTab: boolean;
  show: boolean; // 是否显示
  isFocus: boolean; // 是否聚焦
  baseZIndex: number;
  type: string | undefined;
  uuid: string;
  modelSection: any;
  layerId: string | undefined;
  params?: any
}
const dialogMap: Record<string, any> = {
  BaseMeasure: BaseMeasure,
  CoordPicker: CoordPicker,
  BookMark: BookMark,
  RollingCompare: RollingCompare,
  // SpatialQuery: SpatialQuery,
  DrawPanel: DrawPanel,
  CoordLoc: CoordLoc,
  PlotPanel: PlotPanel,
  DeviceResearch: DeviceResearch,
  RoamingPanel: RoamingPanel,
  RouteManagerPanel: RouteManagerPanel,
  LayerTree: LayerTree,
  // TabulateStatistics: TabulateStatistics,
  ClassifiedStatistics: ClassifiedStatistics,
  MapAnnotation: MapAnnotation,
  PipePropertyCorrectionDialog: PipePropertyCorrectionDialog,
  DeviceDetail: DeviceDetail,
  AlarmDetail: AlarmDetail,
  LeakDetail: LeakDetail,
  ModelDetail: ModelDetail,
  PipeNodePanel: PipeNodePanel,
  PipeLinePanel: PipeLinePanel,
  UploadFile: UploadFile
};
const uuid = () => {
  const temp_url = URL.createObjectURL(new Blob());
  const uuid = temp_url.toString();
  URL.revokeObjectURL(temp_url);
  return uuid.substring(uuid.lastIndexOf("/") + 1);
};
import { defineStore } from "pinia";

export const useDialogStore = defineStore("Dialogs", () => {
  const mainComponents: Ref<Array<IMainComponent>> = ref([]);
  const editTabsValue = ref<string>("");
  const detailDialogEnable = ref(true);
  function addDialog(item: IMenuItem) {
    if (!item || !item.path) return false;
    const getIndex = (name: string) =>
      mainComponents.value.findIndex((ele) => ele.name === name);
    const index = getIndex(item.path);
    if (index > -1) {
      // 如果已存在同名对话框，更新标题和组件数据
      const existingDialog = mainComponents.value[index];
      existingDialog.title = item.name;

      // 重新创建组件实例以更新 props 数据
      existingDialog.component = h(dialogMap[item.path], item?.params || {});
      // 更新其他属性
      existingDialog.type = item?.type;
      existingDialog.modelSection = item?.modelSection;
      existingDialog.layerId = item?.layerId;
      existingDialog.params = item?.params;

      // 设置为聚焦状态
      mainComponents.value.forEach((ele: IMainComponent) => (ele.isFocus = false));
      existingDialog.isFocus = true;

      return true; // 返回 true 表示已更新现有对话框
    }
    // 创建组件实例时传递 params 作为 props
    const componentIns = h(dialogMap[item.path], item?.params || {});
    mainComponents.value.forEach(
      (ele: IMainComponent) => (ele.isFocus = false)
    );
    mainComponents.value.push({
      component: componentIns,
      name: item.path,
      title: item.name,
      showTab: true,
      show: true,
      uuid: uuid(),
      isFocus: true,
      baseZIndex: 6,
      type: item?.type,
      modelSection: item?.modelSection,
      layerId: item?.layerId,
      params: item?.params
    });
  }

  function closeDialogById(id: string) {
    mainComponents.value.splice(
      mainComponents.value.findIndex((item: any) => item.uuid === id),
      1
    );
  }

  function closeDialog(componentName: string) {
    const targetIdx = mainComponents.value.findIndex(
      (item) => item.name === componentName
    );
    if (targetIdx === -1) return;
    mainComponents.value.splice(targetIdx, 1);
    useOperateToolStore().disactive(componentName)
  }

  function closeAll() {
    mainComponents.value = []
  }

  const clear = () => {
    mainComponents.value.splice(0, mainComponents.value.length);
  };

  function changeDetailDialogEnable(value: boolean) {
    detailDialogEnable.value = value;
  }

  return {
    mainComponents,
    addDialog,
    closeDialog,
    closeAll,
    closeDialogById,
    clear,
    editTabsValue,
    detailDialogEnable,
    changeDetailDialogEnable
  };
});
