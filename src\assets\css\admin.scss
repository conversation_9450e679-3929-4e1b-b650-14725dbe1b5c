.base-main {
  >div:not(.el-overlay) {
    --at-apply: 'box-border p-5 rounded-1.5 bg-#fff overflow-hidden';
  }

  --at-apply: 'box-border w-full h-full p-4 bg-#eef0f4';
}

// 分页
.pagibox {
  // border-top: 3px solid #e9eff3;
  // width: 100%;
  display: flex;
  // height: 80px;
  align-items: center;
  justify-content: end;
  padding: 20px 10px 0 0;

  .pagitotal {
    font-size: 14px;
    color: #323233;
    padding-right: 20px;
  }

  .admin-pagi {
    .el-select {
      width: 95px;
      font-size: 14px;
      color: #323233;
    }

    .btn-prev,
    .btn-next {
      width: 32px;
      height: 32px;
      border: 1px solid #d8d8d8;
      border-radius: 4px;
      color: rgba(0, 0, 0, 0.75);
      background: transparent;
    }

    .el-pagination__jump {
      font-size: 14px;
      color: #323233;
    }

    .el-pager {

      .number,
      .more {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #ebebeb;
        background-color: #fff;
        font: 400 14px Source-CN-Regular;
        color: #202020;
        letter-spacing: 0.56px;
      }

      .number.is-active {
        background-color: #1966FF !important;
        color: #ffffff;
      }
    }

    .btn-prev:hover,
    .btn-next:hover {
      border: 1px solid #1966FF;
      color: #1966FF;
    }

    .btn-prev:disabled {
      border: 1px solid #ebebeb;
      color: rgba(0, 0, 0, 0.75);
      background: transparent;
    }

    .btn-next:disabled {
      border: 1px solid #ebebeb;
      color: rgba(0, 0, 0, 0.75);
      background: transparent;
    }

    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .el-pager li {
      background-color: transparent;
    }
  }
}

.admin-add-btn {
  width: 100px;
}

.admin-query-btn {
  background: linear-gradient(90.00deg, rgba(4, 83, 255, 1), rgba(50, 127, 254, 1) 100%);
}

.admin-reset-btn.el-button {
  border-color: rgba(25, 102, 255, 1);
  color: #1966FF;
}

.tb-line {
  width: 1px;
  height: 20px;
  background: #DBE7F9;
  margin: 0 8px;
}

// .admin-select.el-select {
//   .el-select-dropdown__item {
//     color: #1D2129;
//     /* 下拉选项字体颜色 */
//   }
// }
// .admin-select {

//   .el-select__wrapper.is-disabled {
//     background-color: transparent;
//   }
// }

.admin-popper-select {
  --el-fill-color-light: #F2F3F5;

  .el-popper__arrow::before {
    content: none;
  }

  .el-select-dropdown__item {
    color: #1D2129;
  }
}