import { ref } from 'vue'
import { defineStore } from 'pinia'
export interface LegendItem {
  name: string;
  value: string;
}
export interface LegendObj {
  title: string,
  data: LegendItem[]
}

export const useLegend = defineStore('Legend', () => {
  const legends = ref<LegendObj>()
  function setLegend(value: LegendObj) {
    if(value !== legends.value) {
      legends.value = value
    }
  }
  function clearLegend() {
    legends.value = undefined;
  }
  return { legends, setLegend, clearLegend }
})