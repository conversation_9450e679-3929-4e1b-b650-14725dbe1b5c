<template>
  <BaseCard title="管径统计">
    <div class="chart" ref="pipeRef"></div>
  </BaseCard>
</template>
<script lang="ts" setup>
import { initDiameterEchart } from "@/lib/echarts";
import { gjLengthStat } from "@/api/home";

const { initChart } = useEchart();
const pipeRef = ref();
const xData = ref<any>([]);
const list = ref<any>([]);
const getChart = async () => {
  const { data, code } = await gjLengthStat();
  if (code === 200) {
    xData.value = Object.keys(data).map((key) => {
      return key;
    });
    list.value = Object.keys(data).map((key) => {
      return data[key];
    });
  }
  pipeRef.value &&
    initChart(pipeRef.value, initDiameterEchart(xData.value, list.value));
};
onMounted(() => {
  getChart();
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 250px;
}
</style>
