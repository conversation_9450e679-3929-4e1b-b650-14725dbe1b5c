/**
 * @fileoverview 标绘数据管理器
 * @description 管理标绘要素的数据库持久化存储、CRUD操作，使用统一的GeoJSON Feature格式
 * <AUTHOR>
 * @version 4.0.0
 */

import { type PlotFeature, type FeatureStyle, DEFAULT_FEATURE_STYLE, type PlotGeoJSONFeature, type PlotFeatureProperties } from './types/LayerTypes';
import { PlotFeatureAdapter } from '@/services/PlotFeatureAdapter';
import { 
  plottingPage, 
  plottingAdd, 
  plottingEdit, 
  plottingDetail, 
  plottingDelete 
} from '@/api/plotting';
import type { PlottingPageQuery } from '@/types/plotting';
import { EngineType } from '@/types/plotting';

/**
 * @description 二维标绘引擎类型常量 - 固定为MapLibre
 */
const MAPLIBRE_ENGINE_TYPE = EngineType.MAPLIBRE;

/**
 * @description 批量操作结果
 */
interface BatchOperationResult {
  success: boolean;
  processedCount: number;
  errors: Array<{ id: string; error: string }>;
  totalCount: number;
}

/**
 * @description 数据验证结果
 */
interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * @description 分页查询结果
 */
interface PageResult {
  features: PlotFeature[];
  total: number;
  pageNum: number;
  pageSize: number;
}

/**
 * @class PlotDataManager - 数据库版
 * @description 标绘数据管理器，提供数据库的高性能CRUD操作
 * 专门用于二维MapLibre标绘，engineType固定为'Maplibre'
 */
export class PlotDataManager {
  private static instance: PlotDataManager;

  /**
   * @description 获取单例实例
   */
  static getInstance(): PlotDataManager {
    if (!this.instance) {
      this.instance = new PlotDataManager();
    }
    return this.instance;
  }

  /**
   * @description 私有构造函数
   */
  private constructor() {
    console.log('PlotDataManager 初始化完成 - MapLibre二维标绘模式');
  }

  /**
   * @description 验证要素数据
   * @param feature - 要素数据
   * @returns 验证结果
   * @private
   */
  private validateFeature(feature: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // 必需字段检查（临时ID -1 在保存时会被数据库替换）
    if (!feature.id && feature.id !== -1) {
      result.errors.push('要素缺少ID');
      result.isValid = false;
    }

    if (!feature.name) {
      result.warnings.push('要素缺少名称');
    }

    if (!feature.geojson) {
      result.errors.push('要素缺少GeoJSON数据');
      result.isValid = false;
      return result;
    }

    if (!feature.geojson.geometry) {
      result.errors.push('要素缺少几何数据');
      result.isValid = false;
    }

    if (!feature.geojson.properties?.geometryType) {
      result.errors.push('要素缺少几何类型');
      result.isValid = false;
    }

    // 引擎类型验证 - 二维标绘必须为MapLibre
    if (!feature.engineType) {
      result.errors.push('要素缺少引擎类型');
      result.isValid = false;
    } else if (feature.engineType !== MAPLIBRE_ENGINE_TYPE) {
      result.errors.push('二维标绘要素引擎类型必须为MapLibre');
      result.isValid = false;
    }

    // 几何数据验证
    if (feature.geojson.geometry) {
      const geoValidation = this.validateGeometry(feature.geojson.geometry);
      if (!geoValidation.isValid) {
        result.errors.push(...geoValidation.errors);
        result.isValid = false;
      }
      result.warnings.push(...geoValidation.warnings);
    }

    return result;
  }

  /**
   * @description 验证几何数据
   * @param geometry - 几何数据
   * @returns 验证结果
   * @private
   */
  private validateGeometry(geometry: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    if (!geometry.type) {
      result.errors.push('几何数据缺少类型');
      result.isValid = false;
      return result;
    }

    if (!geometry.coordinates || !Array.isArray(geometry.coordinates)) {
      result.errors.push('几何数据缺少有效坐标');
      result.isValid = false;
      return result;
    }

    // 根据类型验证具体坐标结构
    switch (geometry.type) {
      case 'Point':
        return this.validatePointCoordinates(geometry.coordinates);
      case 'LineString':
        return this.validateLineStringCoordinates(geometry.coordinates);
      case 'Polygon':
        return this.validatePolygonCoordinates(geometry.coordinates);
      default:
        console.warn(`未知的几何类型: ${geometry.type}`);
          result.isValid = false;
        result.errors.push(`未知的几何类型: ${geometry.type}`);
        return result;
    }
  }

  /**
   * @description 验证点坐标
   */
  private validatePointCoordinates(coordinates: any): ValidationResult {
    const result: ValidationResult = { isValid: true, errors: [], warnings: [] };
    
    if (!Array.isArray(coordinates) || 
        coordinates.length < 2 || 
        typeof coordinates[0] !== 'number' || 
        typeof coordinates[1] !== 'number') {
      result.isValid = false;
      result.errors.push('点坐标不完整');
    }

    return result;
  }

  /**
   * @description 验证线坐标
   */
  private validateLineStringCoordinates(coordinates: any): ValidationResult {
    const result: ValidationResult = { isValid: true, errors: [], warnings: [] };
    
    if (!Array.isArray(coordinates) || 
        coordinates.length < 2 || 
        !coordinates.every((coord: any) => this.validatePointCoordinates(coord).isValid)) {
      result.isValid = false;
      result.errors.push('线段至少需要2个有效点');
    }
    
    return result;
  }

  /**
   * @description 验证面坐标
   */
  private validatePolygonCoordinates(coordinates: any): ValidationResult {
    const result: ValidationResult = { isValid: true, errors: [], warnings: [] };
    
    if (!Array.isArray(coordinates) || 
        coordinates.length < 1 || 
        !Array.isArray(coordinates[0]) || 
        coordinates[0].length < 4 || 
        !coordinates[0].every((coord: any) => this.validatePointCoordinates(coord).isValid)) {
      result.isValid = false;
      result.errors.push('多边形环至少需要4个有效点');
    }
    
    return result;
  }

  /**
   * @description 分页获取要素
   * @param pageQuery 分页查询参数
   * @returns Promise<PageResult> 分页结果
   */
  async getFeatures(pageQuery: PlottingPageQuery): Promise<PageResult> {
    try {
      // 添加MapLibre引擎类型过滤条件
      const queryWithEngineType: PlottingPageQuery = {
        ...pageQuery,
        engineType: MAPLIBRE_ENGINE_TYPE
      };
      
      const response = await plottingPage(queryWithEngineType);
      if (response.code === 200 && response.data) {
        const features = PlotFeatureAdapter.fromPageVoList(response.data.list);
        return {
          features,
          total: response.data.totalCount,
          pageNum: pageQuery.pageNum || 1,
          pageSize: pageQuery.pageSize || 10
        };
      } else {
        throw new Error(`获取要素失败: ${response.msg || '未知错误'}`);
      }
        } catch (error) {
      console.error('获取要素数据失败:', error);
      throw error;
    }
  }

  /**
   * @description 获取所有要素（用于兼容性）
   * @returns Promise<PlotFeature[]> 所有要素
   */
  async getAllFeatures(): Promise<PlotFeature[]> {
    try {
      // 获取第一页数据来确定总数，仅获取MapLibre引擎的数据
      const firstPage = await this.getFeatures({ 
        pageNum: 1, 
        pageSize: 10,
        engineType: MAPLIBRE_ENGINE_TYPE 
      });
      
      if (firstPage.total <= 10) {
        return firstPage.features;
      }
      
      // 如果总数大于10，获取所有数据
      const allPage = await this.getFeatures({ 
        pageNum: 1, 
        pageSize: firstPage.total,
        engineType: MAPLIBRE_ENGINE_TYPE 
      });
      return allPage.features;
    } catch (error) {
      console.error('获取所有要素失败:', error);
      return [];
    }
  }

  /**
   * @description 根据ID获取要素
   * @param id - 要素ID
   * @returns Promise<PlotFeature | undefined> 要素数据
   */
  async getFeature(id: number): Promise<PlotFeature | undefined> {
    try {
      if (!id || id < 0) {
        console.warn(`无效的要素ID: ${id}`);
        return undefined;
      }
      
      const response = await plottingDetail(id);
      
      if (response.code === 200 && response.data) {
        return PlotFeatureAdapter.fromInfoVo(response.data);
      } else {
        console.warn(`获取要素详情失败: ${response.msg || '未知错误'}`);
        return undefined;
      }
    } catch (error) {
      console.error('获取要素失败:', error);
      return undefined;
    }
  }

  /**
   * @description 添加要素
   * @param feature - 要素数据
   * @returns Promise<boolean> 是否成功
   */
  async addFeature(feature: PlotFeature): Promise<boolean> {
    try {
      // 强制设置为MapLibre引擎类型（二维标绘专用）
      feature.engineType = MAPLIBRE_ENGINE_TYPE;
      
      // 验证要素数据
      const validation = this.validateFeature(feature);
      if (!validation.isValid) {
        console.error('要素数据验证失败:', validation.errors);
        return false;
      }

      if (validation.warnings.length > 0) {
        console.warn('要素数据警告:', validation.warnings);
      }

      const createRequest = PlotFeatureAdapter.toCreateRequest(feature);
      const response = await plottingAdd(createRequest);
      
      if (response.code === 200 && response.data) {
        // 更新要素ID为数据库返回的ID
        feature.id = response.data;
        
        return true;
      } else {
        console.error(`保存要素失败: ${response.msg || '未知错误'}`);
        return false;
      }
    } catch (error) {
      console.error('保存要素失败:', error);
      return false;
    }
  }

  /**
   * @description 更新要素
   * @param feature - 要素数据
   * @returns Promise<boolean> 是否成功
   */
  async updateFeature(feature: PlotFeature): Promise<boolean> {
    try {
      // 确保引擎类型为MapLibre（二维标绘专用）
      feature.engineType = MAPLIBRE_ENGINE_TYPE;
      
      // 验证要素数据
      const validation = this.validateFeature(feature);
      if (!validation.isValid) {
        console.error('要素数据验证失败:', validation.errors);
        return false;
      }

      if (validation.warnings.length > 0) {
        console.warn('要素数据警告:', validation.warnings);
      }

      const updateRequest = PlotFeatureAdapter.toUpdateRequest(feature);
      const response = await plottingEdit(updateRequest);
      
      if (response.code === 200) {
        return true;
      } else {
        console.error(`更新要素失败: ${response.msg || '未知错误'}`);
        return false;
      }
    } catch (error) {
      console.error('更新要素失败:', error);
      return false;
    }
  }

  /**
   * @description 删除要素
   * @param id - 要素ID
   * @returns Promise<boolean> 是否成功
   */
  async deleteFeature(id: number): Promise<boolean> {
    try {
      if (!id || id < 0) {
        console.warn(`无效的要素ID: ${id}`);
        return false;
      }
      
      const response = await plottingDelete(id);
      
      if (response.code === 200) {
        return true;
      } else {
        console.error(`删除要素失败: ${response.msg || '未知错误'}`);
        return false;
      }
    } catch (error) {
      console.error('删除要素失败:', error);
      return false;
    }
  }

  /**
   * @description 批量删除要素
   * @param ids - 要素ID数组
   * @returns Promise<number> 成功删除的数量
   */
  async deleteFeatures(ids: number[]): Promise<number> {
      let deleteCount = 0;
      
    for (const id of ids) {
      try {
        const success = await this.deleteFeature(id);
        if (success) {
          deleteCount++;
        }
      } catch (error) {
        console.error(`删除要素 ${id} 失败:`, error);
      }
      }
      
      return deleteCount;
  }

  /**
   * @description 获取存储统计信息
   * @returns Promise<object> 统计信息
   */
  async getStorageStats(): Promise<{
    totalFeatures: number;
    featuresByType: Record<string, number>;
  }> {
    try {
      // 获取第一页来获取总数
      const firstPage = await this.getFeatures({ pageNum: 1, pageSize: 1 });
      
      // 如果需要详细的类型统计，获取所有数据
      const allFeatures = await this.getAllFeatures();
      const featuresByType: Record<string, number> = {};
      
      allFeatures.forEach(feature => {
        const geometryType = feature.geojson.properties.geometryType;
        featuresByType[geometryType] = (featuresByType[geometryType] || 0) + 1;
      });
      
      return {
        totalFeatures: firstPage.total,
        featuresByType
      };
    } catch (error) {
      console.error('获取存储统计失败:', error);
      return {
        totalFeatures: 0,
        featuresByType: {}
      };
    }
  }

  /**
   * @description 销毁实例并清理资源
   */
  destroy(): void {
    PlotDataManager.instance = null as any;
    console.log('PlotDataManager 资源已清理 - MapLibre二维标绘模式');
  }
} 