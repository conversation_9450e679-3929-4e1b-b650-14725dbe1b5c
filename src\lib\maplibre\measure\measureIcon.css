
 
.measure-move {
    background-color: white;
    border-radius: 3px;
    height: 16px;
    line-height: 16px;
    padding: 0 3px;
    font-size: 12px;
    box-shadow: 0 0 0 1px #ccc;
    float: right;
    cursor: default;
}
 
.measure-result {
    background-color: white;
    border-radius: 3px;
    height: 16px;
    line-height: 16px;
    padding: 0 3px;
    font-size: 12px;
    box-shadow: 0 0 0 1px #ccc;
    float: right;
    cursor: default;
    z-index: 10;
}
 
.close {
    width: 3px;
    height: 14px;
    text-align: center;
    border-radius: 3px;
    padding-top: 0px;
    padding-right: 10px;
    box-shadow: 0 0 0 0px #ccc;
    cursor: pointer;
    background: url('@/assets/images/close.png') no-repeat center;
    border: 1px solid rgba(100, 100, 100, 0)
}
 
.clear {
    width: 3px;
    height: 14px;
    text-align: center;
    border-radius: 3px;
    padding-right: 10px;
    box-shadow: 0 0 0 0px #ccc;
    cursor: pointer;
    float: right;
    background: url('@/assets/images/close.png') no-repeat center;
    border: 1px solid rgba(100, 100, 100, 0)
}
 
.edit {
    width: 3px;
    height: 14px;
    text-align: center;
    border-radius: 3px;
    padding-right: 10px;
    box-shadow: 0 0 0 0px #ccc;
    cursor: pointer;
    float: right;
    /* background: url(../measureicon/edit.png) no-repeat center; */
    border: 1px solid rgba(100, 100, 100, 0)
}
 
.close:hover {
    border: 1px solid rgba(52, 98, 152, 1);
}
 
.clear:hover {
    border: 1px solid rgba(52, 98, 152, 1);
}
 
.edit:hover {
    border: 1px solid rgba(52, 98, 152, 1);
}