/*
 * @Author: xiao
 * @Date: 2024-07-03 14:52:08
 * @LastEditors: xiao
 * @LastEditTime: 2024-07-19 09:59:28
 * @Description:
 */
// uno.config.ts
import {
  defineConfig,
  presetAttributify,
  presetUno,
  transformerVariantGroup,
  transformerDirectives
} from 'unocss';
import presetRemToPx from '@unocss/preset-rem-to-px';

export default defineConfig({
  // ...UnoCSS options
  rules: [
    ['flex-c', { display: 'flex', 'align-items': 'center', 'justify-content': 'center' }],
    [
      'base-main',
      {
        'box-sizing': 'border-box',
        width: '100%',
        height: '100%',
        padding: '10px',
        'background-color': '#eef0f4'
      }
    ],
    ['admin-shadow', { 'box-shadow': '0 0 14px 0 rgba(92,122,239,0.16)' }]
  ],
  theme: {},
  presets: [
    presetUno(),
    presetRemToPx({
      baseFontSize: 16
    }),
    /**
     * 官网-预设-属性预设(Attributify)
     * 无值属性: 原生html元素可以直接写样式,不需要在class中写了 <span w-2 h-2 ...></span>
     * 属性分类: <span absolute left-2 top-2 ...></span> 可以写成 <span position="left-2 top-2" border="..." font="..."></span>
     * */
    presetAttributify()
  ],
  transformers: [
    transformerVariantGroup(), // 例: hover:... hover:... --> hover:(... ...)
    transformerDirectives({
      // @apply 使用(多个元素有相同样式) .case {--at--apply: 'flex flex-col ....'} <div class="case"></div>
      applyVariable: ['--at-apply']
    })
  ]
});
