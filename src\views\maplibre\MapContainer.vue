<!--
  @fileoverview MapLibre地图容器组件
  @description 负责创建和管理MapLibre地图实例，配置高德地图底图和交互事件
  <AUTHOR>
  @version 2.0.0 - 增强生命周期管理和引擎切换支持
-->

<template>
  <!-- 地图容器DOM元素 -->
  <div id="map" ref="map" />

  <!-- 鼠标坐标显示组件 -->
  <MapLibreCoordinateBar v-if="!isAnalysisOnly" />

  <!-- 地名地址搜索框组件 -->
  <div class="place-search-wrapper" v-if="!isAnalysisOnly">
    <BMapPlaceSearchBar />
  </div>

  <!-- 坐标搜索框组件 -->
  <MapLibreSearchBar v-if="!isAnalysisOnly" />
  <!-- <DrawToolExample /> -->
</template>

<script setup lang="ts">
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { Map as GlMap, NavigationControl, ScaleControl } from "maplibre-gl";
import MapLibreCoordinateBar from "@/components/mouseCoordinate/MapLibreCoordinateBar.vue";
// import PlaceSearchBar from "./searchBar/PlaceSearchBar.vue";
import BMapPlaceSearchBar from './searchBar/BMapPlaceSearchBar.vue';
import MapLibreSearchBar from './searchBar/index.vue';
import { onMounted, onUnmounted, onBeforeUnmount } from 'vue';
import { getEngineSwitch } from '@/utils/EngineSwitch';

/** @description 地图DOM元素引用 */
const map = ref();
const analysisModeStore = useAnalysisModeStore();
const { isAnalysisOnly } = storeToRefs(analysisModeStore);
/** @description MapLibre地图实例引用 */
let mapInstance: GlMap | null = null;

/** @description 组件是否已销毁标志 */
let isDestroyed = false;

/** @description 初始化是否成功标志 */
let initializationSuccessful = false;

/** @description 引擎切换管理器实例 */
const engineSwitch = getEngineSwitch();

/** @description 导航控件实例 */
let navigationControl: NavigationControl | null = null;

/** @description 比例尺控件实例 */
let scaleControl: ScaleControl | null = null;

/** @description 绘制模块实例 */
let drawModule: any = null;

/**
 * @description 组件挂载后初始化地图
 */
onMounted(async () => {
  try {
    console.log("🚀 [MapContainer] 开始初始化MapLibre地图...");

    // 检查DOM容器是否存在
    if (!map.value) {
      throw new Error("地图容器DOM元素不存在");
    }

    // 初始化地图实例
    mapInstance = await initializeMapLibre();

    if (!mapInstance) {
      throw new Error("MapLibre地图创建失败");
    }

    // 添加地图控件
    await addMapControls(mapInstance);

    // 设置地图事件监听器
    setupMapEventListeners(mapInstance);

    // 初始化标绘模块
    await initializeDrawModule(mapInstance);

    // 标记初始化成功
    initializationSuccessful = true;

    // 注册清理任务到引擎切换管理器
    registerCleanupTasks();

    console.log("✅ [MapContainer] MapLibre地图初始化完成");
  } catch (error) {
    console.error("❌ [MapContainer] MapLibre初始化失败:", error);

    // 初始化失败时的清理
    await performEmergencyCleanup();

    // 可以向用户显示错误提示
    // ElMessage.error('二维地图初始化失败，请刷新页面重试');
  }
});

/**
 * @description 初始化MapLibre地图实例
 * @returns Promise<GlMap | null> MapLibre地图实例
 */
const initializeMapLibre = async (): Promise<GlMap | null> => {
  try {
    console.log("📋 [MapContainer] 创建MapLibre地图实例...");

    // 初始化地图实例
    const _map = AppMaplibre.initMap("map");

    if (!_map) {
      throw new Error("AppMaplibre.initMap返回null");
    }

    console.log("✅ [MapContainer] MapLibre地图实例创建成功");
    return _map;
  } catch (error) {
    console.error("❌ [MapContainer] 创建MapLibre地图失败:", error);
    return null;
  }
};

/**
 * @description 添加地图控件
 * @param _map MapLibre地图实例
 */
const addMapControls = async (_map: GlMap): Promise<void> => {
  try {
    console.log("📋 [MapContainer] 添加地图控件...");

    /**
     * @description 创建导航控件
     * @details 配置导航控件选项，隐藏俯仰角可视化、指南针和缩放按钮
     */
    navigationControl = new NavigationControl({
      visualizePitch: false, // 不显示俯仰角可视化
      showCompass: false, // 不显示指南针
      showZoom: false, // 不显示缩放按钮
    });

    // 将导航控件添加到地图右上角
    _map.addControl(navigationControl, "top-right");

    /**
     * @description 创建比例尺控件
     * @details 显示地图比例尺，支持公制和英制单位切换
     */
    scaleControl = new ScaleControl({
      maxWidth: 100, // 比例尺最大宽度（像素）
      unit: "metric", // 使用公制单位（米/千米）
    });

    // 将比例尺控件添加到地图左下角
    _map.addControl(scaleControl, "bottom-left");

    console.log("✅ [MapContainer] 地图控件添加完成");
  } catch (error) {
    console.error("❌ [MapContainer] 添加地图控件失败:", error);
    throw error;
  }
};

/**
 * @description 设置地图事件监听器
 * @param _map MapLibre地图实例
 */
const setupMapEventListeners = (_map: GlMap): void => {
  try {
    console.log("📋 [MapContainer] 设置地图事件监听器...");

    /**
     * @description 地图点击事件处理
     * @param {any} e - 点击事件对象
     * @details 在点击位置周围查询要素并输出到控制台
     */
    const handleMapClick = (e: any) => {
      // 设置查询区域的偏移量（像素）
      const offset = 10;
      console.log(_map.getLayersOrder());
      /**
       * @description 查询点击位置周围的渲染要素
       * @details 在点击点周围10像素范围内查询可见的地图要素
       */
      const allLayerIds = _map.getLayersOrder();
      const clickLayerIds = allLayerIds.filter((item) =>
        item.includes("device_")
      );
      if (allLayerIds.includes("alarm_layer")) {
        clickLayerIds.push("alarm_layer");
      }
      if (allLayerIds.includes("leak_layer")) {
        clickLayerIds.push("leak_layer");
      }
      const features = _map.queryRenderedFeatures(
        [
          [e.point.x - offset / 2, e.point.y - offset / 2],
          [e.point.x + offset / 2, e.point.y + offset / 2],
        ],
        {
          layers: clickLayerIds,
        }
      );

      // 如果查询到要素，输出第一个要素信息
      if (features.length > 0) {
        const feature = features[0];
        if (feature.layer.id.indexOf("_cluster") !== -1) return;
        if (feature.layer.id === "alarm_layer") {
          useDialogStore().addDialog({
            name: "告警详情",
            path: "AlarmDetail",
            params: {
              properties: feature.properties.alarmLists,
            },
          });
        } else if (feature.layer.id === "leak_layer") {
          //todo: 漏水事件弹框
          useDialogStore().addDialog({
            name: "漏水事件详情",
            path: "LeakDetail",
            params: {
              properties: feature.properties,
            },
          });
        } else {
          useDialogStore().addDialog({
            name: "设备详情",
            path: "DeviceDetail",
            params: {
              properties: feature.properties,
            },
          });
        }

        console.log(feature);
      }
    };

    _map.on("click", handleMapClick);

    console.log("✅ [MapContainer] 地图事件监听器设置完成");
  } catch (error) {
    console.error("❌ [MapContainer] 设置地图事件监听器失败:", error);
  }
};

/**
 * @description 初始化标绘模块
 * @param _map MapLibre地图实例
 */
const initializeDrawModule = async (_map: GlMap): Promise<void> => {
  try {
    console.log("📋 [MapContainer] 初始化标绘模块...");

    /**
     * @description 地图加载完成后的回调
     * @details 初始化标绘模块，加载localStorage中的图形到地图
     */
    const handleMapLoad = async () => {
      try {
        // 动态导入DrawModule避免循环依赖
        const { DrawModule } = await import(
          "@/views/maplibre/plotPanel/DrawModule"
        );
        drawModule = DrawModule.getInstance();

        // 初始化DrawModule（此时地图已完全加载）
        const initialized = await drawModule.initialize();
        if (initialized) {
          console.log(
            "✅ [MapContainer] 标绘模块初始化成功，localStorage中的图形已加载到地图"
          );
        } else {
          console.warn("❌ [MapContainer] 标绘模块初始化失败");
        }
      } catch (error) {
        console.error("❌ [MapContainer] 初始化标绘模块失败:", error);
      }
    };

    if (_map.loaded()) {
      await handleMapLoad();
    } else {
      _map.once("load", handleMapLoad);
    }
  } catch (error) {
    console.error("❌ [MapContainer] 初始化标绘模块失败:", error);
  }
};

/**
 * @description 注册清理任务到引擎切换管理器
 */
const registerCleanupTasks = (): void => {
  engineSwitch.addCleanupTask(async () => {
    console.log("🧹 [MapContainer] 执行引擎切换清理任务...");
    await performComponentCleanup();
  });
};

/**
 * @description 组件销毁前的清理（Vue生命周期）
 */
onBeforeUnmount(async () => {
  console.log("🔄 [MapContainer] 组件即将销毁，开始清理...");
  // 只有不是通过引擎切换销毁的情况下才执行清理
  // 引擎切换时的清理由EngineSwitch统一管理
  if (!engineSwitch.isSwitching()) {
    await performComponentCleanup();
  } else {
    console.log("⚠️ [MapContainer] 检测到引擎切换中，跳过Vue生命周期清理");
  }
});

/**
 * @description 组件卸载时的清理（Vue生命周期）
 */
onUnmounted(async () => {
  console.log("🗑️ [MapContainer] 组件已卸载，执行最终清理...");
  isDestroyed = true;

  // 只有不是通过引擎切换销毁的情况下才执行最终清理
  if (!engineSwitch.isSwitching()) {
    await performFinalCleanup();
  } else {
    console.log("⚠️ [MapContainer] 检测到引擎切换中，跳过最终清理");
  }
});

/**
 * @description 执行组件清理
 */
const performComponentCleanup = async (): Promise<void> => {
  if (isDestroyed) {
    console.log("⚠️ [MapContainer] 组件已销毁，跳过重复清理");
    return;
  }

  try {
    console.log("🧹 [MapContainer] 开始执行组件清理...");

    // 清理标绘模块
    await cleanupDrawModule();

    // 清理地图控件
    cleanupMapControls();

    // 清理地图实例
    await cleanupMapLibre();

    // 清理AppMaplibre状态
    await cleanupAppMaplibre();

    // 清理DOM事件监听器
    cleanupEventListeners();

    // 清理定时器和异步任务
    cleanupTimersAndAsync();

    console.log("✅ [MapContainer] 组件清理完成");
  } catch (error) {
    console.error("❌ [MapContainer] 组件清理失败:", error);
  }
};

/**
 * @description 清理标绘模块
 */
const cleanupDrawModule = async (): Promise<void> => {
  try {
    if (drawModule && typeof drawModule.destroy === "function") {
      console.log("🗑️ [MapContainer] 清理标绘模块...");
      await drawModule.destroy();
      drawModule = null;
      console.log("✅ [MapContainer] 标绘模块清理完成");
    }
  } catch (error) {
    console.error("❌ [MapContainer] 清理标绘模块失败:", error);
    drawModule = null;
  }
};

/**
 * @description 清理地图控件
 */
const cleanupMapControls = (): void => {
  try {
    console.log("🧹 [MapContainer] 清理地图控件...");

    if (mapInstance) {
      // 移除导航控件
      if (navigationControl) {
        try {
          mapInstance.removeControl(navigationControl);
        } catch (e) {
          console.warn("移除导航控件时出错:", e);
        }
        navigationControl = null;
      }

      // 移除比例尺控件
      if (scaleControl) {
        try {
          mapInstance.removeControl(scaleControl);
        } catch (e) {
          console.warn("移除比例尺控件时出错:", e);
        }
        scaleControl = null;
      }
    }

    console.log("✅ [MapContainer] 地图控件清理完成");
  } catch (error) {
    console.error("❌ [MapContainer] 清理地图控件失败:", error);
  }
};

/**
 * @description 清理MapLibre地图实例
 */
const cleanupMapLibre = async (): Promise<void> => {
  try {
    if (mapInstance && typeof mapInstance.remove === "function") {
      console.log("🗑️ [MapContainer] 销毁MapLibre地图实例...");

      // 移除所有事件监听器
      try {
        // MapLibre会在remove()时自动清理事件监听器
        // 这里只记录日志
        console.log("事件监听器将在地图销毁时自动清理");
      } catch (e) {
        console.warn("清理事件监听器时出错:", e);
      }

      // 清理地图图层和数据源
      try {
        // 清理所有图层
        const layers = mapInstance.getStyle()?.layers || [];
        layers.forEach((layer) => {
          try {
            mapInstance!.removeLayer(layer.id);
          } catch (e) {
            // 图层可能已被移除
          }
        });

        // 清理所有数据源
        const sources = mapInstance.getStyle()?.sources || {};
        Object.keys(sources).forEach((sourceId) => {
          try {
            mapInstance!.removeSource(sourceId);
          } catch (e) {
            // 数据源可能已被移除
          }
        });
      } catch (error) {
        console.warn("清理图层和数据源时出错:", error);
      }
      console.log(mapInstance);
      // 销毁地图实例
      // mapInstance.remove();
      mapInstance = null;

      console.log("✅ [MapContainer] MapLibre地图实例已销毁");
    }
  } catch (error) {
    console.error("❌ [MapContainer] 清理MapLibre地图失败:", error);
    // 强制重置引用
    mapInstance = null;
  }
};

/**
 * @description 清理AppMaplibre状态
 */
const cleanupAppMaplibre = async (): Promise<void> => {
  try {
    console.log("🧹 [MapContainer] 清理AppMaplibre状态...");

    // 调用AppMaplibre的销毁方法
    AppMaplibre.destroy();

    console.log("✅ [MapContainer] AppMaplibre状态清理完成");
  } catch (error) {
    console.error("❌ [MapContainer] 清理AppMaplibre状态失败:", error);
  }
};

/**
 * @description 清理DOM事件监听器
 */
const cleanupEventListeners = (): void => {
  try {
    console.log("🧹 [MapContainer] 清理DOM事件监听器...");

    // 移除可能的DOM事件监听器
    if (map.value) {
      // 清理容器事件
      const container = map.value;
      const clonedContainer = container.cloneNode(false);
      container.parentNode?.replaceChild(clonedContainer, container);
    }

    console.log("✅ [MapContainer] DOM事件监听器清理完成");
  } catch (error) {
    console.error("❌ [MapContainer] 清理DOM事件监听器失败:", error);
  }
};

/**
 * @description 清理定时器和异步任务
 */
const cleanupTimersAndAsync = (): void => {
  try {
    console.log("🧹 [MapContainer] 清理定时器和异步任务...");

    // 这里可以清理组件内的定时器
    // 如果有的话，添加clearTimeout/clearInterval调用

    console.log("✅ [MapContainer] 定时器和异步任务清理完成");
  } catch (error) {
    console.error("❌ [MapContainer] 清理定时器和异步任务失败:", error);
  }
};

/**
 * @description 执行最终清理
 */
const performFinalCleanup = async (): Promise<void> => {
  try {
    console.log("🗑️ [MapContainer] 执行最终清理...");

    // 清理容器DOM
    if (map.value) {
      map.value.innerHTML = "";
    }

    // 强制垃圾回收（如果支持）
    if (typeof (window as any).gc === "function") {
      (window as any).gc();
    }

    console.log("✅ [MapContainer] 最终清理完成");
  } catch (error) {
    console.error("❌ [MapContainer] 最终清理失败:", error);
  }
};

/**
 * @description 紧急清理（初始化失败时调用）
 */
const performEmergencyCleanup = async (): Promise<void> => {
  try {
    console.log("🚨 [MapContainer] 执行紧急清理...");

    // 清理可能已创建的地图实例
    if (mapInstance) {
      try {
        mapInstance.remove();
      } catch (e) {
        console.warn("紧急清理地图实例时出错:", e);
      }
      mapInstance = null;
    }

    // 清理AppMaplibre
    try {
      AppMaplibre.destroy();
    } catch (e) {
      console.warn("紧急清理AppMaplibre时出错:", e);
    }

    // 清理DOM容器
    if (map.value) {
      map.value.innerHTML = "";
    }

    console.log("✅ [MapContainer] 紧急清理完成");
  } catch (error) {
    console.error("❌ [MapContainer] 紧急清理失败:", error);
  }
};

/**
 * @description 获取组件状态信息（用于调试）
 */
const getComponentStatus = () => {
  return {
    isDestroyed,
    initializationSuccessful,
    hasMapInstance: !!mapInstance,
    containerExists: !!map.value,
    hasNavigationControl: !!navigationControl,
    hasScaleControl: !!scaleControl,
    hasDrawModule: !!drawModule,
  };
};

// 开发环境下暴露调试信息
if (import.meta.env.DEV) {
  (window as any).mapContainerStatus = getComponentStatus;
}
</script>

<style scoped lang="scss">
/**
 * 地图容器样式
 * 设置为全屏显示，绝对定位，无边距和内边距
 */
#map {
  height: 100%;
  width: 100%;
  position: absolute;
  margin: 0;
  padding: 0;
}

/**
 * 地名搜索框包装器样式
 * 浮动在地图上方，居中显示
 */
.place-search-wrapper {
  position: absolute;
  top: 16px;
  right: 120px;
  z-index: 1;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  /* 响应式调整 */
  @media (max-width: 768px) {
    top: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    width: auto;
  }
}
</style>
