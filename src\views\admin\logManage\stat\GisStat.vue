<template>
  <div>
    <div>GIS服务调用统计</div>
    <div class="chart" ref="categoryRef"></div>
  </div>
</template>
<script lang="ts" setup>
import { initGisStatEchart } from "@/lib/echarts";
import { visitorStat } from "@/api/log";
import { system } from "@/utils/constant";
import { useConstValue } from "@/utils/utils";
const { initChart } = useEchart();
const categoryRef = ref();
const list = ref<any>([]);
const getChart = async () => {
  const { data, code } = await visitorStat();
  if (code === 200) {
    list.value = Object.keys(data).map((key) => ({
      name: useConstValue(system, key),
      value: data[key],
    }));
  }
  categoryRef.value && initChart(categoryRef.value, initGisStatEchart(list.value));
};
onMounted(() => {
  getChart();
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 220px;
  // height: calc(100vh - 740px);
}
</style>
