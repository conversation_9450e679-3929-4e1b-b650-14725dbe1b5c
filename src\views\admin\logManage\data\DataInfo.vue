<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    label-width="auto"
    class="admin-sub-form"
    :disabled="editable"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="操作人" prop="operName">
          <el-input
            class="custom-input"
            v-model.trim="formData.operName"
            placeholder=""
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="标题" prop="title">
          <el-input
            class="custom-input"
            v-model.trim="formData.title"
            placeholder=""
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="操作类型" prop="businessType">
          <el-input
            class="custom-input"
            v-model.trim="formData.businessType"
            placeholder=""
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="方法名" prop="method">
          <el-input
            class="custom-input"
            v-model.trim="formData.method"
            placeholder=""
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="请求方式" prop="requestMethod">
          <el-input
            class="custom-input"
            v-model.trim="formData.requestMethod"
            placeholder=""
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="操作ip" prop="operIp">
          <el-input
            class="custom-input"
            v-model.trim="formData.operIp"
            placeholder=""
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="请求参数" prop="operParam">
          <el-input
            class="custom-input"
            v-model.trim="formData.operParam"
            placeholder=""
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="返回参数" prop="jsonResult">
          <el-input
            class="custom-input"
            v-model.trim="formData.jsonResult"
            placeholder=""
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="操作状态" prop="status">
          <el-input
            class="custom-input"
            v-model.trim="formData.status"
            placeholder=""
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="操作时间" prop="operTime">
          <el-input
            class="custom-input"
            v-model.trim="formData.operTime"
            placeholder=""
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="请求url" prop="operUrl">
          <el-input
            class="custom-input"
            v-model.trim="formData.operUrl"
            placeholder=""
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="错误信息" prop="errorMsg">
          <el-input
            class="custom-input"
            v-model.trim="formData.errorMsg"
            placeholder=""
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="消耗时间" prop="costTime">
          <el-input placeholder="" v-model="formData.costTime"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
const props = defineProps({
  formData: Object,
  editable: Boolean,
});
let formData: any = ref(props.formData);
let editable = ref(props.editable);
watch(
  () => props.formData,
  (val) => {
    resetForm();
    formData.value = val;
  }
);
watch(
  () => props.editable,
  (val) => {
    editable.value = val;
  }
);

const rules = {};
let form: any = ref(null);
const submitForm = async (): Promise<any> => {
  let result: boolean = await form.value.validate();
  return result ? formData.value : null;
};
const resetForm = () => {
  form.value.resetFields();
};
onMounted(async () => {
  resetForm();
});
defineExpose({
  submitForm,
  resetForm,
});
</script>
<style lang="scss" scoped></style>
