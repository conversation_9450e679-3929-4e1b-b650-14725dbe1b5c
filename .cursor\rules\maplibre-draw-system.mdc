---
description: 
globs: 
alwaysApply: false
---
# MapLibre 绘制系统架构指南

## 核心组件

### 绘制工具管理器
主要的绘制工具管理类位于 [DrawManager.ts](mdc:src/lib/maplibre/draw/DrawManager.ts)，它是基于Terra Draw的MapLibre绘制工具核心管理类，提供：
- 多种绘制模式（点、线、多边形、矩形、圆形、自由绘制）
- 完整的编辑功能（移动、缩放、旋转、添加/删除点）
- 事件系统和状态管理
- 样式配置管理

### AppMaplibre集成
地图核心管理类 [AppMaplibre.ts](mdc:src/lib/maplibre/AppMaplibre.ts) 提供绘制工具的单例访问：
- `getDrawTool()` - 获取绘制工具实例
- `recreateDrawTool()` - 重新创建绘制工具实例（用于状态恢复）

### 空间查询组件
可复用的空间查询按钮组件 [SpatialQueryButtons.vue](mdc:src/components/SpatialQueryButtons.vue) 提供：
- 四种查询类型：全区范围、当前范围、多边形查询、矩形查询
- 双引擎支持：Cesium和MapLibre
- 完整的事件系统和状态管理
- 自动清理和状态恢复机制

## 重要设计模式

### 状态管理
- Terra Draw实例状态检查：使用 `isEnabled()` 方法
- 自动状态恢复：组件初始化时检查并恢复Terra Draw状态
- 分级错误恢复：重启实例 → 重新创建实例 → 完整重新初始化

### 事件处理
绘制事件由 [DrawEventHandler.ts](mdc:src/lib/maplibre/draw/DrawEventHandler.ts) 统一管理，将Terra Draw的原生事件转换为标准化的绘制事件。

### 组件生命周期
组件销毁时执行完整清理：
1. 检查绘制状态，取消进行中的操作
2. 移除事件监听器
3. 清除绘制要素
4. 停止Terra Draw实例
5. 重置组件状态

## 常见问题解决

### "Terra Draw is not enabled" 错误
- 原因：多次开关组件导致Terra Draw实例状态不一致
- 解决：组件初始化时检查状态，必要时重新启动或重新创建实例

### 绘制中断处理
- 组件销毁时正确停止正在进行的绘制操作
- 恢复鼠标样式和地图交互状态
- 触发相应的错误事件通知用户

