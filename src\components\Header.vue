<template>
  <div class="custom-header">
    <div class="header-left items-center">
      <div class="flex items-center">
        <img class="logo" src="@/assets/images/header/logo.svg" alt="" />
        <div class="sys-title">铜河集团智慧水务</div>
      </div>
      <span class="mx-8px font-700">/</span>
      <el-text class="sys-des mt-0.5">供水管网地理信息系统</el-text>
    </div>
    <div class="header-rt flex items-center">
      <div
        class="mr-10 cursor-pointer flex items-center"
        v-if="allWeather.forecast"
        @mouseover="isShow = true"
        @mouseout="isShow = false"
      >
        <span class="color-#fff font-500 font-size-4">乐山市</span>
        <img
          :src="
            allWeather.forecast[0].type.includes('雨')
              ? imgMapper['雨']
              : imgMapper[allWeather.forecast[0].type]
          "
          class="w-8 h-8 mx-2.5"
          alt=""
        />
        <span class="text-white font-size-3.5">{{
          allWeather.forecast[0].type
        }}</span>
        <span class="text-white font-size-3.5 ml-1">{{
          Math.floor(allWeather.wendu) + "°"
        }}</span>
      </div>
      <el-dropdown>
        <div
          class="h-full w-full cursor-pointer el-dropdown-link"
          flex="~ items-center gap-x-2"
        >
          <img class="size-35px" :src="localCache.getCache('Photo') ?? user" />
          <span class="text-#ffffff" font="size-3.5 400">{{
            localCache.getCache("Name")
          }}</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <!-- <el-dropdown-item @click="toGISPlatform"> GIS服务器平台 </el-dropdown-item>
            <el-dropdown-item v-if="mapEngine === 'cesium'" @click="to2DModule"> 二 维 </el-dropdown-item>
            <el-dropdown-item v-if="mapEngine === 'maplibre'" @click="to3DModule"> 三 维 </el-dropdown-item>
            <el-dropdown-item @click="toAdminModule"> 后台管理 </el-dropdown-item> -->

            <el-dropdown-item @click="toHomePage"> 首 页 </el-dropdown-item>
            <el-dropdown-item @click="loginOut"> 登 出 </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div v-if="isShow" class="absolute top-30 right-30 z-2">
      <weather v-model:params="allWeather" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDialogStore } from "@/stores/Dialogs";
import { getImages } from "@/utils/getImages";
import weather from "@/views/maplibre/weather/index.vue";
import axios from "axios";
import router from "@/router";
import localCache from "@/utils/auth";
// import { menuList } from "@/views/gateway/data";

const route = useRoute();

// const systemChildName = ref('');

// watch(() => route.path, (newPath) => {
//   if (newPath) {
//     const firstRoute = newPath.split('/')[1];

//     const matchedItem = menuList.find(item => {
//       const itemPath = item.path.replace(/^\//, '');
//       return itemPath === firstRoute;
//     });

//     systemChildName.value = matchedItem?.name || '';
//   }
// }, { immediate: true });

const user = getImages("header/user.png");
const isShow = ref(false);

watchEffect(() => {
  console.log("当前路由变化:", route.path);
  useDialogStore().changeDetailDialogEnable(
    route.name === "MaplibreHome" || route.name === "CesiumHome"
  );
});





const toHomePage = () => {
  router.push("/gateway");
  // 清除所有弹框
  useDialogStore().clear();
};

/**
 * @description 登出清除localstorage,跳转到登录页
 */
const loginOut = () => {
  localStorage.clear();
  window.location.href = "https://engine.lshywater.cn/#/login";
};

const allWeather = ref<any>([]);
const imgMapper: any = {
  阴: getImages("weather/yin.png"),
  雨: getImages("weather/rain.png"),
  多云: getImages("weather/csun.png"),
  晴: getImages("weather/sunny.png"),
};
const getWeather = () => {
  axios.get("/weather/api/weather/city/101271401").then((res: any) => {
    // axios
    //   .get(
    //     "/weather/api?appid=34241366&cityid=101271401&appsecret=U4PYA88I&version=v63"
    //   )
    // .then((res: any) => {
    allWeather.value = res.data.data;
    console.log(res.data.data);
  });
};
getWeather();
</script>

<style scoped lang="scss">
.custom-header {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 13px;
  background: url("@/assets/images/header/header-bg.png") left top / 100% 100%
    no-repeat;
}
.header-left {
  display: flex;
  color: #fff;

  .logo {
    width: 32px;
    height: 32px;
  }
  .sys-title {
    font: 24px YousheBiaoTiHei;
    box-shadow: 0 2px 4px 0 rgba($color: #4f87f8, $alpha: 0.4);
    margin-left: 4px;
  }

  .sys-des {
    color: #fff;
    // margin-left: 5px;
    font: 400 22px YousheBiaoTiHei;
  }
}
</style>
