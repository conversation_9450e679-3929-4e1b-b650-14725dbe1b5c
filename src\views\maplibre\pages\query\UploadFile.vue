<!--
 * @Author: xiao
 * @Date: 2023-12-21 10:01:03
 * @LastEditors: xiao
 * @LastEditTime: 2024-09-04 13:43:36
 * @Description: 
-->
<template>
  <el-upload
    ref="upload"
    :action="reqUrl"
    :limit="1"
    v-model:file-list="fileList"
    :on-exceed="handleExceed"
    :before-upload="beforeUpload"
    :on-error="handleError"
    :on-success="handleSuccess"
    :on-remove="handleRemove"
    :headers="header"
    :accept="accept"
    v-loading.fullscreen.lock="loading"
    element-loading-text="正在上传"
  >
    <template #trigger >
      <el-button type="primary">{{ btnLabel }}</el-button>
    </template>
  </el-upload>
</template>

<script setup lang="ts">
import localCache from '@/utils/auth'
// import { blobType } from '@/utils/fileType'
import type { UploadInstance, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus'
import { genFileId } from 'element-plus'

const props = withDefaults(
  defineProps<{
    type: string[]
    fileUrl: string
    btnLabel: string
    disabled: boolean
    showBtn?: boolean
  }>(),
  {
    btnLabel: '上传',
    type: () => ['']
  }
)


const accept = computed(() => props.type.map((item) => `.${item}`).join(','))

const emits = defineEmits(['update:fileUrl', 'upload-success'])

const loading = ref(false)
const fileList = ref<UploadUserFile[]>([])

const upload = ref<UploadInstance>()

const reqUrl = import.meta.env.VITE_XHR_URL + '/v1/resource'

const header = {
  Latias: localCache.getCache('Latias') || ''
}
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
  upload.value!.submit()
}

const typeList = ['image/png', 'image/jpeg', 'image/webp']

const beforeUpload = (rawFile: UploadRawFile) => {
  if (!typeList.includes(rawFile.type)) {
    ElMessage.error('请上传png,jpg,jpeg,webp格式')
    return false
  }
  loading.value = true
  return true
}

const handleRemove: UploadProps['onRemove'] = () => {
  emits('update:fileUrl', '')
}

const handleError = (error: Error) => {
  console.error('上传失败:', error)
  ElMessage.error('上传失败，请重试')
  loading.value = false
}

const handleSuccess = (response: any) => {
  switch (response.code) {
    case 200:
      ElMessage.success('上传成功')
      emits('update:fileUrl', response.data.url)
      emits('upload-success', response.data)
      break
    case 401:
      ElMessage.error(response.msg)
      break
    default:
      ElMessage.error(response.msg)
      break
  }
  loading.value = false
}

const setFile = (item: UploadUserFile) => {
  fileList.value.push(item)
}

defineExpose({
  setFile
})
</script>

<style scoped lang="scss">
:deep(.el-upload-list__item) .el-upload-list__item-info {
  width: 170px;
}
.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  text-align: center;
}
.person-avatar {
  width: 148px;
  height: 148px;
}

.avatar-delete-icon {
  position: absolute;
  right: 0;
  top: 0;
  &:hover {
    color: var(--el-color-primary);
  }
}
</style>
