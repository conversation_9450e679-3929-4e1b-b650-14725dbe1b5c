<!--
 * @Description: MapLibre地图坐标搜索框组件
 * @Date: 2024-01-16 10:00:00
 * @Author: 项目开发团队
 * @LastEditTime: 2024-01-16 10:00:00
 * @Version: 1.0.0
-->
<template>
  <custom-card
    :width="'460px'"
    @closeHandler="close"
    v-show="mainItem.show"
    :main-item="mainItem"
    :title="mainItem.title ?? (mainItem.title = '坐标定位')"
  >
    <div class="search-box">
      <el-input
        v-model="searchText"
        placeholder="输入坐标 (经度,纬度) 如: 116.404,39.915"
        clearable
        @keyup.enter="handleSearch"
        @clear="handleClear"
        class="search-input"
        :class="{ error: hasError }"
      >
        <template #append>
          <el-button
            class="bg-#1966FF!"
            @click="handleSearch"
            :loading="isSearching"
            >确定</el-button
          >
        </template>
      </el-input>

      <!-- 错误提示 -->
      <div v-if="errorMessage" class="error-tooltip">
        <i class="el-icon-warning-outline"></i>
        {{ errorMessage }}
      </div>

      <!-- 成功提示 -->
      <div v-if="successMessage" class="success-tooltip">
        <i class="el-icon-circle-check"></i>
        {{ successMessage }}
      </div>

      <!-- 示例坐标快捷选择 -->
      <div v-if="showExamples && !searchText" class="examples-container">
        <div class="examples-title">示例坐标：</div>
        <div class="examples-list">
          <span
            v-for="example in exampleCoordinates"
            :key="example.name"
            class="example-item"
            @click="selectExample(example)"
            :title="example.description"
          >
            {{ example.name }}
          </span>
        </div>
      </div>
    </div>
  </custom-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { ElMessage } from "element-plus";
import type { Map } from "maplibre-gl";
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});
/**
 * @description 组件状态管理
 */
const searchText = ref("");
const errorMessage = ref("");
const successMessage = ref("");
const hasError = ref(false);
const isSearching = ref(false);
const mapInstance = ref<Map | null>(null);
const showExamples = ref(false);

// 临时图层管理
const TEMP_LAYER_ID = 'coord-location-marker';
const TEMP_SOURCE_ID = 'coord-location-source';

/**
 * @description 示例坐标数据
 */
const exampleCoordinates = ref([
  { name: "沙湾古镇", coords: "103.547,29.409", description: "沙湾古镇" },
  { name: "沙湾广场", coords: "103.551,29.413", description: "沙湾广场" },
  { name: "沙湾农贸市场", coords: "103.549,29.411", description: "沙湾农贸市场" },
  { name: "沙湾工业园区", coords: "103.570,29.400", description: "沙湾工业园区" },
  { name: "大渡河", coords: "103.545,29.405", description: "大渡河" },
  { name: "沙湾公园", coords: "103.552,29.415", description: "沙湾公园" },
]);

/**
 * @description 经纬度坐标验证正则表达式
 * @details 支持多种格式：
 * - 十进制度数：116.404,39.915
 * - 带空格：116.404 , 39.915
 * - 负数坐标：-116.404,-39.915
 * - 不同精度：116.4,39.9
 * - 科学计数法：1.16404e2,3.9915e1
 */
const COORDINATE_REGEX =
  /^(-?\d+(?:\.\d+)?(?:[eE][+-]?\d+)?)\s*,\s*(-?\d+(?:\.\d+)?(?:[eE][+-]?\d+)?)$/;

/**
 * @description 验证经纬度坐标有效性
 * @param {string} coordStr - 坐标字符串
 * @returns {{ isValid: boolean, longitude?: number, latitude?: number, error?: string }}
 */
const validateCoordinates = (coordStr: string) => {
  // 检查基本格式
  const match = coordStr.trim().match(COORDINATE_REGEX);
  if (!match) {
    return {
      isValid: false,
      error: '坐标格式不正确，请使用 "经度,纬度" 格式，如：116.404,39.915',
    };
  }

  const longitude = parseFloat(match[1]);
  const latitude = parseFloat(match[2]);

  // 验证经度范围 (-180 到 180)
  if (longitude < -180 || longitude > 180) {
    return {
      isValid: false,
      error: `经度值 ${longitude} 超出有效范围 (-180 到 180)`,
    };
  }

  // 验证纬度范围 (-90 到 90)
  if (latitude < -90 || latitude > 90) {
    return {
      isValid: false,
      error: `纬度值 ${latitude} 超出有效范围 (-90 到 90)`,
    };
  }

  return {
    isValid: true,
    longitude,
    latitude,
  };
};

/**
 * @description 处理搜索操作
 */
const handleSearch = async () => {
  if (!searchText.value.trim()) {
    showError("请输入坐标信息");
    return;
  }

  // 验证坐标
  const validation = validateCoordinates(searchText.value);
  if (!validation.isValid) {
    showError(validation.error!);
    return;
  }

  // 执行地图定位
  try {
    isSearching.value = true;
    await performMapLocation(validation.longitude!, validation.latitude!);
    showSuccess(
      `已定位到坐标 (${validation.longitude}, ${validation.latitude})`
    );
  } catch (error) {
    console.error("地图定位失败:", error);
    showError("地图定位失败，请检查坐标是否正确");
  } finally {
    isSearching.value = false;
  }
};

/**
 * @description 添加临时标记点
 * @param {number} longitude - 经度
 * @param {number} latitude - 纬度
 */
const addLocationMarker = (longitude: number, latitude: number) => {
  if (!mapInstance.value) return;

  // 清除之前的标记点
  clearLocationMarker();

  // 创建点数据
  const pointData = {
    type: 'FeatureCollection',
    features: [
      {
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        properties: {
          title: '定位点',
          description: `坐标: ${longitude}, ${latitude}`
        }
      }
    ]
  };

  try {
    // 添加数据源
    mapInstance.value.addSource(TEMP_SOURCE_ID, {
      type: 'geojson',
      data: pointData as any
    });

    // 添加圆形标记图层
    mapInstance.value.addLayer({
      id: TEMP_LAYER_ID,
      type: 'circle',
      source: TEMP_SOURCE_ID,
      paint: {
        'circle-radius': 8,
        'circle-color': '#ff4444',
        'circle-stroke-width': 2,
        'circle-stroke-color': '#ffffff',
        'circle-opacity': 0.8
      }
    });

    console.log('✅ 临时定位标记已添加');
  } catch (error) {
    console.error('添加定位标记失败:', error);
  }
};

/**
 * @description 清除临时标记点
 */
const clearLocationMarker = () => {
  if (!mapInstance.value) return;

  try {
    // 检查图层是否存在并移除
    if (mapInstance.value.getLayer(TEMP_LAYER_ID)) {
      mapInstance.value.removeLayer(TEMP_LAYER_ID);
    }

    // 检查数据源是否存在并移除
    if (mapInstance.value.getSource(TEMP_SOURCE_ID)) {
      mapInstance.value.removeSource(TEMP_SOURCE_ID);
    }

    console.log('✅ 临时定位标记已清除');
  } catch (error) {
    console.warn('清除定位标记失败:', error);
  }
};

/**
 * @description 执行地图定位跳转
 * @param {number} longitude - 经度
 * @param {number} latitude - 纬度
 */
const performMapLocation = async (longitude: number, latitude: number) => {
  if (!mapInstance.value) {
    throw new Error("地图实例未初始化");
  }

  // 使用flyTo方法平滑跳转到目标位置
  mapInstance.value.flyTo({
    center: [longitude, latitude],
    zoom: 15,
    duration: 2000,
    essential: true,
  });

  // 等待动画完成
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // 添加临时标记点
  addLocationMarker(longitude, latitude);
};

/**
 * @description 显示错误信息
 * @param {string} message - 错误信息
 */
const showError = (message: string) => {
  errorMessage.value = message;
  hasError.value = true;
  successMessage.value = "";

  // 3秒后自动清除错误信息
  setTimeout(() => {
    clearMessages();
  }, 3000);
};

/**
 * @description 显示成功信息
 * @param {string} message - 成功信息
 */
const showSuccess = (message: string) => {
  successMessage.value = message;
  hasError.value = false;
  errorMessage.value = "";

  // 2秒后自动清除成功信息
  setTimeout(() => {
    clearMessages();
  }, 2000);
};

/**
 * @description 清除所有提示信息
 */
const clearMessages = () => {
  errorMessage.value = "";
  successMessage.value = "";
  hasError.value = false;
};

/**
 * @description 处理清除操作
 */
const handleClear = () => {
  searchText.value = "";
  clearMessages();
};

/**
 * @description 选择示例坐标
 * @param {object} example - 示例坐标对象
 */
const selectExample = (example: {
  name: string;
  coords: string;
  description: string;
}) => {
  searchText.value = example.coords;
  clearMessages();
  // 自动执行搜索
  handleSearch();
};
const close = () => {
  // 关闭对话框
  useDialogStore().closeDialog("CoordLoc");
};
/**
 * @description 组件挂载后初始化
 */
onMounted(() => {
  // 延迟获取地图实例，确保地图已完成初始化
  nextTick(() => {
    setTimeout(() => {
      try {
        mapInstance.value = AppMaplibre.getMap();
        console.log("MapLibre搜索框已连接到地图实例");
      } catch (error) {
        console.error("无法获取MapLibre地图实例:", error);
        ElMessage.error("地图未初始化，搜索功能不可用");
      }
    }, 500);
  });
});

/**
 * @description 组件卸载前清理
 */
onUnmounted(() => {
  console.log("坐标定位组件卸载，清理临时图层");

  // 清除临时标记点
  clearLocationMarker();
});
</script>

<style scoped lang="scss">
.search-input {
  width: 100%;
  height: 36px;
  &.error {
    :deep(.el-input__wrapper) {
      border-color: #f56c6c;
      box-shadow: 0 0 0 1px #f56c6c inset;
    }
  }

  :deep(.el-input__wrapper) {
    // border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1966ff;
    }

    &.is-focus {
      border-color: #1966ff;
      box-shadow: 0 0 0 1px #1966ff inset;
    }
  }

  :deep(.el-input-group__append) {
    border-radius: 0 6px 6px 0;
    background: #1966ff;
    border-color: #1966ff;

    .el-button {
      background: transparent;
      border: none;
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      &.is-loading {
        color: white;
      }
    }
  }
}

.error-tooltip {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding: 8px 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 1.4;

  i {
    margin-right: 6px;
    font-size: 14px;
  }
}

.success-tooltip {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  color: #409eff;
  font-size: 12px;
  line-height: 1.4;

  i {
    margin-right: 6px;
    font-size: 14px;
  }
}

.examples-container {
  // margin-top: 8px;
  // padding-top: 8px;
  // border-top: 1px solid #f0f0f0;
}

.examples-title {
  font-size: 14px;
  color: #707070;
  margin: 30px 0 10px 0;
  font-weight: 500;
}

.examples-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.example-item {
  // padding: 3px 8px;
  width: 56px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  box-sizing: border-box;
  border: 1px solid rgb(220, 220, 220);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  color: #bfbfbf;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #ecf5ff;
    border-color: #b3d8ff;
    color: #409eff;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .maplibre-search-container {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
  }
}

/* 动画效果 */
.error-tooltip,
.success-tooltip {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
