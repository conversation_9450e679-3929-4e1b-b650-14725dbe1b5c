export const initValveEchart = (xData: any, data: any) => {
  const option: any = {
    grid: {
      top: '10%',
      left: "5%",
      bottom: '5%',
      containLabel: true,
    },

    yAxis: {
      // name: "单位：个",
      // nameTextStyle: {
      //   color: "#5C5F66",
      // },
      type: 'category',
      axisTick: { show: false },
      axisLine: {
        lineStyle: {
          color: "#F6F6F6",
        },
      },
      axisLabel: {
        textStyle: {
          color: "#2C3037",
        },
      },
      data: xData.splice(0, 10)
    },
    xAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: "#F6F6F6",
        },
      },
    },

    series: [
      {
        barWidth: 12,
        data: data.splice(0, 10),
        itemStyle: {
          color: '#757FFF'
        },
        type: 'bar',
        label: {
          normal: {
            show: true,
            position: "right",
            formatter: "{c}",
            color: '#757FFF'
          },
        },

      }
    ]
  };
  return option
}