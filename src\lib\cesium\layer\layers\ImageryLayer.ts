import { BaseLayer } from "./BaseLayer";
export class ImageryLayer extends BaseLayer {
  constructor(options:any){
    super(options);
  }
  addToInternal(viewer:BC.Viewer){
    let delegate: any;
    if(this._title === 'Bing影像') {
      // const bing = new Cesium.BingMapsImageryProvider({
      //   url: 'https://dev.virtualearth.net',
      //   key: 'Aq_lQ4tLM94_Bh_ZChuM9X7ydJKTep4X1jHedhc5GC_tPTac8Z3jmK9souVGZpu7',
      //   mapStyle: Cesium.BingMapsStyle.AERIAL //可选参数，指定地图样式
      // })
      // viewer.delegate.imageryLayers.addImageryProvider(bing)
    } else {
      const imageryLayer = BC.ImageryLayerFactory.createImageryLayer(this._options.type, this._options);
      delegate = viewer.imageryLayers.addImageryProvider(imageryLayer);
    }
    this._delegate = delegate;
    return delegate;
  }

  setTransparency(transpant: boolean, alphaValue: number) {
    
  }

  removeInternal() {
    this._viewer.imageryLayers.remove(this._delegate);
  }
}