/**
 * @fileoverview 操作工具配置管理
 * @description 管理Cesium和MapLibre引擎的操作工具配置，提供工具列表和状态管理
 * <AUTHOR>
 * @version 2.0.0
 * @date 2024-01-16
 */

import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useDialogStore } from '@/stores/Dialogs';

/**
 * @description 工具项接口定义
 */
interface ToolItem {
  /** 工具名称 */
  name: string;
  /** 工具路径标识 */
  path: string;
  /** 工具所属引擎类型 */
  type: 'cesium' | 'maplibre';
  /** 工具图标文件名 */
  img: string;
  /** 高亮状态 0-未激活 1-激活 */
  highLight: 0 | 1;
  isShow: boolean;
}

/**
 * @description 初始化Cesium引擎工具列表
 * @details 三维地图模式下可用的操作工具，包含二三维切换功能
 * @returns {ToolItem[]} Cesium工具配置数组
 */
const initCesiumToolList = (): ToolItem[] => {
  return [
    {
      name: "复位视角",
      path: "ResetView",
      type: "cesium",
      img: "reset",
      highLight: 0,
      isShow: false
    },
    {
      name: "地图全屏",
      path: "FullScreen",
      type: "cesium",
      img: "full",
      highLight: 0,
      isShow: false
    },
    {
      name: "图层控制",
      path: "LayerTree",
      type: "cesium",
      img: "layer",
      highLight: 0,
      isShow: false
    },
    {
      name: "坐标拾取",
      path: "CoordPicker",
      type: "cesium",
      img: "pickup",
      highLight: 0,
      isShow: false
    },
    {
      name: "空间量算",
      path: "BaseMeasure",
      type: "cesium",
      img: "measure",
      highLight: 0,
      isShow: false
    },
    {
      name: "视角书签",
      path: "BookMark",
      type: "cesium",
      img: "bookmark",
      highLight: 0,
      isShow: false
    },
    {
      name: "三维漫游",
      path: "RoamingPanel",
      type: "cesium",
      img: "roaming",
      highLight: 0,
      isShow: false
    },
    {
      name: "卷帘对比",
      path: "RollingCompare",
      type: "cesium",
      img: "curtain",
      highLight: 0,
      isShow: false
    },

    // {
    //   name: "二三维切换",
    //   path: "Toggle3D",
    //   type: "cesium",
    //   img: "2D",
    //   highLight: 0,
    // },
    {
      name: "地图标注",
      path: "MapAnnotation",
      type: "cesium",
      img: "mark",
      highLight: 0,
      isShow: false
    },
    {
      name: "地表透明",
      path: "GroundTransparent",
      type: "cesium",
      img: "surface",
      highLight: 0,
      isShow: false
    },
  ];
};

/**
 * @description 初始化MapLibre引擎工具列表
 * @details 二维地图模式下可用的操作工具，不包含二三维切换功能
 * @returns {ToolItem[]} MapLibre工具配置数组
 */
const initMapLibreToolList = (): ToolItem[] => {
  return [
    {
      name: "复位视角",
      path: "ResetView",
      type: "maplibre",
      img: "reset",
      highLight: 0,
      isShow: false
    },
    {
      name: "复位范围",
      path: "ResetRegion",
      type: "maplibre",
      img: "region",
      highLight: 0,
      isShow: false
    },
    {
      name: "地图全屏",
      path: "FullScreen",
      type: "maplibre",
      img: "full",
      highLight: 0,
      isShow: false
    },
    {
      name: "设备搜索",
      path: "DeviceResearch",
      type: "maplibre",
      img: "search",
      highLight: 0,
      isShow: false
    },
    {
      name: "坐标定位",
      path: "CoordLoc",
      type: "maplibre",
      img: "loc",
      highLight: 0,
      isShow: false
    },
    {
      name: "坐标拾取",
      path: "CoordPicker",
      type: "maplibre",
      img: "pickup",
      highLight: 0,
      isShow: false
    },
    {
      name: "视角书签",
      path: "BookMark",
      type: "maplibre",
      img: "bookmark",
      highLight: 0,
      isShow: false
    },
    {
      name: "地图标记",
      path: "PlotPanel",
      type: "maplibre",
      img: "mark",
      highLight: 0,
      isShow: false
    },
    {
      name: "空间量算",
      path: "BaseMeasure",
      type: "maplibre",
      img: "measure",
      highLight: 0,
      isShow: false
    },
    {
      name: "截屏打印",
      path: "ScreenCapture",
      type: "maplibre",
      img: "capture",
      highLight: 0,
      isShow: false
    },
    {
      name: "制图打印",
      path: "DrawPrint",
      type: "maplibre",
      img: "print",
      highLight: 0,
      isShow: false
    },
  ];
};

/**
 * @description 操作工具状态管理存储
 * @details 管理工具列表、高亮状态切换和引擎类型切换
 */
export const useOperateToolStore = defineStore("OperateToolStore", () => {
  /** @description 当前可视化工具列表 */
  const visualToolList = ref<ToolItem[]>(initCesiumToolList());

  /**
   * @description 切换地图引擎系统
   * @param {string} type - 引擎类型 'cesium' | 'maplibre'
   * @details 根据引擎类型加载对应的工具配置列表
   */
  const changeSystem = (type: string): void => {
    visualToolList.value = type === "maplibre"
      ? initMapLibreToolList()
      : initCesiumToolList();

    console.log(`工具系统已切换到: ${type === "maplibre" ? 'MapLibre二维' : 'Cesium三维'}模式`);
  };

  /**
   * @description 切换工具高亮状态
   * @param {ToolItem} item - 要切换状态的工具项
   * @details 激活/取消激活指定工具，同时处理对话框状态
   */
  const toggleHighLight = (item: ToolItem): void => {
    console.log("object", item);
    visualToolList.value.forEach((tool) => {
      if (tool.path === item.path) {
        tool.highLight = tool.highLight === 0 ? 1 : 0;

        // 如果工具被取消激活，同时关闭对应的对话框
        if (tool.highLight === 0) {
          useDialogStore().closeDialog(item.path);
        }

        console.log(`工具"${tool.name}"状态: ${tool.highLight === 1 ? '激活' : '取消激活'}`);
      }
    });
  };

  /**
   * @description 取消激活指定工具
   * @param {string} path - 工具路径标识
   * @details 将指定工具设置为未激活状态
   */
  const disactive = (path: string): void => {
    visualToolList.value.forEach((tool) => {
      if (tool.path === path) {
        tool.highLight = 0;
        console.log(`工具"${tool.name}"已取消激活`);
      }
    });
  };

  /**
   * @description 获取当前激活的工具列表
   * @returns {ToolItem[]} 激活状态的工具数组
   */
  const getActiveTools = (): ToolItem[] => {
    return visualToolList.value.filter(tool => tool.highLight === 1);
  };

  /**
   * @description 取消激活所有工具
   * @details 将所有工具设置为未激活状态
   */
  const disactiveAll = (): void => {
    visualToolList.value.forEach((tool) => {
      tool.highLight = 0;
    });
    console.log('所有工具已取消激活');
  };

  return {
    /** 当前可视化工具列表 */
    visualToolList,
    /** 切换地图引擎系统 */
    changeSystem,
    /** 切换工具高亮状态 */
    toggleHighLight,
    /** 取消激活指定工具 */
    disactive,
    /** 获取激活的工具列表 */
    getActiveTools,
    /** 取消激活所有工具 */
    disactiveAll
  };
});
