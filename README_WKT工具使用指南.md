# GeoJSON到WKT转换工具使用指南

## 概述

这是一个通用的工具函数库，用于将GeoJSON格式的几何数据转换为WKT（Well-Known Text）格式。该工具支持所有标准的GeoJSON几何类型，提供了丰富的配置选项和完善的错误处理机制。

## 安装和导入

```typescript
// 导入整个工具类
import { WktUtils } from '@/utils/geometry/WktUtils';

// 导入便捷函数
import { geoJsonToWkt, featureToWkt } from '@/utils/geometry/WktUtils';

// 导入类型定义
import type { WktOptions, WktResult } from '@/utils/geometry/WktUtils';

// 统一导入
import { WktUtils, geoJsonToWkt, featureToWkt } from '@/utils/geometry';
```

## 支持的几何类型

| GeoJSON类型 | WKT类型 | 说明 |
|------------|---------|------|
| Point | POINT | 点几何体 |
| LineString | LINESTRING | 线几何体 |
| Polygon | POLYGON | 面几何体 |
| MultiPoint | MULTIPOINT | 多点几何体 |
| MultiLineString | MULTILINESTRING | 多线几何体 |
| MultiPolygon | MULTIPOLYGON | 多面几何体 |
| GeometryCollection | GEOMETRYCOLLECTION | 几何集合 |

## 基础使用

### 1. 基本转换

```typescript
import { WktUtils } from '@/utils/geometry/WktUtils';

// Point转换
const point = {
  type: 'Point',
  coordinates: [120.123456, 30.654321]
};

const result = WktUtils.geometryToWkt(point);
if (result.success) {
  console.log(result.wkt); // "POINT (120.123456 30.654321)"
}
```

### 2. 使用便捷函数

```typescript
import { geoJsonToWkt } from '@/utils/geometry/WktUtils';

const lineString = {
  type: 'LineString',
  coordinates: [[120.1, 30.1], [120.2, 30.2], [120.3, 30.3]]
};

const wkt = geoJsonToWkt(lineString, 4); // 精度为4位小数
console.log(wkt); // "LINESTRING (120.1000 30.1000, 120.2000 30.2000, 120.3000 30.3000)"
```

## 高级配置

### 配置选项

```typescript
interface WktOptions {
  precision?: number;          // 坐标精度（小数位数），默认为6
  includeZ?: boolean;          // 是否包含Z坐标，默认为false
  includeM?: boolean;          // 是否包含M坐标，默认为false
  coordinateSeparator?: string; // 坐标分隔符，默认为空格
  formatted?: boolean;         // 是否格式化输出，默认为false
}
```

### 使用示例

```typescript
const point3D = {
  type: 'Point',
  coordinates: [120.123456789, 30.987654321, 100.5]
};

// 高精度转换
const highPrecision = WktUtils.geometryToWkt(point3D, { 
  precision: 8 
});
console.log(highPrecision.wkt); // "POINT (120.12345679 30.98765432)"

// 包含Z坐标
const withZ = WktUtils.geometryToWkt(point3D, { 
  precision: 6, 
  includeZ: true 
});
console.log(withZ.wkt); // "POINT (120.123457 30.987654 100.500000)"

// 自定义分隔符
const customSeparator = WktUtils.geometryToWkt(point3D, { 
  coordinateSeparator: ',' 
});
console.log(customSeparator.wkt); // "POINT (120.123457,30.987654)"
```

## Feature转换

```typescript
import { featureToWkt } from '@/utils/geometry/WktUtils';

const feature = {
  type: 'Feature',
  id: 'example-1',
  geometry: {
    type: 'Polygon',
    coordinates: [[
      [120.0, 30.0],
      [121.0, 30.0], 
      [121.0, 31.0],
      [120.0, 31.0],
      [120.0, 30.0]
    ]]
  },
  properties: {
    name: '示例区域'
  }
};

const wkt = featureToWkt(feature, 4);
console.log(wkt); // "POLYGON ((120.0000 30.0000, 121.0000 30.0000, 121.0000 31.0000, 120.0000 31.0000, 120.0000 30.0000))"
```

## 批量转换

```typescript
const featureCollection = {
  type: 'FeatureCollection',
  features: [
    { type: 'Feature', geometry: { type: 'Point', coordinates: [120.1, 30.1] }, properties: {} },
    { type: 'Feature', geometry: { type: 'LineString', coordinates: [[120.1, 30.1], [120.2, 30.2]] }, properties: {} }
  ]
};

const results = WktUtils.featureCollectionToWktArray(featureCollection, { precision: 4 });
results.forEach((result, index) => {
  if (result.success) {
    console.log(`Feature ${index}: ${result.wkt}`);
  } else {
    console.log(`Feature ${index} 转换失败: ${result.error}`);
  }
});
```

## 错误处理

所有转换方法都返回包含成功状态的结果对象：

```typescript
interface WktResult {
  success: boolean;    // 转换是否成功
  wkt?: string;       // WKT字符串
  error?: string;     // 错误信息
  geometryType?: string; // 几何类型
}
```

### 错误处理示例

```typescript
const invalidGeometry = {
  type: 'LineString',
  coordinates: [[120.1, 30.1]] // LineString需要至少2个点
};

const result = WktUtils.geometryToWkt(invalidGeometry);
if (!result.success) {
  console.error('转换失败:', result.error);
  // 输出: "转换失败: LineString至少需要2个坐标点"
}
```

## 几何验证

```typescript
const geometry = {
  type: 'Point',
  coordinates: [120.1] // 无效：坐标不完整
};

const validation = WktUtils.validateGeometry(geometry);
if (!validation.isValid) {
  console.log('验证失败:', validation.errors.join(', '));
  // 输出: "验证失败: Point坐标格式无效"
}
```

## 实际应用场景

### 1. 地图分析服务调用

```typescript
import { geoJsonToWkt } from '@/utils/geometry/WktUtils';

// 用户在地图上绘制的区域
const drawnArea = {
  type: 'Polygon',
  coordinates: [[
    [103.59274916212075, 29.371853128306228],
    [103.60000000000000, 29.371853128306228],
    [103.60000000000000, 29.380000000000000],
    [103.59274916212075, 29.380000000000000],
    [103.59274916212075, 29.371853128306228]
  ]]
};

// 转换为WKT格式调用分析服务
const wktStr = geoJsonToWkt(drawnArea, 8);
if (wktStr) {
  verticalProfileAnalysis(wktStr);
}
```

### 2. 在Vue组件中使用

```vue
<template>
  <div>
    <button @click="convertGeometry">转换几何体</button>
    <div v-if="wktResult">
      <p>WKT结果：{{ wktResult }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { geoJsonToWkt } from '@/utils/geometry/WktUtils';

const wktResult = ref<string>('');

const convertGeometry = () => {
  const geometry = {
    type: 'Point',
    coordinates: [120.123, 30.456]
  };
  
  const wkt = geoJsonToWkt(geometry, 6);
  if (wkt) {
    wktResult.value = wkt;
  }
};
</script>
```

### 3. 数据库存储

```typescript
import { featureToWkt } from '@/utils/geometry/WktUtils';

// 从前端获取的管线数据
const pipelineFeature = {
  type: 'Feature',
  geometry: {
    type: 'LineString',
    coordinates: [
      [103.592749, 29.371853],
      [103.595000, 29.372000],
      [103.598000, 29.373000]
    ]
  },
  properties: {
    material: 'steel',
    diameter: 300
  }
};

// 转换为WKT格式存储到数据库
const wktGeometry = featureToWkt(pipelineFeature, 6);
if (wktGeometry) {
  // 保存到数据库
  await savePipelineToDatabase({
    geometry: wktGeometry,
    properties: pipelineFeature.properties
  });
}
```

## 性能考虑

1. **批量处理**：对于大量数据，使用`featureCollectionToWktArray`进行批量转换
2. **精度设置**：根据实际需求设置合理的精度，避免不必要的计算
3. **错误处理**：始终检查转换结果的`success`状态
4. **内存管理**：对于大型几何体，考虑分块处理

## API参考

### WktUtils类

| 方法 | 描述 |
|------|------|
| `geometryToWkt(geometry, options?)` | 几何体转WKT |
| `featureToWkt(feature, options?)` | Feature转WKT |
| `featureCollectionToWktArray(collection, options?)` | 批量转换 |
| `validateGeometry(geometry)` | 几何验证 |

### 便捷函数

| 函数 | 描述 |
|------|------|
| `geoJsonToWkt(geometry, precision?)` | 快速几何体转换 |
| `featureToWkt(feature, precision?)` | 快速Feature转换 |

## 常见问题

### Q: 如何处理空几何体？
A: 工具会自动处理空几何体，返回如"POINT EMPTY"这样的标准WKT格式。

### Q: 支持自定义坐标系吗？
A: 目前工具专注于格式转换，不处理坐标系转换。如需坐标系转换，请配合使用`CoordinateTransform`工具。

### Q: 如何处理无效的几何体？
A: 使用`validateGeometry`方法预先验证，或检查转换结果的`success`状态。

### Q: 转换精度如何设置？
A: 通过`precision`选项设置小数位数，默认为6位。根据实际应用场景选择合适的精度。

## 测试

运行测试示例：

```typescript
import { runAllExamples } from '@/utils/geometry/WktUtils.example';

// 在浏览器控制台中运行所有示例
runAllExamples();
```

## 更新日志

- v1.0.0: 初始版本，支持所有标准GeoJSON几何类型的WKT转换 