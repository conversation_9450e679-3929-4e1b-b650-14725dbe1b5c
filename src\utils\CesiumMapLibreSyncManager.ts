/**
 * @fileoverview Cesium与MapLibre视角同步管理器
 * @description 基于视口边界同步策略实现异构引擎间的精确联动
 * <AUTHOR> Assistant
 * @version 4.0.0
 */

/**
 * @interface ViewportBounds
 * @description 视口边界接口
 */
interface ViewportBounds {
  west: number
  south: number
  east: number
  north: number
}

/**
 * @interface CenterPosition
 * @description 中心位置接口
 */
interface CenterPosition {
  longitude: number
  latitude: number
  height?: number
}

/**
 * @class CesiumMapLibreSyncManager
 * @description Cesium与MapLibre视角同步管理器
 */
export class CesiumMapLibreSyncManager {
  private static instance: CesiumMapLibreSyncManager

  /**
   * @description 构造函数（私有）
   */
  private constructor() {
    // 单例模式实现
  }

  /**
   * @description 获取单例实例
   * @returns {CesiumMapLibreSyncManager} 单例实例
   */
  static getInstance(): CesiumMapLibreSyncManager {
    if (!CesiumMapLibreSyncManager.instance) {
      CesiumMapLibreSyncManager.instance = new CesiumMapLibreSyncManager()
    }
    return CesiumMapLibreSyncManager.instance
  }

  /**
   * @description 获取Cesium视口边界
   * @param {any} cesiumViewer Cesium viewer实例
   * @returns {ViewportBounds|null} 视口边界或null
   */
  getCesiumViewportBounds(cesiumViewer: any): ViewportBounds | null {
    try {
      const { Cesium } = BC.Namespace
      const canvas = cesiumViewer.canvas
      const camera = cesiumViewer.camera
      const ellipsoid = Cesium.Ellipsoid.WGS84
      
      // 获取屏幕四角的坐标点
      const corners = [
        new Cesium.Cartesian2(0, 0), // 左上
        new Cesium.Cartesian2(canvas.clientWidth, 0), // 右上
        new Cesium.Cartesian2(canvas.clientWidth, canvas.clientHeight), // 右下
        new Cesium.Cartesian2(0, canvas.clientHeight) // 左下
      ]
      
      const positions = []
      
      // 将屏幕坐标转换为地表坐标
      for (const corner of corners) {
        const ray = camera.getPickRay(corner)
        if (ray) {
          const intersection = Cesium.IntersectionTests.rayEllipsoid(ray, ellipsoid)
          if (intersection) {
            const intersectionPoint = Cesium.Ray.getPoint(ray, intersection.start)
            const cartographic = ellipsoid.cartesianToCartographic(intersectionPoint)
            positions.push({
              longitude: Cesium.Math.toDegrees(cartographic.longitude),
              latitude: Cesium.Math.toDegrees(cartographic.latitude)
            })
          }
        }
      }
      
      // 如果无法获取足够的角点，使用相机视锥计算
      if (positions.length < 3) {
        return this.getCesiumViewportBoundsByFrustum(cesiumViewer)
      }
      
      // 计算边界
      const longitudes = positions.map(p => p.longitude)
      const latitudes = positions.map(p => p.latitude)
      
      const west = Math.min(...longitudes)
      const east = Math.max(...longitudes)
      const south = Math.min(...latitudes)
      const north = Math.max(...latitudes)
      
      return { west, south, east, north }
      
    } catch (error) {
      console.error('[CesiumMapLibreSyncManager] 获取Cesium视口边界失败:', error)
      return null
    }
  }

  /**
   * @description 通过相机视锥计算Cesium边界（备用方法）
   * @param {any} cesiumViewer Cesium viewer实例
   * @returns {ViewportBounds|null} 边界信息或null
   */
  private getCesiumViewportBoundsByFrustum(cesiumViewer: any): ViewportBounds | null {
    try {
      const { Cesium } = BC.Namespace
      const camera = cesiumViewer.camera
      
      // 基于相机位置和方向计算近似边界
      const position = camera.positionCartographic
      const longitude = Cesium.Math.toDegrees(position.longitude)
      const latitude = Cesium.Math.toDegrees(position.latitude)
      const height = position.height
      
      // 根据高度估算视野范围
      const fov = camera.frustum.fov || Cesium.Math.toRadians(60)
      const aspectRatio = cesiumViewer.canvas.clientWidth / cesiumViewer.canvas.clientHeight
      
      // 估算地面覆盖范围
      const groundDistance = height * Math.tan(fov / 2) * 2
      const latSpan = (groundDistance / 111320) // 纬度1度约111320米
      const lonSpan = latSpan / Math.cos(Cesium.Math.toRadians(latitude)) * aspectRatio
      
      return {
        west: longitude - lonSpan / 2,
        south: latitude - latSpan / 2,
        east: longitude + lonSpan / 2,
        north: latitude + latSpan / 2
      }
      
    } catch (error) {
      console.error('[CesiumMapLibreSyncManager] 通过视锥计算Cesium边界失败:', error)
      return null
    }
  }

  /**
   * @description 获取MapLibre视口边界
   * @param {any} maplibreMap MapLibre地图实例
   * @returns {ViewportBounds|null} 视口边界或null
   */
  getMapLibreViewportBounds(maplibreMap: any): ViewportBounds | null {
    try {
      const bounds = maplibreMap.getBounds()
      
      return {
        west: bounds.getWest(),
        south: bounds.getSouth(),
        east: bounds.getEast(),
        north: bounds.getNorth()
      }
      
    } catch (error) {
      console.error('[CesiumMapLibreSyncManager] 获取MapLibre视口边界失败:', error)
      return null
    }
  }

  /**
   * @description 根据边界设置Cesium视图
   * @param {any} cesiumViewer Cesium viewer实例
   * @param {ViewportBounds} bounds 视口边界
   * @returns {boolean} 设置是否成功
   */
  setCesiumViewByBounds(cesiumViewer: any, bounds: ViewportBounds): boolean {
    try {
      const { Cesium } = BC.Namespace
      
      // 计算边界中心点
      const centerLon = (bounds.west + bounds.east) / 2
      const centerLat = (bounds.south + bounds.north) / 2
      
      // 计算边界跨度
      const lonSpan = Math.abs(bounds.east - bounds.west)
      const latSpan = Math.abs(bounds.north - bounds.south)
      
      // 根据跨度计算合适的高度
      const maxSpan = Math.max(lonSpan, latSpan)
      let height: number
      
      if (maxSpan > 10) {
        height = maxSpan * 50000
      } else if (maxSpan > 1) {
        height = maxSpan * 80000
      } else if (maxSpan > 0.1) {
        height = maxSpan * 100000
      } else {
        height = maxSpan * 150000
      }
      
      // 限制高度范围
      height = Math.max(1000, Math.min(height, 100000000))
      
      console.log(`[CesiumMapLibreSyncManager] 设置Cesium视图 - 中心:(${centerLon.toFixed(4)}, ${centerLat.toFixed(4)}), 高度:${height.toFixed(0)}米`)
      
      // 设置相机视角
      cesiumViewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(centerLon, centerLat, height),
        orientation: {
          heading: 0.0,
          pitch: Cesium.Math.toRadians(-30), // 3D视角
          roll: 0.0
        }
      })
      
      return true
      
    } catch (error) {
      console.error('[CesiumMapLibreSyncManager] 设置Cesium视图失败:', error)
      return false
    }
  }

  /**
   * @description 根据边界设置MapLibre视图
   * @param {any} maplibreMap MapLibre地图实例
   * @param {ViewportBounds} bounds 视口边界
   * @returns {boolean} 设置是否成功
   */
  setMapLibreViewByBounds(maplibreMap: any, bounds: ViewportBounds): boolean {
    try {
      console.log(`[CesiumMapLibreSyncManager] 设置MapLibre视图 - 边界:(${bounds.west.toFixed(4)}, ${bounds.south.toFixed(4)}, ${bounds.east.toFixed(4)}, ${bounds.north.toFixed(4)})`)
      
      // 使用MapLibre的fitBounds方法
      maplibreMap.fitBounds([
        [bounds.west, bounds.south], // 西南角
        [bounds.east, bounds.north]  // 东北角
      ], {
        padding: 0,
        duration: 0 // 无动画，立即跳转
      })
      
      return true
      
    } catch (error) {
      console.error('[CesiumMapLibreSyncManager] 设置MapLibre视图失败:', error)
      return false
    }
  }

  /**
   * @description 从Cesium同步到MapLibre
   * @param {any} cesiumViewer Cesium viewer实例
   * @param {any} maplibreMap MapLibre地图实例
   * @returns {boolean} 同步是否成功
   */
  syncCesiumToMapLibre(cesiumViewer: any, maplibreMap: any): boolean {
    try {
      // 获取Cesium视口边界
      const bounds = this.getCesiumViewportBounds(cesiumViewer)
      if (!bounds) return false
      
      console.log('[CesiumMapLibreSyncManager] 从Cesium同步到MapLibre (边界):', bounds)
      
      // 设置MapLibre视图
      return this.setMapLibreViewByBounds(maplibreMap, bounds)
      
    } catch (error) {
      console.error('[CesiumMapLibreSyncManager] Cesium到MapLibre同步失败:', error)
      return false
    }
  }

  /**
   * @description 从MapLibre同步到Cesium
   * @param {any} maplibreMap MapLibre地图实例
   * @param {any} cesiumViewer Cesium viewer实例
   * @returns {boolean} 同步是否成功
   */
  syncMapLibreToCesium(maplibreMap: any, cesiumViewer: any): boolean {
    try {
      // 获取MapLibre视口边界
      const bounds = this.getMapLibreViewportBounds(maplibreMap)
      if (!bounds) return false
      
      console.log('[CesiumMapLibreSyncManager] 从MapLibre同步到Cesium (边界):', bounds)
      
      // 设置Cesium视图
      return this.setCesiumViewByBounds(cesiumViewer, bounds)
      
    } catch (error) {
      console.error('[CesiumMapLibreSyncManager] MapLibre到Cesium同步失败:', error)
      return false
    }
  }

  /**
   * @description 销毁管理器
   */
  destroy(): void {
    // 清理资源
  }
} 