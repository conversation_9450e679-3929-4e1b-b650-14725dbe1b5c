<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    class="admin-sub-form"
    label-width="auto"
    :disabled="editable"
  >
    <el-form-item label="服务器名称" prop="name">
      <el-input
        class=""
        v-model.trim="formData.name"
        placeholder="请输入服务器名称"
      />
    </el-form-item>
    <el-form-item label="租户" prop="tenant">
      <el-input
        class=""
        v-model.trim="formData.tenant"
        placeholder="请输入租户"
      />
    </el-form-item>
    <el-form-item label="服务器标识" prop="sign">
      <el-input
        class=""
        v-model.trim="formData.sign"
        placeholder="请输入服务器标识"
      />
    </el-form-item>
    <el-form-item label="数据库地址" prop="databaseAddress">
      <el-input
        class=""
        v-model.trim="formData.databaseAddress"
        placeholder="请输入数据库地址"
      />
    </el-form-item>
    <el-form-item label="端口号" prop="databasePort">
      <el-input
        class=""
        v-model.trim="formData.databasePort"
        placeholder="请输入端口号"
      />
    </el-form-item>
    <el-form-item label="数据库名称" prop="databaseName">
      <el-input
        class=""
        v-model.trim="formData.databaseName"
        placeholder="请输入数据库名称"
      />
    </el-form-item>
    <el-form-item label="数据库用户名" prop="databaseUserName">
      <el-input
        class=""
        v-model.trim="formData.databaseUserName"
        placeholder="请输入数据库用户名"
      />
    </el-form-item>
    <el-form-item label="数据库密码" prop="databasePassword">
      <el-input
        class=""
        v-model.trim="formData.databasePassword"
        placeholder="请输入数据库密码"
      />
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import { iconType, system } from "@/utils/constant";
import localCache from "@/utils/auth";
const props = defineProps({
  formData: Object,
  editable: Boolean,
});
let formData: any = ref(props.formData);
let editable = ref(props.editable);
watch(
  () => props.formData,
  (val: any) => {
    resetForm();
    formData.value = val;
  }
);
watch(
  () => props.editable,
  (val) => {
    editable.value = val;
  }
);
const rules = {
  name: [
    { required: true, message: "请输入服务器名称", trigger: "blur" },
    { min: 2, message: "长度不低于2个字符", trigger: "blur" },
    { max: 20, message: "长度不超过20个字符", trigger: "blur" },
  ],
  tenant: [{ required: true, message: "请输入租户", trigger: "blur" }],
  sign: [{ required: true, message: "请输入服务器标识", trigger: "blur" }],
  databaseAddress: [
    { required: true, message: "请输入数据库地址", trigger: "blur" },
  ],
  databasePort: [{ required: true, message: "请输入端口号", trigger: "blur" }],
  databaseName: [
    { required: true, message: "请输入数据库名称", trigger: "blur" },
  ],
  databaseUserName: [
    { required: true, message: "请输入数据库用户名", trigger: "blur" },
  ],
  databasePassword: [
    { required: true, message: "请输入数据库密码", trigger: "blur" },
  ],
};
let form: any = ref(null);
const submitForm = async (): Promise<any> => {
  return (await form.value?.validate()) ? formData.value : null;
};
const resetForm = () => {
  form.value?.resetFields();
};

onMounted(async () => {
  resetForm();
});
defineExpose({
  submitForm,
  resetForm,
});
</script>
