/**
 * @description Cesium Canvas录制相关类型定义
 */

/**
 * @description 录制状态枚举
 */
export enum RecordState {
  IDLE = 'idle',           // 空闲状态
  READY = 'ready',         // 准备状态
  RECORDING = 'recording', // 录制中
  STOPPING = 'stopping',   // 正在停止
  ERROR = 'error'          // 错误状态
}

/**
 * @description Canvas录制配置
 */
export interface CanvasRecordConfig {
  frameRate?: number;           // 帧率，默认30
  videoBitsPerSecond?: number;  // 视频比特率
  mimeType?: string;            // 视频格式
  width?: number;               // 录制宽度
  height?: number;              // 录制高度
  fileName?: string;            // 文件名前缀
}

/**
 * @description 录制事件回调接口
 */
export interface RecordCallbacks {
  onStart?: () => void;
  onStop?: (blob: Blob, fileName: string) => void;
  onError?: (error: string) => void;
  onStateChange?: (state: RecordState) => void;
  onProgress?: (duration: number) => void;
}

/**
 * @description 录制结果
 */
export interface RecordResult {
  success: boolean;
  blob?: Blob;
  fileName?: string;
  duration?: number;
  size?: number;
  error?: string;
} 