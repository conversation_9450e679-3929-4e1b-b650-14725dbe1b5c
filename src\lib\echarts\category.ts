export const initCategoryEchart = () => {
  const option: any = {
    grid: {
      top: '26%',
      left: "5%",
      bottom: '5%',
      right: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      axisTick: { show: false },
      axisLine: {
        lineStyle: {
          color: "#F6F6F6",
        },
      },
      axisLabel: {
        interval: 0,
        textStyle: {
          color: "#2C3037",
        },
      },
      data: ['智能消火栓', '摄像头', '视频录像机', '门禁', '智能表']
    },
    yAxis: {

      type: 'value',
      name: "单位：个",
      // nameLocation: 'start', // 设置名称位置为起始位置（左对齐）
      // nameGap: 20,
      nameTextStyle: {
        color: "#5C5F66",
        align: 'left',
        padding: [0, 0, 0, -30]
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#F6F6F6",
        },
      },
    },

    series: [
      {
        barWidth: 12,
        data: [120, 200, 150, 80, 70],
        itemStyle: {
          color: '#1966FF'
        },
        type: 'bar'
      }
    ]
  };
  return option
}