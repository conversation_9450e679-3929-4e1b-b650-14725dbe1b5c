# MapLibre 测量工具使用说明

## 概述

`MeasureTool` 是为 MapLibre GL JS 设计的测量工具类，提供距离测量和面积测量功能，支持交互式绘制和编辑。

## 主要功能

### 1. 距离测量
- 支持折线距离测量
- 实时显示距离结果
- 支持编辑已绘制的测量线

### 2. 面积测量
- 支持多边形面积测量
- 实时显示面积结果
- 支持编辑已绘制的测量面

### 3. 安全的生命周期管理
- 自动检查图层和数据源是否存在
- 安全的资源清理机制
- 完善的错误处理

## 使用方法

### 基本用法

```typescript
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre'

// 获取测量工具实例
const measureTool = AppMaplibre.getMeasureTool()

// 开始距离测量
await measureTool.measureDistance('measure-distance-1')

// 开始面积测量
await measureTool.measureArea('measure-area-1')

// 停止当前测量
measureTool.stopCurrentMeasurement()

// 清除所有测量要素
measureTool.clearMeasureAll()
```

### 状态检查

```typescript
// 获取当前测量状态
const status = measureTool.getCurrentMeasureStatus()
console.log('测量活动状态:', status.isActive)
console.log('测量类型:', status.type)
console.log('当前图层ID:', status.layerId)
console.log('是否在编辑:', status.isEditing)

// 检查是否有活动测量
if (measureTool.hasActiveMeasurements()) {
  console.log('有活动的测量')
}
```

### 在 Vue 组件中使用

```vue
<script setup lang="ts">
import { onUnmounted } from 'vue'
import { AppMaplibre } from '@/lib/maplibre/AppMaplibre'

// 开始测量
const startMeasure = async (type: 'distance' | 'area') => {
  try {
    const measureTool = AppMaplibre.getMeasureTool()
    const layerId = `measure-${type}-${Date.now()}`
    
    if (type === 'distance') {
      await measureTool.measureDistance(layerId)
    } else {
      await measureTool.measureArea(layerId)
    }
  } catch (error) {
    console.error('测量启动失败:', error)
  }
}

// 清除测量
const clearMeasure = () => {
  try {
    const measureTool = AppMaplibre.getMeasureTool()
    measureTool.clearMeasureAll()
  } catch (error) {
    console.error('测量清除失败:', error)
  }
}

// 组件卸载时清理
onUnmounted(() => {
  try {
    const measureTool = AppMaplibre.getMeasureTool()
    const status = measureTool.getCurrentMeasureStatus()
    
    if (status.isActive) {
      measureTool.stopCurrentMeasurement()
    }
    
    measureTool.clearMeasureAll()
  } catch (error) {
    console.warn('测量工具清理失败:', error)
  }
})
</script>
```

## 配置选项

```typescript
interface MeasureConfig {
  /** 是否启用测量功能 */
  enabled?: boolean
  /** 线条颜色 */
  lineColor?: string
  /** 填充颜色 */
  fillColor?: string
  /** 线条宽度 */
  lineWidth?: number
  /** 点的半径 */
  pointRadius?: number
}

// 自定义配置
const measureTool = new MeasureTool(map, {
  enabled: true,
  lineColor: '#00ff00',
  fillColor: '#00ff00',
  lineWidth: 3,
  pointRadius: 4
})
```

## 最新修复

### v3.1.1 (2024-12-19)

1. **修复双击缩放问题**
   - 修复了双击完成测量时触发地图缩放的问题
   - 在双击事件处理中添加了事件阻止逻辑（preventDefault 和 stopPropagation）
   - 延迟恢复双击缩放功能，避免立即触发缩放效果
   - 同时修复了距离测量和面积测量的相同问题

### v3.1.0 (2024-12-19)

1. **修复测量结果不显示问题**
   - 修复了双击完成测量后结果标记不显示的问题
   - 改进了测量完成时的处理逻辑
   - 区分临时 Marker 和结果 Marker 的管理
   - 确保测量结果正确显示并保留

2. **优化清理机制**
   - 添加了 `cleanupMeasurementEvents` 方法专门清理事件监听器
   - 分离了临时元素清理和结果保留的逻辑
   - 改进了面积测量结果的显示样式

### v3.0.0 (2024-12-19)

1. **重构事件监听器管理**
   - 彻底重构了事件监听器的注册和清理机制
   - 使用 Map 和 Set 数据结构管理事件监听器和 Marker 对象
   - 修复了中止测量后无法重新启动的核心问题

2. **简化代码结构**
   - 移除复杂的闭包和局部变量管理
   - 使用箭头函数和统一的事件注册方法
   - 提高代码的可维护性和可靠性

3. **改进测量完成处理**
   - 测量完成时自动调用 stopCurrentMeasurement
   - 确保所有资源都被正确清理
   - 防止状态残留导致的功能异常

4. **完善生命周期管理** (v2.0.0)
   - 添加了当前测量状态追踪
   - 实现了安全的组件卸载清理
   - 修复了测量状态与组件生命周期不同步的问题

5. **修复图层删除错误** (v2.0.0)
   - 添加了安全的图层和数据源检查方法
   - 修复了在绘制过程中点击清除按钮时的图层删除错误
   - 改进了错误处理和日志记录

6. **增强错误处理** (v2.0.0)
   - 所有操作都包装在 try-catch 中
   - 提供详细的错误信息和警告
   - 防止因错误导致的功能中断

## 注意事项

1. **图层ID 唯一性**：确保每次测量使用唯一的图层ID
2. **生命周期管理**：在组件卸载时必须调用清理方法
3. **错误处理**：建议在所有测量操作中添加错误处理
4. **性能考虑**：避免同时进行多个测量操作

## 故障排除

### 常见问题

1. **"图层不存在"错误**
   - 原因：尝试删除未创建的图层
   - 解决：使用最新版本的安全删除方法

2. **测量无法启动**
   - 原因：地图实例未初始化
   - 解决：确保在地图加载完成后调用测量功能

3. **组件卸载时报错**
   - 原因：未正确清理测量状态
   - 解决：在 onUnmounted 中调用清理方法

### 调试信息

测量工具会在控制台输出详细的调试信息：
- 测量开始/停止状态
- 图层创建/删除操作
- 错误和警告信息

可以通过这些信息来诊断问题。 