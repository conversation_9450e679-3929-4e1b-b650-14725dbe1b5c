# Cesium高亮功能使用示例

## 概述

本文档展示了如何在分类查询组件中使用Cesium高亮功能，基于`PipeLocation.ts`工具实现。

## 核心实现

### 1. 导入依赖

```typescript
import { pipeLocationByBms, clearPipeLocationHighlight } from '@/utils/PipeLocation';
```

### 2. 查询结果高亮

```typescript
/**
 * @description 在Cesium上高亮查询结果
 * @param results 查询结果数据
 */
const highlightResultsOnCesium = async (results: any[]) => {
  try {
    console.log('🎯 开始Cesium高亮查询结果，点位数量:', results.length);
    
    // 提取管点编码用于高亮
    const pipeCodes = results
      .map(item => item.gxddh || item.bm || item.gid)
      .filter(code => code); // 过滤掉空值
    
    if (pipeCodes.length === 0) {
      console.warn('⚠️ 没有有效的管点编码可以高亮');
      return;
    }
    
    console.log('📍 提取的管点编码:', pipeCodes);
    
    // 使用pipeLocationByBms方法进行高亮
    await pipeLocationByBms(pipeCodes, 'cesium', 'point', {
      autoFit: false, // 不自动缩放，保持当前视角
      highlightColor: '#ff4444', // 红色高亮
      pointRadius: 8
    });
    
    console.log('✅ Cesium高亮完成');
    
  } catch (error) {
    console.error('❌ Cesium高亮失败:', error);
  }
};
```

### 3. 清除高亮

```typescript
/**
 * @description 清除所有高亮点位
 */
const clearHighlights = () => {
  try {
    console.log('🧹 清除高亮点位');
    
    if (mapEngine.value === 'cesium') {
      // 清除Cesium高亮
      clearPipeLocationHighlight('cesium');
      cesiumHighlightLayer = null;
    } else {
      // MapLibre清除逻辑...
    }
    
    // 清空高亮要素数组
    highlightedFeatures.value = [];
    
  } catch (error) {
    console.error('❌ 清除高亮失败:', error);
  }
};
```

### 4. 单点位飞行和高亮

```typescript
/**
 * @description 在Cesium上飞行到指定点位
 * @param row 行数据
 */
const flyToPointOnCesium = async (row: any) => {
  try {
    console.log('🎯 Cesium飞行到点位:', row);
    
    // 先清除之前的高亮
    clearPipeLocationHighlight('cesium');
    
    // 使用原有的locPipePoint逻辑进行飞行
    locPipePoint(row);
    
    // 使用pipeLocationByBms进行高亮
    const pipeCode = row.gxddh || row.bm || row.gid;
    if (pipeCode) {
      await pipeLocationByBms([pipeCode], 'cesium', 'point', {
        autoFit: false, // 不自动缩放，使用locPipePoint的飞行逻辑
        highlightColor: '#ffff00', // 黄色临时高亮
        pointRadius: 10
      });
      
      // 3秒后清除临时高亮
      setTimeout(() => {
        clearPipeLocationHighlight('cesium');
      }, 3000);
    }
    
    console.log('✅ Cesium飞行和高亮完成');
    
  } catch (error) {
    console.error('❌ Cesium飞行失败:', error);
    throw error;
  }
};
```

## 使用场景

### 场景1：查询完成后自动高亮

```typescript
const executeClassifiedQuery = async () => {
  try {
    // 查询逻辑...
    const result = await classifiedQuery(queryForm.value);
    
    if (result && result.code === 200) {
      tableData.value = result.data.list || [];
      
      // 根据地图引擎高亮查询结果
      if (tableData.value.length > 0) {
        if (mapEngine.value === 'cesium') {
          await highlightResultsOnCesium(tableData.value);
        } else {
          highlightResultsOnMapLibre(tableData.value);
        }
      }
    }
  } catch (error) {
    console.error('查询失败:', error);
  }
};
```

### 场景2：表格行点击定位

```typescript
const handleRowClick = async (row: any) => {
  try {
    if (mapEngine.value === 'cesium') {
      // Cesium模式：飞行和临时高亮
      await flyToPointOnCesium(row);
    } else {
      // MapLibre模式：飞行和临时高亮
      await flyToPointOnMapLibre(row);
    }
  } catch (error) {
    console.error('定位失败:', error);
    ElMessage.error('定位失败，请稍后重试');
  }
};
```

### 场景3：重置时清除高亮

```typescript
const handleReset = () => {
  // 重置查询条件
  queryForm.value = initForm();
  
  // 清除高亮
  clearHighlights();
  
  // 其他重置逻辑...
};
```

## 配置选项

### pipeLocationByBms参数说明

```typescript
await pipeLocationByBms(
  pipeCodes,        // 管点编码数组
  'cesium',         // 地图引擎类型
  'point',          // 定位类型（point/line）
  {
    autoFit: false,           // 是否自动缩放到定位范围
    highlightColor: '#ff4444', // 高亮颜色
    pointRadius: 8            // 点半径
  }
);
```

### 高亮颜色方案

| 场景 | 颜色 | 说明 |
|------|------|------|
| 查询结果高亮 | `#ff4444` | 红色，持续显示 |
| 临时单点高亮 | `#ffff00` | 黄色，3秒后消失 |
| 自定义高亮 | 自定义 | 根据业务需求设置 |

## 错误处理

### 常见错误及解决方案

1. **管点编码为空**
   ```typescript
   if (pipeCodes.length === 0) {
     console.warn('⚠️ 没有有效的管点编码可以高亮');
     return;
   }
   ```

2. **API调用失败**
   ```typescript
   try {
     await pipeLocationByBms(pipeCodes, 'cesium', 'point', options);
   } catch (error) {
     console.error('❌ Cesium高亮失败:', error);
     ElMessage.error('高亮功能暂时不可用');
   }
   ```

3. **地图实例不存在**
   ```typescript
   // PipeLocation.ts内部已处理此情况
   // 会自动检查Cesium实例的有效性
   ```

## 性能优化

### 1. 批量处理
- 一次性传入多个管点编码，避免多次API调用
- 使用数组过滤减少无效数据处理

### 2. 内存管理
- 及时清除临时高亮，避免内存泄漏
- 组件卸载时自动清理所有高亮

### 3. 用户体验
- 提供清晰的加载状态提示
- 合理的高亮持续时间设置
- 友好的错误提示信息

## 调试技巧

### 1. 控制台日志
```typescript
console.log('📍 提取的管点编码:', pipeCodes);
console.log('✅ Cesium高亮完成');
```

### 2. 错误监控
```typescript
try {
  await highlightResultsOnCesium(results);
} catch (error) {
  console.error('❌ Cesium高亮失败:', error);
  // 可以添加错误上报逻辑
}
```

### 3. 状态检查
```typescript
// 检查地图引擎状态
console.log('当前地图引擎:', mapEngine.value);

// 检查高亮要素数量
console.log('高亮要素数量:', highlightedFeatures.value.length);
```

## 注意事项

1. **数据格式**：确保查询结果包含有效的管点编码（gxddh、bm或gid）
2. **异步处理**：高亮函数是异步的，需要正确处理Promise
3. **资源清理**：及时清除不需要的高亮，避免视觉混乱
4. **错误处理**：提供友好的错误提示，增强用户体验

## 总结

通过集成`PipeLocation.ts`工具，Cesium高亮功能实现了：

- ✅ 统一的API接口
- ✅ 完善的错误处理
- ✅ 灵活的配置选项
- ✅ 良好的性能表现
- ✅ 友好的用户体验

这为分类查询功能提供了强大的3D可视化支持！
