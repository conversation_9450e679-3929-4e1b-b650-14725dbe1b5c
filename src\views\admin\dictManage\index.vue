<template>
  <div class="base-main" grid="~ rows-[72px_1fr] gap-y-3">
    <div class="query-form">
      <el-form :model="queryForm">
        <el-row :gutter="40">
          <el-col :span="6">
            <el-form-item label="参数名称">
              <el-input
                v-model="queryForm.configName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="参数键名">
              <el-input
                v-model="queryForm.configKey"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="系统内置">
              <el-select
                class="w-full admin-select"
                popper-class="admin-popper-select"
                v-model="queryForm.type"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in sysBuilt"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button
                class="admin-query-btn"
                type="primary"
                @click="queryData"
                :icon="Search"
              >
                查询
              </el-button>
              <el-button
                class="admin-reset-btn"
                :icon="Refresh"
                @click="resetSearch"
              >
                重置</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="pb-unset! box-border [&_>div]:overflow-hidden">
      <el-button
        class="admin-add-btn"
        type="primary"
        :icon="Plus"
        @click="newData"
      >
        添加</el-button
      >
      <div class="table-height mt-4">
        <el-table
          v-loading="loading"
          class="routeCt"
          :row-class-name="tableRowClassName"
          :data="tableData"
          style="width: 100%"
          height="100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="100"
            align="center"
          />
          <el-table-column prop="configName" label="参数名称" align="center" />
          <el-table-column
            prop="configKey"
            label="参数键名"
            min-width="150"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="configValue"
            label="参数键值"
            min-width="100"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column prop="type" label="系统内置" align="center">
            <template #default="scope">
              <div v-if="scope.row.type === '1'">是</div>
              <div v-else>否</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="remark"
            label="备注"
            show-overflow-tooltip
            align="center"
          >
            <template v-slot="scope">
              <div v-if="scope.row.remark === '' || scope.row.remark === null">
                无
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="操作时间" align="center" />
          <el-table-column label="操作" min-width="100" align="center">
            <template v-slot="{ row }">
              <!-- <el-button link class="primary-link" :icon="View" @click="handleCheck(row)">
                详情</el-button
              > -->
              <div class="flex-c">
                <el-button
                  link
                  class="primary-link color-#1966FF"
                  :icon="Edit"
                  @click="handleEdit(row)"
                  >修改</el-button
                >
                <div class="tb-line"></div>
                <el-button
                  link
                  class="danger-link color-#FF7373"
                  :icon="Delete"
                  @click="handleDelete(row)"
                  >删除</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagibox box-border">
        <div class="pagitotal">共{{ tableSize }}条数据</div>
        <pagination
          class="custom-pagi-card"
          :total="tableSize"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          @pagination="getList"
        ></pagination>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :key="dialogKey"
      :model-value="visible"
      width="30%"
      class="admin-dialog"
      :before-close="handleClose"
      :close-on-click-modal="false"
      ref="test"
    >
      <dict-info
        ref="dictRef"
        v-model:formData="formData"
        v-model:editable="editable"
        v-model:dialogTitle="dialogTitle"
      />
      <template v-slot:footer v-if="dialogTitle != '参数查看'">
        <div>
          <el-button
            @click="handleClose"
            class="custom-close-button"
            :loading="subBtnLoading"
            >取 消</el-button
          >
          <el-button
            type="primary"
            :loading="subBtnLoading"
            class="custom-sub-button"
            @click="eventSubmit"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import {
  Search,
  Refresh,
  Plus,
  View,
  Edit,
  Delete,
} from "@element-plus/icons-vue";
import { sysBuilt } from "@/utils/constant";
import DictInfo from "./DictInfo.vue";
import {
  queryDict,
  addDict,
  editDict,
  detailsDict,
  deleteDict,
} from "@/api/dict";
import type { SysRole, QueryForm } from "./type";
const initQueryForm = () => {
  return {
    configName: "",
    configKey: "",
    type: "",
    pageNum: 1,
    pageSize: 20,
  };
};
const queryForm = ref<QueryForm>(initQueryForm());
const initFormData = () => {
  return {
    id: "",
    configName: "",
    configKey: "",
    configValue: "",
    type: "0",
    remark: "",
  };
};
const formData = ref<SysRole>(initFormData());
const loading = ref(false);
const tableData = ref<SysRole[]>([]);
const dialogTitle = ref("");
const dialogKey = ref(0);
const visible = ref(false);
const editable = ref(false);
const subBtnLoading = ref(false);
const tableSize = ref(0);
const dictRef = ref();
const queryData = () => {
  getList();
};
const resetSearch = () => {
  queryForm.value = initQueryForm();
  getList();
};
const newData = () => {
  formData.value = initFormData();
  editable.value = false;
  dialogTitle.value = "参数新增";
  dialogKey.value++;
  visible.value = true;
};
const handleCheck = async (row: SysRole) => {
  editable.value = true;
  dialogTitle.value = "参数查看";
  const result = await detailsDict(row.id);
  formData.value = result.data;
  visible.value = true;
};
const handleEdit = async (row: SysRole) => {
  editable.value = false;
  dialogTitle.value = "参数修改";
  const result = await detailsDict(row.id);
  formData.value = result.data;
  visible.value = true;
};
const handleDelete = (row: SysRole) => {
  ElMessageBox.confirm("确定要删除当前参数吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox",
  }).then(() => {
    deleteDict(row.id).then((res) => {
      if (res.code == 200) {
        ElMessage({
          showClose: true,
          message: "删除成功",
          type: "success",
        });
        getList();
      }
    });
  });
};
const handleClose = () => {
  visible.value = false;
};
const eventSubmit = async () => {
  try {
    var info = await dictRef.value!.submitForm();
    if (info) {
      subBtnLoading.value = true;
      if (dialogTitle.value === "参数新增") {
        const result = await addDict(info);
        if (result.code === 200) {
          visible.value = false;
          ElMessage.success(result.msg);
          await getList();
        }
      } else {
        const result = await editDict(info);
        if (result.code === 200) {
          visible.value = false;
          ElMessage.success("修改成功");
          await getList();
        }
      }
    }
  } finally {
    subBtnLoading.value = false;
  }
};
const getList = async () => {
  try {
    loading.value = true;
    const result = await queryDict(queryForm.value);
    tableData.value = result.data.list;
    tableSize.value = result.data.totalCount;
  } finally {
    loading.value = false;
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.table-height {
  height: calc(100vh - 345px);
}
</style>
