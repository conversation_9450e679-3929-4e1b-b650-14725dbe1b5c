<!--
 * @Description: 横断面分析
 * @Date: 2025-01-10
 * @Author: 项目开发团队
 * @LastEditTime: 2025-01-10
-->
<template>
  <page-card :close-icon="false" class="tabulate-sta" title="横断面分析">
    <div class="vertical-profile-analysis-container">
      <!-- 使用缓冲区分析绘制按钮组件，但只启用线段绘制 -->
      <BufferAnalysisDrawButtons
        :map-engine="mapEngine"
        :config="profileDrawConfig"
        @draw-start="handleDrawStart"
        @draw-complete="handleDrawComplete"
        @draw-error="handleDrawError"
        @clear-all="handleClearAll"
      />

      <!-- 显示当前绘制结果 -->
      <div v-if="showExternalResult && currentResult" class="external-result">
        <!-- 横断面分析信息 -->
        <div class="analysis-info" v-if="currentResult.geometry">
          <el-divider content-position="left" class="color-#2C3037 mb-2.5"
            >剖面信息</el-divider
          >
          <div class="info-content"></div>
        </div>
      </div>
    </div>
  </page-card>
</template>

<script lang="ts" setup>
import { ref, computed, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import { ElDivider, ElMessage } from "element-plus";
import BufferAnalysisDrawButtons from "@/components/BufferAnalysisDrawButtons.vue";
import type {
  DrawType,
  MapEngineType,
  DrawResult,
  BufferDrawConfig,
} from "@/components/BufferAnalysisDrawButtons.vue";
import { verticalProfileAnalysis } from "@/api/analysis";
import { geoJsonToWkt } from "@/utils/geometry/WktUtils";
import * as d3 from "d3";
import { min, max } from "d3-array";
import { axisTop, axisLeft } from "d3-axis";

/**
 * 组件属性定义
 */
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});
const route = useRoute();
/**
 * @description 计算地图引擎类型
 */
const mapEngine = computed((): MapEngineType => {
  return route.path.includes("cesium") ? "cesium" : "maplibre";
});

/**
 * @description 横断面分析绘制配置（只启用线段绘制）
 */
const profileDrawConfig: Partial<BufferDrawConfig> = {
  // UI配置
  buttonSize: "default",
  buttonLayout: "horizontal",
  buttonSpacing: 8,
  // 功能配置 - 只启用线段绘制
  enabledDrawTypes: ["linestring"],
  showTips: true,
  showResult: true,
  showResultDetails: false, // 横断面分析不需要详细的GeoJSON展示
  // 绘制配置
  clearPreviousDrawing: true,
  autoSwitchToSelect: true,
};

// 组件状态
const currentResult = ref<DrawResult | null>(null);
const showExternalResult = ref(false); // 是否显示外部结果区域
const profileAnalysisData = ref<any[]>([]); // 横断面分析数据
const showProfileChart = ref(false); // 是否显示图表

/**
 * @description 横断面分析数据接口
 */
interface ProfileAnalysisItem {
  ms: number; // 埋深
  gc: number; // 高程
  gj: string; // 管径
  gxlx: string; // 管线类型
  qdms: number; // 起点埋深
  zdms: number; // 终点埋深
  qdbh: string; // 起点编号
  zdbh: string; // 终点编号
  cz: string; // 管线材质类型
  xdata: number; // X轴数据（距离）
  // 新增字段
  szdl: string; // 所在道路
  qdgc: number; // 起点高程
  zdgc: number; // 终点高程
  jj: number; // 间距（相交点的间距）
}

/**
 * @description 处理绘制开始事件
 * @param {DrawType} type - 绘制类型
 */
const handleDrawStart = (type: DrawType) => {
  console.log(`横断面分析绘制开始: ${type}`);
  currentResult.value = null;
  showExternalResult.value = false;
};

/**
 * @description 处理绘制完成事件
 * @param {DrawResult} result - 绘制结果
 */
const handleDrawComplete = async (result: DrawResult) => {
  console.log("横断面分析绘制完成:", result);
  currentResult.value = result;
  showExternalResult.value = true;

  // 这里可以添加额外的业务逻辑
  // 例如：保存剖面线数据、准备高程分析等

  // 使用新的WKT转换工具将绘制结果转换为WKT格式
  if (result.geometry) {
    const wktStr = geoJsonToWkt(result.geometry, 8);

    if (wktStr) {
      console.log("绘制几何体WKT:", wktStr);

      try {
        // 可以调用真实的API
        const { status, data } = await verticalProfileAnalysis(wktStr);
        if (status === 200) {
          const convertedData = convertProfileData(data);
          profileAnalysisData.value = convertedData;

          // 渲染图表
          if (convertedData.length > 0) {
            showProfileChart.value = true;
            setTimeout(() => {
              renderProfileChart();
            }, 100);
          }
        }
      } catch (error) {
        console.error("横断面分析失败:", error);
        ElMessage.error("横断面分析失败");
      }
    } else {
      console.error("几何体转换为WKT失败");
    }
  }
};

/**
 * @description 处理绘制错误事件
 * @param {object} error - 错误信息
 */
const handleDrawError = (error: { type: DrawType; message: string }) => {
  console.error("横断面分析绘制错误:", error);

  // 显示错误提示
  ElMessage.error(`剖面线绘制失败: ${error.message}`);
};

/**
 * @description 处理清空事件
 */
const handleClearAll = () => {
  console.log("清空剖面线绘制");
  currentResult.value = null;
  showExternalResult.value = false;
  clearProfileChart();

  ElMessage.info("已清空剖面线");
};

/**
 * @description 转换横断面分析数据格式
 * @param {any} apiData - API返回的数据
 * @returns {ProfileAnalysisItem[]} 转换后的数据
 */
const convertProfileData = (apiData: any): ProfileAnalysisItem[] => {
  if (!apiData || !apiData.features) {
    return [];
  }
  return apiData.features.map((feature: any, index: number) => {
    const props = feature.properties;
    console.log(props);
    // 计算埋深（负值表示地下）
    const avgDepth = (props.QDMS + props.ZDMS) / 2;

    // 计算间距（模拟相交点间距，实际应根据具体几何计算）
    const spacing = props.GDCD || (index + 1) * 20;
    const QDBH = props.QDBH || '';
    const ZDBH = props.ZDBH || '';
    // const length = props.QDBH.length > 0 ? QDBH.length : 10;
    // console.log(length);
    return {
      ms: -avgDepth, // 埋深（负值）
      gc: (props.QDGC + props.ZDGC) / 2, // 平均高程
      gj: props.GJ?.toString() || "0", // 管径
      gxlx: props.GL || "JS", // 管线类型
      qdms: props.QDMS || 0, // 起点埋深
      zdms: props.ZDMS || 0, // 终点埋深
      qdbh: QDBH, // 起点编号（修正字段名）
      zdbh: ZDBH, // 终点编号（修正字段名）
      cz: props.CZ || "PE", // 材质
      xdata: (index + 1) * 55, // X轴位置
      // 新增字段
      szdl: props.SZDL || "", // 所在道路
      qdgc: props.QDGC || 0, // 起点高程
      zdgc: props.ZDGC || 0, // 终点高程
      jj: spacing, // 间距
    };
  });
};

/**
 * @description 渲染横断面图表
 */
const renderProfileChart = () => {
  if (!profileAnalysisData.value || profileAnalysisData.value.length === 0) {
    return;
  }

  // 清除之前的图表
  (d3 as any).select(".info-content").selectAll("svg").remove();

  const data = profileAnalysisData.value;
  const height = 200;
  const paddingTop = 8;
  let width = data.length * 90;

  if (width < 500) {
    width = 500;
  }

  // 创建SVG容器 - 增加高度以容纳更多字段
  const svg = (d3 as any)
    .select(".info-content")
    .append("svg")
    .attr("style", `width:${width + 100}px;height:470px;margin-left:20px;`);

  // 获取横竖轴最大最小值
  const minDataY = (min as any)(data, (d: any) => d.ms);
  const maxDataX = (max as any)(data, (d: any) => d.xdata);
  console.log(maxDataX)
  // 定义横竖轴范围
  const x = (d3 as any)
    .scaleLinear()
    .domain([0, maxDataX + paddingTop])
    .range([0, width]);

  const y = (d3 as any)
    .scaleLinear()
    .domain([0, minDataY - 0.1])
    .range([0, height]);

  // 创建横竖轴比例
  const xAxis = (axisTop as any)(x).tickFormat("").ticks(0);
  const yAxis = (axisLeft as any)(y);

  // 添加X轴
  svg
    .append("g")
    .attr("class", "axis")
    .attr("transform", "translate(" + 40 + "," + paddingTop + ")")
    .call(xAxis)
    .append("text")
    .attr("fill", "#333333")
    .attr("transform", "translate(" + (width - 20) + "," + 0 + ")")
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "end")
    .text("地表");

  // 添加Y轴
  svg
    .append("g")
    .attr("class", "yaxis")
    .attr("transform", "translate(" + 40 + "," + paddingTop + ")")
    .call(yAxis)
    .append("text")
    .attr("fill", "#333333")
    .attr("transform", "translate(" + 0 + "," + (height - 35) + "),rotate(-90)")
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "end")
    .text("埋深/m");

  // 添加管道圆形
  svg
    .append("g")
    .selectAll("circle")
    .data(data)
    .enter()
    .append("circle")
    .attr("stroke", "#00B1FF")
    .attr("r", (d: any) => {
      return Number(d.gj) / 20;
    })
    .attr("transform", (d: any) => {
      return (
        "translate(" + (x(d.xdata) + 30) + "," + (y(d.ms) + paddingTop) + ")"
      );
    })
    .attr("fill", "rgba(154,222,227,0)");

  // 添加虚线
  svg
    .append("g")
    .selectAll("line")
    .data(data)
    .enter()
    .append("line")
    .attr("x1", (d: any) => x(d.xdata) + 30)
    .attr("x2", (d: any) => x(d.xdata) + 30)
    .attr("y1", (d: any) => y(d.ms) + paddingTop + Number(d.gj) / 50 + 15)
    .attr("y2", 470)
    .style("stroke-dasharray", "5,5")
    .style("stroke", "#00B1FF");

  // 添加管径文本 - 左对齐到虚线位置
  svg
    .append("g")
    .selectAll(".diameter-text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "diameter-text")
    .attr("fill", "#333333")
    .attr("transform", (d: any) => {
      return "translate(" + (x(d.xdata) + 35) + "," + 218 + ")";
    })
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "start") // 改为左对齐
    .attr("font-size", 10)
    .text((d: any) => d.gj);

  // 添加起点埋深 - 左对齐到虚线位置
  svg
    .append("g")
    .selectAll(".start-depth-text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "start-depth-text")
    .attr("fill", "#333333")
    .attr("transform", (d: any) => {
      return "translate(" + (x(d.xdata) + 35) + "," + 243 + ")";
    })
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "start") // 改为左对齐
    .attr("font-size", 10)
    .text((d: any) => d.qdms);

  // 添加终点埋深 - 左对齐到虚线位置
  svg
    .append("g")
    .selectAll(".end-depth-text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "end-depth-text")
    .attr("fill", "#333333")
    .attr("transform", (d: any) => {
      return "translate(" + (x(d.xdata) + 35) + "," + 268 + ")";
    })
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "start") // 改为左对齐
    .attr("font-size", 10)
    .text((d: any) => d.zdms);

  // 添加起点编码 - 左对齐到虚线位置
  svg
    .append("g")
    .selectAll(".start-code-text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "start-code-text")
    .attr("fill", "#333333")
    .attr("transform", (d: any) => {
      return "translate(" + (x(d.xdata) + 35) + "," + 293 + ")";
    })
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "start") // 改为左对齐
    .attr("font-size", 9)
    .text((d: any) => d.qdbh);

  // 添加终点编码 - 左对齐到虚线位置
  svg
    .append("g")
    .selectAll(".end-code-text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "end-code-text")
    .attr("fill", "#333333")
    .attr("transform", (d: any) => {
      return "translate(" + (x(d.xdata) + 35) + "," + 318 + ")";
    })
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "start") // 改为左对齐
    .attr("font-size", 9)
    .text((d: any) => d.zdbh);

  // 添加所在道路 - 左对齐到虚线位置
  svg
    .append("g")
    .selectAll(".road-text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "road-text")
    .attr("fill", "#333333")
    .attr("transform", (d: any) => {
      return "translate(" + (x(d.xdata) + 35) + "," + 343 + ")";
    })
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "start") // 改为左对齐
    .attr("font-size", 9)
    .text((d: any) => d.szdl);

  // 添加起点高程 - 左对齐到虚线位置
  svg
    .append("g")
    .selectAll(".start-elevation-text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "start-elevation-text")
    .attr("fill", "#333333")
    .attr("transform", (d: any) => {
      return "translate(" + (x(d.xdata) + 35) + "," + 368 + ")";
    })
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "start") // 改为左对齐
    .attr("font-size", 10)
    .text((d: any) => d.qdgc.toFixed(2));

  // 添加终点高程 - 左对齐到虚线位置
  svg
    .append("g")
    .selectAll(".end-elevation-text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "end-elevation-text")
    .attr("fill", "#333333")
    .attr("transform", (d: any) => {
      return "translate(" + (x(d.xdata) + 35) + "," + 393 + ")";
    })
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "start") // 改为左对齐
    .attr("font-size", 10)
    .text((d: any) => d.zdgc.toFixed(2));

  // 添加材质 - 左对齐到虚线位置
  svg
    .append("g")
    .selectAll(".material-text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "material-text")
    .attr("fill", "#333333")
    .attr("transform", (d: any) => {
      return "translate(" + (x(d.xdata) + 35) + "," + 418 + ")";
    })
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "start") // 改为左对齐
    .attr("font-size", 10)
    .text((d: any) => d.cz);

  // 添加间距 - 左对齐到虚线位置
  svg
    .append("g")
    .selectAll(".spacing-text")
    .data(data)
    .enter()
    .append("text")
    .attr("class", "spacing-text")
    .attr("fill", "#333333")
    .attr("transform", (d: any) => {
      return "translate(" + (x(d.xdata) + 35) + "," + 443 + ")";
    })
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "start") // 改为左对齐
    .attr("font-size", 10)
    .text((d: any) => d.jj.toFixed(2));

  // 添加表格线 - 绘制到最右侧
  const tableLines = [215, 240, 265, 290, 315, 340, 365, 390, 415, 440, 465];
  tableLines.forEach((y) => {
    svg
      .append("line")
      .attr("x1", 0)
      .attr("x2", width + 80) // 绘制到图表的最右侧
      .attr("y1", y)
      .attr("y2", y)
      .style("stroke-dasharray", "5,5")
      .style("stroke", "#08646E");
  });

  // 添加表格标题 - 左对齐
  const tableLabels = [
    { text: "管径/mm", y: 218 },
    { text: "起点埋深/m", y: 243 },
    { text: "终点埋深/m", y: 268 },
    { text: "起点编码", y: 293 },
    { text: "终点编码", y: 318 },
    { text: "所在道路", y: 343 },
    { text: "起点高程/m", y: 368 },
    { text: "终点高程/m", y: 393 },
    { text: "材质", y: 418 },
    { text: "间距/m", y: 443 },
  ];

  tableLabels.forEach((label) => {
    svg
      .append("text")
      .attr("fill", "#333333")
      .attr("transform", `translate(5, ${label.y})`) // 调整到左侧位置
      .attr("y", 6)
      .attr("dy", "0.71em")
      .attr("text-anchor", "start") // 改为左对齐
      .attr("font-size", 10)
      .attr("font-weight", "bold") // 加粗标题
      .text(label.text);
  });
};

/**
 * @description 清理图表
 */
const clearProfileChart = () => {
  (d3 as any).select(".info-content").selectAll("svg").remove();
  showProfileChart.value = false;
  profileAnalysisData.value = [];
};

// 组件卸载时清理
onUnmounted(() => {
  clearProfileChart();
});
</script>

<style lang="scss" scoped>
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 800px;
  min-height: 120px;
  z-index: 1000;
}

.external-result {
  margin-top: 16px;

  .ml-2 {
    margin-left: 8px;
  }
}

.analysis-info {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;

  .info-content {
    margin-bottom: 12px;
    min-height: 60px;
    //max-height: 400px;
    overflow-x: auto;
    overflow-y: hidden;

    // D3图表样式
    :deep(svg) {
      margin: 0 0 0 10px;

      path {
        fill: none;
        stroke: #76bf8a;
        stroke-width: 3px;
      }

      .axis {
        color: #333333;

        text {
          fill: #333333;
          font-size: 12px;
        }

        path,
        line {
          stroke: #333333;
        }
      }

      .yaxis {
        color: #333333;

        text {
          fill: #333333;
          font-size: 12px;
        }

        path,
        line {
          stroke: #333333;
        }
      }
    }

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 5px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: #409eff;
    }

    &::-webkit-scrollbar-corner {
      background-color: #409eff;
    }

    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 5px rgba(64, 158, 255, 0.2);
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
      font-size: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 500;
        color: #606266;
      }

      .value {
        color: #303133;
        font-family: "Courier New", monospace;
      }
    }
  }

  .analysis-actions {
    :deep(.el-button) {
      font-size: 12px;
      padding: 6px 12px;
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
