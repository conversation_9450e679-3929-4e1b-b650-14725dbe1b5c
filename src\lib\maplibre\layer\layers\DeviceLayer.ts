import { Map as glMap } from 'maplibre-gl';
import { BaseLayer } from './BaseLayer';
import { getImages } from '@/utils/getImages';
import { fetchDeviceSummary } from '@/utils/deviceUtils';
import { mockLngLatInPolygon } from '@/utils/mockUtil';

// 设备状态
export const deviceStatus = [
  {
    label: '停用',
    value: '0',
  },
  {
    label: '在线',
    value: '1',
  },
  {
    label: '离线',
    value: '2',
  },
  {
    label: '故障',
    value: '3',
  },
];

/**
 * 聚合状态管理接口
 */
interface ClusterState {
  expandedClusters: Set<number>;
  expandedDevices: Map<number, any[]>;
  tempLayerIds: Set<string>;
}

export default class DeviceLayer extends BaseLayer {
  protected declare _delegate: any;
  private clusterState: ClusterState;
  private clickHandlers: Array<() => void> = [];

  constructor(options: any) {
    super(options);

    // 如果没有 code 字段，尝试从其他字段获取
    if (!options.code) {
      if (options.icon) {
        options.code = options.icon;
      } else if (options.id && options.id.startsWith('device_')) {
        options.code = options.id.replace('device_', '');
      } else {
        options.code = 'unknown';
      }
    }

    this.clusterState = {
      expandedClusters: new Set(),
      expandedDevices: new Map(),
      tempLayerIds: new Set()
    };
  }

  /**
   * @description 验证并解析经纬度坐标
   * @param longitude - 经度值（字符串或数字）
   * @param latitude - 纬度值（字符串或数字）
   * @returns 有效的坐标数组 [lng, lat] 或 null
   */
  private validateAndParseCoordinates(longitude: any, latitude: any): [number, number] | null {
    // 经度正则表达式：-180 到 180，支持小数
    const longitudeRegex = /^-?(?:180(?:\.0+)?|(?:1[0-7]\d|[1-9]?\d)(?:\.\d+)?)$/;

    // 纬度正则表达式：-90 到 90，支持小数
    const latitudeRegex = /^-?(?:90(?:\.0+)?|(?:[1-8]?\d)(?:\.\d+)?)$/;

    // 转换为字符串进行验证
    const lngStr = String(longitude).trim();
    const latStr = String(latitude).trim();

    // 检查是否为空或无效值
    if (!lngStr || !latStr || lngStr === 'null' || latStr === 'null' ||
        lngStr === 'undefined' || latStr === 'undefined' ||
        lngStr === '' || latStr === '') {
      return null;
    }

    // 正则表达式验证
    if (!longitudeRegex.test(lngStr) || !latitudeRegex.test(latStr)) {
      return null;
    }

    // 转换为数字
    const lng = parseFloat(lngStr);
    const lat = parseFloat(latStr);

    // 数值范围验证（双重保险）
    if (isNaN(lng) || isNaN(lat) || lng < -180 || lng > 180 || lat < -90 || lat > 90) {
      return null;
    }

    return [lng, lat];
  }

  /**
   * @description 从 URL 加载 GeoJSON 数据
   */
  private async loadGeoJsonData(url: string): Promise<GeoJSON.FeatureCollection> {
    try {
      const response = await fetchDeviceSummary({
        size: -1,
        current: 1,
        secondDeviceTypeCode: this.options.code,
      });

      const validFeatures: GeoJSON.Feature[] = [];

      for (const item of response) {
        // 验证经纬度
        const coordinates = this.validateAndParseCoordinates(item.longitude, item.latitude);

        // 如果经纬度验证失败，跳过该项
        if (!coordinates) {
          console.warn('跳过无效坐标的设备:', {
            id: item.id,
            name: item.name,
            longitude: item.longitude,
            latitude: item.latitude
          });
          continue;
        }

        validFeatures.push({
          type: 'Feature',
          properties: item,
          geometry: {
            type: 'Point',
            coordinates: coordinates,
          },
        });
      }

      return {
        type: 'FeatureCollection' as const,
        features: validFeatures,
      };
    } catch (error) {
      throw error;
    }
  }

  async addToInternal(map: glMap) {
    this._map = map;
    let delegate: any;
    const geojson = await this.loadGeoJsonData(this.options.url as string);

    // 为GeoJSON数据添加默认status和id
    geojson.features.forEach((feature, index) => {
      if (!feature.properties) {
        feature.properties = {};
      }
      if (!feature.properties.status) {
        feature.properties.status = '1';
      }
      if (!feature.properties.id) {
        feature.properties.id = `${this.options.icon}_${index}`;
      }
    });

    const status = deviceStatus.map((item) => item.value);

    // 加载所有状态对应的图标
    await Promise.all(
      status.map((statusValue) =>
        this.loadImage(
          `${this.options.icon}_${statusValue}`,
          getImages(`device/${this.options.icon}2x.png`)
        )
      )
    );

    if (geojson.features.length > 0) {
      delegate = this.loadDeviceWithCluster(status, geojson);
    }

    return delegate;
  }

  /**
   * @description 异步加载图片资源并添加到地图
   */
  async loadImage(id: string, imageUrl: string): Promise<void> {
    if (this._map.hasImage(id)) {
      return;
    }

    try {
      const response = await this._map.loadImage(imageUrl);
      this._map.addImage(id, response.data);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 加载带聚合功能的设备图层
   */
  loadDeviceWithCluster(status: string[], geojson: any) {
    const sourceId = `device_${this.options.icon}_source`;
    
    // 添加支持聚合的数据源
    this._map.addSource(sourceId, {
      type: 'geojson',
      data: geojson,
      cluster: true,
      clusterMaxZoom: 16,
      clusterRadius: 120,
      clusterMinPoints: 2,
      promoteId: 'id',
    });

    // 创建聚合图层
    this.createClusterLayers(status, sourceId);
    
    // 设置点击事件
    this.setupClusterEvents();

    return this._map.getLayer(this._id);
  }

  /**
   * 创建聚合相关的图层
   */
  private createClusterLayers(status: string[], sourceId: string) {
    // 1. 聚合圆圈图层 - 统一蓝色
    const clusterLayerId = `${this._id}_clusters`;
    this._map.addLayer({
      id: clusterLayerId,
      type: 'circle',
      source: sourceId,
      filter: ['has', 'point_count'],
      paint: {
        'circle-color': '#ffffff',
        'circle-radius': [
          'step',
          ['get', 'point_count'],
          15, 7,
          20, 14,
          18
        ],
        'circle-stroke-width': 2,
        'circle-stroke-color': '#2b78fe'
      }
    });

    // 2. 聚合数量文本图层
    const clusterCountLayerId = `${this._id}_cluster_count`;
    this._map.addLayer({
      id: clusterCountLayerId,
      type: 'symbol',
      source: sourceId,
      filter: ['has', 'point_count'],
      layout: {
        'text-field': '{point_count_abbreviated}',
        'text-size': 12
      },
      paint: {
        'text-color': '#2b78fe'
      }
    });

    // 3. 非聚合设备图层
    const matchStatus = status.flatMap((item) => [
      [item],
      `${this.options.icon}_${item}`,
    ]);
    const image = [
      'match',
      ['get', 'status'],
      ...matchStatus,
      `${this.options.icon}_${status[0]}`,
    ] as any;

    this._map.addLayer({
      id: this._id,
      type: 'symbol',
      source: sourceId,
      filter: ['!', ['has', 'point_count']],
      layout: {
        'icon-image': image,
        'icon-size': 0.4,
        'text-offset': [0, 0.5],
        'icon-offset': [0, -28],
        'text-anchor': 'top',
        'text-size': 16,
      },
      paint: {
        'text-color': '#2a71af',
        'icon-opacity': [
          'case',
          ['boolean', ['feature-state', 'flash'], false],
          0,
          1,
        ],
      },
    });

    // 保存图层ID
    this.clusterState.tempLayerIds.add(clusterLayerId);
    this.clusterState.tempLayerIds.add(clusterCountLayerId);
  }

  /**
   * 设置聚合点击事件
   */
  private setupClusterEvents() {
    const clusterLayerId = `${this._id}_clusters`;

    // 聚合点击事件
    const clusterClickHandler = (e: any) => {
      const features = this._map.queryRenderedFeatures(e.point, {
        layers: [clusterLayerId]
      });

      if (!features.length) return;

      const clusterId = features[0].properties!.cluster_id;
      const pointCount = features[0].properties!.point_count;

      if (pointCount < 7) {
        // 展开显示真实设备
        this.expandCluster(clusterId, e.lngLat);
      } else {
        // 放大地图到下一级别
        this.zoomToCluster(clusterId);
      }
    };

    // 鼠标悬停效果
    const mouseEnterHandler = () => {
      this._map.getCanvas().style.cursor = 'pointer';
    };

    const mouseLeaveHandler = () => {
      this._map.getCanvas().style.cursor = '';
    };

    // 点击空白区域收缩聚合
    const mapClickHandler = (e: any) => {
      const features = this._map.queryRenderedFeatures(e.point);
      if (!features.length) {
        this.collapseAllClusters();
      }
    };

    // 绑定事件
    this._map.on('click', clusterLayerId, clusterClickHandler);
    this._map.on('mouseenter', clusterLayerId, mouseEnterHandler);
    this._map.on('mouseleave', clusterLayerId, mouseLeaveHandler);
    this._map.on('click', mapClickHandler);

    // 保存处理器引用用于清理
    this.clickHandlers.push(
      () => this._map.off('click', clusterLayerId, clusterClickHandler),
      () => this._map.off('mouseenter', clusterLayerId, mouseEnterHandler), 
      () => this._map.off('mouseleave', clusterLayerId, mouseLeaveHandler),
      () => this._map.off('click', mapClickHandler)
    );
  }

  /**
   * 展开聚合，显示真实设备位置
   */
  private expandCluster(clusterId: number, center: any) {
    if (this.clusterState.expandedClusters.has(clusterId)) {
      this.collapseCluster(clusterId);
      return;
    }

    const sourceId = `device_${this.options.icon}_source`;
    const source = this._map.getSource(sourceId) as any;
    
    if (!source || !source.getClusterLeaves) return;

    // 使用Promise方式获取聚合内的所有叶子节点
    const promise = source.getClusterLeaves(clusterId, 100, 0);
    
    promise.then((features: any[]) => {
      if (!features || features.length === 0) return;

      // 计算环形分布位置
      const positions = this.calculateCirclePositions(center, features.length);
      
      // 创建展开的设备图层
      this.createExpandedDevicesLayer(clusterId, features, positions);
      
      // 标记为已展开
      this.clusterState.expandedClusters.add(clusterId);
      this.clusterState.expandedDevices.set(clusterId, features);
      
    }).catch(() => {
      // 静默处理错误
    });
  }



  /**
   * 计算环形分布位置
   */
  private calculateCirclePositions(center: any, count: number): [number, number][] {
    const radius = 0.0006;
    const angleStep = (2 * Math.PI) / count;
    
    return Array.from({ length: count }, (_, i) => {
      const angle = i * angleStep;
      const offsetLng = radius * Math.cos(angle);
      const offsetLat = radius * Math.sin(angle);
      
      return [
        center.lng + offsetLng,
        center.lat + offsetLat
      ];
    });
  }

  /**
   * 创建展开的设备图层
   */
  private createExpandedDevicesLayer(clusterId: number, devices: any[], positions: [number, number][]) {
    const expandedLayerId = `${this._id}_expanded_${clusterId}`;
    const expandedSourceId = `${this._id}_expanded_source_${clusterId}`;

    // 创建展开后的GeoJSON数据
    const expandedGeojson: GeoJSON.FeatureCollection = {
      type: 'FeatureCollection' as const,
      features: devices.map((device, index) => ({
        type: 'Feature' as const,
        properties: device.properties || {},
        geometry: {
          type: 'Point' as const,
          coordinates: positions[index]
        }
      }))
    };

    // 添加数据源 - 明确禁用聚合
    this._map.addSource(expandedSourceId, {
      type: 'geojson',
      data: expandedGeojson,
      cluster: false  // 展开的设备不要聚合
    });

    // 创建设备图标匹配表达式
    const status = deviceStatus.map((item) => item.value);
    const matchStatus = status.flatMap((item) => [
      [item],
      `${this.options.icon}_${item}`,
    ]);
    const image = [
      'match',
      ['get', 'status'],
      ...matchStatus,
      `${this.options.icon}_${status[0]}`,
    ] as any;

    // 添加展开的设备图层
    this._map.addLayer({
      id: expandedLayerId,
      type: 'symbol',
      source: expandedSourceId,
      layout: {
        'icon-image': image,
        'icon-size': 0.3,
        'text-offset': [0, 0.5],
        'icon-offset': [0, -28],
        'text-anchor': 'top',
        'text-size': 16,
      },
      paint: {
        'text-color': '#2a71af',
        'icon-opacity': 1
      }
    });

    // 添加动画效果
    this.animateExpansion(expandedLayerId);

    // 保存图层ID
    this.clusterState.tempLayerIds.add(expandedLayerId);
  }

  /**
   * 添加展开动画效果
   */
  private animateExpansion(layerId: string) {
    this._map.setPaintProperty(layerId, 'icon-opacity', 0);
    setTimeout(() => {
      this._map.setPaintProperty(layerId, 'icon-opacity', 1);
    }, 100);
  }

  /**
   * 收缩特定聚合
   */
  private collapseCluster(clusterId: number) {
    if (!this.clusterState.expandedClusters.has(clusterId)) return;

    const expandedLayerId = `${this._id}_expanded_${clusterId}`;
    const expandedSourceId = `${this._id}_expanded_source_${clusterId}`;

    // 移除展开图层和数据源
    if (this._map.getLayer(expandedLayerId)) {
      this._map.removeLayer(expandedLayerId);
    }
    if (this._map.getSource(expandedSourceId)) {
      this._map.removeSource(expandedSourceId);
    }

    // 清理状态
    this.clusterState.expandedClusters.delete(clusterId);
    this.clusterState.expandedDevices.delete(clusterId);
    this.clusterState.tempLayerIds.delete(expandedLayerId);
  }

  /**
   * 收缩所有展开的聚合
   */
  private collapseAllClusters() {
    const expandedIds = Array.from(this.clusterState.expandedClusters);
    expandedIds.forEach(clusterId => {
      this.collapseCluster(clusterId);
    });
  }

  /**
   * 缩放到聚合
   */
  private zoomToCluster(clusterId: number) {
    const sourceId = `device_${this.options.icon}_source`;
    const source = this._map.getSource(sourceId) as any;
    
    if (source && source.getClusterExpansionZoom) {
      const zoomPromise = source.getClusterExpansionZoom(clusterId);
      
      zoomPromise.then((zoom: number) => {
        console.log(zoom);
        this._map.easeTo({
          zoom: zoom + 0.2,
          duration: 500
        });
      }).catch(() => {
        // 静默处理错误
      });
    }
  }

  protected showInternal(show: boolean): void {
    if (!this._map) return;

    // 控制聚合相关的所有子图层（聚合圆圈、数量标签、展开的设备图层等）
    // 主设备图层的显示隐藏由父类BaseLayer统一控制
    this.clusterState.tempLayerIds.forEach(layerId => {
      const clusterLayer = this._map.getLayer(layerId);
      if (clusterLayer) {
        this._map.setLayoutProperty(
          layerId,
          'visibility',
          show ? 'visible' : 'none'
        );
      }
    });

    // 调用父类的showInternal方法处理delegate
    super.showInternal(show);
  }

  removeInternal() {
    // 清理事件监听器
    this.clickHandlers.forEach(cleanup => cleanup());
    this.clickHandlers = [];

    // 收缩所有聚合
    this.collapseAllClusters();

    // 移除主图层
    if (this._map.getLayer(this._id)) {
      this._map.removeLayer(this._id);
    }

    // 移除聚合相关图层
    this.clusterState.tempLayerIds.forEach(layerId => {
      if (this._map.getLayer(layerId)) {
        this._map.removeLayer(layerId);
      }
    });

    // 移除数据源
    const sourceId = `device_${this.options.icon}_source`;
    if (this._map.getSource(sourceId)) {
      this._map.removeSource(sourceId);
    }

    // 清理状态
    this.clusterState.expandedClusters.clear();
    this.clusterState.expandedDevices.clear();
    this.clusterState.tempLayerIds.clear();
  }

  metersToPixelsAtMaxZoom(meters: number, latitude: number) {
    return meters / 0.075 / Math.cos((latitude * Math.PI) / 180);
  }
}


