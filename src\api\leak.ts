import axios from 'axios';
import localCache from '@/utils/auth';
//修改为物联网设备URL
const LEAK_URL = 'http://************:7080/lltech/api';
const WATER_NOTICE_URL = 'http://************:7088/revenue/api';
export interface LeakQueryVo {
  pipeLineName?: string;
  repairStatus?: number; //0:未保修，1:已报修，2:已修复
  listenTime_begin?: string;
  listenTime_end?: string;
  pageNo: number;
  pageSize: number;
}
/**
 * 漏水查询
 * @param data 查询条件
 * @returns
 */
export const queryLeakPage = (data: LeakQueryVo) => {
  const token = localCache.getCache('Latias') ?? '';
  if (!token) {
    ElMessage.error('没有找到token，请重新登录');
  }
  return axios.get(LEAK_URL + '/LeakageEventPageList', {
    params: data,
    headers: {
      'Grant-Type': 'client_auth',
      'Client-Id': 'fc72y28h5r3emhrvo8',
      'Client-Secret': 'z2oi87tgffpmuuayzt3c2ept8k8cssmcrbhz',
      Latias: token,
    },
  });
};

/**
 * 发送停水通知
 */
export const sendWaterStopNotifacation = (data: any) => {
  const token = localCache.getCache('Latias') ?? '';
  if (!token) {
    ElMessage.error('没有找到token，请重新登录');
  }
  return axios.get(WATER_NOTICE_URL + '/message/sendMessage', {
    headers: {
      'Grant-Type': 'client_auth',
      'Client-Id': 'gk534l642dcx8t9fi0',
      'Client-Secret': 'flsvpiw921maw0zsqkgsud0mfid8aimvb7qg',
      Latias: token,
    },
    params: data,
  });
};
