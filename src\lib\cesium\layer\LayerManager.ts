import { BehaviorSubject } from "rxjs";
import { BaseLayer } from "./layers/BaseLayer";
import { LayerGroup } from "./layers/LayerGroup";
import { type LayerItem } from "./type/LayerItem";

export class LayerManager {
  private _viewer: BC.Viewer;
  private _layers: any;
  layerChanged = new BehaviorSubject<LayerItem[]>([]);
  constructor(viewer: BC.Viewer) {
    this._viewer = viewer;
  }

  addLayers(config: any) {
    // 移除已加载图层
    this._layers && this._layers.remove();
    this._layers = new LayerGroup({
      children: config,
    });
    this._layers.addTo(this._viewer);
    // 发送图层更改事件
    this.layerChanged.next(this.getLayerTree());
  }
  /**
   * 根据图层查找id
   * @param layerId 图层id
   * @returns
   */
  findLayerById(layerId: string): BaseLayer | null {
    return this._layers.findLayer(layerId);
  }

  /**
   * 根据条件查找图层
   * @param predicate 图层查询条件表达式 
   * @returns 
   */
  find(predicate: (layer: BaseLayer) => boolean){
    return this._layers.findLayers(predicate);
  }


  /**
   * 定位到图层
   * @param layerId 图层id
   */
  flyToLayer(layerId: string) {
    const layer = this.findLayerById(layerId);
    layer && layer.flyTo();
  }

  /**
   * 获取图层树结构
   */
  getLayerTree() {
    const layerTree = this._layers.getLayerTree();
    return layerTree.children ?? [];
  }

  setLayerVisible(layerId: string, show: boolean) {
    const layer = this.findLayerById(layerId);
    if (layer) {
      layer.show = show;
    }
  }

  /**
   * @description 设置图层透明度
   * @param layerId 图层ID
   * @param alphaValue 透明度值 (0.0 - 1.0)
   */
  setLayerTransparency(layerId: string, alphaValue: number) {
    const layer = this.findLayerById(layerId);
    if (layer) {
      // 更新图层属性中的透明度值（转换为百分比）
      const transparencyPercent = Math.round(alphaValue * 100);
      (layer as any).transparency = alphaValue;

      // // 调用具体图层的透明度设置方法
      // if (typeof (layer as any).setTransparency === 'function') {
      //   try {
      //     const isTransparent = alphaValue < 1.0;
      //     (layer as any).setTransparency(isTransparent, alphaValue);
      //     console.log(`✅ 图层 ${layerId} 透明度设置成功: ${transparencyPercent}%`);
      //   } catch (error) {
      //     console.error(`❌ 图层 ${layerId} 透明度设置失败:`, error);
      //   }
      // } else {
      //   console.warn(`⚠️ 图层 ${layerId} 不支持透明度设置`);
      // }

      // 发送图层更改事件，更新UI
      this.layerChanged.next(this.getLayerTree());
    } else {
      console.warn(`⚠️ 未找到图层: ${layerId}`);
    }
  }


}
