{"code": "200", "data": [{"device_first_type_iot": {"iot_device_type_wqtm": [{"fieldKey": "device_code", "fieldName": "设备唯一标识", "displayForm": "text", "fieldValues": "SW001", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "device_name", "fieldName": "设备名称", "displayForm": "text", "fieldValues": "沙湾区政府水质监测站", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "first_device_type_code", "fieldName": "设备大类", "displayForm": "select", "fieldValues": "device_first_type_iot", "source": "device_first_type", "dictMap": {"device_first_type_iot": "iot设备", "device_first_type_trade": "行业设备", "device_first_type_remote_spreadsheet": "远传表"}, "isMultiple": 0}, {"fieldKey": "second_device_type_code", "fieldName": "设备小类", "displayForm": "select", "fieldValues": "iot_device_type_wqtm", "source": "iot_device_type", "dictMap": {"iot_device_type_wqtm": "水质检测仪", "iot_device_type_flow": "流量计", "iot_device_type_pressure": "压力传感器", "iot_device_type_level": "液位传感器"}, "isMultiple": 0}, {"fieldKey": "factory_code", "fieldName": "所属厂家", "displayForm": "select", "fieldValues": "device_factory_tjlx", "source": "device_factory", "dictMap": {"device_factory_tjlx": "天津蓝瑄", "device_factory_cqyy": "重庆怡源", "device_factory_hbzn": "河北智能", "device_factory_sclt": "四川联通"}, "isMultiple": 0}, {"fieldKey": "longitude", "fieldName": "经度", "displayForm": "text", "fieldValues": "103.551", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "latitude", "fieldName": "纬度", "displayForm": "text", "fieldValues": "29.413", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "valve_diameter_code", "fieldName": "阀门口径", "displayForm": "select", "fieldValues": "valve_diameter_100", "source": "valve_diameter", "dictMap": {"valve_diameter_100": "DN100", "valve_diameter_150": "DN150", "valve_diameter_200": "DN200", "valve_diameter_250": "DN250", "valve_diameter_300": "DN300"}, "isMultiple": 0}], "iot_device_type_flow": [{"fieldKey": "device_code", "fieldName": "设备唯一标识", "displayForm": "text", "fieldValues": "SW002", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "device_name", "fieldName": "设备名称", "displayForm": "text", "fieldValues": "太平镇供水流量计", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "longitude", "fieldName": "经度", "displayForm": "text", "fieldValues": "103.580", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "latitude", "fieldName": "纬度", "displayForm": "text", "fieldValues": "29.430", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "factory_code", "fieldName": "所属厂家", "displayForm": "select", "fieldValues": "device_factory_cqyy", "source": "device_factory", "dictMap": {"device_factory_tjlx": "天津蓝瑄", "device_factory_cqyy": "重庆怡源", "device_factory_hbzn": "河北智能", "device_factory_sclt": "四川联通"}, "isMultiple": 0}], "iot_device_type_pressure": [{"fieldKey": "device_code", "fieldName": "设备唯一标识", "displayForm": "text", "fieldValues": "SW003", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "device_name", "fieldName": "设备名称", "displayForm": "text", "fieldValues": "福禄镇压力监测点", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "longitude", "fieldName": "经度", "displayForm": "text", "fieldValues": "103.520", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "latitude", "fieldName": "纬度", "displayForm": "text", "fieldValues": "29.390", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "factory_code", "fieldName": "所属厂家", "displayForm": "select", "fieldValues": "device_factory_hbzn", "source": "device_factory", "dictMap": {"device_factory_tjlx": "天津蓝瑄", "device_factory_cqyy": "重庆怡源", "device_factory_hbzn": "河北智能", "device_factory_sclt": "四川联通"}, "isMultiple": 0}], "iot_device_type_level": [{"fieldKey": "device_code", "fieldName": "设备唯一标识", "displayForm": "text", "fieldValues": "SW004", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "device_name", "fieldName": "设备名称", "displayForm": "text", "fieldValues": "嘉农镇液位传感器", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "longitude", "fieldName": "经度", "displayForm": "text", "fieldValues": "103.560", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "latitude", "fieldName": "纬度", "displayForm": "text", "fieldValues": "29.450", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "factory_code", "fieldName": "所属厂家", "displayForm": "select", "fieldValues": "device_factory_sclt", "source": "device_factory", "dictMap": {"device_factory_tjlx": "天津蓝瑄", "device_factory_cqyy": "重庆怡源", "device_factory_hbzn": "河北智能", "device_factory_sclt": "四川联通"}, "isMultiple": 0}]}}, {"device_first_type_trade": {"trade_pipe_monitor": [{"fieldKey": "device_code", "fieldName": "设备唯一标识", "displayForm": "text", "fieldValues": "SW006", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "device_name", "fieldName": "设备名称", "displayForm": "text", "fieldValues": "沙湾大桥管网监测", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "longitude", "fieldName": "经度", "displayForm": "text", "fieldValues": "103.555", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "latitude", "fieldName": "纬度", "displayForm": "text", "fieldValues": "29.410", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "factory_code", "fieldName": "所属厂家", "displayForm": "select", "fieldValues": "device_factory_cqyy", "source": "device_factory", "dictMap": {"device_factory_tjlx": "天津蓝瑄", "device_factory_cqyy": "重庆怡源", "device_factory_hbzn": "河北智能", "device_factory_sclt": "四川联通"}, "isMultiple": 0}], "trade_valve_control": [{"fieldKey": "device_code", "fieldName": "设备唯一标识", "displayForm": "text", "fieldValues": "SW008", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "device_name", "fieldName": "设备名称", "displayForm": "text", "fieldValues": "沙湾工业园区阀门控制", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "longitude", "fieldName": "经度", "displayForm": "text", "fieldValues": "103.570", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "latitude", "fieldName": "纬度", "displayForm": "text", "fieldValues": "29.400", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "factory_code", "fieldName": "所属厂家", "displayForm": "select", "fieldValues": "device_factory_sclt", "source": "device_factory", "dictMap": {"device_factory_tjlx": "天津蓝瑄", "device_factory_cqyy": "重庆怡源", "device_factory_hbzn": "河北智能", "device_factory_sclt": "四川联通"}, "isMultiple": 0}]}}, {"device_first_type_remote_spreadsheet": {"remote_water_meter": [{"fieldKey": "device_code", "fieldName": "设备唯一标识", "displayForm": "text", "fieldValues": "SW005", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "device_name", "fieldName": "设备名称", "displayForm": "text", "fieldValues": "轸溪乡远传水表", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "longitude", "fieldName": "经度", "displayForm": "text", "fieldValues": "103.500", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "latitude", "fieldName": "纬度", "displayForm": "text", "fieldValues": "29.420", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "factory_code", "fieldName": "所属厂家", "displayForm": "select", "fieldValues": "device_factory_tjlx", "source": "device_factory", "dictMap": {"device_factory_tjlx": "天津蓝瑄", "device_factory_cqyy": "重庆怡源", "device_factory_hbzn": "河北智能", "device_factory_sclt": "四川联通"}, "isMultiple": 0}], "remote_gas_meter": [{"fieldKey": "device_code", "fieldName": "设备唯一标识", "displayForm": "text", "fieldValues": "SW010", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "device_name", "fieldName": "设备名称", "displayForm": "text", "fieldValues": "沙湾公园远传气表", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "longitude", "fieldName": "经度", "displayForm": "text", "fieldValues": "103.552", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "latitude", "fieldName": "纬度", "displayForm": "text", "fieldValues": "29.415", "source": null, "dictMap": null, "isMultiple": 0}, {"fieldKey": "factory_code", "fieldName": "所属厂家", "displayForm": "select", "fieldValues": "device_factory_cqyy", "source": "device_factory", "dictMap": {"device_factory_tjlx": "天津蓝瑄", "device_factory_cqyy": "重庆怡源", "device_factory_hbzn": "河北智能", "device_factory_sclt": "四川联通"}, "isMultiple": 0}]}}], "current": 1, "total": 10, "size": 10, "pages": 1}