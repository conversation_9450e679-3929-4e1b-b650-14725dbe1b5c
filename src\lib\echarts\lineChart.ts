/**
 * @fileoverview 柱状图配置
 * @description 提供柱状图的 ECharts 配置选项
 */

import type { ECOption } from './echarts';

/**
 * 柱状图数据接口
 */
export interface LineChartData {
  name: string;
  value: number;
}

/**
 * 生成柱状图配置
 * @param data 图表数据
 * @param title 图表标题
 * @returns ECharts 配置选项
 */
export function generateLineChartOption(data: LineChartData[], title?: string): ECOption {
  const xAxisData = data.map(item => item.name);
  const seriesData = data.map(item => item.value);

  return {
    title: title ? {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#333'
      }
    } : undefined,
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        const param = params[0];
        return `${param.name}<br/>${param.seriesName}: ${param.value}`;
      }
    },
    legend: {
      show: false
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: xAxisData,
      axisLabel: {
        rotate: xAxisData.length > 6 ? 45 : 0, // 当标签过多时旋转
        interval: 0,
        fontSize: 12,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      axisLabel: {
        fontSize: 12,
        color: '#666',
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#1890ff'
              },
              {
                offset: 1,
                color: '#40a9ff'
              }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        data: seriesData,
        emphasis: {
          focus: 'series',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#40a9ff'
                },
                {
                  offset: 1,
                  color: '#1890ff'
                }
              ]
            },
            shadowBlur: 10,
            shadowColor: 'rgba(24, 144, 255, 0.3)'
          }
        }
      }
    ]
  };
}
