<template>
  <div>
    <div class="round flex-c" @click="toggleExpanded">
      <img :src="getImages('tools/earth.png')" alt="" />
    </div>
    <div
      class="absolute bottom-4 right-25 pointer-events-auto"
      v-show="expanded"
    >
      <div class="bg-white rounded-lg shadow-lg p-4 min-w-[280px]">
        <!-- 标题 -->
        <!-- <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800">底图切换</h3>
        <button 
          @click="toggleExpanded"
          class="p-1 hover:bg-gray-100 rounded transition-colors"
        >
          <svg 
            :class="{ 'rotate-180': expanded }" 
            class="w-5 h-5 text-gray-600 transition-transform"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div> -->

        <!-- 底图选项 -->
        <div class="space-y-3">
          <!-- 天地图组 -->
          <div class="border-b border-gray-200 pb-3">
            <h4 class="text-sm font-medium text-#5C5F66 mb-2">天地图</h4>
            <div class="flex flex-wrap gap-2">
              <div
                v-for="tdtMap in tiandituMaps"
                :key="tdtMap.id"
                @click="switchBasemap(tdtMap)"
                class="group cursor-pointer rounded-0.5"
                border="1px solid  #BFD5FF"
                :class="[
                  'relative overflow-hidden',
                  currentBasemap?.id === tdtMap.id
                    ? 'border-blue-500'
                    : 'border-gray-200 hover:border-gray-300',
                ]"
              >
                <div
                  class="relative aspect-square bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center"
                >
                  <img
                    :src="getImages(tdtMap.thumbnail)"
                    :alt="tdtMap.name"
                    class="w-22.5 h-22.5"
                    @error="handleImageError"
                  />
                  <div
                    class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200"
                  ></div>
                  <div
                    class="absolute bottom-0 h-6 left-0 right-0 flex items-center justify-center bg-#1B3A80/50 color-#fff font-size-3.5"
                    :class="
                      currentBasemap?.id === tdtMap.id ? 'bg-#1966FF/50' : ''
                    "
                  >
                    {{ tdtMap.name }}
                  </div>
                </div>
                <div
                  v-if="currentBasemap?.id === tdtMap.id"
                  class="absolute top-0 right--0.25"
                >
                  <img
                    :src="getImages('basemap/select.png')"
                    alt=""
                    srcset=""
                  />
                </div>
              </div>
              <!-- <button
              v-for="tdtMap in tiandituMaps"
              :key="tdtMap.id"
              @click="switchBasemap(tdtMap)"
              :class="[
                'relative overflow-hidden rounded-lg border-2 transition-all duration-200',
                currentBasemap?.id === tdtMap.id 
                  ? 'border-blue-500 ring-2 ring-blue-200' 
                  : 'border-gray-200 hover:border-gray-300'
              ]"
              class="group"
            >
              <div class="aspect-square bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
                <img 
                  :src="getImages(tdtMap.thumbnail)" 
                  :alt="tdtMap.name"
                  class="w-20 h-20 object-cover"
                  @error="handleImageError"
                />
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200"></div>
              </div>
              <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
                <span class="text-xs text-white font-medium">{{ tdtMap.name }}</span>
              </div>
            选中状态指示器 -->
              <!-- <div 
                v-if="currentBasemap?.id === tdtMap.id"
                class="absolute top-1 right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
              >
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            </button> -->
            </div>
          </div>

          <!-- 百度地图组 -->
          <div class="border-b border-gray-200 pb-3">
            <h4 class="text-sm font-medium text-#5C5F66 mb-2">百度地图</h4>
            <div class="flex flex-wrap gap-2">
              <!-- <button
              v-for="baiduMap in baiduMaps"
              :key="baiduMap.id"
              @click="switchBasemap(baiduMap)"
              :class="[
                'relative overflow-hidden rounded-lg border-2 transition-all duration-200',
                currentBasemap?.id === baiduMap.id
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300',
              ]"
              class="group"
            >
              <div
                class="aspect-square bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center"
              >
                <img
                  :src="getImages(baiduMap.thumbnail)"
                  :alt="baiduMap.name"
                  class="w-20 h-20 object-cover"
                  @error="handleImageError"
                />
                <div
                  class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200"
                ></div>
              </div>
              <div
                class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2"
              >
                <span class="text-xs text-white font-medium">{{
                  baiduMap.name
                }}</span>
              </div>
              <div
                v-if="currentBasemap?.id === baiduMap.id"
                class="absolute top-1 right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
              >
                <svg
                  class="w-3 h-3 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </button> -->
              <div
                v-for="baiduMap in baiduMaps"
                :key="baiduMap.id"
                @click="switchBasemap(baiduMap)"
                class="group cursor-pointer rounded-0.5"
                border="1px solid  #BFD5FF"
                :class="[
                  'relative overflow-hidden',
                  currentBasemap?.id === baiduMap.id
                    ? 'border-blue-500'
                    : 'border-gray-200 hover:border-gray-300',
                ]"
              >
                <div
                  class="relative aspect-square bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center"
                >
                  <img
                    :src="getImages(baiduMap.thumbnail)"
                    :alt="baiduMap.name"
                    class="w-22.5 h-full"
                    @error="handleImageError"
                  />
                  <div
                    class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200"
                  ></div>
                  <div
                    class="absolute bottom-0 h-6 left-0 right-0 flex items-center justify-center bg-#1B3A80/50 color-#fff font-size-3.5"
                    :class="
                      currentBasemap?.id === baiduMap.id ? 'bg-#1966FF/50' : ''
                    "
                  >
                    {{ baiduMap.name }}
                  </div>
                </div>
                <div
                  v-if="currentBasemap?.id === baiduMap.id"
                  class="absolute top-0 right--0.25"
                >
                  <img
                    :src="getImages('basemap/select.png')"
                    alt=""
                    srcset=""
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 高德地图组 -->
          <div>
            <h4 class="text-sm font-medium text-#5C5F66 mb-2">高德地图</h4>
            <div class="flex flex-wrap gap-2">
              <!-- <button
              v-for="amapMap in amapMaps"
              :key="amapMap.id"
              @click="switchBasemap(amapMap)"
              :class="[
                'relative overflow-hidden rounded-lg border-2 transition-all duration-200',
                currentBasemap?.id === amapMap.id
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300',
              ]"
              class="group"
            >
              <div
                class="aspect-square bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center"
              >
                <img
                  :src="getImages(amapMap.thumbnail)"
                  :alt="amapMap.name"
                  class="w-20 h-20 object-cover"
                  @error="handleImageError"
                />
                <div
                  class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200"
                ></div>
              </div>
              <div
                class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2"
              >
                <span class="text-xs text-white font-medium">{{
                  amapMap.name
                }}</span>
              </div>
              <div
                v-if="currentBasemap?.id === amapMap.id"
                class="absolute top-1 right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
              >
                <svg
                  class="w-3 h-3 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </button> -->
              <div
                v-for="amapMap in amapMaps"
                :key="amapMap.id"
                @click="switchBasemap(amapMap)"
                class="group cursor-pointer rounded-0.5"
                border="1px solid  #BFD5FF"
                :class="[
                  'relative overflow-hidden',
                  currentBasemap?.id === amapMap.id
                    ? 'border-blue-500'
                    : 'border-gray-200 hover:border-gray-300',
                ]"
              >
                <div
                  class="relative aspect-square bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center"
                >
                  <img
                    :src="getImages(amapMap.thumbnail)"
                    :alt="amapMap.name"
                    class="w-22.5 h-22.5"
                    @error="handleImageError"
                  />
                  <div
                    class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200"
                  ></div>
                  <div
                    class="absolute bottom-0 h-6 left-0 right-0 flex items-center justify-center bg-#1B3A80/50 color-#fff font-size-3.5"
                    :class="
                      currentBasemap?.id === amapMap.id ? 'bg-#1966FF/50' : ''
                    "
                  >
                    {{ amapMap.name }}
                  </div>
                </div>
                <div
                  v-if="currentBasemap?.id === amapMap.id"
                  class="absolute top-0 right--0.25"
                >
                  <img
                    :src="getImages('basemap/select.png')"
                    alt=""
                    srcset=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载状态和错误信息 -->
        <div
          v-if="loading"
          class="mt-3 text-sm text-blue-600 flex items-center"
        >
          <svg
            class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          正在切换底图...
        </div>

        <div v-if="error" class="mt-3 text-sm text-red-600">
          {{ error }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { getImages } from "@/utils/getImages";
import type { BasemapConfig } from "@/lib/maplibre/basemap/BaseLayerManager";

// Props
interface Props {
  tiandituToken?: string;
}

const props = withDefaults(defineProps<Props>(), {
  tiandituToken: "your-tianditu-token",
});

// 响应式数据
const expanded = ref(false);
const loading = ref(false);
const error = ref("");
const currentBasemap = ref<BasemapConfig | null>(null);

// 底图配置 - 从AppMaplibre获取
const tiandituMaps = ref<BasemapConfig[]>([]);
const baiduMaps = ref<BasemapConfig[]>([]);
const amapMaps = ref<BasemapConfig[]>([]);

/**
 * @description 初始化底图列表
 */
const initializeBasemapLists = () => {
  try {
    const baseLayerManager = AppMaplibre.getBaseLayerManager();

    // 获取底图配置
    tiandituMaps.value = baseLayerManager.tiandituMaps;
    baiduMaps.value = baseLayerManager.baiduMaps;
    amapMaps.value = baseLayerManager.amapMaps;

    // 获取当前底图状态
    currentBasemap.value = baseLayerManager.getCurrentBasemap();

    console.log("底图列表初始化成功");
  } catch (error) {
    console.warn("底图列表初始化失败，基础图层管理器可能尚未就绪:", error);
    // 延迟重试
    setTimeout(() => {
      initializeBasemapLists();
    }, 1000);
  }
};

/**
 * @description 切换底图
 * @param basemap - 底图配置
 */
const switchBasemap = async (basemap: BasemapConfig) => {
  if (currentBasemap.value?.id === basemap.id) {
    return; // 已经是当前底图
  }

  try {
    loading.value = true;
    error.value = "";

    console.log("切换底图:", basemap.name);

    // 使用AppMaplibre的底图切换功能
    await AppMaplibre.switchBasemap(basemap.id);

    // 更新当前底图状态
    currentBasemap.value = basemap;

    console.log("底图切换成功:", basemap.name);
  } catch (err: any) {
    loading.value = false;
    error.value = `切换底图失败: ${err.message || "未知错误"}`;
    console.error("切换底图失败:", err);
  } finally {
    loading.value = false;
  }
};

/**
 * @description 切换展开状态
 */
const toggleExpanded = () => {
  expanded.value = !expanded.value;
};

/**
 * @description 处理图片加载错误
 * @param event - 错误事件
 */
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  // 使用默认占位图
  img.src =
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zNSA0MEw1MCA1NUw2NSA0MEw3NSA1MFY3MEgzNVY0MFoiIGZpbGw9IiNEMUQ1REIiLz4KPGNpcmNsZSBjeD0iNDIiIGN5PSI0NyIgcj0iMyIgZmlsbD0iI0QxRDVEQiIvPgo8L3N2Zz4K";
};

/**
 * @description 监听基础图层管理器状态变化
 */
const watchBaseLayerManagerChanges = () => {
  // 定期检查基础图层管理器的状态
  const checkInterval = setInterval(() => {
    try {
      const newCurrentBasemap = AppMaplibre.getCurrentBasemap();
      if (
        newCurrentBasemap &&
        newCurrentBasemap.id !== currentBasemap.value?.id
      ) {
        currentBasemap.value = newCurrentBasemap;
        console.log("检测到底图状态变化:", newCurrentBasemap.name);
      }
    } catch (error) {
      // 忽略错误，继续检查
    }
  }, 2000); // 每2秒检查一次

  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(checkInterval);
  });
};

// 组件挂载时初始化
onMounted(() => {
  console.log("BaseMapToggle组件挂载，开始初始化...");

  // 初始化底图列表
  initializeBasemapLists();

  // 启动状态监听
  watchBaseLayerManagerChanges();
});

// 暴露给父组件的方法
defineExpose({
  switchBasemap,
  getCurrentBasemap: () => currentBasemap.value,
  reinitialize: initializeBasemapLists,
});
</script>

<style scoped lang="scss">
.round {
  width: 60px;
  height: 60px;
  border-radius: 2px;
  box-shadow: 2px 0px 12px 0px rgba(196, 216, 255, 0.4);
  background: rgb(255, 255, 255);
  position: absolute;
  bottom: 16px;
  right: 16px;
}
/* 自定义滚动条样式 */
.space-y-3::-webkit-scrollbar {
  width: 4px;
}

.space-y-3::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.space-y-3::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.space-y-3::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 按钮悬停效果 */
.group:hover .absolute.inset-0 {
  backdrop-filter: blur(1px);
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .min-w-\[280px\] {
    min-width: 240px;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.375rem;
  }
}
</style>
