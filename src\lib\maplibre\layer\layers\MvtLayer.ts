import { BaseLayer } from './BaseLayer'
import { Map as glMap } from 'maplibre-gl'

/**
 * @class MvtLayer
 * @description MVT矢量瓦片图层，支持管线注记功能
 */
export class MvtLayer extends BaseLayer {
  /** 注记图层ID */
  private _labelLayerId?: string
  /** 注记图层是否显示 */
  private _labelVisible: boolean = true
  /** 是否为管线图层 */
  private _isPipelineLayer: boolean = false

  constructor(options: any) {
    super(options)
    // 检测是否为管线图层
    this._isPipelineLayer = this.checkIsPipelineLayer()
    if (this._isPipelineLayer) {
      this._labelLayerId = `${this.id}_label`
    }
  }

  /**
   * @description 检测是否为管线图层
   * @returns 是否为管线图层
   * @private
   */
  private checkIsPipelineLayer(): boolean {
    return this.name.includes('管线') ||
           this.name.includes('管道') ||
           this.name.includes('pipeline') ||
           this.name.includes('pipe')
  }
  async addToInternal(map: glMap) {
    const { url, ...options } = this.options.options

    // 添加数据源
    map.addSource(this.id, {
      type: 'vector',
      tiles: [url]
    })

    // 特殊图层的颜色配置
    if(this.name === '城乡供水一体化项目首期配水工程') {
      // 按照color颜色进行分类展示
      // options.filter = ['==', ['get', 'color'], '#2b78fe']
      console.log(options);
      options.paint['line-color'] = [
        'match',
        ['get', 'color'],
        '#ff0000', '#ff0000', // 红色
        '#00ffff', '#00ffff', // 青色
        /* default */ '#78F6FF'
      ]
    }

    // 添加主图层
    const layer = {
      id: this.id,
      source: this.id,
      ...options
    }
    map.addLayer(layer)

    // 如果是管线图层，添加注记图层
    if (this._isPipelineLayer && this._labelLayerId) {
      this.createLabelLayer(map)
    }

    const delegate = layer
    return delegate
  }

  /**
   * @description 创建管线注记图层
   * @param map - MapLibre地图实例
   * @private
   */
  private createLabelLayer(map: glMap): void {
    if (!this._labelLayerId) return

    // 构建注记文本表达式，优先使用不同的字段名
    const textExpression = [
      'case',
      // 如果有gj和cz字段（管径和材质）
      ['all', ['has', 'gj'], ['has', 'cz']],
      ['concat', ['to-string', ['get', 'gj']], 'mm\n', ['get', 'cz']],

      // 如果有diameter和material字段
      ['all', ['has', 'diameter'], ['has', 'material']],
      ['concat', ['to-string', ['get', 'diameter']], 'mm\n', ['get', 'material']],

      // 如果有pipe_diameter和pipe_material字段
      ['all', ['has', 'pipe_diameter'], ['has', 'pipe_material']],
      ['concat', ['to-string', ['get', 'pipe_diameter']], 'mm\n', ['get', 'pipe_material']],

      // 如果只有管径信息
      ['has', 'gj'],
      ['concat', ['to-string', ['get', 'gj']], 'mm'],

      ['has', 'diameter'],
      ['concat', ['to-string', ['get', 'diameter']], 'mm'],

      ['has', 'pipe_diameter'],
      ['concat', ['to-string', ['get', 'pipe_diameter']], 'mm'],

      // 如果只有材质信息
      ['has', 'cz'],
      ['get', 'cz'],

      ['has', 'material'],
      ['get', 'material'],

      ['has', 'pipe_material'],
      ['get', 'pipe_material'],

      // 默认显示
      ''
    ]

    const labelLayer = {
      id: this._labelLayerId,
      type: 'symbol' as const,
      source: this.id,
      'source-layer': 'line',
      minzoom: 18, // 仅在缩放级别≥18时显示
      layout: {
        'text-field': textExpression,
        'text-font': ['Open Sans Regular', 'Arial Unicode MS Regular'],
        'text-size': 12,
        'text-anchor': 'center' as const,
        'text-offset': [0, 0] as [number, number],
        'text-allow-overlap': false,
        'text-ignore-placement': false,
        'symbol-placement': 'line' as const,
        'text-rotation-alignment': 'map' as const,
        'text-pitch-alignment': 'viewport' as const,
        'visibility': (this._labelVisible && this.show ? 'visible' : 'none') as 'visible' | 'none'
      }, 
      paint: {
        'text-color': '#2C3037',
        'text-halo-color': '#FFFFFF',
        'text-halo-width': 1,
        'text-opacity': 0.9
      }
    }
    map.addLayer(labelLayer as any)
  }
  removeInternal() {
    // 移除注记图层
    if (this._labelLayerId && this._map.getLayer(this._labelLayerId)) {
      this._map.removeLayer(this._labelLayerId)
    }

    // 移除主图层
    if(this._map.getLayer(this._id)) {
      this._map.removeLayer(this._id)
    }

    // 移除数据源
    if(this._map.getSource(this._id)) {
      this._map.removeSource(this._id)
    }
  }

  async refresh() {
    // 移除注记图层
    if (this._labelLayerId && this._map.getLayer(this._labelLayerId)) {
      this._map.removeLayer(this._labelLayerId);
    }

    // 移除主图层
    if(this._map.getLayer(this._id)) {
      this._map.removeLayer(this._id);
    }

    // 移除数据源
    if(this._map.getSource(this._id)) {
      this._map.removeSource(this._id);
    }

    // 重新添加图层
    setTimeout(async () => {
      await this.addToInternal(this._map);
    }, 500)
  }

  /**
   * @description 重写showInternal方法，控制注记图层的显示
   * @param show - 是否显示
   * @protected
   */
  protected showInternal(show: boolean): void {
    super.showInternal(show)

    // 控制注记图层的显示
    if (this._labelLayerId && this._map && this._map.getLayer(this._labelLayerId)) {
      const visibility = (show && this._labelVisible) ? 'visible' : 'none'
      this._map.setLayoutProperty(this._labelLayerId, 'visibility', visibility)
    }
  }

  /**
   * @description 获取注记图层显示状态
   * @returns 注记图层是否显示
   */
  get labelVisible(): boolean {
    return this._labelVisible
  }

  /**
   * @description 设置注记图层显示状态
   * @param visible - 是否显示注记图层
   */
  set labelVisible(visible: boolean) {
    this._labelVisible = visible

    // 更新注记图层的显示状态
    if (this._labelLayerId && this._map && this._map.getLayer(this._labelLayerId)) {
      const visibility = (visible && this.show) ? 'visible' : 'none'
      this._map.setLayoutProperty(this._labelLayerId, 'visibility', visibility)
    }
  }

  /**
   * @description 切换注记图层显示状态
   * @returns 切换后的显示状态
   */
  toggleLabel(): boolean {
    this.labelVisible = !this._labelVisible
    return this._labelVisible
  }

  /**
   * @description 获取是否为管线图层
   * @returns 是否为管线图层
   */
  get isPipelineLayer(): boolean {
    return this._isPipelineLayer
  }

  /**
   * @description 获取注记图层ID
   * @returns 注记图层ID
   */
  get labelLayerId(): string | undefined {
    return this._labelLayerId
  }
}
