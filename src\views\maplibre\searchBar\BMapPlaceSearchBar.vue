<!--
 * @Description: 百度地图地名地址搜索组件
 * @Date: 2024-01-16 16:00:00
 * @Author: 项目开发团队
 * @LastEditTime: 2024-01-16 16:00:00
 * @Version: 1.0.0 - 百度地图版本
-->
<template>
  <div class="bmap-place-search-container">
    <div class="search-box">
      <el-input
        v-model="searchText"
        placeholder="搜索地名、地址、兴趣点..."
        clearable
        @input="handleInput"
        @keyup.enter="handleSearch"
        @clear="handleClear"
        @focus="handleFocus"
        @blur="handleBlur"
        class="search-input"
        :class="{ focused: isFocused }"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #append>
          <el-button
            class="search-btn"
            @click="handleSearch"
            :loading="isSearching"
            type="primary"
          >
            搜索
          </el-button>
        </template>
      </el-input>

      <!-- 搜索建议下拉列表 -->
      <div
        v-if="showSuggestions && (suggestions.length > 0 || isSearching)"
        class="suggestions-dropdown"
      >
        <!-- 加载提示 -->
        <div v-if="isSearching" class="loading-item">
          <el-icon class="loading-icon"><Loading /></el-icon>
          正在搜索...
        </div>

        <!-- 搜索建议列表 -->
        <div
          v-for="(item, index) in suggestions"
          :key="index"
          class="suggestion-item"
          @click="selectSuggestion(item)"
          @mouseenter="highlightIndex = index"
          :class="{ highlighted: highlightIndex === index }"
        >
          <el-icon class="item-icon">
            <LocationInformation v-if="item.type === 'poi'" />
            <Location v-else />
          </el-icon>
          <div class="item-content">
            <div
              class="item-name"
              v-html="highlightSearchText(item.name)"
            ></div>
            <div class="item-address">{{ item.address }}</div>
          </div>
        </div>

        <!-- 无结果提示 -->
        <div v-if="!isSearching && suggestions.length === 0" class="no-results">
          <el-icon><WarningFilled /></el-icon>
          未找到相关地点
        </div>
      </div>
    </div>

    <!-- 隐藏的百度地图容器 -->
    <div id="bmap-container" style="width: 0; height: 0;"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { ElMessage, ElNotification } from "element-plus";
import {
  Search,
  Location,
  LocationInformation,
  Loading,
  WarningFilled,
} from "@element-plus/icons-vue";
import type { Map } from "maplibre-gl";
import { loadBMap } from './loadMap';
import { CoordinateTransform, type CoordinatePoint } from '@/utils/coordinate/CoordinateTransform';

/**
 * @description 百度地图搜索API配置
 * @note 百度地图使用BD09坐标系，需要转换为WGS84坐标系才能在MapLibre地图上正确显示
 */
const BAIDU_API_CONFIG = {
  ak: 'nSxiPohfziUaCuONe4ViUP2N',
  region: '乐山市', // 限制搜索区域
  cityLimit: true
};

/**
 * @description 搜索结果项接口
 */
interface SearchResultItem {
  name: string; // 地点名称
  address: string; // 详细地址
  location: {
    // 经纬度坐标
    lng: number;
    lat: number;
  };
  type: "poi" | "geo"; // 类型：兴趣点或地理位置
  uid?: string; // 百度POI唯一标识
}

/**
 * @description 组件状态管理
 */
const searchText = ref("");
const suggestions = ref<SearchResultItem[]>([]);
const isSearching = ref(false);
const isFocused = ref(false);
const showSuggestions = ref(false);
const highlightIndex = ref(-1);
const mapInstance = ref<Map | null>(null);

// 百度地图相关
const BMapGL = ref<any>(null);
const BMapInstance = ref<any>(null);
const localSearch = ref<any>(null);

/**
 * @description 防抖定时器
 */
let debounceTimer: number | null = null;

/**
 * @description 处理输入事件
 */
const handleInput = () => {
  // 清除之前的防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  if (!searchText.value.trim()) {
    showSuggestions.value = false;
    return;
  }

  // 500ms防抖
  debounceTimer = setTimeout(() => {
    performSuggestionSearch();
  }, 500);
};

/**
 * @description 处理输入框获得焦点
 */
const handleFocus = () => {
  isFocused.value = true;
  if (searchText.value.trim()) {
    showSuggestions.value = suggestions.value.length > 0;
  }
};

/**
 * @description 处理输入框失去焦点
 */
const handleBlur = () => {
  // 延迟隐藏，以便点击建议项时能正常触发
  setTimeout(() => {
    isFocused.value = false;
    showSuggestions.value = false;
  }, 200);
};

/**
 * @description 执行搜索建议
 */
const performSuggestionSearch = async () => {
  if (!searchText.value.trim() || !BMapGL.value) return;

  try {
    isSearching.value = true;
    showSuggestions.value = true;

    const results = await searchPlaces(searchText.value, "suggestion");
    suggestions.value = results.slice(0, 10); // 限制建议数量为6条
    highlightIndex.value = -1;
  } catch (error) {
    console.error("搜索建议失败:", error);
    suggestions.value = [];
  } finally {
    isSearching.value = false;
  }
};

/**
 * @description 处理搜索操作
 */
const handleSearch = async () => {
  if (!searchText.value.trim()) {
    ElMessage.warning("请输入搜索关键词");
    return;
  }

  try {
    isSearching.value = true;
    showSuggestions.value = true;

    console.log("🔍 [BMapPlaceSearch] 执行搜索:", searchText.value);

    const results = await searchPlaces(searchText.value, "search");
    suggestions.value = results;

    if (results.length === 0) {
      ElMessage.info("未找到相关地点，请尝试其他关键词");
    } else if (results.length === 1) {
      // 只有一个结果时自动选择
      selectSuggestion(results[0]);
    }
  } catch (error) {
    console.error("搜索失败:", error);
    ElMessage.error("搜索失败，请稍后重试");
    suggestions.value = [];
  } finally {
    isSearching.value = false;
  }
};

/**
 * @description 使用百度地图API搜索地点
 */
const searchPlaces = async (
  keyword: string,
  type: "suggestion" | "search"
): Promise<SearchResultItem[]> => {
  return new Promise((resolve, reject) => {
    if (!BMapGL.value || !localSearch.value) {
      reject(new Error('百度地图未初始化'));
      return;
    }

    console.log(`🌍 [BMapPlaceSearch] 搜索: ${keyword}, type: ${type}`);

    // 创建新的搜索实例，避免回调冲突
    const search = new BMapGL.value.LocalSearch(BMapInstance.value, {
      onSearchComplete: (results: any) => {
        try {
          const searchResults: SearchResultItem[] = [];
          
          if (results && results.getNumPois && results.getNumPois() > 0) {
            // const maxResults = type === "suggestion" ? 10 : 30;
            const numResults = Math.min(results.getNumPois(), 10);

            for (let i = 0; i < numResults; i++) {
              const poi = results.getPoi(i);
              if (poi) {
                // 百度地图返回的是BD09坐标，需要转换为WGS84坐标
                const bd09Point: CoordinatePoint = {
                  lng: poi.point.lng,
                  lat: poi.point.lat
                };

                // 转换为WGS84坐标系
                const wgs84Point = CoordinateTransform.bd09ToWgs84(bd09Point);

                console.log(`🔄 [坐标转换] BD09(${bd09Point.lng}, ${bd09Point.lat}) -> WGS84(${wgs84Point.lng}, ${wgs84Point.lat})`);

                searchResults.push({
                  name: poi.title || '',
                  address: poi.address || '',
                  location: {
                    lng: wgs84Point.lng,
                    lat: wgs84Point.lat,
                  },
                  type: poi.type === 0 ? 'poi' : 'geo',
                  uid: poi.uid || '',
                });
              }
            }
          }

          console.log('🔍 [BMapPlaceSearch] 搜索结果:', searchResults);
          resolve(searchResults);
        } catch (error) {
          console.error('处理搜索结果失败:', error);
          resolve([]);
        }
      },
      onSearchError: (error: any) => {
        console.error('百度地图搜索失败:', error);
        reject(new Error('搜索失败'));
      }
    });

    // 设置搜索区域（如果配置了region）
    if (BAIDU_API_CONFIG.region) {
      search.searchInBounds(keyword, BMapInstance.value.getBounds());
    } else {
      search.search(keyword);
    }
  });
};

/**
 * @description 选择搜索建议
 */
const selectSuggestion = async (item: SearchResultItem) => {
  try {
    console.log("📍 [BMapPlaceSearch] 选择地点:", item);
    console.log(`🗺️ [坐标系统] 使用WGS84坐标进行地图定位: (${item.location.lng}, ${item.location.lat})`);

    // 更新搜索框文本
    searchText.value = item.name;

    // 隐藏下拉列表
    showSuggestions.value = false;

    // 执行地图定位（使用已转换的WGS84坐标）
    await performMapLocation(item.location.lng, item.location.lat);

    // 显示成功提示
    ElNotification({
      title: "定位成功",
      message: `已定位到：${item.name}`,
      type: "success",
      duration: 2000,
      position: "top-right",
    });
  } catch (error) {
    console.error("选择地点失败:", error);
    ElMessage.error("定位失败，请重试");
  }
};

/**
 * @description 执行地图定位跳转
 */
const performMapLocation = async (longitude: number, latitude: number) => {
  if (!mapInstance.value) {
    throw new Error("地图实例未初始化");
  }

  console.log("🗺️ [BMapPlaceSearch] 地图定位:", { longitude, latitude });

  // 使用flyTo方法平滑跳转到目标位置
  mapInstance.value.flyTo({
    center: [longitude, latitude],
    zoom: 18,
    duration: 2000,
    essential: true,
  });
};

/**
 * @description 处理清除操作
 */
const handleClear = () => {
  searchText.value = "";
  suggestions.value = [];
  showSuggestions.value = false;
};

/**
 * @description 高亮搜索关键词
 */
const highlightSearchText = (text: string): string => {
  if (!searchText.value.trim()) return text;

  const keyword = searchText.value.trim();
  const regex = new RegExp(`(${keyword})`, "gi");
  return text.replace(regex, '<span class="highlight">$1</span>');
};

/**
 * @description 处理键盘事件
 */
const handleKeydown = (event: KeyboardEvent) => {
  if (!showSuggestions.value || suggestions.value.length === 0) return;

  switch (event.key) {
    case "ArrowDown":
      event.preventDefault();
      highlightIndex.value = Math.min(
        highlightIndex.value + 1,
        suggestions.value.length - 1
      );
      break;
    case "ArrowUp":
      event.preventDefault();
      highlightIndex.value = Math.max(highlightIndex.value - 1, -1);
      break;
    case "Enter":
      event.preventDefault();
      if (
        highlightIndex.value >= 0 &&
        highlightIndex.value < suggestions.value.length
      ) {
        selectSuggestion(suggestions.value[highlightIndex.value]);
      }
      break;
    case "Escape":
      showSuggestions.value = false;
      break;
  }
};

/**
 * @description 处理点击外部事件
 */
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  const container = target.closest(".bmap-place-search-container");

  if (!container) {
    showSuggestions.value = false;
    isFocused.value = false;
  }
};




/**
 * @description 组件挂载后初始化
 */
onMounted(async () => {
  

  // 延迟获取MapLibre地图实例
  nextTick(() => {
    setTimeout(async () => {
      try {
        mapInstance.value = AppMaplibre.getMap();
        console.log("✅ [BMapPlaceSearch] 已连接到MapLibre地图实例");
        // 初始化百度地图
        await initBaiduMap();
      } catch (error) {
        console.error("❌ [BMapPlaceSearch] 无法获取MapLibre地图实例:", error);
      }
    }, 500);
  });

  // 添加键盘事件监听
  document.addEventListener("keydown", handleKeydown);

  // 添加点击外部关闭下拉列表的监听
  document.addEventListener("click", handleClickOutside);
});

/**
 * @description 初始化百度地图
 */
const initBaiduMap = async () => {
  try {
    console.log('🗺️ [BMapPlaceSearch] 开始加载百度地图...');

    // 加载百度地图API
    await loadBMap(BAIDU_API_CONFIG.ak);
    BMapGL.value = (window as any).BMapGL;
    // 创建地图实例（隐藏容器）
    BMapInstance.value = new BMapGL.value.Map('bmap-container');

    // 设置地图中心点为乐山市
    // const point = new BMapGL.value.Point(103.761263, 29.582024); // 乐山市中心坐标
    // BMapInstance.value.centerAndZoom(point, 12);

    // 初始化本地搜索
    localSearch.value = new BMapGL.value.LocalSearch(BMapInstance.value, {
      renderOptions: { map: BMapInstance.value },
      pageCapacity: 10
    });

    // // 设置搜索区域为乐山市
    // if (BAIDU_API_CONFIG.region) {
    //   localSearch.value.setLocation(BAIDU_API_CONFIG.region);
    // }

    console.log('✅ [BMapPlaceSearch] 百度地图初始化成功');
  } catch (error) {
    console.error('❌ [BMapPlaceSearch] 百度地图初始化失败:', error);
    ElMessage.error('地图服务初始化失败');
  }
};

/**
 * @description 组件卸载前清理
 */
onUnmounted(() => {
  // 清理定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  // 移除事件监听器
  document.removeEventListener("keydown", handleKeydown);
  document.removeEventListener("click", handleClickOutside);

  // 清理百度地图实例
  if (BMapInstance.value) {
    BMapInstance.value = null;
  }
});
</script>

<style scoped lang="scss">
.bmap-place-search-container {
  position: relative;
  width: 380px;
  z-index: 1;
}

.search-box {
  position: relative;
}

.search-input {
  width: 100%;

  &.focused {
    :deep(.el-input__wrapper) {
      border-color: #409eff;
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }

  :deep(.el-input__wrapper) {
    border-radius: 8px 0 0 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
    }
  }

  :deep(.el-input__prefix) {
    color: #909399;
  }

  :deep(.el-input-group__append) {
    border-radius: 0 6px 6px 0;
    border-left: none;

    .search-btn {
      border: none;
      border-radius: 0 6px 6px 0;
    }
  }
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  max-height: 320px;
  overflow-y: auto;
  z-index: 1;
  margin-top: 4px;
}

.loading-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #909399;
  font-size: 14px;

  .loading-icon {
    margin-right: 8px;
    animation: rotate 1s linear infinite;
  }
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f5f7fa;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover,
  &.highlighted {
    background-color: #f5f7fa;
  }

  .item-icon {
    margin-right: 12px;
    color: #409eff;
    flex-shrink: 0;
  }

  .item-content {
    flex: 1;
    min-width: 0;

    .item-name {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
      word-break: break-all;

      :deep(.highlight) {
        color: #409eff;
        font-weight: 600;
        background-color: rgba(64, 158, 255, 0.1);
        padding: 0 2px;
        border-radius: 2px;
      }
    }

    .item-address {
      font-size: 12px;
      color: #909399;
      word-break: break-all;
    }
  }
}

.no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  color: #909399;
  font-size: 14px;

  .el-icon {
    margin-right: 8px;
  }
}

/* 滚动条样式 */
.suggestions-dropdown::-webkit-scrollbar {
  width: 6px;
}

.suggestions-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.suggestions-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

/* 动画效果 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bmap-place-search-container {
    width: 100%;
    max-width: 300px;
  }
}
</style>