import { BaseLayer } from './BaseLayer'
import { Map as glMap } from 'maplibre-gl'
export class ImageryLayer extends BaseLayer {
  constructor(options: any) {
    super(options)
  }
  async addToInternal(map: glMap) {
    const imageOptions: any = this.options.options;
    console.log(this.id)
    console.log(imageOptions);
    const url = imageOptions.url.replaceAll('{TileMatrixSet}', imageOptions.tileMatrixSetID).replace('{style}', imageOptions.style).replace('TileMatrix', 'z').replace('TileRow', 'y').replace('TileCol', 'x');
    this._map.addSource(this.id, {
      type: 'raster',
      tiles: [
        url
      ],
      tileSize: 256
    })

    this._map.addLayer({
      id: this.id,
      type: 'raster',
      source: this.id
    })
    if(this._name.indexOf('影像') !== -1) {
      this.show = false;
    }
    
    let delegate = this._map.getLayer(this.id)
    return delegate
  }

  removeInternal() {
    // this._viewer.imageryLayers.remove(this._delegate);
  }
}
