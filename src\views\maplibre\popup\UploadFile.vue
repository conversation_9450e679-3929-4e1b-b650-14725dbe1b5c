<template>
  <custom-card
    @closeHandler="close"
    :width="'400px'"
    :top="'90px'"
    :right="'150px'"
    :title="mainItem.title"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      class="admin-sub-form"
      label-width="auto"
      :disabled="editable"
    >
      <el-form-item label="图片上传" prop="fileList">
        <UploadImage
          ref="uploadImgRef"
          v-model:list="formData.fileList"
          @validFile="validatorFile"
        />
        <!-- <el-upload
          class="avatar-uploader"
          ref="upload"
          :limit="1"
          :action="reqUrl"
          :headers="headers"
          :on-success="handleSuccess"
          :before-upload="beforeUpload"
          :data="infoData"
          accept=".jpeg,.png,.jpg"
          :disabled="editable"
        >
          <div v-if="!dialogImageUrl" class="avatar-uploader-icon">
            <el-icon>
              <Plus />
            </el-icon>
            <span>点击上传图片</span>
          </div>
          <div v-else class="avatar-uploader-icon">
            <img :src="dialogImageUrl" class="avatar" />
          </div>
        </el-upload> -->
      </el-form-item>
    </el-form>
    <div class="line"></div>
    <div class="mt-5 flex justify-end w-full pr-5 box-border">
      <el-button
        @click="handleClose"
        class="custom-close-button"
        :loading="subBtnLoading"
        >取 消</el-button
      >
      <el-button
        type="primary"
        :loading="subBtnLoading"
        class="custom-sub-button"
        @click="eventSubmit"
        >确 定</el-button
      >
    </div>
  </custom-card>
</template>
<script lang="ts" setup>
import { Plus } from "@element-plus/icons-vue";
import { updateDevice, queryDeviceSum } from "@/api/query";
import localCache from "@/utils/auth";
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});
interface FormData {
  fileList: any;
}
const initFormData = () => {
  return {
    fileList: [],
  };
};
const formData = ref<FormData>(initFormData());
const rules = {
  fileList: [{ required: true, message: "请上传图片", trigger: "change" }],
};
const fileList = ref(props.mainItem.params.pictureList);
let form: any = ref(null);
// const infoData = ref();
const subBtnLoading = ref(false);
const validatorFile = () => {
  form.value?.validateField("fileList");
};
// const headers = {
//   Latias: localCache.getCache("Latias") ?? "",
// };
const deviceFieldList = ref<any>([]);
// const reqUrl = ref(import.meta.env.VITE_XHR_URL + "/v1/resource");
// const handleSuccess = (response: any, uploadFile: any) => {
//   if (response.code === 200) {
//     // formData.value.url = response.data.url;
//     // formData.value.fileInfoDto = response.data;
//     dialogImageUrl.value = URL.createObjectURL(uploadFile.raw!);
//   } else {
//     fileList.value = [];
//     ElMessage.error(response.msg);
//   }
// };
// const beforeUpload = (rawFile: any) => {
//   const formDatas = new FormData();
//   infoData.value = formDatas.append("file", rawFile, rawFile.name);
// };
const close = () => {
  useDialogStore().closeDialog("UploadFile");
};
const getData = async () => {
  const result = await queryDeviceSum(props.mainItem.params.code);
  deviceFieldList.value = result.data;
};
getData();
const eventSubmit = async () => {
  console.log(formData.value);
  if (!form.value?.validate()) {
    return;
  } else {
    console.log(fileList.value);
    formData.value.fileList.forEach((el: any) => {
      fileList.value.push(el);
    });
    const putData = {
      code: props.mainItem.params.code,
      isTemplate: 1,
      firstDeviceTypeCode: props.mainItem.params.first_device_type_code,
      deviceFieldList: deviceFieldList.value,
      pictureList: fileList.value,
    };
    const response = await updateDevice(putData);
    if (response.code === 200) {
      ElMessage.success("修改成功");
      useDialogStore().closeDialog("UploadFile");
    } else {
      ElMessage.error(response.message);
    }
  }
};
const handleClose = () => {
  useDialogStore().closeDialog("UploadFile");
};
// const updateClick = () => {}
</script>
<style lang="scss" scoped>
.line {
  // width: 100%;
  border: 1px solid #eeeeee;
  margin-bottom: 10px;
}
:deep(.custom_card_body) {
  padding: 20px 0 !important;
}
:deep(.el-upload--picture-card) {
  width: 140px;
  height: 94px;
  background-color: transparent;
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 140px;
  height: 94px;
}
</style>
