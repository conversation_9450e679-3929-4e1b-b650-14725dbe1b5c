
// import type { TreeKey } from "element-plus/es/components/tree-v2/src/types"
export interface QueryForm {
  status: string;
  name: string;
  pageSize: number;
  pageNum: number;
}
export interface SysRole {
  /**
   * 角色标识（权限字符）
   */
  roleKey: string
  /**
   * ID
   */
  id: string
  /**
   * 菜单ID集合
   */
  menuIds?: any
  /**
   * 角色名称
   */
  name: string
  /**
   * 显示顺序
   */
  orderNum: number
  /**
   * 备注
   */
  remark?: string
  /**
   * 状态（0停用，1正常）
   */
  status: string

}