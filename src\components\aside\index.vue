<!--
 * @Author: xiao
 * @Date: 2024-07-03 14:52:08
 * @LastEditors: xiao
 * @LastEditTime: 2025-04-11 14:23:31
 * @Description: 
-->
<template>
  <div class="menu-box font-lt">
    <el-scrollbar>
      <el-menu
        :collapse="isCollapse"
        :default-active="active"
        popper-class="menu-popper"
        class="lt-menu border-r-0 w-full bg-transparent"
        unique-opened
        router
      >
        <template v-for="(item, index) in myMaplibreRoute">
          <aside-component
            v-if="item.visible === '1'"
            :key="index"
            :router-info="item"
          />
        </template>
      </el-menu>
    </el-scrollbar>

    <!-- 折叠面板操作按钮 -->
    <div class="collapse-btn-container">
      <el-button class="expand-btn" link @click="handleExpand">
        <el-icon size="18" :class="{ 'icon-rotated': isCollapse }">
          <SvgIcon name="menu-expand" />
        </el-icon>
        <span v-if="!isCollapse">折叠面板</span>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import AsideComponent from "./asideComponent/index.vue";
import { maplibreRouter } from "@/router/maplibre";
import { cesiumRouter } from "@/router/cesium";
import { adminRouter } from "@/router/admin";
import type { RouteRecordRaw } from "vue-router";

defineOptions({
  name: "BaseAside",
});

const route = useRoute();

/**
 * @interface MenuItem
 * @description 菜单项数据结构
 */
interface MenuItem {
  name?: string;
  visible: string;
  icon?: string;
  path: string;
  acIcon?: string;
  children?: MenuItem[];
}

const layoutStore = useLayoutStore();
const { isCollapse } = storeToRefs(layoutStore);
const menus: any = useMenuStore();
/**
 * @description 处理折叠面板展开/收起
 */
const handleExpand = () => {
  isCollapse.value = !isCollapse.value;
};

/**
 * @description 处理菜单数据
 * @param menu 路由数据
 * @param parentPath 父级路径
 * @returns 处理后的菜单数据
 */
const handleMenu = (
  menu: RouteRecordRaw[],
  parentPath: string = ""
): MenuItem[] => {
  // debugger;
  return menu.map((item) => {
    // 构建当前项的完整路径
    const fullPath = parentPath ? `${parentPath}/${item.path}` : item.path;

    // 基础菜单项
    const menuItem: MenuItem = {
      name: item.meta?.title as string | undefined,
      visible: "1",
      icon: item.meta?.icon as string | undefined,
      acIcon: item.meta?.acIcon as string | undefined,
      path: fullPath,
    };

    // 如果有子菜单，递归处理
    if (item.children && item.children.length > 0) {
      return {
        ...menuItem,
        children: handleMenu(item.children, fullPath),
      };
    }

    return menuItem;
  });
};

const myMaplibreRoute = ref();
const analysisModeStore = useAnalysisModeStore();
const { isAnalysisOnly } = storeToRefs(analysisModeStore);

watchEffect(() => {
  const firstPath = route.fullPath.split("/")[1];
  // const firstPath = menus.menu;
  const matchedRoute = menus.menu.find((item) => item.path === `/${firstPath}`);
  myMaplibreRoute.value = matchedRoute.children;
  console.log(matchedRoute.value);
  // if (firstPath === "cesium") {
  //   myMaplibreRoute.value = handleMenu(
  //     cesiumRouter.children,
  //     cesiumRouter.path
  //   );
  // } else if (firstPath === "admin") {
  //   console.log(menus.menu);
  //   myMaplibreRoute.value = matchedRoute.children;
  // } else {
  //   if (isAnalysisOnly.value) {
  //     myMaplibreRoute.value = handleMenu(
  //       maplibreRouter.children.filter(
  //         (item) => item.name === "MaplibrePipeNetwork"
  //       ),
  //       maplibreRouter.path
  //     );
  //   } else {
  //     myMaplibreRoute.value = handleMenu(
  //       maplibreRouter.children,
  //       maplibreRouter.path
  //     );
  //   }
  // }
});

const active = ref("");

watchEffect(() => {
  active.value = (route.meta.activeName as string) || (route.path as string);
});
</script>

<style scoped lang="scss">
:deep(.menu-box) {
  height: 100%;
  width: 100%;
  overflow: hidden; // 防止整个容器产生横向滚动条
  box-sizing: border-box; // 确保尺寸计算正确
}
</style>
<style lang="scss">
.lt-menu {
  --at-apply: "bg-transparent mt-16px overflow-hidden";

  .el-sub-menu .el-sub-menu__title,
  .el-menu-item {
    --at-apply: "h-40px font-size-3.5 box-border overflow-hidden";
    font-family: "Noto Sans SC";
  }

  & > .el-sub-menu .el-sub-menu__title,
  & > .el-menu-item {
    --at-apply: "relative whitespace-nowrap text-ellipsis";

    &::before {
      --at-apply: "content-empty absolute left-10.5px top-0 h-full bg-transparent transition-all duration-200 ease-in-out z--1 rounded-4px";
      width: calc(100% - 21px);
    }

    &:hover {
      color: var(--el-color-primary);
      background-color: transparent;
    }
  }
  & > .el-menu-item {
    &:hover {
      background-color: transparent;
    }
  }
  & > .el-menu-item.is-active {
    // color: #fff !important;
    --at-apply: "text-#fff! font-700";
    &::before {
      --at-apply: "bg-gradient-to-r from-#0453FF to-#327FFE z--1";
    }
  }

  .el-menu-item:hover {
    color: var(--el-color-primary);
    background-color: transparent;
    .menu-item-icon {
      border-color: var(--el-color-primary);
    }
  }

  .el-sub-menu.is-active .el-sub-menu__title {
    --at-apply: "text-#fff font-700";

    &::before {
      --at-apply: "bg-gradient-to-r from-#0453FF to-#327FFE z--1";
    }
  }

  .el-sub-menu .el-menu {
    --at-apply: "bg-transparent";
  }

  .el-sub-menu .el-menu .el-menu-item {
    --at-apply: "pl-20px color-#707278 font-size-13px";
  }
  .el-sub-menu .el-menu .el-menu-item.is-active,
  .el-sub-menu .el-menu .el-menu-item:hover {
    color: var(--el-color-primary);
    font-weight: 700;
  }

  .el-sub-menu .el-sub-menu .el-sub-menu__title {
    --at-apply: "pl-50px!";
  }

  .el-sub-menu .el-sub-menu__icon-arrow {
    --at-apply: "font-size-3.5";
  }

  // .el-menu-vertical-demo:not(.el-menu--collapse) {
  //   width: 210px;
  // }
}

// 折叠按钮容器样式
.collapse-btn-container {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  box-sizing: border-box;

  .expand-btn {
    width: 100%;
    color: var(--el-text-color-primary);
    justify-content: center;
    --el-button-active-color: rgba(
      $color: var(--el-text-color-primary),
      $alpha: 0.8
    );
    box-sizing: border-box;
    overflow: hidden;

    .el-icon {
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      flex-shrink: 0;
    }

    span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .icon-rotated {
    transform: rotate(180deg);
  }
}

// 为菜单容器添加相对定位，为折叠按钮提供定位基准
.menu-box {
  position: relative;
  height: 100%;
  overflow: hidden; // 防止横向滚动条
  .el-scrollbar {
    height: calc(100% - 80px); // 为底部按钮留出空间

    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden; // 强制隐藏横向滚动条
    }
  }
}
</style>
