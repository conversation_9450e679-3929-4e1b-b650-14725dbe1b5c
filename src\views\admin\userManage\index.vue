<template>
  <div class="base-main" grid="~ rows-[72px_1fr] gap-y-3">
    <div class="query-form">
      <el-form :model="queryForm">
        <el-row :gutter="40">
          <el-col :span="6">
            <el-form-item label="用户名称">
              <el-input
                v-model="queryForm.name"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系方式">
              <el-input
                v-model="queryForm.name"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="用户类型">
              <el-select
                class="w-full"
                v-model="queryForm.type"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in userType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button
                class="admin-query-btn"
                type="primary"
                @click="queryData"
                :icon="Search"
              >
                查询
              </el-button>
              <el-button class="admin-reset-btn" :icon="Refresh" @click="reset">
                重置</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="pb-unset! box-border [&_>div]:overflow-hidden">
      <div class="table-height">
        <el-table
          v-loading="loading"
          class="routeCt"
          :row-class-name="tableRowClassName"
          :data="tableData"
          style="width: 100%"
          height="100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="100"
            align="center"
          />
          <el-table-column prop="name" label="用户名称" align="center" />
          <el-table-column prop="nickname" label="用户昵称" align="center" />
          <el-table-column prop="name" label="用户角色" align="center" />
          <el-table-column prop="type" label="用户类型" align="center">
            <template #default="{ row }">
              {{ useConstValue(userType, row.type) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            show-overflow-tooltip
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="updateTime"
            label="更新时间"
            show-overflow-tooltip
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="sourcePlatform"
            label="来源平台"
            show-overflow-tooltip
            align="center"
          >
          </el-table-column>
          <el-table-column label="操作" min-width="100" align="center">
            <template v-slot="{ row }">
              <div class="flex-c">
                <el-button
                  link
                  class="primary-link color-#1966FF"
                  @click="handleCheck(row)"
                >
                  详情</el-button
                >
                <div class="tb-line"></div>
                <el-button
                  link
                  class="primary-link color-#1966FF"
                  @click="handleEdit(row)"
                  >修改</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagibox box-border">
        <div class="pagitotal">共{{ tableSize }}条数据</div>
        <pagination
          class="admin-pagi"
          :total="tableSize"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          @pagination="getList"
        ></pagination>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :model-value="visible"
      width="50%"
      class="admin-dialog"
      :before-close="handleClose"
      :close-on-click-modal="false"
      ref="test"
    >
      <user-info
        ref="userRef"
        v-model:formData="formData"
        v-model:editable="editable"
        v-model:dialogTitle="dialogTitle"
      />
      <template v-slot:footer v-if="dialogTitle != '用户查看'">
        <div>
          <el-button
            @click="handleClose"
            class="custom-close-button"
            :loading="subBtnLoading"
            >取 消</el-button
          >
          <el-button
            type="primary"
            :loading="subBtnLoading"
            class="custom-sub-button"
            @click="eventSubmit"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { Search, Refresh, Plus } from "@element-plus/icons-vue";
import { userType, system } from "@/utils/constant";
import UserInfo from "./UserInfo.vue";
import {
  userPage,
  addUser,
  editUser,
  detailsUser,
  deleteUser,
} from "@/api/user";
import type { SysUser, QueryForm } from "./type";
import { useConstValue } from "@/utils/utils";
const initQueryForm = () => {
  return {
    name: "",
    type: "",
    phone: "",
    pageNum: 1,
    pageSize: 20,
  };
};
const queryForm = ref<QueryForm>(initQueryForm());
const initFormData = () => {
  return {
    id: "",
    name: "",
    code: "",
    gender: "",
    username: "",
    email: "",
    phone: "",
    birthday: "",
    nickname: "",
    sourcePlatform: "",
    createTime: "",
    updateTime: "",
    activation: "",
    realStatus: "",
    source: "",
    photo: "",
    roleIds: "",
    type: "",
  };
};
const formData = ref<SysUser>(initFormData());
const loading = ref(false);
const tableData = ref<SysUser[]>([]);
const dialogTitle = ref("");
const visible = ref(false);
const editable = ref(false);
const subBtnLoading = ref(false);
const tableSize = ref(0);
const userinfo = ref();
const userRef = ref();
const queryData = () => {
  getList();
};
const reset = () => {
  queryForm.value = initQueryForm();
  getList();
};
const eventSubmit = async () => {
  try {
    var info = await userRef.value!.submitForm();
    console.log(info);
    if (info) {
      const result = await editUser({ id: info.id, roleIds: info.roleIds });
      if (result.code === 200) {
        visible.value = false;
        ElMessage.success("修改成功");
        await getList();
      }
    }
  } catch (error) {
    console.log(error);
  }
};
const handleCheck = async (row: SysUser) => {
  editable.value = true;
  dialogTitle.value = "用户查看";
  const result = await detailsUser(row.id);
  formData.value = result.data;
  formData.value.type = useConstValue(userType, row.type);
  formData.value.realStatus = row.realStatus == "0" ? "认证" : "未认证";
  formData.value.activation = row.activation == "0" ? "可用" : "禁用";
  formData.value.source = useConstValue(system, row.source);
  visible.value = true;
};
const handleEdit = async (row: SysUser) => {
  editable.value = true;
  dialogTitle.value = "用户修改";
  const result = await detailsUser(row.id);
  formData.value = result.data;
  formData.value.type = useConstValue(userType, row.type);
  formData.value.realStatus = row.realStatus == "0" ? "认证" : "未认证";
  formData.value.activation = row.activation == "0" ? "可用" : "禁用";
  formData.value.source = useConstValue(system, row.source);
  visible.value = true;
};
const handleClose = () => {
  visible.value = false;
};
const getList = async () => {
  try {
    loading.value = true;
    const result = await userPage(queryForm.value);
    tableData.value = result.data.list;
    tableSize.value = result.data.totalCount;
  } finally {
    loading.value = false;
  }
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.table-height {
  height: calc(100vh - 295px);
}
</style>
