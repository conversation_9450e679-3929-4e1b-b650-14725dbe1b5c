/*
 * @Description:
 * @Date: 2022-09-26 15:24:34
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2022-11-01 16:23:50
 */

export default class Coord {
  
  /**
   * @description: 设置笛卡尔坐标系的高
   * @param {type}
   * @return:
   */
  static setHeight(point: Cesium.Cartesian3, height: number) {
    const {Cesium} = BC.Namespace;
    const cartographic = Cesium.Cartographic.fromCartesian(point);
    cartographic.height = height;
    return Cesium.Cartographic.toCartesian(cartographic);
  }
  /**
   * @description: 批量设置笛卡尔坐标系的高
   * @param {type}
   * @return:
   */
  static setHeights(points: Cesium.Cartesian3[], height: number) {
    const {Cesium} = BC.Namespace;
    const pointsArr = [];
    for (const p of points) {
      const point = this.setHeight(p, height);
      pointsArr.push(point);
    }
    return pointsArr;
  }

  static getMaxHeight(positions: Cesium.Cartesian3[]) {
    //获取最高点
    let maxHeight: number = Number.MIN_VALUE;
    for (const position of positions) {
      const height: number = Coord.getHeight(position);
      if (maxHeight < height) {
        maxHeight = height;
      }
    }
    return maxHeight;
  }

  static getMinHeight(positions: Cesium.Cartesian3[]) {
    // 获取最低点
    let minHeight: number = Number.MAX_VALUE;
    for (const positon of positions) {
      const height: number = Coord.getHeight(positon);
      if (minHeight > height) {
        minHeight = height;
      }
    }
    return minHeight;
  }
  /**
   * @description: 获取高度
   * @param {type}
   * @return:
   */
  static getHeight(point: Cesium.Cartesian3) {
    const {Cesium} = BC.Namespace;
    const cartographic = Cesium.Cartographic.fromCartesian(point);
    return cartographic.height;
  }
  /**
   * @description: 批量克隆
   * @param {type}
   * @return:
   */
  static cartesiansClone(cartesians: Cesium.Cartesian3[]) {
    const clones = [];
    for (const c of cartesians) {
      clones.push(c.clone());
    }
    return clones;
  }

  

  static formatNum(num: number, digits: number) {
    return parseFloat(num.toFixed(digits));
  }

  /**
   * 获取屏幕中心的地理坐标（重构版本）
   * @description 更可靠地获取屏幕中心对应的地理坐标，确保二三维切换时中心点一致
   * 
   * @param camera Cesium相机对象
   * @returns 屏幕中心的地理坐标信息
   */
  static getScreenCenterCoordinate(camera: any) {
    const {Cesium} = BC.Namespace;
    const viewer = camera._viewer || camera.viewer;
    const scene = viewer?.scene || camera.scene;
    
    try {
      // 获取屏幕中心点
      const canvas = scene.canvas;
      const centerX = canvas.clientWidth / 2;
      const centerY = canvas.clientHeight / 2;
      const screenCenter = new Cesium.Cartesian2(centerX, centerY);
      
      console.log(`屏幕中心像素坐标: (${centerX}, ${centerY})`);
      
      // 方法1：尝试从屏幕中心发射射线到地球表面
      const ray = camera.getPickRay(screenCenter);
      if (ray) {
        // 先尝试与地球椭球面相交
        const ellipsoidIntersection = scene.globe.ellipsoid.intersectRay(ray);
        if (ellipsoidIntersection) {
          const cartographic = Cesium.Cartographic.fromCartesian(ellipsoidIntersection);
          const result = {
            position: ellipsoidIntersection,
            cartographic: cartographic,
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            height: 0, // 椭球面高度为0
            method: 'ellipsoid_intersection'
          };
          console.log(`屏幕中心坐标(椭球面): 经度=${result.longitude.toFixed(6)}°, 纬度=${result.latitude.toFixed(6)}°`);
          return result;
        }
        
        // 如果椭球面相交失败，尝试与地形相交
        const terrainIntersection = scene.globe.pick(ray, scene);
        if (terrainIntersection) {
          const cartographic = Cesium.Cartographic.fromCartesian(terrainIntersection);
          const result = {
            position: terrainIntersection,
            cartographic: cartographic,
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            height: cartographic.height,
            method: 'terrain_intersection'
          };
          console.log(`屏幕中心坐标(地形): 经度=${result.longitude.toFixed(6)}°, 纬度=${result.latitude.toFixed(6)}°`);
          return result;
        }
      }
      
      // 方法2：使用相机位置投影到地面
      console.warn('射线投射失败，使用相机位置投影方法');
      return this.getCameraGroundProjection(camera);
      
    } catch (error) {
      console.error('获取屏幕中心坐标失败:', error);
      return this.getCameraGroundProjection(camera);
    }
  }

  /**
   * 获取相机位置的地面投影（改进版本）
   * @description 将相机位置投影到地面，作为屏幕中心的降级方案
   * 
   * @param camera Cesium相机对象
   * @returns 相机位置的地面投影信息
   */
  static getCameraGroundProjection(camera: any) {
    const {Cesium} = BC.Namespace;
    try {
      const position = camera.position;
      const cartographic = Cesium.Cartographic.fromCartesian(position);
      
      // 创建地面投影点（高度设为0）
      const groundCartographic = new Cesium.Cartographic(
        cartographic.longitude,
        cartographic.latitude,
        0
      );
      const groundPosition = Cesium.Cartographic.toCartesian(groundCartographic);
      
      const result = {
        position: groundPosition,
        cartographic: groundCartographic,
        longitude: Cesium.Math.toDegrees(cartographic.longitude),
        latitude: Cesium.Math.toDegrees(cartographic.latitude),
        height: 0,
        method: 'camera_projection'
      };
      
      console.log(`相机投影坐标: 经度=${result.longitude.toFixed(6)}°, 纬度=${result.latitude.toFixed(6)}°`);
      return result;
      
    } catch (error) {
      console.error('获取相机位置地面投影失败:', error);
      
      // 最终降级方案：返回一个默认的中心点
      const defaultCartographic = new Cesium.Cartographic(
        Cesium.Math.toRadians(116.3974), // 北京经度
        Cesium.Math.toRadians(39.9093),  // 北京纬度
        0
      );
      return {
        position: Cesium.Cartographic.toCartesian(defaultCartographic),
        cartographic: defaultCartographic,
        longitude: 116.3974,
        latitude: 39.9093,
        height: 0,
        method: 'default_fallback'
      };
    }
  }

  /**
   * 获取当前相机视角的真实中心点
   * @description 计算屏幕中心射线与地面的交点，这是用户实际看到的中心位置
   * 
   * @param camera Cesium相机对象
   * @returns 视角中心点信息：{position, cartographic, longitude, latitude}
   * 
   * @remarks
   * - 通过屏幕中心射线投射到地面计算真实中心点
   * - 这个点与边界框几何中心可能不同，特别是在倾斜视角下
   * - 用于确保3D转2D时视角中心的一致性
   */
  static getCameraViewCenter(camera: any) {
    const {Cesium} = BC.Namespace;
    const viewer = camera._viewer || camera.viewer;
    const scene = viewer?.scene || camera.scene;
    
    try {
      // 获取屏幕中心点
      const canvas = scene.canvas;
      const centerX = canvas.clientWidth / 2;
      const centerY = canvas.clientHeight / 2;
      const screenCenter = new Cesium.Cartesian2(centerX, centerY);
      
      // 从屏幕中心发射射线到地面
      const ray = camera.getPickRay(screenCenter);
      if (ray) {
        const intersection = scene.globe.pick(ray, scene);
        if (intersection) {
          const cartographic = Cesium.Cartographic.fromCartesian(intersection);
          return {
            position: intersection,
            cartographic: cartographic,
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            height: cartographic.height
          };
        }
      }
      
      // 如果无法投射到地面，尝试使用相机位置的地面投影
      console.warn('无法从屏幕中心投射到地面，使用相机位置投影');
      return this.getCameraPositionGroundProjection(camera);
      
    } catch (error) {
      console.error('获取视角中心点失败:', error);
      return this.getCameraPositionGroundProjection(camera);
    }
  }

  /**
   * 获取相机位置的地面投影点（降级方案）
   * @description 当无法从屏幕中心投射到地面时的备选方案
   * 
   * @param camera Cesium相机对象
   * @returns 相机位置的地面投影信息
   */
  private static getCameraPositionGroundProjection(camera: any) {
    const {Cesium} = BC.Namespace;
    try {
      const position = camera.position;
      const cartographic = Cesium.Cartographic.fromCartesian(position);
      
      // 创建地面投影点（高度设为0）
      const groundCartographic = new Cesium.Cartographic(
        cartographic.longitude,
        cartographic.latitude,
        0
      );
      const groundPosition = Cesium.Cartographic.toCartesian(groundCartographic);
      
      return {
        position: groundPosition,
        cartographic: groundCartographic,
        longitude: Cesium.Math.toDegrees(cartographic.longitude),
        latitude: Cesium.Math.toDegrees(cartographic.latitude),
        height: 0
      };
      
    } catch (error) {
      console.error('获取相机位置地面投影失败:', error);
      
      // 最终降级方案：返回一个默认的中心点
      const defaultCartographic = new Cesium.Cartographic(0, 0, 0);
      return {
        position: Cesium.Cartographic.toCartesian(defaultCartographic),
        cartographic: defaultCartographic,
        longitude: 0,
        latitude: 0,
        height: 0
      };
    }
  }

  /**
   * 计算当前相机视野的地面边界框
   * @description 基于相机位置、方向和FOV计算可视地面范围
   * 
   * @param camera Cesium相机对象
   * @returns 返回边界框信息：{west, east, south, north, width, height}
   * 
   * @remarks
   * - 通过屏幕四个角点投射到地面计算边界
   * - 考虑相机高度和俯仰角的影响
   * - 适用于计算3D到2D转换时的视野范围
   */
  static getCameraGroundBounds(camera: any) {
    const {Cesium} = BC.Namespace;
    const viewer = camera._viewer || camera.viewer;
    const scene = viewer?.scene || camera.scene;
    
    try {
      // 获取屏幕四个角点
      const canvas = scene.canvas;
      const width = canvas.clientWidth;
      const height = canvas.clientHeight;
      
      const corners = [
        new Cesium.Cartesian2(0, 0),           // 左上
        new Cesium.Cartesian2(width, 0),       // 右上  
        new Cesium.Cartesian2(width, height),  // 右下
        new Cesium.Cartesian2(0, height)       // 左下
      ];
      
      // 将屏幕坐标投射到地球表面
      const groundPositions = [];
      for (const corner of corners) {
        const ray = camera.getPickRay(corner);
        if (ray) {
          const intersection = scene.globe.pick(ray, scene);
          if (intersection) {
            groundPositions.push(intersection);
          }
        }
      }
      
      if (groundPositions.length === 0) {
        // 如果无法投射到地面，使用相机位置计算估算范围
        return this.estimateBoundsFromCamera(camera);
      }
      
      // 计算边界框
      const cartographics = groundPositions.map(pos => 
        Cesium.Cartographic.fromCartesian(pos)
      );
      
      let west = Number.MAX_VALUE;
      let east = Number.MIN_VALUE;
      let south = Number.MAX_VALUE; 
      let north = Number.MIN_VALUE;
      
      for (const cartographic of cartographics) {
        const lon = Cesium.Math.toDegrees(cartographic.longitude);
        const lat = Cesium.Math.toDegrees(cartographic.latitude);
        
        west = globalThis.Math.min(west, lon);
        east = globalThis.Math.max(east, lon);
        south = globalThis.Math.min(south, lat);
        north = globalThis.Math.max(north, lat);
      }
      
      return {
        west,
        east, 
        south,
        north,
        width: east - west,
        height: north - south,
        centerLon: (west + east) / 2,
        centerLat: (south + north) / 2
      };
      
    } catch (error) {
      console.warn('计算相机地面边界失败，使用估算方法:', error);
      return this.estimateBoundsFromCamera(camera);
    }
  }

  /**
   * 基于相机参数估算视野边界（降级方案）
   * @description 当无法准确投射到地面时的估算方法
   * 
   * @param camera Cesium相机对象
   * @returns 估算的边界框信息
   */
  private static estimateBoundsFromCamera(camera: any) {
    const {Cesium} = BC.Namespace;
    const position = camera.position;
    const cartographic = Cesium.Cartographic.fromCartesian(position);
    const height = cartographic.height;
    
    // 基于高度使用固定比例估算可视范围
    // 经验值：可视范围约为高度的0.5倍
    const viewDistance = height * 0.5;
    const degreeDistance = viewDistance / 111000; // 粗略转换为度数
    
    const centerLon = Cesium.Math.toDegrees(cartographic.longitude);
    const centerLat = Cesium.Math.toDegrees(cartographic.latitude);
    
    return {
      west: centerLon - degreeDistance / 2,
      east: centerLon + degreeDistance / 2,
      south: centerLat - degreeDistance / 2,
      north: centerLat + degreeDistance / 2,
      width: degreeDistance,
      height: degreeDistance,
      centerLon,
      centerLat
    };
  }

  /**
   * 计算最佳2D相机位置和参数（重构版本）
   * @description 基于屏幕中心坐标计算最佳的2D俯视位置，确保中心点一致
   * 
   * @param camera 当前相机对象
   * @returns 2D相机位置和参数信息
   */
  static calculateOptimal2DPosition(camera: any) {
    const {Cesium} = BC.Namespace;
    try {
      // 1. 获取屏幕中心的地理坐标
      const centerCoord = this.getScreenCenterCoordinate(camera);
      
      // 2. 计算合适的2D视角高度
      const currentHeight = this.getHeight(camera.position);
      let optimal2DHeight: number;
      
      // 基于当前高度智能计算2D高度
      if (currentHeight < 5000) {
        // 低空视角：提升到合适的俯视高度
        optimal2DHeight = globalThis.Math.max(currentHeight * 2, 5000);
      } else if (currentHeight < 50000) {
        // 中等高度：适度调整
        optimal2DHeight = globalThis.Math.max(currentHeight * 1.2, 8000);
      } else {
        // 高空视角：保持或适度降低
        optimal2DHeight = globalThis.Math.max(currentHeight * 0.8, 15000);
      }
      
      // 限制高度范围
      optimal2DHeight = globalThis.Math.max(3000, globalThis.Math.min(50000, optimal2DHeight));
      
      // 3. 创建2D相机位置（在中心点正上方）
      const centerCartographic = new Cesium.Cartographic(
        Cesium.Math.toRadians(centerCoord.longitude),
        Cesium.Math.toRadians(centerCoord.latitude),
        optimal2DHeight
      );
      const centerPosition = Cesium.Cartographic.toCartesian(centerCartographic);
      
      console.log(`2D位置计算: 中心(${centerCoord.longitude.toFixed(6)}°, ${centerCoord.latitude.toFixed(6)}°), 高度=${optimal2DHeight.toFixed(0)}m`);
      
      return {
        position: centerPosition,
        height: optimal2DHeight,
        centerCoord: centerCoord,
        method: centerCoord.method
      };
      
    } catch (error) {
      console.error('计算最佳2D位置失败:', error);
      
      // 降级方案：使用当前位置和默认高度
      const currentHeight = this.getHeight(camera.position);
      const fallbackHeight = globalThis.Math.max(currentHeight * 1.5, 10000);
      
      return {
        position: this.setHeight(camera.position.clone(), fallbackHeight),
        height: fallbackHeight,
        centerCoord: null,
        method: 'fallback'
      };
    }
  }

  /**
   * 验证屏幕中心坐标一致性
   * @description 验证切换前后屏幕中心坐标是否一致
   * 
   * @param camera 相机对象
   * @param expectedCoord 期望的中心坐标
   * @param tolerance 容差（度），默认0.001度
   * @returns 是否一致
   */
  static validateScreenCenterConsistency(camera: any, expectedCoord: any, tolerance: number = 0.001) {
    try {
      const currentCenter = this.getScreenCenterCoordinate(camera);
      
      const lonDiff = globalThis.Math.abs(currentCenter.longitude - expectedCoord.longitude);
      const latDiff = globalThis.Math.abs(currentCenter.latitude - expectedCoord.latitude);
      
      const isConsistent = lonDiff <= tolerance && latDiff <= tolerance;
      
      console.log(`中心坐标验证: 期望(${expectedCoord.longitude.toFixed(6)}°, ${expectedCoord.latitude.toFixed(6)}°), 实际(${currentCenter.longitude.toFixed(6)}°, ${currentCenter.latitude.toFixed(6)}°), 差异(${lonDiff.toFixed(6)}°, ${latDiff.toFixed(6)}°), 一致性: ${isConsistent}`);
      
      return {
        isConsistent,
        expectedCoord,
        actualCoord: currentCenter,
        difference: { longitude: lonDiff, latitude: latDiff }
      };
      
    } catch (error) {
      console.error('验证屏幕中心坐标一致性失败:', error);
      return {
        isConsistent: false,
        expectedCoord,
        actualCoord: null,
        difference: null
      };
    }
  }

  /**
   * 计算2D俯视所需的相机高度
   * @description 基于期望覆盖的地面范围计算合适的俯视高度
   * 
   * @param bounds 地面边界框信息
   * @param margin 边距系数，默认1.5（增加50%边距确保完全覆盖）
   * @returns 建议的相机高度（米）
   * 
   * @remarks
   * - 使用简化的高度计算，不依赖FOV
   * - 基于视野范围使用固定比例系数
   * - 设置合理的高度范围限制
   */
  static calculateOptimal2DHeight(bounds: any, margin: number = 1.5) {
    try {
      // 计算边界框距离（米）
      const widthMeters = bounds.width * 111000 * globalThis.Math.cos(globalThis.Math.PI * bounds.centerLat / 180);
      const heightMeters = bounds.height * 111000;
      
      // 取较大的边作为主要计算依据
      const maxDimension = globalThis.Math.max(widthMeters, heightMeters);
      
      // 使用固定比例系数计算高度（不依赖FOV）
      // 经验值：高度约为最大视野范围的1.5-2倍可获得良好的俯视效果
      const requiredHeight = maxDimension * margin;
      
      // 设置合理的高度范围限制
      const minHeight = 3000;   // 最低1万米
      const maxHeight = 20000; // 最高2000万米
      
      let finalHeight = globalThis.Math.max(minHeight, globalThis.Math.min(maxHeight, requiredHeight));
      
      // 对于小范围，适当增加高度确保良好的观察效果
      if (maxDimension < 1000) {
        finalHeight = globalThis.Math.max(finalHeight, 3000);
      }
      
      console.log(`视野范围计算: 宽度=${widthMeters.toFixed(0)}m, 高度=${heightMeters.toFixed(0)}m, 建议俯视高度=${finalHeight.toFixed(0)}m`);
      
      return finalHeight;
      
    } catch (error) {
      console.error('计算2D高度失败:', error);
      // 降级到默认高度
      return 500000; // 默认50万米
    }
  }

  /**
   * 获取3D到2D转换的最佳相机参数
   * @description 综合分析当前3D视角，计算最佳的2D俯视参数，确保中心点一致性
   * 
   * @param camera 当前相机对象
   * @returns 2D相机参数：{position, height, bounds, centerInfo}
   * 
   * @example
   * const optimal2D = Coord.getOptimal2DCameraParams(viewer.camera);
   * camera.setView({
   *   destination: optimal2D.position,
   *   orientation: {
   *     heading: 0,
   *     pitch: CesiumMath.toRadians(-90),
   *     roll: 0
   *   }
   * });
   */
  static getOptimal2DCameraParams(camera: any) {
    const {Cesium} = BC.Namespace;
    try {
      // 1. 获取真实的视角中心点
      const viewCenter = this.getCameraViewCenter(camera);
      console.log(`3D视角中心点: 经度=${viewCenter.longitude.toFixed(6)}°, 纬度=${viewCenter.latitude.toFixed(6)}°`);
      
      // 2. 获取当前视野边界（用于计算合适的高度）
      const bounds = this.getCameraGroundBounds(camera);
      
      // 3. 对比真实中心点与边界框几何中心的差异
      if (bounds && bounds.centerLon !== undefined) {
        const centerDiffLon = globalThis.Math.abs(viewCenter.longitude - bounds.centerLon);
        const centerDiffLat = globalThis.Math.abs(viewCenter.latitude - bounds.centerLat);
        console.log(`中心点差异: 经度差=${centerDiffLon.toFixed(6)}°, 纬度差=${centerDiffLat.toFixed(6)}°`);
        
        if (centerDiffLon > 0.001 || centerDiffLat > 0.001) {
          console.log('检测到显著的中心点差异，使用真实视角中心点确保一致性');
        }
      }
      
      // 4. 计算最佳俯视高度（基于视野边界范围）
      const optimalHeight = this.calculateOptimal2DHeight(bounds);
      
      // 6. 使用真实视角中心点创建2D相机位置
      const centerCartographic = new Cesium.Cartographic(
        Cesium.Math.toRadians(viewCenter.longitude),
        Cesium.Math.toRadians(viewCenter.latitude),
        optimalHeight
      );
      const centerPosition = Cesium.Cartographic.toCartesian(centerCartographic);
      
      console.log(`最佳2D参数: 中心(${viewCenter.longitude.toFixed(6)}°, ${viewCenter.latitude.toFixed(6)}°), 高度=${optimalHeight.toFixed(0)}m`);
      
      return {
        position: centerPosition,
        height: optimalHeight,
        bounds: bounds,
        centerInfo: viewCenter
      };
      
    } catch (error) {
      console.error('获取最佳2D相机参数失败:', error);
      
      // 降级方案：使用当前位置和默认高度
      const currentHeight = this.getHeight(camera.position);
      const fallbackHeight = globalThis.Math.max(currentHeight + 500000, 1000000);
      
      console.log(`使用降级方案: 高度=${fallbackHeight.toFixed(0)}m`);
      
      return {
        position: this.setHeight(camera.position.clone(), fallbackHeight),
        height: fallbackHeight,
        bounds: null,
        centerInfo: null
      };
    }
  }
}
