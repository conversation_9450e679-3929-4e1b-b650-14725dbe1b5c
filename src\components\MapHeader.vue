<template>
  <div class="header box-border">
    <div class="title">供水管网地理信息系统</div>
    <div class="w-630px flex color-#fff box-border pt-7 pl-9">
      <div v-for="item in list.slice(0, 3)" :key="item.name" class="menu-item">
        <div>
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="absolute top-7 right-15 flex color-#fff box-border">
      <div v-for="item in list.slice(3, 5)" :key="item.name" class="menu-item">
        <div>
          {{ item.name }}
        </div>
      </div>
      <div class="ml-10">
        <el-dropdown>
          <div
            class="h-full w-full cursor-pointer el-dropdown-link"
            flex="~ items-center gap-x-2"
          >
            <img class="w-40px h-40px" :src="userAvater" />
            <span class="text-#ffffff font-regular" font="size-4 400"
              >超级管理员</span
            >
            <!-- <img :src="getImages('airport/down.png')" alt="" /> -->
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="loginOut"> 登 出 </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getImages } from "@/utils/getImages";
const userAvater = getImages("user.png");
const list = ref([
  {
    name: "GIS一张图",
  },
  {
    name: "查询统计",
  },
  {
    name: "管网分析",
  },
  {
    name: "专题展示",
  },
  {
    name: "数据编辑",
  },
]);
const loginOut = () => {};
</script>
<style lang="scss" scoped>
.header {
  width: 100%;
  position: absolute;
  // display: none;
  top: 0;
  z-index: 2;
  .title {
    line-height: 73px;
    font-size: 26px;
    color: #fff;
    font-family: DouYu;
    font-weight: normal;
    letter-spacing: 4.68px;
    text-shadow: 0px 3px 12px 0px #00b1ff;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
  }
}
.menu-item {
  width: 183px;
  height: 52px;
  line-height: 50px;
  background: url("@/assets/images/menu-bg.png") no-repeat;
  text-align: center;
  margin-left: 20px;
  cursor: pointer;
  color: #c6f5ff;
  font-family: Douyu;
}
</style>
