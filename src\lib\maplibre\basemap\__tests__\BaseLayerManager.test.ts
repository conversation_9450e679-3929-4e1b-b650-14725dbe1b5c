/**
 * @fileoverview 基础图层管理器测试
 * @description 测试BaseLayerManager的图层检测和顺序管理功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock MapLibre GL
const mockMap = {
  getStyle: vi.fn(),
  getLayer: vi.fn(),
  moveLayer: vi.fn(),
  addLayer: vi.fn(),
  removeLayer: vi.fn(),
  addSource: vi.fn(),
  removeSource: vi.fn(),
  on: vi.fn(),
  off: vi.fn()
};

// Mock图层数据
const mockLayers = [
  // 底图图层
  { id: 'tdt-vector', type: 'raster' },
  { id: 'tdt-vector-annotation', type: 'symbol' },
  { id: 'baidu-normal', type: 'raster' },
  
  // 业务图层
  { id: 'plot-features-layer-point', type: 'circle' },
  { id: 'plot-features-layer-line', type: 'line' },
  { id: 'pipe-nodes-layer', type: 'circle' },
  { id: 'pipe-lines-layer', type: 'line' },
  { id: 'device-layer', type: 'symbol' },
  { id: 'mvt_pipeNode', type: 'circle' },
  { id: 'mvt_pipeLine', type: 'line' },
  
  // MVT注记图层
  { id: 'pipeline_layer_label', type: 'symbol' },
  { id: 'water_system_label', type: 'symbol' },
  
  // 设备聚合图层
  { id: 'device_pressure_clusters', type: 'circle' },
  { id: 'device_pressure_cluster_count', type: 'symbol' },
  { id: 'device_noise', type: 'symbol' },
  { id: 'device_flow_expanded_123', type: 'symbol' },
  
  // 其他业务图层
  { id: 'custom_analysis_layer', type: 'fill' }
];

describe('BaseLayerManager 图层检测功能测试', () => {
  let baseLayerManager: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // 设置mock返回值
    mockMap.getStyle.mockReturnValue({ layers: mockLayers });
    mockMap.getLayer.mockImplementation((id: string) => {
      return mockLayers.find(layer => layer.id === id);
    });

    // 动态导入BaseLayerManager（避免实际的地图依赖）
    baseLayerManager = {
      map: mockMap,
      debug: true,
      currentBasemap: { id: 'tdt-vector' },
      currentAnnotationLayer: 'tdt-vector-annotation',
      
      // 模拟isBasemapLayer方法
      isBasemapLayer(layerId: string): boolean {
        // 当前底图图层
        if (this.currentBasemap && layerId === this.currentBasemap.id) {
          return true;
        }
        
        // 当前注记图层
        if (this.currentAnnotationLayer && layerId === this.currentAnnotationLayer) {
          return true;
        }
        
        // 天地图相关图层
        if (layerId.startsWith('tdt-') || layerId.includes('-annotation')) {
          return true;
        }
        
        // 百度地图相关图层
        if (layerId.startsWith('baidu-')) {
          return true;
        }
        
        // 高德地图相关图层
        if (layerId.startsWith('amap-')) {
          return true;
        }
        
        return false;
      },
      
      // 模拟getTopLayerIds方法
      getTopLayerIds(): string[] {
        const layerIds: string[] = [];
        const allMapLayers = this.map.getStyle().layers || [];
        
        // 静态图层
        const plotLayerIds = [
          'plot-features-layer-point',
          'plot-features-layer-line', 
          'plot-features-layer-polygon-fill',
          'plot-features-layer-polygon-stroke'
        ];
        
        const pipeLayerIds = [
          'pipe-nodes-layer',
          'pipe-lines-layer',
          'pipe-nodes-labels',
          'pipe-lines-labels'
        ];

        const deviceLayerIds = ['device-layer'];
        const pipeNodeLayerIds = ['mvt_pipeNode'];
        const pipeLineLayerIds = ['mvt_pipeLine'];

        // 动态检测MVT注记图层
        const mvtLabelLayerIds: string[] = [];
        allMapLayers.forEach((layer: any) => {
          if (layer.id.endsWith('_label') && layer.type === 'symbol') {
            mvtLabelLayerIds.push(layer.id);
          }
        });

        // 动态检测设备聚合图层
        const deviceClusterLayerIds: string[] = [];
        allMapLayers.forEach((layer: any) => {
          const layerId = layer.id;
          if (layerId.includes('_clusters') && layerId.startsWith('device_')) {
            deviceClusterLayerIds.push(layerId);
          }
          if (layerId.includes('_cluster_count') && layerId.startsWith('device_')) {
            deviceClusterLayerIds.push(layerId);
          }
          if (layerId.includes('_expanded_') && layerId.startsWith('device_')) {
            deviceClusterLayerIds.push(layerId);
          }
          if (layerId.startsWith('device_') && 
              !layerId.includes('_clusters') && 
              !layerId.includes('_cluster_count') && 
              !layerId.includes('_expanded_') &&
              !layerId.includes('_source')) {
            deviceClusterLayerIds.push(layerId);
          }
        });

        // 其他业务图层
        const businessLayerIds: string[] = [];
        allMapLayers.forEach((layer: any) => {
          const layerId = layer.id;
          if (this.isBasemapLayer(layerId)) {
            return;
          }
          if ([...plotLayerIds, ...pipeLayerIds, ...deviceLayerIds, 
               ...pipeNodeLayerIds, ...pipeLineLayerIds, 
               ...mvtLabelLayerIds, ...deviceClusterLayerIds].includes(layerId)) {
            return;
          }
          businessLayerIds.push(layerId);
        });

        // 合并所有图层（注意顺序：从底层到顶层）
        const allBusinessLayerIds = [
          ...plotLayerIds,           // 1. 标绘图层（最底层的业务图层）
          ...pipeLayerIds,           // 2. 管网图层
          ...deviceLayerIds,         // 3. 基础设备图层
          ...pipeLineLayerIds,       // 4. 管线图层
          ...pipeNodeLayerIds,       // 5. 管点图层（在管线图层之上）
          ...deviceClusterLayerIds,  // 6. 设备聚合图层
          ...mvtLabelLayerIds,       // 7. MVT注记图层
          ...businessLayerIds        // 8. 其他业务图层（最顶层）
        ];

        // 检查图层是否存在
        allBusinessLayerIds.forEach(layerId => {
          if (this.map.getLayer(layerId)) {
            layerIds.push(layerId);
          }
        });
        
        return layerIds;
      }
    };
  });

  describe('底图图层识别', () => {
    it('应该正确识别天地图底图图层', () => {
      expect(baseLayerManager.isBasemapLayer('tdt-vector')).toBe(true);
      expect(baseLayerManager.isBasemapLayer('tdt-vector-annotation')).toBe(true);
      expect(baseLayerManager.isBasemapLayer('tdt-satellite')).toBe(true);
    });

    it('应该正确识别百度地图底图图层', () => {
      expect(baseLayerManager.isBasemapLayer('baidu-normal')).toBe(true);
      expect(baseLayerManager.isBasemapLayer('baidu-satellite')).toBe(true);
    });

    it('应该正确识别高德地图底图图层', () => {
      expect(baseLayerManager.isBasemapLayer('amap-normal')).toBe(true);
      expect(baseLayerManager.isBasemapLayer('amap-satellite')).toBe(true);
    });

    it('应该正确识别当前活动底图', () => {
      expect(baseLayerManager.isBasemapLayer('tdt-vector')).toBe(true);
      expect(baseLayerManager.isBasemapLayer('tdt-vector-annotation')).toBe(true);
    });

    it('应该正确排除业务图层', () => {
      expect(baseLayerManager.isBasemapLayer('plot-features-layer-point')).toBe(false);
      expect(baseLayerManager.isBasemapLayer('device_pressure_clusters')).toBe(false);
      expect(baseLayerManager.isBasemapLayer('pipeline_layer_label')).toBe(false);
    });
  });

  describe('业务图层检测', () => {
    it('应该检测到所有静态预定义图层', () => {
      const topLayerIds = baseLayerManager.getTopLayerIds();
      
      // 标绘图层
      expect(topLayerIds).toContain('plot-features-layer-point');
      expect(topLayerIds).toContain('plot-features-layer-line');
      
      // 管网图层
      expect(topLayerIds).toContain('pipe-nodes-layer');
      expect(topLayerIds).toContain('pipe-lines-layer');
      
      // 设备图层
      expect(topLayerIds).toContain('device-layer');
      
      // 管线管点图层
      expect(topLayerIds).toContain('mvt_pipeNode');
      expect(topLayerIds).toContain('mvt_pipeLine');
    });

    it('应该动态检测MVT注记图层', () => {
      const topLayerIds = baseLayerManager.getTopLayerIds();
      
      expect(topLayerIds).toContain('pipeline_layer_label');
      expect(topLayerIds).toContain('water_system_label');
    });

    it('应该动态检测设备聚合图层', () => {
      const topLayerIds = baseLayerManager.getTopLayerIds();
      
      expect(topLayerIds).toContain('device_pressure_clusters');
      expect(topLayerIds).toContain('device_pressure_cluster_count');
      expect(topLayerIds).toContain('device_noise');
      expect(topLayerIds).toContain('device_flow_expanded_123');
    });

    it('应该检测到其他业务图层', () => {
      const topLayerIds = baseLayerManager.getTopLayerIds();
      
      expect(topLayerIds).toContain('custom_analysis_layer');
    });

    it('应该排除底图图层', () => {
      const topLayerIds = baseLayerManager.getTopLayerIds();

      expect(topLayerIds).not.toContain('tdt-vector');
      expect(topLayerIds).not.toContain('tdt-vector-annotation');
      expect(topLayerIds).not.toContain('baidu-normal');
    });

    it('应该确保管点图层在管线图层之上', () => {
      const topLayerIds = baseLayerManager.getTopLayerIds();

      const pipeLineIndex = topLayerIds.indexOf('mvt_pipeLine');
      const pipeNodeIndex = topLayerIds.indexOf('mvt_pipeNode');

      // 两个图层都应该存在
      expect(pipeLineIndex).toBeGreaterThanOrEqual(0);
      expect(pipeNodeIndex).toBeGreaterThanOrEqual(0);

      // 管点图层的索引应该大于管线图层（在数组中越靠后，图层越靠上）
      expect(pipeNodeIndex).toBeGreaterThan(pipeLineIndex);
    });
  });

  describe('图层存在性验证', () => {
    it('应该只返回实际存在的图层', () => {
      // 模拟某些图层不存在
      mockMap.getLayer.mockImplementation((id: string) => {
        if (id === 'plot-features-layer-polygon-fill' || 
            id === 'pipe-nodes-labels') {
          return undefined; // 图层不存在
        }
        return mockLayers.find(layer => layer.id === id);
      });

      const topLayerIds = baseLayerManager.getTopLayerIds();
      
      // 存在的图层应该被包含
      expect(topLayerIds).toContain('plot-features-layer-point');
      expect(topLayerIds).toContain('pipe-nodes-layer');
      
      // 不存在的图层应该被排除
      expect(topLayerIds).not.toContain('plot-features-layer-polygon-fill');
      expect(topLayerIds).not.toContain('pipe-nodes-labels');
    });
  });

  describe('边界情况测试', () => {
    it('应该处理空图层列表', () => {
      mockMap.getStyle.mockReturnValue({ layers: [] });
      
      const topLayerIds = baseLayerManager.getTopLayerIds();
      
      expect(topLayerIds).toEqual([]);
    });

    it('应该处理只有底图的情况', () => {
      mockMap.getStyle.mockReturnValue({ 
        layers: [
          { id: 'tdt-vector', type: 'raster' },
          { id: 'tdt-vector-annotation', type: 'symbol' }
        ] 
      });
      
      const topLayerIds = baseLayerManager.getTopLayerIds();
      
      expect(topLayerIds).toEqual([]);
    });

    it('应该处理复杂的设备图层命名', () => {
      const complexLayers = [
        { id: 'device_complex_name_clusters', type: 'circle' },
        { id: 'device_with_underscore_cluster_count', type: 'symbol' },
        { id: 'device_test_expanded_456', type: 'symbol' },
        { id: 'device_simple', type: 'symbol' }
      ];
      
      mockMap.getStyle.mockReturnValue({ layers: complexLayers });
      mockMap.getLayer.mockImplementation((id: string) => {
        return complexLayers.find(layer => layer.id === id);
      });
      
      const topLayerIds = baseLayerManager.getTopLayerIds();
      
      expect(topLayerIds).toContain('device_complex_name_clusters');
      expect(topLayerIds).toContain('device_with_underscore_cluster_count');
      expect(topLayerIds).toContain('device_test_expanded_456');
      expect(topLayerIds).toContain('device_simple');
    });
  });
});
