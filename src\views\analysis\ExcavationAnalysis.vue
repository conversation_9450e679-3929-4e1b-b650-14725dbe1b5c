/**
 * @Description: 开挖分析组件 - 基于三维场景的开挖剖切分析功能
 * @Date: 2025-01-10
 * @Author: 项目开发团队
 * @LastEditTime: 2025-01-10
 * @Features: 
 * - 仅支持三维(Cesium)环境
 * - 多边形绘制功能
 * - 凹凸多边形自动处理
 * - 开挖深度可配置
 * - 简化的坡度开挖实现（方案二）
 * - 开挖面可视化
 */
<template>
  <page-card class="tabulate-sta" :close-icon="false" title="开挖分析">
    <!-- 输入区域 -->
    <el-row class="input-section">
      <el-text class="label-text">开挖深度：</el-text>
      <div class="input-container">
          <el-input-number
            v-model="excavationDepth"
            placeholder="请输入开挖深度"
            :min="1"
            :max="500"
            :step="10"
            controls-position="right"
            class="requirement-input"
          />
          <span class="unit-text">(m)</span>
        </div>
    </el-row>
    <el-row class="input-section">
      <el-text class="label-text">坡度角度：</el-text>
      <div class="input-container">
          <el-input-number
            v-model="slopeAngle"
            placeholder="请输入坡度角度"
            :min="30"
            :max="90"
            :step="5"
            controls-position="right"
            class="requirement-input"
          />
          <span class="unit-text">(°)</span>
        </div>
        <div class="slope-tips">
          <span class="tip-text">90°为垂直开挖，角度越小坡度越缓</span>
        </div>
    </el-row>
    <div class="excavation-analysis-container">
      <!-- 使用绘制按钮组件进行多边形绘制 -->
      <BufferAnalysisDrawButtons
        ref="bufferAnalysisDrawButtonsRef"
        :map-engine="mapEngine"
        :config="bufferDrawConfig"
        @draw-start="handleDrawStart"
        @draw-complete="handleDrawComplete"
        @draw-error="handleDrawError"
        @clear-all="handleClearAll"
      />
    </div>
  </page-card>
</template>

<script lang="ts" setup>
import { ref, computed, onUnmounted } from 'vue';
import { ElInputNumber, ElMessage } from 'element-plus';
import BufferAnalysisDrawButtons from '@/components/BufferAnalysisDrawButtons.vue';
import type { 
  DrawType, 
  MapEngineType, 
  DrawResult, 
  BufferDrawConfig 
} from '@/components/BufferAnalysisDrawButtons.vue';
import { AppCesium } from '@/lib/cesium/AppCesium';

const bufferAnalysisDrawButtonsRef = ref<InstanceType<typeof BufferAnalysisDrawButtons>>();

// 开挖参数
const excavationDepth = ref(50); // 开挖深度（米）
const slopeAngle = ref(90); // 坡度角度（度）

/**
 * @description 计算地图引擎类型 - 开挖分析仅支持三维(Cesium)
 */
const mapEngine = computed((): MapEngineType => {
  return 'cesium';
});

/**
 * @description 开挖分析绘制配置
 */
const bufferDrawConfig: Partial<BufferDrawConfig> = {
  // UI配置
  buttonSize: 'default',
  buttonLayout: 'horizontal',
  buttonSpacing: 8,
  // 功能配置
  enabledDrawTypes: ['polygon'],
  showTips: true,
  showResult: false,
  showResultDetails: false,
  // 绘制配置
  clearPreviousDrawing: true,
  autoSwitchToSelect: true
};

// 组件状态
const currentResult = ref<DrawResult | null>(null);

// 开挖分析图层管理
let cesiumExcavationLayer: any = null;

/**
 * @description 判断多边形是否为凹多边形
 * @param {any[]} p - 多边形坐标数组
 * @returns {object} 判断结果 {isConcave: 1(凸) | -1(凹) | 0(不符合), delPoint: 凹点}
 */
const concave = (p: any[]) => {
  var n = p.length; // 顶点个数
  var j, k, z;
  var flag = 0;
  let delPoint;
  
  if (n < 3) {
    return {
      isConcave: 0,
      delPoint: delPoint
    };
  }
  
  for (var i = 0; i < n; i++) {
    j = (i + 1) % n;
    k = (i + 2) % n;
    z = (p[j].lng - p[i].lng) * (p[k].lat - p[j].lat);
    z -= (p[j].lat - p[i].lat) * (p[k].lng - p[j].lng);
    
    if (z < 0) {
      flag |= 1;
    } else if (z > 0) {
      flag |= 2;
    }
    
    if (flag == 3) {
      console.log('凹多边形，不符合要求');
      delPoint = p[j];
      return {
        isConcave: -1,
        delPoint: delPoint
      }; // CONCAVE
    }
  }
  
  if (flag != 0) {
    console.log('凸多边形');
    return {
      isConcave: 1,
      delPoint: delPoint
    }; // CONVEX
  } else {
    return {
      isConcave: 0,
      delPoint: delPoint
    };
  }
};

/**
 * @description 计算底部多边形坐标（方案二：简化的坡度计算）
 * @param {any[]} topPoints - 顶部多边形顶点
 * @param {number} depth - 开挖深度
 * @param {number} angle - 坡度角度（度）
 * @returns {any[]} 底部多边形顶点
 */
const calculateBottomPolygon = (topPoints: any[], depth: number, angle: number): any[] => {
  if (angle >= 90) {
    // 垂直开挖，底部和顶部相同
    return [...topPoints];
  }
  
  // 计算水平偏移量
  const radians = (90 - angle) * Math.PI / 180;
  const horizontalOffset = depth * Math.tan(radians);
  
  // 计算多边形中心点
  const centerLng = topPoints.reduce((sum, p) => sum + p.lng, 0) / topPoints.length;
  const centerLat = topPoints.reduce((sum, p) => sum + p.lat, 0) / topPoints.length;
  
  // 将每个顶点向中心点方向偏移
  return topPoints.map(point => {
    const vectorToCenter = {
      lng: centerLng - point.lng,
      lat: centerLat - point.lat
    };
    
    // 归一化向量
    const distance = Math.sqrt(vectorToCenter.lng * vectorToCenter.lng + vectorToCenter.lat * vectorToCenter.lat);
    if (distance === 0) return point;
    
    const normalizedVector = {
      lng: vectorToCenter.lng / distance,
      lat: vectorToCenter.lat / distance
    };
    
    // 应用偏移（转换为度数）
    const offsetInDegrees = horizontalOffset / 111000; // 粗略转换
    
    return {
      lng: point.lng + normalizedVector.lng * offsetInDegrees,
      lat: point.lat + normalizedVector.lat * offsetInDegrees
    };
  });
};

/**
 * @description 处理绘制开始事件
 * @param {DrawType} type - 绘制类型
 */
const handleDrawStart = (type: DrawType) => {
  console.log(`开挖分析绘制开始: ${type}`);
  currentResult.value = null;
};

/**
 * @description 处理绘制完成事件
 * @param {DrawResult} result - 绘制结果
 */
const handleDrawComplete = async (result: DrawResult) => {
  console.log('开挖分析绘制完成:', result);
  currentResult.value = result;
  
  // 检查坡度和深度的组合合理性
  const depth = Number(excavationDepth.value);
  const angle = Number(slopeAngle.value);
  
  //清除绘制的图形
  bufferAnalysisDrawButtonsRef.value?.clearAll();

  if (angle < 90) {
    const radians = (90 - angle) * Math.PI / 180;
    const maxOffset = depth * Math.tan(radians);
    if (maxOffset > 100) {
      ElMessage.warning(`当前坡度角度(${angle}°)和开挖深度(${depth}m)组合将产生${maxOffset.toFixed(1)}m的水平偏移，可能影响分析效果`);
    }
  }
  
  try {
    await performExcavationAnalysis(result.geometry);
    
    const slopeText = angle >= 90 ? '垂直开挖' : `${angle}°坡度开挖`;
    ElMessage.success(`${getDrawTypeLabel(result.type)}绘制完成，${slopeText}分析已完成`);
  } catch (error) {
    console.error('开挖分析失败:', error);
    ElMessage.error(`开挖分析失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

/**
 * @description 处理绘制错误事件
 * @param {object} error - 错误信息
 */
const handleDrawError = (error: { type: DrawType; message: string }) => {
  console.error('开挖分析绘制错误:', error);
  
  // 显示错误提示
  ElMessage.error(`${getDrawTypeLabel(error.type)}失败: ${error.message}`);
};

/**
 * @description 执行开挖分析
 * @param {any} geometry - 输入几何体
 */
const performExcavationAnalysis = async (geometry: any) => {
  if (!geometry) {
    throw new Error('几何体不能为空');
  }
  
  // 验证开挖参数
  const depth = Number(excavationDepth.value);
  const angle = Number(slopeAngle.value);
  
  if (!depth || depth <= 0 || depth > 500) {
    throw new Error('开挖深度必须在1-500米之间');
  }
  
  if (!angle || angle < 30 || angle > 90) {
    throw new Error('坡度角度必须在30-90度之间');
  }
  
  console.log(`执行开挖分析 - 深度: ${depth}米, 坡度角度: ${angle}度`);
  
  try {
    const { Cesium } = BC.Namespace;
    const viewer = AppCesium.getInstance().getViewer();
    
    // 转换GeoJSON坐标为开挖分析需要的格式
    let positions: any[] = [];
    if (geometry.type === 'Polygon') {
      const coordinates = geometry.coordinates[0]; // 取外环
      positions = coordinates.slice(0, -1).map((coord: number[]) => ({
        lng: coord[0],
        lat: coord[1]
      }));
    } else {
      throw new Error('开挖分析只支持多边形');
    }
    
    // 判断是否为凸多边形，如果是凹多边形，删掉内凹的点
    const { isConcave, delPoint } = concave(positions);
    let points: any[] = [];
    
    if (isConcave === 1) {
      points = positions;
    } else if (isConcave === -1) {
      // 移除凹点
      points = positions.filter((p) => p.lng !== delPoint.lng);
      console.log('检测到凹多边形，已移除凹点');
    } else {
      throw new Error('多边形不符合要求');
    }
    
    // 获取第一个点的高度
    const firstPosition = points[0];
    const clampPosition = viewer.scene.clampToHeight(
      Cesium.Cartesian3.fromDegrees(firstPosition.lng, firstPosition.lat)
    );
    const height = Cesium.Cartographic.fromCartesian(clampPosition).height;
    
    console.log(`地面高度: ${height}米, 开挖深度: ${depth}米, 坡度角度: ${angle}度`);
    
    // 执行简化的剖切操作
    await performSimplifiedClipping(points, height, depth, angle);
    
    // 创建开挖可视化效果（方案二）
    await createSimplifiedExcavationVisualization(points, height, depth, angle);
    
    console.log('开挖分析完成');
  } catch (error) {
    console.error('开挖分析失败:', error);
    throw error;
  }
};

/**
 * @description 处理清空事件
 */
const handleClearAll = () => {
  console.log('清空所有绘制');
  
  // 清除开挖分析结果
  clearExcavationResults();
  
  currentResult.value = null;
  
  ElMessage.info('已清空所有绘制结果');
};

/**
 * @description 执行简化的剖切操作（方案二）
 * @param {any[]} points - 多边形点数组
 * @param {number} height - 地面高度
 * @param {number} depth - 开挖深度
 * @param {number} angle - 坡度角度
 */
const performSimplifiedClipping = async (points: any[], height: number, depth: number, angle: number) => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    
    // 清除之前的剖切
    if (viewer.scene.globe.clippingPlanes) {
      viewer.scene.globe.clippingPlanes.removeAll();
    }
    
    // 简化的图层剖切处理
    viewer.eachLayer((layer: any) => {
      if (layer.attr && layer.attr.type === 'reality') {
        const overlays = layer.getOverlaysByAttr('id', layer.attr.id);
        if (overlays && overlays.length > 0) {
          const tileset = overlays[0];

          const clipUtil = AppCesium.getInstance().getClipUtil() as BC.ClippingTileset
          const clipTerrain = AppCesium.getInstance().getClipTerrainUtil() as BC.ClippingTerrain
          clipUtil.clipTileset(tileset.delegate, points, true)
          clipTerrain.clipTerrain(points, true)
        }
      }
    }, this);
    
    console.log('简化剖切操作完成');
  } catch (error) {
    console.error('剖切操作失败:', error);
    throw error;
  }
};

/**
 * @description 创建简化的开挖可视化效果（方案二）
 * @param {any[]} points - 多边形点数组
 * @param {number} height - 地面高度
 * @param {number} depth - 开挖深度
 * @param {number} angle - 坡度角度
 */
const createSimplifiedExcavationVisualization = async (points: any[], height: number, depth: number, angle: number) => {
  try {
    const { Cesium } = BC.Namespace;
    const viewer = AppCesium.getInstance().getViewer();
    
    // 创建开挖分析图层
    if (!cesiumExcavationLayer) {
      cesiumExcavationLayer = new BC.VectorLayer('excavation-analysis-layer');
      viewer.addLayer(cesiumExcavationLayer);
    }
    
    // 清除之前的结果
    cesiumExcavationLayer.clear();
    
    // 计算底部多边形
    const bottomPoints = calculateBottomPolygon(points, depth, angle);
    
    if (angle >= 90) {
      // 垂直开挖 - 使用简单的拉伸多边形
      const wallPolygon = new BC.Polygon(points);
      wallPolygon.setStyle({
        extrudedHeight: height + 3,
        height: height - depth,
        closeTop: false,
        closeBottom: false,
        perPositionHeight: false,
        material: new Cesium.ImageMaterialProperty({
          image: 'images/wall.jpg',
          repeat: new BC.Cartesian2(20, 100)
        })
      });
      
      cesiumExcavationLayer.addOverlay(wallPolygon);
    } else {
      // 坡度开挖 - 方案二：使用perPositionHeight创建倾斜墙面
      await createSlopeWallWithPerPositionHeight(points, bottomPoints, height, depth, cesiumExcavationLayer);
    }
    
    // 创建底面多边形
    const bottomPolygon = new BC.Polygon(bottomPoints);
    bottomPolygon.setStyle({
      height: height - depth,
      material: new Cesium.ImageMaterialProperty({
        image: 'images/bottom.jpg',
        repeat: new BC.Cartesian2(100, 5)
      }),
      perPositionHeight: false,
      outline: true,
      outlineColor: BC.Color.fromCssColorString('#fafa5a').withAlpha(0.4),
      outlineWidth: 1
    });
    
    cesiumExcavationLayer.addOverlay(bottomPolygon);
    
    console.log('简化开挖可视化效果创建完成');
  } catch (error) {
    console.error('开挖可视化效果创建失败:', error);
    throw error;
  }
};

/**
 * @description 使用perPositionHeight创建倾斜墙面（方案二核心实现）
 * @param {any[]} topPoints - 顶部多边形顶点
 * @param {any[]} bottomPoints - 底部多边形顶点
 * @param {number} height - 地面高度
 * @param {number} depth - 开挖深度
 * @param {any} layer - Cesium图层对象
 */
const createSlopeWallWithPerPositionHeight = async (
  topPoints: any[],
  bottomPoints: any[],
  height: number,
  depth: number,
  layer: any
) => {
  try {
    const { Cesium } = BC.Namespace;
    
    // 为墙面的每个边创建四边形
    for (let i = 0; i < topPoints.length; i++) {
      const nextIndex = (i + 1) % topPoints.length;
      
      // 创建四边形的四个顶点，使用不同的高度
      const positions = [
        // 顶部边的两个点（地面高度）
        new BC.Position(topPoints[i].lng, topPoints[i].lat, height),
        new BC.Position(topPoints[nextIndex].lng, topPoints[nextIndex].lat, height),
        // 底部边的两个点（开挖深度）
        new BC.Position(bottomPoints[nextIndex].lng, bottomPoints[nextIndex].lat, height - depth),
        new BC.Position(bottomPoints[i].lng, bottomPoints[i].lat, height - depth)
      ];
      
      const wallPolygon = new BC.Polygon(positions);
      wallPolygon.setStyle({
        perPositionHeight: true, // 关键：允许每个顶点有不同高度
        material: new Cesium.ImageMaterialProperty({
          image: '/images/wall.jpg',
          repeat: new BC.Cartesian2(5, 5)
        }),
        outline: false,
        closeTop: false,
        closeBottom: false
      });
      
      layer.addOverlay(wallPolygon);
    }
    
    console.log('倾斜墙面创建完成');
  } catch (error) {
    console.error('创建倾斜墙面失败:', error);
  }
};

/**
 * @description 清除开挖分析结果
 */
const clearExcavationResults = () => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    
    // 清除剖切平面
    if (viewer.scene.globe.clippingPlanes) {
      viewer.scene.globe.clippingPlanes.removeAll();
    }
    
    // 清除图层剖切
    viewer.eachLayer((layer: any) => {
      if (layer.attr && layer.attr.type === 'reality') {
        const overlays = layer.getOverlaysByAttr('id', layer.attr.id);
        if (overlays && overlays.length > 0) {
          const tileset = overlays[0];
          if (tileset && tileset.delegate && tileset.delegate.clippingPlanes) {
            tileset.delegate.clippingPlanes.removeAll();
          }
        }
      }
    }, this);
    
    // 清除开挖分析图层
    if (cesiumExcavationLayer) {
      cesiumExcavationLayer.clear();
      viewer.removeLayer(cesiumExcavationLayer);
      cesiumExcavationLayer = null;
    }
    
    console.log('开挖分析结果已清除');
  } catch (error) {
    console.error('清除开挖分析结果失败:', error);
  }
};

/**
 * @description 获取绘制类型标签
 * @param {DrawType} type - 绘制类型
 * @returns {string} 类型标签
 */
const getDrawTypeLabel = (type: DrawType): string => {
  const labels = {
    'point': '点绘制',
    'linestring': '线段绘制',
    'polygon': '多边形绘制',
    'rectangle': '矩形绘制'
  };
  return labels[type] || type;
};

/**
 * @description 组件卸载时清理资源
 */
onUnmounted(() => {
  console.log('开挖分析组件卸载，开始清理资源');
  
  // 清除开挖分析结果
  clearExcavationResults();
  
  console.log('开挖分析组件资源清理完成');
});
</script>

<style lang="scss" scoped>
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  // width: 550px;
  min-height: 120px;
  z-index: 1000;
}
.input-section {
  margin-bottom: 16px;
  .label-text {
    font-weight: 500;
    color: #606266;
    margin-bottom: 8px;
    display: block;
    
  }
  
  .input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 350px;
    
    .requirement-input {
      flex: 1;
    }
    
    .unit-text {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .slope-tips {
    margin-top: 4px;
    width: 100%;
    
    .tip-text {
      color: #909399;
      font-size: 12px;
      line-height: 1.2;
    }
  }
}

.excavation-analysis-container {
  padding: 8px;
}

/* 开挖分析专用样式 */
</style>