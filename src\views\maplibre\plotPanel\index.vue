<!--
 * @Description: 标绘面板
 * @Date: 2022-04-22 08:54:28
 * @Author: GISerZ
 * @LastEditor: 项目开发团队
 * @LastEditTime: 2023-08-10 15:00:00
-->
<template>
  <custom-card
    @closeHandler="close"
    v-show="mainItem.show"
    width="45%"
    :main-item="mainItem"
    :title="mainItem.title ?? (mainItem.title = '标绘管理')"
  >
    <el-row class="custom-card-search-row">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-dropdown
            trigger="click"
            class="plot-add"
            popper-class="custom-dropdown"
            type="primary"
            @command="addPlot"
          >
            <el-button
              >新增<el-icon class="el-icon--right"> <ArrowDown /> </el-icon
            ></el-button>
            <template #dropdown>
              <el-dropdown-menu class="custom-dropdown-menu">
                <el-dropdown-item
                  :command="item.value"
                  v-for="item in plotTypes"
                  >{{ item.label }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-col>
        <!-- <el-col :span="12" style="text-align: right;">
          <el-button
            type="success"
            :disabled="tableData.length === 0"
            @click="shareAllPlots"
            >批量分享</el-button
          >
        </el-col> -->
      </el-row>
    </el-row>

    <div class="table-vis-bd">
      <el-table
        :row-class-name="tableRowClassName"
        :data="tableData"
        style="width: 100%"
        height="100%"
        class="routeCt"
      >
        <el-table-column prop="name" label="标绘名称" min-width="90" />
        <el-table-column prop="type" label="标绘类型">
          <template v-slot="scope">
            <div v-if="scope.row.type === 'Point'">点</div>
            <div v-if="scope.row.type === 'Polyline'">线</div>
            <div v-if="scope.row.type === 'Polygon'">多边形</div>
            <div v-if="scope.row.type === 'Rectangle'">矩形</div>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" min-width="90" />
        <el-table-column prop="remark" label="标绘描述" />
        <el-table-column label="操作" min-width="90">
          <template v-slot="scope">
            <el-button
              @click="locParcel(scope.row)"
              type="text"
              class="route_oth_btn"
              >定位</el-button
            >
            <!-- <el-button
              @click="editPlot(scope.row)"
              class="route_oth_btn"
              type="text"
              >编辑</el-button
            > -->
            <el-button
              class="route_dlt"
              type="text"
              @click="deletePlot(scope.row)"
              >删除</el-button
            >
            <el-button
              class="route_share_btn"
              type="text"
              @click="sharePlot(scope.row)"
              >分享</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex justify-between items-center">
      <div>
        <div class="font-size-3.5 color-#323233">共{{ total }}条数据</div>
      </div>
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :total="total"
        :pager-count="5"
        layout="prev, pager, next, jumper"
        class="pagination"
        background
        small
      ></el-pagination>
    </div>
  </custom-card>
</template>

<script lang="ts" setup>
import { ref, type Ref, onMounted } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";
import CustomCard from "@/components/dialog/CustomCard.vue";
import { plotTypes } from "./index";
import { dayjs, ElMessage, ElMessageBox } from "element-plus";
import { useDialogStore } from "@/stores/Dialogs";
import { DrawBar, DrawModule } from "./DrawModule";
import { PlotDataManager } from "@/lib/maplibre/layer/PlotDataManager";
import type { PlotFeature } from "@/lib/maplibre/layer/types/LayerTypes";
/**
 * 组件属性定义
 */
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});

const close = () => {
  useDialogStore().closeDialog("PlotPanel");
};

const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
const locParcel = (row: any) => {
  try {
    if (!row.geometry) {
      ElMessage.warning("该要素没有几何数据，无法定位");
      return;
    }

    // 获取DrawModule实例并定位
    const drawModule = DrawModule.getInstance();
    const featureLayer = drawModule["featureLayer"]; // 访问私有属性

    if (featureLayer) {
      featureLayer.zoomToFeature(row.id);
      ElMessage.success(`已定位到 "${row.name}"`);
    } else {
      ElMessage.warning("地图图层未初始化，无法定位");
    }
  } catch (error) {
    console.error("定位要素失败:", error);
    ElMessage.error("定位失败");
  }
};

onMounted(() => {
  // 初始化分页参数
  pageSize.value = searchForm.value.pageSize;
  currentPage.value = searchForm.value.pageNum;

  getlist();
});

// 数据管理器实例
const plotDataManager = PlotDataManager.getInstance();

const SearchForm = class {
  modifiedStartDate = "";
  modifiedEndDate = "";
  createEndDate = "";
  createStartDate = "";
  constructor(
    public name = "",
    public type = "",
    public pageNum = 1,
    public pageSize = 10,
    public projectType = 0,
    public createDateInterval = [],
    public editDateInterval = []
  ) {}
};
const searchForm = ref(new SearchForm());

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);
const pageSizes = ref([5, 10, 20, 50]);
const total = ref(0);

// 分页事件处理函数
/**
 * 处理页码变更
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  searchForm.value.pageNum = page;
  getlist();
};

/**
 * 处理页面大小变更
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  searchForm.value.pageSize = size;
  currentPage.value = 1;
  searchForm.value.pageNum = 1;
  getlist();
};

const tableData: Ref<any[]> = ref([]);
const tableSize = ref(0);

/**
 * @description 从数据库获取标绘数据列表
 */
const getlist = async (data?: any) => {
  try {
    // 构建查询参数
    const queryParams = {
      pageNum: searchForm.value.pageNum,
      pageSize: searchForm.value.pageSize,
      // 这里可以添加其他查询条件
      name: data?.name || "",
      type: data?.type || "",
    };

    // 从数据库获取分页数据
    const pageResult = await plotDataManager.getFeatures(queryParams);
    // 转换数据格式以适配表格显示
    let filteredFeatures = pageResult.features.map(transformFeatureForTable);
    // 更新分页状态
    total.value = pageResult.total || 0;
    currentPage.value = searchForm.value.pageNum;
    tableSize.value = pageResult.total || 0;
    tableData.value = filteredFeatures || [];
  } catch (error) {
    console.error("获取标绘数据失败:", error);
    ElMessage.error("获取标绘数据失败");
    tableData.value = [];
    tableSize.value = 0;
    total.value = 0;
  }
};

/**
 * @description 转换要素数据为表格显示格式
 */
const transformFeatureForTable = (feature: PlotFeature) => {
  return {
    id: feature.id,
    name: feature.name,
    type: getFeatureTypeLabel(feature.geojson.properties.geometryType),
    rawType: feature.geojson.properties.geometryType,
    updateTime: feature.updateTime || "--",
    remark: feature.remark || "无描述",
    geometry: feature.geojson.geometry,
  };
};

/**
 * @description 获取要素类型的中文标签
 */
const getFeatureTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    point: "点",
    linestring: "线",
    polygon: "多边形",
    rectangle: "矩形",
  };
  return typeMap[type] || type;
};

const addPlot = (val: any) => {
  DrawModule.showChange.next(new DrawBar(true, `Draw${val}Panel`));
  useDialogStore().addDialog({
    name: "标绘新增",
    path: "DrawPanel",
    type: "maplibre",
  });
  useDialogStore().closeDialog("PlotPanel");
};

const editPlot = async (row: any) => {
  try {
    // 从数据库获取完整的要素数据
    const feature = await plotDataManager.getFeature(row.id);
    if (!feature) {
      ElMessage.error("要素数据不存在");
      return;
    }

    // 确定面板类型 - 使用类型映射确保正确的组件名称
    const componentType = getComponentTypeFromFeatureType(
      feature.geojson.properties.geometryType
    );
    const panelType = `Draw${componentType}Panel`;

    // 获取DrawModule实例并先设置编辑状态
    const drawModule = DrawModule.getInstance();

    try {
      // 先启动编辑模式，设置当前要素
      await drawModule.startEdit(feature);

      // 设置面板状态为编辑模式，同时传递要素数据
      DrawModule.showChange.next(
        new DrawBar(true, panelType, "标绘修改", false, feature)
      );

      // 关闭当前面板
      useDialogStore().closeDialog("PlotPanel");

      // 打开编辑面板
      useDialogStore().addDialog({
        name: "标绘修改",
        path: "DrawPanel",
        type: "maplibre",
      });

      ElMessage.info("图形已进入编辑状态，可拖拽锚点修改或重新绘制");
    } catch (error) {
      console.error("启动编辑模式失败:", error);
      ElMessage.error("启动编辑模式失败");
    }
  } catch (error) {
    console.error("编辑要素失败:", error);
    ElMessage.error("编辑要素失败");
  }
};

/**
 * @description 首字母大写
 */
const capitalizeFirst = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * @description 将要素类型映射到对应的面板组件类型
 */
const getComponentTypeFromFeatureType = (featureType: string): string => {
  const typeMapping: Record<string, string> = {
    point: "Point",
    linestring: "Polyline", // 关键修复：linestring -> Polyline
    polygon: "Polygon",
    rectangle: "Rectangle",
  };

  const componentType = typeMapping[featureType.toLowerCase()];
  if (!componentType) {
    return capitalizeFirst(featureType);
  }

  return componentType;
};

const deletePlot = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除标绘 "${row.name}" 吗？`,
      "删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    const drawModule = DrawModule.getInstance();
    const success = await drawModule.deleteFeature(row.id);

    if (success) {
      ElMessage.success("删除成功");
      getlist(); // 刷新列表
    } else {
      ElMessage.error("删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除要素失败:", error);
      ElMessage.error("删除要素失败");
    }
  }
};

/**
 * @description 分享标绘要素 - 导出为GeoJSON文件
 * @param row 标绘要素数据
 */
const sharePlot = async (row: any) => {
  try {
    if (!row.geometry && !row.geojson) {
      ElMessage.warning('该要素没有几何数据，无法分享');
      return;
    }

    // 构建GeoJSON数据
    const geojsonData = createGeoJSONFromPlot(row);

    // 生成文件名
    const fileName = `${row.name || '标绘要素'}_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.geojson`;

    // 下载文件
    downloadGeoJSON(geojsonData, fileName);

    // ElMessage.success(`标绘 "${row.name}" 已导出为GeoJSON文件`);
  } catch (error) {
    console.error('分享标绘要素失败:', error);
    ElMessage.error('分享失败');
  }
};

/**
 * @description 从标绘要素创建标准GeoJSON数据
 * @param plotFeature 标绘要素数据
 * @returns GeoJSON FeatureCollection
 */
const createGeoJSONFromPlot = (plotFeature: any) => {
  // 基础GeoJSON Feature结构
  let feature: any = {
    type: 'Feature',
    properties: {
      name: plotFeature.name || '',
      description: plotFeature.remark || '',
      type: plotFeature.type || '',
      updateTime: plotFeature.updateTime || dayjs().format('YYYY-MM-DD HH:mm:ss'),
      // 保留原始样式信息（如果存在）
      style: plotFeature.geojson?.properties?.style || {}
    },
    geometry: null
  };

  // 处理几何数据
  if (plotFeature.geojson && plotFeature.geojson.geometry) {
    // 如果有标准的geojson格式数据
    feature.geometry = plotFeature.geojson.geometry;

    // 合并原有的properties
    if (plotFeature.geojson.properties) {
      feature.properties = {
        ...feature.properties,
        ...plotFeature.geojson.properties
      };
    }
  } else if (plotFeature.geometry) {
    // 如果只有geometry数据
    feature.geometry = plotFeature.geometry;
  } else {
    throw new Error('无效的几何数据');
  }

  // 创建FeatureCollection
  const geojsonCollection = {
    type: 'FeatureCollection',
    name: plotFeature.name || '标绘要素',
    crs: {
      type: 'name',
      properties: {
        name: 'urn:ogc:def:crs:OGC:1.3:CRS84'
      }
    },
    features: [feature],
    // metadata: {
    //   title: plotFeature.name || '标绘要素',
    //   description: plotFeature.remark || '',
    //   exportTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    //   exportBy: '管网分析系统',
    //   version: '1.0'
    // }
  };

  return geojsonCollection;
};

/**
 * @description 下载GeoJSON数据为文件
 * @param geojsonData GeoJSON数据对象
 * @param fileName 文件名
 */
const downloadGeoJSON = (geojsonData: any, fileName: string) => {
  try {
    // 将数据转换为JSON字符串，格式化输出
    const jsonString = JSON.stringify(geojsonData, null, 2);

    // 创建Blob对象
    const blob = new Blob([jsonString], {
      type: 'application/geo+json;charset=utf-8'
    });

    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log(`✅ GeoJSON文件已下载: ${fileName}`);
  } catch (error) {
    console.error('下载GeoJSON文件失败:', error);
    throw error;
  }
};

/**
 * @description 批量分享所有标绘要素 - 导出为单个GeoJSON文件
 */
const shareAllPlots = async () => {
  try {
    if (!tableData.value || tableData.value.length === 0) {
      ElMessage.warning('没有可分享的标绘要素');
      return;
    }

    // 确认操作
    await ElMessageBox.confirm(
      `确定要导出所有 ${tableData.value.length} 个标绘要素吗？`,
      '批量分享确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    );

    // 创建包含所有要素的GeoJSON FeatureCollection
    const features: any[] = [];
    const validFeatures: any[] = [];

    // 处理每个要素
    tableData.value.forEach((plotFeature: any) => {
      try {
        if (plotFeature.geometry || plotFeature.geojson) {
          const geojsonData = createGeoJSONFromPlot(plotFeature);
          // 提取feature并添加到数组中
          if (geojsonData.features && geojsonData.features.length > 0) {
            features.push(geojsonData.features[0]);
            validFeatures.push(plotFeature);
          }
        }
      } catch (error) {
        console.warn(`跳过无效要素 "${plotFeature.name}":`, error);
      }
    });

    if (features.length === 0) {
      ElMessage.warning('没有有效的几何数据可以导出');
      return;
    }

    // 创建完整的FeatureCollection
    const completeGeoJSON = {
      type: 'FeatureCollection',
      name: '标绘要素集合',
      crs: {
        type: 'name',
        properties: {
          name: 'urn:ogc:def:crs:OGC:1.3:CRS84'
        }
      },
      features: features,
      // metadata: {
      //   title: '标绘要素集合',
      //   description: `包含 ${features.length} 个标绘要素`,
      //   totalFeatures: features.length,
      //   exportTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      //   exportBy: '管网分析系统',
      //   version: '1.0',
      //   featureTypes: getFeatureTypesStatistics(validFeatures)
      // }
    };

    // 生成文件名
    const fileName = `标绘要素集合_${features.length}个要素_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.geojson`;

    // 下载文件
    downloadGeoJSON(completeGeoJSON, fileName);

    // ElMessage.success(`已成功导出 ${features.length} 个标绘要素到GeoJSON文件`);
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量分享标绘要素失败:', error);
      ElMessage.error('批量分享失败');
    }
  }
};

/**
 * @description 获取要素类型统计信息
 * @param features 要素数组
 * @returns 类型统计对象
 */
const getFeatureTypesStatistics = (features: any[]) => {
  const stats: Record<string, number> = {};

  features.forEach(feature => {
    const type = feature.type || '未知';
    stats[type] = (stats[type] || 0) + 1;
  });

  return stats;
};


// 开发环境下暴露调试方法到全局
if (import.meta.env.DEV) {
  (window as any).plotDataManager = plotDataManager;
}
</script>

<style lang="scss" scoped>
.route_dlt {
  color: #ff7373;
}

.route_oth_btn {
  font-size: 14px;
  color: #1966ff;
}

.route_share_btn {
  font-size: 14px;
  color: #67c23a;

  &:hover {
    color: #529b2e;
  }
}

.pagination {
  margin-top: 15px;
  justify-content: end;
  display: flex;
}

// 批量分享按钮样式
.custom-card-search-row {
  .el-button {
    &[type="success"] {
      background-color: #67c23a;
      border-color: #67c23a;

      &:hover {
        background-color: #529b2e;
        border-color: #529b2e;
      }

      &:disabled {
        background-color: #a0cfff;
        border-color: #a0cfff;
        color: #ffffff;
      }
    }
  }
}
</style>
