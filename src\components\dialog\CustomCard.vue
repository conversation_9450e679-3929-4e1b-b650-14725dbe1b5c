<!--
 * @Description:
 * @Date: 2022-09-28 15:37:59
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-11-03 11:18:36
-->
<template>
  <div
    ref="dialogRef"
    :style="cardStyle"
    class="custom-earth-card"
    :class="[
      mainItem?.isFocus ? 'card-z-index' : '',
      showHeader ? '' : 'right-box',
    ]"
  >
    <div ref="headerRef" class="card-header" v-if="showHeader">
      <div class="left-txt">
        <!-- <el-icon><DArrowRight /></el-icon> -->
        <span>{{ mainItem?.title ? mainItem.title : title }}</span>
      </div>
      <div class="card-handle-icon">
        <el-icon @click="closeHandler" class="card-close-icon">
          <close-bold />
        </el-icon>
      </div>
    </div>
    <div
      class="custom_card_body"
      :class="[showHeader ? '' : 'custom_card_body_no_header']"
    >
      <slot></slot>
    </div>
  </div>
  <!-- <div class="filter-box"></div> -->
</template>

<script setup lang="ts">
import { CloseBold } from "@element-plus/icons-vue";
import { useDraggable } from "./useDraggable";
import { computed, ref, defineProps, defineEmits } from "vue";
const dialogRef = ref<HTMLElement>();
const headerRef = ref<HTMLElement>();
const draggable = ref(true);
const draggableStore = useDraggable(dialogRef, headerRef, draggable);
const props = defineProps({
  width: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "",
  },
  id: {
    type: [String, Number],
  },
  close: {
    type: Function,
  },
  mainItem: {
    type: Object,
    default: {} as any,
  },
  isMiniIcon: {
    type: Boolean,
    default: () => true,
  },
  showFullScreenIcon: {
    type: Boolean,
    default: () => false,
  },
  right: {
    type: String,
    default: "100px",
  },
  left: {
    type: String,
    default: () => "unset",
  },
  top: {
    type: String,
    default: "70px",
  },
  showHeader: {
    type: Boolean,
    default: true,
  },

  showReadSwitch: {
    type: Boolean,
    default: false,
  },
  showImportSwitch: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["closeHandler"]);
const drawerStore = useDrawerStore();
const { drawerView } = storeToRefs(drawerStore);
console.log(drawerView.value, "....");
const route = useRoute();
// console.log(route.);
("CesiumHome");
const cardStyle = computed(() => {
  draggable.value = true;
  draggableStore.clearTransform();
  return {
    width: props.width,
    // height: 'max-content',
    left: route.name === "CesiumHome" ? "unset" : props.left,
    right:
      route.name === "CesiumHome" && drawerView.value ? "510px" : props.right,
    top: route.name === "CesiumHome" ? "16px" : props.top,
    "z-index": 1,
  };
});

const closeHandler = () => {
  emit("closeHandler");
};
</script>

<style lang="scss" scoped>
.base-position {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.right-box {
  left: initial !important;
  right: v-bind(right);
}

.custom-earth-card {
  box-sizing: border-box;
  position: absolute;
  border-radius: 4px;
  border-radius: 6px;
  box-shadow: 0px 0px 12px 0px rgba(169, 190, 232, 0.4);
  background: rgb(255, 255, 255);
  overflow: hidden;
  z-index: 5;
  pointer-events: auto;
  .card-header {
    position: relative;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border-bottom: 1px solid #eeeeee;
    border-radius: 2px 2px 0px 0px;
    padding: 0 20px;

    .left-txt {
      display: flex;
      align-items: center;
      z-index: 2;
    }

    span {
      font: 400 16px Source-CN-Bold;
      color: #2c3037;
    }

    .card-handle-icon {
      display: flex;
      align-items: center;
      column-gap: 8px;
      cursor: pointer;
      color: #a8b5d8;
      font-size: 18px;
      z-index: 2;
    }
  }

  .custom_card_body {
    box-sizing: border-box;
    padding: 20px;
    overflow-y: auto;
    border-top: unset;
  }

  .custom_card_body::-webkit-scrollbar {
    width: 5px;
    height: 4px;
    border-radius: 10px;
  }

  .custom_card_body::-webkit-scrollbar-thumb {
    width: 5px;
    background-color: #b8b8b8a6;
    border-radius: 10px;
  }

  .custom_card_body_no_header {
    padding: 10px;
  }

  :deep(.custom-card-search-row) {
    padding: 0 10px;

    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.layer-position {
  right: -1450px !important;
}

.full-screen-icon {
  width: 16px;
  height: 16px;
}
</style>
