<!--
 * @Description: SHP导入面板
 * @Date: 2024-01-16
 * @Author: AI Assistant
 * @LastEditTime: 2024-01-16
 -->
<template>
  <page-card class="shp-import-panel" title="SHP数据导入" @closeCard="closeCard">
    <!-- 操作工具栏 -->
    <div class="toolbar">
      <el-button 
        type="primary" 
        :icon="Download"
        @click="handleTemplateDownload"
        size="default"
      >
        下载模板
      </el-button>
      
      <el-button 
        type="success" 
        :icon="Upload"
        @click="handleImport"
        :loading="isImporting"
        :disabled="!hasValidFiles || isImporting"
        size="default"
      >
        {{ isImporting ? '导入中...' : '开始导入' }}
      </el-button>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-section">
      <el-upload
        class="shp-upload"
        drag
        multiple
        :auto-upload="false"
        :show-file-list="false"
        accept=".shp,.shx,.dbf,.prj,.cpg"
        :on-change="handleFileChange"
        :before-upload="() => false"
      >
        <el-icon class="el-icon--upload">
          <FolderOpened />
        </el-icon>
        <div class="el-upload__text">
          拖拽或点击选择SHP文件包<br>
          <span class="upload-hint">(需包含.shp、.shx、.dbf文件)</span>
        </div>
      </el-upload>
      
      <!-- 简化的文件状态 -->
      <div v-if="selectedFiles.length > 0" class="file-status">
        <el-text 
          :type="hasValidFiles ? 'success' : 'warning'" 
          size="small"
        >
          {{ statusMessage }}
        </el-text>
      </div>
    </div>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElButton, ElUpload, ElIcon, ElText } from 'element-plus';
import { Download, Upload, FolderOpened } from '@element-plus/icons-vue';
import PageCard from '@/components/PageCard.vue';
import ImportFileApi, { ImportFileUtils } from '@/api/importFile';
import type { UploadFile, UploadFiles } from 'element-plus';

/**
 * @interface Emits
 * @description 组件事件接口
 */
interface Emits {
  (e: 'close'): void;
}

const emit = defineEmits<Emits>();

// ============ 响应式状态 ============

/** 导入状态 */
const isImporting = ref<boolean>(false);

/** 选中的文件列表 */
const selectedFiles = ref<File[]>([]);

// ============ 计算属性 ============

/** 是否有有效文件 */
const hasValidFiles = computed<boolean>(() => {
  if (selectedFiles.value.length === 0) return false;
  const validation = ImportFileUtils.validateShpFiles(selectedFiles.value);
  return validation.valid;
});

/** 状态消息 */
const statusMessage = computed<string>(() => {
  if (selectedFiles.value.length === 0) return '';
  
  const validation = ImportFileUtils.validateShpFiles(selectedFiles.value);
  if (validation.valid) {
    return `✓ 已选择 ${selectedFiles.value.length} 个文件，验证通过`;
  } else {
    return `⚠ ${validation.message}`;
  }
});

// ============ 方法定义 ============

/**
 * @function handleTemplateDownload
 * @description 处理模板文件下载
 */
const handleTemplateDownload = (): void => {
  try {
    // 创建下载链接
    const link = document.createElement('a');
    link.href = '/data/template/shp_import_template.zip';
    link.download = 'shp_import_template.zip';
    link.style.display = 'none';
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    ElMessage.success('模板文件下载已开始');
  } catch (error) {
    console.error('模板下载失败:', error);
    ElMessage.error('模板下载失败，请重试');
  }
};

/**
 * @function handleFileChange
 * @description 处理文件选择变化
 */
const handleFileChange = (uploadFile: UploadFile, uploadFiles: UploadFiles): void => {
  try {
    // 转换为File数组
    const files = uploadFiles
      .filter(file => file.raw)
      .map(file => file.raw as File);
    
    selectedFiles.value = files;
    
  } catch (error) {
    console.error('文件处理失败:', error);
    ElMessage.error('文件处理失败');
  }
};

/**
 * @function handleImport
 * @description 处理SHP导入
 */
const handleImport = async (): Promise<void> => {
  if (!hasValidFiles.value) {
    ElMessage.warning('请先选择有效的SHP文件');
    return;
  }

  if (isImporting.value) {
    ElMessage.warning('导入正在进行中，请稍候...');
    return;
  }

  try {
    isImporting.value = true;
    ElMessage.info('正在导入SHP数据，请稍候...');

    // 调用API导入SHP文件
    const response = await ImportFileApi.importPipeNodeShp(selectedFiles.value);
    
    if (response.code === 200) {
      ElMessage.success('SHP数据导入成功');
      // 清空文件选择
      selectedFiles.value = [];
    } else {
      throw new Error(response.msg || '导入失败');
    }
  } catch (error: any) {
    console.error('SHP导入失败:', error);
    ElMessage.error(`导入失败: ${error.message || '未知错误'}`);
  } finally {
    isImporting.value = false;
  }
};

/**
 * @function closeCard
 * @description 处理关闭面板
 */
const closeCard = (): void => {
  try {
    console.log('关闭SHP导入面板');
    emit('close');
  } catch (error) {
    console.error('关闭面板失败:', error);
    ElMessage.error('关闭面板失败');
  }
};


</script>

<style scoped lang="scss">
.shp-import-panel {
  position: absolute;
  left: 10px;
  top: 150px;
  width: 500px;
  max-height: 85vh;
  z-index: 999;
}

// ============ 工具栏样式 ============
.toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  
  .el-button {
    flex: 1;
  }
}

// ============ 上传区域样式 ============
.upload-section {
  .shp-upload {
    width: 100%;

    :deep(.el-upload-dragger) {
      width: 99%;
      height: 150px;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      transition: border-color 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-bottom: 5px;
      &:hover {
        border-color: #409eff;
      }
    }

    .el-icon--upload {
      font-size: 48px;
      color: #c0c4cc;
      margin: 20px 0 12px;
    }

    .el-upload__text {
      color: #606266;
      font-size: 14px;
      text-align: center;
      line-height: 1.5;

      .upload-hint {
        display: block;
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }
  }

  // 文件状态样式
  .file-status {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    text-align: center;
  }
}

// ============ 响应式样式 ============
@media (max-width: 768px) {
  .shp-import-panel {
    width: 90vw;
    left: 5vw;
  }

  .toolbar {
    flex-direction: column;
  }
}
</style> 