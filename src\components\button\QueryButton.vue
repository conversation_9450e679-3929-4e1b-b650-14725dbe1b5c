<template>
  <el-button
    class="query-button"
    :class="{ 'is-active': active }"
  >
    <slot></slot>
  </el-button>
</template>

<script setup lang="ts">
interface Props {
  active?: boolean;
}

defineProps<Props>();
</script>

<style scoped lang="scss">
.query-button {
  &.is-active {
    background-color: var(--el-color-primary) !important;
    border-color: var(--el-color-primary) !important;
    color: #fff !important;
  }
}
</style>