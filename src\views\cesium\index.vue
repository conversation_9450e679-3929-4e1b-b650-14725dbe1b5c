<!--
  @fileoverview Cesium模块布局组件
  @description 作为Cesium三维地图模块的布局容器，提供子路由视图渲染
  <AUTHOR>
  @version 1.0.0
-->

<template>
  <div class="cesium-container">
    <router-view />
    <TinyTools :type="'cesium'" v-show="!isDualMapRoute"></TinyTools>

    <!-- 动态布局容器 -->
    <div :class="containerClass">
      <!-- 三维部分 - 始终存在，动态调整宽度 -->
      <div :class="cesiumClass">
        <MapContainer ref="cesiumContainerRef" />
      </div>

      <!-- 二维部分 - 仅在 dualmap 路由时显示 -->
      <div v-if="isDualMapRoute" :class="openLayersClass">
        <div ref="openLayersContainer" class="ol-map-container">
          <!-- 加载状态 -->
          <div v-if="isOlLoading" class="ol-loading">
            <el-loading element-loading-text="正在初始化二维地图..." />
          </div>
        </div>
      </div>
    </div>

    <!-- 其他组件 -->
    <div>
      <template v-for="item in mainComponents" :key="item.name">
        <component :is="item.component" :mainItem="item"></component>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { AppCesium } from '@/lib/cesium/AppCesium'
import { ElMessage } from 'element-plus'
import olcsCamera from '@/utils/syncCamera/Camera'
import Map from 'ol/Map'
import View from 'ol/View'
import 'ol/ol.css'
import Tile from 'ol/layer/Tile'
import XYZ from 'ol/source/XYZ'
import { fromLonLat } from 'ol/proj'

// 现有导入
import TinyTools from "@/views/tinytools/index.vue"
import MapContainer from './CesiumContainer.vue'
import { useDialogStore } from "@/stores/Dialogs"
import VectorTileLayer from 'ol/layer/VectorTile'
import VectorTile from 'ol/source/VectorTile'
import MVT from 'ol/format/MVT'
import Style from 'ol/style/Style'
import Stroke from 'ol/style/Stroke'
import Circle from 'ol/style/Circle'
import Fill from 'ol/style/Fill'
const route = useRoute()
const Dialogs = useDialogStore()
const { mainComponents } = storeToRefs(Dialogs)

// ==================== 路由判断 ====================
const isDualMapRoute = computed(() => route.name === 'DualMap')

// ==================== 样式类计算 ====================
const containerClass = computed(() =>
  isDualMapRoute.value ? 'dual-map-layout' : 'single-map-layout'
) 

const cesiumClass = computed(() =>
  isDualMapRoute.value ? 'cesium-half' : 'cesium-full'
)

const openLayersClass = computed(() => 'openlayers-half')

// ==================== 二维地图相关 ====================
const openLayersContainer = ref<HTMLElement>()
const openLayersMap = ref<any>(null)
const isOlLoading = ref(false)
const olInitialized = ref(false)

// ==================== 同步相关 ====================
const cesiumContainerRef = ref()
const cameraSync = ref<any>(null)
const renderId = ref<number>()

// ==================== 监听路由变化 ====================
watch(isDualMapRoute, async (newValue) => {
  if (newValue) {
    // 进入 dualmap 路由，初始化二维地图
    await nextTick()
    await initializeOpenLayers()
    await setupCameraSync()
    // 隐藏倾斜摄影图层
    // const cesiumInstance = AppCesium.getInstance().getViewer()
    const layers = AppCesium.getInstance().getLayerManager().find((layer:any) => {
      return layer.options.type === 'reality'
    });
    layers.forEach((layer: any) => {
      layer.show = false
    })
  } else {
    // 离开 dualmap 路由，清理二维地图
    cleanupOpenLayers()
    const layers = AppCesium.getInstance().getLayerManager().find((layer:any) => {
      return layer.options.type === 'reality'
    });
    layers.forEach((layer: any) => {
      layer.show = true
    })
  }
})

// ==================== 二维地图初始化 ====================
const initializeOpenLayers = async (): Promise<void> => {
  try {
    if (!openLayersContainer.value) {
      throw new Error('OpenLayers容器未找到')
    }

    isOlLoading.value = true
    console.log('[CesiumIndex] 开始初始化OpenLayers')

    // 给容器添加ID
    const containerId = 'ol-container-' + Date.now()
    openLayersContainer.value.id = containerId

    // 创建底图图层
    const vectorBaseLayer = new Tile({
      source: new XYZ({
        url: 'https://map.lshywater.cn/bcserver/services/swq_tdt_digital/rest/xyz/{z}/{x}/{y}/tile.png',
        maxZoom: 18,
        attributions: '乐山市沙湾区矢量底图服务',
      }),
    })
    // 创建底图图层
    const labelBaseLayer = new Tile({
      source: new XYZ({
        url: 'https://map.lshywater.cn/bcserver/services/swq_tdt_label/rest/xyz/{z}/{x}/{y}/tile.png',
        maxZoom: 18,
        attributions: '乐山市沙湾区矢量注记服务',
      }),
    })

    const mvtLayer = new VectorTileLayer({
          source: new VectorTile({
            format: new MVT(),
            url: 'https://gis.lshywater.cn/api/analyse/gs/ln/mvt/{z}/{x}/{y}.mvt',
            maxZoom: 18,
          }),
          style: new Style({
            stroke: new Stroke({
              color: '#2b78fe',
              width: 3,
            }),
          }),
        });
    const pointmvtLayer = new VectorTileLayer({
          source: new VectorTile({
            format: new MVT(),
            url: 'https://gis.lshywater.cn/api/analyse/gs/pt/mvt/{z}/{x}/{y}.mvt',
            maxZoom: 18,
          }),
          style: new Style({
            image: new Circle({
              radius: 4,
              fill: new Fill({
                color: '#ffffff',
              }),
              stroke: new Stroke({
                color: '#2b78fe',
                width: 2,
              }),
            }),
          }),
        });

    // 创建地图实例
    const map = new Map({
      target: containerId,
      layers: [vectorBaseLayer, labelBaseLayer, mvtLayer, pointmvtLayer],
      view: new View({
        center: fromLonLat([103.55, 29.41]),
        zoom: 13,
        projection: 'EPSG:3857',
      }),
    })

    vectorBaseLayer.setVisible(true)
    openLayersMap.value = map
    olInitialized.value = true
    isOlLoading.value = false

    console.log('[CesiumIndex] OpenLayers初始化完成')
    ElMessage.success('二维地图初始化完成')
  } catch (error) {
    console.error('[CesiumIndex] OpenLayers初始化失败:', error)
    isOlLoading.value = false
    ElMessage.error('二维地图初始化失败')
  }
}

// ==================== 同步设置 ====================
const setupCameraSync = async (): Promise<void> => {
  try {
    // 等待 Cesium 容器准备就绪
    await nextTick()

    // 获取 Cesium viewer 实例
    const cesiumInstance = AppCesium.getInstance().getViewer()
    const cesiumViewer = (cesiumInstance as any).delegate
    if (!cesiumViewer || !openLayersMap.value) {
      console.warn('[CesiumIndex] Cesium viewer 或 OpenLayers map 未准备就绪')
      return
    }

    // 创建同步实例
    cameraSync.value = new olcsCamera(
      cesiumViewer.scene,
      openLayersMap.value
    )

    // 启动同步检查
    cameraSync.value.checkCameraChange()
    startRenderLoop()

    console.log('[CesiumIndex] 二三维同步设置完成')
  } catch (error) {
    console.error('[CesiumIndex] 同步设置失败:', error)
  }
}

// ==================== 渲染循环 ====================
const startRenderLoop = (): void => {
  const render = () => {
    if (renderId.value !== undefined) {
      cancelAnimationFrame(renderId.value)
    }

    renderId.value = requestAnimationFrame(() => {
      if (cameraSync.value && isDualMapRoute.value) {
        cameraSync.value.checkCameraChange()
        render() // 继续循环
      }
    })
  }

  render()
}

// ==================== 清理方法 ====================
const cleanupOpenLayers = (): void => {
  console.log('[CesiumIndex] 开始清理OpenLayers资源')

  // 停止渲染循环
  if (renderId.value !== undefined) {
    cancelAnimationFrame(renderId.value)
    renderId.value = undefined
  }

  // 清理同步实例
  if (cameraSync.value) {
    cameraSync.value = null
  }

  // 销毁地图实例
  if (openLayersMap.value) {
    try {
      openLayersMap.value.setTarget(null)
      openLayersMap.value = null
    } catch (error) {
      console.error('[CesiumIndex] OpenLayers清理失败:', error)
    }
  }

  // 重置状态
  olInitialized.value = false
  isOlLoading.value = false

  console.log('[CesiumIndex] OpenLayers资源清理完成')
}

// ==================== 生命周期 ====================
onMounted(async () => {
  // 如果当前就是 dualmap 路由，立即初始化
  if (isDualMapRoute.value) {
    await nextTick()
    await initializeOpenLayers()
    await setupCameraSync()
  }
})

onUnmounted(() => {
  cleanupOpenLayers()
})
</script>

<style scoped lang="scss">
.cesium-container {
  width: 100%;
  height: 100%;
  position: relative;
}

// 单地图布局
.single-map-layout {
  width: 100%;
  height: 100%;
  position: relative;
}

.cesium-full {
  width: 100%;
  height: 100%;
  position: relative;
}

// 双地图布局
.dual-map-layout {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
}

.cesium-half {
  width: 50%;
  height: 100%;
  position: relative;
  border-right: 1px solid #ddd;
}

.openlayers-half {
  width: 50%;
  height: 100%;
  position: relative;
  background: #f8f9fa;
}

.ol-map-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.ol-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

// OpenLayers 样式覆盖
:deep(.ol-viewport) {
  outline: none;
}

:deep(.ol-control) {
  font-family: inherit;
}
</style>
