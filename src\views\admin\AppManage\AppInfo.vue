<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    class="admin-sub-form"
    label-width="auto"
    :disabled="editable"
  >
    <el-form-item label="名称" prop="name">
      <el-input
        class=""
        v-model.trim="formData.name"
        placeholder="请输入名称"
      />
    </el-form-item>
    <el-form-item label="版本号" prop="version">
      <el-input
        class="w-full"
        v-model.trim="formData.version"
        placeholder="请输入版本号"
      />
    </el-form-item>
    <el-form-item label="APK文件" prop="fileInfoDto">
      <el-upload
        class="w-full"
        ref="upload"
        v-model:file-list="fileList"
        :limit="1"
        :action="reqUrl"
        :headers="headers"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        :data="infoData"
        accept=".apk"
      >
        <el-button type="primary">上传</el-button>
      </el-upload>
    </el-form-item>
    <el-form-item label="描述" prop="remark">
      <el-input
        placeholder="请输入描述"
        v-model="formData.remark"
        type="textarea"
        resize="none"
        :rows="3"
        maxlength="500"
      ></el-input>
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import localCache from "@/utils/auth";
const props = defineProps({
  formData: Object,
  editable: Boolean,
});
const formData = ref<any>(props.formData);
const editable = ref(props.editable);
watch(
  () => props.formData,
  (val: any) => {
    resetForm();
    formData.value = val;
    if (val?.fileInfoDto == null) {
      fileList.value = [];
    } else {
      fileList.value = [val?.fileInfoDto];
    }
  }
);
watch(
  () => props.editable,
  (val) => {
    editable.value = val;
  }
);
const rules = {
  name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 2, message: "长度不低于2个字符", trigger: "blur" },
    { max: 20, message: "长度不超过20个字符", trigger: "blur" },
  ],
  version: [{ required: true, message: "请选择图标类型", trigger: "change" }],
  fileInfoDto: [
    { required: true, message: "请选择.apk文件上传", trigger: "change" },
  ],
};
let form: any = ref(null);
const infoData = ref();
const fileList = ref<any>([]);
const reqUrl = ref(import.meta.env.VITE_XHR_URL + "/common/upload");
const headers = {
  Latias: localCache.getCache("Latias"),
};
const handleSuccess = (response: any) => {
  if (response.code === 200) {
    form.value?.resetFields('fileInfoDto');
    formData.value.fileInfoDto = response.data;
  } else {
    fileList.value = [];
    ElMessage.error(response.msg);
  }
};
const beforeUpload = (rawFile: any) => {
  const formDatas = new FormData();
  console.log(rawFile);
  infoData.value = formDatas.append("file", rawFile, rawFile.name);
};
const submitForm = async (): Promise<any> => {
  return (await form.value?.validate()) ? formData.value : null;
};
const resetForm = () => {
  form.value?.resetFields();
};

onMounted(async () => {
  resetForm();
  if (formData.value.fileInfoDto != null) {
    fileList.value = [formData.value.fileInfoDto];
  }
});
defineExpose({
  submitForm,
  resetForm,
});
</script>
