/** * 爆管分析 */
<template>
  <page-card :close-icon="false" class="tabulate-sta" title="爆管分析">
    <!-- 操作按钮区域 -->

    <el-button
      type="primary"
      class="primary-btn w-130px h-9"
      :loading="isSelectingPoint"
      @click="handleSelectBurstPoint"
    >
      选择爆管点
    </el-button>
    <el-button
      type="primary"
      class="primary-btn h-9 w-130px"
      :loading="isAnalyzing"
      @click="handleAnalysis"
      :disabled="!burstPoint || isSelectingPoint"
    >
      分析
    </el-button>
    <el-button
      class="clear-btn w-130px"
      @click="handleClear"
      :disabled="isAnalyzing || isSelectingPoint"
    >
      清除
    </el-button>

    <!-- 爆管点显示区域 -->
    <el-row v-if="burstPoint" class="point-section">
      <el-col :span="24">
        <el-text class="point-label">已选择爆管点：</el-text>
        <div class="point-info">
          <div class="point-coordinates">
            <el-icon class="color-#1966FF mr-1"><Location /></el-icon>
            <span class="point-text">
              经度: {{ burstPoint.lng.toFixed(6) }}, 纬度:
              {{ burstPoint.lat.toFixed(6) }}
            </span>
          </div>
          <div v-if="burstPoint.gxbm" class="pipeline-info">
            <span class="pipeline-label">所在管线：</span>
            <span class="pipeline-text">{{ burstPoint.gxbm }}</span>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 分析结果显示区域 -->
    <el-row v-if="showResult" class="result-section">
      <el-col :span="24">
        <el-text class="result-label">分析结果</el-text>
      </el-col>
    </el-row>

    <!-- 相关阀门表格 -->
    <el-row v-if="showValveTable" class="table-section">
      <el-col :span="24">
        <div class="flex items-center mb-1">
          <div class="line"></div>
          <el-text class="table-label">需关断阀门</el-text>
        </div>

        <el-table
          class="routeCt"
          :row-class-name="tableRowClassName"
          :data="getCurrentValvePageData()"
          style="width: 100%"
        >
          <el-table-column
            prop="index"
            label="序号"
            min-width="50"
          ></el-table-column>
          <el-table-column prop="valveName" label="阀门名称"></el-table-column>
          <el-table-column
            prop="valveCode"
            label="阀门编码"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="valvePosition"
            label="阀门位置"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="roadName"
            label="所在道路"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleValveLocation(scope.row)"
              >
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- <el-pagination
          @current-change="handleValveCurrentChange"
          @size-change="handleValveSizeChange"
          :current-page="valveCurrentPage"
          :page-sizes="pageSizes"
          :page-size="valvePageSize"
          :total="valveTotal"
          layout="prev, pager, next, total"
          class="pagination"
          small
        ></el-pagination> -->
        <div class="flex justify-between items-center">
          <div>
            <div class="font-size-3.5 color-#323233">
              共{{ valveTotal }}条数据
            </div>
          </div>
          <el-pagination
            @current-change="handleValveCurrentChange"
            @size-change="handleValveSizeChange"
            :current-page="valveCurrentPage"
            :page-sizes="pageSizes"
            :page-size="valvePageSize"
            :total="valveTotal"
            :pager-count="5"
            layout="prev, pager, next, jumper"
            class="pagination"
            background
            small
          ></el-pagination>
        </div>
      </el-col>
    </el-row>

    <!-- 影响用户表格 -->
    <el-row v-if="showUserTable" class="table-section">
      <el-col :span="24">
        <div flex="~ row justify-between items-center" class="pb-2">
          <div class="flex items-center">
            <div class="line"></div>
            <el-text class="table-label">影响用户</el-text>
          </div>
          <div class="flex items-center">
            <div class="color-#2C3037 font-size-3.5">
              停水通知（已选择{{ selectedUsers.length }}位用户）：
            </div>
            <div
              class="rounded-1 w-8 h-8 text-center line-height-8 cursor-pointer"
              :class="
                selectedUsers.length > 0
                  ? 'border-1px-solid-#1966ff'
                  : 'border-1px-solid-#d9d9d9'
              "
              @click="sendWaterStopNotice"
              :title="
                selectedUsers.length > 0 ? '发送停水通知' : '请先勾选用户'
              "
            >
              <img
                src="@/assets/images/basemap/file.png"
                alt=""
                class="w-4 h-4"
                :style="{ opacity: selectedUsers.length > 0 ? 1 : 0.5 }"
              />
            </div>
          </div>
          <!-- <el-button type="text" @click="sendWaterStopNotice">
            发送停水通知
          </el-button> -->
        </div>
        <el-table
          ref="userTableRef"
          class="routeCt"
          :row-class-name="tableRowClassName"
          :data="getCurrentUserPageData()"
          style="width: 100%"
          @selection-change="handleUserSelectionChange"
          row-key="id"
        >
          <el-table-column
            type="selection"
            width="55"
            reserve-selection
          ></el-table-column>
          <el-table-column
            type="index"
            label="序号"
            min-width="50"
          ></el-table-column>
          <el-table-column
            prop="userName"
            label="用户姓名"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="userNo"
            label="用户号"
            show-overflow-tooltip
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="contactsPhone"
            label="联系电话"
            show-overflow-tooltip
            min-width="120"
          ></el-table-column>
          <!-- <el-table-column
            prop="waterAddress"
            label="用户地址"
            min-width="100"
            show-overflow-tooltip
          ></el-table-column> -->
          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <el-button
                type="text"
                style="color: #1966ff"
                @click="handleValveLocation(scope.row)"
              >
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-between items-center">
          <div>
            <div class="font-size-3.5 color-#323233">
              共{{ userTotal }}条数据
            </div>
          </div>
          <el-pagination
            @current-change="handleUserCurrentChange"
            @size-change="handleUserSizeChange"
            :current-page="userCurrentPage"
            :page-sizes="pageSizes"
            :page-size="userPageSize"
            :total="userTotal"
            :pager-count="5"
            layout="prev, pager, next, jumper"
            class="pagination"
            background
            small
          ></el-pagination>
        </div>
      </el-col>
    </el-row>
  </page-card>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from "vue";
import { Location } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
import type { MapEngineType } from "@/components/BufferAnalysisDrawButtons.vue";
import { AppMaplibre } from "@/lib/maplibre/AppMaplibre";
import { AppCesium } from "@/lib/cesium/AppCesium";
import {
  lineListByBms,
  pointListByBms,
  queryExplosion,
  waterListByIds,
} from "@/api/analysis";
import { sendWaterStopNotifacation } from "@/api/leak";

/**
 * 定义相关阀门数据项接口
 */
interface ValveItem {
  index: number;
  valveName: string; // 阀门名称
  valveCode: string; // 阀门编码
  valvePosition: string; // 阀门位置
  roadName: string; // 所在道路
}

/**
 * 定义影响用户数据项接口
 */
interface AffectedUserItem {
  id: string; // 用户ID
  index: number;
  userName: string; // 用户姓名
  userNo: string; // 用户号
  contactsPhone: string; // 联系电话
  waterAddress: string; // 用户地址
  valvePosition?: string; // 水表位置
}

/**
 * 定义爆管点坐标接口
 */
interface BurstPoint {
  lng: number;
  lat: number;
  gxbm?: string; // 管线编码
  pipelineInfo?: any; // 管线信息
}

/**
 * 组件属性定义
 */
const props = defineProps({
  mainItem: {
    type: Object,
    default: () => ({}),
  },
});

const route = useRoute();
const mapEngine = computed((): MapEngineType => {
  return route.path.includes("cesium") ? "cesium" : "maplibre";
});

/**
 * 响应式数据状态
 */
// 爆管点相关状态
const burstPoint = ref<BurstPoint | null>(null);
const isSelectingPoint = ref<boolean>(false);
const isAnalyzing = ref<boolean>(false);
const showResult = ref<boolean>(false);

// 相关阀门表格状态
const criticalValveData = ref<ValveItem[]>([]);
const valveCurrentPage = ref<number>(1);
const valvePageSize = ref<number>(5);
const valveTotal = ref<number>(0);
const showValveTable = ref<boolean>(false);

// 影响用户表格状态
const userData = ref<AffectedUserItem[]>([]);
const userCurrentPage = ref<number>(1);
const userPageSize = ref<number>(5);
const userTotal = ref<number>(0);
const showUserTable = ref<boolean>(false);

// 选中用户状态
const selectedUsers = ref<AffectedUserItem[]>([]);

// 表格引用
const userTableRef = ref();

// 分页选项
const pageSizes = ref<number[]>([5, 10, 20, 50]);

// Cesium VectorLayer管理
const cesiumBurstPointLayer = ref<any>(null); // 爆管点图层

/**
 * @description 在Cesium中选择爆管点（在管线上选择）
 * @param {Function} onSuccess - 选择成功回调函数
 * @param {Function} onError - 选择失败回调函数
 */
const selectBurstPointOnCesium = (
  onSuccess: (point: BurstPoint) => void,
  onError: (error: any) => void
) => {
  try {
    const tipMessage = "请在3D地图上点击管线选择爆管点";
    ElMessage.info(tipMessage);

    AppCesium.getInstance().selectPipeComponent((res: any) => {
      try {
        // 检查选择的是否为管线类型（edge、pipe等）
        if (res && res.type === "line" && res.id && res.position) {
          const selectedPoint: BurstPoint = {
            lng: res.position.lng,
            lat: res.position.lat,
            gxbm: res.id, // 管线编码
            pipelineInfo: res, // 保存完整的管线信息
          };

          onSuccess(selectedPoint);
          ElMessage.success(`已选择爆管点（管线：${selectedPoint.gxbm}）`);
        } else if (res && res.type === "node") {
          // 如果选择的是管点，提示用户选择管线
          onError(new Error("请在管线上点击爆管位置，而不是管点"));
        } else {
          onError(new Error("请在管线上点击爆管位置"));
        }
      } catch (error) {
        onError(error);
      }
    });
  } catch (error) {
    onError(error);
  }
};

/**
 * @description 在Cesium中添加爆管点显示图层
 * @param {BurstPoint} point - 爆管点信息
 */
const addCesiumBurstPointLayer = (point: BurstPoint) => {
  try {
    const viewer = AppCesium.getInstance().getViewer();
    const { Cesium } = BC.Namespace;

    // 创建或获取爆管点图层
    if (!cesiumBurstPointLayer.value) {
      cesiumBurstPointLayer.value = new BC.VectorLayer("burst-point-layer");
      viewer.addLayer(cesiumBurstPointLayer.value);
    }

    // 清除之前的爆管点
    cesiumBurstPointLayer.value.clear();

    const position = new BC.Position(point.lng, point.lat, 50); // 高度设为50米

    // 创建爆管点标记（红色大圆点）
    const burstPoint = new BC.Point(position);
    burstPoint.id = "burst-point-marker";
    burstPoint.setStyle({
      pixelSize: 15,
      color: BC.Color.RED,
      outlineColor: BC.Color.WHITE,
      outlineWidth: 3,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    });

    // 创建爆管点标签
    const labelPosition = new BC.Position(point.lng, point.lat, 52);
    const label = new BC.Label(labelPosition, `爆管点\n${point.gxbm || ""}`);
    label.id = "burst-point-label";
    label.setStyle({
      font: "14px Microsoft YaHei",
      fillColor: BC.Color.RED,
      outlineColor: BC.Color.WHITE,
      outlineWidth: 2,
      offsetX: 0,
      offsetY: -30,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    });

    // 添加到图层
    cesiumBurstPointLayer.value.addOverlay(burstPoint);
    cesiumBurstPointLayer.value.addOverlay(label);

    // 缩放到爆管点位置
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(point.lng, point.lat, 800),
      duration: 2.0,
    });

    console.log("已在Cesium中创建爆管点标记:", point.gxbm);
  } catch (error) {
    console.error("在Cesium中添加爆管点图层失败:", error);
    throw error;
  }
};

/**
 * @description 清除Cesium中的爆管点图层
 */
const removeCesiumBurstPointLayer = () => {
  try {
    if (cesiumBurstPointLayer.value) {
      const viewer = AppCesium.getInstance().getViewer();
      cesiumBurstPointLayer.value.clear();
      viewer.removeLayer(cesiumBurstPointLayer.value);
      cesiumBurstPointLayer.value = null;
      console.log("已清除Cesium爆管点图层");
    }
  } catch (error) {
    console.error("清除Cesium爆管点图层失败:", error);
  }
};

/**
 * 处理选择爆管点
 */
const handleSelectBurstPoint = async () => {
  isSelectingPoint.value = true;
  try {
    if (mapEngine.value === "cesium") {
      // 使用Cesium选择爆管点逻辑
      selectBurstPointOnCesium(
        (selectedPoint: BurstPoint) => {
          burstPoint.value = selectedPoint;

          // 添加爆管点临时图层显示
          addCesiumBurstPointLayer(selectedPoint);

          isSelectingPoint.value = false;
        },
        (error: any) => {
          console.error("选择爆管点失败:", error);
          ElMessage.error(
            `${error instanceof Error ? error.message : "未知错误"}`
          );
          isSelectingPoint.value = false;
        }
      );
    } else {
      // 实现MapLibre地图点选择
      await selectBurstPointOnMapLibre();
    }
  } catch (error) {
    console.error("选择爆管点失败:", error);
    ElMessage.error("选择爆管点失败");
    isSelectingPoint.value = false;
  }
};

/**
 * 在MapLibre地图上选择爆管点
 */
const selectBurstPointOnMapLibre = async (): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      const map = AppMaplibre.getMap();

      ElMessage.info("请在管线上点击选择爆管点");

      // 鼠标移动事件处理器 - 实时检测管线并显示提示点
      const mouseMoveHandler = (e: any) => {
        // 查询鼠标位置的管线要素
        const degree = 10;
        const features = map.queryRenderedFeatures(
          [
            [e.point.x - degree / 2, e.point.y - degree / 2],
            [e.point.x + degree / 2, e.point.y + degree / 2],
          ],
          {
            layers: ["mvt_pipeLine"], // 管线矢量瓦片图层
          }
        );

        if (features.length > 0) {
          // 鼠标在管线上，显示提示点
          showHintPoint(e.lngLat);
          // 改变鼠标样式
          map.getCanvas().style.cursor = "crosshair";
        } else {
          // 鼠标不在管线上，隐藏提示点
          hideHintPoint();
          // 恢复默认鼠标样式
          map.getCanvas().style.cursor = "";
        }
      };

      // 点击事件处理器 - 确定爆管点位置
      const clickHandler = (e: any) => {
        // 查询点击位置的管线要素
        const features = map.queryRenderedFeatures(e.point, {
          layers: ["mvt_pipeLine"], // 管线矢量瓦片图层
        });

        if (features.length > 0) {
          // 获取管线信息
          const pipelineFeature = features[0];
          const gxbm =
            pipelineFeature.properties?.gxbm ||
            pipelineFeature.properties?.GXBM ||
            "";

          // 点击在管线上，确定爆管点
          burstPoint.value = {
            lng: e.lngLat.lng,
            lat: e.lngLat.lat,
            gxbm: gxbm,
            pipelineInfo: pipelineFeature.properties,
          };

          // 添加爆管点显示图层
          addBurstPointLayer(e.lngLat);

          // 清理事件监听器
          cleanupMapEvents();

          ElMessage.success(`已选择爆管点${gxbm ? `（管线：${gxbm}）` : ""}`);
          isSelectingPoint.value = false;
          resolve();
        } else {
          // 点击不在管线上，提示用户
          ElMessage.warning("请在管线上点击选择爆管点");
        }
      };

      // 清理事件监听器和提示点的函数
      const cleanupMapEvents = () => {
        map.off("mousemove", mouseMoveHandler);
        map.off("click", clickHandler);
        hideHintPoint();
        map.getCanvas().style.cursor = "";
      };

      // 绑定事件监听器
      map.on("mousemove", mouseMoveHandler);
      map.on("click", clickHandler);

      // 设置超时机制（可选）
      setTimeout(() => {
        if (isSelectingPoint.value) {
          cleanupMapEvents();
          isSelectingPoint.value = false;
          reject(new Error("选择爆管点超时"));
        }
      }, 60000); // 60秒超时
    } catch (error) {
      console.error("MapLibre地图点选择失败:", error);
      isSelectingPoint.value = false;
      reject(error);
    }
  });
};

/**
 * 显示提示点
 */
const showHintPoint = (lngLat: any) => {
  try {
    const map = AppMaplibre.getMap();

    // 移除已存在的提示点
    hideHintPoint();

    // 添加提示点数据源
    if (!map.getSource("burst-hint-point")) {
      map.addSource("burst-hint-point", {
        type: "geojson",
        data: {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [lngLat.lng, lngLat.lat],
          },
          properties: {},
        },
      });
    } else {
      // 更新提示点位置 - 获取并类型转换为GeoJSONSource
      const source = map.getSource("burst-hint-point") as any;
      if (source && source.setData) {
        source.setData({
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [lngLat.lng, lngLat.lat],
          },
          properties: {},
        });
      }
    }

    // 添加提示点图层
    if (!map.getLayer("burst-hint-point-layer")) {
      map.addLayer({
        id: "burst-hint-point-layer",
        type: "circle",
        source: "burst-hint-point",
        paint: {
          "circle-radius": 8,
          "circle-color": "#ff0000",
          "circle-opacity": 0.8,
          "circle-stroke-width": 2,
          "circle-stroke-color": "#ffffff",
          "circle-stroke-opacity": 1,
        },
      });
    }
  } catch (error) {
    console.error("显示提示点失败:", error);
  }
};

/**
 * 隐藏提示点
 */
const hideHintPoint = () => {
  try {
    const map = AppMaplibre.getMap();

    // 移除提示点图层
    if (map.getLayer("burst-hint-point-layer")) {
      map.removeLayer("burst-hint-point-layer");
    }

    // 移除提示点数据源
    if (map.getSource("burst-hint-point")) {
      map.removeSource("burst-hint-point");
    }
  } catch (error) {
    console.error("隐藏提示点失败:", error);
  }
};

/**
 * 添加爆管点显示图层
 * @param lngLat 爆管点坐标
 */
const addBurstPointLayer = (lngLat: any) => {
  try {
    const map = AppMaplibre.getMap();

    // 先移除已存在的爆管点图层
    removeBurstPointLayer();

    // 添加爆管点数据源
    map.addSource("burst-point-source", {
      type: "geojson",
      data: {
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [lngLat.lng, lngLat.lat],
        },
        properties: {},
      },
    });

    // 添加爆管点外圈图层（较大的红色圆圈）
    map.addLayer({
      id: "burst-point-outer",
      type: "circle",
      source: "burst-point-source",
      paint: {
        "circle-radius": 12,
        "circle-color": "#ff4d4f",
        "circle-opacity": 0.6,
        "circle-stroke-width": 0,
      },
    });

    // 添加爆管点内圈图层（较小的白色圆圈）
    map.addLayer({
      id: "burst-point-inner",
      type: "circle",
      source: "burst-point-source",
      paint: {
        "circle-radius": 6,
        "circle-color": "#ffffff",
        "circle-opacity": 1,
        "circle-stroke-width": 2,
        "circle-stroke-color": "#ff4d4f",
        "circle-stroke-opacity": 1,
      },
    });

    // 添加爆管点中心点图层（红色小点）
    map.addLayer({
      id: "burst-point-center",
      type: "circle",
      source: "burst-point-source",
      paint: {
        "circle-radius": 2,
        "circle-color": "#ff4d4f",
        "circle-opacity": 1,
      },
    });

    // 添加爆管点文字标签图层
    map.addLayer({
      id: "burst-point-label",
      type: "symbol",
      source: "burst-point-source",
      layout: {
        "text-field": "爆管点",
        "text-font": ["Open Sans Regular"],
        "text-size": 12,
        "text-offset": [0, 2],
        "text-anchor": "top",
      },
      paint: {
        "text-color": "#ff4d4f",
        "text-halo-color": "#ffffff",
        "text-halo-width": 1,
      },
    });

    console.log("爆管点图层已添加");
  } catch (error) {
    console.error("添加爆管点图层失败:", error);
  }
};

/**
 * 移除爆管点显示图层
 */
const removeBurstPointLayer = () => {
  try {
    const map = AppMaplibre.getMap();

    // 移除所有爆管点相关图层
    const layerIds = [
      "burst-point-label",
      "burst-point-center",
      "burst-point-inner",
      "burst-point-outer",
    ];
    layerIds.forEach((layerId) => {
      if (map.getLayer(layerId)) {
        map.removeLayer(layerId);
      }
    });

    // 移除爆管点数据源
    if (map.getSource("burst-point-source")) {
      map.removeSource("burst-point-source");
    }

    console.log("爆管点图层已移除");
  } catch (error) {
    console.error("移除爆管点图层失败:", error);
  }
};

/**
 * 添加水表管点图层
 * @param waterMeterData 水表管点数据数组
 */
const addWaterMeterPointsLayer = (waterMeterData: any[]) => {
  try {
    const map = AppMaplibre.getMap();

    // 先移除已存在的水表管点图层
    removeWaterMeterPointsLayer();

    if (waterMeterData && waterMeterData.length > 0) {
      console.log("添加水表管点图层，数据：", waterMeterData);

      // 创建水表管点FeatureCollection
      const waterMeterFeatures = waterMeterData
        .map((waterMeter, index) => {
          try {
            return {
              type: "Feature" as const,
              id: `water-meter-${index}`,
              geometry: JSON.parse(waterMeter.geojson),
              properties: {
                id: waterMeter.gid || `water-meter-${index}`,
                name: waterMeter.bm || `水表${index + 1}`,
                type: "water-meter",
                waterUserId: waterMeter.waterUserId,
                fsw: waterMeter.fsw,
                ...waterMeter,
              },
            };
          } catch (error) {
            console.error(
              `解析水表管点 ${index} 的geojson失败:`,
              error,
              waterMeter
            );
            return null;
          }
        })
        .filter((feature) => feature !== null);

      const waterMeterFeatureCollection = {
        type: "FeatureCollection" as const,
        features: waterMeterFeatures,
      };

      // 添加水表管点数据源
      map.addSource("burst-analysis-water-meters", {
        type: "geojson",
        data: waterMeterFeatureCollection,
      });

      // 添加水表管点外圈图层（蓝色大圆圈）
      map.addLayer({
        id: "burst-analysis-water-meters-outer",
        type: "circle",
        source: "burst-analysis-water-meters",
        paint: {
          "circle-radius": 12,
          "circle-color": "#1890ff",
          "circle-opacity": 0.6,
          "circle-stroke-width": 0,
        },
      });

      // 添加水表管点内圈图层（白色圆圈）
      map.addLayer({
        id: "burst-analysis-water-meters-inner",
        type: "circle",
        source: "burst-analysis-water-meters",
        paint: {
          "circle-radius": 6,
          "circle-color": "#ffffff",
          "circle-opacity": 1,
          "circle-stroke-width": 2,
          "circle-stroke-color": "#1890ff",
          "circle-stroke-opacity": 1,
        },
      });

      // 添加水表管点中心点图层（蓝色小点）
      map.addLayer({
        id: "burst-analysis-water-meters-center",
        type: "circle",
        source: "burst-analysis-water-meters",
        paint: {
          "circle-radius": 2,
          "circle-color": "#1890ff",
          "circle-opacity": 1,
        },
      });

      // 添加水表管点文字标签图层
      map.addLayer({
        id: "burst-analysis-water-meters-label",
        type: "symbol",
        source: "burst-analysis-water-meters",
        layout: {
          "text-field": "水表",
          "text-font": ["Open Sans Regular"],
          "text-size": 10,
          "text-offset": [0, 2],
          "text-anchor": "top",
        },
        paint: {
          "text-color": "#1890ff",
          "text-halo-color": "#ffffff",
          "text-halo-width": 1,
        },
      });

      console.log(`已添加 ${waterMeterData.length} 个水表管点到地图`);
    }
  } catch (error) {
    console.error("添加水表管点图层失败:", error);
  }
};

/**
 * 移除水表管点图层
 */
const removeWaterMeterPointsLayer = () => {
  try {
    const map = AppMaplibre.getMap();

    // 移除水表管点相关图层
    const waterMeterLayerIds = [
      "burst-analysis-water-meters-label",
      "burst-analysis-water-meters-center",
      "burst-analysis-water-meters-inner",
      "burst-analysis-water-meters-outer",
    ];
    waterMeterLayerIds.forEach((layerId) => {
      if (map.getLayer(layerId)) {
        map.removeLayer(layerId);
      }
    });

    // 移除水表管点数据源
    if (map.getSource("burst-analysis-water-meters")) {
      map.removeSource("burst-analysis-water-meters");
    }

    console.log("水表管点图层已移除");
  } catch (error) {
    console.error("移除水表管点图层失败:", error);
  }
};

/**
 * 添加分析结果图层（关键阀门和受影响管线）
 * @param valveData 阀门数据数组，每个元素包含geojson字段
 * @param pipeData 管线数据数组，每个元素包含geojson字段
 */
const addAnalysisResultLayers = (valveData: any[], pipeData: any[]) => {
  try {
    const map = AppMaplibre.getMap();

    // 先移除已存在的分析结果图层
    removeAnalysisResultLayers();

    // 处理关键阀门数据
    if (valveData && valveData.length > 0) {
      console.log("添加关键阀门图层，数据：", valveData);

      // 创建阀门FeatureCollection
      const valveFeatures = valveData.map((valve, index) => ({
        type: "Feature" as const,
        id: `valve-${index}`,
        geometry: JSON.parse(valve.geojson),
        properties: {
          id: valve.id || `valve-${index}`,
          name: valve.name || `阀门${index + 1}`,
          type: "valve",
          ...valve,
        },
      }));

      const valveFeatureCollection = {
        type: "FeatureCollection" as const,
        features: valveFeatures,
      };

      // 添加阀门数据源
      map.addSource("burst-analysis-valves", {
        type: "geojson",
        data: valveFeatureCollection,
      });

      // 添加阀门外圈图层（橙色大圆圈）
      map.addLayer({
        id: "burst-analysis-valves-outer",
        type: "circle",
        source: "burst-analysis-valves",
        paint: {
          "circle-radius": 14,
          "circle-color": "#fa8c16",
          "circle-opacity": 0.6,
          "circle-stroke-width": 0,
        },
      });

      // 添加阀门内圈图层（白色圆圈）
      map.addLayer({
        id: "burst-analysis-valves-inner",
        type: "circle",
        source: "burst-analysis-valves",
        paint: {
          "circle-radius": 8,
          "circle-color": "#ffffff",
          "circle-opacity": 1,
          "circle-stroke-width": 2,
          "circle-stroke-color": "#fa8c16",
          "circle-stroke-opacity": 1,
        },
      });

      // 添加阀门中心点图层（橙色小点）
      map.addLayer({
        id: "burst-analysis-valves-center",
        type: "circle",
        source: "burst-analysis-valves",
        paint: {
          "circle-radius": 3,
          "circle-color": "#fa8c16",
          "circle-opacity": 1,
        },
      });

      // 添加阀门文字标签图层
      map.addLayer({
        id: "burst-analysis-valves-label",
        type: "symbol",
        source: "burst-analysis-valves",
        layout: {
          "text-field": "关键阀门",
          "text-font": ["Open Sans Regular"],
          "text-size": 12,
          "text-offset": [0, 2.5],
          "text-anchor": "top",
        },
        paint: {
          "text-color": "#fa8c16",
          "text-halo-color": "#ffffff",
          "text-halo-width": 1,
        },
      });

      console.log(`已添加 ${valveData.length} 个关键阀门到地图`);
    }

    // 处理受影响管线数据
    if (pipeData && pipeData.length > 0) {
      console.log("添加受影响管线图层，数据：", pipeData);

      // 创建管线FeatureCollection
      const pipeFeatures = pipeData.map((pipe, index) => ({
        type: "Feature" as const,
        id: `pipe-${index}`,
        geometry: JSON.parse(pipe.geojson),
        properties: {
          id: pipe.id || `pipe-${index}`,
          name: pipe.name || `管线${index + 1}`,
          type: "pipe",
          ...pipe,
        },
      }));

      const pipeFeatureCollection = {
        type: "FeatureCollection" as const,
        features: pipeFeatures,
      };

      // 添加管线数据源
      map.addSource("burst-analysis-pipes", {
        type: "geojson",
        data: pipeFeatureCollection,
      });

      // 添加管线图层（红色高亮）
      map.addLayer({
        id: "burst-analysis-pipes-line",
        type: "line",
        source: "burst-analysis-pipes",
        layout: {
          "line-join": "round",
          "line-cap": "round",
        },
        paint: {
          "line-color": "#ff4d4f",
          "line-width": 6,
          "line-opacity": 0.8,
        },
      });

      console.log(`已添加 ${pipeData.length} 条受影响管线到地图`);
    }

    console.log("分析结果图层添加完成");
  } catch (error) {
    console.error("添加分析结果图层失败:", error);
  }
};

/**
 * 移除分析结果图层
 */
const removeAnalysisResultLayers = () => {
  try {
    const map = AppMaplibre.getMap();

    // 移除阀门相关图层
    const valveLayerIds = [
      "burst-analysis-valves-label",
      "burst-analysis-valves-center",
      "burst-analysis-valves-inner",
      "burst-analysis-valves-outer",
    ];
    valveLayerIds.forEach((layerId) => {
      if (map.getLayer(layerId)) {
        map.removeLayer(layerId);
      }
    });

    // 移除管线相关图层
    const pipeLayerIds = ["burst-analysis-pipes-line"];
    pipeLayerIds.forEach((layerId) => {
      if (map.getLayer(layerId)) {
        map.removeLayer(layerId);
      }
    });

    // 移除数据源
    if (map.getSource("burst-analysis-valves")) {
      map.removeSource("burst-analysis-valves");
    }
    if (map.getSource("burst-analysis-pipes")) {
      map.removeSource("burst-analysis-pipes");
    }

    // 同时移除水表管点图层
    removeWaterMeterPointsLayer();

    console.log("分析结果图层已移除");
  } catch (error) {
    console.error("移除分析结果图层失败:", error);
  }
};

const currUserIds = ref<string[]>([]);

/**
 * 处理分析操作
 */
const handleAnalysis = async () => {
  if (!burstPoint.value) {
    ElMessage.warning("请先选择爆管点");
    return;
  }

  isAnalyzing.value = true;
  currUserIds.value = [];
  try {
    // TODO: 调用爆管分析API
    // const result = await burstAnalysisAPI({
    //   lng: burstPoint.value.lng,
    //   lat: burstPoint.value.lat
    // });
    const gxbm = mapEngine.value === "cesium" ? burstPoint.value.gxbm?.replace('_', '') : burstPoint.value.gxbm;
    //暂时写死
    const queryParams = {
      // edgeId: 'JSTSBLC248JSTSBLC247',
      edgeId: gxbm,
      facilities: "阀门",
      fieldName: "FSW",
    };
    const { status, data } = await queryExplosion(queryParams);
    if (status === 200 && data && data.data && data.data.nodes.length > 0) {
      const result = data.data;
      // 最近关键阀门设备
      const valveId = result.nodes[0];
      //受影响管线
      const pipeIds = result.edges.map((item: any) => item.id);
      //受影响用户(查询下游所有附属物为水表所关联的用户)todo

      const valveData = await pointListByBms([valveId]);
      const pipeData = await lineListByBms(pipeIds);

      // 准备在地图上显示的数据
      const valveDisplayData =
        valveData.code === 200 && valveData.data ? valveData.data : [];
      const pipeDisplayData =
        pipeData.code === 200 && pipeData.data ? pipeData.data : [];

      // 在地图上展示关键阀门位置和受影响管线
      if (valveDisplayData.length > 0 || pipeDisplayData.length > 0) {
        criticalValveData.value = valveDisplayData.map(
          (item: any, index: number) => ({
            index: index + 1,
            //管线点编号去掉英文字母
            valveName: `阀门${item.gxddh.replace(/[a-zA-Z]/g, "")}`,
            valveCode: item.gxddh,
            valvePosition: `${item.longitude.toFixed(
              6
            )},${item.latitude.toFixed(6)}`,
            roadName: item.szdl,
          })
        );
        valveTotal.value = valveDisplayData.length;
        showValveTable.value = true;
        addAnalysisResultLayers(valveDisplayData, pipeDisplayData);
        console.log(
          `地图上已显示 ${valveDisplayData.length} 个关键阀门和 ${pipeDisplayData.length} 条受影响管线`
        );
        const ptIds: string[] = [];
        pipeDisplayData.forEach((item: any) => {
          ptIds.push(item.zdbh);
          ptIds.push(item.qdbh);
        });
        const uniquePtIds = Array.from(new Set(ptIds));

        const ptData = await pointListByBms(uniquePtIds);
        //筛选出水表管点
        const waterMeterPoints = ptData.data.filter(
          (item: any) => item.fsw === "水表"
        );

        // 在地图上展示水表管点
        addWaterMeterPointsLayer(waterMeterPoints);
        console.log(
          `地图上已显示 ${waterMeterPoints.length} 个受影响的水表管点`
        );

        //查询受影响用户
        currUserIds.value = waterMeterPoints.map(
          (item: any) => item.waterUserId
        );

        const { code, data } = await waterListByIds(currUserIds.value);
        if (code === 200) {
          userData.value = data;
          userData.value.forEach((user: any) => {
            const waterMeterPoint = waterMeterPoints.find(
              (item: any) => item.waterUserId === user.id
            );
            user.valvePosition = `${waterMeterPoint.longitude.toFixed(
              6
            )},${waterMeterPoint.latitude.toFixed(6)}`;
          });

          // 调试信息：检查数据结构
          console.log("用户数据结构:", userData.value);
          console.log("第一个用户的ID:", userData.value[0]?.id);

          userTotal.value = data.length;
          showUserTable.value = true;
          showResult.value = true;
          isAnalyzing.value = false;

          // 数据加载完成
        }
      }
    } else {
      ElMessage.error("爆管分析失败，请重新选择爆管点");
      isAnalyzing.value = false;
      showResult.value = false;
      showValveTable.value = false;
      showUserTable.value = false;
    }
  } catch (error) {
    console.error("爆管分析失败:", error);
    ElMessage.error("分析失败，请重试");
    showResult.value = false;
    showValveTable.value = false;
    showUserTable.value = false;
    isAnalyzing.value = false;
  }
};

/**
 * 组件卸载时清理资源
 */
onUnmounted(() => {
  handleClear();

  // 额外的Cesium资源清理
  if (mapEngine.value === "cesium") {
    removeCesiumBurstPointLayer();
  }

  console.log("爆管分析组件已卸载，清理完成");
});

/**
 * 处理清除操作
 */
const handleClear = () => {
  // 清除爆管点数据
  burstPoint.value = null;

  // 根据地图引擎移除相应的图层
  if (mapEngine.value === "cesium") {
    // 清除Cesium模式下的图层
    removeCesiumBurstPointLayer();
    // TODO: 需要实现Cesium模式下的分析结果图层清除
  } else {
    // 移除MapLibre地图上的爆管点图层
    removeBurstPointLayer();
    // 移除MapLibre地图上的分析结果图层
    removeAnalysisResultLayers();
  }

  // 清除分析结果
  showResult.value = false;
  criticalValveData.value = [];
  showValveTable.value = false;
  valveCurrentPage.value = 1;
  userData.value = [];
  showUserTable.value = false;
  userCurrentPage.value = 1;
  currUserIds.value = [];
  selectedUsers.value = []; // 清除选中用户
  ElMessage.info("已清除");
};

/**
 * 相关阀门表格分页处理
 */
const handleValveCurrentChange = (page: number) => {
  valveCurrentPage.value = page;
};

const handleValveSizeChange = (size: number) => {
  valvePageSize.value = size;
  if (
    Math.ceil(valveTotal.value / valvePageSize.value) < valveCurrentPage.value
  ) {
    valveCurrentPage.value = 1;
  }
};

const getCurrentValvePageData = () => {
  const startIndex = (valveCurrentPage.value - 1) * valvePageSize.value;
  const endIndex = Math.min(
    startIndex + valvePageSize.value,
    criticalValveData.value.length
  );
  return criticalValveData.value.slice(startIndex, endIndex);
};

/**
 * 影响用户表格分页处理
 */
const handleUserCurrentChange = (page: number) => {
  userCurrentPage.value = page;
};

const handleUserSizeChange = (size: number) => {
  userPageSize.value = size;
  if (Math.ceil(userTotal.value / userPageSize.value) < userCurrentPage.value) {
    userCurrentPage.value = 1;
  }
};

const getCurrentUserPageData = () => {
  const startIndex = (userCurrentPage.value - 1) * userPageSize.value;
  const endIndex = Math.min(
    startIndex + userPageSize.value,
    userData.value.length
  );
  return userData.value.slice(startIndex, endIndex);
};

/**
 * 处理用户选择变化
 */
const handleUserSelectionChange = (selection: AffectedUserItem[]) => {
  selectedUsers.value = selection;
  console.log("选中的用户:", selectedUsers.value);
};

// 移除了复杂的恢复选中状态逻辑，简化为基本功能

/**
 * 处理阀门定位操作（根据mapEngine区分）
 */
const handleValveLocation = (row: ValveItem) => {
  try {
    // 从阀门位置字符串中提取经纬度
    const coordinates = row.valvePosition.split(",");
    if (coordinates.length !== 2) {
      ElMessage.error("阀门位置数据格式错误");
      return;
    }

    const longitude = parseFloat(coordinates[0]);
    const latitude = parseFloat(coordinates[1]);

    if (isNaN(longitude) || isNaN(latitude)) {
      ElMessage.error("阀门位置坐标无效");
      return;
    }

    if (mapEngine.value === "cesium") {
      // TODO: 3D场景中的阀门定位
      console.log("3D阀门定位:", { longitude, latitude, row });
      // ElMessage.info(`3D模式下定位到阀门: ${row.valveName}（功能待实现）`);
    } else {
      // 2D地图中的阀门定位
      console.log("2D阀门定位:", { longitude, latitude, row });

      const map = AppMaplibre.getMap();
      map.flyTo({
        center: [longitude, latitude],
        zoom: 19,
        duration: 2000,
      });

      ElMessage.success(`已定位到阀门: ${row.valveName}`);
    }
  } catch (error) {
    console.error("阀门定位失败:", error);
    ElMessage.error("阀门定位失败，请检查坐标数据");
  }
};

// /**
//  * 处理用户详情操作（根据mapEngine区分）
//  */
// const handleUserDetail = (row: AffectedUserItem) => {
//   if (mapEngine.value === 'cesium') {
//     // TODO: 3D场景中的用户详情展示
//     console.log('3D用户详情:', row);
//     ElMessage.info(`3D模式下查看用户详情: ${row.userName}`);
//   } else {
//     // TODO: 2D地图中的用户详情展示
//     console.log('2D用户详情:', row);
//     // ElMessage.info(`2D模式下查看用户详情: ${row.userName}`);
//   }
// };

/**
 * 发送停水通知
 */
const sendWaterStopNotice = async () => {
  if (currUserIds.value.length === 0) {
    ElMessage.warning("没有受影响的用户");
    return;
  }

  if (selectedUsers.value.length === 0) {
    ElMessage.warning("请先勾选需要发送通知的用户");
    return;
  }

  // 获取选中用户的ID列表
  const selectedUserIds = selectedUsers.value.map((user) => user.id);
  const contactPhones = selectedUsers.value.map((user) => user.contactsPhone);
  console.log("发送停水通知给用户:", selectedUserIds);
  ElMessage.success(`已向${selectedUsers.value.length}位用户发送停水通知`);

  await sendWaterStopNotifacation({
    phoneList: contactPhones,
    content: "前方水管爆管，暂时停水！",
  });
};
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "success-row"; //这是类名
  } else {
    return "dft";
  }
};
</script>

<style scoped lang="scss">
.tabulate-sta {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 550px;
  min-height: 130px;
  z-index: 1000;
}

// .button-section {
//   margin-bottom: 20px;
//   gap: 12px;

//   .el-col {
//     padding: 0 4px;
//   }

//   .select-btn,
//   .analysis-btn {
//     width: 100%;
//     background-color: #1890ff;
//     border-color: #1890ff;
//     font-size: 13px;
//     padding: 8px 12px;

//     &:hover {
//       background-color: #40a9ff;
//       border-color: #40a9ff;
//     }

//     &:disabled {
//       background-color: #d9d9d9;
//       border-color: #d9d9d9;
//     }
//   }

//   .clear-btn {
//     width: 100%;
//     background-color: #8c8c8c;
//     border-color: #8c8c8c;
//     color: white;
//     font-size: 13px;
//     padding: 8px 12px;

//     &:hover {
//       background-color: #a8a8a8;
//       border-color: #a8a8a8;
//     }

//     &:disabled {
//       background-color: #d9d9d9;
//       border-color: #d9d9d9;
//     }
//   }
// }

.point-section {
  margin: 20px 0;
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 6px;

  .point-label {
    color: #2c3037;
    font-size: 14px;
    margin-right: 8px;
  }

  .point-info {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .point-coordinates {
      display: flex;
      align-items: center;
      .point-text {
        color: #1966ff;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .pipeline-info {
      display: flex;
      align-items: center;
      padding: 6px 12px;
      // background-color: #e6f7ff;
      border-radius: 4px;
      // border-left: 3px solid #1890ff;
      box-sizing: border-box;
      border: 1px dashed rgba(146, 183, 255, 1);
      background: rgba(241, 248, 254, 1);
      color: #2c3037;

      .pipeline-label {
        font-size: 14px;
        margin-right: 8px;
        white-space: nowrap;
      }

      .pipeline-text {
        font-size: 14px;
        font-weight: 400;
        // background-color: #ffffff;
        padding: 2px 8px;
        border-radius: 3px;
        // border: 1px solid #91d5ff;
      }
    }
  }
}

.result-section {
  margin-bottom: 20px;

  .result-label {
    color: #2c3037;
    font-size: 16px;
    font-weight: 600;
  }
}

.table-section {
  margin-bottom: 20px;
  .line {
    height: 13px;
    border-left: 2px solid #999999;
  }
  .table-label {
    color: #999999;
    // border-left: 2px solid #999999;
    padding-left: 6px;
    font-size: 14px;
    display: block;
  }

  .el-table {
    margin-bottom: 15px;
    font-size: 13px;

    :deep(th) {
      background-color: #f2f6fc;
      color: #606266;
      font-weight: 500;
      font-size: 13px;
    }

    :deep(td) {
      padding: 6px 0;
    }

    :deep(.el-button--small) {
      padding: 4px 8px;
      font-size: 12px;
      min-width: 50px;
    }
  }

  // .pagination {
  //   margin-top: 15px;
  //   justify-content: end;
  //   display: flex;
  // }
}

// 停水通知按钮样式
.border-1px-solid-\#1966ff {
  border: 1px solid #1966ff;
}

.border-1px-solid-\#d9d9d9 {
  border: 1px solid #d9d9d9;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
