<!--
  @fileoverview 二三维联动主容器 - Cesium+MapLibre版
  @description 基于Cesium和MapLibre实现三维和二维地图的精确联动
  <AUTHOR> Assistant  
  @version 4.0.0
-->

<template>
  <div class="dual-map-container">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <el-loading element-loading-text="正在初始化双视图地图..." />
    </div>

    <!-- 1:1分屏布局 -->
    <div class="split-layout">
      <!-- 左侧三维视图 -->
      <div class="cesium-container">
        <!-- Cesium 3D容器 -->
        <div ref="cesium3DContainerRef" class="cesium-viewer cesium-3d"></div>
      </div>

      <!-- 右侧二维视图 -->
      <div class="maplibre-container">
        <!-- MapLibre GL容器 -->
        <div
          ref="maplibreContainerRef"
          class="maplibre-viewer maplibre-2d"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { AppCesium } from '@/lib/cesium/AppCesium';
import { ElMessage } from 'element-plus';
import olcsCamera from '@/utils/syncCamera/Camera';
import Map from 'ol/Map';
import View from 'ol/View';
import 'ol/ol.css';
import Tile from 'ol/layer/Tile';
import XYZ from 'ol/source/XYZ';
import { fromLonLat } from 'ol/proj';
// ==================== 响应式数据 ====================
const isLoading = ref(true);

// 地图容器引用
const cesium3DContainerRef = ref<HTMLElement>();
const maplibreContainerRef = ref<HTMLElement>();

// 地图实例
const cesium3DInstance = ref<AppCesium | null>(null);
const maplibreMap = ref<any>(null);

// viewer实例
const cesium3DViewer = ref<any>(null);

// 初始化状态
const cesium3DInitialized = ref(false);
const maplibreInitialized = ref(false);

// 同步状态
let syncTimeout: number | null = null;
const camera_ = ref();
// ==================== 生命周期方法 ====================

onMounted(async () => {
  try {
    console.log('[DualMapContainer] 开始初始化双视图容器');

    // 等待DOM更新
    await nextTick();

    // 并行初始化Cesium和MapLibre实例
    await Promise.all([initializeCesium3D(), initializeMapLibre()]);

    // 设置初始视角
    setInitialViewport();

    camera_.value = new olcsCamera(
      cesium3DViewer.value.scene,
      maplibreMap.value
    );
    camera_.value.checkCameraChange();
    // 启用自动同步
    // setupAutoSync()

    isLoading.value = false;

    ElMessage.success('双视图地图初始化完成');
    console.log('[DualMapContainer] 双视图初始化完成');
  } catch (error) {
    console.error('[DualMapContainer] 初始化失败:', error);
    ElMessage.error('双视图地图初始化失败');
    isLoading.value = false;
  }
});

onUnmounted(() => {
  // 清理资源
  cleanup();
});

// ==================== 地图初始化方法 ====================

/**
 * @description 初始化Cesium三维地图
 */
const initializeCesium3D = async (): Promise<void> => {
  try {
    if (!cesium3DContainerRef.value) {
      throw new Error('Cesium 3D容器未找到');
    }

    console.log('[DualMapContainer] 开始初始化Cesium 3D');

    // 获取AppCesium单例实例
    cesium3DInstance.value = AppCesium.getInstance();

    // 创建Cesium 3D viewer
    const viewer = new BC.Viewer(cesium3DContainerRef.value, {
      contextOptions: {
        webgl: {
          // 通过canvas.toDataURL()实现截图需要将该项设置为true
          preserveDrawingBuffer: true,
        },
      },
    });

    // 启用地形深度测试
    viewer.scene.globe.depthTestAgainstTerrain = true;
    // 比例尺
    viewer.distanceLegend.enable = true;

    // 初始化基础地图和viewer
    await cesium3DInstance.value.initBaseMap(viewer);
    cesium3DInstance.value.initViewer(viewer);
    cesium3DInstance.value.goToHome();
    render_();
    // 保存viewer实例
    cesium3DViewer.value = viewer;

    cesium3DInitialized.value = true;
    console.log('[DualMapContainer] Cesium 3D初始化完成');
  } catch (error) {
    console.error('[DualMapContainer] Cesium 3D初始化失败:', error);
    throw error;
  }
};

/**
 * @description 初始化MapLibre二维地图
 */
const initializeMapLibre = async (): Promise<void> => {
  try {
    if (!maplibreContainerRef.value) {
      throw new Error('MapLibre容器未找到');
    }

    console.log('[DualMapContainer] 开始初始化MapLibre');

    // 给容器添加ID，MapLibre需要ID进行初始化
    const containerId = 'maplibre-container-' + Date.now();
    maplibreContainerRef.value.id = containerId;
    const vectorBaseLayer = new Tile({
      // className: 'baseLayerClass',
      source: new XYZ({
        url: 'http://*************:8077/bcserver/services/swq_tdt_digital/rest/xyz/{z}/{x}/{y}/tile.png',
        maxZoom: 18,
        attributions: '乐山市沙湾区矢量底图服务',
      }),
      // title: 'vectorBase',
    });
    const map = new Map({
      target: containerId,
      layers: [
        vectorBaseLayer,
        // imageBaseLayer,
        // labelsLayer,
        // mvtLayer,
        // mvtLayer2,
      ],
      view: new View({
        // center: [103.59274916212075, 29.371853128306228],
        center: fromLonLat([103.55, 29.41]),
        zoom: 13,
        projection: 'EPSG:3857',
      }),
    });
    vectorBaseLayer.setVisible(true);

    console.log('[DualMapContainer] MapLibre初始化完成');

    // 保存地图实例
    maplibreMap.value = map;
    maplibreInitialized.value = true;
  } catch (error) {
    console.error('[DualMapContainer] MapLibre初始化失败:', error);
    // 重置状态
    maplibreInitialized.value = false;
    maplibreMap.value = null;
    throw error;
  }
};

// ==================== 视角设置方法 ====================

/**
 * @description 设置初始视角
 */
const setInitialViewport = (): void => {
  console.log('[DualMapContainer] 设置初始视角');

  // 检查两个地图是否都已初始化
  if (!cesium3DInitialized.value || !maplibreInitialized.value) {
    console.warn('[DualMapContainer] 地图未完全初始化，无法设置初始视角');
    return;
  }

  // 设置Cesium 3D视角
  if (cesium3DViewer.value) {
    const { Cesium } = BC.Namespace;
    cesium3DViewer.value.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        103.59274916212075,
        29.371853128306228,
        20000
      ),
      orientation: {
        heading: 0.0,
        pitch: Cesium.Math.toRadians(-30),
        roll: 0.0,
      },
    });
  }

  // MapLibre已在初始化时设置了视角
  console.log('[DualMapContainer] 初始视角设置完成');
};

// ==================== 清理方法 ====================

/**
 * @description 清理资源
 */
const cleanup = (): void => {
  console.log('[DualMapContainer] 开始清理资源');

  // 清理定时器
  if (syncTimeout) {
    clearTimeout(syncTimeout);
    syncTimeout = null;
  }

  // 销毁Cesium viewer
  if (cesium3DViewer.value) {
    try {
      cesium3DViewer.value.destroy();
    } catch (error) {
      console.error('[DualMapContainer] Cesium 3D销毁失败:', error);
    }
    cesium3DViewer.value = null;
  }

  // 销毁MapLibre地图
  if (maplibreMap.value) {
    try {
      maplibreMap.value.remove();
    } catch (error) {
      console.error('[DualMapContainer] MapLibre销毁失败:', error);
    }
    maplibreMap.value = null;
  }

  // 重置状态
  cesium3DInitialized.value = false;
  maplibreInitialized.value = false;

  console.log('[DualMapContainer] 资源清理完成');
};
const renderId_ = ref();
/**
 * Render the Cesium scene
 */
const render_ = () => {
  // if a call to `requestAnimationFrame` is pending, cancel it
  if (renderId_.value !== undefined) {
    cancelAnimationFrame(renderId_.value);
    renderId_.value = undefined;
  }

  renderId_.value = requestAnimationFrame(onAnimationFrame_.bind(this));
};

const targetFrameRate_ = Number.POSITIVE_INFINITY;
const lastFrameTime_ = ref();
/**
 * Callback for `requestAnimationFrame`.
 * @param {number} frameTime The frame time, from `performance.now()`.
 * @private
 */
const onAnimationFrame_ = (frameTime: any) => {
  renderId_.value = undefined;

  // check if a frame was rendered within the target frame rate
  const interval = 1000.0 / targetFrameRate_;
  const delta = frameTime - lastFrameTime_.value;
  if (delta < interval) {
    // too soon, don't render yet
    render_();
    return;
  }

  // time to render a frame, save the time
  lastFrameTime_.value = frameTime;
  // const {Cesium} = BC.Namespace;
  // const julianDate = Cesium.JulianDate.now();
  cesium3DViewer.value.scene.initializeFrame();

  // cesium3DViewer.value.scene.render(julianDate);
  camera_.value.checkCameraChange();

  // request the next render call after this one completes to ensure the browser doesn't get backed up
  render_();
};
</script>

<style scoped lang="scss">
.dual-map-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: #f5f7fa;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.split-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

.cesium-container,
.maplibre-container {
  width: 50%;
  height: 100%;
  position: relative;
  background: #e8f4f8;
}

.cesium-viewer,
.maplibre-viewer {
  width: 100%;
  height: 100%;
  background: #f0f2f5;
  position: relative;
  overflow: hidden;
}

.cesium-3d {
  border-right: 1px solid #ddd;
}

.maplibre-2d {
  background: #f8f9fa;
}

/* MapLibre 特定样式 */
.maplibre-viewer {
  :deep(.maplibregl-canvas) {
    outline: none;
  }

  :deep(.maplibregl-control-container) {
    font-family: inherit;
  }
}
</style>
