import hRequest from '@/utils/http';
import type { DataType } from '@/utils/http/types';
import axios from 'axios';

/**根据管径统计长度 */
export const getLengthByPipeDiameter = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/analyse/gs/ln/gj/length/stat',
    params
  });
};

/** 按道路统计长度 */
export const getLengthByPath = () => {
  return hRequest.get<DataType>({
    url: '/analyse/gs/ln/szdl/length/stat',
  })
}

/** 按管材统计长度 */
export const getLengthByPipeMaterial = () => {
  return hRequest.get<DataType>({
    url: '/analyse/gs/ln/cz/length/stat'
  })
}

/** 汇总统计导出 */
export const tabulateExport = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/analyse/gs/ln/export',
    params,
    responseType: 'blob'
  })
}

/** 分类查询 */
export const classifiedQuery = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/analyse/gs/pt/classified/page',
    params
  })
}

/** 查询所在道路列表 */
export const roadList = () => {
  return hRequest.get<DataType>({
    url: '/analyse/gs/pt/szdl/list',
  })
}

/** 设备导出(物联网设备列表) */
// export const iotDeviceExport = (data: any) => {
//   return axios.post('iot url' + '/v1/device/coordinate/export', data)
// }

/**设备信息导出 */
export const deviceExport = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/v1/device/export',
    // url: '/v1/device/coordinate/export',
    params,
    responseType: 'blob'
  })
}

/** 分页获取设备汇总列表 */
export const getDevicePage = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/v1/device_summary',
    params
  })
}
/** 分页获取设备预警记录 */
export const getDeviceWarnRecord = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/v1/alarm_record/page',
    params
  })
}
/** 根据设备唯⼀标识获取设备基础数据 */
export const getDevice = (code: any) => {
  return hRequest.get<DataType<any>>({
    url: `/v1/device_summary/${code}/basic`
  })
}
/** 分页查询预警记录 - 设备类型(小类) */
export const getWarnRecordByDeviceType = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/v1/dict',
    params
  })
}

/** 空间查询设备信息 */
export const queryDevice = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/v1/device/coordinate',
    params
  })
}

/**
 * 获取本地模拟物联网设备数据
 * @returns Promise 包含设备数据的响应
 */
export const getMockIotDevices = () => {
  return axios.get('/data/mock/iot_spatial_device.json');
};

/** 修改设备 */
export const updateDevice = (data: any) => {
  return hRequest.put<DataType<any>>({
    url: '/v1/device',
    data
  })
}

/** 查询设备字段 */
export const queryDeviceField = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/v1/device/fields',
    params
  })
}

/** 设备汇总分页 */
export const deviceSummaryPage = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/v1/device_summary',
    params
  })
}

/** 管点-统计字段列表 */
export const queryPtStatisticsField = () => {
  return hRequest.get<DataType<any>>({
    url: '/analyse/gs/pt/stat/field/list'
  })
}

/** 管点-根据分类字段统计数量 */
export const queryPtStatistics = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/analyse/gs/pt/stat/classified/count',
    params
  })
}

/** 管线-统计字段列表 */
export const queryLnStatisticsField = () => {
  return hRequest.get<DataType<any>>({
    url: '/analyse/gs/ln/stat/field/list'
  })
}

/** 管线-根据分类字段统计数量 */
export const queryLnStatistics = (params: any) => {
  return hRequest.get<DataType<any>>({
    url: '/analyse/gs/ln/stat/classified/count',
    params
  })
}

/** 管点附属物列表 */
export const getPipeNodeAccessoryList = () => {
  return hRequest.get<DataType<any>>({
    url: '/analyse/gs/pt/dynamic/field/list'
  });
};

/** 管线附属物列表 */
export const getPipeLineAccessoryList = () => {
  return hRequest.get<DataType<any>>({
    url: '/analyse/gs/ln/dynamic/field/list'
  });
};

/** 管点高级查询 */
export const advancedQueryForPipeNode = (data: any) => {
  return hRequest.post<DataType<any>>({
    url: '/analyse/gs/pt/dynamic/query',
    data
  });
};

/** 管线高级查询 */
export const advancedQueryForPipeLine = (data: any) => {
  return hRequest.post<DataType<any>>({
    url: '/analyse/gs/ln/dynamic/query',
    data
  });
};
/** 设备唯⼀标识获取设备详细数据 */
export const queryDeviceSum = (code: string) => {
  return hRequest.get<DataType<any>>({
    url: `/v1/device_summary/${code}/detail`
  })
}

/** 统计所在道路下每个附属物数量 */
export const queryAccessoryCountByRoad = () => {
  return hRequest.get<DataType<any>>({
    url: '/analyse/gs/pt/stat/road/count'
  })
}