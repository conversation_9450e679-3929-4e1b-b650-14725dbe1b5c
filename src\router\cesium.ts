/**
 * @fileoverview Cesium模块路由配置
 * @description 定义Cesium三维地图模块的路由规则和组件映射
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * @description Cesium模块路由配置对象
 * @details 配置三维地图模块的路由结构，包括布局组件和子路由
 */
export const cesiumRouter = {
  path: '/cesium',
  name: 'CesiumModule',
  meta: {
    title: '三维模块'
  },
  redirect: '/cesium/home',
  component: () => import('@/views/cesium/index.vue'),
  children: [
    {
      path: 'home',
      name: 'CesiumHome',
      meta: {
        title: '资产总览',
        icon: 'map',
        acIcon: 'map-ac'
      },
      component: () => import('@/views/cesium/pages/home/<USER>'),
    },

    {
      path: 'query',
      name: 'CesiumQuery',
      meta: {
        title: '查询统计',
        icon: 'query',
        acIcon: 'query-ac'
      },
      component: () => import('@/views/maplibre/pages/query/index.vue'),
      redirect: '/cesium/query/spatial',
      children: [
        {
          path: 'spatial',
          name: 'CsSpatialQuery',
          meta: { title: '空间查询' },
          component: () => import('@/views/maplibre/pages/query/SpatialQuery.vue')
        },
        {
          path: 'classified-query',
          name: 'CsClassifiedQuery',
          meta: { title: '分类查询' },
          component: () => import('@/views/maplibre/pages/query/ClassifiedQuery.vue')
        },
        {
          path: 'tabulate',
          name: 'CsTabulateStatistics',
          meta: { title: '汇总统计' },
          component: () => import('@/views/maplibre/pages/query/TabulateStatistics.vue')
        },
        {
          path: 'general',
          name: 'CsGeneralStatistics',
          meta: { title: '通用统计' },
          component: () => import('@/views/maplibre/pages/query/GeneralStatistics.vue')
        },
        {
          path: 'classified',
          name: 'CsClassifiedStatistics',
          meta: { title: '分类统计' },
          component: () => import('@/views/maplibre/pages/query/ClassifiedStatistics.vue')
        },
        {
          path: 'pipe',
          name: 'CsPipeStatistics',
          meta: { title: '管网统计' },
          component: () => import('@/views/cesium/pages/query/PipeStatistics.vue')
        },
        // 条件统计
        {
          path: 'condition',
          name: 'CsConditionStatistics',
          meta: { title: '条件统计' },
          component: () => import('@/views/cesium/pages/query/ConditionStatistics.vue')
        }
      ]
    },
    {
      path: 'pipeNetwork',
      name: 'CsPipeNetwork',
      meta: {
        title: '管网分析',
        icon: 'pipe',
        acIcon: 'pipe-ac'
      },
      component: () => import('@/views/maplibre/pages/pipeNetwork/index.vue'),
      redirect: '/cesium/pipeNetwork/crossSection',
      children: [
        {
          path: 'crossSection',
          name: 'CsCrossSection',
          meta: { title: '横断面分析' },
          component: () => import('@/views/analysis/VerticalProfileAnalysis.vue')
        },
        {
          path: 'horizontalProfile',
          name: 'CsHorizontalProfile',
          meta: { title: '纵断面分析' },
          component: () => import('@/views/analysis/HorizontalProfileAnalysis.vue')
        },
        {
          path: 'depthAnalysis',
          name: 'CsDepthAnalysis',
          meta: { title: '埋深分析' },
          component: () => import('@/views/analysis/DepthAnalysis.vue')
        },
        {
          path: 'bufferAnalysis',
          name: 'CsBufferAnalysis',
          meta: { title: '缓冲区分析' },
          component: () => import('@/views/analysis/BufferAnalysis.vue')
        },
        {
          path: 'horizontalDistanceAnalysis',
          name: 'CsHorizontalDistanceAnalysis',
          meta: { title: '水平净距分析' },
          component: () => import('@/views/analysis/HorizontalDistanceAnalysis.vue')
        },
        {
          path: 'verticalDistanceAnalysis',
          name: 'CsVerticalDistanceAnalysis',
          meta: { title: '垂直净距分析' },
          component: () => import('@/views/analysis/VerticalDistanceAnalysis.vue')
        },
        {
          path: 'flowAnalysis',
          name: 'CsFlowAnalysis',
          meta: { title: '流向分析' },
          component: () => import('@/views/analysis/FlowAnalysis.vue')
        },
        {
          path: 'connectivityAnalysis',
          name: 'CsConnectivityAnalysis',
          meta: { title: '连通性分析' },
          component: () => import('@/views/analysis/ConnectivityAnalysis.vue')
        },
        {
          path: 'burstAnalysis',
          name: 'CsBurstAnalysis',
          meta: { title: '爆管分析' },
          component: () => import('@/views/analysis/BurstAnalysis.vue')
        },
        {
          path: 'excavationAnalysis',
          name: 'CsExcavationAnalysis',
          meta: { title: '开挖分析' },
          component: () => import('@/views/analysis/ExcavationAnalysis.vue')
        },
        {
          path: 'tunnelSimulation',
          name: 'CsTunnelSimulation',
          meta: { title: '隧道模拟' },
          component: () => import('@/views/analysis/TunnelSimulation.vue')
        },
        // {
        //   path: 'tunnelSimulationThree',
        //   name: 'CsTunnelSimulationThree',
        //   meta: { title: '隧道模拟(Three.js)' },
        //   component: () => import('@/views/analysis/TunnelSimulationThree.vue')
        // }
      ]
    },
        {
      path: 'dualmap',
      name: 'DualMap',
      meta: {
        title: '二三维联动',
        icon: 'map',
        acIcon: 'map-ac'
      },
      component: () => import('@/views/cesium/pages/dualmap/index.vue'),
    }
  ],
}