import { Map as glMap } from 'maplibre-gl';
import { BaseLayer } from './BaseLayer';
import { getImages } from '@/utils/getImages';
import { mockLngLatInPolygon } from '@/utils/mockUtil';
import { queryLeakPage } from '@/api/leak';
import dayjs from 'dayjs';

export default class LeakLayer extends BaseLayer {
  protected declare _delegate: any;
  constructor(options: any) {
    super(options);
  }

  /**
   * @description 从 URL 加载 GeoJSON 数据
   * @param url - GeoJSON 数据文件路径
   * @returns Promise<GeoJSON.FeatureCollection>
   */
  private async loadGeoJsonData(
    url: string
  ): Promise<GeoJSON.FeatureCollection> {
    try {
      // const leakDatas = [
      //   {
      //     listenType: 2,
      //     confirmUser: '1645243389114900482',
      //     confirmTime: '2024-04-01 10:14:28', //确认漏电时间
      //     storeyId: '1765349054298013698',
      //     equipmentId: '1717023891698855937',
      //     listenTime: '2024-04-01 01:12:42', //听漏时间
      //     listenPlace: '测试位置', //漏点位置
      //     lng: null, //经度
      //     lat: null, //纬度
      //     influenceRange: null, //影响范围
      //     repairStatus: 0, //报修状态（0：未报修；1：已报修，2：已修复）
      //     repairTime: null, //报修时间
      //     pipeLine: '1673610798175399938',
      //     listenType_dictText: '设备听漏', //听漏类型
      //     storeyId_dictText: '城区高压分区三', //所属分区
      //     repairStatus_dictText: '未报修', //报修状态
      //   },
      //   {
      //     listenType: 2,
      //     confirmUser: '1645243389114900482',
      //     confirmTime: '2024-03-28 16:39:05',
      //     storeyId: '1735145507883376641',
      //     equipmentId: '1717023891698855937',
      //     listenTime: '2024-03-28 16:38:02',
      //     listenPlace: '测试位置',
      //     lng: null,
      //     lat: null,
      //     influenceRange: null,
      //     repairStatus: 0,
      //     repairTime: null,
      //     pipeLine: '1664531441742245889',
      //     listenType_dictText: '设备听漏',
      //     storeyId_dictText: '测试高位分区一',
      //     repairStatus_dictText: '未报修',
      //   },
      // ];
      // const features: any[] = [];
      // leakDatas.forEach((item: any, index: number) => {
      //   const [lng, lat] = mockLngLatInPolygon();
      //   const longitude = item.lng || lng;
      //   const latitude = item.lat || lat;
      //   features.push({
      //     type: 'Feature',
      //     properties: item,
      //     geometry: {
      //       type: 'Point',
      //       coordinates: [longitude, latitude],
      //     },
      //   });
      // });

      const response = await queryLeakPage({
        pageNo: 1,
        pageSize: 10,
        repairStatus: 0,
        listenTime_begin: dayjs().subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss'),
        listenTime_end: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      });
      const features: any[] = [];
      if(response.status === 200) {
        const result = response.data;
        if(result.code === 200) {
          const leakDatas = result.result.records;

          leakDatas.forEach((item: any, index: number) => {
            const [lng, lat] = mockLngLatInPolygon();
            const longitude = item.lng || lng;
            const latitude = item.lat || lat;
            features.push({
              type: 'Feature',
              properties: item,
              geometry: {
                type: 'Point',
                coordinates: [longitude, latitude],
              },
            });
          })
        }
      }
      return {
        type: 'FeatureCollection',
        features: features as any,
      };
    } catch (error) {
      console.error(`加载 GeoJSON 数据失败: ${url}`, error);
      throw error;
    }
  }
  async addToInternal(map: glMap) {
    this._map = map;
    let delegate: any;
    const geojson = await this.loadGeoJsonData(this.options.url as string);

    await this.loadImage(
      `${this.options.icon}`,
      getImages(`device/${this.options.icon}2x.png`)
    );

    if (geojson.features.length > 0) {
      delegate = this.loadDevice(geojson);
    }

    return delegate;
  }
  /**
   * @description 异步加载图片资源并添加到地图
   * @param id - 图片ID
   * @param imageUrl - 图片URL路径
   * @returns Promise<void>
   */
  async loadImage(id: string, imageUrl: string): Promise<void> {
    // 如果图片已存在，直接返回
    if (this._map.hasImage(id)) {
      return;
    }

    try {
      // 使用MapLibre GL的Promise API加载图片
      const response = await this._map.loadImage(imageUrl);

      // 将图片添加到地图样式中
      this._map.addImage(id, response.data);

      // console.log(`✅ 成功加载设备图标: ${id} from ${imageUrl}`);
    } catch (error) {
      console.error(`❌ 加载设备图标失败: ${id} from ${imageUrl}`, error);
      throw error;
    }
  }
  loadDevice(geojson: any) {
    // const sourceId = `${this.options.id}_source`
    const sourceId = `leak_${this.options.icon}_source`;
    this._map.addSource(sourceId, {
      type: 'geojson',
      data: geojson,
      promoteId: 'id',
    });

    this._map.addLayer({
      id: this._id,
      type: 'symbol',
      source: sourceId,
      layout: {
        'icon-image': `${this.options.icon}`,
        // 'text-field': '{name}',
        'icon-size': 1,
        'text-offset': [0, 0.5],
        'icon-offset': [0, -28],
        'text-anchor': 'top',
        'text-size': 16,
      },
      paint: {
        'text-color': '#2a71af',
        'icon-opacity': [
          'case',
          ['boolean', ['feature-state', 'flash'], false],
          0,
          1,
        ],
      },
    });
    return this._map.getLayer(this._id);
  }
  protected showInternal(show: boolean): void {
    // 主图层由父类BaseLayer控制，这里只需要调用父类方法处理delegate
    super.showInternal(show);
  }
  removeInternal() {
    this._map.removeLayer(this._id);
    const sourceId = `leak_${this.options.icon}_source`;
    this._map.removeSource(sourceId);
  }
  metersToPixelsAtMaxZoom(meters: number, latitude: number) {
    return meters / 0.075 / Math.cos((latitude * Math.PI) / 180);
  }
}
