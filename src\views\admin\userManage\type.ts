
export interface QueryForm {
  type: string;
  name: string;
  phone: string;
  pageSize: number;
  pageNum: number;
}
export interface SysUser {
  /**
   * ID
   */
  id: string
  /**
   *用户名称
   */
  name: string
  /**
   *用户角色
   */
  roleIds: string
  /**
   * 用户编码
   */
  code: string
  /**
   * 用户性别
   */
  gender: string
  /**
   * 平台账号
   */
  username: string
  /**
   * 电子邮箱
   */
  email: string
  /**
   * 手机号码
   */
  phone: string
  /**
   * 出身年月
   */
  birthday: string
  /**
   * 用户昵称
   */
  nickname: string
  /**
   * 来源平台
   */
  sourcePlatform: string
  /**
   * 用户来源
   */
  source: string
  /**
   * 用户类型
   */
  type: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 激活时间
   */
  updateTime: string
  /**
   * 激活状态
   */
  activation: string
  /**
   * 认证状态
   */
  realStatus: string
  /**
   * 头像
   */
  photo: any
}